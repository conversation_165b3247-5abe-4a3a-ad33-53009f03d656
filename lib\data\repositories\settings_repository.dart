import 'package:shared_preferences/shared_preferences.dart';
import '../../core/models/company_profile.dart';
import '../../core/utils/app_logger.dart';

/// مستودع الإعدادات
/// يوفر وظائف للوصول إلى إعدادات التطبيق وملف الشركة
class SettingsRepository {
  static final SettingsRepository _instance = SettingsRepository._internal();

  factory SettingsRepository() {
    return _instance;
  }

  SettingsRepository._internal();

  /// الحصول على ملف الشركة
  Future<CompanyProfile?> getCompanyProfile() async {
    try {
      // في الإصدار الحالي، نعيد ملف شركة افتراضي
      // في الإصدار المستقبلي، سيتم استرجاع البيانات من قاعدة البيانات
      return CompanyProfile(
        name: 'شركة تاجر بلس',
        logo: null,
        address: 'الرياض، المملكة العربية السعودية',
        phone: '0*********',
        email: '<EMAIL>',
        website: 'www.tajerplus.com',
        taxNumber: '*********',
        commercialRegister: '*********',
        currency: 'ريال',
        currencySymbol: 'ر.س',
        currencySymbolOnLeft: false,
        decimalPlaces: 2,
        thousandsSeparator: ',',
        decimalSeparator: '.',
        receiptFooter: 'شكراً لتعاملكم معنا',
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      AppLogger.error('Error getting company profile: $e');
      return null;
    }
  }

  /// حفظ ملف الشركة
  Future<bool> saveCompanyProfile(CompanyProfile profile) async {
    try {
      // في الإصدار المستقبلي، سيتم حفظ البيانات في قاعدة البيانات
      return true;
    } catch (e) {
      AppLogger.error('Error saving company profile: $e');
      return false;
    }
  }

  /// الحصول على إعدادات التطبيق
  Future<Map<String, dynamic>> getAppSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // إعدادات افتراضية
      return {
        'theme': prefs.getString('theme') ?? 'light',
        'language': prefs.getString('language') ?? 'ar',
        'currency': prefs.getString('currency') ?? 'SAR',
        'tax_enabled': prefs.getBool('tax_enabled') ?? true,
        'tax_rate': prefs.getDouble('tax_rate') ?? 15.0,
        'printer_enabled': prefs.getBool('printer_enabled') ?? false,
        'printer_name': prefs.getString('printer_name'),
        'printer_ip': prefs.getString('printer_ip'),
        'printer_port': prefs.getInt('printer_port') ?? 9100,
      };
    } catch (e) {
      AppLogger.error('Error getting app settings: $e');
      return {};
    }
  }

  /// حفظ إعدادات التطبيق
  Future<bool> saveAppSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      for (final entry in settings.entries) {
        final key = entry.key;
        final value = entry.value;

        if (value is String) {
          await prefs.setString(key, value);
        } else if (value is bool) {
          await prefs.setBool(key, value);
        } else if (value is int) {
          await prefs.setInt(key, value);
        } else if (value is double) {
          await prefs.setDouble(key, value);
        }
      }

      return true;
    } catch (e) {
      AppLogger.error('Error saving app settings: $e');
      return false;
    }
  }
}
