import 'package:flutter/foundation.dart';
import 'journal_entry_detail.dart';

/// نموذج القيد المحاسبي
class JournalEntry {
  final String id;
  final String reference;
  final DateTime date;
  final String description;
  final String status;
  final String type;
  final double totalDebit;
  final double totalCredit;
  final bool isDeleted;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<JournalEntryDetail> details;

  // خصائص محسوبة للتوافق مع الكود القديم
  String get entryNumber => reference;
  DateTime get entryDate => date;
  String? get referenceNumber => reference;
  String get referenceType => type;

  JournalEntry({
    required this.id,
    required this.reference,
    required this.date,
    required this.description,
    required this.status,
    required this.type,
    required this.totalDebit,
    required this.totalCredit,
    this.isDeleted = false,
    required this.createdAt,
    this.updatedAt,
    this.details = const [],
  });

  /// إنشاء نسخة جديدة مع تحديث بعض الخصائص
  JournalEntry copyWith({
    String? id,
    String? reference,
    DateTime? date,
    String? description,
    String? status,
    String? type,
    double? totalDebit,
    double? totalCredit,
    bool? isDeleted,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<JournalEntryDetail>? details,
  }) {
    return JournalEntry(
      id: id ?? this.id,
      reference: reference ?? this.reference,
      date: date ?? this.date,
      description: description ?? this.description,
      status: status ?? this.status,
      type: type ?? this.type,
      totalDebit: totalDebit ?? this.totalDebit,
      totalCredit: totalCredit ?? this.totalCredit,
      isDeleted: isDeleted ?? this.isDeleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      details: details ?? this.details,
    );
  }

  /// إنشاء كائن من JSON
  factory JournalEntry.fromJson(Map<String, dynamic> json) {
    return JournalEntry(
      id: json['id'] as String,
      reference: json['reference'] as String,
      date: DateTime.parse(json['date'] as String),
      description: json['description'] as String,
      status: json['status'] as String,
      type: json['type'] as String,
      totalDebit: (json['total_debit'] as num?)?.toDouble() ?? 0.0,
      totalCredit: (json['total_credit'] as num?)?.toDouble() ?? 0.0,
      isDeleted: (json['is_deleted'] as int) == 1,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      details: const [], // سيتم تعبئتها لاحقًا
    );
  }

  /// تحويل الكائن إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reference': reference,
      'date': date.toIso8601String().split('T').first,
      'description': description,
      'status': status,
      'type': type,
      'is_deleted': isDeleted ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JournalEntry &&
        other.id == id &&
        other.reference == reference &&
        other.date == date &&
        other.description == description &&
        other.status == status &&
        other.type == type &&
        other.totalDebit == totalDebit &&
        other.totalCredit == totalCredit &&
        other.isDeleted == isDeleted &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        listEquals(other.details, details);
  }

  @override
  int get hashCode {
    return id.hashCode ^
        reference.hashCode ^
        date.hashCode ^
        description.hashCode ^
        status.hashCode ^
        type.hashCode ^
        totalDebit.hashCode ^
        totalCredit.hashCode ^
        isDeleted.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        Object.hashAll(details);
  }
}
