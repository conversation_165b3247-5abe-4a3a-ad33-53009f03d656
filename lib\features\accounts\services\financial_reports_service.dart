import 'package:flutter/material.dart';

import '../../users/services/activity_log_service.dart';
import '../../../core/models/financial_report.dart';
import '../../../core/database/database_service.dart';

/// أنواع التقارير المالية
enum FinancialReportType {
  /// قائمة المركز المالي (الميزانية)
  balanceSheet,

  /// قائمة الدخل
  incomeStatement,

  /// قائمة التدفقات النقدية
  cashFlow,

  /// قائمة التغيرات في حقوق الملكية
  equityChanges,

  /// دفتر الأستاذ العام
  generalLedger,

  /// ميزان المراجعة
  trialBalance,
}

/// خدمة إدارة التقارير المالية
/// تقوم بإنشاء التقارير المالية المختلفة وفق المعايير المحاسبية العالمية
class FinancialReportsService {
  static final FinancialReportsService _instance =
      FinancialReportsService._internal();
  factory FinancialReportsService() => _instance;
  FinancialReportsService._internal();

  // سيتم استخدام هذا المتغير في المستقبل عند تنفيذ وظائف متقدمة
  // final DatabaseHelper _databaseHelper = DatabaseHelper();
  final ActivityLogService _activityLogService = ActivityLogService();
  final DatabaseService _db = DatabaseService.instance;

  /// إنشاء تقرير مالي بناءً على نوع التقرير والفترة الزمنية
  Future<FinancialReport> generateReport({
    required FinancialReportType reportType,
    required DateTimeRange? period,
    String? userId,
  }) async {
    // تحديد الفترة الزمنية الافتراضية إذا لم يتم توفيرها
    final reportPeriod = period ??
        DateTimeRange(
          start: DateTime.now().subtract(const Duration(days: 30)),
          end: DateTime.now(),
        );

    try {
      // إنشاء التقرير المناسب حسب النوع
      FinancialReport report;

      switch (reportType) {
        case FinancialReportType.balanceSheet:
          report =
              await _generateBalanceSheet(reportPeriod, userId ?? 'system');
          break;
        case FinancialReportType.incomeStatement:
          report =
              await _generateIncomeStatement(reportPeriod, userId ?? 'system');
          break;
        case FinancialReportType.cashFlow:
          report = await _generateCashFlow(reportPeriod, userId ?? 'system');
          break;
        case FinancialReportType.equityChanges:
          report =
              await _generateEquityChanges(reportPeriod, userId ?? 'system');
          break;
        default:
          throw Exception('نوع التقرير غير مدعوم');
      }

      // تسجيل نشاط إنشاء التقرير
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'create',
        module: 'financial_report',
        details: 'إنشاء تقرير ${report.title}',
      );

      return report;
    } catch (e) {
      // إنشاء تقرير فارغ في حالة الخطأ
      switch (reportType) {
        case FinancialReportType.balanceSheet:
          return FinancialReport.balanceSheet(
            reportDate: reportPeriod.end,
            details: {},
            createdBy: userId ?? 'system',
          );
        case FinancialReportType.incomeStatement:
          return FinancialReport.incomeStatement(
            startDate: reportPeriod.start,
            endDate: reportPeriod.end,
            details: {},
            createdBy: userId ?? 'system',
          );
        case FinancialReportType.cashFlow:
          return FinancialReport.cashFlow(
            startDate: reportPeriod.start,
            endDate: reportPeriod.end,
            details: {},
            createdBy: userId ?? 'system',
          );
        case FinancialReportType.equityChanges:
          return FinancialReport.empty();
        default:
          throw Exception('نوع التقرير غير مدعوم');
      }
    }
  }

  /// حفظ التقرير المالي في قاعدة البيانات
  Future<bool> saveReport(FinancialReport report) async {
    try {
      await _db.insert('financial_reports', report.toMap());

      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على التقارير المالية المحفوظة
  Future<List<FinancialReport>> getSavedReports({
    FinancialReportType? reportType,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 20,
  }) async {
    try {
      // بناء شروط الاستعلام
      final List<String> whereConditions = ['is_deleted = 0'];
      final List<dynamic> whereArgs = [];

      if (reportType != null) {
        whereConditions.add('type = ?');
        whereArgs.add(reportType.toString().split('.').last);
      }

      if (startDate != null) {
        whereConditions.add('start_date >= ?');
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereConditions.add('end_date <= ?');
        whereArgs.add(endDate.toIso8601String());
      }

      // تنفيذ الاستعلام
      final results = await _db.query(
        'financial_reports',
        where: whereConditions.join(' AND '),
        whereArgs: whereArgs,
        orderBy: 'created_at DESC',
        limit: limit,
      );

      // تحويل النتائج إلى كائنات التقارير المالية
      return results.map((result) => FinancialReport.fromMap(result)).toList();
    } catch (e) {
      return [];
    }
  }

  /// حذف تقرير مالي
  Future<bool> deleteReport(String reportId) async {
    try {
      await _db.update(
        'financial_reports',
        {'is_deleted': 1},
        where: 'id = ?',
        whereArgs: [reportId],
      );

      return true;
    } catch (e) {
      return false;
    }
  }

  /// إنشاء قائمة المركز المالي (الميزانية العمومية)
  Future<FinancialReport> _generateBalanceSheet(
      DateTimeRange period, String? userId) async {
    // إنشاء نموذج التقرير الفارغ
    final report = FinancialReport.balanceSheet(
      reportDate: period.end,
      details: {},
      createdBy: userId ?? 'system',
    );

    try {
      // الحصول على حسابات الأصول
      final assets = await _getAccountsByType(
        ['asset', 'current_asset', 'fixed_asset'],
        period,
      );

      // الحصول على حسابات الخصوم
      final liabilities = await _getAccountsByType(
        ['liability', 'current_liability', 'long_term_liability'],
        period,
      );

      // الحصول على حسابات حقوق الملكية
      final equity = await _getAccountsByType(
        ['equity', 'capital', 'retained_earnings'],
        period,
      );

      // حساب المجاميع
      final totalAssets = _sumBalances(assets);
      final totalLiabilities = _sumBalances(liabilities);
      final totalEquity = _sumBalances(equity);

      // تجهيز بيانات التقرير
      final Map<String, dynamic> summary = {
        'totalAssets': totalAssets,
        'totalLiabilities': totalLiabilities,
        'totalEquity': totalEquity,
        'totalLiabilitiesAndEquity': totalLiabilities + totalEquity,
        'isBalanced':
            (totalAssets - (totalLiabilities + totalEquity)).abs() < 0.01,
        'currency': 'SAR',
      };

      final List<Map<String, dynamic>> details = [
        {
          'section': 'الأصول',
          'accounts': assets,
        },
        {
          'section': 'الخصوم',
          'accounts': liabilities,
        },
        {
          'section': 'حقوق الملكية',
          'accounts': equity,
        },
      ];

      // إنشاء التقرير النهائي
      return report.copyWith(
        summary: summary,
        details: {'sections': details},
      );
    } catch (e) {
      // في حالة الخطأ، إرجاع التقرير الفارغ
      return report;
    }
  }

  /// إنشاء قائمة الدخل (الأرباح والخسائر)
  Future<FinancialReport> _generateIncomeStatement(
      DateTimeRange period, String? userId) async {
    // إنشاء نموذج التقرير الفارغ
    final report = FinancialReport.incomeStatement(
      startDate: period.start,
      endDate: period.end,
      details: {},
      createdBy: userId ?? 'system',
    );

    try {
      // الحصول على حسابات الإيرادات
      final revenues = await _getAccountsByType(
        ['revenue', 'income', 'sales'],
        period,
      );

      // الحصول على حسابات المصروفات
      final expenses = await _getAccountsByType(
        ['expense', 'cost_of_sales', 'operating_expense'],
        period,
      );

      // حساب المجاميع
      final totalRevenues = _sumBalances(revenues);
      final totalExpenses = _sumBalances(expenses);
      final netIncome = totalRevenues - totalExpenses;

      // حساب هامش الربح
      final profitMargin =
          totalRevenues > 0 ? (netIncome / totalRevenues) * 100 : 0;

      // تجهيز بيانات التقرير
      final Map<String, dynamic> summary = {
        'totalRevenues': totalRevenues,
        'totalExpenses': totalExpenses,
        'netIncome': netIncome,
        'profitMargin': profitMargin,
        'currency': 'SAR',
      };

      final List<Map<String, dynamic>> details = [
        {
          'section': 'الإيرادات',
          'accounts': revenues,
        },
        {
          'section': 'المصروفات',
          'accounts': expenses,
        },
      ];

      // إنشاء التقرير النهائي
      return report.copyWith(
        summary: summary,
        details: {'sections': details},
      );
    } catch (e) {
      // في حالة الخطأ، إرجاع التقرير الفارغ
      return report;
    }
  }

  /// إنشاء قائمة التدفقات النقدية
  Future<FinancialReport> _generateCashFlow(
      DateTimeRange period, String? userId) async {
    // إنشاء نموذج التقرير الفارغ
    final report = FinancialReport.cashFlow(
      startDate: period.start,
      endDate: period.end,
      details: {},
      createdBy: userId ?? 'system',
    );

    try {
      // الحصول على التدفقات النقدية من الأنشطة التشغيلية
      final operatingActivities = await _getCashFlowByActivity(
        'operating',
        period,
      );

      // الحصول على التدفقات النقدية من الأنشطة الاستثمارية
      final investingActivities = await _getCashFlowByActivity(
        'investing',
        period,
      );

      // الحصول على التدفقات النقدية من الأنشطة التمويلية
      final financingActivities = await _getCashFlowByActivity(
        'financing',
        period,
      );

      // حساب المجاميع
      final totalOperating = _sumCashFlows(operatingActivities);
      final totalInvesting = _sumCashFlows(investingActivities);
      final totalFinancing = _sumCashFlows(financingActivities);
      final netCashFlow = totalOperating + totalInvesting + totalFinancing;

      // الحصول على الرصيد النقدي في بداية الفترة
      final beginningCashBalance = await _getBeginningCashBalance(period.start);
      final endingCashBalance = beginningCashBalance + netCashFlow;

      // تجهيز بيانات التقرير
      final Map<String, dynamic> summary = {
        'beginningCashBalance': beginningCashBalance,
        'totalOperatingActivities': totalOperating,
        'totalInvestingActivities': totalInvesting,
        'totalFinancingActivities': totalFinancing,
        'netCashFlow': netCashFlow,
        'endingCashBalance': endingCashBalance,
        'currency': 'SAR',
      };

      final List<Map<String, dynamic>> details = [
        {
          'section': 'الأنشطة التشغيلية',
          'entries': operatingActivities,
        },
        {
          'section': 'الأنشطة الاستثمارية',
          'entries': investingActivities,
        },
        {
          'section': 'الأنشطة التمويلية',
          'entries': financingActivities,
        },
      ];

      // إنشاء التقرير النهائي
      return report.copyWith(
        summary: summary,
        details: {'sections': details},
      );
    } catch (e) {
      // في حالة الخطأ، إرجاع التقرير الفارغ
      return report;
    }
  }

  /// إنشاء قائمة التغيرات في حقوق الملكية
  Future<FinancialReport> _generateEquityChanges(
      DateTimeRange period, String? userId) async {
    // إنشاء نموذج التقرير الفارغ
    final report = FinancialReport.empty();

    try {
      // الحصول على رصيد بداية الفترة لحسابات حقوق الملكية
      final startingBalances = await _getAccountsByType(
        ['equity', 'capital', 'retained_earnings'],
        DateTimeRange(
          start: period.start
              .subtract(const Duration(days: 365)), // سنة قبل بداية الفترة
          end: period.start
              .subtract(const Duration(days: 1)), // يوم قبل بداية الفترة
        ),
      );

      // الحصول على التغيرات خلال الفترة
      final changes = await _getEquityChanges(period);

      // الحصول على صافي الدخل للفترة
      final netIncome = await _getNetIncomeForPeriod(period);

      // حساب المجاميع
      final totalBeginningEquity = _sumBalances(startingBalances);
      final totalChanges = _sumBalances(changes);
      final totalEndingEquity = totalBeginningEquity + totalChanges + netIncome;

      // تجهيز بيانات التقرير
      final Map<String, dynamic> summary = {
        'totalBeginningEquity': totalBeginningEquity,
        'totalEquityChanges': totalChanges,
        'netIncome': netIncome,
        'totalEndingEquity': totalEndingEquity,
        'currency': 'SAR',
      };

      final List<Map<String, dynamic>> details = [
        {
          'section': 'رصيد بداية الفترة',
          'accounts': startingBalances,
        },
        {
          'section': 'التغيرات خلال الفترة',
          'accounts': changes,
        },
        {
          'section': 'صافي الدخل للفترة',
          'amount': netIncome,
        },
      ];

      // إنشاء التقرير النهائي
      return report.copyWith(
        summary: summary,
        details: {'sections': details},
      );
    } catch (e) {
      // في حالة الخطأ، إرجاع التقرير الفارغ
      return report;
    }
  }

  /// الحصول على الحسابات حسب النوع
  Future<List<Map<String, dynamic>>> _getAccountsByType(
    List<String> accountTypes,
    DateTimeRange period,
  ) async {
    try {
      // استعلام القيود المحاسبية للحصول على أرصدة الحسابات
      final query = '''
        SELECT
          a.id,
          a.code,
          a.name,
          a.type,
          COALESCE(SUM(CASE WHEN je.debit_credit = 'debit' THEN jel.amount ELSE -jel.amount END), 0) as balance
        FROM accounts a
        LEFT JOIN journal_entry_lines jel ON a.id = jel.account_id
        LEFT JOIN journal_entries je ON jel.journal_entry_id = je.id
        WHERE a.type IN (${accountTypes.map((_) => '?').join(',')})
          AND (je.date IS NULL OR (je.date >= ? AND je.date <= ?))
          AND a.is_deleted = 0
          AND (je.is_deleted IS NULL OR je.is_deleted = 0)
        GROUP BY a.id
        ORDER BY a.code
      ''';

      final args = [
        ...accountTypes,
        period.start.toIso8601String(),
        period.end.toIso8601String(),
      ];

      final results = await _db.rawQuery(query, args);

      return results
          .map((result) => {
                'id': result['id'] as String,
                'code': result['code'] as String,
                'name': result['name'] as String,
                'type': result['type'] as String,
                'balance': result['balance'] is int
                    ? (result['balance'] as int).toDouble()
                    : result['balance'] as double,
              })
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على التدفقات النقدية حسب نوع النشاط
  Future<List<Map<String, dynamic>>> _getCashFlowByActivity(
    String activityType,
    DateTimeRange period,
  ) async {
    try {
      // استعلام القيود المحاسبية للتدفقات النقدية
      const query = '''
        SELECT
          je.id,
          je.number as entry_number,
          je.date as entry_date,
          je.description,
          COALESCE(SUM(CASE WHEN je.debit_credit = 'debit' THEN jel.amount ELSE -jel.amount END), 0) as amount
        FROM journal_entries je
        JOIN journal_entry_lines jel ON je.id = jel.journal_entry_id
        JOIN accounts a ON jel.account_id = a.id
        WHERE je.date >= ? AND je.date <= ?
          AND je.is_deleted = 0
          AND a.is_cash_equivalent = 1
          AND je.cash_flow_type = ?
        GROUP BY je.id
        ORDER BY je.date
      ''';

      final args = [
        period.start.toIso8601String(),
        period.end.toIso8601String(),
        activityType,
      ];

      final results = await _db.rawQuery(query, args);

      return results
          .map((result) => {
                'id': result['id'] as String,
                'entry_number': result['entry_number'] as String,
                'entry_date': result['entry_date'] as String,
                'description': result['description'] as String,
                'amount': result['amount'] is int
                    ? (result['amount'] as int).toDouble()
                    : result['amount'] as double,
              })
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على الرصيد النقدي في بداية الفترة
  Future<double> _getBeginningCashBalance(DateTime date) async {
    try {
      const query = '''
        SELECT COALESCE(SUM(
          CASE
            WHEN je.debit_credit = 'debit' THEN jel.amount
            ELSE -jel.amount
          END), 0) as balance
        FROM accounts a
        LEFT JOIN journal_entry_lines jel ON a.id = jel.account_id
        LEFT JOIN journal_entries je ON jel.journal_entry_id = je.id
        WHERE a.is_cash_equivalent = 1
          AND a.is_deleted = 0
          AND (je.is_deleted IS NULL OR je.is_deleted = 0)
          AND (je.date IS NULL OR je.date < ?)
      ''';

      final results = await _db.rawQuery(query, [date.toIso8601String()]);

      if (results.isNotEmpty) {
        final balance = results.first['balance'];
        return balance is int ? balance.toDouble() : balance as double;
      }

      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على التغيرات في حقوق الملكية خلال الفترة
  Future<List<Map<String, dynamic>>> _getEquityChanges(
      DateTimeRange period) async {
    try {
      const query = '''
        SELECT
          a.id,
          a.code,
          a.name,
          a.type,
          COALESCE(SUM(CASE WHEN je.debit_credit = 'debit' THEN jel.amount ELSE -jel.amount END), 0) as balance
        FROM accounts a
        JOIN journal_entry_lines jel ON a.id = jel.account_id
        JOIN journal_entries je ON jel.journal_entry_id = je.id
        WHERE a.type IN ('equity', 'capital', 'retained_earnings')
          AND je.date >= ? AND je.date <= ?
          AND a.is_deleted = 0
          AND je.is_deleted = 0
          AND je.type != 'closing'
        GROUP BY a.id
        ORDER BY a.code
      ''';

      final results = await _db.rawQuery(query, [
        period.start.toIso8601String(),
        period.end.toIso8601String(),
      ]);

      return results
          .map((result) => {
                'id': result['id'] as String,
                'code': result['code'] as String,
                'name': result['name'] as String,
                'type': result['type'] as String,
                'balance': result['balance'] is int
                    ? (result['balance'] as int).toDouble()
                    : result['balance'] as double,
              })
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على صافي الدخل للفترة
  Future<double> _getNetIncomeForPeriod(DateTimeRange period) async {
    try {
      const query = '''
        SELECT
          COALESCE(SUM(
            CASE
              WHEN a.type IN ('income', 'revenue', 'sales') THEN
                CASE WHEN je.debit_credit = 'credit' THEN jel.amount ELSE -jel.amount END
              WHEN a.type IN ('expense', 'cost_of_sales', 'operating_expense') THEN
                CASE WHEN je.debit_credit = 'debit' THEN -jel.amount ELSE jel.amount END
              ELSE 0
            END
          ), 0) as net_income
        FROM journal_entry_lines jel
        JOIN journal_entries je ON jel.journal_entry_id = je.id
        JOIN accounts a ON jel.account_id = a.id
        WHERE a.type IN ('income', 'revenue', 'sales', 'expense', 'cost_of_sales', 'operating_expense')
          AND je.date >= ? AND je.date <= ?
          AND a.is_deleted = 0
          AND je.is_deleted = 0
      ''';

      final results = await _db.rawQuery(query, [
        period.start.toIso8601String(),
        period.end.toIso8601String(),
      ]);

      if (results.isNotEmpty) {
        final netIncome = results.first['net_income'];
        return netIncome is int ? netIncome.toDouble() : netIncome as double;
      }

      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  /// حساب مجموع الأرصدة للحسابات
  double _sumBalances(List<Map<String, dynamic>> accounts) {
    return accounts.fold(
        0.0, (sum, account) => sum + (account['balance'] as double));
  }

  /// حساب مجموع التدفقات النقدية
  double _sumCashFlows(List<Map<String, dynamic>> flows) {
    return flows.fold(0.0, (sum, flow) => sum + (flow['amount'] as double));
  }

  // Helper method to format period from DateTimeRange to string
  // Kept for future use
  /*
  String _formatPeriod(DateTimeRange range) {
    final formatter = DateFormat('yyyy-MM-dd');
    return '${formatter.format(range.start)} to ${formatter.format(range.end)}';
  }
  */
}
