import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/theme/index.dart';
import '../../../core/utils/index.dart';

import '../../../core/models/simple_tie.dart';
import '../../accounts/presenters/account_presenter.dart';
import '../../../core/widgets/index.dart';
import '../../currencies/presenters/currency_presenter.dart';
import '../presenters/simple_tie_presenter.dart';

class SimpleTieScreen extends StatefulWidget {
  final SimpleTie? simpleTie;

  const SimpleTieScreen({Key? key, this.simpleTie}) : super(key: key);

  @override
  State<SimpleTieScreen> createState() => _SimpleTieScreenState();
}

class _SimpleTieScreenState extends State<SimpleTieScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  String? _selectedFromAccountId;
  String? _selectedToAccountId;
  String? _selectedCurrencyId;
  bool _isLoading = false;
  bool _isSaving = false;
  List<SimpleTie> _filteredSimpleTies = [];
  bool _showSearchField = false;

  // استخدام التحميل الكسول
  late final AccountPresenter _accountPresenter;
  late final CurrencyPresenter _currencyPresenter;
  late final SimpleTiePresenter _simpleTiePresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _accountPresenter = AppProviders.getLazyPresenter<AccountPresenter>(
        () => AccountPresenter());
    _currencyPresenter = AppProviders.getLazyPresenter<CurrencyPresenter>(
        () => CurrencyPresenter());
    _simpleTiePresenter = AppProviders.getLazyPresenter<SimpleTiePresenter>(
        () => SimpleTiePresenter());
    _loadData();

    // إذا كان هناك قيد للتعديل، قم بتعبئة النموذج
    if (widget.simpleTie != null) {
      _amountController.text = widget.simpleTie!.amount.toString();
      _notesController.text = widget.simpleTie!.notes ?? '';
      _selectedDate = widget.simpleTie!.date;
      _selectedFromAccountId = widget.simpleTie!.fromAccountId;
      _selectedToAccountId = widget.simpleTie!.toAccountId;
      _selectedCurrencyId = widget.simpleTie!.currencyId;
    }

    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    _filterSimpleTies();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الحسابات
      await _loadAccounts();
      if (!mounted) return;

      // تحميل العملات
      await _loadCurrencies();
      if (!mounted) return;

      // تحميل القيود البسيطة
      await _loadSimpleTies();
      if (!mounted) return;
    } catch (e) {
      if (mounted) {
        _showSnackBar('فشل في تحميل البيانات: ${e.toString()}',
            isSuccess: false);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تحميل الحسابات
  Future<void> _loadAccounts() async {
    await _accountPresenter.loadAccounts();
  }

  /// تحميل العملات
  Future<void> _loadCurrencies() async {
    await _currencyPresenter.loadCurrencies();

    // تعيين العملة الافتراضية
    if (!mounted) return;

    if (_selectedCurrencyId == null &&
        _currencyPresenter.defaultCurrency != null) {
      setState(() {
        _selectedCurrencyId = _currencyPresenter.defaultCurrency!.id;
      });
    }
  }

  /// تحميل القيود البسيطة
  Future<void> _loadSimpleTies() async {
    await _simpleTiePresenter.loadSimpleTies();

    if (!mounted) return;

    setState(() {
      _filteredSimpleTies = _simpleTiePresenter.state.simpleTies;
    });
  }

  /// عرض رسالة للمستخدم
  void _showSnackBar(String message, {bool isSuccess = true}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isSuccess ? AppColors.success : AppColors.error,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _filterSimpleTies() {
    if (!mounted) return;

    final query = _searchController.text.toLowerCase();

    if (query.isEmpty) {
      setState(() {
        _filteredSimpleTies = _simpleTiePresenter.state.simpleTies;
      });
      return;
    }

    setState(() {
      _filteredSimpleTies =
          _simpleTiePresenter.state.simpleTies.where((simpleTie) {
        // البحث في رقم القيد والحسابات والملاحظات
        final number = simpleTie.number?.toLowerCase() ?? '';
        final notes = simpleTie.notes?.toLowerCase() ?? '';

        return number.contains(query) || notes.contains(query);
      }).toList();
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveSimpleTie() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من اختيار الحسابات
    if (_selectedFromAccountId == null) {
      _showSnackBar('يرجى اختيار الحساب المدين', isSuccess: false);
      return;
    }

    if (_selectedToAccountId == null) {
      _showSnackBar('يرجى اختيار الحساب الدائن', isSuccess: false);
      return;
    }

    // التحقق من أن الحسابين مختلفين
    if (_selectedFromAccountId == _selectedToAccountId) {
      _showSnackBar('يجب أن يكون الحساب المدين مختلفًا عن الحساب الدائن',
          isSuccess: false);
      return;
    }

    // التحقق من اختيار العملة
    if (_selectedCurrencyId == null) {
      _showSnackBar('يرجى اختيار العملة', isSuccess: false);
      return;
    }

    // عرض مربع حوار التأكيد
    final bool? confirm = await _showConfirmationDialog();
    if (confirm != true) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // إنشاء قيد بسيط جديد
      final simpleTie = SimpleTie(
        id: widget.simpleTie?.id,
        amount: double.parse(_amountController.text),
        currencyId: _selectedCurrencyId!,
        fromAccountId: _selectedFromAccountId!,
        toAccountId: _selectedToAccountId!,
        date: _selectedDate,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        userId: 'admin', // يجب استبداله بمعرف المستخدم الحالي
      );

      // حفظ القيد البسيط
      if (!mounted) return;

      await _saveTieToDatabase(simpleTie, _simpleTiePresenter);
    } catch (e) {
      if (mounted) {
        _showSnackBar('فشل في حفظ القيد البسيط: ${e.toString()}',
            isSuccess: false);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// عرض مربع حوار التأكيد
  Future<bool?> _showConfirmationDialog() async {
    return showDialog<bool>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('تأكيد الحفظ'),
        content: const Text('هل تريد حفظ القيد البسيط؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(dialogContext).pop(true),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// حفظ القيد البسيط في قاعدة البيانات
  Future<void> _saveTieToDatabase(
      SimpleTie simpleTie, SimpleTiePresenter presenter) async {
    bool success;

    if (widget.simpleTie == null) {
      // إضافة قيد بسيط جديد
      final id = await presenter.createSimpleTie(simpleTie);
      success = id != null;
    } else {
      // تعديل قيد بسيط موجود
      success = await presenter.updateSimpleTie(simpleTie);
    }

    // التحقق من حالة الـ widget
    if (!mounted) return;

    if (success) {
      _showSnackBar('تم حفظ القيد البسيط بنجاح', isSuccess: true);

      // إعادة تحميل القيود البسيطة
      await presenter.refreshSimpleTies();

      // التحقق من حالة الـ widget مرة أخرى
      if (!mounted) return;

      setState(() {
        _filteredSimpleTies = presenter.state.simpleTies;
      });

      // إعادة تعيين النموذج إذا كان إضافة جديدة
      if (widget.simpleTie == null) {
        _resetForm();
      } else {
        Navigator.pop(context);
      }
    } else {
      _showSnackBar('فشل في حفظ القيد البسيط', isSuccess: false);
    }
  }

  void _resetForm() {
    _amountController.clear();
    _notesController.clear();
    setState(() {
      _selectedDate = DateTime.now();
      _selectedFromAccountId = null;
      _selectedToAccountId = null;

      // إعادة تعيين العملة إلى العملة الافتراضية
      if (_currencyPresenter.defaultCurrency != null) {
        _selectedCurrencyId = _currencyPresenter.defaultCurrency!.id;
      }
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.simpleTie != null;

    return Scaffold(
      appBar: AkAppBar(
        title: isEditing ? 'تعديل قيد بسيط' : 'قيد بسيط',
        showBackButton: true,
        actions: [
          // أيقونة البحث
          IconButton(
            icon: Icon(_showSearchField ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                  _filterSimpleTies();
                }
              });
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث)
                if (_showSearchField)
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: AkSearchInput(
                      controller: _searchController,
                      hint: 'بحث عن قيد بسيط...',
                      onChanged: (value) {
                        _filterSimpleTies();
                      },
                    ),
                  ),
                // نموذج إضافة القيد البسيط
                Expanded(
                  flex: 1,
                  child: SingleChildScrollView(
                    padding: EdgeInsets.zero,
                    child: AkCard(
                      padding: EdgeInsets.zero,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4.0, vertical: 4.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // المبلغ والعملة
                              Row(
                                children: [
                                  // المبلغ
                                  Expanded(
                                    flex: 2,
                                    child: AkTextInput(
                                      controller: _amountController,
                                      label: 'المبلغ',
                                      hint: 'أدخل المبلغ',
                                      isRequired: true,
                                      keyboardType:
                                          const TextInputType.numberWithOptions(
                                              decimal: true),
                                      prefixIcon: Icons.attach_money,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'يرجى إدخال المبلغ';
                                        }
                                        if (double.tryParse(value) == null) {
                                          return 'يرجى إدخال رقم صحيح';
                                        }
                                        if (double.parse(value) <= 0) {
                                          return 'يجب أن يكون المبلغ أكبر من صفر';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  // العملة
                                  Expanded(
                                    flex: 1,
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedCurrencyId,
                                      decoration: InputDecoration(
                                        labelText: 'العملة *',
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                              Layout.defaultRadius),
                                        ),
                                        filled: true,
                                        prefixIcon:
                                            const Icon(Icons.currency_exchange),
                                      ),
                                      items: _currencyPresenter.currencies
                                          .map((currency) {
                                        return DropdownMenuItem<String>(
                                          value: currency.id,
                                          child: Text(
                                              '${currency.name} (${currency.symbol})'),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedCurrencyId = value;
                                        });
                                      },
                                      validator: (value) {
                                        if (value == null) {
                                          return 'يرجى اختيار العملة';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: AppDimensions.spacing4),

                              // الحساب المدين (من)
                              DropdownButtonFormField<String>(
                                value: _selectedFromAccountId,
                                decoration: InputDecoration(
                                  labelText: 'الحساب المدين (من) *',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                        Layout.defaultRadius),
                                  ),
                                  filled: true,
                                  prefixIcon: const Icon(Icons.account_balance),
                                ),
                                items:
                                    _accountPresenter.accounts.map((account) {
                                  return DropdownMenuItem<String>(
                                    value: account['id'].toString(),
                                    child: Text(account['name']),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedFromAccountId = value;
                                  });
                                },
                                validator: (value) {
                                  if (value == null) {
                                    return 'يرجى اختيار الحساب المدين';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: AppDimensions.spacing4),

                              // الحساب الدائن (إلى)
                              DropdownButtonFormField<String>(
                                value: _selectedToAccountId,
                                decoration: InputDecoration(
                                  labelText: 'الحساب الدائن (إلى) *',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                        Layout.defaultRadius),
                                  ),
                                  filled: true,
                                  prefixIcon: const Icon(Icons.account_balance),
                                ),
                                items:
                                    _accountPresenter.accounts.map((account) {
                                  return DropdownMenuItem<String>(
                                    value: account['id'].toString(),
                                    child: Text(account['name']),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedToAccountId = value;
                                  });
                                },
                                validator: (value) {
                                  if (value == null) {
                                    return 'يرجى اختيار الحساب الدائن';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: AppDimensions.spacing4),

                              // التاريخ
                              InkWell(
                                onTap: () => _selectDate(context),
                                child: InputDecorator(
                                  decoration: InputDecoration(
                                    labelText: 'التاريخ',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          Layout.defaultRadius),
                                    ),
                                    filled: true,
                                    prefixIcon:
                                        const Icon(Icons.calendar_today),
                                  ),
                                  child: Text(
                                    '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}',
                                  ),
                                ),
                              ),
                              const SizedBox(height: AppDimensions.spacing4),

                              // الملاحظات
                              AkTextInput(
                                controller: _notesController,
                                label: 'ملاحظات',
                                hint: 'أدخل ملاحظات إضافية (اختياري)',
                                maxLines: 2,
                                prefixIcon: Icons.note,
                              ),
                              const SizedBox(height: AppDimensions.spacing4),

                              // زر الحفظ
                              Center(
                                child: SizedBox(
                                  width:
                                      Layout.isTablet() ? 200 : double.infinity,
                                  height: 45,
                                  child: ElevatedButton.icon(
                                    onPressed:
                                        _isSaving ? null : _saveSimpleTie,
                                    icon: _isSaving
                                        ? const SizedBox(
                                            width: 18,
                                            height: 18,
                                            child: CircularProgressIndicator(
                                                color: AppColors
                                                    .lightTextSecondary,
                                                strokeWidth: 2))
                                        : const Icon(Icons.save, size: 20),
                                    label: Text(
                                      isEditing ? 'تحديث القيد' : 'حفظ',
                                      style: const AppTypography(fontSize: 15),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.primary,
                                      foregroundColor: AppColors.onPrimary,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // جدول القيود
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 4.0, vertical: 4.0),
                    child: AkCard(
                      padding: EdgeInsets.zero,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4.0, vertical: 4.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4.0, vertical: 2.0),
                              child: Text(
                                'قائمة القيود البسيطة',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                            ),
                            Expanded(
                              child: AdvancedDataTable(
                                columns: const [
                                  DataColumn(label: Text('إجراءات')),
                                  DataColumn(label: Text('م')),
                                  DataColumn(label: Text('الرقم')),
                                  DataColumn(label: Text('التاريخ')),
                                  DataColumn(label: Text('من حساب')),
                                  DataColumn(label: Text('إلى حساب')),
                                  DataColumn(label: Text('المبلغ')),
                                  DataColumn(label: Text('العملة')),
                                  DataColumn(label: Text('ملاحظات')),
                                ],
                                rows: _filteredSimpleTies
                                    .asMap()
                                    .entries
                                    .map((entry) {
                                  final simpleTie = entry.value;
                                  final isHighlighted =
                                      widget.simpleTie != null &&
                                          widget.simpleTie!.id == simpleTie.id;

                                  // الحصول على أسماء الحسابات
                                  final fromAccount = _accountPresenter.accounts
                                      .firstWhere(
                                          (a) =>
                                              a['id'].toString() ==
                                              simpleTie.fromAccountId,
                                          orElse: () => {'name': 'غير معروف'});
                                  final toAccount = _accountPresenter.accounts
                                      .firstWhere(
                                          (a) =>
                                              a['id'].toString() ==
                                              simpleTie.toAccountId,
                                          orElse: () => {'name': 'غير معروف'});

                                  // الحصول على رمز العملة
                                  final currency = _currencyPresenter.currencies
                                      .firstWhere(
                                          (c) => c.id == simpleTie.currencyId,
                                          orElse: () => _currencyPresenter
                                              .currencies.first);

                                  return DataRow(
                                    color: isHighlighted
                                        ? WidgetStateProperty.all(
                                            Theme.of(context)
                                                .primaryColor
                                                .withValues(alpha: 0.1))
                                        : null,
                                    cells: [
                                      DataCell(
                                        PopupMenuButton<String>(
                                          icon: const Icon(Icons.more_vert),
                                          onSelected: (value) async {
                                            if (value == 'edit') {
                                              // تحرير القيد
                                              Navigator.of(context)
                                                  .push(
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      SimpleTieScreen(
                                                          simpleTie: simpleTie),
                                                ),
                                              )
                                                  .then((_) {
                                                _loadData();
                                              });
                                            } else if (value == 'delete') {
                                              // حذف القيد بعد التأكيد
                                              final confirm =
                                                  await showDialog<bool>(
                                                context: context,
                                                builder: (context) =>
                                                    AlertDialog(
                                                  title:
                                                      const Text('تأكيد الحذف'),
                                                  content: const Text(
                                                      'هل أنت متأكد من حذف هذا القيد؟'),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () =>
                                                          Navigator.of(context)
                                                              .pop(false),
                                                      child:
                                                          const Text('إلغاء'),
                                                    ),
                                                    ElevatedButton(
                                                      onPressed: () =>
                                                          Navigator.of(context)
                                                              .pop(true),
                                                      style: ElevatedButton
                                                          .styleFrom(
                                                        backgroundColor:
                                                            AppColors.error,
                                                      ),
                                                      child: const Text('حذف'),
                                                    ),
                                                  ],
                                                ),
                                              );

                                              if (confirm == true) {
                                                try {
                                                  final success =
                                                      await _simpleTiePresenter
                                                          .deleteSimpleTie(
                                                              simpleTie.id);

                                                  if (success && mounted) {
                                                    _showSnackBar(
                                                        'تم حذف القيد بنجاح',
                                                        isSuccess: true);

                                                    // إعادة تحميل القيود
                                                    await _simpleTiePresenter
                                                        .refreshSimpleTies();
                                                    setState(() {
                                                      _filteredSimpleTies =
                                                          _simpleTiePresenter
                                                              .state.simpleTies;
                                                    });
                                                  }
                                                } catch (e) {
                                                  if (mounted) {
                                                    _showSnackBar(
                                                        'فشل في حذف القيد: ${e.toString()}',
                                                        isSuccess: false);
                                                  }
                                                }
                                              }
                                            }
                                          },
                                          itemBuilder: (context) => [
                                            const PopupMenuItem<String>(
                                              value: 'edit',
                                              child: Row(
                                                children: [
                                                  Icon(Icons.edit),
                                                  SizedBox(width: 8),
                                                  Text('تعديل'),
                                                ],
                                              ),
                                            ),
                                            const PopupMenuItem<String>(
                                              value: 'delete',
                                              child: Row(
                                                children: [
                                                  Icon(Icons.delete,
                                                      color: AppColors.error),
                                                  SizedBox(width: 8),
                                                  Text('حذف'),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      DataCell(Text('${entry.key + 1}')),
                                      DataCell(Text(simpleTie.number ?? '-')),
                                      DataCell(Text(
                                          '${simpleTie.date.year}-${simpleTie.date.month.toString().padLeft(2, '0')}-${simpleTie.date.day.toString().padLeft(2, '0')}')),
                                      DataCell(Text(fromAccount['name'])),
                                      DataCell(Text(toAccount['name'])),
                                      DataCell(Text(
                                          simpleTie.amount.toStringAsFixed(2))),
                                      DataCell(Text(currency.symbol ?? '')),
                                      DataCell(Text(simpleTie.notes ?? '-')),
                                    ],
                                  );
                                }).toList(),
                                isLoading: _simpleTiePresenter.state.isLoading,
                                showCellBorder: true,
                                zebraPattern: true,
                                headerBackgroundColor: AppColors.primary,
                                headerTextColor: AppColors.onPrimary,
                                showRowNumbers: false,
                                emptyMessage: 'لا توجد قيود بسيطة',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
