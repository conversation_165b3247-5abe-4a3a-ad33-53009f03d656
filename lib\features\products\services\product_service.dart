import 'package:uuid/uuid.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/models/product.dart';

/// خدمة المنتجات
class ProductService {
  final DatabaseHelper _db = DatabaseHelper();

  /// الحصول على جميع المنتجات
  Future<List<Product>> getAllProducts() async {
    try {
      final db = await _db.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'products',
        where: 'is_deleted = ?',
        whereArgs: [0],
      );

      return List.generate(maps.length, (i) {
        return Product.fromMap(maps[i]);
      });
    } catch (e) {
      AppLogger.error('خطأ في الحصول على المنتجات: $e');
      return [];
    }
  }

  /// الحصول على منتج بواسطة المعرف
  Future<Product?> getProductById(String id) async {
    try {
      final db = await _db.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'products',
        where: 'id = ? AND is_deleted = ?',
        whereArgs: [id, 0],
      );

      if (maps.isEmpty) {
        return null;
      }

      return Product.fromMap(maps.first);
    } catch (e) {
      AppLogger.error('خطأ في الحصول على المنتج: $e');
      return null;
    }
  }

  /// إنشاء منتج جديد
  Future<bool> createProduct(Product product) async {
    try {
      final db = await _db.database;
      final productMap = product.toMap();

      // إضافة معرف جديد إذا لم يكن موجودًا
      if (productMap['id'] == null) {
        productMap['id'] = const Uuid().v4();
      }

      // إضافة وقت الإنشاء
      productMap['created_at'] = DateTime.now().millisecondsSinceEpoch;

      await db.insert(
        'products',
        productMap,
      );

      return true;
    } catch (e) {
      AppLogger.error('خطأ في إنشاء المنتج: $e');
      return false;
    }
  }

  /// تحديث منتج
  Future<bool> updateProduct(Product product) async {
    try {
      final db = await _db.database;
      final productMap = product.toMap();

      // تحديث وقت التعديل
      productMap['updated_at'] = DateTime.now().millisecondsSinceEpoch;

      await db.update(
        'products',
        productMap,
        where: 'id = ?',
        whereArgs: [product.id],
      );

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تحديث المنتج: $e');
      return false;
    }
  }

  /// حذف منتج
  Future<bool> deleteProduct(String id) async {
    try {
      final db = await _db.database;

      // حذف منطقي (تحديث حقل is_deleted)
      await db.update(
        'products',
        {
          'is_deleted': 1,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      return true;
    } catch (e) {
      AppLogger.error('خطأ في حذف المنتج: $e');
      return false;
    }
  }

  /// الحصول على جميع فئات المنتجات
  Future<List<Map<String, dynamic>>> getAllCategories() async {
    try {
      final db = await _db.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'categories',
        where: 'is_deleted = ? AND type = ?',
        whereArgs: [0, 'product'],
      );

      return maps;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على فئات المنتجات: $e');
      return [];
    }
  }

  /// الحصول على جميع وحدات المنتجات
  Future<List<Map<String, dynamic>>> getAllUnits() async {
    try {
      final db = await _db.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'units',
        where: 'is_deleted = ?',
        whereArgs: [0],
      );

      return maps;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على وحدات المنتجات: $e');
      return [];
    }
  }

  /// تحديث مخزون المنتج
  Future<bool> updateProductStock(String productId, double quantity) async {
    try {
      final db = await _db.database;

      // الحصول على المنتج الحالي
      final product = await getProductById(productId);
      if (product == null) {
        return false;
      }

      // تحديث الكمية
      final newQuantity = product.quantity + quantity;

      await db.update(
        'products',
        {
          'quantity': newQuantity,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'id = ?',
        whereArgs: [productId],
      );

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تحديث مخزون المنتج: $e');
      return false;
    }
  }
}
