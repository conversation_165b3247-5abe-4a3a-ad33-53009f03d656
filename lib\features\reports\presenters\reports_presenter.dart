import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../core/database/database_service.dart';

import '../../../core/utils/error_tracker.dart';

/// مقدم التقارير
///
/// يوفر هذا المقدم واجهة للتعامل مع تقارير النظام المختلفة
/// مثل تقارير المبيعات والمشتريات والمخزون والمالية
class ReportsPresenter extends ChangeNotifier {
  final _db = DatabaseService.instance;

  bool _isLoading = false;
  String? _errorMessage;

  // حالة التحميل
  bool get isLoading => _isLoading;

  // رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// إنشاء فهارس للجداول المتعلقة بالتقارير لتحسين أداء الاستعلامات
  Future<void> _createReportsIndexes() async {
    try {
      // فهرس للمبيعات على حقل date
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_sales_date
        ON sales (date)
      ''');

      // فهرس للمبيعات على حقل status
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_sales_status
        ON sales (status)
      ''');

      // فهرس لبنود المبيعات على حقل sale_id
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_sale_items_sale_id
        ON sale_items (sale_id)
      ''');

      // فهرس لبنود المبيعات على حقل product_id
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_sale_items_product_id
        ON sale_items (product_id)
      ''');

      // فهرس مركب لبنود المبيعات على حقلي sale_id و product_id
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_sale_items_sale_product
        ON sale_items (sale_id, product_id)
      ''');
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في إنشاء فهارس التقارير',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// الحصول على المنتجات الأكثر مبيعًا
  Future<List<Map<String, dynamic>>> getTopSellingProducts({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      // التأكد من وجود الفهارس لتحسين أداء الاستعلام
      await _createReportsIndexes();

      // تنسيق التواريخ
      final startDateStr = startDate != null
          ? DateFormat('yyyy-MM-dd').format(startDate)
          : '2000-01-01';
      final endDateStr = endDate != null
          ? DateFormat('yyyy-MM-dd')
              .format(endDate.add(const Duration(days: 1)))
          : DateFormat('yyyy-MM-dd')
              .format(DateTime.now().add(const Duration(days: 1)));

      // بناء استعلام SQL للحصول على المنتجات الأكثر مبيعًا
      final results = await _db.rawQuery('''
        SELECT
          si.product_id,
          p.name as product_name,
          p.code,
          p.barcode,
          SUM(si.quantity) as quantity,
          SUM(si.total) as total_sales,
          COUNT(DISTINCT si.sale_id) as invoice_count,
          AVG(si.price) as average_price
        FROM
          sale_items si
        JOIN
          sales s ON si.sale_id = s.id
        JOIN
          products p ON si.product_id = p.id
        WHERE
          s.date BETWEEN ? AND ?
          AND s.status = 'completed'
          AND p.is_deleted = 0
        GROUP BY
          si.product_id
        ORDER BY
          quantity DESC
        LIMIT ?
      ''', [startDateStr, endDateStr, limit]);

      _isLoading = false;
      notifyListeners();

      return results;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage =
          'فشل في الحصول على المنتجات الأكثر مبيعًا: ${e.toString()}';
      ErrorTracker.captureError(
        'خطأ في الحصول على المنتجات الأكثر مبيعًا',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
      return [];
    }
  }

  /// الحصول على المنتجات الأكثر ربحية
  Future<List<Map<String, dynamic>>> getMostProfitableProducts({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      // التأكد من وجود الفهارس لتحسين أداء الاستعلام
      await _createReportsIndexes();

      // تنسيق التواريخ
      final startDateStr = startDate != null
          ? DateFormat('yyyy-MM-dd').format(startDate)
          : '2000-01-01';
      final endDateStr = endDate != null
          ? DateFormat('yyyy-MM-dd')
              .format(endDate.add(const Duration(days: 1)))
          : DateFormat('yyyy-MM-dd')
              .format(DateTime.now().add(const Duration(days: 1)));

      // بناء استعلام SQL للحصول على المنتجات الأكثر ربحية
      final results = await _db.rawQuery('''
        SELECT
          si.product_id,
          p.name as product_name,
          p.code,
          p.barcode,
          SUM(si.quantity) as quantity,
          SUM(si.total) as total_sales,
          SUM(si.quantity * p.purchase_price) as total_cost,
          SUM(si.total - (si.quantity * p.purchase_price)) as total_profit,
          (SUM(si.total - (si.quantity * p.purchase_price)) / SUM(si.total)) * 100 as profit_margin
        FROM
          sale_items si
        JOIN
          sales s ON si.sale_id = s.id
        JOIN
          products p ON si.product_id = p.id
        WHERE
          s.date BETWEEN ? AND ?
          AND s.status = 'completed'
          AND p.is_deleted = 0
        GROUP BY
          si.product_id
        ORDER BY
          total_profit DESC
        LIMIT ?
      ''', [startDateStr, endDateStr, limit]);

      _isLoading = false;
      notifyListeners();

      return results;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage =
          'فشل في الحصول على المنتجات الأكثر ربحية: ${e.toString()}';
      ErrorTracker.captureError(
        'خطأ في الحصول على المنتجات الأكثر ربحية',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
      return [];
    }
  }

  /// الحصول على إحصائيات المبيعات
  Future<Map<String, dynamic>> getSalesStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      // التأكد من وجود الفهارس لتحسين أداء الاستعلام
      await _createReportsIndexes();

      // تنسيق التواريخ
      final startDateStr = startDate != null
          ? DateFormat('yyyy-MM-dd').format(startDate)
          : '2000-01-01';
      final endDateStr = endDate != null
          ? DateFormat('yyyy-MM-dd')
              .format(endDate.add(const Duration(days: 1)))
          : DateFormat('yyyy-MM-dd')
              .format(DateTime.now().add(const Duration(days: 1)));

      // بناء استعلام SQL للحصول على إحصائيات المبيعات
      final results = await _db.rawQuery('''
        SELECT
          COUNT(*) as total_sales,
          SUM(total) as total_amount,
          AVG(total) as average_sale,
          MIN(total) as min_sale,
          MAX(total) as max_sale,
          COUNT(DISTINCT customer_id) as customer_count
        FROM
          sales
        WHERE
          date BETWEEN ? AND ?
          AND status = 'completed'
      ''', [startDateStr, endDateStr]);

      _isLoading = false;
      notifyListeners();

      if (results.isNotEmpty) {
        return results.first;
      }

      return {
        'total_sales': 0,
        'total_amount': 0.0,
        'average_sale': 0.0,
        'min_sale': 0.0,
        'max_sale': 0.0,
        'customer_count': 0,
      };
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'فشل في الحصول على إحصائيات المبيعات: ${e.toString()}';
      ErrorTracker.captureError(
        'خطأ في الحصول على إحصائيات المبيعات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();

      return {
        'total_sales': 0,
        'total_amount': 0.0,
        'average_sale': 0.0,
        'min_sale': 0.0,
        'max_sale': 0.0,
        'customer_count': 0,
      };
    }
  }

  /// الحصول على المبيعات حسب الفترة الزمنية (يومي، أسبوعي، شهري)
  Future<List<Map<String, dynamic>>> getSalesByPeriod({
    DateTime? startDate,
    DateTime? endDate,
    String period = 'daily', // daily, weekly, monthly
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      // التأكد من وجود الفهارس لتحسين أداء الاستعلام
      await _createReportsIndexes();

      // تنسيق التواريخ
      final startDateStr = startDate != null
          ? DateFormat('yyyy-MM-dd').format(startDate)
          : '2000-01-01';
      final endDateStr = endDate != null
          ? DateFormat('yyyy-MM-dd')
              .format(endDate.add(const Duration(days: 1)))
          : DateFormat('yyyy-MM-dd')
              .format(DateTime.now().add(const Duration(days: 1)));

      // تحديد صيغة التجميع حسب الفترة
      String groupFormat;
      switch (period) {
        case 'weekly':
          groupFormat = "strftime('%Y-%W', date)";
          break;
        case 'monthly':
          groupFormat = "strftime('%Y-%m', date)";
          break;
        case 'daily':
        default:
          groupFormat = "strftime('%Y-%m-%d', date)";
          break;
      }

      // بناء استعلام SQL للحصول على المبيعات حسب الفترة
      final results = await _db.rawQuery('''
        SELECT
          $groupFormat as period,
          COUNT(*) as sales_count,
          SUM(total) as total_amount
        FROM
          sales
        WHERE
          date BETWEEN ? AND ?
          AND status = 'completed'
        GROUP BY
          period
        ORDER BY
          period ASC
      ''', [startDateStr, endDateStr]);

      _isLoading = false;
      notifyListeners();

      return results;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'فشل في الحصول على المبيعات حسب الفترة: ${e.toString()}';
      ErrorTracker.captureError(
        'خطأ في الحصول على المبيعات حسب الفترة',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
      return [];
    }
  }
}
