import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:tajer_plus/core/auth/permission_initializer.dart';

void main() {
  // تهيئة Flutter binding للاختبارات
  TestWidgetsFlutterBinding.ensureInitialized();

  // تهيئة قاعدة بيانات الاختبار
  setUpAll(() {
    // تهيئة sqflite_common_ffi
    sqfliteFfiInit();
    // تعيين مصنع قاعدة البيانات
    databaseFactory = databaseFactoryFfi;
  });

  group('PermissionInitializer Tests', () {
    late Database db;

    // إنشاء قاعدة بيانات مؤقتة قبل كل اختبار
    setUp(() async {
      // فتح قاعدة بيانات في الذاكرة
      db = await openDatabase(
        inMemoryDatabasePath,
        version: 1,
        onCreate: (db, version) async {
          // إنشاء جداول الصلاحيات والأدوار
          await db.execute('''
            CREATE TABLE permissions (
              id TEXT PRIMARY KEY,
              name TEXT NOT NULL,
              description TEXT,
              module TEXT,
              created_at TEXT,
              is_deleted INTEGER DEFAULT 0
            )
          ''');

          await db.execute('''
            CREATE TABLE roles (
              id TEXT PRIMARY KEY,
              name TEXT NOT NULL,
              description TEXT,
              is_active INTEGER DEFAULT 1,
              created_at TEXT,
              is_deleted INTEGER DEFAULT 0
            )
          ''');

          await db.execute('''
            CREATE TABLE role_permissions (
              id TEXT PRIMARY KEY,
              role_id TEXT NOT NULL,
              permission_id TEXT NOT NULL,
              created_at TEXT,
              is_deleted INTEGER DEFAULT 0,
              FOREIGN KEY (role_id) REFERENCES roles (id),
              FOREIGN KEY (permission_id) REFERENCES permissions (id)
            )
          ''');
        },
      );
    });

    // إغلاق قاعدة البيانات بعد كل اختبار
    tearDown(() async {
      await db.close();
    });

    test('initializePermissions should create permissions', () async {
      // تنفيذ تهيئة الصلاحيات
      await db.transaction((txn) async {
        final permissionIds =
            await PermissionInitializer.initializePermissions(txn);

        // التحقق من وجود الصلاحيات
        expect(permissionIds.isNotEmpty, isTrue,
            reason: 'يجب أن تكون هناك صلاحيات بعد التهيئة');

        // التحقق من وجود بعض الصلاحيات الرئيسية
        expect(permissionIds.containsKey('add_user'), isTrue,
            reason: 'يجب أن تكون هناك صلاحية إضافة مستخدم');
        expect(permissionIds.containsKey('view_inventory'), isTrue,
            reason: 'يجب أن تكون هناك صلاحية عرض المخزون');
      });

      // التحقق من عدد الصلاحيات في قاعدة البيانات
      final permissions = await db.query('permissions');
      expect(permissions.length, greaterThan(0),
          reason: 'يجب أن تكون هناك صلاحيات في قاعدة البيانات');
    });

    test('initializeRoles should create roles', () async {
      // تنفيذ تهيئة الأدوار
      await db.transaction((txn) async {
        final roleIds = await PermissionInitializer.initializeRoles(txn);

        // التحقق من وجود الأدوار
        expect(roleIds.isNotEmpty, isTrue,
            reason: 'يجب أن تكون هناك أدوار بعد التهيئة');

        // التحقق من وجود بعض الأدوار الرئيسية
        expect(roleIds.containsKey('admin'), isTrue,
            reason: 'يجب أن يكون هناك دور مدير');
        expect(roleIds.containsKey('cashier'), isTrue,
            reason: 'يجب أن يكون هناك دور كاشير');
      });

      // التحقق من عدد الأدوار في قاعدة البيانات
      final roles = await db.query('roles');
      expect(roles.length, greaterThan(0),
          reason: 'يجب أن تكون هناك أدوار في قاعدة البيانات');
    });

    test('linkPermissionsToRoles should link permissions to roles', () async {
      // تنفيذ تهيئة الصلاحيات والأدوار وربطها
      await db.transaction((txn) async {
        await PermissionInitializer.initializePermissions(txn);
        await PermissionInitializer.initializeRoles(txn);
        await PermissionInitializer.linkPermissionsToRoles(txn);
      });

      // التحقق من وجود روابط بين الصلاحيات والأدوار
      final rolePermissions = await db.query('role_permissions');
      expect(rolePermissions.length, greaterThan(0),
          reason: 'يجب أن تكون هناك روابط بين الصلاحيات والأدوار');
    });

    test('initializeDatabase should initialize all auth data', () async {
      // تنفيذ تهيئة جميع بيانات المصادقة
      await db.transaction((txn) async {
        await PermissionInitializer.initializeDatabase(txn);
      });

      // التحقق من وجود الصلاحيات والأدوار والروابط
      final permissions = await db.query('permissions');
      final roles = await db.query('roles');
      final rolePermissions = await db.query('role_permissions');

      expect(permissions.length, greaterThan(0),
          reason: 'يجب أن تكون هناك صلاحيات بعد التهيئة');
      expect(roles.length, greaterThan(0),
          reason: 'يجب أن تكون هناك أدوار بعد التهيئة');
      expect(rolePermissions.length, greaterThan(0),
          reason: 'يجب أن تكون هناك روابط بين الصلاحيات والأدوار');
    });

    test('getPermissionIds and getRoleIds should return correct IDs', () async {
      // تنفيذ تهيئة جميع بيانات المصادقة
      await db.transaction((txn) async {
        await PermissionInitializer.initializeDatabase(txn);
      });

      // الحصول على معرفات الصلاحيات والأدوار
      final permissionIds = PermissionInitializer.getPermissionIds();
      final roleIds = PermissionInitializer.getRoleIds();

      // التحقق من وجود معرفات الصلاحيات والأدوار
      expect(permissionIds.isNotEmpty, isTrue,
          reason: 'يجب أن تكون هناك معرفات صلاحيات');
      expect(roleIds.isNotEmpty, isTrue,
          reason: 'يجب أن تكون هناك معرفات أدوار');
    });
  });
}
