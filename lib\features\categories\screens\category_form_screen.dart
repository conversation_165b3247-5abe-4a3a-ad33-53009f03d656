import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/models/category.dart';
import '../presenters/category_presenter.dart';

import '../../../core/theme/index.dart';

class CategoryFormScreen extends StatefulWidget {
  final Category? category;
  final String? parentId;
  final String? categoryType; // إضافة نوع الفئة

  const CategoryFormScreen({
    Key? key,
    this.category,
    this.parentId,
    this.categoryType = 'product', // نوع افتراضي
  }) : super(key: key);

  @override
  State<CategoryFormScreen> createState() => _CategoryFormScreenState();
}

class _CategoryFormScreenState extends State<CategoryFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late CategoryPresenter _presenter;

  String? _name;
  String? _description;
  String? _imageUrl;

  @override
  void initState() {
    super.initState();
    _presenter = AppProviders.getLazyPresenter<CategoryPresenter>(
        () => CategoryPresenter());
    _initializeFields();
  }

  void _initializeFields() {
    if (widget.category != null) {
      _name = widget.category!.name;
      _description = widget.category!.description;
      _imageUrl = widget.category!.imageUrl;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.category == null ? 'Add Category' : 'Edit Category'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              initialValue: _name,
              decoration: const InputDecoration(
                labelText: 'Name',
                hintText: 'Enter category name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a name';
                }
                return null;
              },
              onSaved: (value) => _name = value,
            ),
            const SizedBox(height: AppDimensions.spacing16),
            TextFormField(
              initialValue: _description,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Enter category description (optional)',
              ),
              maxLines: 3,
              onSaved: (value) => _description = value,
            ),
            const SizedBox(height: AppDimensions.spacing16),
            TextFormField(
              initialValue: _imageUrl,
              decoration: const InputDecoration(
                labelText: 'Image URL',
                hintText: 'Enter image URL (optional)',
              ),
              onSaved: (value) => _imageUrl = value,
            ),
            const SizedBox(height: AppDimensions.spacing32),
            ElevatedButton(
              onPressed: _submitForm,
              child: Text(
                  widget.category == null ? 'Add Category' : 'Update Category'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    _formKey.currentState!.save();

    final category = Category(
      id: widget.category?.id,
      name: _name!,
      description: _description,
      parentId: widget.parentId ?? widget.category?.parentId,
      imageUrl: _imageUrl,
      type: widget.categoryType ?? widget.category?.type ?? 'product',
    );

    bool success;
    if (widget.category == null) {
      success = await _presenter.addCategory(category);
    } else {
      success = await _presenter.updateCategory(category);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
                ? 'Category ${widget.category == null ? 'added' : 'updated'} successfully'
                : 'Failed to ${widget.category == null ? 'add' : 'update'} category',
          ),
          backgroundColor: success ? AppColors.success : AppColors.error,
        ),
      );

      if (success) {
        Navigator.pop(context, true);
      }
    }
  }
}
