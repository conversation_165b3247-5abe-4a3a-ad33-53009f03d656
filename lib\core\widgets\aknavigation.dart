import 'package:flutter/material.dart';
import '../theme/index.dart';

/// نظام التنقل الموحد لتطبيق تاجر بلس
/// يحتوي على جميع عناصر التنقل المستخدمة في التطبيق
///
/// **المميزات:**
/// - تصميم موحد ومتناسق لجميع عناصر التنقل
/// - دعم كامل للوضع المظلم/الفاتح
/// - تأثيرات تفاعلية متقدمة
/// - تحميل كسول للعناصر الثقيلة
/// - دوال مساعدة سريعة
/// - تعليقات شاملة باللغة العربية

// ═══════════════════════════════════════════════════════════════════════════════
// ● الأنواع والثوابت
// ═══════════════════════════════════════════════════════════════════════════════

/// أنواع شرائح التبويب المختلفة
enum AkTabType {
  /// شريط تبويب عادي
  normal,

  /// شريط تبويب مع أيقونات
  withIcons,

  /// شريط تبويب مع شارات
  withBadges,

  /// شريط تبويب قابل للتمرير
  scrollable,
}

/// أنواع أشرطة التقدم المختلفة
enum AkProgressType {
  /// شريط تقدم خطي
  linear,

  /// شريط تقدم دائري
  circular,

  /// شريط تقدم مع خطوات
  stepped,

  /// شريط تقدم مع نسبة مئوية
  percentage,
}

/// أحجام عناصر التنقل
enum AkNavigationSize {
  /// حجم صغير
  small,

  /// حجم متوسط
  medium,

  /// حجم كبير
  large,
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● 1. شريط التبويب الموحد (AkTabBar)
// ═══════════════════════════════════════════════════════════════════════════════

/// شريط تبويب موحد مع تصميم متناسق
///
/// **المميزات:**
/// - تصميم موحد لجميع شرائح التبويب
/// - دعم أنواع مختلفة (عادي، مع أيقونات، مع شارات)
/// - تأثيرات تفاعلية متقدمة
/// - دعم كامل للوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkTabBar(
///   tabs: ['المنتجات', 'العملاء', 'المبيعات'],
///   onTabChanged: (index) => handleTabChange(index),
///   type: AkTabType.withIcons,
///   icons: [Icons.inventory, Icons.people, Icons.shopping_cart],
/// )
/// ```
class AkTabBar extends StatefulWidget {
  /// قائمة عناوين التبويبات
  final List<String> tabs;

  /// دالة تغيير التبويب
  final Function(int)? onTabChanged;

  /// نوع شريط التبويب
  final AkTabType type;

  /// قائمة الأيقونات (للنوع withIcons)
  final List<IconData>? icons;

  /// قائمة الشارات (للنوع withBadges)
  final List<int>? badges;

  /// الفهرس الأولي المختار
  final int initialIndex;

  /// هل يمكن التمرير
  final bool isScrollable;

  /// لون مخصص للتبويب النشط
  final Color? activeColor;

  /// لون مخصص للتبويب غير النشط
  final Color? inactiveColor;

  /// حجم شريط التبويب
  final AkNavigationSize size;

  const AkTabBar({
    super.key,
    required this.tabs,
    this.onTabChanged,
    this.type = AkTabType.normal,
    this.icons,
    this.badges,
    this.initialIndex = 0,
    this.isScrollable = false,
    this.activeColor,
    this.inactiveColor,
    this.size = AkNavigationSize.medium,
  });

  @override
  State<AkTabBar> createState() => _AkTabBarState();
}

class _AkTabBarState extends State<AkTabBar>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.tabs.length,
      vsync: this,
      initialIndex: widget.initialIndex,
    );
    _tabController.addListener(() {
      if (widget.onTabChanged != null && _tabController.indexIsChanging) {
        widget.onTabChanged!(_tabController.index);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // تحديد الألوان حسب الوضع
    final activeColor = widget.activeColor ?? AppColors.primary;
    final inactiveColor = widget.inactiveColor ??
        (isDark ? AppColors.darkTextSecondary : AppColors.lightTextSecondary);

    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppColors.darkSurface : AppColors.lightSurface,
        border: Border(
          bottom: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable:
            widget.isScrollable || widget.type == AkTabType.scrollable,
        indicatorColor: activeColor,
        indicatorWeight: 3,
        indicatorSize: TabBarIndicatorSize.tab,
        labelColor: activeColor,
        unselectedLabelColor: inactiveColor,
        labelStyle: AppTypography.createCustomStyle(
          fontSize: _getFontSize(),
          fontWeight: AppTypography.weightMedium,
          color: activeColor,
        ),
        unselectedLabelStyle: AppTypography.createCustomStyle(
          fontSize: _getFontSize(),
          fontWeight: AppTypography.weightRegular,
          color: inactiveColor,
        ),
        padding: EdgeInsets.symmetric(horizontal: AppDimensions.smallMargin),
        tabs: List.generate(widget.tabs.length, (index) {
          return _buildTab(index, isDark);
        }),
      ),
    );
  }

  /// بناء تبويب واحد
  Widget _buildTab(int index, bool isDark) {
    final tab = widget.tabs[index];
    final hasIcon = widget.type == AkTabType.withIcons &&
        widget.icons != null &&
        index < widget.icons!.length;
    final hasBadge = widget.type == AkTabType.withBadges &&
        widget.badges != null &&
        index < widget.badges!.length &&
        widget.badges![index] > 0;

    return Tab(
      height: _getTabHeight(),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // الأيقونة
          if (hasIcon) ...[
            Icon(
              widget.icons![index],
              size: _getIconSize(),
            ),
            SizedBox(width: AppDimensions.tinySpacing),
          ],

          // النص مع الشارة
          if (hasBadge)
            Badge(
              label: Text(
                widget.badges![index].toString(),
                style: AppTypography.createCustomStyle(
                  fontSize: AppDimensions.tinyFontSize,
                  fontWeight: AppTypography.weightBold,
                  color: AppColors.onPrimary,
                ),
              ),
              backgroundColor: AppColors.error,
              child: Text(tab),
            )
          else
            Text(tab),
        ],
      ),
    );
  }

  /// الحصول على حجم الخط حسب الحجم
  double _getFontSize() {
    switch (widget.size) {
      case AkNavigationSize.small:
        return AppDimensions.smallFontSize;
      case AkNavigationSize.medium:
        return AppDimensions.defaultFontSize;
      case AkNavigationSize.large:
        return AppDimensions.mediumFontSize;
    }
  }

  /// الحصول على حجم الأيقونة حسب الحجم
  double _getIconSize() {
    switch (widget.size) {
      case AkNavigationSize.small:
        return AppDimensions.smallIconSize;
      case AkNavigationSize.medium:
        return AppDimensions.mediumIconSize;
      case AkNavigationSize.large:
        return AppDimensions.largeIconSize;
    }
  }

  /// الحصول على ارتفاع التبويب حسب الحجم
  double _getTabHeight() {
    switch (widget.size) {
      case AkNavigationSize.small:
        return 40;
      case AkNavigationSize.medium:
        return 48;
      case AkNavigationSize.large:
        return 56;
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● 2. شريط التقدم الموحد (AkProgressBar)
// ═══════════════════════════════════════════════════════════════════════════════

/// شريط تقدم موحد مع أنواع مختلفة
///
/// **المميزات:**
/// - تصميم موحد لجميع أشرطة التقدم
/// - دعم أنواع مختلفة (خطي، دائري، خطوات، نسبة مئوية)
/// - تأثيرات تفاعلية متقدمة
/// - دعم كامل للوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkProgressBar(
///   value: 0.7,
///   type: AkProgressType.linear,
///   showPercentage: true,
///   label: 'تقدم التحميل',
/// )
/// ```
class AkProgressBar extends StatelessWidget {
  /// قيمة التقدم (0.0 إلى 1.0)
  final double value;

  /// نوع شريط التقدم
  final AkProgressType type;

  /// هل يتم عرض النسبة المئوية
  final bool showPercentage;

  /// تسمية شريط التقدم
  final String? label;

  /// لون مخصص لشريط التقدم
  final Color? color;

  /// لون خلفية شريط التقدم
  final Color? backgroundColor;

  /// حجم شريط التقدم
  final AkNavigationSize size;

  /// عدد الخطوات (للنوع stepped)
  final int? totalSteps;

  /// الخطوة الحالية (للنوع stepped)
  final int? currentStep;

  /// قائمة تسميات الخطوات
  final List<String>? stepLabels;

  const AkProgressBar({
    super.key,
    required this.value,
    this.type = AkProgressType.linear,
    this.showPercentage = false,
    this.label,
    this.color,
    this.backgroundColor,
    this.size = AkNavigationSize.medium,
    this.totalSteps,
    this.currentStep,
    this.stepLabels,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // تحديد الألوان حسب الوضع
    final progressColor = color ?? AppColors.primary;
    final bgColor = backgroundColor ??
        (isDark ? AppColors.darkSurfaceVariant : AppColors.lightSurfaceVariant);

    switch (type) {
      case AkProgressType.linear:
        return _buildLinearProgress(progressColor, bgColor, isDark);
      case AkProgressType.circular:
        return _buildCircularProgress(progressColor, bgColor, isDark);
      case AkProgressType.stepped:
        return _buildSteppedProgress(progressColor, bgColor, isDark);
      case AkProgressType.percentage:
        return _buildPercentageProgress(progressColor, bgColor, isDark);
    }
  }

  /// بناء شريط التقدم الخطي
  Widget _buildLinearProgress(Color progressColor, Color bgColor, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // التسمية والنسبة المئوية
        if (label != null || showPercentage)
          Padding(
            padding: EdgeInsets.only(bottom: AppDimensions.tinySpacing),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (label != null)
                  Text(
                    label!,
                    style: AppTypography.createCustomStyle(
                      fontSize: _getFontSize(),
                      fontWeight: AppTypography.weightMedium,
                      color: isDark
                          ? AppColors.darkTextPrimary
                          : AppColors.lightTextPrimary,
                    ),
                  ),
                if (showPercentage)
                  Text(
                    '${(value * 100).round()}%',
                    style: AppTypography.createCustomStyle(
                      fontSize: _getFontSize(),
                      fontWeight: AppTypography.weightMedium,
                      color: progressColor,
                    ),
                  ),
              ],
            ),
          ),

        // شريط التقدم
        Container(
          height: _getProgressHeight(),
          decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.circular(AppDimensions.tinyRadius),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppDimensions.tinyRadius),
            child: LinearProgressIndicator(
              value: value,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
              minHeight: _getProgressHeight(),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء شريط التقدم الدائري
  Widget _buildCircularProgress(
      Color progressColor, Color bgColor, bool isDark) {
    final circularSize = _getCircularSize();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // الدائرة مع النسبة المئوية
        SizedBox(
          width: circularSize,
          height: circularSize,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // شريط التقدم الدائري
              SizedBox(
                width: circularSize,
                height: circularSize,
                child: CircularProgressIndicator(
                  value: value,
                  backgroundColor: bgColor,
                  valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                  strokeWidth: _getStrokeWidth(),
                ),
              ),
              // النسبة المئوية في المنتصف
              if (showPercentage)
                Text(
                  '${(value * 100).round()}%',
                  style: AppTypography.createCustomStyle(
                    fontSize: _getFontSize(),
                    fontWeight: AppTypography.weightBold,
                    color: progressColor,
                  ),
                ),
            ],
          ),
        ),

        // التسمية
        if (label != null) ...[
          SizedBox(height: AppDimensions.smallSpacing),
          Text(
            label!,
            style: AppTypography.createCustomStyle(
              fontSize: _getFontSize(),
              fontWeight: AppTypography.weightMedium,
              color: isDark
                  ? AppColors.darkTextPrimary
                  : AppColors.lightTextPrimary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  /// بناء شريط التقدم بالخطوات
  Widget _buildSteppedProgress(
      Color progressColor, Color bgColor, bool isDark) {
    final steps = totalSteps ?? 3;
    final current = currentStep ?? (value * steps).round();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // التسمية
        if (label != null)
          Padding(
            padding: EdgeInsets.only(bottom: AppDimensions.smallSpacing),
            child: Text(
              label!,
              style: AppTypography.createCustomStyle(
                fontSize: _getFontSize(),
                fontWeight: AppTypography.weightMedium,
                color: isDark
                    ? AppColors.darkTextPrimary
                    : AppColors.lightTextPrimary,
              ),
            ),
          ),

        // الخطوات
        Row(
          children: List.generate(steps, (index) {
            final isCompleted = index < current;
            final isCurrent = index == current - 1;

            return Expanded(
              child: Row(
                children: [
                  // دائرة الخطوة
                  Container(
                    width: _getStepSize(),
                    height: _getStepSize(),
                    decoration: BoxDecoration(
                      color: isCompleted || isCurrent ? progressColor : bgColor,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: progressColor,
                        width: isCurrent ? 2 : 1,
                      ),
                    ),
                    child: Center(
                      child: isCompleted
                          ? Icon(
                              Icons.check,
                              size: _getStepIconSize(),
                              color: AppColors.onPrimary,
                            )
                          : Text(
                              '${index + 1}',
                              style: AppTypography.createCustomStyle(
                                fontSize: _getStepFontSize(),
                                fontWeight: AppTypography.weightBold,
                                color: isCompleted || isCurrent
                                    ? AppColors.onPrimary
                                    : progressColor,
                              ),
                            ),
                    ),
                  ),

                  // خط الاتصال
                  if (index < steps - 1)
                    Expanded(
                      child: Container(
                        height: 2,
                        color: index < current - 1 ? progressColor : bgColor,
                      ),
                    ),
                ],
              ),
            );
          }),
        ),

        // تسميات الخطوات
        if (stepLabels != null && stepLabels!.length >= steps) ...[
          SizedBox(height: AppDimensions.tinySpacing),
          Row(
            children: List.generate(steps, (index) {
              return Expanded(
                child: Text(
                  stepLabels![index],
                  style: AppTypography.createCustomStyle(
                    fontSize: AppDimensions.tinyFontSize,
                    fontWeight: AppTypography.weightRegular,
                    color: isDark
                        ? AppColors.darkTextSecondary
                        : AppColors.lightTextSecondary,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              );
            }),
          ),
        ],
      ],
    );
  }

  /// بناء شريط التقدم مع النسبة المئوية
  Widget _buildPercentageProgress(
      Color progressColor, Color bgColor, bool isDark) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.defaultMargin),
      decoration: BoxDecoration(
        color: isDark ? AppColors.darkSurface : AppColors.lightSurface,
        borderRadius: BorderRadius.circular(AppDimensions.smallRadius),
        border: Border.all(
          color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
        ),
      ),
      child: Row(
        children: [
          // شريط التقدم الدائري الصغير
          SizedBox(
            width: 40,
            height: 40,
            child: CircularProgressIndicator(
              value: value,
              backgroundColor: bgColor,
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
              strokeWidth: 3,
            ),
          ),

          SizedBox(width: AppDimensions.defaultSpacing),

          // المعلومات
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // التسمية
                if (label != null)
                  Text(
                    label!,
                    style: AppTypography.createCustomStyle(
                      fontSize: _getFontSize(),
                      fontWeight: AppTypography.weightMedium,
                      color: isDark
                          ? AppColors.darkTextPrimary
                          : AppColors.lightTextPrimary,
                    ),
                  ),

                // النسبة المئوية
                Text(
                  '${(value * 100).round()}% مكتمل',
                  style: AppTypography.createCustomStyle(
                    fontSize: AppDimensions.smallFontSize,
                    fontWeight: AppTypography.weightRegular,
                    color: isDark
                        ? AppColors.darkTextSecondary
                        : AppColors.lightTextSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على حجم الخط حسب الحجم
  double _getFontSize() {
    switch (size) {
      case AkNavigationSize.small:
        return AppDimensions.smallFontSize;
      case AkNavigationSize.medium:
        return AppDimensions.defaultFontSize;
      case AkNavigationSize.large:
        return AppDimensions.mediumFontSize;
    }
  }

  /// الحصول على ارتفاع شريط التقدم حسب الحجم
  double _getProgressHeight() {
    switch (size) {
      case AkNavigationSize.small:
        return 4;
      case AkNavigationSize.medium:
        return 6;
      case AkNavigationSize.large:
        return 8;
    }
  }

  /// الحصول على حجم الدائرة حسب الحجم
  double _getCircularSize() {
    switch (size) {
      case AkNavigationSize.small:
        return 60;
      case AkNavigationSize.medium:
        return 80;
      case AkNavigationSize.large:
        return 100;
    }
  }

  /// الحصول على عرض الخط للدائرة حسب الحجم
  double _getStrokeWidth() {
    switch (size) {
      case AkNavigationSize.small:
        return 3;
      case AkNavigationSize.medium:
        return 4;
      case AkNavigationSize.large:
        return 5;
    }
  }

  /// الحصول على حجم دائرة الخطوة حسب الحجم
  double _getStepSize() {
    switch (size) {
      case AkNavigationSize.small:
        return 24;
      case AkNavigationSize.medium:
        return 32;
      case AkNavigationSize.large:
        return 40;
    }
  }

  /// الحصول على حجم أيقونة الخطوة حسب الحجم
  double _getStepIconSize() {
    switch (size) {
      case AkNavigationSize.small:
        return 12;
      case AkNavigationSize.medium:
        return 16;
      case AkNavigationSize.large:
        return 20;
    }
  }

  /// الحصول على حجم خط الخطوة حسب الحجم
  double _getStepFontSize() {
    switch (size) {
      case AkNavigationSize.small:
        return AppDimensions.tinyFontSize;
      case AkNavigationSize.medium:
        return AppDimensions.smallFontSize;
      case AkNavigationSize.large:
        return AppDimensions.defaultFontSize;
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● الدوال المساعدة السريعة (AkNavigation)
// ═══════════════════════════════════════════════════════════════════════════════

/// فئة الدوال المساعدة السريعة لعناصر التنقل
/// توفر طرق سريعة لإنشاء عناصر التنقل الشائعة
class AkNavigation {
  AkNavigation._();

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال شرائح التبويب السريعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// شريط تبويب للمنتجات
  static Widget productsTabBar({
    required Function(int) onTabChanged,
    int initialIndex = 0,
  }) {
    return AkTabBar(
      tabs: const ['جميع المنتجات', 'مخزون منخفض', 'الأكثر مبيعاً'],
      icons: const [Icons.inventory_2, Icons.warning, Icons.trending_up],
      type: AkTabType.withIcons,
      onTabChanged: onTabChanged,
      initialIndex: initialIndex,
    );
  }

  /// شريط تبويب للمبيعات
  static Widget salesTabBar({
    required Function(int) onTabChanged,
    int initialIndex = 0,
  }) {
    return AkTabBar(
      tabs: const ['اليوم', 'هذا الأسبوع', 'هذا الشهر'],
      onTabChanged: onTabChanged,
      initialIndex: initialIndex,
    );
  }

  /// شريط تبويب للتقارير
  static Widget reportsTabBar({
    required Function(int) onTabChanged,
    int initialIndex = 0,
  }) {
    return AkTabBar(
      tabs: const ['المبيعات', 'المشتريات', 'الأرباح', 'العملاء'],
      icons: const [
        Icons.point_of_sale,
        Icons.shopping_cart,
        Icons.trending_up,
        Icons.people
      ],
      type: AkTabType.withIcons,
      onTabChanged: onTabChanged,
      initialIndex: initialIndex,
      isScrollable: true,
    );
  }

  /// شريط تبويب للإعدادات
  static Widget settingsTabBar({
    required Function(int) onTabChanged,
    int initialIndex = 0,
  }) {
    return AkTabBar(
      tabs: const ['عام', 'المظهر', 'الأمان', 'النسخ الاحتياطي'],
      icons: const [
        Icons.settings,
        Icons.palette,
        Icons.security,
        Icons.backup
      ],
      type: AkTabType.withIcons,
      onTabChanged: onTabChanged,
      initialIndex: initialIndex,
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال أشرطة التقدم السريعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// شريط تقدم تحميل البيانات
  static Widget dataLoadingProgress({
    required double progress,
    String? label,
  }) {
    return AkProgressBar(
      value: progress,
      type: AkProgressType.linear,
      showPercentage: true,
      label: label ?? 'جاري تحميل البيانات...',
    );
  }

  /// شريط تقدم رفع الملفات
  static Widget fileUploadProgress({
    required double progress,
    String? fileName,
  }) {
    return AkProgressBar(
      value: progress,
      type: AkProgressType.percentage,
      label: fileName != null ? 'رفع $fileName' : 'رفع الملف',
    );
  }

  /// شريط تقدم النسخ الاحتياطي
  static Widget backupProgress({
    required double progress,
  }) {
    return AkProgressBar(
      value: progress,
      type: AkProgressType.circular,
      showPercentage: true,
      label: 'إنشاء نسخة احتياطية',
    );
  }

  /// شريط تقدم خطوات التسجيل
  static Widget registrationSteps({
    required int currentStep,
    int totalSteps = 4,
  }) {
    return AkProgressBar(
      value: currentStep / totalSteps,
      type: AkProgressType.stepped,
      totalSteps: totalSteps,
      currentStep: currentStep,
      stepLabels: const [
        'المعلومات الأساسية',
        'تفاصيل المتجر',
        'الإعدادات',
        'التأكيد'
      ],
      label: 'خطوات إنشاء الحساب',
    );
  }

  /// شريط تقدم خطوات الطلب
  static Widget orderSteps({
    required int currentStep,
  }) {
    return AkProgressBar(
      value: currentStep / 4,
      type: AkProgressType.stepped,
      totalSteps: 4,
      currentStep: currentStep,
      stepLabels: const ['السلة', 'الدفع', 'التأكيد', 'التسليم'],
      label: 'حالة الطلب',
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال متخصصة للمشروع التجاري
  // ───────────────────────────────────────────────────────────────────────────────

  /// شريط تبويب لوحة التحكم الرئيسية
  static Widget dashboardTabBar({
    required Function(int) onTabChanged,
    int initialIndex = 0,
    List<int>? notificationCounts,
  }) {
    return AkTabBar(
      tabs: const ['نظرة عامة', 'المبيعات', 'المخزون', 'التقارير'],
      icons: const [
        Icons.dashboard,
        Icons.point_of_sale,
        Icons.inventory,
        Icons.analytics
      ],
      type: notificationCounts != null
          ? AkTabType.withBadges
          : AkTabType.withIcons,
      badges: notificationCounts,
      onTabChanged: onTabChanged,
      initialIndex: initialIndex,
    );
  }

  /// شريط تقدم مبيعات اليوم
  static Widget dailySalesProgress({
    required double currentSales,
    required double targetSales,
    String? currency = 'ر.ي',
  }) {
    final progress = (currentSales / targetSales).clamp(0.0, 1.0);
    return AkProgressBar(
      value: progress,
      type: AkProgressType.percentage,
      label:
          'مبيعات اليوم: ${currentSales.toStringAsFixed(0)} $currency من ${targetSales.toStringAsFixed(0)} $currency',
    );
  }

  /// شريط تقدم مستوى المخزون
  static Widget stockLevelProgress({
    required int currentStock,
    required int maxStock,
    required String productName,
  }) {
    final progress = (currentStock / maxStock).clamp(0.0, 1.0);
    Color? progressColor;

    if (progress < 0.2) {
      progressColor = AppColors.error;
    } else if (progress < 0.5) {
      progressColor = AppColors.warning;
    } else {
      progressColor = AppColors.success;
    }

    return AkProgressBar(
      value: progress,
      type: AkProgressType.linear,
      showPercentage: true,
      label: 'مخزون $productName: $currentStock من $maxStock',
      color: progressColor,
    );
  }

  /// شريط تقدم تحقيق الهدف الشهري
  static Widget monthlyTargetProgress({
    required double currentAmount,
    required double targetAmount,
    String? currency = 'ر.ي',
  }) {
    final progress = (currentAmount / targetAmount).clamp(0.0, 1.0);
    return AkProgressBar(
      value: progress,
      type: AkProgressType.circular,
      showPercentage: true,
      label: 'الهدف الشهري',
      size: AkNavigationSize.large,
    );
  }

  /// شريط تبويب نقطة البيع (POS)
  static Widget posTabBar({
    required Function(int) onTabChanged,
    int initialIndex = 0,
  }) {
    return AkTabBar(
      tabs: const ['المنتجات', 'السلة', 'العملاء'],
      icons: const [Icons.inventory_2, Icons.shopping_cart, Icons.people],
      type: AkTabType.withIcons,
      onTabChanged: onTabChanged,
      initialIndex: initialIndex,
      size: AkNavigationSize.large,
    );
  }

  /// شريط تقدم عملية المزامنة
  static Widget syncProgress({
    required double progress,
    String? status,
  }) {
    return AkProgressBar(
      value: progress,
      type: AkProgressType.linear,
      showPercentage: true,
      label: status ?? 'مزامنة البيانات...',
      color: AppColors.info,
    );
  }
}
