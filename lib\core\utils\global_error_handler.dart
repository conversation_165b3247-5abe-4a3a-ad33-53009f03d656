import 'dart:async';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'error_tracker.dart';
import '../../../core/theme/index.dart';

/// A comprehensive global error handling system for the application.
///
/// This class provides methods to catch and handle errors at different levels:
/// - Flutter framework errors
/// - Dart isolate errors
/// - Async errors in the zone
/// - Platform errors
///
/// It also provides utilities to analyze errors and suggest solutions.
class GlobalErrorHandler {
  /// Initialize all error handlers
  static void init() {
    // Set up Flutter error handling
    FlutterError.onError = _handleFlutterError;

    // Set up custom error widget
    ErrorWidget.builder = _buildErrorWidget;

    // Listen for errors from the Dart runtime
    Isolate.current.addErrorListener(RawReceivePort((pair) {
      final List<dynamic> errorAndStacktrace = pair;
      _handleIsolateError(
        errorAndStacktrace[0],
        errorAndStacktrace[1],
      );
    }).sendPort);

    // Set up PlatformDispatcher error handling (for errors outside Flutter)
    PlatformDispatcher.instance.onError = _handlePlatformError;
  }

  /// Initialize the zone-guarded app execution
  static R runZonedApp<R>(R Function() body) {
    return runZonedGuarded(
      body,
      _handleZoneError,
    ) as R;
  }

  /// Handle Flutter framework errors
  static void _handleFlutterError(FlutterErrorDetails details) {
    // Log the error with our tracker
    ErrorTracker.captureError(
      'Flutter framework error',
      error: details.exception,
      stackTrace: details.stack ?? StackTrace.current,
      context: {
        'library': details.library,
        'context': details.context?.toString(),
        'silent': details.silent,
      },
    );

    // Let Flutter handle the error too
    FlutterError.presentError(details);
  }

  /// Handle errors from the Dart runtime/isolate
  static void _handleIsolateError(Object error, StackTrace stackTrace) {
    ErrorTracker.captureError(
      'Isolate error',
      error: error,
      stackTrace: stackTrace,
      context: {
        'isolate': Isolate.current.debugName,
      },
    );
  }

  /// Handle async errors from the Zone
  static void _handleZoneError(Object error, StackTrace stackTrace) {
    ErrorTracker.captureError(
      'Uncaught async error',
      error: error,
      stackTrace: stackTrace,
      context: {
        'zone': Zone.current.toString(),
      },
    );
  }

  /// Handle platform errors
  static bool _handlePlatformError(Object error, StackTrace stackTrace) {
    ErrorTracker.captureError(
      'Platform error',
      error: error,
      stackTrace: stackTrace,
      context: {
        'platform': defaultTargetPlatform.toString(),
      },
    );
    // Return true to indicate the error was handled
    return true;
  }

  /// Build a custom error widget
  static Widget _buildErrorWidget(FlutterErrorDetails details) {
    // Log the error that caused this widget to be built
    ErrorTracker.captureError(
      'UI rendering error',
      error: details.exception,
      stackTrace: details.stack ?? StackTrace.current,
      context: {
        'widget': details.context.toString(),
      },
    );

    // Return a more user-friendly error widget
    return Material(
      color: AppColors.onPrimary,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.error_outline,
                color: AppColors.errorDark,
                size: 60,
              ),
              const SizedBox(height: AppDimensions.spacing16),
              const Text(
                'Something went wrong',
                style: AppTypography(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.spacing8),
              Text(
                kDebugMode
                    ? details.exception.toString()
                    : 'The application encountered an error. Please try again.',
                textAlign: TextAlign.center,
                style: const AppTypography(color: AppColors.lightTextSecondary),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Analyze an error and suggest possible solutions
  static String analyzeProblem(Object error, StackTrace stackTrace) {
    final errorString = error.toString().toLowerCase();

    // Database related errors
    if (errorString.contains('sqlite') || errorString.contains('database')) {
      return 'This appears to be a database issue. Check if the database schema is correct, '
          'tables exist, and the query syntax is valid.';
    }

    // Network related errors
    if (errorString.contains('socket') ||
        errorString.contains('http') ||
        errorString.contains('connection')) {
      return 'This appears to be a network issue. Check your internet connection, '
          'API endpoints, or server status.';
    }

    // State management errors
    if (errorString.contains('provider') ||
        errorString.contains('inheritedwidget') ||
        errorString.contains('notifier')) {
      return 'This appears to be a state management issue. Check if providers are properly '
          'initialized and accessed within the correct context.';
    }

    // UI rendering errors
    if (errorString.contains('render') ||
        errorString.contains('layout') ||
        errorString.contains('overflow')) {
      return 'This appears to be a UI rendering issue. Check for layout constraints, '
          'overflow issues, or widget tree inconsistencies.';
    }

    // Default suggestion
    return 'Review the stack trace to identify the source of the error and check the '
        'related code for potential issues.';
  }
}
