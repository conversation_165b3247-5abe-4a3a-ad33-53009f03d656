import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/models/product.dart';
import '../../units/presenters/unit_presenter.dart';
import '../../suppliers/presenters/supplier_presenter.dart';
import '../../../core/widgets/index.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/theme/index.dart';
import '../../../core/services/image_service.dart';
import '../../../core/services/image_optimizer_service.dart';

class ProductFormDialog extends StatefulWidget {
  final Product? product;

  const ProductFormDialog({
    super.key,
    this.product,
  });

  @override
  State<ProductFormDialog> createState() => _ProductFormDialogState();
}

class _ProductFormDialogState extends State<ProductFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _skuController = TextEditingController();
  final _priceController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _quantityController = TextEditingController();
  final _minStockController = TextEditingController();
  final _maxStockController = TextEditingController();
  String? _selectedCategory;
  String? _selectedUnit;
  String? _selectedSupplier;
  bool _isActive = true;

  // متغيرات الصورة مع استخدام الخدمات الموجودة
  File? _selectedImage;
  String? _currentImageUrl;
  final ImageService _imageService = ImageService();
  final ImageOptimizerService _imageOptimizer = ImageOptimizerService();

  // تم إزالة المتغيرات غير المستخدمة

  @override
  void initState() {
    super.initState();

    // تحميل البيانات من قاعدة البيانات
    _loadData();

    // تعبئة البيانات إذا كان هناك منتج للتعديل
    if (widget.product != null) {
      _nameController.text = widget.product!.name;
      _descriptionController.text = widget.product!.description ?? '';
      _barcodeController.text = widget.product!.barcode ?? '';
      _skuController.text = widget.product!.sku ?? '';
      _priceController.text = widget.product!.salePrice.toString();
      _costPriceController.text = widget.product!.purchasePrice.toString();
      _quantityController.text = widget.product!.quantity.toString();
      _minStockController.text = widget.product!.minStock.toString();
      _maxStockController.text = widget.product!.maxStock?.toString() ?? '';
      _selectedCategory = widget.product!.categoryId;
      _selectedUnit = widget.product!.unitId;
      _selectedSupplier = widget.product!.metadata?['supplier_id'] as String?;
      _isActive = widget.product!.isActive;
      _currentImageUrl = widget.product!.imageUrl;
    }
  }

  // تحميل البيانات من قاعدة البيانات
  Future<void> _loadData() async {
    // تخزين المراجع قبل العمليات غير المتزامنة
    final categoryPresenter = AppProviders.getCategoryPresenter();
    final unitPresenter =
        AppProviders.getLazyPresenter<UnitPresenter>(() => UnitPresenter());
    final supplierPresenter = AppProviders.getLazyPresenter<SupplierPresenter>(
        () => SupplierPresenter());

    // تحميل البيانات مع تحديد النوع للجداول الموحدة
    categoryPresenter.loadCategories(type: 'product');
    unitPresenter.loadUnits(type: 'product');
    supplierPresenter.loadSuppliers();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _barcodeController.dispose();
    _skuController.dispose();
    _priceController.dispose();
    _costPriceController.dispose();
    _quantityController.dispose();
    _minStockController.dispose();
    _maxStockController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                widget.product != null ? 'تعديل منتج' : 'إضافة منتج',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              AkCard(
                child: Column(
                  children: [
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم المنتج',
                        prefixIcon: Icon(Icons.inventory_2_outlined),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الرجاء إدخال اسم المنتج';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _descriptionController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        labelText: 'وصف المنتج',
                        prefixIcon: Icon(Icons.description_outlined),
                        alignLabelWithHint: true,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              // قسم الصورة مع النظام الموحد AK
              AkCard(
                child: _buildImageSection(),
              ),
              const SizedBox(height: 16),
              AkCard(
                child: Column(
                  children: [
                    TextFormField(
                      controller: _barcodeController,
                      decoration: InputDecoration(
                        labelText: 'الباركود',
                        prefixIcon: const Icon(Icons.qr_code),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.camera_alt_outlined),
                          onPressed: _scanBarcode,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _skuController,
                      decoration: const InputDecoration(
                        labelText: 'رقم المنتج (SKU)',
                        prefixIcon: Icon(Icons.tag),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              AkCard(
                child: Column(
                  children: [
                    TextFormField(
                      controller: _priceController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      decoration: const InputDecoration(
                        labelText: 'سعر البيع',
                        prefixIcon: Icon(Icons.attach_money),
                        suffixText: 'ر.س',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الرجاء إدخال سعر البيع';
                        }
                        if (double.tryParse(value) == null) {
                          return 'الرجاء إدخال سعر صحيح';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _costPriceController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      decoration: const InputDecoration(
                        labelText: 'سعر التكلفة',
                        prefixIcon: Icon(Icons.price_change_outlined),
                        suffixText: 'ر.س',
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              AkCard(
                child: Column(
                  children: [
                    TextFormField(
                      controller: _quantityController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      decoration: const InputDecoration(
                        labelText: 'الكمية',
                        prefixIcon: Icon(Icons.inventory_outlined),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الرجاء إدخال الكمية';
                        }
                        if (int.tryParse(value) == null) {
                          return 'الرجاء إدخال رقم صحيح';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _minStockController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            decoration: const InputDecoration(
                              labelText: 'الحد الأدنى',
                              prefixIcon: Icon(Icons.arrow_downward),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _maxStockController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            decoration: const InputDecoration(
                              labelText: 'الحد الأقصى',
                              prefixIcon: Icon(Icons.arrow_upward),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              AkCard(
                child: Column(
                  children: [
                    ListenableBuilder(
                      listenable: AppProviders.getCategoryPresenter(),
                      builder: (context, child) {
                        final categoryPresenter =
                            AppProviders.getCategoryPresenter();
                        if (categoryPresenter.isLoading) {
                          return const Center(
                              child: CircularProgressIndicator());
                        }

                        return DropdownButtonFormField<String>(
                          value: _selectedCategory,
                          decoration: const InputDecoration(
                            labelText: 'التصنيف',
                            prefixIcon: Icon(Icons.category_outlined),
                          ),
                          items: [
                            ...categoryPresenter.categories
                                .map((category) => DropdownMenuItem(
                                      value: category.id,
                                      child: Text(category.name),
                                    )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedCategory = value;
                            });
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'الرجاء اختيار تصنيف';
                            }
                            return null;
                          },
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    ListenableBuilder(
                      listenable: AppProviders.getLazyPresenter<UnitPresenter>(
                          () => UnitPresenter()),
                      builder: (context, child) {
                        final unitPresenter =
                            AppProviders.getLazyPresenter<UnitPresenter>(
                                () => UnitPresenter());
                        if (unitPresenter.isLoading) {
                          return const Center(
                              child: CircularProgressIndicator());
                        }

                        return DropdownButtonFormField<String>(
                          value: _selectedUnit,
                          decoration: const InputDecoration(
                            labelText: 'وحدة القياس',
                            prefixIcon: Icon(Icons.straighten),
                          ),
                          items: [
                            ...unitPresenter.units
                                .map((unit) => DropdownMenuItem(
                                      value: unit.id,
                                      child: Text(unit.name),
                                    )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedUnit = value;
                            });
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'الرجاء اختيار وحدة قياس';
                            }
                            return null;
                          },
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    ListenableBuilder(
                      listenable:
                          AppProviders.getLazyPresenter<SupplierPresenter>(
                              () => SupplierPresenter()),
                      builder: (context, child) {
                        final supplierPresenter =
                            AppProviders.getLazyPresenter<SupplierPresenter>(
                                () => SupplierPresenter());
                        if (supplierPresenter.isLoading) {
                          return const Center(
                              child: CircularProgressIndicator());
                        }

                        return DropdownButtonFormField<String>(
                          value: _selectedSupplier,
                          decoration: const InputDecoration(
                            labelText: 'المورد (اختياري)',
                            prefixIcon: Icon(Icons.business),
                          ),
                          items: [
                            const DropdownMenuItem<String>(
                              value: null,
                              child: Text('بدون مورد'),
                            ),
                            ...supplierPresenter.suppliers
                                .map((supplier) => DropdownMenuItem(
                                      value: supplier.id,
                                      child: Text(supplier.name),
                                    )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedSupplier = value;
                            });
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              AkCard(
                child: SwitchListTile(
                  title: const Text('منتج نشط'),
                  subtitle: const Text('يمكن بيع وشراء المنتج'),
                  value: _isActive,
                  onChanged: (value) {
                    setState(() {
                      _isActive = value;
                    });
                  },
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _handleSubmit,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        widget.product != null
                            ? 'حفظ التغييرات'
                            : 'إضافة المنتج',
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قسم الصورة مع النظام الموحد AK
  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Row(
          children: [
            Icon(
              Icons.image_outlined,
              color: DynamicColors.primary,
              size: AppDimensions.iconSizeSmall,
            ),
            const SizedBox(width: AppDimensions.spacing8),
            Text(
              'صورة المنتج',
              style: AppTypography(
                fontSize: AppDimensions.mediumFontSize,
                fontWeight: FontWeight.w600,
                color: DynamicColors.textPrimary(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacing12),

        // عرض الصورة الحالية أو العنصر النائب
        Container(
          height: 200,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            border: Border.all(
              color: DynamicColors.border(context),
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            child: _buildImagePreview(),
          ),
        ),

        const SizedBox(height: AppDimensions.spacing12),

        // أزرار إدارة الصورة
        Row(
          children: [
            Expanded(
              child: AkButton(
                text: 'اختيار من المعرض',
                icon: Icons.photo_library_outlined,
                size: AkButtonSize.small,
                type: AkButtonType.secondary,
                onPressed: _pickImageFromGallery,
              ),
            ),
            const SizedBox(width: AppDimensions.spacing8),
            Expanded(
              child: AkButton(
                text: 'التقاط صورة',
                icon: Icons.camera_alt_outlined,
                size: AkButtonSize.small,
                type: AkButtonType.secondary,
                onPressed: _captureImageFromCamera,
              ),
            ),
          ],
        ),

        // زر حذف الصورة (إذا كانت موجودة)
        if (_selectedImage != null || _currentImageUrl != null) ...[
          const SizedBox(height: AppDimensions.spacing8),
          AkButton(
            text: 'حذف الصورة',
            icon: Icons.delete_outline,
            size: AkButtonSize.small,
            type: AkButtonType.danger,
            onPressed: _removeImage,
          ),
        ],
      ],
    );
  }

  /// بناء معاينة الصورة
  Widget _buildImagePreview() {
    // إذا تم اختيار صورة جديدة
    if (_selectedImage != null) {
      return Image.file(
        _selectedImage!,
        fit: BoxFit.cover,
        width: double.infinity,
        height: 200,
        errorBuilder: (context, error, stackTrace) => _buildImagePlaceholder(),
      );
    }

    // إذا كانت هناك صورة حالية
    if (_currentImageUrl != null && _currentImageUrl!.isNotEmpty) {
      return Image.file(
        File(_currentImageUrl!),
        fit: BoxFit.cover,
        width: double.infinity,
        height: 200,
        errorBuilder: (context, error, stackTrace) => _buildImagePlaceholder(),
      );
    }

    // عنصر نائب إذا لم تكن هناك صورة
    return _buildImagePlaceholder();
  }

  /// بناء عنصر نائب للصورة
  Widget _buildImagePlaceholder() {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DynamicColors.surfaceVariant(context),
            DynamicColors.surfaceVariant(context).withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_not_supported_outlined,
            size: AppDimensions.iconSizeLarge,
            color: DynamicColors.onSurfaceVariant(context),
          ),
          const SizedBox(height: AppDimensions.spacing8),
          Text(
            'لا توجد صورة',
            style: AppTypography(
              color: DynamicColors.onSurfaceVariant(context),
              fontSize: AppDimensions.smallFontSize,
            ),
          ),
          const SizedBox(height: AppDimensions.spacing4),
          Text(
            'اضغط على أحد الأزرار لإضافة صورة',
            style: AppTypography(
              color: DynamicColors.onSurfaceVariant(context),
              fontSize: AppDimensions.smallFontSize,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// اختيار صورة من المعرض باستخدام الخدمة الموجودة
  Future<void> _pickImageFromGallery() async {
    try {
      // عرض مؤشر التحميل
      _showLoadingMessage('جاري فتح المعرض...');

      final File? pickedFile = await _imageService.pickImage(
        maxWidth: 1024,
        maxHeight: 1024,
        quality: 85,
      );

      // إخفاء مؤشر التحميل
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      if (pickedFile != null) {
        // عرض مؤشر معالجة الصورة
        _showLoadingMessage('جاري معالجة الصورة...');

        // ضغط وحفظ الصورة باستخدام خدمة التحسين
        final String? optimizedPath = await _imageOptimizer.saveOptimizedImage(
          pickedFile,
          quality: 85,
          maxWidth: 1024,
          maxHeight: 1024,
        );

        // إخفاء مؤشر المعالجة
        if (mounted) {
          ScaffoldMessenger.of(context).clearSnackBars();
        }

        if (optimizedPath != null) {
          setState(() {
            _selectedImage = File(optimizedPath);
            _currentImageUrl = null; // إلغاء الصورة الحالية
          });
          _showSuccessMessage('تم اختيار الصورة وحفظها بنجاح');
        } else {
          _showErrorMessage('فشل في تحسين الصورة');
        }
      } else {
        _showInfoMessage('تم إلغاء اختيار الصورة');
      }
    } catch (e) {
      // إخفاء أي مؤشرات تحميل
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      // معالجة أخطاء محددة
      if (e.toString().contains('photo_access_denied')) {
        _showErrorMessage(
            'تم رفض الوصول للمعرض. يرجى منح الصلاحية من إعدادات التطبيق');
      } else {
        _showErrorMessage(
            'حدث خطأ أثناء اختيار الصورة. يرجى المحاولة مرة أخرى');
      }
    }
  }

  /// التقاط صورة من الكاميرا باستخدام الخدمة الموجودة
  Future<void> _captureImageFromCamera() async {
    try {
      // عرض مؤشر التحميل
      _showLoadingMessage('جاري فتح الكاميرا...');

      final File? capturedFile = await _imageService.captureImage(
        maxWidth: 1024,
        maxHeight: 1024,
        quality: 85,
      );

      // إخفاء مؤشر التحميل
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      if (capturedFile != null) {
        // عرض مؤشر معالجة الصورة
        _showLoadingMessage('جاري معالجة الصورة...');

        // ضغط وحفظ الصورة باستخدام خدمة التحسين
        final String? optimizedPath = await _imageOptimizer.saveOptimizedImage(
          capturedFile,
          quality: 85,
          maxWidth: 1024,
          maxHeight: 1024,
        );

        // إخفاء مؤشر المعالجة
        if (mounted) {
          ScaffoldMessenger.of(context).clearSnackBars();
        }

        if (optimizedPath != null) {
          setState(() {
            _selectedImage = File(optimizedPath);
            _currentImageUrl = null; // إلغاء الصورة الحالية
          });
          _showSuccessMessage('تم التقاط الصورة وحفظها بنجاح');
        } else {
          _showErrorMessage('فشل في تحسين الصورة');
        }
      } else {
        _showInfoMessage('تم إلغاء التقاط الصورة');
      }
    } catch (e) {
      // إخفاء أي مؤشرات تحميل
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      // معالجة أخطاء محددة
      if (e.toString().contains('no_available_camera')) {
        _showErrorMessage('الكاميرا غير متاحة على هذا الجهاز');
      } else if (e.toString().contains('camera_access_denied')) {
        _showErrorMessage(
            'تم رفض الوصول للكاميرا. يرجى منح الصلاحية من إعدادات التطبيق');
      } else {
        _showErrorMessage(
            'حدث خطأ أثناء التقاط الصورة. يرجى المحاولة مرة أخرى');
      }
    }
  }

  /// عرض رسالة خطأ موحدة
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: DynamicColors.error,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle_outline, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: DynamicColors.success,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// عرض رسالة معلومات
  void _showInfoMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.info_outline, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: DynamicColors.info,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// عرض رسالة تحميل
  void _showLoadingMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: DynamicColors.primary,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 10), // مدة أطول للتحميل
        ),
      );
    }
  }

  /// حذف الصورة
  void _removeImage() {
    setState(() {
      _selectedImage = null;
      _currentImageUrl = null;
    });
  }

  // مسح الباركود
  void _scanBarcode() {
    // عرض حوار لإدخال الباركود يدويًا
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إدخال الباركود'),
        content: TextField(
          controller: controller,
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'أدخل الباركود',
            prefixIcon: Icon(Icons.qr_code),
          ),
          onSubmitted: (value) {
            if (value.isNotEmpty) {
              setState(() {
                _barcodeController.text = value;
              });
              Navigator.pop(context);
            }
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final value = controller.text;
              if (value.isNotEmpty) {
                setState(() {
                  _barcodeController.text = value;
                });
              }
              Navigator.pop(context);
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  void _handleSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      final Map<String, dynamic> metadata = {};
      if (_selectedSupplier != null) {
        metadata['supplier_id'] = _selectedSupplier;
      }

      final product = Product(
        id: widget.product?.id,
        name: _nameController.text,
        description: _descriptionController.text.isEmpty
            ? null
            : _descriptionController.text,
        barcode:
            _barcodeController.text.isEmpty ? null : _barcodeController.text,
        sku: _skuController.text.isEmpty ? null : _skuController.text,
        salePrice: double.parse(_priceController.text),
        purchasePrice: _costPriceController.text.isEmpty
            ? 0
            : double.parse(_costPriceController.text),
        quantity: double.parse(_quantityController.text),
        minStock: _minStockController.text.isEmpty
            ? 0.0
            : double.parse(_minStockController.text),
        maxStock: _maxStockController.text.isEmpty
            ? null
            : double.parse(_maxStockController.text),
        categoryId: _selectedCategory,
        unitId: _selectedUnit,
        isActive: _isActive,
        metadata: metadata.isNotEmpty ? metadata : null,
        // إضافة مسار الصورة المحسنة
        imageUrl: _selectedImage?.path ?? _currentImageUrl,
      );

      Navigator.of(context).pop(product);
    }
  }
}
