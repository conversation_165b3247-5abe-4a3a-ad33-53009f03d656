import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_providers.dart';
import '../utils/app_logger.dart';
import '../../../core/theme/index.dart';

/// ودجة تلف الشاشات وتحمل الـ Presenters المطلوبة كسولياً
class LazyProviderWrapper<T extends ChangeNotifier> extends StatefulWidget {
  final Widget child;
  final T Function() presenterFactory;
  final String? presenterName;

  const LazyProviderWrapper({
    Key? key,
    required this.child,
    required this.presenterFactory,
    this.presenterName,
  }) : super(key: key);

  @override
  State<LazyProviderWrapper<T>> createState() => _LazyProviderWrapperState<T>();
}

class _LazyProviderWrapperState<T extends ChangeNotifier>
    extends State<LazyProviderWrapper<T>> {
  T? _presenter;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadPresenter();
  }

  Future<void> _loadPresenter() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // تحميل الـ presenter كسولياً
      _presenter = LazyPresenterManager.getOrCreate<T>(widget.presenterFactory);

      final presenterName = widget.presenterName ?? T.toString();
      AppLogger.info('LazyProviderWrapper: تم تحميل $presenterName بنجاح');

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      final presenterName = widget.presenterName ?? T.toString();
      AppLogger.error('LazyProviderWrapper: فشل في تحميل $presenterName: $e');

      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري تحميل البيانات...'),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: AppColors.error),
              const SizedBox(height: 16),
              Text('خطأ في التحميل: $_error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadPresenter,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    if (_presenter == null) {
      return const Scaffold(
        body: Center(
          child: Text('فشل في تحميل البيانات'),
        ),
      );
    }

    return ChangeNotifierProvider<T>.value(
      value: _presenter!,
      child: widget.child,
    );
  }

  @override
  void dispose() {
    // لا نتخلص من الـ presenter هنا لأنه مُدار بواسطة LazyPresenterManager
    super.dispose();
  }
}

/// ودجة مساعدة لتحميل عدة presenters
class MultiLazyProviderWrapper extends StatefulWidget {
  final Widget child;
  final List<LazyPresenterConfig> presenters;

  const MultiLazyProviderWrapper({
    Key? key,
    required this.child,
    required this.presenters,
  }) : super(key: key);

  @override
  State<MultiLazyProviderWrapper> createState() =>
      _MultiLazyProviderWrapperState();
}

class _MultiLazyProviderWrapperState extends State<MultiLazyProviderWrapper> {
  bool _isLoading = true;
  String? _error;
  final List<ChangeNotifier> _loadedPresenters = [];

  @override
  void initState() {
    super.initState();
    _loadPresenters();
  }

  Future<void> _loadPresenters() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      _loadedPresenters.clear();

      for (final config in widget.presenters) {
        final presenter = config.factory();
        _loadedPresenters.add(presenter);
        AppLogger.info(
            'MultiLazyProviderWrapper: تم تحميل ${config.name} بنجاح');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error(
          'MultiLazyProviderWrapper: فشل في تحميل الـ presenters: $e');

      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري تحميل البيانات...'),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: AppColors.error),
              const SizedBox(height: 16),
              Text('خطأ في التحميل: $_error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadPresenters,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    // بناء MultiProvider مع جميع الـ presenters المحملة
    Widget child = widget.child;

    for (int i = widget.presenters.length - 1; i >= 0; i--) {
      child = ChangeNotifierProvider.value(
        value: _loadedPresenters[i],
        child: child,
      );
    }

    return child;
  }

  @override
  void dispose() {
    // لا نتخلص من الـ presenters هنا لأنها مُدارة بواسطة LazyPresenterManager
    super.dispose();
  }
}

/// تكوين presenter للتحميل الكسول
class LazyPresenterConfig {
  final String name;
  final ChangeNotifier Function() factory;

  const LazyPresenterConfig({
    required this.name,
    required this.factory,
  });
}

/// دوال مساعدة لاستخدام التحميل الكسول
class LazyLoadingHelper {
  /// تحميل presenter محدد
  static T loadPresenter<T extends ChangeNotifier>(T Function() factory) {
    return LazyPresenterManager.getOrCreate<T>(factory);
  }

  /// التحقق من تحميل presenter
  static bool isPresenterLoaded<T>() {
    return LazyPresenterManager.exists<T>();
  }

  /// الحصول على إحصائيات التحميل
  static Map<String, dynamic> getLoadingStats() {
    return LazyPresenterManager.getMemoryStats();
  }

  /// تنظيف presenter محدد
  static void unloadPresenter<T>() {
    LazyPresenterManager.clear<T>();
  }

  /// تنظيف جميع الـ presenters
  static void unloadAllPresenters() {
    LazyPresenterManager.clearAll();
  }
}

/// مثال على الاستخدام:
///
/// ```dart
/// class ProductsScreen extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     return LazyProviderWrapper<ProductPresenter>(
///       presenterFactory: () => ProductPresenter(),
///       presenterName: 'ProductPresenter',
///       child: Scaffold(
///         appBar: AppBar(title: Text('المنتجات')),
///         body: Consumer<ProductPresenter>(
///           builder: (context, presenter, child) {
///             return ListView.builder(
///               itemCount: presenter.products.length,
///               itemBuilder: (context, index) {
///                 return ListTile(
///                   title: Text(presenter.products[index].name),
///                 );
///               },
///             );
///           },
///         ),
///       ),
///     );
///   }
/// }
/// ```
///
/// أو استخدام الطريقة المبسطة:
/// ```dart
/// class ProductsScreen extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     final presenter = AppProviders.getLazyPresenter<ProductPresenter>(
///       () => ProductPresenter(),
///     );
///
///     return ChangeNotifierProvider.value(
///       value: presenter,
///       child: Scaffold(
///         // باقي الكود...
///       ),
///     );
///   }
/// }
/// ```
