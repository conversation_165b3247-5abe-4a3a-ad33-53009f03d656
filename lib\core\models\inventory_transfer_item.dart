import 'package:uuid/uuid.dart';
import 'base_model.dart';
import 'product.dart';

/// نموذج عنصر تحويل المخزون
class InventoryTransferItem extends BaseModel {
  final String transferId;
  final String productId;
  final Product? product; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final double quantity;
  final String? notes;

  InventoryTransferItem({
    String? id,
    required this.transferId,
    required this.productId,
    this.product,
    required this.quantity,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: null,
          updatedAt: updatedAt,
          updatedBy: null,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا العنصر مع استبدال الحقول المحددة بقيم جديدة
  InventoryTransferItem copyWith({
    String? id,
    String? transferId,
    String? productId,
    Product? product,
    double? quantity,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
  }) {
    return InventoryTransferItem(
      id: id ?? this.id,
      transferId: transferId ?? this.transferId,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل عنصر التحويل إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'transfer_id': transferId,
      'product_id': productId,
      'quantity': quantity,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء عنصر تحويل من Map
  factory InventoryTransferItem.fromMap(Map<String, dynamic> map) {
    return InventoryTransferItem(
      id: map['id'],
      transferId: map['transfer_id'],
      productId: map['product_id'],
      product: map['product'] != null ? Product.fromMap(map['product']) : null,
      quantity: map['quantity'] is int
          ? (map['quantity'] as int).toDouble()
          : (map['quantity'] as double? ?? 0.0),
      notes: map['notes'],
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      isDeleted: map['is_deleted'] == 1,
    );
  }

  @override
  String toString() {
    return 'InventoryTransferItem(id: $id, productId: $productId, quantity: $quantity)';
  }
}
