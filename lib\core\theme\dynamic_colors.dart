import 'package:flutter/material.dart';
import 'app_colors.dart';

/// نظام الألوان الديناميكي الذي يتكيف مع الثيم الحالي
/// يوفر ألوان متكيفة حسب الوضع الفاتح أو الداكن
class DynamicColors {
  // منع إنشاء كائن من هذا الكلاس
  DynamicColors._();

  /// الحصول على لون الخلفية المتكيف
  static Color background(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkBackground : AppColors.lightBackground;
  }

  /// الحصول على لون السطح المتكيف
  static Color surface(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkSurface : AppColors.lightSurface;
  }

  /// الحصول على لون النص الأساسي المتكيف
  static Color textPrimary(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary;
  }

  /// الحصول على لون النص الثانوي المتكيف
  static Color textSecondary(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkTextSecondary : AppColors.lightTextSecondary;
  }

  /// الحصول على لون الحدود المتكيف
  static Color border(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkBorder : AppColors.lightBorder;
  }

  /// الحصول على لون الفاصل المتكيف
  static Color divider(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkDivider : AppColors.lightDivider;
  }

  /// الحصول على لون الظل المتكيف
  static Color shadow(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkShadow : AppColors.lightShadow;
  }

  /// الحصول على لون البطاقة المتكيف
  static Color card(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkSurface : AppColors.lightSurface;
  }

  /// الحصول على لون الأيقونة المتكيف
  static Color icon(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkTextSecondary : AppColors.lightTextSecondary;
  }

  /// الحصول على لون النص المعطل المتكيف
  static Color textDisabled(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkTextDisabled : AppColors.lightTextDisabled;
  }

  /// الحصول على لون النص التلميحي المتكيف
  static Color textHint(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkTextHint : AppColors.lightTextHint;
  }

  /// الحصول على لون متغير السطح المتكيف
  static Color surfaceVariant(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark
        ? AppColors.darkSurfaceVariant
        : AppColors.lightSurfaceVariant;
  }

  /// الحصول على لون النص على الخلفية المتكيف
  static Color onBackground(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkOnBackground : AppColors.lightOnBackground;
  }

  /// الحصول على لون النص على السطح المتكيف
  static Color onSurface(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColors.darkOnSurface : AppColors.lightOnSurface;
  }

  /// الحصول على لون النص على متغير السطح المتكيف
  static Color onSurfaceVariant(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark
        ? AppColors.darkOnSurfaceVariant
        : AppColors.lightOnSurfaceVariant;
  }

  // ========== ألوان ديناميكية تتكيف مع الثيم المختار ==========

  /// الحصول على اللون الأساسي الديناميكي من الثيم الحالي
  /// يتكيف مع تغيير اللون الأساسي (أحمر/أخضر/أزرق) والوضع (فاتح/مظلم)
  static Color primaryDynamic(BuildContext context) {
    return Theme.of(context).colorScheme.primary;
  }

  /// الحصول على اللون الأساسي الداكن الديناميكي
  static Color primaryDarkDynamic(BuildContext context) {
    return Theme.of(context).colorScheme.primary;
  }

  /// الحصول على اللون الأساسي الفاتح الديناميكي
  static Color primaryLightDynamic(BuildContext context) {
    return Theme.of(context).colorScheme.primaryContainer;
  }

  // ========== ألوان ثابتة للاستخدام بدون context ==========

  /// الألوان الأساسية الثابتة (للاستخدام عند عدم توفر context)
  static Color get primary => AppColors.primary;
  static Color get primaryDark => AppColors.primaryDark;
  static Color get primaryLight => AppColors.primaryLight;
  static Color get secondary => AppColors.secondary;
  static Color get accent => AppColors.accent;

  /// ألوان الحالة (لا تتغير مع الثيم)
  static Color get success => AppColors.success;
  static Color get warning => AppColors.warning;
  static Color get error => AppColors.error;
  static Color get info => AppColors.info;

  /// ألوان الحالة الفاتحة
  static Color get successLight => AppColors.successLight;
  static Color get warningLight => AppColors.warningLight;
  static Color get errorLight => AppColors.errorLight;
  static Color get infoLight => AppColors.infoLight;

  /// ألوان الحالة الداكنة
  static Color get successDark => AppColors.successDark;
  static Color get warningDark => AppColors.warningDark;
  static Color get errorDark => AppColors.errorDark;
  static Color get infoDark => AppColors.infoDark;

  /// ألوان إضافية
  static Color get amber => AppColors.amber;
  static Color get teal => AppColors.teal;
  static Color get indigo => AppColors.indigo;
  static Color get brown => AppColors.brown;
  static Color get deepOrange => AppColors.deepOrange;

  // ========== دوال مساعدة ==========

  /// فحص ما إذا كان الثيم داكن
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  /// الحصول على لون متكيف بناءً على الثيم
  static Color adaptive(
    BuildContext context, {
    required Color light,
    required Color dark,
  }) {
    return isDarkMode(context) ? dark : light;
  }

  /// الحصول على لون النص المناسب للخلفية
  static Color getTextColorForBackground(
    BuildContext context,
    Color backgroundColor,
  ) {
    return AppColors.getTextColorForBackground(backgroundColor);
  }

  /// الحصول على لون الأيقونة المناسب للخلفية
  static Color getIconColorForBackground(
    BuildContext context,
    Color backgroundColor,
  ) {
    return AppColors.getIconColorForBackground(backgroundColor);
  }

  /// إنشاء تدرج متكيف
  static LinearGradient adaptiveGradient(
    BuildContext context, {
    required List<Color> lightColors,
    required List<Color> darkColors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    final colors = isDarkMode(context) ? darkColors : lightColors;
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
    );
  }

  /// إنشاء ظل متكيف
  static List<BoxShadow> adaptiveShadow(
    BuildContext context, {
    double blurRadius = 8.0,
    double spreadRadius = 0.0,
    Offset offset = const Offset(0, 2),
    double opacity = 0.15,
  }) {
    final shadowColor = shadow(context);
    return [
      BoxShadow(
        color: shadowColor.withValues(alpha: opacity),
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
        offset: offset,
      ),
    ];
  }

  /// الحصول على لون الحالة المتكيف
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'نشط':
        return AppColors.statusActive;
      case 'inactive':
      case 'غير نشط':
        return AppColors.statusInactive;
      case 'pending':
      case 'انتظار':
        return AppColors.statusPending;
      case 'cancelled':
      case 'ملغي':
        return AppColors.statusCancelled;
      default:
        return AppColors.secondary;
    }
  }

  /// الحصول على لون الوحدة
  static Color getModuleColor(String module) {
    return AppColors.getModuleColor(module);
  }

  /// الحصول على لون نوع المعاملة
  static Color getTransactionTypeColor(String type) {
    return AppColors.getTransactionTypeColor(type);
  }

  /// الحصول على لون مستوى الوصول
  static Color getAccessLevelColor(String level) {
    return AppColors.getAccessLevelColor(level);
  }

  /// الحصول على لون حالة المزامنة
  static Color getSyncStatusColor(String status) {
    return AppColors.getSyncStatusColor(status);
  }

  /// الحصول على لون الأولوية
  static Color getPriorityColor(String priority) {
    return AppColors.getPriorityColor(priority);
  }

  /// الحصول على لون نوع الحساب
  static Color getAccountTypeColor(String type) {
    return AppColors.getAccountTypeColor(type);
  }
}
