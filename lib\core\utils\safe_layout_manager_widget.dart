import 'package:flutter/material.dart';

/// ودجت مدير التخطيط الآمن
/// يستخدم لتغليف المحتوى وضمان عدم حدوث مشاكل في التخطيط
class SafeLayoutManagerWidget extends StatelessWidget {
  final Widget child;
  final bool preventOverflow;
  final bool enableScrolling;
  final EdgeInsetsGeometry? padding;

  const SafeLayoutManagerWidget({
    Key? key,
    required this.child,
    this.preventOverflow = true,
    this.enableScrolling = true,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget content = child;

    // إضافة حشوة إذا تم تحديدها
    if (padding != null) {
      content = Padding(
        padding: padding!,
        child: content,
      );
    }

    // تطبيق التخطيط الآمن بطريقة مختلفة لتجنب التكرار اللانهائي
    if (preventOverflow) {
      // استخدام SizedBox بدلاً من Container مع LayoutBuilder
      content = SizedBox(
        width: double.infinity,
        child: content,
      );
    }

    // تمكين التمرير إذا كان مطلوبًا - يجب أن يكون هذا بعد منع التجاوز
    if (enableScrolling) {
      content = SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: content,
      );
    }

    return content;
  }
}
