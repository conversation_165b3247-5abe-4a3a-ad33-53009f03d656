import 'package:uuid/uuid.dart';
import 'base_model.dart';
import 'account.dart';

/// نموذج فئة الحساب
class AccountCategory extends BaseModel {
  final String name;
  final String? description;
  final AccountType accountType;
  final bool isActive;

  AccountCategory({
    String? id,
    required this.name,
    this.description,
    required this.accountType,
    this.isActive = true,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذه الفئة مع استبدال الحقول المحددة بقيم جديدة
  AccountCategory copyWith({
    String? id,
    String? name,
    String? description,
    AccountType? accountType,
    bool? isActive,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return AccountCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      accountType: accountType ?? this.accountType,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل فئة الحساب إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': 'account', // نوع الفئة (حساب)
      'account_type': accountType.toString().split('.').last, // نوع الحساب
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء فئة حساب من Map
  factory AccountCategory.fromMap(Map<String, dynamic> map) {
    return AccountCategory(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      accountType: _parseAccountType(map['account_type']),
      isActive: map['is_active'] == 1,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحليل نوع الحساب من النص
  static AccountType _parseAccountType(String? typeString) {
    switch (typeString) {
      case 'asset':
        return AccountType.asset;
      case 'liability':
        return AccountType.liability;
      case 'equity':
        return AccountType.equity;
      case 'revenue':
        return AccountType.revenue;
      case 'expense':
        return AccountType.expense;
      default:
        return AccountType.asset;
    }
  }

  @override
  String toString() {
    return 'AccountCategory(id: $id, name: $name, accountType: $accountType)';
  }
}
