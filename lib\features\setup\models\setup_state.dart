import 'package:flutter/material.dart';

/// نموذج خطوات الإعداد
/// يستخدم لتحديد الخطوات المختلفة في عملية إعداد النظام
enum SetupStep {
  /// التحقق من قاعدة البيانات الحالية
  checkingExistingDatabase,

  /// إنشاء قاعدة البيانات
  creatingDatabase,

  /// إنشاء الجداول
  creatingTables,

  /// تهيئة البيانات الأساسية
  initializingBasicData,

  /// تهيئة الإعدادات
  initializingSettings,

  /// إنهاء الإعداد
  finalizingSetup,

  /// اكتمال الإعداد
  completed,

  /// حدوث خطأ
  error
}

/// نموذج حالة الإعداد
/// يستخدم لإدارة حالة شاشة الإعداد الأولي
class SetupState extends ChangeNotifier {
  /// الخطوة الحالية
  SetupStep _currentStep;

  /// نسبة التقدم (0.0 - 1.0)
  double _progress;

  /// رسالة الحالة
  String _message;

  /// رسالة الخطأ (إذا حدث خطأ)
  String? _error;

  /// قائمة الخطوات المكتملة
  final List<SetupStep> _completedSteps = [];

  /// قائمة الخطوات الفاشلة
  final List<SetupStep> _failedSteps = [];

  /// الوقت المستغرق لكل خطوة (بالمللي ثانية)
  final Map<SetupStep, int> _stepDurations = {};

  /// وقت بدء الخطوة الحالية
  DateTime? _stepStartTime;

  /// إجمالي الوقت المستغرق (بالمللي ثانية)
  int _totalDuration = 0;

  /// المنشئ
  SetupState({
    SetupStep currentStep = SetupStep.checkingExistingDatabase,
    double progress = 0.0,
    String message = 'جاري التحقق من قاعدة البيانات الحالية...',
    String? error,
  })  : _currentStep = currentStep,
        _progress = progress,
        _message = message,
        _error = error {
    _stepStartTime = DateTime.now();
  }

  /// الحصول على الخطوة الحالية
  SetupStep get currentStep => _currentStep;

  /// الحصول على الخطوة الحالية (للويدجت)
  SetupStep get step => _currentStep;

  /// الحصول على نسبة التقدم
  double get progress => _progress;

  /// الحصول على رسالة الحالة
  String get message => _message;

  /// الحصول على رسالة الخطأ
  String? get error => _error;

  /// الحصول على قائمة الخطوات المكتملة
  List<SetupStep> get completedSteps => List.unmodifiable(_completedSteps);

  /// الحصول على قائمة الخطوات الفاشلة
  List<SetupStep> get failedSteps => List.unmodifiable(_failedSteps);

  /// الحصول على إجمالي الوقت المستغرق
  int get totalDuration => _totalDuration;

  /// الحصول على الوقت المستغرق لكل خطوة
  Map<SetupStep, int> get stepDurations => Map.unmodifiable(_stepDurations);

  /// تحديث حالة الإعداد
  void updateState({
    required SetupStep step,
    required double progress,
    required String message,
    String? error,
  }) {
    // حساب الوقت المستغرق للخطوة السابقة
    if (_stepStartTime != null && _currentStep != step) {
      final duration =
          DateTime.now().difference(_stepStartTime!).inMilliseconds;
      _stepDurations[_currentStep] = duration;
      _totalDuration += duration;

      // إضافة الخطوة السابقة إلى قائمة الخطوات المكتملة
      if (_currentStep != SetupStep.error &&
          !_completedSteps.contains(_currentStep)) {
        _completedSteps.add(_currentStep);
      }

      // إعادة تعيين وقت بدء الخطوة الحالية
      _stepStartTime = DateTime.now();
    }

    _currentStep = step;
    _progress = progress;
    _message = message;
    _error = error;

    // إذا كانت الخطوة الحالية هي خطأ، أضفها إلى قائمة الخطوات الفاشلة
    if (step == SetupStep.error && !_failedSteps.contains(_currentStep)) {
      _failedSteps.add(_currentStep);
    }

    notifyListeners();
  }

  /// التحقق مما إذا كانت الخطوة مكتملة
  bool isStepCompleted(SetupStep step) {
    return _completedSteps.contains(step);
  }

  /// التحقق مما إذا كانت الخطوة فاشلة
  bool isStepFailed(SetupStep step) {
    return _failedSteps.contains(step);
  }

  /// إعادة تعيين حالة الإعداد إلى الحالة الأولية
  void resetState() {
    _currentStep = SetupStep.checkingExistingDatabase;
    _progress = 0.0;
    _message = 'جاري التحقق من قاعدة البيانات الحالية...';
    _error = null;
    _completedSteps.clear();
    _failedSteps.clear();
    _stepDurations.clear();
    _totalDuration = 0;
    _stepStartTime = DateTime.now();
    notifyListeners();
  }

  /// الحصول على عنوان الخطوة
  static String getStepTitle(SetupStep step) {
    switch (step) {
      case SetupStep.checkingExistingDatabase:
        return 'التحقق من قاعدة البيانات الحالية';
      case SetupStep.creatingDatabase:
        return 'إنشاء قاعدة البيانات';
      case SetupStep.creatingTables:
        return 'إنشاء الجداول';
      case SetupStep.initializingBasicData:
        return 'تهيئة البيانات الأساسية';
      case SetupStep.initializingSettings:
        return 'تهيئة الإعدادات';
      case SetupStep.finalizingSetup:
        return 'إنهاء الإعداد';
      case SetupStep.completed:
        return 'اكتمال الإعداد';
      case SetupStep.error:
        return 'حدث خطأ';
    }
  }
}
