import '../database/database_service.dart';
import '../models/expense.dart';
import '../models/category.dart';
import '../services/category_service.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';

/// خدمة للعمليات المتعلقة بالمصروفات
class ExpenseService {
  // نمط Singleton
  static final ExpenseService _instance = ExpenseService._internal();
  factory ExpenseService() => _instance;
  ExpenseService._internal();

  final DatabaseService _db = DatabaseService.instance;
  final CategoryService _categoryService = CategoryService();

  /// الحصول على جميع المصروفات
  Future<List<Expense>> getAllExpenses({
    bool includeDeleted = false,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? categoryId,
  }) async {
    try {
      AppLogger.info('الحصول على جميع المصروفات');

      // بناء شرط WHERE
      String whereClause = includeDeleted ? '' : 'e.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause += whereClause.isEmpty ? '' : ' AND ';
        whereClause += '(e.description LIKE ? OR e.reference_number LIKE ?)';
        whereArgs.addAll(['%$searchQuery%', '%$searchQuery%']);
      }

      if (startDate != null) {
        whereClause += whereClause.isEmpty ? '' : ' AND ';
        whereClause += 'e.expense_date >= ?';
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereClause += whereClause.isEmpty ? '' : ' AND ';
        whereClause += 'e.expense_date <= ?';
        whereArgs.add(endDate.toIso8601String());
      }

      if (categoryId != null) {
        whereClause += whereClause.isEmpty ? '' : ' AND ';
        whereClause += 'e.category_id = ?';
        whereArgs.add(categoryId);
      }

      // استعلام قاعدة البيانات مع JOIN للحصول على أسماء الفئات
      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          e.*,
          c.name as category_name,
          u.name as user_name
        FROM expenses e
        LEFT JOIN ${DatabaseService.tableCategories} c ON e.category_id = c.id AND c.type = 'expense'
        LEFT JOIN users u ON e.created_by = u.id
        ${whereClause.isNotEmpty ? 'WHERE $whereClause' : ''}
        ORDER BY e.expense_date DESC
      ''', whereArgs);

      // تحويل إلى كائنات Expense
      return List.generate(maps.length, (i) {
        return Expense.fromMap(maps[i]);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع المصروفات',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على مصروف بواسطة المعرف
  Future<Expense?> getExpenseById(String id) async {
    try {
      AppLogger.info('الحصول على مصروف بواسطة المعرف: $id');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          e.*,
          c.name as category_name,
          u.name as user_name
        FROM expenses e
        LEFT JOIN ${DatabaseService.tableCategories} c ON e.category_id = c.id AND c.type = 'expense'
        LEFT JOIN users u ON e.created_by = u.id
        WHERE e.id = ?
      ''', [id]);

      if (maps.isEmpty) {
        return null;
      }

      return Expense.fromMap(maps.first);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على مصروف بواسطة المعرف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return null;
    }
  }

  /// إضافة مصروف جديد
  Future<bool> addExpense(Expense expense, {String? userId}) async {
    try {
      AppLogger.info('إضافة مصروف جديد: ${expense.description}');

      final expenseMap = expense.toMap();

      // تعيين created_by إذا تم توفيره
      if (userId != null) {
        expenseMap['created_by'] = userId;
      }

      await _db.insert('expenses', expenseMap);

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة مصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'expense': expense.toString()},
      );
      return false;
    }
  }

  /// تحديث مصروف
  Future<bool> updateExpense(Expense expense, {String? userId}) async {
    try {
      AppLogger.info('تحديث مصروف: ${expense.id}');

      final expenseMap = expense.toMap();

      // تعيين updated_by إذا تم توفيره
      if (userId != null) {
        expenseMap['updated_by'] = userId;
      }

      // تعيين وقت التحديث
      expenseMap['updated_at'] = DateTime.now().toIso8601String();

      await _db.update(
        'expenses',
        expenseMap,
        where: 'id = ?',
        whereArgs: [expense.id],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث مصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'expense': expense.toString()},
      );
      return false;
    }
  }

  /// حذف مصروف (حذف منطقي)
  Future<bool> deleteExpense(String id, {String? userId}) async {
    try {
      AppLogger.info('حذف مصروف: $id');

      final now = DateTime.now().toIso8601String();

      await _db.update(
        'expenses',
        {
          'is_deleted': 1,
          'updated_at': now,
          'updated_by': userId,
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف مصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// الحصول على جميع فئات المصروفات
  Future<List<Category>> getAllExpenseCategories({
    bool includeInactive = false,
    String? searchQuery,
  }) async {
    try {
      AppLogger.info('الحصول على جميع فئات المصروفات');

      // استخدام خدمة الفئات الموحدة
      return await _categoryService.getAllCategories(
        includeInactive: includeInactive,
        searchQuery: searchQuery,
        type: 'expense',
      );
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع فئات المصروفات',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على فئة مصروف بواسطة المعرف
  Future<Category?> getExpenseCategoryById(String id) async {
    try {
      AppLogger.info('الحصول على فئة مصروف بواسطة المعرف: $id');

      // استخدام خدمة الفئات الموحدة
      final category = await _categoryService.getCategoryById(id);

      if (category == null || !category.isExpenseCategory) {
        return null;
      }

      return category;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على فئة مصروف بواسطة المعرف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return null;
    }
  }

  /// إضافة فئة مصروف جديدة
  Future<bool> addExpenseCategory(Category category, {String? userId}) async {
    try {
      AppLogger.info('إضافة فئة مصروف جديدة: ${category.name}');

      // التأكد من أن نوع الفئة هو مصروفات
      final categoryToAdd = category.copyWith(type: 'expense');

      // استخدام خدمة الفئات الموحدة
      return await _categoryService.addCategory(categoryToAdd, userId: userId);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة فئة مصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'category': category.toString()},
      );
      return false;
    }
  }

  /// تحديث فئة مصروف
  Future<bool> updateExpenseCategory(Category category,
      {String? userId}) async {
    try {
      AppLogger.info('تحديث فئة مصروف: ${category.id}');

      // التأكد من أن نوع الفئة هو مصروفات
      final categoryToUpdate = category.copyWith(type: 'expense');

      // استخدام خدمة الفئات الموحدة
      return await _categoryService.updateCategory(categoryToUpdate,
          userId: userId);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث فئة مصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'category': category.toString()},
      );
      return false;
    }
  }

  /// حذف فئة مصروف (حذف منطقي)
  Future<bool> deleteExpenseCategory(String id, {String? userId}) async {
    try {
      AppLogger.info('حذف فئة مصروف: $id');

      // استخدام خدمة الفئات الموحدة
      return await _categoryService.deleteCategory(id,
          userId: userId, type: 'expense');
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف فئة مصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }
}
