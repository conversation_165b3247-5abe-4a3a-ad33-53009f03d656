/// حالات المبيعات
enum SaleStatus {
  /// مسودة
  draft,

  /// معلق
  pending,

  /// مكتمل
  completed,

  /// ملغي
  cancelled,

  /// مرتجع
  returned,
}

/// تحويل حالة المبيع إلى نص
String saleStatusToString(SaleStatus status) {
  switch (status) {
    case SaleStatus.draft:
      return 'draft';
    case SaleStatus.pending:
      return 'pending';
    case SaleStatus.completed:
      return 'completed';
    case SaleStatus.cancelled:
      return 'cancelled';
    case SaleStatus.returned:
      return 'returned';
  }
}

/// تحويل نص إلى حالة مبيع
SaleStatus stringToSaleStatus(String status) {
  switch (status.toLowerCase()) {
    case 'draft':
      return SaleStatus.draft;
    case 'pending':
      return SaleStatus.pending;
    case 'completed':
      return SaleStatus.completed;
    case 'cancelled':
      return SaleStatus.cancelled;
    case 'returned':
      return SaleStatus.returned;
    default:
      return SaleStatus.draft;
  }
}

/// الحصول على اسم حالة المبيع بالعربية
String getSaleStatusName(SaleStatus status) {
  switch (status) {
    case SaleStatus.draft:
      return 'مسودة';
    case SaleStatus.pending:
      return 'معلق';
    case SaleStatus.completed:
      return 'مكتمل';
    case SaleStatus.cancelled:
      return 'ملغي';
    case SaleStatus.returned:
      return 'مرتجع';
  }
}

/// الحصول على اسم حالة المبيع بالإنجليزية
String getSaleStatusNameEnglish(SaleStatus status) {
  switch (status) {
    case SaleStatus.draft:
      return 'Draft';
    case SaleStatus.pending:
      return 'Pending';
    case SaleStatus.completed:
      return 'Completed';
    case SaleStatus.cancelled:
      return 'Cancelled';
    case SaleStatus.returned:
      return 'Returned';
  }
}
