import 'package:flutter/foundation.dart';
import '../../../core/utils/error_tracker.dart';
import '../models/cart_item.dart';
import '../../products/presenters/product_presenter.dart';
import '../../customers/presenters/customer_presenter.dart';

/// مقدم نقطة البيع
class POSPresenter extends ChangeNotifier {
  final ProductPresenter productPresenter;
  final CustomerPresenter customerPresenter;

  final List<CartItem> _cart = [];
  double _cartDiscount = 0.0;
  bool _isDiscountPercentage = false;
  bool _isLoading = false;
  String? _errorMessage;

  POSPresenter({
    required this.productPresenter,
    required this.customerPresenter,
  });

  /// الحصول على قائمة عناصر السلة
  List<CartItem> get cart => _cart;

  /// الحصول على خصم السلة
  double get cartDiscount => _cartDiscount;

  /// هل الخصم نسبة مئوية؟
  bool get isDiscountPercentage => _isDiscountPercentage;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// تهيئة المقدم
  Future<void> init() async {
    try {
      setLoading(true);

      // تحميل المنتجات والعملاء
      await productPresenter.loadProducts();
      await customerPresenter.loadCustomers();

      // تهيئة السلة
      initializeCart();

      setLoading(false);
    } catch (e, stackTrace) {
      setErrorMessage('حدث خطأ أثناء تهيئة نقطة البيع: $e');
      ErrorTracker.captureError(
        'خطأ في تهيئة نقطة البيع',
        error: e,
        stackTrace: stackTrace,
      );
      setLoading(false);
    }
  }

  /// تهيئة السلة
  void initializeCart() {
    _cart.clear();
    _cartDiscount = 0.0;
    _isDiscountPercentage = false;
    notifyListeners();
  }

  /// إضافة منتج إلى السلة
  void addToCart(CartItem item) {
    try {
      // البحث عن المنتج في السلة
      final index = _cart.indexWhere((i) => i.product.id == item.product.id);

      if (index >= 0) {
        // إذا كان المنتج موجوداً بالفعل، زيادة الكمية
        final existingItem = _cart[index];
        _cart[index] =
            existingItem.copyWith(quantity: existingItem.quantity + 1);
      } else {
        // إضافة المنتج كعنصر جديد
        _cart.add(item);
      }

      notifyListeners();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في إضافة منتج إلى السلة',
        error: e,
        stackTrace: stackTrace,
        context: {
          'product_id': item.product.id,
          'product_name': item.product.name,
        },
      );
    }
  }

  /// تحديث كمية منتج في السلة
  void updateCartItemQuantity(String productId, double quantity) {
    try {
      if (quantity <= 0) {
        // إذا كانت الكمية صفر أو أقل، حذف المنتج من السلة
        removeFromCart(productId);
        return;
      }

      final index = _cart.indexWhere((item) => item.product.id == productId);

      if (index >= 0) {
        final item = _cart[index];
        _cart[index] = item.copyWith(quantity: quantity);
        notifyListeners();
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في تحديث كمية منتج في السلة',
        error: e,
        stackTrace: stackTrace,
        context: {
          'product_id': productId,
          'quantity': quantity,
        },
      );
    }
  }

  /// حذف منتج من السلة
  void removeFromCart(String productId) {
    try {
      _cart.removeWhere((item) => item.product.id == productId);
      notifyListeners();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في حذف منتج من السلة',
        error: e,
        stackTrace: stackTrace,
        context: {
          'product_id': productId,
        },
      );
    }
  }

  /// تفريغ السلة
  void clearCart() {
    _cart.clear();
    _cartDiscount = 0.0;
    _isDiscountPercentage = false;
    notifyListeners();
  }

  /// تطبيق خصم على السلة
  void applyDiscount(double discount, bool isPercentage) {
    try {
      _cartDiscount = discount;
      _isDiscountPercentage = isPercentage;

      // التحقق من أن الخصم لا يتجاوز المجموع الفرعي
      if (isPercentage) {
        if (discount > 100) {
          _cartDiscount = 100;
        }
      } else {
        if (discount > cartSubtotal) {
          _cartDiscount = cartSubtotal;
        }
      }

      notifyListeners();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في تطبيق خصم على السلة',
        error: e,
        stackTrace: stackTrace,
        context: {
          'discount': discount,
          'is_percentage': isPercentage,
        },
      );
    }
  }

  /// حساب المجموع الفرعي للسلة
  double get cartSubtotal {
    return _cart.fold(0, (sum, item) => sum + item.subtotal);
  }

  /// حساب قيمة الخصم
  double get cartDiscountAmount {
    if (_isDiscountPercentage) {
      return cartSubtotal * (_cartDiscount / 100);
    } else {
      return _cartDiscount;
    }
  }

  /// حساب المجموع بعد الخصم
  double get cartTotalAfterDiscount {
    return cartSubtotal - cartDiscountAmount;
  }

  /// حساب قيمة الضريبة
  double get cartTaxAmount {
    return _cart.fold(0, (sum, item) {
      final itemSubtotal = item.subtotal;
      final itemDiscountAmount = _isDiscountPercentage
          ? itemSubtotal * (_cartDiscount / 100)
          : (_cartDiscount > 0
              ? (itemSubtotal / cartSubtotal) * _cartDiscount
              : 0);
      final itemTotalAfterDiscount = itemSubtotal - itemDiscountAmount;
      return sum + (itemTotalAfterDiscount * (item.taxRate / 100));
    });
  }

  /// حساب المجموع النهائي للسلة
  double get cartTotal {
    return cartTotalAfterDiscount + cartTaxAmount;
  }

  /// الحصول على عنصر من السلة بواسطة معرف المنتج
  CartItem? getCartItemByProductId(String productId) {
    try {
      return _cart.firstWhere((item) => item.product.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// تعيين حالة التحميل
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void setErrorMessage(String? message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// البحث عن منتج بواسطة الباركود
  Future<CartItem?> findProductByBarcode(String barcode) async {
    try {
      if (barcode.isEmpty) return null;

      // البحث عن المنتج بواسطة الباركود
      final product = productPresenter.findProductByBarcode(barcode);

      if (product == null) return null;

      // إنشاء عنصر سلة جديد
      return CartItem(
        product: product,
        quantity: 1,
        price: product.salePrice,
        discount: 0,
        isDiscountPercentage: false,
        taxRate: 0,
      );
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في البحث عن منتج بواسطة الباركود',
        error: e,
        stackTrace: stackTrace,
        context: {
          'barcode': barcode,
        },
      );
      return null;
    }
  }

  /// البحث عن منتجات بواسطة نص البحث
  Future<void> searchProducts(String query) async {
    await productPresenter.searchProducts(query);
  }

  /// الحصول على فئات المنتجات
  List<dynamic> get productCategories {
    return productPresenter.categories;
  }

  /// الحصول على قائمة العملاء
  List<dynamic> get customers {
    return customerPresenter.customers;
  }

  /// البحث عن عملاء بواسطة نص البحث
  List<dynamic> searchCustomers(String query) {
    return customerPresenter.searchCustomers(query);
  }
}
