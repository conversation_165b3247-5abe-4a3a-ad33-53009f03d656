import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';

/// مؤشر تقدم دائري متحرك
class AnimatedCircularProgressIndicator extends StatefulWidget {
  /// قيمة التقدم (0.0 إلى 1.0)
  final double value;

  /// نص العنوان
  final String? title;

  /// نص القيمة
  final String? valueText;

  /// لون التقدم
  final Color color;

  /// لون الخلفية
  final Color backgroundColor;

  /// سمك الخط
  final double strokeWidth;

  /// قطر الدائرة
  final double size;

  /// مدة الحركة
  final Duration duration;

  /// نمط نص العنوان
  final AppTypography? titleStyle;

  /// نمط نص القيمة
  final AppTypography? valueStyle;

  const AnimatedCircularProgressIndicator({
    Key? key,
    required this.value,
    this.title,
    this.valueText,
    required this.color,
    this.backgroundColor = AppColors.lightTextSecondary,
    this.strokeWidth = 10.0,
    this.size = 100.0,
    this.duration = const Duration(milliseconds: 1500),
    this.titleStyle,
    this.valueStyle,
  }) : super(key: key);

  @override
  State<AnimatedCircularProgressIndicator> createState() =>
      _AnimatedCircularProgressIndicatorState();
}

class _AnimatedCircularProgressIndicatorState
    extends State<AnimatedCircularProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    _animation = Tween<double>(begin: 0, end: widget.value).animate(
        CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic))
      ..addListener(() {
        setState(() {});
      });
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedCircularProgressIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _animation = Tween<double>(begin: oldWidget.value, end: widget.value)
          .animate(
              CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic));
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: widget.size,
          height: widget.size,
          child: Stack(
            children: [
              Center(
                child: SizedBox(
                  width: widget.size,
                  height: widget.size,
                  child: CircularProgressIndicator(
                    value: 1.0,
                    strokeWidth: widget.strokeWidth,
                    backgroundColor:
                        widget.backgroundColor.withValues(alpha: 0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(
                        widget.backgroundColor.withValues(alpha: 0.1)),
                  ),
                ),
              ),
              Center(
                child: SizedBox(
                  width: widget.size,
                  height: widget.size,
                  child: CircularProgressIndicator(
                    value: _animation.value,
                    strokeWidth: widget.strokeWidth,
                    backgroundColor: Colors.transparent,
                    valueColor: AlwaysStoppedAnimation<Color>(widget.color),
                  ),
                ),
              ),
              if (widget.valueText != null)
                Center(
                  child: Text(
                    widget.valueText!,
                    style: widget.valueStyle ??
                        AppTypography(
                          fontSize: widget.size / 4,
                          fontWeight: FontWeight.bold,
                          color: widget.color,
                        ),
                  ),
                ),
            ],
          ),
        ),
        if (widget.title != null) ...[
          const SizedBox(height: AppDimensions.spacing8),
          Text(
            widget.title!,
            style: widget.titleStyle ??
                AppTypography(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: widget.color,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// مؤشر تقدم خطي متحرك
class AnimatedLinearProgressIndicator extends StatefulWidget {
  /// قيمة التقدم (0.0 إلى 1.0)
  final double value;

  /// نص العنوان
  final String? title;

  /// نص القيمة
  final String? valueText;

  /// لون التقدم
  final Color color;

  /// لون الخلفية
  final Color backgroundColor;

  /// ارتفاع المؤشر
  final double height;

  /// مدة الحركة
  final Duration duration;

  /// نمط نص العنوان
  final AppTypography? titleStyle;

  /// نمط نص القيمة
  final AppTypography? valueStyle;

  /// نصف قطر الحواف
  final double borderRadius;

  const AnimatedLinearProgressIndicator({
    Key? key,
    required this.value,
    this.title,
    this.valueText,
    required this.color,
    this.backgroundColor = AppColors.lightTextSecondary,
    this.height = 10.0,
    this.duration = const Duration(milliseconds: 1500),
    this.titleStyle,
    this.valueStyle,
    this.borderRadius = 4.0,
  }) : super(key: key);

  @override
  State<AnimatedLinearProgressIndicator> createState() =>
      _AnimatedLinearProgressIndicatorState();
}

class _AnimatedLinearProgressIndicatorState
    extends State<AnimatedLinearProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    _animation = Tween<double>(begin: 0, end: widget.value).animate(
        CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic))
      ..addListener(() {
        setState(() {});
      });
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedLinearProgressIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _animation = Tween<double>(begin: oldWidget.value, end: widget.value)
          .animate(
              CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic));
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.title != null || widget.valueText != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (widget.title != null)
                  Text(
                    widget.title!,
                    style: widget.titleStyle ??
                        AppTypography(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: widget.color,
                        ),
                  ),
                if (widget.valueText != null)
                  Text(
                    widget.valueText!,
                    style: widget.valueStyle ??
                        AppTypography(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: widget.color,
                        ),
                  ),
              ],
            ),
          ),
        ClipRRect(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          child: LinearProgressIndicator(
            value: _animation.value,
            backgroundColor: widget.backgroundColor.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(widget.color),
            minHeight: widget.height,
          ),
        ),
      ],
    );
  }
}
