import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/lazy_provider_wrapper.dart' as wrapper;
import '../../../core/models/customer.dart';
import '../presenters/customer_presenter.dart';
import '../../../core/widgets/index.dart'; // إضافة النظام الموحد AK

import 'customer_form_screen.dart';
import '../../../core/theme/index.dart';

/// شاشة إدارة العملاء الرئيسية (محسنة بالتحميل الكسول)
///
/// 👥 الوظائف:
/// - عرض قائمة العملاء مع إمكانية البحث والفلترة
/// - إضافة وتعديل وحذف العملاء
/// - عرض تفاصيل العميل والرصيد
/// - إدارة معلومات الاتصال والعناوين
/// - واجهة سهلة ومتجاوبة لإدارة العملاء
/// 🚀 يستخدم LazyProviderWrapper لتحسين الأداء
class CustomersScreen extends StatelessWidget {
  const CustomersScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return wrapper.LazyProviderWrapper<CustomerPresenter>(
      presenterFactory: () => CustomerPresenter(),
      presenterName: 'CustomerPresenter',
      child: const _CustomersScreenContent(),
    );
  }
}

class _CustomersScreenContent extends StatefulWidget {
  const _CustomersScreenContent({Key? key}) : super(key: key);

  @override
  State<_CustomersScreenContent> createState() =>
      _CustomersScreenContentState();
}

class _CustomersScreenContentState extends State<_CustomersScreenContent> {
  late CustomerPresenter _customerPresenter;
  final _searchController = TextEditingController();
  bool _showSearchField = false; // متغير لإظهار/إخفاء حقل البحث

  @override
  void initState() {
    super.initState();
    _customerPresenter = AppProviders.getLazyPresenter<CustomerPresenter>(
        () => CustomerPresenter());
    _customerPresenter.loadCustomers(type: 'customer');

    // إضافة مستمع للبحث (نفس آلية UsersScreen)
    _searchController.addListener(() {
      _customerPresenter.searchQuery =
          _searchController.text.isNotEmpty ? _searchController.text : null;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'إدارة العملاء',
        actions: [
          // أيقونة البحث (نفس آلية UsersScreen)
          IconButton(
            icon: Icon(_showSearchField ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                }
              });
            },
          ),
          // أيقونة الإضافة
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'إضافة عميل جديد',
            onPressed: () => _navigateToCustomerForm(),
          ),
        ],
      ),
      body: Column(
        children: [
          // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث - نفس آلية UsersScreen)
          if (_showSearchField) _buildSearchBar(),
          Expanded(
            child: _buildCustomersList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: AppDimensions.getResponsivePadding(horizontal: 4, vertical: 2),
      child: AkSearchInput(
        controller: _searchController,
        hint: 'بحث في العملاء (الاسم، الهاتف، البريد الإلكتروني)...',
        onChanged: (value) {
          // منطق البحث سيتم تطبيقه تلقائياً عبر المستمع في initState
        },
        onClear: () {
          _searchController.clear();
        },
      ),
    );
  }

  Widget _buildCustomersList() {
    return ListenableBuilder(
      listenable: _customerPresenter,
      builder: (context, child) {
        if (_customerPresenter.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_customerPresenter.error != null) {
          return Center(
            child: Text(
              'Error: ${_customerPresenter.error}',
              style: const AppTypography(color: AppColors.error),
            ),
          );
        }

        final customers = _customerPresenter.customers;
        if (customers.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('No customers found'),
                const SizedBox(height: AppDimensions.spacing16),
                ElevatedButton.icon(
                  icon: const Icon(Icons.add),
                  label: const Text('Add Customer'),
                  onPressed: () => _navigateToCustomerForm(),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: customers.length,
          itemBuilder: (context, index) {
            final customer = customers[index];
            return _buildCustomerCard(customer);
          },
        );
      },
    );
  }

  Widget _buildCustomerCard(Customer customer) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => _navigateToCustomerForm(customer),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      customer.name,
                      style: const AppTypography(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (!customer.isActive)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color:
                            AppColors.lightTextSecondary.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Inactive',
                        style: AppTypography(
                          fontSize: 12,
                          color: AppColors.lightTextSecondary,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing8),
              if (customer.phone != null) ...[
                Row(
                  children: [
                    const Icon(Icons.phone,
                        size: 16, color: AppColors.lightTextSecondary),
                    const SizedBox(width: 8),
                    Text(customer.phone!),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing4),
              ],
              if (customer.email != null) ...[
                Row(
                  children: [
                    const Icon(Icons.email,
                        size: 16, color: AppColors.lightTextSecondary),
                    const SizedBox(width: 8),
                    Text(customer.email!),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing4),
              ],
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  const Text(
                    'Balance:',
                    style: AppTypography(color: AppColors.lightTextSecondary),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    customer.balance.toStringAsFixed(2),
                    style: AppTypography(
                      fontWeight: FontWeight.bold,
                      color: customer.balance > 0
                          ? AppColors.error
                          : AppColors.lightTextPrimary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _navigateToCustomerForm([Customer? customer]) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerFormScreen(customer: customer),
      ),
    );

    if (result == true) {
      _customerPresenter.loadCustomers();
    }
  }
}
