import 'package:uuid/uuid.dart';
import 'base_model.dart';
import 'dart:convert';

/// نموذج المنتج
/// تم توحيده من جميع نماذج المنتج في المشروع
class Product extends BaseModel {
  // معلومات أساسية
  final String name;
  final String? description;
  final String? code;
  final String? sku;
  final String? barcode;

  // التصنيف والوحدة
  final String? categoryId;
  final String? categoryName; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final String? unitId;
  final String? unitName; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final String? unitSymbol; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات

  // الأسعار
  final double purchasePrice; // سعر الشراء
  final double salePrice; // سعر البيع
  final double? wholesalePrice; // سعر البيع بالجملة

  // المخزون
  final double quantity; // الكمية المتاحة
  final double minStock; // الحد الأدنى للمخزون
  final double? maxStock; // الحد الأقصى للمخزون
  final bool trackInventory; // تتبع المخزون
  final bool allowDecimal; // السماح بالكميات العشرية

  // خصائص المنتج
  final bool isActive; // نشط
  final bool isService; // خدمة
  final bool isSellable; // قابل للبيع
  final bool isPurchasable; // قابل للشراء

  // الصلاحية
  final bool hasExpiry; // له تاريخ انتهاء
  final DateTime? expiryDate; // تاريخ انتهاء الصلاحية

  // الصورة والضريبة
  final String? imageUrl; // رابط الصورة
  final double taxRate; // معدل الضريبة

  // بيانات إضافية
  final Map<String, dynamic>? metadata; // بيانات إضافية

  Product({
    String? id,
    required this.name,
    this.description,
    this.code,
    this.sku,
    this.barcode,
    this.categoryId,
    this.categoryName,
    this.unitId,
    this.unitName,
    this.unitSymbol,
    this.purchasePrice = 0.0,
    this.salePrice = 0.0,
    this.wholesalePrice,
    this.quantity = 0.0,
    this.minStock = 0.0,
    this.maxStock,
    this.trackInventory = true,
    this.allowDecimal = false,
    this.isActive = true,
    this.isService = false,
    this.isSellable = true,
    this.isPurchasable = true,
    this.hasExpiry = false,
    this.expiryDate,
    this.imageUrl,
    this.taxRate = 0.0,
    this.metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا المنتج مع استبدال الحقول المحددة بقيم جديدة
  Product copyWith({
    String? id,
    String? name,
    String? description,
    String? code,
    String? sku,
    String? barcode,
    String? categoryId,
    String? categoryName,
    String? unitId,
    String? unitName,
    String? unitSymbol,
    double? purchasePrice,
    double? salePrice,
    double? wholesalePrice,
    double? quantity,
    double? minStock,
    double? maxStock,
    bool? trackInventory,
    bool? allowDecimal,
    bool? isActive,
    bool? isService,
    bool? isSellable,
    bool? isPurchasable,
    bool? hasExpiry,
    DateTime? expiryDate,
    String? imageUrl,
    double? taxRate,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      code: code ?? this.code,
      sku: sku ?? this.sku,
      barcode: barcode ?? this.barcode,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      unitId: unitId ?? this.unitId,
      unitName: unitName ?? this.unitName,
      unitSymbol: unitSymbol ?? this.unitSymbol,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      salePrice: salePrice ?? this.salePrice,
      wholesalePrice: wholesalePrice ?? this.wholesalePrice,
      quantity: quantity ?? this.quantity,
      minStock: minStock ?? this.minStock,
      maxStock: maxStock ?? this.maxStock,
      trackInventory: trackInventory ?? this.trackInventory,
      allowDecimal: allowDecimal ?? this.allowDecimal,
      isActive: isActive ?? this.isActive,
      isService: isService ?? this.isService,
      isSellable: isSellable ?? this.isSellable,
      isPurchasable: isPurchasable ?? this.isPurchasable,
      hasExpiry: hasExpiry ?? this.hasExpiry,
      expiryDate: expiryDate ?? this.expiryDate,
      imageUrl: imageUrl ?? this.imageUrl,
      taxRate: taxRate ?? this.taxRate,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل المنتج إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'code': code,
      'sku': sku,
      'barcode': barcode,
      'category_id': categoryId,
      'category_name': categoryName,
      'unit_id': unitId,
      'unit_name': unitName,
      'unit_symbol': unitSymbol,
      'purchase_price': purchasePrice,
      'sale_price': salePrice,
      'wholesale_price': wholesalePrice,
      'quantity': quantity,
      'min_stock': minStock,
      'max_stock': maxStock,
      'track_inventory': trackInventory ? 1 : 0,
      'allow_decimal': allowDecimal ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'is_service': isService ? 1 : 0,
      'is_sellable': isSellable ? 1 : 0,
      'is_purchasable': isPurchasable ? 1 : 0,
      'has_expiry': hasExpiry ? 1 : 0,
      'expiry_date': expiryDate?.toIso8601String(),
      'image_url': imageUrl,
      'tax_rate': taxRate,
      'metadata': metadata != null ? _encodeMetadata(metadata!) : null,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء منتج من Map
  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'],
      name: map['name'] ?? '',
      description: map['description'],
      code: map['code'],
      sku: map['sku'],
      barcode: map['barcode'],
      categoryId: map['category_id'],
      categoryName: map['category_name'],
      unitId: map['unit_id'],
      unitName: map['unit_name'],
      unitSymbol: map['unit_symbol'],
      purchasePrice: map['purchase_price'] is int
          ? (map['purchase_price'] as int).toDouble()
          : (map['purchase_price'] as double? ?? 0.0),
      salePrice: map['sale_price'] is int
          ? (map['sale_price'] as int).toDouble()
          : (map['sale_price'] as double? ?? 0.0),
      wholesalePrice: map['wholesale_price'] is int
          ? (map['wholesale_price'] as int).toDouble()
          : map['wholesale_price'] as double?,
      quantity: map['quantity'] is int
          ? (map['quantity'] as int).toDouble()
          : (map['quantity'] as double? ?? 0.0),
      minStock: map['min_stock'] is int
          ? (map['min_stock'] as int).toDouble()
          : (map['min_stock'] as double? ?? 0.0),
      maxStock: map['max_stock'] != null
          ? (map['max_stock'] is int
              ? (map['max_stock'] as int).toDouble()
              : map['max_stock'] as double)
          : null,
      trackInventory: map['track_inventory'] == 1,
      allowDecimal: map['allow_decimal'] == 1,
      isActive: map['is_active'] == 1,
      isService: map['is_service'] == 1,
      isSellable: map['is_sellable'] == 1,
      isPurchasable: map['is_purchasable'] == 1,
      hasExpiry: map['has_expiry'] == 1,
      expiryDate: map['expiry_date'] != null
          ? DateTime.parse(map['expiry_date'])
          : null,
      imageUrl: map['image_url'],
      taxRate: map['tax_rate'] is int
          ? (map['tax_rate'] as int).toDouble()
          : (map['tax_rate'] as double? ?? 0.0),
      metadata:
          map['metadata'] != null ? _decodeMetadata(map['metadata']) : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل البيانات الوصفية إلى نص JSON
  static String _encodeMetadata(Map<String, dynamic> metadata) {
    try {
      return jsonEncode(metadata);
    } catch (e) {
      return '{}';
    }
  }

  /// تحويل نص JSON إلى بيانات وصفية
  static Map<String, dynamic> _decodeMetadata(String metadataString) {
    try {
      return jsonDecode(metadataString) as Map<String, dynamic>;
    } catch (e) {
      return {};
    }
  }

  /// هل المنتج منخفض المخزون
  bool get isLowStock {
    if (isService) return false;
    return quantity <= minStock;
  }

  /// تحويل المنتج إلى JSON
  String toJson() {
    return jsonEncode(toMap());
  }

  /// إنشاء منتج من JSON
  factory Product.fromJson(String source) {
    return Product.fromMap(jsonDecode(source));
  }

  @override
  String toString() {
    return 'Product(id: $id, name: $name, code: $code, barcode: $barcode, salePrice: $salePrice, quantity: $quantity)';
  }
}

/// استيراد مكتبة json
