import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/models/customer.dart';
import '../../../core/theme/index.dart';

/// مربع حوار فلترة المبيعات
class SaleFilterDialog extends StatefulWidget {
  final String? selectedCustomerId;
  final String? selectedPaymentMethod;
  final String? selectedStatus; // سيتم تحويله إلى SaleStatus
  final DateTime? startDate;
  final DateTime? endDate;
  final List<Customer> customers;
  final Function(String?, String?, String?, DateTime?, DateTime?) onApplyFilter;
  final VoidCallback onClearFilter;

  const SaleFilterDialog({
    Key? key,
    this.selectedCustomerId,
    this.selectedPaymentMethod,
    this.selectedStatus,
    this.startDate,
    this.endDate,
    required this.customers,
    required this.onApplyFilter,
    required this.onClearFilter,
  }) : super(key: key);

  @override
  State<SaleFilterDialog> createState() => _SaleFilterDialogState();
}

class _SaleFilterDialogState extends State<SaleFilterDialog> {
  // القيم المحددة
  String? _selectedCustomerId;
  String? _selectedPaymentMethod;
  String? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();

    // تعيين القيم الأولية
    _selectedCustomerId = widget.selectedCustomerId;
    _selectedPaymentMethod = widget.selectedPaymentMethod;
    _selectedStatus = widget.selectedStatus;
    _startDate = widget.startDate;
    _endDate = widget.endDate;
  }

  /// اختيار تاريخ البداية
  Future<void> _selectStartDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null) {
      setState(() {
        _startDate = pickedDate;
      });
    }
  }

  /// اختيار تاريخ النهاية
  Future<void> _selectEndDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null) {
      setState(() {
        _endDate = pickedDate;
      });
    }
  }

  /// تطبيق الفلترة
  void _applyFilter() {
    widget.onApplyFilter(
      _selectedCustomerId,
      _selectedPaymentMethod,
      _selectedStatus,
      _startDate,
      _endDate,
    );
    Navigator.pop(context);
  }

  /// مسح الفلترة
  void _clearFilter() {
    setState(() {
      _selectedCustomerId = null;
      _selectedPaymentMethod = null;
      _selectedStatus = null;
      _startDate = null;
      _endDate = null;
    });

    widget.onClearFilter();
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('فلترة المبيعات'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // فلترة حسب العميل
            const Text(
              'العميل:',
              style: AppTypography(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String?>(
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              value: _selectedCustomerId,
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('جميع العملاء'),
                ),
                ...widget.customers.map((customer) {
                  return DropdownMenuItem<String?>(
                    value: customer.id,
                    child: Text(customer.name),
                  );
                }).toList(),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCustomerId = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // فلترة حسب طريقة الدفع
            const Text(
              'طريقة الدفع:',
              style: AppTypography(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String?>(
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              value: _selectedPaymentMethod,
              items: const [
                DropdownMenuItem<String?>(
                  value: null,
                  child: Text('جميع طرق الدفع'),
                ),
                DropdownMenuItem<String?>(
                  value: 'cash',
                  child: Text('نقداً'),
                ),
                DropdownMenuItem<String?>(
                  value: 'credit_card',
                  child: Text('بطاقة ائتمان'),
                ),
                DropdownMenuItem<String?>(
                  value: 'debit_card',
                  child: Text('بطاقة خصم'),
                ),
                DropdownMenuItem<String?>(
                  value: 'bank_transfer',
                  child: Text('تحويل بنكي'),
                ),
                DropdownMenuItem<String?>(
                  value: 'check',
                  child: Text('شيك'),
                ),
                DropdownMenuItem<String?>(
                  value: 'credit',
                  child: Text('آجل'),
                ),
                DropdownMenuItem<String?>(
                  value: 'other',
                  child: Text('أخرى'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedPaymentMethod = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // فلترة حسب الحالة
            const Text(
              'الحالة:',
              style: AppTypography(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String?>(
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              value: _selectedStatus,
              items: const [
                DropdownMenuItem<String?>(
                  value: null,
                  child: Text('جميع الحالات'),
                ),
                DropdownMenuItem<String?>(
                  value: 'completed',
                  child: Text('مكتمل'),
                ),
                DropdownMenuItem<String?>(
                  value: 'cancelled',
                  child: Text('ملغي'),
                ),
                DropdownMenuItem<String?>(
                  value: 'draft',
                  child: Text('مسودة'),
                ),
                DropdownMenuItem<String?>(
                  value: 'returned',
                  child: Text('مرتجع'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // فلترة حسب التاريخ
            const Text(
              'الفترة الزمنية:',
              style: AppTypography(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: _selectStartDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        labelText: 'من',
                      ),
                      child: Text(
                        _startDate != null
                            ? DateFormat('yyyy-MM-dd').format(_startDate!)
                            : '',
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: _selectEndDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        labelText: 'إلى',
                      ),
                      child: Text(
                        _endDate != null
                            ? DateFormat('yyyy-MM-dd').format(_endDate!)
                            : '',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _clearFilter,
          child: const Text('مسح الفلاتر'),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _applyFilter,
          child: const Text('تطبيق'),
        ),
      ],
    );
  }
}
