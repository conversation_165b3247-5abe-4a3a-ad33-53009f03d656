# 🎯 نظام حقول الإدخال المتخصصة

نظام شامل لحقول الإدخال المتخصصة مع التحميل الكسول والدوال المساعدة، مخصص لليمن مع دعم كامل لنظام الثيمات.

## 📋 الحقول المتاحة

### 🔢 الحقول الرقمية

#### 1. CurrencyInputField - حقل المبالغ المالية
```dart
CurrencyInputField(
  label: 'سعر المنتج',
  controller: priceController,
  currencySymbol: 'ر.ي', // ريال يمني
  minAmount: 0.01,
  maxAmount: 999999.99,
  isRequired: true,
)
```

**المميزات:**
- تنسيق تلقائي للمبالغ مع فاصل الآلاف
- رمز العملة اليمنية (ر.ي) افتراضياً
- تحقق من صحة المبلغ مع رسائل خطأ واضحة
- تحميل كسول لمنسق الأرقام

#### 2. PercentageInputField - حقل النسب المئوية
```dart
PercentageInputField(
  label: 'نسبة الخصم',
  controller: discountController,
  minPercentage: 0.0,
  maxPercentage: 50.0,
  isRequired: true,
)
```

**المميزات:**
- تحقق تلقائي من النطاق (0-100% افتراضياً)
- تنسيق تلقائي مع رمز النسبة المئوية
- دعم الأرقام العشرية

#### 3. NumericInputField - حقل الأرقام العامة
```dart
NumericInputField(
  label: 'العمر',
  controller: ageController,
  minValue: 18,
  maxValue: 100,
  allowDecimal: false,
  unit: 'سنة',
)
```

**المميزات:**
- تحقق من النطاق المحدد
- دعم الأرقام الصحيحة والعشرية
- وحدة قياس اختيارية

### 📞 حقول الاتصال

#### 4. PhoneInputField - حقل أرقام الهاتف
```dart
PhoneInputField(
  label: 'رقم الهاتف',
  controller: phoneController,
  defaultCountryCode: '+967', // اليمن
  isRequired: true,
)
```

**المميزات:**
- تنسيق تلقائي لأرقام الهاتف اليمنية
- رمز البلد اليمني (+967) افتراضياً
- تحقق من صحة رقم الهاتف اليمني (9 أرقام تبدأ بـ 7)
- تحميل كسول لعنصر رمز البلد

### 🔐 حقول الأمان

#### 5. EmailInputField - حقل البريد الإلكتروني
```dart
EmailInputField(
  label: 'البريد الإلكتروني',
  controller: emailController,
  isRequired: true,
  showSuggestions: true,
)
```

**المميزات:**
- تحقق متقدم من صحة البريد الإلكتروني
- اقتراحات تلقائية للنطاقات الشائعة
- تحقق من النطاقات المحظورة
- تنسيق تلقائي للنص

#### 6. PasswordInputField - حقل كلمة المرور
```dart
PasswordInputField(
  label: 'كلمة المرور',
  controller: passwordController,
  isRequired: true,
  showStrengthIndicator: true,
  minLength: 8,
)
```

**المميزات:**
- إخفاء/إظهار كلمة المرور
- مؤشر قوة كلمة المرور مع التحميل الكسول
- تحقق متقدم من قوة كلمة المرور
- اقتراحات لتحسين كلمة المرور

### 📝 حقول النصوص المتخصصة

#### 7. LongTextInputField - حقل النصوص الطويلة
```dart
LongTextInputField(
  label: 'ملاحظات',
  controller: notesController,
  maxLength: 500,
  minLines: 3,
  maxLines: 8,
)
```

**المميزات:**
- دعم النصوص متعددة الأسطر
- عداد الأحرف مع التحميل الكسول
- تغيير حجم الحقل تلقائياً
- تحقق من الطول الأدنى والأقصى

#### 8. SearchInputField - حقل البحث مع الاقتراحات
```dart
SearchInputField<Product>(
  label: 'البحث عن منتج',
  onSearch: (query) => productService.search(query),
  onSelected: (product) => print('تم اختيار: ${product.name}'),
  displayStringForOption: (product) => product.name,
  searchDelay: Duration(milliseconds: 300),
)
```

**المميزات:**
- اقتراحات تلقائية مع التحميل الكسول
- دعم البحث في المنتجات والعملاء
- تأخير البحث لتحسين الأداء
- أيقونة بحث وزر مسح النص

### 🔤 حقول الرموز والملفات

#### 9. CodeInputField - حقل الرموز والباركود
```dart
CodeInputField(
  label: 'باركود المنتج',
  controller: barcodeController,
  codeType: CodeType.barcode,
  enableScanning: true,
  onScanned: (code) => print('تم مسح: $code'),
)
```

**المميزات:**
- دعم مسح الباركود والـ QR Code مع التحميل الكسول
- إدخال يدوي للرموز
- تحقق من صحة تنسيق الرموز
- دعم أنواع مختلفة من الرموز

#### 10. FileInputField - حقل رفع الملفات
```dart
FileInputField(
  label: 'رفع المستندات',
  allowedExtensions: ['pdf', 'doc', 'docx', 'jpg'],
  maxFileSize: 5 * 1024 * 1024, // 5 MB
  allowMultiple: true,
  onFilesSelected: (files) => print('تم اختيار ${files.length} ملف'),
)
```

**المميزات:**
- دعم رفع المستندات والصور مع التحميل الكسول
- معاينة الملفات المختارة
- تحديد أنواع الملفات المسموحة
- عرض حجم الملف وحالة الرفع

### 📋 حقول الاختيار المتقدمة

#### 11. DropdownInputField - قائمة منسدلة محسنة
```dart
DropdownInputField<String>(
  label: 'اختر المدينة',
  items: ['صنعاء', 'عدن', 'تعز', 'الحديدة'],
  onChanged: (value) => print('تم اختيار: $value'),
  enableSearch: true,
  isRequired: true,
)
```

**المميزات:**
- قائمة منسدلة مع إمكانية البحث داخلها
- دعم البحث النصي في الخيارات مع التحميل الكسول
- تصميم متناسق مع باقي الحقول
- دعم الخيارات المخصصة

#### 12. DateInputField - حقل التاريخ
```dart
DateInputField(
  label: 'تاريخ الميلاد',
  controller: birthDateController,
  firstDate: DateTime(1950),
  lastDate: DateTime.now(),
  dateFormat: 'yyyy/MM/dd',
  showHijriDate: true,
)
```

**المميزات:**
- منتقي تاريخ محلي باللغة العربية
- تنسيق التاريخ حسب النمط المحلي
- تحميل كسول لمنتقي التاريخ
- تحديد نطاق التواريخ المسموحة
- دعم التواريخ الهجرية والميلادية

## 🚀 المميزات الرئيسية

### ✅ التحميل الكسول
- **منسق الأرقام**: يتم تحميله عند الحاجة فقط
- **منتقي التاريخ**: يتم تحميله عند النقر
- **عنصر رمز البلد**: يتم بناؤه عند الحاجة

### ✅ الدوال المساعدة
- **Validators**: تحقق متقدم من صحة البيانات
- **Helpers**: دوال مساعدة للتنسيق والعرض
- **AppColors**: نظام ألوان موحد
- **AppDimensions**: نظام أبعاد متجاوب

### ✅ التخصيص اليمني
- **رمز العملة**: ر.ي (ريال يمني)
- **رمز البلد**: +967 (اليمن)
- **تنسيق الهاتف**: XXX XXX XXX (9 أرقام)
- **تنسيق التاريخ**: yyyy/MM/dd

## 📦 الاستيراد والاستخدام

```dart
import '../core/widgets/index.dart';

// أو استيراد مباشر
import '../core/widgets/input_fields.dart';
```

## 🎨 نظام الثيمات

جميع الحقول تستخدم نظام الثيمات الموحد:

```dart
// الألوان
AppColors.primary
AppColors.lightBorder
AppColors.error

// الأبعاد
AppDimensions.mediumRadius
AppDimensions.defaultMargin
AppDimensions.mediumIconSize

// الخطوط
AppTypography.fontSizeMedium
AppTypography.weightRegular
```

## 🔧 التحقق من صحة البيانات

جميع الحقول تستخدم نظام التحقق الموحد:

```dart
// التحقق المطلوب
Validators.required('اسم الحقل')

// التحقق من الأرقام الموجبة
Validators.positiveNumber('اسم الحقل')

// التحقق من النطاق
Validators.numberRange(min, max, 'اسم الحقل')
```

## 📱 الاستجابة والتكيف

- **التخطيط المتجاوب**: استخدام Layout.w() و Layout.h()
- **الأيقونات المتكيفة**: أحجام مختلفة حسب الشاشة
- **النصوص المتجاوبة**: خطوط متكيفة مع حجم الشاشة

## 🧪 الاختبار

```dart
// مثال لاختبار حقل المبلغ
testWidgets('Currency input field validation', (tester) async {
  final controller = TextEditingController();
  
  await tester.pumpWidget(
    MaterialApp(
      home: Scaffold(
        body: CurrencyInputField(
          label: 'السعر',
          controller: controller,
          isRequired: true,
        ),
      ),
    ),
  );
  
  // اختبار التحقق
  await tester.enterText(find.byType(TextFormField), '');
  await tester.pump();
  
  expect(find.text('السعر مطلوب'), findsOneWidget);
});
```

## 🔮 التطوير المستقبلي

- [ ] حقل البريد الإلكتروني
- [ ] حقل كلمة المرور مع مؤشر القوة
- [ ] حقل البحث مع الاقتراحات
- [ ] حقل الباركود مع المسح
- [ ] حقل التوقيع الرقمي
