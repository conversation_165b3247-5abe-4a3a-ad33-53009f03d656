import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../core/theme/index.dart';

/// مكون لعرض إحصائيات الحسابات المحاسبية
class AccountStatsWidget extends StatelessWidget {
  /// قائمة الحسابات للتحليل
  final List<Map<String, dynamic>> accounts;

  /// إحصائيات معدة مسبقًا حسب نوع الحساب
  final Map<String, double> stats;

  const AccountStatsWidget({
    Key? key,
    required this.accounts,
    required this.stats,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return accounts.isEmpty
        ? const Center(child: Text('لا توجد بيانات كافية للعرض'))
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSummaryCards(context),
                const SizedBox(height: AppDimensions.spacing24),
                _buildAccountTypePieChart(context),
                const SizedBox(height: AppDimensions.spacing24),
                _buildAccountsBalanceBarChart(context),
                const SizedBox(height: AppDimensions.spacing24),
                _buildAccountsTable(context),
              ],
            ),
          );
  }

  Widget _buildSummaryCards(BuildContext context) {
    // حساب الإجماليات
    final totalAccounts = accounts.length;
    final totalAssets = stats['asset'] ?? 0.0;
    final totalLiabilities = stats['liability'] ?? 0.0;
    final totalEquity = stats['equity'] ?? 0.0;
    final totalRevenue = stats['revenue'] ?? 0.0;
    final totalExpenses = stats['expense'] ?? 0.0;
    // صافي القيمة = الأصول - الالتزامات (سنستخدمها في تحديثات مستقبلية)
    // final netWorth = totalAssets - totalLiabilities;

    return GridView.count(
      crossAxisCount: 3,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 12.0,
      crossAxisSpacing: 12.0,
      childAspectRatio: 1.5,
      children: [
        _buildSummaryCard(
          context,
          title: 'إجمالي الحسابات',
          value: '$totalAccounts',
          icon: Icons.account_balance_wallet,
          color: AppColors.lightTextSecondary,
        ),
        _buildSummaryCard(
          context,
          title: 'الأصول',
          value: _formatCurrency(totalAssets),
          icon: Icons.account_balance,
          color: AppColors.lightTextSecondary,
        ),
        _buildSummaryCard(
          context,
          title: 'الالتزامات',
          value: _formatCurrency(totalLiabilities),
          icon: Icons.money_off,
          color: AppColors.lightTextSecondary,
        ),
        _buildSummaryCard(
          context,
          title: 'حقوق الملكية',
          value: _formatCurrency(totalEquity),
          icon: Icons.pie_chart,
          color: AppColors.lightTextSecondary,
        ),
        _buildSummaryCard(
          context,
          title: 'الإيرادات',
          value: _formatCurrency(totalRevenue),
          icon: Icons.trending_up,
          color: AppColors.infoDark,
        ),
        _buildSummaryCard(
          context,
          title: 'المصروفات',
          value: _formatCurrency(totalExpenses),
          icon: Icons.trending_down,
          color: AppColors.lightTextSecondary,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 28, color: color),
            const SizedBox(height: AppDimensions.spacing8),
            Text(
              value,
              style: const AppTypography(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: AppDimensions.spacing4),
            Text(
              title,
              style: const AppTypography(
                fontSize: 12,
                color: AppColors.lightTextSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountTypePieChart(BuildContext context) {
    return Card(
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع الأرصدة حسب نوع الحساب',
              style: AppTypography(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: AppDimensions.spacing16),
            SizedBox(
              height: 200,
              child: Row(
                children: [
                  Expanded(
                    flex: 4,
                    child: SizedBox(
                      height: 200,
                      child: CustomPaint(
                        painter: PieChartPainter(stats),
                        size: const Size(200, 200),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: _buildPieChartLegend(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPieChartLegend() {
    final List<MapEntry<String, double>> items = stats.entries.toList();
    items.sort((a, b) => b.value.compareTo(a.value));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: items.map((entry) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getAccountTypeColor(entry.key),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8.0),
              Expanded(
                child: Text(
                  _getAccountTypeDisplayName(entry.key),
                  style: const AppTypography(fontSize: 12),
                ),
              ),
              Text(
                _formatCurrency(entry.value),
                style: const AppTypography(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAccountsBalanceBarChart(BuildContext context) {
    // اختيار أعلى 5 حسابات من حيث الرصيد
    final sortedAccounts = List.from(accounts);
    sortedAccounts.sort((a, b) {
      final aBalance = (a['balance'] as num?)?.abs() ?? 0;
      final bBalance = (b['balance'] as num?)?.abs() ?? 0;
      return bBalance.compareTo(aBalance);
    });

    final top5Accounts = sortedAccounts.take(5).toList();

    return Card(
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أعلى 5 حسابات من حيث الرصيد',
              style: AppTypography(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: AppDimensions.spacing16),
            ...top5Accounts.map((account) => _buildAccountBalanceBar(account)),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountBalanceBar(Map<String, dynamic> account) {
    final name = account['name'] as String? ?? '';
    final balance = account['balance'] as num? ?? 0;
    final accountType = account['account_type'] as String? ?? '';
    final color = _getAccountTypeColor(accountType);

    // تحديد النسبة المئوية (بحد أقصى 100%)
    final maxBalance = accounts.fold<double>(0, (max, acc) {
      final accBalance = (acc['balance'] as num?)?.abs() ?? 0;
      return math.max(max, accBalance.toDouble());
    });

    final percentage = maxBalance > 0 ? (balance.abs() / maxBalance) : 0.0;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  name,
                  style: const AppTypography(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                _formatCurrency(balance.toDouble()),
                style: AppTypography(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  color: balance < 0 ? AppColors.error : null,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing4),
          Stack(
            children: [
              Container(
                height: 8,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.lightTextSecondary,
                  borderRadius: BorderRadius.circular(4.0),
                ),
              ),
              FractionallySizedBox(
                widthFactor: percentage.clamp(0.0, 1.0),
                child: Container(
                  height: 8,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccountsTable(BuildContext context) {
    // تجميع الحسابات حسب النوع
    Map<String, List<Map<String, dynamic>>> accountsByType = {};

    for (var account in accounts) {
      final type = account['account_type'] as String? ?? 'other';
      accountsByType[type] ??= [];
      accountsByType[type]!.add(account);
    }

    // ترتيب الأنواع
    final sortedTypes = accountsByType.keys.toList()
      ..sort((a, b) =>
          _getAccountTypeSortOrder(a).compareTo(_getAccountTypeSortOrder(b)));

    return Card(
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص الحسابات',
              style: AppTypography(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: AppDimensions.spacing16),
            DataTable(
              columnSpacing: 16.0,
              horizontalMargin: 0,
              columns: const [
                DataColumn(label: Text('نوع الحساب')),
                DataColumn(label: Text('عدد الحسابات')),
                DataColumn(label: Text('إجمالي الرصيد'), numeric: true),
              ],
              rows: sortedTypes.map((type) {
                final accountsOfType = accountsByType[type]!;
                final count = accountsOfType.length;
                final totalBalance = accountsOfType.fold<double>(
                  0,
                  (sum, account) =>
                      sum + (account['balance'] as num? ?? 0).toDouble(),
                );

                return DataRow(
                  cells: [
                    DataCell(
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 10,
                            height: 10,
                            decoration: BoxDecoration(
                              color: _getAccountTypeColor(type),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8.0),
                          Text(_getAccountTypeDisplayName(type)),
                        ],
                      ),
                    ),
                    DataCell(Text('$count')),
                    DataCell(
                      Text(
                        _formatCurrency(totalBalance),
                        style: AppTypography(
                          fontWeight: FontWeight.bold,
                          color: totalBalance < 0 ? AppColors.error : null,
                        ),
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  int _getAccountTypeSortOrder(String type) {
    switch (type) {
      case 'asset':
        return 0;
      case 'liability':
        return 1;
      case 'equity':
        return 2;
      case 'revenue':
        return 3;
      case 'expense':
        return 4;
      default:
        return 99;
    }
  }

  String _getAccountTypeDisplayName(String type) {
    switch (type) {
      case 'asset':
        return 'أصول';
      case 'liability':
        return 'التزامات';
      case 'equity':
        return 'حقوق ملكية';
      case 'revenue':
        return 'إيرادات';
      case 'expense':
        return 'مصروفات';
      default:
        return type;
    }
  }

  Color _getAccountTypeColor(String type) {
    switch (type) {
      case 'asset':
        return AppColors.info;
      case 'liability':
        return AppColors.error;
      case 'equity':
        return AppColors.accent;
      case 'revenue':
        return AppColors.success;
      case 'expense':
        return AppColors.warning;
      default:
        return AppColors.lightTextSecondary;
    }
  }

  String _formatCurrency(double value) {
    // يمكن تخصيص تنسيق العملة حسب الاحتياج
    return value.toStringAsFixed(2);
  }
}

/// رسام مخطط الدائرة
class PieChartPainter extends CustomPainter {
  final Map<String, double> data;

  PieChartPainter(this.data);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;

    // حساب المجموع الكلي
    final total =
        data.values.fold<double>(0, (sum, value) => sum + value.abs());

    if (total == 0) {
      // لا توجد بيانات، رسم دائرة رمادية
      final paint = Paint()
        ..color = AppColors.lightSurfaceVariant
        ..style = PaintingStyle.fill;

      canvas.drawCircle(center, radius, paint);
      return;
    }

    // رسم القطاعات
    double startAngle = 0;

    // ترتيب البيانات
    final sortedEntries = data.entries.toList();
    sortedEntries.sort((a, b) => b.value.abs().compareTo(a.value.abs()));

    for (var entry in sortedEntries) {
      // تجاوز القيم الصفرية أو السالبة
      if (entry.value <= 0) continue;

      final sweepAngle = entry.value / total * 2 * math.pi;

      final paint = Paint()
        ..color = _getAccountTypeColor(entry.key)
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }

    // رسم دائرة بيضاء في المنتصف للحصول على شكل دونات
    final centerPaint = Paint()
      ..color = AppColors.onPrimary
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.5, centerPaint);
  }

  Color _getAccountTypeColor(String type) {
    return AppColors.getAccountTypeColor(type);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
