import 'package:flutter/material.dart';

import '../../../core/utils/index.dart';
import '../../../core/widgets/index.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/theme/index.dart';

class SimpleAccountFormScreen extends StatefulWidget {
  final Map<String, dynamic>? account;

  const SimpleAccountFormScreen({Key? key, this.account}) : super(key: key);

  @override
  State<SimpleAccountFormScreen> createState() =>
      _SimpleAccountFormScreenState();
}

class _SimpleAccountFormScreenState extends State<SimpleAccountFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // حقول النموذج
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // متغيرات الحالة
  bool _isMainAccount = true; // رئيسي (يقبل التفرع) أو فرعي (لا يقبل التفرع)
  String? _selectedParentId; // الحساب الأب المحدد
  List<Map<String, dynamic>> _parentAccounts = []; // قائمة الحسابات الرئيسية
  bool _isLoading = false; // حالة التحميل
  bool _isSaving = false; // حالة الحفظ
  bool _isActive = true; // حالة الحساب (نشط/غير نشط)

  @override
  void initState() {
    super.initState();
    _loadParentAccounts();

    // If editing an existing account, populate the form
    if (widget.account != null) {
      _nameController.text = widget.account!['name'] as String;

      // تحديد نوع الحساب (رئيسي أو فرعي) بناءً على قيمة account_type
      final accountType = widget.account!['account_type'] as String?;
      _isMainAccount = accountType == 'main';

      // تحديد الحساب الأب إذا كان موجودًا
      _selectedParentId = widget.account!['parent_id']?.toString();

      // تحديد حالة الحساب (نشط/غير نشط)
      _isActive = widget.account!['is_active'] == 1;

      // Load phone and notes if available
      if (widget.account!['phone'] != null) {
        _phoneController.text = widget.account!['phone'] as String;
      }
      if (widget.account!['notes'] != null) {
        _notesController.text = widget.account!['notes'] as String;
      }
    }
  }

  Future<void> _loadParentAccounts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final db = await _databaseHelper.database;

      // جلب الحسابات الرئيسية فقط (account_type = 'main') ما عدا الحساب الحالي
      final List<Map<String, dynamic>> accounts = await db.rawQuery('''
        WITH RECURSIVE account_hierarchy AS (
          SELECT id, name, type, parent_id, code, 0 AS level, name AS path, account_type
          FROM accounts
          WHERE parent_id IS NULL

          UNION ALL

          SELECT a.id, a.name, a.type, a.parent_id, a.code, ah.level + 1, ah.path || ' > ' || a.name, a.account_type
          FROM accounts a
          JOIN account_hierarchy ah ON a.parent_id = ah.id
        )
        SELECT id, name, type, parent_id, code, level, path, account_type
        FROM account_hierarchy
        WHERE account_type = 'main'
        ${widget.account != null ? 'AND id != ${widget.account!['id']}' : ''}
        ORDER BY path
      ''');

      setState(() {
        _parentAccounts = accounts;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to load parent accounts',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'SimpleAccountFormScreen'},
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل في تحميل الحسابات الرئيسية')),
        );
      }
    }
  }

  Future<void> _saveAccount() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate account logic
    if (!_isMainAccount && _selectedParentId == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يجب اختيار حساب أب للحساب الفرعي')),
        );
      }
      return;
    }

    // التحقق من عدم وجود حساب بنفس الاسم أو نفس الكود
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم وجود حساب بنفس الاسم
      final List<Map<String, dynamic>> existingAccountsByName = await db.query(
        'accounts',
        where: 'name = ? ${widget.account != null ? 'AND id != ?' : ''}',
        whereArgs: widget.account != null
            ? [_nameController.text, widget.account!['id']]
            : [_nameController.text],
      );

      if (existingAccountsByName.isNotEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يوجد حساب بنفس الاسم بالفعل، يرجى اختيار اسم آخر'),
              backgroundColor: AppColors.error,
            ),
          );
        }
        return;
      }

      // إذا كنا نقوم بتحديث حساب موجود، نتحقق من عدم وجود حساب آخر بنفس الكود
      if (widget.account != null) {
        // الحصول على الكود الحالي للحساب
        final String accountCode =
            await _generateAccountNumber(_selectedParentId);

        // التحقق من عدم وجود حساب آخر بنفس الكود
        final List<Map<String, dynamic>> existingAccountsByCode =
            await db.query(
          'accounts',
          where: 'code = ? AND id != ?',
          whereArgs: [accountCode, widget.account!['id']],
        );

        if (existingAccountsByCode.isNotEmpty) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'يوجد حساب بنفس الكود بالفعل، يرجى اختيار حساب أب مختلف'),
                backgroundColor: AppColors.error,
              ),
            );
          }
          return;
        }
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error checking for duplicate account names or codes',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'SimpleAccountFormScreen'},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء التحقق من البيانات: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
      return;
    }

    // التحقق من أن الحساب المحدد هو حساب رئيسي (يقبل التفرع)
    if (_selectedParentId != null) {
      try {
        final db = await _databaseHelper.database;
        final List<Map<String, dynamic>> parentAccount = await db.query(
          'accounts',
          columns: ['account_type'],
          where: 'id = ?',
          whereArgs: [_selectedParentId],
        );

        if (parentAccount.isNotEmpty &&
            parentAccount.first['account_type'] != 'main') {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'لا يمكن إضافة حساب تحت حساب فرعي. يجب اختيار حساب رئيسي فقط.'),
                backgroundColor: AppColors.error,
              ),
            );
          }
          return;
        }
      } catch (e, stackTrace) {
        // في حالة حدوث خطأ، نستمر في العملية
        ErrorTracker.captureError(
          'Error checking parent account type',
          error: e,
          stackTrace: stackTrace,
          context: {'screen': 'SimpleAccountFormScreen'},
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'حدث خطأ أثناء التحقق من نوع الحساب الأب: ${e.toString()}'),
              backgroundColor: AppColors.warning,
            ),
          );
        }
      }
    }

    // Check if widget is still mounted before showing dialog
    if (!mounted) return;

    // Show confirmation dialog
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('تأكيد الحفظ'),
        content: const Text('هل تريد حفظ بيانات الحساب؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(dialogContext).pop(true),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    // Check if widget is still mounted after dialog
    if (!mounted || confirm != true) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now().toIso8601String();

      // تم التحقق من هيكل جدول الحسابات

      // تحديد مستوى الحساب (level) بناءً على الحساب الأب
      int accountLevel = 0;
      if (_selectedParentId != null) {
        // إذا كان هناك حساب أب، نحدد المستوى بناءً على نوع الحساب
        // الحسابات الرئيسية تكون في المستوى 0، والحسابات الفرعية في المستوى 1
        accountLevel = _isMainAccount ? 0 : 1;
      }

      // تسجيل معلومات تصحيح الأخطاء
      AppLogger.debug('Creating account with data:');
      AppLogger.debug('- Name: ${_nameController.text}');
      AppLogger.debug('- Parent ID: $_selectedParentId');
      AppLogger.debug('- Account Type: ${_isMainAccount ? 'main' : 'sub'}');
      AppLogger.debug('- Level: $accountLevel');

      // تحديث البيانات لتتوافق مع هيكل جدول الحسابات الموحد
      final Map<String, dynamic> accountData = {
        'name': _nameController.text,
        'type': 'asset', // نوع افتراضي للحساب (يمكن تغييره لاحقاً)
        'category': 'current_asset', // فئة افتراضية للحساب
        'subcategory': 'cash', // فئة فرعية افتراضية للحساب
        'parent_id': _selectedParentId, // استخدام الحساب الأب المحدد
        'is_parent': _isMainAccount ? 1 : 0, // هل الحساب أب لحسابات أخرى
        'level': accountLevel, // مستوى الحساب في التسلسل الهرمي
        'balance': 0.0, // الرصيد الافتتاحي
        'description':
            _notesController.text.isEmpty ? null : _notesController.text,
        'phone': _phoneController.text.isEmpty ? null : _phoneController.text,
        'is_active': _isActive ? 1 : 0, // حالة الحساب (نشط/غير نشط)
        'updated_at': now,
        'updated_by': 1, // معرف المستخدم الحالي
      };

      if (widget.account == null) {
        // Creating a new account
        accountData['created_at'] = now;
        accountData['created_by'] = 1; // Replace with actual user ID

        // Generate account number based on hierarchy
        final accountNumber = await _generateAccountNumber(_selectedParentId);
        accountData['code'] = accountNumber;

        AppLogger.debug('- Generated Code: $accountNumber');

        try {
          // محاولة إدخال البيانات
          final int newId = await db.insert('accounts', accountData);
          AppLogger.info('Account created successfully with ID: $newId');

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إنشاء الحساب بنجاح (ID: $newId)'),
                backgroundColor: AppColors.success,
              ),
            );
          }
        } catch (e) {
          AppLogger.error('Failed to insert account: ${e.toString()}');
          // إعادة رمي الاستثناء ليتم التقاطه في كتلة الـ catch الخارجية
          rethrow;
        }
      } else {
        // Updating an existing account
        try {
          // محاولة تحديث البيانات
          final int updatedRows = await db.update(
            'accounts',
            accountData,
            where: 'id = ?',
            whereArgs: [widget.account!['id']],
          );

          AppLogger.info(
              'Account updated successfully, rows affected: $updatedRows');

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم تحديث الحساب بنجاح (rows: $updatedRows)'),
                backgroundColor: AppColors.success,
              ),
            );
          }
        } catch (e) {
          AppLogger.error('Failed to update account: ${e.toString()}');
          // إعادة رمي الاستثناء ليتم التقاطه في كتلة الـ catch الخارجية
          rethrow;
        }
      }

      setState(() {
        _isSaving = false;
      });

      // Volver a la pantalla anterior después de un breve retraso
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          Navigator.pop(context);
        }
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to save account',
        error: e,
        stackTrace: stackTrace,
        context: {
          'screen': 'SimpleAccountFormScreen',
          'isEditing': widget.account != null,
          'accountName': _nameController.text,
        },
      );

      setState(() {
        _isSaving = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حفظ الحساب: ${e.toString()}'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  Future<String> _generateAccountNumber(String? parentId) async {
    try {
      final db = await _databaseHelper.database;
      String accountNumber = '';

      if (parentId == null) {
        // هذا حساب جذر (بدون أب)، قم بإنشاء رقم جذر جديد
        final result = await db.rawQuery('''
          SELECT code
          FROM accounts
          WHERE parent_id IS NULL OR parent_id = 0
          ORDER BY CAST(code AS INTEGER) DESC
          LIMIT 1
        ''');

        if (result.isEmpty) {
          // لا توجد حسابات جذر، ابدأ من 1
          accountNumber = '1';
        } else {
          // استخراج الرقم وزيادته بمقدار 1
          final lastCode = result.first['code'] as String? ?? '0';
          try {
            final lastNumber = int.parse(lastCode);
            accountNumber = (lastNumber + 1).toString();
          } catch (e) {
            // إذا لم يكن الرقم صالحًا، استخدم طابع زمني
            accountNumber = DateTime.now().millisecondsSinceEpoch.toString();
          }
        }
      } else {
        // هذا حساب له أب (قد يكون رئيسيًا أو فرعيًا)، استخدم رقم الأب كبادئة
        final parentResult = await db.query(
          'accounts',
          columns: ['code', 'account_type'],
          where: 'id = ?',
          whereArgs: [parentId],
        );

        if (parentResult.isNotEmpty) {
          final parentNumber = parentResult.first['code'] as String;
          final parentType =
              parentResult.first['account_type'] as String? ?? 'main';

          // التحقق من أن الأب هو حساب رئيسي
          if (parentType != 'main') {
            // إذا لم يكن الأب حسابًا رئيسيًا، أظهر خطأ واستخدم طابع زمني
            ErrorTracker.captureError(
              'Parent account is not a main account',
              error: 'Invalid parent account type: $parentType',
              stackTrace: StackTrace.current,
              context: {'parentId': parentId},
            );
            return DateTime.now().millisecondsSinceEpoch.toString();
          }

          // البحث عن أعلى رقم فرعي لهذا الأب
          final childResult = await db.rawQuery('''
            SELECT code
            FROM accounts
            WHERE parent_id = ? AND code LIKE '$parentNumber%'
            ORDER BY LENGTH(code) DESC, code DESC
            LIMIT 1
          ''', [parentId]);

          if (childResult.isEmpty) {
            // لا توجد حسابات فرعية لهذا الأب، ابدأ من 1
            accountNumber = '$parentNumber-1';
          } else {
            final lastChildCode = childResult.first['code'] as String;

            // استخراج الرقم الفرعي
            if (lastChildCode.contains('-')) {
              final parts = lastChildCode.split('-');
              final lastNumber = int.tryParse(parts.last) ?? 0;
              accountNumber = '${parts.first}-${lastNumber + 1}';
            } else {
              // إذا لم يكن هناك فاصل، أضف فاصلًا ورقم 1
              accountNumber = '$parentNumber-1';
            }
          }
        } else {
          // إذا لم يتم العثور على الأب، استخدم طابع زمني
          accountNumber = DateTime.now().millisecondsSinceEpoch.toString();
        }
      }

      return accountNumber;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to generate account number',
        error: e,
        stackTrace: stackTrace,
        context: {'parentId': parentId},
      );

      // استخدم طابع زمني كرقم احتياطي إذا فشل التوليد
      return DateTime.now().millisecondsSinceEpoch.toString();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.account != null;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    return Scaffold(
      backgroundColor: isDark
          ? AppColors.lightSurfaceVariant
          : AppColors.lightSurfaceVariant,
      appBar: AkAppBar(
        title: isEditing ? 'تعديل حساب' : 'إضافة حساب جديد',
        showBackButton: true,
        actions: [
          if (!_isSaving)
            IconButton(
              icon: const Icon(Icons.save),
              tooltip: 'حفظ',
              onPressed: _saveAccount,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildBody(isDark, primaryColor, isEditing),
      bottomNavigationBar:
          Layout.isMobile() ? _buildBottomBar(primaryColor, isEditing) : null,
    );
  }

  Widget _buildBody(bool isDark, Color primaryColor, bool isEditing) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // نوع الحساب (رئيسي/فرعي)
                _buildAccountTypeSelector(isDark, primaryColor),
                const SizedBox(height: AppDimensions.spacing24),

                // معلومات الحساب
                _buildAccountInfoCard(isDark, primaryColor),
                const SizedBox(height: AppDimensions.spacing16),

                // معلومات إضافية
                _buildAdditionalInfoCard(isDark, primaryColor),
                const SizedBox(height: AppDimensions.spacing24),

                // زر الحفظ للأجهزة اللوحية والحواسيب
                if (Layout.isTablet() || Layout.isDesktop())
                  Center(
                    child: ElevatedButton.icon(
                      onPressed: _isSaving ? null : _saveAccount,
                      icon: _isSaving
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: AppColors.lightTextSecondary,
                                strokeWidth: 2,
                              ),
                            )
                          : const Icon(Icons.save),
                      label: Text(
                        isEditing ? 'تحديث الحساب' : 'حفظ الحساب',
                        style: const AppTypography(fontSize: 16),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        foregroundColor: AppColors.onPrimary,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomBar(Color primaryColor, bool isEditing) {
    return BottomAppBar(
      elevation: 8,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: ElevatedButton(
          onPressed: _isSaving ? null : _saveAccount,
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryColor,
            foregroundColor: AppColors.onPrimary,
            padding: const EdgeInsets.symmetric(vertical: 14),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: _isSaving
              ? const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: AppColors.lightTextSecondary,
                        strokeWidth: 2,
                      ),
                    ),
                    SizedBox(width: 12),
                    Text('جاري الحفظ...'),
                  ],
                )
              : Text(
                  isEditing ? 'تحديث الحساب' : 'حفظ الحساب',
                  style: const AppTypography(fontSize: 16),
                ),
        ),
      ),
    );
  }

  Widget _buildAccountTypeSelector(bool isDark, Color primaryColor) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.category, color: primaryColor),
                const SizedBox(width: 8),
                Text(
                  'نوع الحساب',
                  style: AppTypography(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark
                        ? AppColors.onPrimary
                        : AppColors.textEmphasisHigh,
                  ),
                ),
              ],
            ),
            const Divider(height: 24),

            // نوع الحساب (رئيسي/فرعي)
            Row(
              children: [
                Expanded(
                  child: _buildAccountTypeOption(
                    isDark: isDark,
                    primaryColor: primaryColor,
                    isSelected: _isMainAccount,
                    title: 'حساب رئيسي',
                    subtitle: 'يقبل إضافة حسابات فرعية',
                    icon: Icons.account_tree,
                    onTap: () {
                      setState(() {
                        _isMainAccount = true;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildAccountTypeOption(
                    isDark: isDark,
                    primaryColor: primaryColor,
                    isSelected: !_isMainAccount,
                    title: 'حساب فرعي',
                    subtitle: 'لا يقبل إضافة حسابات فرعية',
                    icon: Icons.subdirectory_arrow_right,
                    onTap: () {
                      setState(() {
                        _isMainAccount = false;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountTypeOption({
    required bool isDark,
    required Color primaryColor,
    required bool isSelected,
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? (isDark
                  ? AppColors.lightSurfaceVariant
                  : primaryColor.withValues(alpha: 0.1))
              : (isDark
                  ? AppColors.lightSurfaceVariant
                  : AppColors.lightSurfaceVariant),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? primaryColor
                : AppColors.lightTextSecondary.withValues(alpha: 0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? primaryColor : AppColors.lightTextSecondary,
              size: 32,
            ),
            const SizedBox(height: AppDimensions.spacing8),
            Text(
              title,
              style: AppTypography(
                fontWeight: FontWeight.bold,
                color: isSelected
                    ? primaryColor
                    : (isDark
                        ? AppColors.onPrimary
                        : AppColors.textEmphasisHigh),
              ),
            ),
            const SizedBox(height: AppDimensions.spacing4),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: AppTypography(
                fontSize: 12,
                color: isDark
                    ? AppColors.lightSurfaceVariant
                    : AppColors.lightSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountInfoCard(bool isDark, Color primaryColor) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: primaryColor),
                const SizedBox(width: 8),
                Text(
                  'معلومات الحساب',
                  style: AppTypography(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark
                        ? AppColors.onPrimary
                        : AppColors.textEmphasisHigh,
                  ),
                ),
              ],
            ),
            const Divider(height: 24),

            // اسم الحساب
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'اسم الحساب *',
                hintText: 'أدخل اسم الحساب',
                prefixIcon: const Icon(Icons.account_balance),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: isDark
                    ? AppColors.lightSurfaceVariant
                    : AppColors.lightSurfaceVariant,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'اسم الحساب مطلوب';
                }
                if (value.length < 3) {
                  return 'يجب أن يكون اسم الحساب 3 أحرف على الأقل';
                }
                return null;
              },
            ),
            const SizedBox(height: AppDimensions.spacing16),

            // الحساب الأب (يظهر دائمًا)
            Text(
              _isMainAccount ? 'الحساب الأب (اختياري)' : 'الحساب الأب *',
              style: AppTypography(
                fontWeight: FontWeight.bold,
                color:
                    isDark ? AppColors.onPrimary : AppColors.textEmphasisHigh,
              ),
            ),
            const SizedBox(height: AppDimensions.spacing8),
            DropdownButtonFormField<String?>(
              value: _selectedParentId,
              decoration: InputDecoration(
                hintText: 'اختر الحساب الأب',
                prefixIcon: const Icon(Icons.account_tree),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: isDark
                    ? AppColors.lightSurfaceVariant
                    : AppColors.lightSurfaceVariant,
              ),
              items: _parentAccounts.isEmpty
                  ? []
                  : _parentAccounts.map((account) {
                      final level = account['level'] as int;
                      final accountName = account['name'] as String;
                      final accountPath = account['path'] as String;
                      final accountType =
                          account['account_type'] as String? ?? 'main';
                      final isMainAccount = accountType == 'main';

                      return DropdownMenuItem<String?>(
                        value: account['id'].toString(),
                        child: Padding(
                          padding: EdgeInsets.only(right: level * 16.0),
                          child: Row(
                            children: [
                              // أيقونة تشير إلى مستوى الحساب
                              if (level > 0) ...[
                                const Icon(
                                  Icons.subdirectory_arrow_right,
                                  size: 16,
                                  color: AppColors.lightTextSecondary,
                                ),
                                const SizedBox(width: 4),
                              ],

                              // أيقونة نوع الحساب
                              Icon(
                                isMainAccount
                                    ? Icons.account_balance
                                    : Icons.subdirectory_arrow_right,
                                size: 16,
                                color: isMainAccount
                                    ? AppColors.info
                                    : AppColors.warning,
                              ),
                              const SizedBox(width: 8),

                              // اسم الحساب
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      accountName,
                                      style: const AppTypography(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    if (level > 0)
                                      Flexible(
                                        child: Text(
                                          accountPath,
                                          style: const AppTypography(
                                            fontSize: 10,
                                            color: AppColors.lightTextSecondary,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 1,
                                        ),
                                      ),
                                  ],
                                ),
                              ),

                              // رمز الحساب
                              if (account['code'] != null) ...[
                                const SizedBox(width: 8),
                                Text(
                                  '#${account['code']}',
                                  style: const AppTypography(
                                    fontSize: 12,
                                    color: AppColors.lightTextSecondary,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      );
                    }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedParentId = value;
                });
              },
              validator: (value) {
                if (!_isMainAccount && value == null) {
                  return 'يجب اختيار حساب أب للحساب الفرعي';
                }
                return null;
              },
              isExpanded: true,
              icon: const Icon(Icons.arrow_drop_down_circle),
              dropdownColor:
                  isDark ? AppColors.lightSurfaceVariant : AppColors.onPrimary,
              menuMaxHeight: 400, // ارتفاع أقصى للقائمة المنسدلة
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard(bool isDark, Color primaryColor) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.more_horiz, color: primaryColor),
                const SizedBox(width: 8),
                Text(
                  'معلومات إضافية',
                  style: AppTypography(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark
                        ? AppColors.onPrimary
                        : AppColors.textEmphasisHigh,
                  ),
                ),
              ],
            ),
            const Divider(height: 24),

            // رقم الهاتف
            TextFormField(
              controller: _phoneController,
              decoration: InputDecoration(
                labelText: 'رقم الهاتف',
                hintText: 'أدخل رقم الهاتف (اختياري)',
                prefixIcon: const Icon(Icons.phone),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: isDark
                    ? AppColors.lightSurfaceVariant
                    : AppColors.lightSurfaceVariant,
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  if (value.length < 8 ||
                      !RegExp(r'^[0-9+\-\s]+$').hasMatch(value)) {
                    return 'يرجى إدخال رقم هاتف صحيح';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: AppDimensions.spacing16),

            // الملاحظات
            TextFormField(
              controller: _notesController,
              decoration: InputDecoration(
                labelText: 'ملاحظات',
                hintText: 'أدخل ملاحظات إضافية (اختياري)',
                prefixIcon: const Icon(Icons.note),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: isDark
                    ? AppColors.lightSurfaceVariant
                    : AppColors.lightSurfaceVariant,
              ),
              maxLines: 3,
            ),

            const SizedBox(height: AppDimensions.spacing24),

            // حالة الحساب (نشط/غير نشط)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isDark
                    ? AppColors.lightSurfaceVariant
                    : AppColors.lightSurfaceVariant,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.lightTextSecondary.withValues(alpha: 0.4),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _isActive ? Icons.check_circle : Icons.cancel,
                    color: _isActive ? AppColors.success : AppColors.error,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'حالة الحساب',
                          style: AppTypography(
                            fontWeight: FontWeight.bold,
                            color: isDark
                                ? AppColors.onPrimary
                                : AppColors.textEmphasisHigh,
                          ),
                        ),
                        Text(
                          _isActive ? 'الحساب نشط' : 'الحساب غير نشط',
                          style: AppTypography(
                            fontSize: 12,
                            color: isDark
                                ? AppColors.lightSurfaceVariant
                                : AppColors.lightSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: _isActive,
                    onChanged: (value) {
                      setState(() {
                        _isActive = value;
                      });
                    },
                    activeColor: primaryColor,
                    activeTrackColor: primaryColor.withValues(alpha: 0.4),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
