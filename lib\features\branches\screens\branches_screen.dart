import 'package:flutter/material.dart';
import '../../../core/utils/app_logger.dart';

import '../../../core/widgets/index.dart'; // استخدام الودجات الموحدة
import '../models/branch.dart';
import '../services/branch_service.dart';
import 'branch_form_screen.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

/// شاشة إدارة الفروع
class BranchesScreen extends StatefulWidget {
  const BranchesScreen({Key? key}) : super(key: key);

  @override
  State<BranchesScreen> createState() => _BranchesScreenState();
}

class _BranchesScreenState extends State<BranchesScreen> {
  final _searchController = TextEditingController();
  bool _isLoading = false;
  bool _includeInactive = false;
  List<Branch> _branches = [];
  List<Branch> _filteredBranches = [];
  final _branchService = BranchService();

  @override
  void initState() {
    super.initState();
    _loadBranches();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل قائمة الفروع
  Future<void> _loadBranches() async {
    setState(() {
      _isLoading = true;
    });

    try {
      AppLogger.info('بدء تحميل الفروع في شاشة الفروع...');

      final branches =
          await _branchService.getBranches(includeInactive: _includeInactive);

      AppLogger.info('تم استرجاع ${branches.length} فرع في شاشة الفروع');

      // طباعة تفاصيل الفروع للتشخيص
      for (var branch in branches) {
        AppLogger.info(
            'فرع: ${branch.name}, معرف: ${branch.id}, رئيسي: ${branch.isMain}, نشط: ${branch.isActive}');
      }

      setState(() {
        _branches = branches;
        _filteredBranches = branches;
        _isLoading = false;
      });

      // عرض رسالة إذا كانت القائمة فارغة
      if (branches.isEmpty && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('لا توجد فروع متاحة. قم بإضافة فرع جديد.')),
        );
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل الفروع: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تحميل الفروع: $e')),
        );
      }
    }
  }

  /// تصفية الفروع حسب البحث
  void _filterBranches(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredBranches = _branches;
      });
      return;
    }

    setState(() {
      _filteredBranches = _branches.where((branch) {
        return branch.name.toLowerCase().contains(query.toLowerCase()) ||
            (branch.code?.toLowerCase().contains(query.toLowerCase()) ??
                false) ||
            (branch.address?.toLowerCase().contains(query.toLowerCase()) ??
                false);
      }).toList();
    });
  }

  /// إضافة فرع جديد
  void _addBranch() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BranchFormScreen(),
      ),
    );

    if (result == true) {
      _loadBranches();
    }
  }

  /// تعديل فرع
  void _editBranch(Branch branch) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BranchFormScreen(branch: branch),
      ),
    );

    if (result == true) {
      _loadBranches();
    }
  }

  /// حذف فرع
  void _deleteBranch(Branch branch) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الفرع "${branch.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child:
                const Text('حذف', style: AppTypography(color: AppColors.error)),
          ),
        ],
      ),
    );

    if (confirm == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        await _branchService.deleteBranch(branch.id);
        _loadBranches();
      } catch (e) {
        AppLogger.error('خطأ في حذف الفرع: $e');
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('حدث خطأ أثناء حذف الفرع')),
          );
        }
      }
    }
  }

  /// تغيير حالة الفرع (نشط/غير نشط)
  void _toggleBranchStatus(Branch branch) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _branchService.updateBranchStatus(branch.id, !branch.isActive);
      _loadBranches();
    } catch (e) {
      AppLogger.error('خطأ في تغيير حالة الفرع: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ أثناء تغيير حالة الفرع')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'إدارة الفروع',
        showBackButton: true,
        actions: [
          IconButton(
            icon: Icon(
                _includeInactive ? Icons.visibility : Icons.visibility_off),
            tooltip: _includeInactive
                ? 'إخفاء الفروع غير النشطة'
                : 'إظهار الفروع غير النشطة',
            onPressed: () {
              setState(() {
                _includeInactive = !_includeInactive;
              });
              _loadBranches();
            },
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: _addBranch,
        tooltip: 'إضافة فرع جديد',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // حقل البحث
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'بحث عن فرع...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: _filterBranches,
          ),
        ),

        // قائمة الفروع
        Expanded(
          child: _filteredBranches.isEmpty
              ? const Center(child: Text('لا توجد فروع'))
              : ListView.builder(
                  padding: const EdgeInsets.all(8.0),
                  itemCount: _filteredBranches.length,
                  itemBuilder: (context, index) {
                    final branch = _filteredBranches[index];
                    return _buildBranchCard(branch);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildBranchCard(Branch branch) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: branch.isMain
              ? AppColors.warning.withValues(alpha: 0.2)
              : branch.isActive
                  ? AppColors.success.withValues(alpha: 0.2)
                  : AppColors.error.withValues(alpha: 0.2),
          child: Icon(
            Icons.store,
            color: branch.isMain
                ? AppColors.warning
                : branch.isActive
                    ? AppColors.success
                    : AppColors.error,
          ),
        ),
        title: Text(
          branch.name,
          style: AppTypography(
            fontWeight: FontWeight.bold,
            color: branch.isActive ? null : AppColors.error,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (branch.code != null && branch.code!.isNotEmpty)
              Text('الرمز: ${branch.code}'),
            if (branch.address != null && branch.address!.isNotEmpty)
              Text('العنوان: ${branch.address}'),
            if (branch.phone != null && branch.phone!.isNotEmpty)
              Text('الهاتف: ${branch.phone}'),
            Row(
              children: [
                if (branch.isMain)
                  Chip(
                    label: const Text('رئيسي'),
                    backgroundColor: AppColors.amber.withValues(alpha: 0.2),
                    labelStyle: const AppTypography(
                        color: AppColors.amber, fontSize: 12),
                  ),
                const SizedBox(width: 8),
                Chip(
                  label: Text(branch.isActive ? 'نشط' : 'غير نشط'),
                  backgroundColor: branch.isActive
                      ? AppColors.success.withValues(alpha: 0.2)
                      : AppColors.error.withValues(alpha: 0.2),
                  labelStyle: AppTypography(
                    color:
                        branch.isActive ? AppColors.success : AppColors.error,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _editBranch(branch);
                break;
              case 'toggle':
                _toggleBranchStatus(branch);
                break;
              case 'delete':
                if (!branch.isMain) {
                  _deleteBranch(branch);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('لا يمكن حذف الفرع الرئيسي')),
                  );
                }
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem<String>(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            PopupMenuItem<String>(
              value: 'toggle',
              child: Row(
                children: [
                  Icon(branch.isActive
                      ? Icons.visibility_off
                      : Icons.visibility),
                  const SizedBox(width: 8),
                  Text(branch.isActive ? 'تعطيل' : 'تفعيل'),
                ],
              ),
            ),
            if (!branch.isMain)
              const PopupMenuItem<String>(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: AppColors.error),
                    SizedBox(width: 8),
                    Text('حذف', style: AppTypography(color: AppColors.error)),
                  ],
                ),
              ),
          ],
        ),
        onTap: () => _editBranch(branch),
      ),
    );
  }
}
