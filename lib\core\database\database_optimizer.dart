import '../utils/app_logger.dart';
import 'database_service.dart';

/// مُحسن قاعدة البيانات - يحسن الأداء ويدير الترقيات
class DatabaseOptimizer {
  static const String _tag = 'DatabaseOptimizer';

  /// إصدار قاعدة البيانات الحالي
  static const int currentVersion = 1;

  /// تحسين قاعدة البيانات
  static Future<void> optimizeDatabase() async {
    try {
      AppLogger.info('$_tag: بدء تحسين قاعدة البيانات...');

      final stopwatch = Stopwatch()..start();

      // 1. تحليل الجداول
      await _analyzeTablesPerformance();

      // 2. تحسين الفهارس
      await _optimizeIndexes();

      // 3. تنظيف البيانات القديمة
      await _cleanupOldData();

      // 4. تحديث الإحصائيات
      await _updateStatistics();

      stopwatch.stop();
      AppLogger.info(
          '$_tag: تم تحسين قاعدة البيانات في ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      AppLogger.error('$_tag: فشل في تحسين قاعدة البيانات: $e');
    }
  }

  /// تحليل أداء الجداول
  static Future<void> _analyzeTablesPerformance() async {
    try {
      AppLogger.info('$_tag: تحليل أداء الجداول...');

      final tables = [
        'products',
        'sales',
        'purchases',
        'inventory',
        'customers',
        'suppliers',
        'accounts'
      ];

      for (final table in tables) {
        await _analyzeTable(table);
      }
    } catch (e) {
      AppLogger.error('$_tag: فشل في تحليل الجداول: $e');
    }
  }

  /// تحليل جدول محدد
  static Future<void> _analyzeTable(String tableName) async {
    try {
      final db = DatabaseService();

      // عدد الصفوف
      final countResult =
          await db.rawQuery('SELECT COUNT(*) as count FROM $tableName');
      final rowCount = countResult.first['count'] as int;

      // حجم الجدول (تقديري)
      final sizeEstimate = rowCount * 100; // تقدير 100 بايت لكل صف

      AppLogger.info(
          '$_tag: جدول $tableName - الصفوف: $rowCount، الحجم المقدر: ${sizeEstimate}B');

      // إذا كان الجدول كبير، اقترح تحسينات
      if (rowCount > 10000) {
        AppLogger.info('$_tag: جدول $tableName كبير، يُنصح بتحسين الفهارس');
      }
    } catch (e) {
      AppLogger.error('$_tag: فشل في تحليل جدول $tableName: $e');
    }
  }

  /// تحسين الفهارس
  static Future<void> _optimizeIndexes() async {
    try {
      AppLogger.info('$_tag: تحسين الفهارس...');

      final db = DatabaseService();

      // فهارس مقترحة للأداء
      final suggestedIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)',
        'CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(sale_date)',
        'CREATE INDEX IF NOT EXISTS idx_purchases_date ON purchases(purchase_date)',
        'CREATE INDEX IF NOT EXISTS idx_inventory_product ON inventory(product_id)',
        'CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)',
        'CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name)',
      ];

      for (final indexSql in suggestedIndexes) {
        try {
          await db.rawQuery(indexSql);
          AppLogger.info('$_tag: تم إنشاء فهرس بنجاح');
        } catch (e) {
          // الفهرس موجود بالفعل أو خطأ آخر
          AppLogger.debug('$_tag: فهرس موجود أو خطأ: $e');
        }
      }
    } catch (e) {
      AppLogger.error('$_tag: فشل في تحسين الفهارس: $e');
    }
  }

  /// تنظيف البيانات القديمة
  static Future<void> _cleanupOldData() async {
    try {
      AppLogger.info('$_tag: تنظيف البيانات القديمة...');

      final db = DatabaseService();

      // حذف السجلات المحذوفة منذ أكثر من 30 يوم
      final thirtyDaysAgo =
          DateTime.now().subtract(const Duration(days: 30)).toIso8601String();

      final tables = ['products', 'customers', 'suppliers'];

      for (final table in tables) {
        try {
          final result = await db.delete(
            table,
            where: 'is_deleted = 1 AND updated_at < ?',
            whereArgs: [thirtyDaysAgo],
          );
          AppLogger.info('$_tag: تم تنظيف $result سجل من جدول $table');
        } catch (e) {
          AppLogger.debug('$_tag: لا يمكن تنظيف جدول $table: $e');
        }
      }

      // تنظيف سجلات النشاط القديمة (أكثر من 90 يوم)
      final ninetyDaysAgo =
          DateTime.now().subtract(const Duration(days: 90)).toIso8601String();
      try {
        await db.delete(
          'activity_logs',
          where: 'created_at < ?',
          whereArgs: [ninetyDaysAgo],
        );
        AppLogger.info('$_tag: تم تنظيف سجلات النشاط القديمة');
      } catch (e) {
        AppLogger.debug('$_tag: لا يمكن تنظيف سجلات النشاط: $e');
      }
    } catch (e) {
      AppLogger.error('$_tag: فشل في تنظيف البيانات: $e');
    }
  }

  /// تحديث إحصائيات قاعدة البيانات
  static Future<void> _updateStatistics() async {
    try {
      AppLogger.info('$_tag: تحديث إحصائيات قاعدة البيانات...');

      final db = DatabaseService();

      // تحديث إحصائيات SQLite
      await db.rawQuery('ANALYZE');

      AppLogger.info('$_tag: تم تحديث الإحصائيات بنجاح');
    } catch (e) {
      AppLogger.error('$_tag: فشل في تحديث الإحصائيات: $e');
    }
  }

  /// ترقية قاعدة البيانات
  static Future<void> upgradeDatabase(int oldVersion, int newVersion) async {
    try {
      AppLogger.info(
          '$_tag: ترقية قاعدة البيانات من $oldVersion إلى $newVersion');

      for (int version = oldVersion + 1; version <= newVersion; version++) {
        await _upgradeToVersion(version);
      }

      AppLogger.info('$_tag: تمت ترقية قاعدة البيانات بنجاح');
    } catch (e) {
      AppLogger.error('$_tag: فشل في ترقية قاعدة البيانات: $e');
      rethrow;
    }
  }

  /// ترقية إلى إصدار محدد
  static Future<void> _upgradeToVersion(int version) async {
    switch (version) {
      case 1:
        // الإصدار الأول - لا حاجة لترقية
        AppLogger.info('$_tag: الإصدار الأول - لا حاجة لترقية');
        break;
      case 2:
        // مثال: إضافة عمود جديد
        // final db = DatabaseService();
        // await db.rawQuery('ALTER TABLE products ADD COLUMN new_field TEXT');
        AppLogger.info('$_tag: ترقية إلى الإصدار 2 - لا توجد تغييرات مطلوبة');
        break;
      case 3:
        // مثال: إنشاء جدول جديد
        // final db = DatabaseService();
        // await db.rawQuery('CREATE TABLE new_table (id INTEGER PRIMARY KEY)');
        AppLogger.info('$_tag: ترقية إلى الإصدار 3 - لا توجد تغييرات مطلوبة');
        break;
      default:
        AppLogger.warning('$_tag: إصدار غير معروف: $version');
    }
  }

  /// الحصول على معلومات قاعدة البيانات
  static Future<Map<String, dynamic>> getDatabaseInfo() async {
    try {
      final db = DatabaseService();

      // معلومات أساسية
      final info = <String, dynamic>{
        'version': currentVersion,
        'tables': <String, int>{},
        'total_size_estimate': 0,
      };

      // عدد الصفوف في كل جدول
      final tables = [
        'products',
        'categories',
        'units',
        'customers',
        'suppliers',
        'sales',
        'purchases',
        'inventory',
        'accounts'
      ];

      int totalRows = 0;
      for (final table in tables) {
        try {
          final result =
              await db.rawQuery('SELECT COUNT(*) as count FROM $table');
          final count = result.first['count'] as int;
          info['tables'][table] = count;
          totalRows += count;
        } catch (e) {
          info['tables'][table] = 0;
        }
      }

      info['total_rows'] = totalRows;
      info['total_size_estimate'] = '${totalRows * 100}B'; // تقدير تقريبي

      return info;
    } catch (e) {
      AppLogger.error('$_tag: فشل في الحصول على معلومات قاعدة البيانات: $e');
      return {'error': e.toString()};
    }
  }
}
