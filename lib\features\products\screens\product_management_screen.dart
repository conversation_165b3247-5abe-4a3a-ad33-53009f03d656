import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';

import '../../../core/widgets/safe_layout.dart';
import '../../../core/widgets/index.dart';
import '../../../core/models/product.dart';
import '../presenters/product_presenter.dart';
import 'product_form_screen.dart';
import '../../../core/theme/index.dart';

/// شاشة إدارة المنتجات
class ProductManagementScreen extends StatefulWidget {
  const ProductManagementScreen({Key? key}) : super(key: key);

  @override
  State<ProductManagementScreen> createState() =>
      _ProductManagementScreenState();
}

class _ProductManagementScreenState extends State<ProductManagementScreen> {
  // مقدم المنتجات
  late ProductPresenter _productPresenter;

  // حالة البحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // حالة الفرز
  String _sortField = 'name';
  bool _sortAscending = true;

  // حالة التحميل
  bool _isLoading = false;

  // حالة الفلترة
  String? _selectedCategoryId;
  bool _showOutOfStock = false;
  bool _showLowStock = false;

  @override
  void initState() {
    super.initState();

    // تهيئة مقدم المنتجات
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
        () => ProductPresenter());

    // تحميل المنتجات
    _loadProducts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل المنتجات
  Future<void> _loadProducts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _productPresenter.loadProducts();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تحميل المنتجات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// فتح شاشة إضافة منتج جديد
  void _openAddProductScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ProductFormScreen(),
      ),
    ).then((_) => _loadProducts());
  }

  /// فتح شاشة تعديل منتج
  void _openEditProductScreen(Product product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductFormScreen(product: product),
      ),
    ).then((_) => _loadProducts());
  }

  /// حذف منتج
  Future<void> _deleteProduct(Product product) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنتج'),
        content: Text('هل أنت متأكد من حذف المنتج "${product.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final success = await _productPresenter.deleteProduct(product.id);

        if (success) {
          _showSuccessSnackBar('تم حذف المنتج بنجاح');
          _loadProducts();
        } else {
          _showErrorSnackBar('فشل حذف المنتج');
        }
      } catch (e) {
        _showErrorSnackBar('حدث خطأ أثناء حذف المنتج: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// فرز المنتجات
  void _sortProducts(String field) {
    setState(() {
      if (_sortField == field) {
        _sortAscending = !_sortAscending;
      } else {
        _sortField = field;
        _sortAscending = true;
      }
    });
  }

  /// عرض مربع حوار الفلترة
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        String? tempCategoryId = _selectedCategoryId;
        bool tempShowOutOfStock = _showOutOfStock;
        bool tempShowLowStock = _showLowStock;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('فلترة المنتجات'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // فلترة حسب الفئة
                    DropdownButtonFormField<String?>(
                      decoration: const InputDecoration(
                        labelText: 'الفئة',
                        border: OutlineInputBorder(),
                      ),
                      value: tempCategoryId,
                      items: [
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: Text('جميع الفئات'),
                        ),
                        ..._productPresenter.categories.map((category) {
                          return DropdownMenuItem<String?>(
                            value: category.id,
                            child: Text(category.name),
                          );
                        }).toList(),
                      ],
                      onChanged: (value) {
                        setState(() {
                          tempCategoryId = value;
                        });
                      },
                    ),
                    const SizedBox(height: AppDimensions.spacing16),

                    // فلترة حسب المخزون
                    CheckboxListTile(
                      title: const Text('إظهار المنتجات النافدة'),
                      value: tempShowOutOfStock,
                      onChanged: (value) {
                        setState(() {
                          tempShowOutOfStock = value ?? false;
                        });
                      },
                    ),
                    CheckboxListTile(
                      title: const Text('إظهار المنتجات منخفضة المخزون'),
                      value: tempShowLowStock,
                      onChanged: (value) {
                        setState(() {
                          tempShowLowStock = value ?? false;
                        });
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _selectedCategoryId = tempCategoryId;
                      _showOutOfStock = tempShowOutOfStock;
                      _showLowStock = tempShowLowStock;
                    });
                    Navigator.pop(context);
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// الحصول على المنتجات المفلترة
  List<Product> _getFilteredProducts() {
    // فلترة المنتجات حسب البحث والفئة والمخزون
    return _productPresenter.products.where((product) {
      // فلترة حسب البحث
      final name = product.name.toLowerCase();
      final sku = product.sku?.toLowerCase() ?? '';
      final barcode = product.barcode?.toLowerCase() ?? '';
      final query = _searchQuery.toLowerCase();

      final matchesSearch = name.contains(query) ||
          sku.contains(query) ||
          barcode.contains(query);

      // فلترة حسب الفئة
      final matchesCategory = _selectedCategoryId == null ||
          product.categoryId == _selectedCategoryId;

      // فلترة حسب المخزون
      bool matchesStock = true;
      if (_showOutOfStock && !_showLowStock) {
        matchesStock = product.quantity <= 0;
      } else if (!_showOutOfStock && _showLowStock) {
        matchesStock = product.quantity > 0 && product.isLowStock;
      } else if (_showOutOfStock && _showLowStock) {
        matchesStock = product.quantity <= 0 || product.isLowStock;
      }

      return matchesSearch && matchesCategory && matchesStock;
    }).toList()
      // فرز المنتجات
      ..sort((a, b) {
        int compare;

        switch (_sortField) {
          case 'name':
            compare = a.name.compareTo(b.name);
            break;
          case 'code':
            final aCode = a.sku ?? '';
            final bCode = b.sku ?? '';
            compare = aCode.compareTo(bCode);
            break;
          case 'price':
            compare = a.salePrice.compareTo(b.salePrice);
            break;
          case 'quantity':
            compare = a.quantity.compareTo(b.quantity);
            break;
          default:
            compare = a.name.compareTo(b.name);
        }

        return _sortAscending ? compare : -compare;
      });
  }

  @override
  Widget build(BuildContext context) {
    return SafeLayout(
      title: 'إدارة المنتجات',
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFilterDialog,
          tooltip: 'فلترة',
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadProducts,
          tooltip: 'تحديث',
        ),
      ],
      floatingActionButton: FloatingActionButton(
        onPressed: _openAddProductScreen,
        tooltip: 'إضافة منتج جديد',
        child: const Icon(Icons.add),
      ),
      body: _buildContent(),
      child: Container(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // شريط البحث
        Padding(
          padding: const EdgeInsets.all(16),
          child: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'بحث عن منتج...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
        ),

        // عدد المنتجات وحالة الفلترة
        Padding(
          padding:
              const EdgeInsets.symmetric(horizontal: AppDimensions.spacing16),
          child: Row(
            children: [
              Text(
                'عدد المنتجات: ${_getFilteredProducts().length}',
                style: AppTypography.lightTextTheme.titleMedium,
              ),
              const Spacer(),
              if (_searchQuery.isNotEmpty ||
                  _selectedCategoryId != null ||
                  _showOutOfStock ||
                  _showLowStock)
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                      _searchController.clear();
                      _selectedCategoryId = null;
                      _showOutOfStock = false;
                      _showLowStock = false;
                    });
                  },
                  icon: const Icon(Icons.clear),
                  label: const Text('مسح الفلاتر'),
                ),
            ],
          ),
        ),

        // قائمة المنتجات
        Expanded(
          child: _buildProductsList(),
        ),
      ],
    );
  }

  /// بناء قائمة المنتجات
  Widget _buildProductsList() {
    final products = _getFilteredProducts();

    if (products.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: AppDimensions.extraLargeIconSize,
              color: AppColors.lightTextSecondary,
            ),
            const SizedBox(height: AppDimensions.spacing16),
            Text(
              _searchQuery.isEmpty &&
                      _selectedCategoryId == null &&
                      !_showOutOfStock &&
                      !_showLowStock
                  ? 'لا يوجد منتجات'
                  : 'لا يوجد منتجات مطابقة للفلاتر',
              style: AppTypography.lightTextTheme.titleMedium,
            ),
            const SizedBox(height: AppDimensions.spacing16),
            if (_searchQuery.isEmpty &&
                _selectedCategoryId == null &&
                !_showOutOfStock &&
                !_showLowStock)
              ElevatedButton.icon(
                onPressed: _openAddProductScreen,
                icon: const Icon(Icons.add),
                label: const Text('إضافة منتج جديد'),
              ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: [
            DataColumn(
              label: const Text('الاسم'),
              onSort: (_, __) => _sortProducts('name'),
            ),
            DataColumn(
              label: const Text('الكود'),
              onSort: (_, __) => _sortProducts('code'),
            ),
            const DataColumn(
              label: Text('الفئة'),
            ),
            DataColumn(
              label: const Text('سعر البيع'),
              onSort: (_, __) => _sortProducts('price'),
              numeric: true,
            ),
            DataColumn(
              label: const Text('الكمية'),
              onSort: (_, __) => _sortProducts('quantity'),
              numeric: true,
            ),
            const DataColumn(
              label: Text('الإجراءات'),
            ),
          ],
          rows: products.map((product) {
            final categoryName =
                _productPresenter.getCategoryName(product.categoryId ?? '');
            final isLowStock =
                product.minStock > 0 && product.quantity < product.minStock;
            final isOutOfStock = product.quantity <= 0;

            return DataRow(
              cells: [
                DataCell(Text(product.name)),
                DataCell(Text(product.sku ?? '-')),
                DataCell(Text(categoryName)),
                DataCell(Text(product.salePrice.toStringAsFixed(2))),
                DataCell(
                  Text(
                    product.quantity.toStringAsFixed(2),
                    style: AppTypography(
                      color: isOutOfStock
                          ? AppColors.error
                          : isLowStock
                              ? AppColors.warning
                              : null,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () => _openEditProductScreen(product),
                        tooltip: 'تعديل',
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _deleteProduct(product),
                        tooltip: 'حذف',
                        color: AppColors.lightTextSecondary,
                      ),
                    ],
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
        ),
      );
    });
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.success,
        ),
      );
    });
  }
}
