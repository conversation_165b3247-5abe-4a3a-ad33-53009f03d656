import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/index.dart';
import '../presenters/voucher_presenter.dart';
import 'receipt_voucher_screen.dart';
import 'payment_voucher_screen.dart';
import 'double_entry_voucher_screen.dart';
import '../../simple_ties/screens/simple_tie_screen.dart';
import '../../../core/theme/index.dart';

class VouchersScreen extends StatefulWidget {
  const VouchersScreen({Key? key}) : super(key: key);

  @override
  State<VouchersScreen> createState() => _VouchersScreenState();
}

class _VouchersScreenState extends State<VouchersScreen> {
  bool _isLoading = false;
  DateTimeRange? _dateRange;

  // استخدام التحميل الكسول
  late final VoucherPresenter _voucherPresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _voucherPresenter = AppProviders.getLazyPresenter<VoucherPresenter>(
        () => VoucherPresenter());
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _voucherPresenter.loadVouchers(dateRange: _dateRange);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل البيانات: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'السندات',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية حسب التاريخ',
            onPressed: () {
              _showFilterDialog();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إدارة السندات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),

                  // السندات
                  Text(
                    'أنواع السندات',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      // سند القبض
                      Expanded(
                        child: _buildMenuCard(
                          context,
                          title: 'سند قبض',
                          icon: Icons.arrow_downward,
                          color: AppColors.lightTextSecondary,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const ReceiptVoucherScreen(),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      // سند الصرف
                      Expanded(
                        child: _buildMenuCard(
                          context,
                          title: 'سند صرف',
                          icon: Icons.arrow_upward,
                          color: AppColors.lightTextSecondary,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const PaymentVoucherScreen(),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      // قيد بسيط
                      Expanded(
                        child: _buildMenuCard(
                          context,
                          title: 'قيد بسيط',
                          icon: Icons.compare_arrows,
                          color: AppColors.lightTextSecondary,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SimpleTieScreen(),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      // قيد مزدوج
                      Expanded(
                        child: _buildMenuCard(
                          context,
                          title: 'قيد مزدوج',
                          icon: Icons.swap_horiz,
                          color: AppColors.lightTextSecondary,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const DoubleEntryVoucherScreen(),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // إحصائيات
                  _buildStatisticsCard(context),
                ],
              ),
            ),
    );
  }

  Widget _buildMenuCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return AkCard(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية حسب التاريخ'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                decoration: const InputDecoration(
                  labelText: 'تصفية حسب التاريخ',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                readOnly: true,
                onTap: () async {
                  Navigator.pop(context);
                  _loadData();
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard(BuildContext context) {
    final receiptVouchers = _voucherPresenter.vouchers
        .where((v) => v.voucherType == 'receipt')
        .toList();
    final paymentVouchers = _voucherPresenter.vouchers
        .where((v) => v.voucherType == 'payment')
        .toList();
    final journalVouchers = _voucherPresenter.vouchers
        .where((v) => v.voucherType == 'journal')
        .toList();
    final doubleEntryVouchers = _voucherPresenter.vouchers
        .where((v) => v.voucherType == 'double_entry')
        .toList();

    // حساب إجمالي المبالغ
    double totalReceipts = 0;
    double totalPayments = 0;

    for (var voucher in receiptVouchers) {
      totalReceipts += voucher.localAmount;
    }

    for (var voucher in paymentVouchers) {
      totalPayments += voucher.localAmount;
    }

    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات السندات',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    title: 'سندات القبض',
                    value: receiptVouchers.length.toString(),
                    amount: totalReceipts.toStringAsFixed(2),
                    icon: Icons.arrow_downward,
                    color: AppColors.lightTextSecondary,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatItem(
                    context,
                    title: 'سندات الصرف',
                    value: paymentVouchers.length.toString(),
                    amount: totalPayments.toStringAsFixed(2),
                    icon: Icons.arrow_upward,
                    color: AppColors.lightTextSecondary,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatItem(
                    context,
                    title: 'القيود البسيطة',
                    value: journalVouchers.length.toString(),
                    amount: '',
                    icon: Icons.compare_arrows,
                    color: AppColors.lightTextSecondary,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatItem(
                    context,
                    title: 'القيود المزدوجة',
                    value: doubleEntryVouchers.length.toString(),
                    amount: '',
                    icon: Icons.swap_horiz,
                    color: AppColors.lightTextSecondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الرصيد الصافي:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Text(
                  '${(totalReceipts - totalPayments).toStringAsFixed(2)} ر.ي',
                  style: AppTypography(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: totalReceipts - totalPayments >= 0
                        ? AppColors.success
                        : AppColors.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required String title,
    required String value,
    required String amount,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(width: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const AppTypography(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (amount.isNotEmpty)
          Text(
            '$amount ر.ي',
            style: AppTypography(
              fontSize: 14,
              color: color,
            ),
          ),
      ],
    );
  }
}
