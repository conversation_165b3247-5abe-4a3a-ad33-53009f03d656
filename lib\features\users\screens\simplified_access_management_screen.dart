import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/app_drawer.dart';

import '../../../core/theme/index.dart';
import '../presenters/role_presenter.dart';
import '../../../core/auth/models/user_role.dart';

/// شاشة إدارة الوصول المبسطة
/// تتيح للمستخدم إدارة مستويات الوصول بطريقة سهلة وبسيطة
class SimplifiedAccessManagementScreen extends StatefulWidget {
  static const String routeName = '/simplified-access-management';

  const SimplifiedAccessManagementScreen({Key? key}) : super(key: key);

  @override
  State<SimplifiedAccessManagementScreen> createState() =>
      _SimplifiedAccessManagementScreenState();
}

class _SimplifiedAccessManagementScreenState
    extends State<SimplifiedAccessManagementScreen> {
  bool _isLoading = true;
  String? _error;

  // الوظائف المتاحة في النظام
  final List<JobFunction> _jobFunctions = [
    JobFunction(
      id: 'sales',
      name: 'المبيعات',
      icon: Icons.point_of_sale,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المبيعات والفواتير والعملاء',
    ),
    JobFunction(
      id: 'inventory',
      name: 'المخزون',
      icon: Icons.inventory_2,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المنتجات والمخزون والمستودعات',
    ),
    JobFunction(
      id: 'purchases',
      name: 'المشتريات',
      icon: Icons.shopping_cart,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المشتريات والموردين',
    ),
    JobFunction(
      id: 'finance',
      name: 'المالية',
      icon: Icons.account_balance,
      color: AppColors.lightTextSecondary,
      description: 'إدارة الحسابات والمصروفات والإيرادات',
    ),
    JobFunction(
      id: 'reports',
      name: 'التقارير',
      icon: Icons.bar_chart,
      color: AppColors.lightTextSecondary,
      description: 'عرض وطباعة التقارير المختلفة',
    ),
    JobFunction(
      id: 'settings',
      name: 'الإعدادات',
      icon: Icons.settings,
      color: AppColors.lightTextSecondary,
      description: 'إدارة إعدادات النظام والمستخدمين',
    ),
  ];

  // مستويات الوصول المتاحة
  List<AccessLevel> _accessLevels = [];

  @override
  void initState() {
    super.initState();

    // تحميل البيانات بعد بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // الحصول على مقدم الأدوار
      final rolePresenter =
          AppProviders.getLazyPresenter<RolePresenter>(() => RolePresenter());

      // تحميل الأدوار
      await rolePresenter.loadRoles();

      // تحويل الأدوار إلى مستويات وصول
      _accessLevels = _convertRolesToAccessLevels(rolePresenter.roles);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'حدث خطأ أثناء تحميل البيانات: $e';
      });
    }
  }

  /// تحويل الأدوار إلى مستويات وصول
  List<AccessLevel> _convertRolesToAccessLevels(List<UserRole> roles) {
    return roles.map((role) {
      // تحديد مستوى الوصول لكل وظيفة
      final functionLevels = <String, AccessLevelType>{};

      for (final jobFunction in _jobFunctions) {
        // البحث عن صلاحيات هذه الوظيفة
        final hasViewPermission = role.permissions.any((p) =>
            p.startsWith('view_${jobFunction.id}') ||
            p.contains('${jobFunction.id}_view'));

        final hasEditPermission = role.permissions.any((p) =>
            p.startsWith('edit_${jobFunction.id}') ||
            p.contains('${jobFunction.id}_edit') ||
            p.startsWith('create_${jobFunction.id}') ||
            p.contains('${jobFunction.id}_create'));

        final hasFullPermission = role.permissions.any((p) =>
            p.startsWith('manage_${jobFunction.id}') ||
            p.contains('${jobFunction.id}_manage') ||
            p.startsWith('delete_${jobFunction.id}') ||
            p.contains('${jobFunction.id}_delete'));

        // تحديد مستوى الوصول بناءً على الصلاحيات
        if (hasFullPermission) {
          functionLevels[jobFunction.id] = AccessLevelType.full;
        } else if (hasEditPermission) {
          functionLevels[jobFunction.id] = AccessLevelType.edit;
        } else if (hasViewPermission) {
          functionLevels[jobFunction.id] = AccessLevelType.view;
        } else {
          functionLevels[jobFunction.id] = AccessLevelType.none;
        }
      }

      return AccessLevel(
        id: role.id,
        name: role.displayName,
        isCustom: role.isCustom,
        functionLevels: functionLevels,
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الوصول'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _createNewAccessLevel,
            tooltip: 'إضافة مستوى وصول جديد',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _buildBody(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const AppTypography(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_accessLevels.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.no_accounts,
              size: 64,
              color: AppColors.lightTextSecondary,
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد مستويات وصول',
              style: AppTypography(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _createNewAccessLevel,
              icon: const Icon(Icons.add),
              label: const Text('إضافة مستوى وصول جديد'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _accessLevels.length,
      itemBuilder: (context, index) {
        final accessLevel = _accessLevels[index];
        return _buildAccessLevelCard(accessLevel);
      },
    );
  }

  /// بناء بطاقة مستوى الوصول
  Widget _buildAccessLevelCard(AccessLevel accessLevel) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان البطاقة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.12),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    accessLevel.name,
                    style: const AppTypography(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (accessLevel.isCustom)
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () => _editAccessLevel(accessLevel),
                    tooltip: 'تعديل',
                    color: AppColors.lightTextSecondary,
                  ),
                if (accessLevel.isCustom)
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () => _deleteAccessLevel(accessLevel),
                    tooltip: 'حذف',
                    color: AppColors.lightTextSecondary,
                  ),
              ],
            ),
          ),

          // محتوى البطاقة
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'الوصول إلى:',
                  style: AppTypography(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),

                // قائمة الوظائف ومستويات الوصول
                ...List.generate(_jobFunctions.length, (index) {
                  final jobFunction = _jobFunctions[index];
                  final accessLevelType =
                      accessLevel.functionLevels[jobFunction.id] ??
                          AccessLevelType.none;

                  return _buildFunctionAccessRow(jobFunction, accessLevelType);
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف الوصول للوظيفة
  Widget _buildFunctionAccessRow(
      JobFunction jobFunction, AccessLevelType accessLevelType) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // أيقونة الوظيفة
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: jobFunction.color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              jobFunction.icon,
              color: jobFunction.color,
            ),
          ),
          const SizedBox(width: 12),

          // اسم الوظيفة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  jobFunction.name,
                  style: const AppTypography(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  jobFunction.description,
                  style: const AppTypography(
                    fontSize: 12,
                    color: AppColors.lightTextSecondary,
                  ),
                ),
              ],
            ),
          ),

          // مستوى الوصول
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color:
                  _getAccessLevelColor(accessLevelType).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              _getAccessLevelName(accessLevelType),
              style: AppTypography(
                color: _getAccessLevelColor(accessLevelType),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم مستوى الوصول
  String _getAccessLevelName(AccessLevelType type) {
    switch (type) {
      case AccessLevelType.none:
        return 'لا وصول';
      case AccessLevelType.view:
        return 'عرض فقط';
      case AccessLevelType.edit:
        return 'تعديل';
      case AccessLevelType.full:
        return 'وصول كامل';
    }
  }

  /// الحصول على لون مستوى الوصول
  Color _getAccessLevelColor(AccessLevelType type) {
    switch (type) {
      case AccessLevelType.none:
        return AppColors.accessNone;
      case AccessLevelType.view:
        return AppColors.accessView;
      case AccessLevelType.edit:
        return AppColors.accessEdit;
      case AccessLevelType.full:
        return AppColors.accessFull;
    }
  }

  /// إنشاء مستوى وصول جديد
  void _createNewAccessLevel() {
    // سيتم تنفيذها لاحقاً
  }

  /// تعديل مستوى وصول
  void _editAccessLevel(AccessLevel accessLevel) {
    // سيتم تنفيذها لاحقاً
  }

  /// حذف مستوى وصول
  void _deleteAccessLevel(AccessLevel accessLevel) {
    // سيتم تنفيذها لاحقاً
  }
}

/// نموذج الوظيفة
class JobFunction {
  final String id;
  final String name;
  final IconData icon;
  final Color color;
  final String description;

  JobFunction({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    required this.description,
  });
}

/// نوع مستوى الوصول
enum AccessLevelType {
  none, // لا وصول
  view, // عرض فقط
  edit, // تعديل
  full, // وصول كامل
}

/// نموذج مستوى الوصول
class AccessLevel {
  final String id;
  final String name;
  final bool isCustom;
  final Map<String, AccessLevelType> functionLevels;

  AccessLevel({
    required this.id,
    required this.name,
    required this.isCustom,
    required this.functionLevels,
  });
}
