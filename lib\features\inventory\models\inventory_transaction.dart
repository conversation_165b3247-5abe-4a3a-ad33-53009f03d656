import 'package:uuid/uuid.dart';

/// نوع حركة المخزون
enum InventoryTransactionType {
  /// إضافة (شراء، تعديل رصيد، إرجاع مبيعات)
  incoming,

  /// صرف (بيع، تعديل رصيد، إرجاع مشتريات)
  out,

  /// تحويل من مخزن لآخر
  transfer,

  /// ضبط كميات المخزون
  adjustment,
}

/// حالة حركة المخزون
enum InventoryTransactionStatus {
  /// مسودة
  draft,

  /// مرحلة
  posted,

  /// ملغاة
  cancelled,
}

/// نموذج حركة المخزون
class InventoryTransaction {
  final String id;
  final DateTime date;
  final String reference;
  final String productId;
  final String? warehouseId;
  final String? targetWarehouseId; // في حالة التحويل بين المخازن
  final InventoryTransactionType type;
  final InventoryTransactionStatus status;
  final double quantity;
  final double? unitCost;
  final double totalCost;
  final String? notes;
  final String? sourceDocumentId; // معرف المستند المصدر (فاتورة، إرجاع، الخ)
  final String? sourceDocumentType; // نوع المستند المصدر (فاتورة مبيعات، فاتورة مشتريات، إلخ)
  final DateTime createdAt;
  final String? createdBy;
  final Map<String, dynamic>? metadata;

  InventoryTransaction({
    String? id,
    required this.date,
    required this.reference,
    required this.productId,
    this.warehouseId,
    this.targetWarehouseId,
    required this.type,
    InventoryTransactionStatus? status,
    required this.quantity,
    this.unitCost,
    required this.totalCost,
    this.notes,
    this.sourceDocumentId,
    this.sourceDocumentType,
    DateTime? createdAt,
    this.createdBy,
    this.metadata,
  }) : 
    id = id ?? const Uuid().v4(),
    status = status ?? InventoryTransactionStatus.draft,
    createdAt = createdAt ?? DateTime.now();

  /// تحويل النموذج إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'reference': reference,
      'product_id': productId,
      'warehouse_id': warehouseId,
      'target_warehouse_id': targetWarehouseId,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'quantity': quantity,
      'unit_cost': unitCost,
      'total_cost': totalCost,
      'notes': notes,
      'source_document_id': sourceDocumentId,
      'source_document_type': sourceDocumentType,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'metadata': metadata?.toString(),
    };
  }

  /// إنشاء نموذج من خريطة
  factory InventoryTransaction.fromMap(Map<String, dynamic> map) {
    return InventoryTransaction(
      id: map['id'],
      date: DateTime.parse(map['date']),
      reference: map['reference'],
      productId: map['product_id'],
      warehouseId: map['warehouse_id'],
      targetWarehouseId: map['target_warehouse_id'],
      type: InventoryTransactionType.values.firstWhere(
        (e) => e.toString().split('.').last == map['type'],
        orElse: () => InventoryTransactionType.adjustment,
      ),
      status: InventoryTransactionStatus.values.firstWhere(
        (e) => e.toString().split('.').last == map['status'],
        orElse: () => InventoryTransactionStatus.draft,
      ),
      quantity: map['quantity'] is int ? (map['quantity'] as int).toDouble() : (map['quantity'] as double),
      unitCost: map['unit_cost'] != null 
        ? (map['unit_cost'] is int ? (map['unit_cost'] as int).toDouble() : (map['unit_cost'] as double))
        : null,
      totalCost: map['total_cost'] is int ? (map['total_cost'] as int).toDouble() : (map['total_cost'] as double),
      notes: map['notes'],
      sourceDocumentId: map['source_document_id'],
      sourceDocumentType: map['source_document_type'],
      createdAt: DateTime.parse(map['created_at']),
      createdBy: map['created_by'],
      metadata: map['metadata'] != null 
          ? Map<String, dynamic>.from(map['metadata']) 
          : null,
    );
  }

  /// نسخة جديدة من النموذج مع تغيير بعض الخصائص
  InventoryTransaction copyWith({
    String? id,
    DateTime? date,
    String? reference,
    String? productId,
    String? warehouseId,
    String? targetWarehouseId,
    InventoryTransactionType? type,
    InventoryTransactionStatus? status,
    double? quantity,
    double? unitCost,
    double? totalCost,
    String? notes,
    String? sourceDocumentId,
    String? sourceDocumentType,
    DateTime? createdAt,
    String? createdBy,
    Map<String, dynamic>? metadata,
  }) {
    return InventoryTransaction(
      id: id ?? this.id,
      date: date ?? this.date,
      reference: reference ?? this.reference,
      productId: productId ?? this.productId,
      warehouseId: warehouseId ?? this.warehouseId,
      targetWarehouseId: targetWarehouseId ?? this.targetWarehouseId,
      type: type ?? this.type,
      status: status ?? this.status,
      quantity: quantity ?? this.quantity,
      unitCost: unitCost ?? this.unitCost,
      totalCost: totalCost ?? this.totalCost,
      notes: notes ?? this.notes,
      sourceDocumentId: sourceDocumentId ?? this.sourceDocumentId,
      sourceDocumentType: sourceDocumentType ?? this.sourceDocumentType,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      metadata: metadata ?? this.metadata,
    );
  }
}
