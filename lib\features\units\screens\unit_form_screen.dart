import 'package:flutter/material.dart';
import '../../../core/models/unit.dart';
import '../presenters/unit_presenter.dart';

import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';
import '../../../core/providers/app_providers.dart';

class UnitFormScreen extends StatefulWidget {
  final Unit? unit;
  final String? unitType; // إضافة نوع الوحدة

  const UnitFormScreen({Key? key, this.unit, this.unitType = 'product'})
      : super(key: key);

  @override
  State<UnitFormScreen> createState() => _UnitFormScreenState();
}

class _UnitFormScreenState extends State<UnitFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late UnitPresenter _presenter;

  String? _name;
  String? _symbol;
  String? _description;
  bool _isBase = false;
  double _conversionFactor = 1.0;
  String? _baseUnitId;

  @override
  void initState() {
    super.initState();
    _presenter =
        AppProviders.getLazyPresenter<UnitPresenter>(() => UnitPresenter());
    _initializeFields();
  }

  void _initializeFields() {
    if (widget.unit != null) {
      _name = widget.unit!.name;
      _symbol = widget.unit!.symbol;
      _description = widget.unit!.description;
      _isBase = widget.unit!.isBase;
      _conversionFactor = widget.unit!.conversionFactor;
      _baseUnitId = widget.unit!.baseUnitId;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: widget.unit == null ? 'Add Unit' : 'Edit Unit',
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              initialValue: _name,
              decoration: const InputDecoration(
                labelText: 'Name',
                hintText: 'Enter unit name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a name';
                }
                return null;
              },
              onSaved: (value) => _name = value,
            ),
            const SizedBox(height: 16),
            TextFormField(
              initialValue: _symbol,
              decoration: const InputDecoration(
                labelText: 'Symbol',
                hintText: 'Enter unit symbol',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a symbol';
                }
                return null;
              },
              onSaved: (value) => _symbol = value,
            ),
            const SizedBox(height: 16),
            TextFormField(
              initialValue: _description,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Enter unit description (optional)',
              ),
              maxLines: 3,
              onSaved: (value) => _description = value,
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Base Unit'),
              subtitle: const Text('Is this a base unit?'),
              value: _isBase,
              onChanged: (value) {
                setState(() {
                  _isBase = value;
                  if (value) {
                    _conversionFactor = 1.0;
                    _baseUnitId = null;
                  }
                });
              },
            ),
            if (!_isBase) ...[
              const SizedBox(height: 16),
              ListenableBuilder(
                listenable: _presenter,
                builder: (context, child) {
                  final baseUnits =
                      _presenter.units.where((u) => u.isBase).toList();

                  if (baseUnits.isEmpty) {
                    return const Card(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Text(
                          'Please create a base unit first before adding derived units.',
                          style: AppTypography(color: AppColors.error),
                        ),
                      ),
                    );
                  }

                  return Column(
                    children: [
                      DropdownButtonFormField<String>(
                        value: _baseUnitId,
                        decoration: const InputDecoration(
                          labelText: 'Base Unit',
                          hintText: 'Select base unit',
                        ),
                        items: baseUnits.map((unit) {
                          return DropdownMenuItem(
                            value: unit.id,
                            child: Text('${unit.name} (${unit.symbol})'),
                          );
                        }).toList(),
                        validator: (value) {
                          if (!_isBase && (value == null || value.isEmpty)) {
                            return 'Please select a base unit';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          setState(() => _baseUnitId = value);
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        initialValue: _conversionFactor.toString(),
                        decoration: const InputDecoration(
                          labelText: 'Conversion Factor',
                          hintText: 'Enter conversion factor',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        validator: (value) {
                          if (!_isBase) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a conversion factor';
                            }
                            final factor = double.tryParse(value);
                            if (factor == null || factor <= 0) {
                              return 'Please enter a valid positive number';
                            }
                          }
                          return null;
                        },
                        onSaved: (value) {
                          if (value != null && value.isNotEmpty) {
                            _conversionFactor = double.parse(value);
                          }
                        },
                      ),
                    ],
                  );
                },
              ),
            ],
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _submitForm,
              child: Text(widget.unit == null ? 'Add Unit' : 'Update Unit'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    _formKey.currentState!.save();

    final unit = Unit(
      id: widget.unit?.id,
      name: _name!,
      symbol: _symbol!,
      description: _description,
      type: widget.unitType ?? widget.unit?.type ?? 'product',
      isBase: _isBase,
      conversionFactor: _conversionFactor,
      baseUnitId: _isBase ? null : _baseUnitId,
    );

    bool success;
    if (widget.unit == null) {
      success = await _presenter.addUnit(unit);
    } else {
      success = await _presenter.updateUnit(unit);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
                ? 'Unit ${widget.unit == null ? 'added' : 'updated'} successfully'
                : 'Failed to ${widget.unit == null ? 'add' : 'update'} unit',
          ),
          backgroundColor: success ? AppColors.success : AppColors.error,
        ),
      );

      if (success) {
        Navigator.pop(context, true);
      }
    }
  }
}
