import 'dart:io';
import 'package:archive/archive.dart';
import 'package:intl/intl.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../database/database_service.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';

/// خدمة النسخ الاحتياطي
class BackupService {
  /// إنشاء نسخة احتياطية
  static Future<File?> createBackup() async {
    try {
      final dbService = DatabaseService.instance;
      final dbPath = await dbService.database.then((db) => db.path);
      final dbFile = File(dbPath);

      // التحقق من وجود قاعدة البيانات
      if (!await dbFile.exists()) {
        AppLogger.error('قاعدة البيانات غير موجودة');
        return null;
      }

      // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجودًا
      final appDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory(path.join(appDir.path, 'backups'));
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      // إنشاء اسم ملف النسخة الاحتياطية
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final backupFileName = 'tajer_plus_backup_$timestamp.zip';
      final backupFilePath = path.join(backupDir.path, backupFileName);

      // قراءة محتوى قاعدة البيانات
      final dbBytes = await dbFile.readAsBytes();

      // إنشاء أرشيف ZIP
      final archive = Archive();

      // إضافة ملف قاعدة البيانات إلى الأرشيف
      final dbFileName = path.basename(dbPath);
      final archiveFile = ArchiveFile(dbFileName, dbBytes.length, dbBytes);
      archive.addFile(archiveFile);

      // إضافة ملف البيانات الوصفية
      final metadataContent = '''
      {
        "app_version": "1.0.0",
        "backup_date": "${DateTime.now().toIso8601String()}",
        "database_version": "1.0",
        "database_name": "$dbFileName"
      }
      ''';
      final metadataBytes = metadataContent.codeUnits;
      final metadataFile =
          ArchiveFile('metadata.json', metadataBytes.length, metadataBytes);
      archive.addFile(metadataFile);

      // ضغط الأرشيف وحفظه
      final zipEncoder = ZipEncoder();
      final zipData = zipEncoder.encode(archive);
      if (zipData.isEmpty) {
        AppLogger.error('فشل في ضغط الأرشيف');
        return null;
      }

      final backupFile = File(backupFilePath);
      await backupFile.writeAsBytes(zipData);

      AppLogger.info('تم إنشاء نسخة احتياطية بنجاح: $backupFilePath');
      return backupFile;
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في إنشاء نسخة احتياطية');
      ErrorTracker.trackError(e, stackTrace, 'BackupService.createBackup');
      return null;
    }
  }

  /// استعادة نسخة احتياطية
  static Future<bool> restoreBackup(File backupFile) async {
    try {
      final dbService = DatabaseService.instance;

      // فك ضغط الأرشيف
      final bytes = await backupFile.readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);

      // البحث عن ملف البيانات الوصفية
      final metadataFile = archive.findFile('metadata.json');
      if (metadataFile == null) {
        AppLogger.error('ملف البيانات الوصفية غير موجود في النسخة الاحتياطية');
        return false;
      }

      // البحث عن ملف قاعدة البيانات
      String? dbFileName;
      for (final file in archive.files) {
        if (file.name != 'metadata.json' && !file.isDirectory) {
          dbFileName = file.name;
          break;
        }
      }

      if (dbFileName == null) {
        AppLogger.error('ملف قاعدة البيانات غير موجود في النسخة الاحتياطية');
        return false;
      }

      final dbFile = archive.findFile(dbFileName);
      if (dbFile == null) {
        AppLogger.error('ملف قاعدة البيانات غير موجود في النسخة الاحتياطية');
        return false;
      }

      // إغلاق قاعدة البيانات الحالية
      final db = await dbService.database;
      await db.close();

      // الحصول على مسار قاعدة البيانات
      final dbPath = db.path;

      // حفظ ملف قاعدة البيانات المستعاد
      final outputFile = File(dbPath);
      await outputFile.writeAsBytes(dbFile.content as List<int>);

      AppLogger.info('تم استعادة النسخة الاحتياطية بنجاح');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في استعادة النسخة الاحتياطية');
      ErrorTracker.trackError(e, stackTrace, 'BackupService.restoreBackup');
      return false;
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية
  static Future<List<File>> getBackupFiles() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory(path.join(appDir.path, 'backups'));

      if (!await backupDir.exists()) {
        return [];
      }

      final files = await backupDir.list().toList();
      final backupFiles = files
          .whereType<File>()
          .where((file) => file.path.endsWith('.zip'))
          .toList();

      // ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
      backupFiles
          .sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      return backupFiles;
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في الحصول على قائمة النسخ الاحتياطية');
      ErrorTracker.trackError(e, stackTrace, 'BackupService.getBackupFiles');
      return [];
    }
  }

  /// حذف نسخة احتياطية
  static Future<bool> deleteBackup(File backupFile) async {
    try {
      if (await backupFile.exists()) {
        await backupFile.delete();
        AppLogger.info('تم حذف النسخة الاحتياطية بنجاح: ${backupFile.path}');
        return true;
      }
      return false;
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في حذف النسخة الاحتياطية');
      ErrorTracker.trackError(e, stackTrace, 'BackupService.deleteBackup');
      return false;
    }
  }

  /// حذف النسخ الاحتياطية القديمة
  static Future<int> deleteOldBackups({int keepCount = 5}) async {
    try {
      final backupFiles = await getBackupFiles();

      if (backupFiles.length <= keepCount) {
        return 0;
      }

      final filesToDelete = backupFiles.sublist(keepCount);
      int deletedCount = 0;

      for (final file in filesToDelete) {
        final success = await deleteBackup(file);
        if (success) {
          deletedCount++;
        }
      }

      AppLogger.info('تم حذف $deletedCount نسخة احتياطية قديمة');
      return deletedCount;
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في حذف النسخ الاحتياطية القديمة');
      ErrorTracker.trackError(e, stackTrace, 'BackupService.deleteOldBackups');
      return 0;
    }
  }
}
