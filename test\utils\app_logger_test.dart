import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/utils/app_logger.dart';

void main() {
  group('AppLogger Tests', () {
    test('AppLogger should log messages without errors', () {
      // اختبار تسجيل رسائل مختلفة
      expect(() => AppLogger.info('رسالة معلومات اختبارية'), returnsNormally);
      expect(() => AppLogger.warning('رسالة تحذير اختبارية'), returnsNormally);
      expect(() => AppLogger.error('رسالة خطأ اختبارية'), returnsNormally);
      expect(() => AppLogger.debug('رسالة تصحيح اختبارية'), returnsNormally);
    });

    test('AppLogger should handle null and empty messages', () {
      // اختبار تسجيل رسائل فارغة أو null
      expect(() => AppLogger.info(''), returnsNormally);
      expect(() => AppLogger.warning(''), returnsNormally);
      expect(() => AppLogger.error(''), returnsNormally);
      expect(() => AppLogger.debug(''), returnsNormally);
    });

    test('AppLogger should handle special characters', () {
      // اختبار تسجيل رسائل تحتوي على أحرف خاصة
      expect(() => AppLogger.info('رسالة تحتوي على أحرف خاصة: !@#\$%^&*()'), returnsNormally);
      expect(() => AppLogger.warning('رسالة تحتوي على أحرف خاصة: !@#\$%^&*()'), returnsNormally);
      expect(() => AppLogger.error('رسالة تحتوي على أحرف خاصة: !@#\$%^&*()'), returnsNormally);
      expect(() => AppLogger.debug('رسالة تحتوي على أحرف خاصة: !@#\$%^&*()'), returnsNormally);
    });

    test('AppLogger should handle long messages', () {
      // اختبار تسجيل رسائل طويلة
      final longMessage = 'رسالة طويلة جداً ' * 100;
      expect(() => AppLogger.info(longMessage), returnsNormally);
      expect(() => AppLogger.warning(longMessage), returnsNormally);
      expect(() => AppLogger.error(longMessage), returnsNormally);
      expect(() => AppLogger.debug(longMessage), returnsNormally);
    });
  });
}
