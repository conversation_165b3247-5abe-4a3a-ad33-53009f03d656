import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/utils/index.dart';

import '../../../core/widgets/index.dart';
import '../../accounts/presenters/account_presenter.dart';
import '../presenters/currency_presenter.dart';
import '../../../core/widgets/data_table_widget.dart';
import '../../../core/theme/index.dart';

// import '../../../core/models/currency.dart';

class CurrencyExchangeScreen extends StatefulWidget {
  const CurrencyExchangeScreen({Key? key}) : super(key: key);

  @override
  State<CurrencyExchangeScreen> createState() => _CurrencyExchangeScreenState();
}

class _CurrencyExchangeScreenState extends State<CurrencyExchangeScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _exchangeRateController = TextEditingController();
  final TextEditingController _resultController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  String? _selectedFromCurrencyId;
  String? _selectedToCurrencyId;
  String? _selectedAccountId;
  bool _isLoading = false;
  bool _isSaving = false;
  bool _isManualRate = false;

  // استخدام التحميل الكسول
  late final AccountPresenter _accountPresenter;
  late final CurrencyPresenter _currencyPresenter;

  @override
  void initState() {
    super.initState();

    // تهيئة التحميل الكسول
    _accountPresenter = AppProviders.getLazyPresenter<AccountPresenter>(
        () => AccountPresenter());
    _currencyPresenter = AppProviders.getLazyPresenter<CurrencyPresenter>(
        () => CurrencyPresenter());

    // Usar addPostFrameCallback para evitar llamar a setState durante la construcción
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });

    // إضافة مستمعين للتغييرات
    _amountController.addListener(_calculateResult);
    _exchangeRateController.addListener(_calculateResult);
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الحسابات
      await _accountPresenter.loadAccounts();

      // تحميل العملات
      await _currencyPresenter.loadCurrencies();

      if (!mounted) return;

      // تعيين العملات الافتراضية
      if (_currencyPresenter.currencies.length >= 2) {
        setState(() {
          _selectedFromCurrencyId = _currencyPresenter.currencies[0].id;
          _selectedToCurrencyId = _currencyPresenter.currencies[1].id;
        });
        _updateExchangeRate();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل البيانات: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _updateExchangeRate() {
    if (_selectedFromCurrencyId == null || _selectedToCurrencyId == null) {
      return;
    }

    final fromCurrency = _currencyPresenter.currencies
        .firstWhere((c) => c.id == _selectedFromCurrencyId);
    final toCurrency = _currencyPresenter.currencies
        .firstWhere((c) => c.id == _selectedToCurrencyId);

    // حساب سعر الصرف بين العملتين
    final rate = toCurrency.exchangeRate / fromCurrency.exchangeRate;

    setState(() {
      // Usar el formateador para mostrar el número con formato profesional
      _exchangeRateController.text =
          NumberFormatter.formatForInput(rate, decimalPlaces: 6);
    });

    _calculateResult();
  }

  void _calculateResult() {
    if (_amountController.text.isEmpty ||
        _exchangeRateController.text.isEmpty) {
      _resultController.text = '';
      return;
    }

    try {
      // Eliminar cualquier formato (comas, espacios) antes de parsear
      final String cleanAmount =
          _amountController.text.replaceAll(RegExp(r'[^\d.]'), '');
      final String cleanRate =
          _exchangeRateController.text.replaceAll(RegExp(r'[^\d.]'), '');

      final amount = double.parse(cleanAmount);
      final rate = double.parse(cleanRate);
      final result = amount * rate;

      // Usar el formateador para mostrar el resultado con formato profesional
      _resultController.text =
          NumberFormatter.formatNumber(result, decimalPlaces: 2);
    } catch (e) {
      _resultController.text = 'خطأ في الحساب';
    }
  }

  Future<void> _saveExchange() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من اختيار العملات والحساب
    if (_selectedFromCurrencyId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار العملة المصدر')),
      );
      return;
    }

    if (_selectedToCurrencyId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار العملة الهدف')),
      );
      return;
    }

    if (_selectedAccountId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار الحساب')),
      );
      return;
    }

    // عرض مربع حوار التأكيد
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد المصارفة'),
        content: const Text('هل تريد تنفيذ عملية مصارفة العملات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تنفيذ'),
          ),
        ],
      ),
    );

    if (confirm != true) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // هنا يمكن إضافة الكود الخاص بحفظ عملية المصارفة
      // مثلاً إنشاء سندات قبض وصرف أو قيود محاسبية

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تنفيذ عملية المصارفة بنجاح')),
        );
      }

      // إعادة تعيين النموذج
      _resetForm();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('فشل في تنفيذ عملية المصارفة: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _resetForm() {
    _amountController.clear();
    _resultController.clear();
    _notesController.clear();
    setState(() {
      _isManualRate = false;
      _updateExchangeRate();
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _exchangeRateController.dispose();
    _resultController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: Listenable.merge([_accountPresenter, _currencyPresenter]),
      builder: (context, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('مصارفة العملات'),
            backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          ),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      // نموذج المصارفة
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4.0, vertical: 4.0),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // العملات
                                Row(
                                  children: [
                                    // العملة المصدر
                                    Expanded(
                                      child: DropdownButtonFormField<String>(
                                        value: _selectedFromCurrencyId,
                                        decoration: InputDecoration(
                                          labelText: 'من العملة *',
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(
                                                Layout.defaultRadius),
                                          ),
                                          filled: true,
                                          prefixIcon: const Icon(
                                              Icons.currency_exchange),
                                        ),
                                        items: _currencyPresenter.currencies
                                            .map((currency) {
                                          return DropdownMenuItem<String>(
                                            value: currency.id,
                                            child: Text(
                                                '${currency.name} (${currency.symbol ?? ''})'),
                                          );
                                        }).toList(),
                                        onChanged: (value) {
                                          setState(() {
                                            _selectedFromCurrencyId = value;
                                            _updateExchangeRate();
                                          });
                                        },
                                        validator: (value) {
                                          if (value == null) {
                                            return 'يرجى اختيار العملة المصدر';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    // العملة الهدف
                                    Expanded(
                                      child: DropdownButtonFormField<String>(
                                        value: _selectedToCurrencyId,
                                        decoration: InputDecoration(
                                          labelText: 'إلى العملة *',
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(
                                                Layout.defaultRadius),
                                          ),
                                          filled: true,
                                          prefixIcon: const Icon(
                                              Icons.currency_exchange),
                                        ),
                                        items: _currencyPresenter.currencies
                                            .map((currency) {
                                          return DropdownMenuItem<String>(
                                            value: currency.id,
                                            child: Text(
                                                '${currency.name} (${currency.symbol ?? ''})'),
                                          );
                                        }).toList(),
                                        onChanged: (value) {
                                          setState(() {
                                            _selectedToCurrencyId = value;
                                            _updateExchangeRate();
                                          });
                                        },
                                        validator: (value) {
                                          if (value == null) {
                                            return 'يرجى اختيار العملة الهدف';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: AppDimensions.spacing4),

                                // المبلغ وسعر الصرف
                                Row(
                                  children: [
                                    // المبلغ
                                    Expanded(
                                      child: FinancialTextField(
                                        controller: _amountController,
                                        label: 'المبلغ',
                                        hint: 'أدخل المبلغ',
                                        isRequired: true,
                                        prefixIcon: Icons.attach_money,
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'يرجى إدخال المبلغ';
                                          }
                                          if (double.tryParse(value.replaceAll(
                                                  RegExp(r'[^\d.]'), '')) ==
                                              null) {
                                            return 'يرجى إدخال رقم صحيح';
                                          }
                                          if (double.parse(value.replaceAll(
                                                  RegExp(r'[^\d.]'), '')) <=
                                              0) {
                                            return 'يجب أن يكون المبلغ أكبر من صفر';
                                          }
                                          return null;
                                        },
                                        onChanged: (_) => _calculateResult(),
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    // سعر الصرف
                                    Expanded(
                                      child: FinancialTextField(
                                        controller: _exchangeRateController,
                                        label: 'سعر الصرف',
                                        hint: 'سعر الصرف',
                                        isRequired: true,
                                        readOnly: !_isManualRate,
                                        decimalPlaces: 6,
                                        prefixIcon: Icons.currency_exchange,
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'يرجى إدخال سعر الصرف';
                                          }
                                          if (double.tryParse(value.replaceAll(
                                                  RegExp(r'[^\d.]'), '')) ==
                                              null) {
                                            return 'يرجى إدخال رقم صحيح';
                                          }
                                          if (double.parse(value.replaceAll(
                                                  RegExp(r'[^\d.]'), '')) <=
                                              0) {
                                            return 'يجب أن يكون سعر الصرف أكبر من صفر';
                                          }
                                          return null;
                                        },
                                        suffixIcon: _isManualRate
                                            ? Icons.lock_open
                                            : Icons.lock,
                                        onSuffixIconPressed: () {
                                          setState(() {
                                            _isManualRate = !_isManualRate;
                                            if (!_isManualRate) {
                                              _updateExchangeRate();
                                            }
                                          });
                                        },
                                        onChanged: (_) => _calculateResult(),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: AppDimensions.spacing4),

                                // المبلغ المحول
                                FinancialTextField(
                                  controller: _resultController,
                                  label: 'المبلغ المحول',
                                  hint: 'المبلغ بعد التحويل',
                                  readOnly: true,
                                  prefixIcon: Icons.calculate,
                                ),
                                const SizedBox(height: AppDimensions.spacing4),

                                // الحساب
                                DropdownButtonFormField<String>(
                                  value: _selectedAccountId,
                                  decoration: InputDecoration(
                                    labelText: 'الحساب *',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          Layout.defaultRadius),
                                    ),
                                    filled: true,
                                    prefixIcon:
                                        const Icon(Icons.account_balance),
                                  ),
                                  items:
                                      _accountPresenter.accounts.map((account) {
                                    return DropdownMenuItem<String>(
                                      value: account['id'].toString(),
                                      child: Text(account['name']),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedAccountId = value;
                                    });
                                  },
                                  validator: (value) {
                                    if (value == null) {
                                      return 'يرجى اختيار الحساب';
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: AppDimensions.spacing4),

                                // الملاحظات
                                TextFormField(
                                  controller: _notesController,
                                  decoration: const InputDecoration(
                                    labelText: 'ملاحظات',
                                    hintText: 'أدخل ملاحظات إضافية (اختياري)',
                                    border: OutlineInputBorder(),
                                    prefixIcon: Icon(Icons.note),
                                  ),
                                  maxLines: 2,
                                ),
                                const SizedBox(height: AppDimensions.spacing4),

                                // زر التنفيذ
                                Center(
                                  child: SizedBox(
                                    width: Layout.isTablet()
                                        ? 200
                                        : double.infinity,
                                    height: 45,
                                    child: ElevatedButton.icon(
                                      onPressed:
                                          _isSaving ? null : _saveExchange,
                                      icon: _isSaving
                                          ? const SizedBox(
                                              width: 18,
                                              height: 18,
                                              child: CircularProgressIndicator(
                                                  color: AppColors
                                                      .lightTextSecondary,
                                                  strokeWidth: 2))
                                          : const Icon(Icons.currency_exchange,
                                              size: 20),
                                      label: const Text(
                                        'تنفيذ المصارفة',
                                        style: AppTypography(fontSize: 15),
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: AppColors.primary,
                                        foregroundColor: AppColors.onPrimary,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: AppDimensions.spacing16),

                      // جدول أسعار العملات
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4.0, vertical: 4.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 4.0, vertical: 2.0),
                                child: Text(
                                  'أسعار العملات',
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                ),
                              ),
                              AdvancedDataTable(
                                columns: const [
                                  DataColumn(label: Text('العملة')),
                                  DataColumn(label: Text('الرمز')),
                                  DataColumn(label: Text('سعر الصرف')),
                                  DataColumn(label: Text('الحالة')),
                                ],
                                rows: _currencyPresenter.currencies
                                    .map((currency) {
                                  return DataRow(
                                    cells: [
                                      DataCell(Text(currency.name)),
                                      DataCell(Text(currency.symbol ?? '')),
                                      DataCell(Text(
                                          NumberFormatter.formatNumber(
                                              currency.exchangeRate,
                                              decimalPlaces: 6,
                                              showThousandsSeparator: false))),
                                      DataCell(
                                        Row(
                                          children: [
                                            Icon(
                                              currency.isDefault
                                                  ? Icons.star
                                                  : Icons.circle,
                                              color: currency.isDefault
                                                  ? AppColors.amber
                                                  : currency.isActive
                                                      ? AppColors.success
                                                      : AppColors.error,
                                              size: 16,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              currency.isDefault
                                                  ? 'افتراضية'
                                                  : currency.isActive
                                                      ? 'نشطة'
                                                      : 'غير نشطة',
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                }).toList(),
                                isLoading: _currencyPresenter.isLoading,
                                showCellBorder: true,
                                zebraPattern: true,
                                headerBackgroundColor: AppColors.primary,
                                headerTextColor: AppColors.onPrimary,
                                showRowNumbers: false,
                                emptyMessage: 'لا توجد عملات',
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
        );
      },
    );
  }
}
