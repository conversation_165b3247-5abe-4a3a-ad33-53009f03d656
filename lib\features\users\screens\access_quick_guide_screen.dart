import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';

/// شاشة الدليل السريع لنظام إدارة الوصول
/// تشرح بشكل مبسط كيفية استخدام نظام إدارة الوصول
class AccessQuickGuideScreen extends StatelessWidget {
  static const String routeName = '/access-quick-guide';

  const AccessQuickGuideScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الدليل السريع لإدارة الوصول'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // مقدمة
          _buildSection(
            title: 'ما هو نظام إدارة الوصول؟',
            content:
                'نظام إدارة الوصول يتيح لك تحديد ما يمكن لكل مستخدم الوصول إليه في التطبيق. '
                'يمكنك إنشاء مستويات وصول مختلفة وتعيينها للمستخدمين حسب وظائفهم.',
            icon: Icons.security,
          ),

          // مستويات الوصول
          _buildSection(
            title: 'مستويات الوصول',
            content:
                'مستوى الوصول هو مجموعة من الصلاحيات التي تحدد ما يمكن للمستخدم فعله في التطبيق. '
                'على سبيل المثال، يمكنك إنشاء مستوى وصول "مدير مبيعات" يتيح للمستخدم إدارة المبيعات والعملاء.',
            icon: Icons.admin_panel_settings,
          ),

          // أنواع الوصول
          _buildSection(
            title: 'أنواع الوصول',
            content: 'هناك أربعة أنواع من الوصول لكل وظيفة في النظام:',
            icon: Icons.category,
            children: [
              _buildAccessTypeItem(
                type: 'لا وصول',
                description: 'لا يمكن للمستخدم الوصول إلى هذه الوظيفة نهائياً.',
                color: AppColors.lightTextSecondary,
                icon: Icons.block,
              ),
              _buildAccessTypeItem(
                type: 'عرض فقط',
                description: 'يمكن للمستخدم عرض البيانات فقط دون تعديلها.',
                color: AppColors.lightTextSecondary,
                icon: Icons.visibility,
              ),
              _buildAccessTypeItem(
                type: 'تعديل',
                description: 'يمكن للمستخدم عرض وتعديل البيانات دون حذفها.',
                color: AppColors.lightTextSecondary,
                icon: Icons.edit,
              ),
              _buildAccessTypeItem(
                type: 'وصول كامل',
                description: 'يمكن للمستخدم عرض وتعديل وحذف البيانات.',
                color: AppColors.lightTextSecondary,
                icon: Icons.admin_panel_settings,
              ),
            ],
          ),

          // الوظائف الرئيسية
          _buildSection(
            title: 'الوظائف الرئيسية',
            content: 'يتكون النظام من الوظائف الرئيسية التالية:',
            icon: Icons.work,
            children: [
              _buildJobFunctionItem(
                name: 'المبيعات',
                description: 'إدارة المبيعات والفواتير والعملاء',
                color: AppColors.lightTextSecondary,
                icon: Icons.point_of_sale,
              ),
              _buildJobFunctionItem(
                name: 'المخزون',
                description: 'إدارة المنتجات والمخزون والمستودعات',
                color: AppColors.lightTextSecondary,
                icon: Icons.inventory_2,
              ),
              _buildJobFunctionItem(
                name: 'المشتريات',
                description: 'إدارة المشتريات والموردين',
                color: AppColors.lightTextSecondary,
                icon: Icons.shopping_cart,
              ),
              _buildJobFunctionItem(
                name: 'المالية',
                description: 'إدارة الحسابات والمصروفات والإيرادات',
                color: AppColors.lightTextSecondary,
                icon: Icons.account_balance,
              ),
              _buildJobFunctionItem(
                name: 'التقارير',
                description: 'عرض وطباعة التقارير المختلفة',
                color: AppColors.lightTextSecondary,
                icon: Icons.bar_chart,
              ),
              _buildJobFunctionItem(
                name: 'الإعدادات',
                description: 'إدارة إعدادات النظام والمستخدمين',
                color: AppColors.lightTextSecondary,
                icon: Icons.settings,
              ),
            ],
          ),

          // كيفية إنشاء مستوى وصول جديد
          _buildSection(
            title: 'كيفية إنشاء مستوى وصول جديد',
            content: 'لإنشاء مستوى وصول جديد، اتبع الخطوات التالية:',
            icon: Icons.add_circle,
            children: [
              _buildStepItem(
                step: 1,
                description: 'انتقل إلى شاشة "إدارة الوصول"',
              ),
              _buildStepItem(
                step: 2,
                description: 'اضغط على زر "إضافة مستوى وصول جديد"',
              ),
              _buildStepItem(
                step: 3,
                description: 'أدخل اسم مستوى الوصول (مثل "مدير مبيعات")',
              ),
              _buildStepItem(
                step: 4,
                description:
                    'حدد مستوى الوصول لكل وظيفة (لا وصول، عرض فقط، تعديل، وصول كامل)',
              ),
              _buildStepItem(
                step: 5,
                description: 'اضغط على زر "حفظ"',
              ),
            ],
          ),

          // كيفية تعيين مستوى وصول لمستخدم
          _buildSection(
            title: 'كيفية تعيين مستوى وصول لمستخدم',
            content: 'لتعيين مستوى وصول لمستخدم، اتبع الخطوات التالية:',
            icon: Icons.person_add,
            children: [
              _buildStepItem(
                step: 1,
                description: 'انتقل إلى شاشة "وصول المستخدمين"',
              ),
              _buildStepItem(
                step: 2,
                description: 'ابحث عن المستخدم المطلوب',
              ),
              _buildStepItem(
                step: 3,
                description: 'اختر مستوى الوصول المناسب من القائمة المنسدلة',
              ),
              _buildStepItem(
                step: 4,
                description: 'سيتم حفظ التغييرات تلقائياً',
              ),
            ],
          ),

          // نصائح وإرشادات
          _buildSection(
            title: 'نصائح وإرشادات',
            content:
                'إليك بعض النصائح والإرشادات لاستخدام نظام إدارة الوصول بشكل فعال:',
            icon: Icons.lightbulb,
            children: [
              _buildTipItem(
                tip: 'قم بإنشاء مستويات وصول محددة لكل وظيفة في مؤسستك.',
              ),
              _buildTipItem(
                tip:
                    'امنح المستخدمين الحد الأدنى من الصلاحيات التي يحتاجونها لأداء وظائفهم.',
              ),
              _buildTipItem(
                tip:
                    'راجع مستويات الوصول بشكل دوري للتأكد من أنها لا تزال مناسبة.',
              ),
              _buildTipItem(
                tip:
                    'قم بتعطيل حسابات المستخدمين الذين لم يعودوا يعملون في المؤسسة.',
              ),
            ],
          ),

          // زر العودة
          Container(
            margin: const EdgeInsets.symmetric(vertical: 24),
            child: ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back),
              label: const Text('العودة إلى إدارة الوصول'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم
  Widget _buildSection({
    required String title,
    required String content,
    required IconData icon,
    List<Widget>? children,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: AppColors.lightTextSecondary,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.lightTextPrimary.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.12),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.lightTextSecondary,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const AppTypography(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // محتوى القسم
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  content,
                  style: const AppTypography(
                    fontSize: 16,
                  ),
                ),
                if (children != null) ...[
                  const SizedBox(height: 16),
                  ...children,
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر نوع الوصول
  Widget _buildAccessTypeItem({
    required String type,
    required String description,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.12),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: color,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type,
                  style: AppTypography(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  description,
                  style: const AppTypography(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر الوظيفة
  Widget _buildJobFunctionItem({
    required String name,
    required String description,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.12),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: color,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: AppTypography(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  description,
                  style: const AppTypography(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر الخطوة
  Widget _buildStepItem({
    required int step,
    required String description,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.lightTextSecondary,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: const BoxDecoration(
              color: AppColors.lightTextSecondary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                step.toString(),
                style: const AppTypography(
                  color: AppColors.lightTextSecondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              description,
              style: const AppTypography(
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر النصيحة
  Widget _buildTipItem({
    required String tip,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.amber.withValues(alpha: 0.12),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.lightbulb,
            color: AppColors.lightTextSecondary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const AppTypography(
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
