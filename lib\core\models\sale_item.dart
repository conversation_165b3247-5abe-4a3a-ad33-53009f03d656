import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج عنصر المبيعات الموحد
/// تم توحيده من جميع نماذج عناصر المبيعات في المشروع
class SaleItem extends BaseModel {
  final String? saleId;
  final String productId;
  final String? productName;
  final String? productCode;
  final String? unitId;
  final String? unitName;
  final double quantity;
  final double price;
  final double cost;
  final double discount;
  final bool isDiscountPercentage;
  final double tax;
  final bool isTaxPercentage;
  final double subtotal;
  final double total;
  final Map<String, dynamic>? metadata;

  SaleItem({
    String? id,
    this.saleId,
    required this.productId,
    this.productName,
    this.productCode,
    this.unitId,
    this.unitName,
    required this.quantity,
    required this.price,
    this.cost = 0.0,
    this.discount = 0.0,
    this.isDiscountPercentage = false,
    this.tax = 0.0,
    this.isTaxPercentage = false,
    double? subtotal,
    double? total,
    this.metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  })  : subtotal = subtotal ?? (quantity * price),
        total = total ??
            _calculateTotal(
              quantity,
              price,
              discount,
              isDiscountPercentage,
              tax,
              isTaxPercentage,
            ),
        super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// حساب إجمالي العنصر
  static double _calculateTotal(
    double quantity,
    double price,
    double discount,
    bool isDiscountPercentage,
    double tax,
    bool isTaxPercentage,
  ) {
    final subtotal = quantity * price;
    final discountAmount =
        isDiscountPercentage ? subtotal * (discount / 100) : discount;
    final afterDiscount = subtotal - discountAmount;
    final taxAmount = isTaxPercentage ? afterDiscount * (tax / 100) : tax;
    return afterDiscount + taxAmount;
  }

  /// إنشاء نسخة من هذا العنصر مع استبدال الحقول المحددة بقيم جديدة
  SaleItem copyWith({
    String? id,
    String? saleId,
    String? productId,
    String? productName,
    String? productCode,
    String? unitId,
    String? unitName,
    double? quantity,
    double? price,
    double? cost,
    double? discount,
    bool? isDiscountPercentage,
    double? tax,
    bool? isTaxPercentage,
    double? subtotal,
    double? total,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return SaleItem(
      id: id ?? this.id,
      saleId: saleId ?? this.saleId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      unitId: unitId ?? this.unitId,
      unitName: unitName ?? this.unitName,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      cost: cost ?? this.cost,
      discount: discount ?? this.discount,
      isDiscountPercentage: isDiscountPercentage ?? this.isDiscountPercentage,
      tax: tax ?? this.tax,
      isTaxPercentage: isTaxPercentage ?? this.isTaxPercentage,
      subtotal: subtotal ?? this.subtotal,
      total: total ?? this.total,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل عنصر المبيعات إلى Map
  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    return {
      ...map,
      'sale_id': saleId,
      'product_id': productId,
      'product_name': productName,
      'product_code': productCode,
      'unit_id': unitId,
      'unit_name': unitName,
      'quantity': quantity,
      'price': price,
      'cost': cost,
      'discount': discount,
      'is_discount_percentage': isDiscountPercentage ? 1 : 0,
      'tax': tax,
      'is_tax_percentage': isTaxPercentage ? 1 : 0,
      'subtotal': subtotal,
      'total': total,
      'metadata': metadata != null ? jsonEncode(metadata) : null,
    };
  }

  /// إنشاء عنصر مبيعات من Map
  factory SaleItem.fromMap(Map<String, dynamic> map) {
    return SaleItem(
      id: map['id'],
      saleId: map['sale_id'],
      productId: map['product_id'] ?? '',
      productName: map['product_name'],
      productCode: map['product_code'],
      unitId: map['unit_id'],
      unitName: map['unit_name'],
      quantity: map['quantity'] is int
          ? (map['quantity'] as int).toDouble()
          : (map['quantity'] as double? ?? 0.0),
      price: map['price'] is int
          ? (map['price'] as int).toDouble()
          : (map['price'] as double? ?? 0.0),
      cost: map['cost'] is int
          ? (map['cost'] as int).toDouble()
          : (map['cost'] as double? ?? 0.0),
      discount: map['discount'] is int
          ? (map['discount'] as int).toDouble()
          : (map['discount'] as double? ?? 0.0),
      isDiscountPercentage: map['is_discount_percentage'] == 1,
      tax: map['tax'] is int
          ? (map['tax'] as int).toDouble()
          : (map['tax'] as double? ?? 0.0),
      isTaxPercentage: map['is_tax_percentage'] == 1,
      subtotal: map['subtotal'] is int
          ? (map['subtotal'] as int).toDouble()
          : (map['subtotal'] as double? ?? 0.0),
      total: map['total'] is int
          ? (map['total'] as int).toDouble()
          : (map['total'] as double? ?? 0.0),
      metadata: map['metadata'] != null
          ? jsonDecode(map['metadata']) as Map<String, dynamic>
          : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل عنصر المبيعات إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء عنصر مبيعات من JSON
  factory SaleItem.fromJson(String source) =>
      SaleItem.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'SaleItem(id: $id, productId: $productId, productName: $productName, quantity: $quantity, price: $price, total: $total)';
  }
}
