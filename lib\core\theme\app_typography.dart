import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'dynamic_colors.dart';

/// نظام الطباعة الموحد لتطبيق تاجر بلس
/// يحتوي على جميع أنماط النصوص المستخدمة في التطبيق
class AppTypography extends TextStyle {
  /// Constructor للكلاس AppTypography مع دعم محسن للخطوط العربية
  const AppTypography({
    super.inherit = true,
    super.color,
    super.backgroundColor,
    super.fontSize,
    super.fontWeight,
    super.fontStyle,
    super.letterSpacing,
    super.wordSpacing,
    super.textBaseline,
    super.height,
    super.leadingDistribution,
    super.locale,
    super.foreground,
    super.background,
    super.shadows,
    super.fontFeatures,
    super.fontVariations,
    super.decoration,
    super.decorationColor,
    super.decorationStyle,
    super.decorationThickness,
    super.debugLabel,
    String? fontFamily,
    List<String>? fontFamilyFallback,
    String? package,
  }) : super(
          fontFamily: fontFamily ?? primaryFontFamily,
          fontFamilyFallback: fontFamilyFallback ?? arabicFontFallback,
          package: package,
        );

  // ========== الخطوط الأساسية ==========

  /// الخط الأساسي للتطبيق - يدعم العربية بشكل ممتاز
  static const String primaryFontFamily = 'Cairo';
  static const String secondaryFontFamily = 'Roboto';

  /// قائمة الخطوط الاحتياطية للنصوص العربية - تحسين دعم Impeller
  static const List<String> arabicFontFallback = [
    'Cairo',
    'Noto Sans Arabic',
    'Roboto',
    'sans-serif',
  ];

  /// قائمة الخطوط الاحتياطية للنصوص الإنجليزية
  static const List<String> englishFontFallback = [
    'Roboto',
    'Cairo',
    'sans-serif',
  ];

  // ========== أحجام الخطوط ==========

  static const double fontSizeXXLarge = 32.0;
  static const double fontSizeXLarge = 28.0;
  static const double fontSizeLarge = 24.0;
  static const double fontSizeMediumLarge = 20.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeSmallMedium = 14.0;
  static const double fontSizeSmall = 12.0;
  static const double fontSizeXSmall = 10.0;

  // ========== أوزان الخطوط ==========

  static const FontWeight weightLight = FontWeight.w300;
  static const FontWeight weightRegular = FontWeight.w400;
  static const FontWeight weightMedium = FontWeight.w500;
  static const FontWeight weightSemiBold = FontWeight.w600;
  static const FontWeight weightBold = FontWeight.w700;
  static const FontWeight weightExtraBold = FontWeight.w800;

  // ========== نمط النصوص للوضع الفاتح ==========

  static TextTheme get lightTextTheme => const TextTheme(
        // العناوين الرئيسية
        displayLarge: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeXXLarge,
          fontWeight: weightBold,
          color: AppColors.lightTextPrimary,
          height: 1.2,
          letterSpacing: -0.5,
        ),
        displayMedium: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeXLarge,
          fontWeight: weightBold,
          color: AppColors.lightTextPrimary,
          height: 1.3,
          letterSpacing: -0.25,
        ),
        displaySmall: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeLarge,
          fontWeight: weightSemiBold,
          color: AppColors.lightTextPrimary,
          height: 1.3,
        ),

        // العناوين الفرعية
        headlineLarge: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeMediumLarge,
          fontWeight: weightSemiBold,
          color: AppColors.lightTextPrimary,
          height: 1.4,
        ),
        headlineMedium: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeMedium,
          fontWeight: weightSemiBold,
          color: AppColors.lightTextPrimary,
          height: 1.4,
        ),
        headlineSmall: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeSmallMedium,
          fontWeight: weightMedium,
          color: AppColors.lightTextPrimary,
          height: 1.4,
        ),

        // عناوين الأقسام
        titleLarge: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeMedium,
          fontWeight: weightMedium,
          color: AppColors.lightTextPrimary,
          height: 1.5,
        ),
        titleMedium: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeSmallMedium,
          fontWeight: weightMedium,
          color: AppColors.lightTextPrimary,
          height: 1.5,
        ),
        titleSmall: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeSmall,
          fontWeight: weightMedium,
          color: AppColors.lightTextSecondary,
          height: 1.5,
        ),

        // النصوص العادية
        bodyLarge: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeMedium,
          fontWeight: weightRegular,
          color: AppColors.lightTextPrimary,
          height: 1.6,
        ),
        bodyMedium: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeSmallMedium,
          fontWeight: weightRegular,
          color: AppColors.lightTextPrimary,
          height: 1.6,
        ),
        bodySmall: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeSmall,
          fontWeight: weightRegular,
          color: AppColors.lightTextSecondary,
          height: 1.5,
        ),

        // التسميات والأزرار
        labelLarge: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeSmallMedium,
          fontWeight: weightMedium,
          color: AppColors.lightTextPrimary,
          height: 1.4,
          letterSpacing: 0.1,
        ),
        labelMedium: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeSmall,
          fontWeight: weightMedium,
          color: AppColors.lightTextPrimary,
          height: 1.4,
          letterSpacing: 0.5,
        ),
        labelSmall: AppTypography(
          fontFamily: primaryFontFamily,
          fontSize: fontSizeXSmall,
          fontWeight: weightMedium,
          color: AppColors.lightTextSecondary,
          height: 1.4,
          letterSpacing: 0.5,
        ),
      );

  // ========== نمط النصوص للوضع الداكن ==========

  static TextTheme get darkTextTheme => lightTextTheme.copyWith(
        displayLarge: lightTextTheme.displayLarge?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        displayMedium: lightTextTheme.displayMedium?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        displaySmall: lightTextTheme.displaySmall?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        headlineLarge: lightTextTheme.headlineLarge?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        headlineMedium: lightTextTheme.headlineMedium?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        headlineSmall: lightTextTheme.headlineSmall?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        titleLarge: lightTextTheme.titleLarge?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        titleMedium: lightTextTheme.titleMedium?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        titleSmall: lightTextTheme.titleSmall?.copyWith(
          color: AppColors.darkTextSecondary,
        ),
        bodyLarge: lightTextTheme.bodyLarge?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        bodyMedium: lightTextTheme.bodyMedium?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        bodySmall: lightTextTheme.bodySmall?.copyWith(
          color: AppColors.darkTextSecondary,
        ),
        labelLarge: lightTextTheme.labelLarge?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        labelMedium: lightTextTheme.labelMedium?.copyWith(
          color: AppColors.darkTextPrimary,
        ),
        labelSmall: lightTextTheme.labelSmall?.copyWith(
          color: AppColors.darkTextSecondary,
        ),
      );

  // ========== أنماط نصوص خاصة ==========

  /// نمط نص للأرقام والمبالغ المالية
  static AppTypography get currencyStyle => AppTypography(
        fontFamily: secondaryFontFamily,
        fontSize: fontSizeMediumLarge,
        fontWeight: weightBold,
        color: DynamicColors.primary,
        letterSpacing: 0.5,
      );

  /// نمط نص للأرقام الكبيرة (الإحصائيات)
  static AppTypography get statisticsStyle => AppTypography(
        fontFamily: secondaryFontFamily,
        fontSize: fontSizeXLarge,
        fontWeight: weightExtraBold,
        color: DynamicColors.primary,
        letterSpacing: -0.5,
      );

  /// نمط نص للحالات الإيجابية (أرباح)
  static const AppTypography positiveStyle = AppTypography(
    fontFamily: primaryFontFamily,
    fontSize: fontSizeMedium,
    fontWeight: weightSemiBold,
    color: AppColors.success,
  );

  /// نمط نص للحالات السلبية (خسائر)
  static const AppTypography negativeStyle = AppTypography(
    fontFamily: primaryFontFamily,
    fontSize: fontSizeMedium,
    fontWeight: weightSemiBold,
    color: AppColors.error,
  );

  /// نمط نص للتحذيرات
  static const AppTypography warningStyle = AppTypography(
    fontFamily: primaryFontFamily,
    fontSize: fontSizeSmallMedium,
    fontWeight: weightMedium,
    color: AppColors.warning,
  );

  /// نمط نص للمعلومات
  static const AppTypography infoStyle = AppTypography(
    fontFamily: primaryFontFamily,
    fontSize: fontSizeSmallMedium,
    fontWeight: weightMedium,
    color: AppColors.info,
  );

  // ========== دوال مساعدة ==========

  /// إنشاء نمط نص مخصص
  static AppTypography createCustomStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    String? fontFamily,
    double? height,
    double? letterSpacing,
  }) {
    return AppTypography(
      fontFamily: fontFamily ?? primaryFontFamily,
      fontSize: fontSize ?? fontSizeMedium,
      fontWeight: fontWeight ?? weightRegular,
      color: color ?? DynamicColors.primary,
      height: height ?? 1.5,
      letterSpacing: letterSpacing,
    );
  }

  /// إنشاء نمط نص محسن للنصوص العربية مع دعم Impeller
  static TextStyle createArabicOptimizedStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
  }) {
    return TextStyle(
      fontFamily: primaryFontFamily,
      fontFamilyFallback: arabicFontFallback,
      fontSize: fontSize ?? fontSizeMedium,
      fontWeight: fontWeight ?? weightRegular,
      color: color ?? DynamicColors.primary,
      height: height ?? 1.5,
      letterSpacing: letterSpacing,
      locale: const Locale('ar'),
      // تحسينات خاصة لـ Impeller
      textBaseline: TextBaseline.alphabetic,
      leadingDistribution: TextLeadingDistribution.even,
    );
  }

  /// تطبيق لون على نمط نص موجود
  static AppTypography applyColor(AppTypography style, Color color) {
    return AppTypography(
      fontSize: style.fontSize,
      fontWeight: style.fontWeight,
      fontStyle: style.fontStyle,
      letterSpacing: style.letterSpacing,
      wordSpacing: style.wordSpacing,
      textBaseline: style.textBaseline,
      height: style.height,
      decoration: style.decoration,
      decorationColor: style.decorationColor,
      decorationStyle: style.decorationStyle,
      decorationThickness: style.decorationThickness,
      fontFamily: style.fontFamily,
      color: color,
    );
  }

  /// تطبيق حجم خط على نمط نص موجود
  static AppTypography applyFontSize(AppTypography style, double fontSize) {
    return AppTypography(
      fontSize: fontSize,
      fontWeight: style.fontWeight,
      fontStyle: style.fontStyle,
      letterSpacing: style.letterSpacing,
      wordSpacing: style.wordSpacing,
      textBaseline: style.textBaseline,
      height: style.height,
      decoration: style.decoration,
      decorationColor: style.decorationColor,
      decorationStyle: style.decorationStyle,
      decorationThickness: style.decorationThickness,
      fontFamily: style.fontFamily,
      color: style.color,
    );
  }

  /// تطبيق وزن خط على نمط نص موجود
  static AppTypography applyFontWeight(
      AppTypography style, FontWeight fontWeight) {
    return AppTypography(
      fontSize: style.fontSize,
      fontWeight: fontWeight,
      fontStyle: style.fontStyle,
      letterSpacing: style.letterSpacing,
      wordSpacing: style.wordSpacing,
      textBaseline: style.textBaseline,
      height: style.height,
      decoration: style.decoration,
      decorationColor: style.decorationColor,
      decorationStyle: style.decorationStyle,
      decorationThickness: style.decorationThickness,
      fontFamily: style.fontFamily,
      color: style.color,
    );
  }
}
