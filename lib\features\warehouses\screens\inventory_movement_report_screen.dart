import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

import '../presenters/inventory_presenter.dart';

import '../../../core/models/inventory_transaction.dart';
import '../../../core/models/product.dart';
import '../../products/presenters/product_presenter.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

/// شاشة تقرير حركة المخزون
///
/// تعرض هذه الشاشة تقرير حركة المخزون مع رسوم بيانية لتوضيح حركة المخزون عبر الزمن
class InventoryMovementReportScreen extends StatefulWidget {
  final String? productId;
  final String? warehouseId;

  const InventoryMovementReportScreen({
    Key? key,
    this.productId,
    this.warehouseId,
  }) : super(key: key);

  @override
  State<InventoryMovementReportScreen> createState() =>
      _InventoryMovementReportScreenState();
}

class _InventoryMovementReportScreenState
    extends State<InventoryMovementReportScreen> {
  bool _isLoading = false;
  List<InventoryTransaction> _transactions = [];
  Product? _selectedProduct;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  // بيانات الرسم البياني
  List<FlSpot> _inSpots = [];
  List<FlSpot> _outSpots = [];
  double _maxY = 0;

  // استخدام التحميل الكسول
  late final InventoryPresenter _inventoryPresenter;
  late final ProductPresenter _productPresenter;

  @override
  void initState() {
    super.initState();
    _inventoryPresenter = AppProviders.getLazyPresenter<InventoryPresenter>(
        () => InventoryPresenter());
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
        () => ProductPresenter());
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل المنتج المحدد إذا كان متاحاً
      if (widget.productId != null) {
        _selectedProduct = _productPresenter.getProductById(widget.productId!);
      }

      // تحميل حركات المخزون
      await _inventoryPresenter.loadProductTransactions(
        widget.productId ?? '',
        warehouseId: widget.warehouseId,
      );

      _transactions = _inventoryPresenter.transactions;

      // تحضير بيانات الرسم البياني
      _prepareChartData();
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _prepareChartData() {
    // فلترة الحركات حسب التاريخ
    final filteredTransactions = _transactions.where((transaction) {
      return transaction.transactionDate.isAfter(_startDate) &&
          transaction.transactionDate
              .isBefore(_endDate.add(const Duration(days: 1)));
    }).toList();

    // تجميع الحركات حسب اليوم
    final Map<DateTime, double> inTransactions = {};
    final Map<DateTime, double> outTransactions = {};

    for (var transaction in filteredTransactions) {
      // تنسيق التاريخ ليكون فقط اليوم (بدون الوقت)
      final date = DateTime(
        transaction.transactionDate.year,
        transaction.transactionDate.month,
        transaction.transactionDate.day,
      );

      if (transaction.transactionType == InventoryTransactionType.purchase ||
          transaction.transactionType == InventoryTransactionType.returnIn ||
          (transaction.transactionType == InventoryTransactionType.adjustment &&
              transaction.quantity > 0) ||
          (transaction.transactionType == InventoryTransactionType.transfer &&
              transaction.quantity > 0)) {
        // حركات الإضافة
        inTransactions[date] =
            (inTransactions[date] ?? 0) + transaction.quantity;
      } else {
        // حركات الخصم
        outTransactions[date] =
            (outTransactions[date] ?? 0) + transaction.quantity;
      }
    }

    // إنشاء نقاط الرسم البياني
    _inSpots = [];
    _outSpots = [];
    _maxY = 0;

    // إنشاء قائمة بجميع الأيام بين تاريخ البداية وتاريخ النهاية
    final allDates = <DateTime>[];
    for (var date = _startDate;
        date.isBefore(_endDate.add(const Duration(days: 1)));
        date = date.add(const Duration(days: 1))) {
      allDates.add(DateTime(date.year, date.month, date.day));
    }

    // إنشاء نقاط الرسم البياني لكل يوم
    for (int i = 0; i < allDates.length; i++) {
      final date = allDates[i];
      final inValue = inTransactions[date] ?? 0;
      final outValue = outTransactions[date] ?? 0;

      _inSpots.add(FlSpot(i.toDouble(), inValue));
      _outSpots.add(FlSpot(i.toDouble(), outValue));

      _maxY = _maxY < inValue ? inValue : _maxY;
      _maxY = _maxY < outValue ? outValue : _maxY;
    }

    // إضافة هامش للقيمة القصوى
    _maxY = _maxY * 1.2;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير حركة المخزون'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildReportContent(),
    );
  }

  Widget _buildReportContent() {
    if (_transactions.isEmpty) {
      return const Center(
        child: Text('لا توجد حركات مخزون للعرض'),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDateRangeSelector(),
            const SizedBox(height: 20),
            _buildSummaryCard(),
            const SizedBox(height: 20),
            _buildMovementChart(),
            const SizedBox(height: 20),
            _buildTransactionsTable(),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نطاق التاريخ',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDateSelector(
                    label: 'من',
                    date: _startDate,
                    onDateSelected: (date) {
                      setState(() {
                        _startDate = date;
                        _prepareChartData();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateSelector(
                    label: 'إلى',
                    date: _endDate,
                    onDateSelected: (date) {
                      setState(() {
                        _endDate = date;
                        _prepareChartData();
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector({
    required String label,
    required DateTime date,
    required Function(DateTime) onDateSelected,
  }) {
    return InkWell(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date,
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
        );

        if (selectedDate != null) {
          onDateSelected(selectedDate);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.lightBorder),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const AppTypography(
                fontSize: 14,
                color: AppColors.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              DateFormat('yyyy/MM/dd').format(date),
              style: const AppTypography(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard() {
    // حساب إجمالي الحركات
    int totalTransactions = _transactions.length;

    // حساب إجمالي الإضافات والخصومات
    double totalIn = 0;
    double totalOut = 0;

    for (var transaction in _transactions) {
      if (transaction.transactionType == InventoryTransactionType.purchase ||
          transaction.transactionType == InventoryTransactionType.returnIn ||
          (transaction.transactionType == InventoryTransactionType.adjustment &&
              transaction.quantity > 0) ||
          (transaction.transactionType == InventoryTransactionType.transfer &&
              transaction.quantity > 0)) {
        totalIn += transaction.quantity;
      } else {
        totalOut += transaction.quantity;
      }
    }

    // حساب صافي الحركة
    double netMovement = totalIn - totalOut;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _selectedProduct != null
                  ? 'ملخص حركة المنتج: ${_selectedProduct!.name}'
                  : 'ملخص حركة المخزون',
              style: const AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: 'إجمالي الحركات',
                    value: totalTransactions.toString(),
                    icon: Icons.swap_horiz,
                    color: AppColors.lightTextSecondary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: 'إجمالي الإضافات',
                    value: totalIn.toString(),
                    icon: Icons.add_circle,
                    color: AppColors.lightTextSecondary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: 'إجمالي الخصومات',
                    value: totalOut.toString(),
                    icon: Icons.remove_circle,
                    color: AppColors.lightTextSecondary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: 'صافي الحركة',
                    value: netMovement.toString(),
                    icon: Icons.compare_arrows,
                    color:
                        netMovement >= 0 ? AppColors.success : AppColors.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          size: 32,
          color: color,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const AppTypography(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTypography(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMovementChart() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'حركة المخزون عبر الزمن',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: _inSpots.isEmpty
                  ? const Center(child: Text('لا توجد بيانات للعرض'))
                  : LineChart(
                      LineChartData(
                        gridData: FlGridData(
                          show: true,
                          drawVerticalLine: true,
                          horizontalInterval: _maxY / 5,
                          verticalInterval: 1,
                        ),
                        titlesData: FlTitlesData(
                          show: true,
                          rightTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          topTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 30,
                              interval: _inSpots.length > 10
                                  ? (_inSpots.length / 5).floor().toDouble()
                                  : 1,
                              getTitlesWidget: (value, meta) {
                                if (value.toInt() >= 0 &&
                                    value.toInt() < _inSpots.length) {
                                  final date = _startDate
                                      .add(Duration(days: value.toInt()));
                                  return Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      DateFormat('MM/dd').format(date),
                                      style: const AppTypography(
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  );
                                }
                                return const SizedBox();
                              },
                            ),
                          ),
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              interval: _maxY / 5,
                              reservedSize: 42,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  value.toInt().toString(),
                                  style: const AppTypography(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        borderData: FlBorderData(
                          show: true,
                          border: Border.all(color: AppColors.lightBorder),
                        ),
                        minX: 0,
                        maxX: _inSpots.length.toDouble() - 1,
                        minY: 0,
                        maxY: _maxY,
                        lineBarsData: [
                          // خط الإضافات
                          LineChartBarData(
                            spots: _inSpots,
                            isCurved: true,
                            color: AppColors.lightTextSecondary,
                            barWidth: 3,
                            isStrokeCapRound: true,
                            dotData: const FlDotData(show: false),
                            belowBarData: BarAreaData(
                              show: true,
                              color: AppColors.success
                                  .withValues(alpha: 0.2), // 0.2 * 255 = 51
                            ),
                          ),
                          // خط الخصومات
                          LineChartBarData(
                            spots: _outSpots,
                            isCurved: true,
                            color: AppColors.lightTextSecondary,
                            barWidth: 3,
                            isStrokeCapRound: true,
                            dotData: const FlDotData(show: false),
                            belowBarData: BarAreaData(
                              show: true,
                              color: AppColors.error
                                  .withValues(alpha: 0.2), // 0.2 * 255 = 51
                            ),
                          ),
                        ],
                      ),
                    ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLegendItem('الإضافات', AppColors.success),
                const SizedBox(width: 24),
                _buildLegendItem('الخصومات', AppColors.error),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          color: color,
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: const AppTypography(fontSize: 14),
        ),
      ],
    );
  }

  Widget _buildTransactionsTable() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'قائمة حركات المخزون',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('نوع الحركة')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('المرجع')),
                  DataColumn(label: Text('ملاحظات')),
                ],
                rows: _transactions.map((transaction) {
                  final date = DateFormat('yyyy/MM/dd')
                      .format(transaction.transactionDate);
                  final type =
                      _getTransactionTypeName(transaction.transactionType);
                  final quantity = transaction.quantity.toString();
                  final reference = transaction.referenceId ?? '-';
                  final notes = transaction.notes ?? '-';

                  return DataRow(
                    cells: [
                      DataCell(Text(date)),
                      DataCell(
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: _getTransactionTypeColor(
                                transaction.transactionType),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            type,
                            style:
                                const AppTypography(color: AppColors.onPrimary),
                          ),
                        ),
                      ),
                      DataCell(Text(quantity)),
                      DataCell(Text(reference)),
                      DataCell(Text(notes)),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getTransactionTypeName(InventoryTransactionType type) {
    switch (type) {
      case InventoryTransactionType.purchase:
        return 'شراء';
      case InventoryTransactionType.sale:
        return 'بيع';
      case InventoryTransactionType.adjustment:
        return 'تسوية مخزون';
      case InventoryTransactionType.transfer:
        return 'تحويل مخزون';
      case InventoryTransactionType.returnIn:
        return 'مرتجع وارد';
      case InventoryTransactionType.returnOut:
        return 'مرتجع صادر';
      case InventoryTransactionType.count:
        return 'جرد مخزون';
    }
  }

  Color _getTransactionTypeColor(InventoryTransactionType type) {
    // الحركات التي تزيد المخزون
    if (type == InventoryTransactionType.purchase ||
        type == InventoryTransactionType.returnIn) {
      return AppColors.success;
    }

    // الحركات التي تنقص المخزون
    if (type == InventoryTransactionType.sale ||
        type == InventoryTransactionType.returnOut) {
      return AppColors.error;
    }

    // الحركات التي قد تزيد أو تنقص المخزون
    if (type == InventoryTransactionType.adjustment ||
        type == InventoryTransactionType.transfer ||
        type == InventoryTransactionType.count) {
      // نحدد اللون بناءً على قيمة الكمية
      // هذا يتطلب وجود الكمية، لذا سنستخدم اللون البنفسجي كقيمة افتراضية
      return AppColors.accent;
    }

    // الحالات الأخرى
    return AppColors.secondary;
  }
}
