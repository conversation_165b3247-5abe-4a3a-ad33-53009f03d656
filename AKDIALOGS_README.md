# 🔔 نظام الحوارات الموحد (AK Dialogs System)

نظام شامل وموحد لجميع أنواع الحوارات في تطبيق تاجر بلس، مصمم خصيصاً للمشاريع التجارية اليمنية.

## 🎯 **المميزات الرئيسية**

- ✅ **تصميم موحد ومتناسق** لجميع الحوارات
- ✅ **دعم كامل للوضع المظلم/الفاتح** باستخدام `Theme.of(context).brightness`
- ✅ **عدم وجود قيم صريحة** - استخدام `AppColors.*`, `AppDimensions.*`, `AppTypography.*` فقط
- ✅ **تأثيرات تفاعلية متقدمة** مع رسوم متحركة سلسة
- ✅ **دوال مساعدة سريعة** للاستخدام المباشر
- ✅ **تعليقات شاملة باللغة العربية**
- ✅ **تحميل كسول للعناصر الثقيلة**
- ✅ **دوال متخصصة للمشروع التجاري**

## 📋 **الحوارات المتوفرة**

### 🔘 **1. حوار التأكيد (AkConfirmDialog)**
حوار تأكيد موحد مع أنواع مختلفة (عادي، تحذير، خطر، نجاح، معلومات).

### 🔔 **2. حوار التنبيه (AkAlertDialog)**
حوار تنبيه متقدم مع إغلاق تلقائي وتأثيرات بصرية.

## 🚀 **أمثلة الاستخدام**

### **حوار التأكيد الأساسي:**
```dart
// حوار تأكيد بسيط
AkConfirmDialog.show(
  context: context,
  title: 'تأكيد الحذف',
  content: 'هل أنت متأكد من حذف هذا العنصر؟',
  type: AkDialogType.danger,
  onConfirm: () => deleteItem(),
);
```

### **حوار تنبيه نجاح:**
```dart
// تنبيه نجاح مع إغلاق تلقائي
AkAlertDialog.success(
  context: context,
  title: 'تم الحفظ',
  message: 'تم حفظ البيانات بنجاح',
  duration: Duration(seconds: 3),
);
```

### **الدوال المساعدة السريعة:**
```dart
// تأكيد حذف سريع
AkDialogs.confirmDelete(
  context: context,
  onConfirm: () => deleteProduct(),
);

// تنبيه نجاح عملية بيع
AkDialogs.saleSuccess(
  context: context,
  amount: '1,250 ر.ي',
  customerName: 'أحمد محمد',
);

// تحذير نقص مخزون
AkDialogs.lowStockWarning(
  context: context,
  productName: 'هاتف ذكي',
  currentStock: 2,
  minStock: 5,
);
```

## 🎨 **أنواع الحوارات**

| النوع | الوصف | اللون | الأيقونة الافتراضية |
|-------|--------|-------|-------------------|
| `normal` | حوار عادي | أزرق | `help_outline` |
| `warning` | تحذير | برتقالي | `warning_amber` |
| `danger` | خطر | أحمر | `error_outline` |
| `success` | نجاح | أخضر | `check_circle_outline` |
| `info` | معلومات | أزرق فاتح | `info_outline` |
| `loading` | تحميل | أزرق | `hourglass_empty` |

## 🛠️ **الدوال المساعدة المتوفرة**

### **دوال التأكيد:**
- `AkDialogs.confirmDelete()` - تأكيد حذف
- `AkDialogs.confirmSave()` - تأكيد حفظ
- `AkDialogs.confirmExit()` - تأكيد خروج
- `AkDialogs.confirmCashierClose()` - تأكيد إغلاق الكاشير

### **دوال التنبيه:**
- `AkDialogs.success()` - تنبيه نجاح
- `AkDialogs.error()` - تنبيه خطأ
- `AkDialogs.warning()` - تنبيه تحذير
- `AkDialogs.info()` - تنبيه معلومات

### **دوال متخصصة للمشروع:**
- `AkDialogs.saleSuccess()` - نجاح عملية بيع
- `AkDialogs.purchaseSuccess()` - نجاح عملية شراء
- `AkDialogs.lowStockWarning()` - تحذير نقص مخزون
- `AkDialogs.paymentError()` - خطأ في الدفع

## 🎯 **أمثلة متقدمة**

### **حوار تأكيد مخصص:**
```dart
AkConfirmDialog(
  title: 'إغلاق الكاشير',
  content: 'إجمالي المبيعات: 15,750 ر.ي\nهل تريد إغلاق الكاشير؟',
  type: AkDialogType.warning,
  icon: Icons.point_of_sale,
  confirmText: 'إغلاق',
  cancelText: 'إلغاء',
  customColor: AppColors.warning,
  onConfirm: () => closeCashier(),
);
```

### **تنبيه مع أزرار مخصصة:**
```dart
AkAlertDialog(
  title: 'تحديث متوفر',
  message: 'يتوفر إصدار جديد من التطبيق',
  type: AkDialogType.info,
  dismissible: true,
  actions: [
    TextButton(
      onPressed: () => Navigator.pop(context),
      child: Text('لاحقاً'),
    ),
    ElevatedButton(
      onPressed: () => updateApp(),
      child: Text('تحديث الآن'),
    ),
  ],
);
```

## 🔧 **التخصيص المتقدم**

### **ألوان مخصصة:**
```dart
AkConfirmDialog.show(
  context: context,
  title: 'إجراء مخصص',
  content: 'هل تريد المتابعة؟',
  customColor: Color(0xFF6B46C1), // بنفسجي مخصص
  icon: Icons.star,
);
```

### **مدة إغلاق مخصصة:**
```dart
AkAlertDialog.success(
  context: context,
  title: 'تم الإرسال',
  message: 'تم إرسال الرسالة بنجاح',
  duration: Duration(seconds: 5), // إغلاق بعد 5 ثوان
);
```

## 📱 **التوافق والاستجابة**

- ✅ **متوافق مع جميع أحجام الشاشات**
- ✅ **يتكيف مع اتجاه الشاشة**
- ✅ **يدعم الخطوط العربية**
- ✅ **متوافق مع إعدادات إمكانية الوصول**

## 🎨 **الدعم للوضع المظلم/الفاتح**

النظام يتكيف تلقائياً مع وضع التطبيق:

```dart
// يتم التحقق تلقائياً من الوضع
final isDark = Theme.of(context).brightness == Brightness.dark;

// الألوان تتغير تلقائياً
backgroundColor: isDark ? AppColors.darkSurface : AppColors.lightSurface,
textColor: isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
```

## 🔄 **الترقية من الأنظمة القديمة**

### **من confirmation_dialog.dart:**
```dart
// ❌ القديم
ConfirmationDialog.show(context, 'حذف', 'تأكيد الحذف؟');

// ✅ الجديد
AkDialogs.confirmDelete(context: context, onConfirm: () => delete());
```

### **من app_alert.dart:**
```dart
// ❌ القديم
AppAlert.showSuccess(context, 'تم الحفظ');

// ✅ الجديد
AkDialogs.success(context: context, message: 'تم الحفظ بنجاح');
```

## 📊 **الإحصائيات**

- **عدد الحوارات**: 2 نوع أساسي
- **عدد الدوال المساعدة**: 12 دالة
- **عدد الأنواع**: 6 أنواع مختلفة
- **الدعم للغات**: العربية (أساسي)
- **حجم الملف**: ~880 سطر
- **التبعيات**: لا توجد تبعيات خارجية

---

**تم تطوير هذا النظام خصيصاً لتطبيق تاجر بلس - نظام إدارة المبيعات اليمني** 🇾🇪
