import 'package:flutter/foundation.dart';
import '../../../core/services/customer_service.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/models/customer.dart';

/// مقدم العملاء
class CustomerPresenter extends ChangeNotifier {
  final CustomerService _customerService = CustomerService();

  List<Customer> _customers = [];
  Customer? _selectedCustomer;
  bool _isLoading = false;
  String? _errorMessage;
  String? _searchQuery;

  /// الحصول على قائمة العملاء
  List<Customer> get customers => _customers;

  /// الحصول على العميل المحدد
  Customer? get selectedCustomer => _selectedCustomer;

  /// تعيين العميل المحدد
  set selectedCustomer(Customer? customer) {
    _selectedCustomer = customer;
    notifyListeners();
  }

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// رسالة الخطأ (للتوافق مع الكود القديم)
  String? get error => _errorMessage;

  /// استعلام البحث
  String? get searchQuery => _searchQuery;

  /// تعيين استعلام البحث
  set searchQuery(String? query) {
    _searchQuery = query;
    notifyListeners();
  }

  /// تهيئة المقدم
  Future<void> init() async {
    await loadCustomers();
  }

  /// تحميل العملاء
  Future<void> loadCustomers({String? type}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final customers = await _customerService.getAllCustomers(type: type);

      _customers = customers;
      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل العملاء: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل العملاء',
        error: e,
        stackTrace: stackTrace,
        context: {
          'type': type,
        },
      );
      notifyListeners();
      rethrow;
    }
  }

  /// الحصول على عميل بواسطة المعرف
  Customer? getCustomerById(String id) {
    try {
      return _customers.firstWhere((customer) => customer.id == id);
    } catch (e) {
      return null;
    }
  }

  /// إضافة عميل جديد
  Future<bool> addCustomer(Customer customer) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _customerService.addCustomer(customer);

      if (success) {
        _customers.add(customer);
      }

      _isLoading = false;
      notifyListeners();

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء إضافة العميل: $e';
      ErrorTracker.captureError(
        'خطأ في إضافة عميل',
        error: e,
        stackTrace: stackTrace,
        context: {
          'customer_name': customer.name,
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// تحديث عميل
  Future<bool> updateCustomer(Customer customer) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _customerService.updateCustomer(customer);

      if (success) {
        final index = _customers.indexWhere((c) => c.id == customer.id);
        if (index >= 0) {
          _customers[index] = customer;
        }
      }

      _isLoading = false;
      notifyListeners();

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحديث العميل: $e';
      ErrorTracker.captureError(
        'خطأ في تحديث عميل',
        error: e,
        stackTrace: stackTrace,
        context: {
          'customer_id': customer.id,
          'customer_name': customer.name,
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// حذف عميل
  Future<bool> deleteCustomer(String id) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _customerService.deleteCustomer(id);

      if (success) {
        _customers.removeWhere((customer) => customer.id == id);
      }

      _isLoading = false;
      notifyListeners();

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء حذف العميل: $e';
      ErrorTracker.captureError(
        'خطأ في حذف عميل',
        error: e,
        stackTrace: stackTrace,
        context: {
          'customer_id': id,
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// تحديث رصيد العميل
  Future<bool> updateCustomerBalance(
      String id, double amount, String reason) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final customer = getCustomerById(id);
      if (customer == null) {
        throw Exception('العميل غير موجود');
      }

      final updatedCustomer = customer.copyWith(
        balance: customer.balance + amount,
        updatedAt: DateTime.now(),
      );

      final success = await updateCustomer(updatedCustomer);

      _isLoading = false;
      notifyListeners();

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحديث رصيد العميل: $e';
      ErrorTracker.captureError(
        'خطأ في تحديث رصيد عميل',
        error: e,
        stackTrace: stackTrace,
        context: {
          'customer_id': id,
          'amount': amount,
          'reason': reason,
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// البحث عن العملاء
  List<Customer> searchCustomers(String query) {
    if (query.isEmpty) {
      return _customers;
    }

    final lowercaseQuery = query.toLowerCase();

    return _customers.where((customer) {
      final name = customer.name.toLowerCase();
      final phone = customer.phone?.toLowerCase() ?? '';
      final email = customer.email?.toLowerCase() ?? '';
      final address = customer.address?.toLowerCase() ?? '';

      return name.contains(lowercaseQuery) ||
          phone.contains(lowercaseQuery) ||
          email.contains(lowercaseQuery) ||
          address.contains(lowercaseQuery);
    }).toList();
  }

  /// الحصول على عميل بواسطة المعرف بشكل غير متزامن
  Future<Customer?> getCustomerByIdAsync(String id) async {
    try {
      return await _customerService.getCustomerById(id);
    } catch (e, stackTrace) {
      _errorMessage = 'حدث خطأ أثناء الحصول على العميل: $e';
      ErrorTracker.captureError(
        'خطأ في الحصول على عميل',
        error: e,
        stackTrace: stackTrace,
        context: {
          'customer_id': id,
        },
      );
      return null;
    }
  }

  /// تحميل عميل بواسطة المعرف
  Future<void> loadCustomer(String id) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final customer = await _customerService.getCustomerById(id);
      if (customer != null) {
        _selectedCustomer = customer;
      }

      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل العميل: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل عميل',
        error: e,
        stackTrace: stackTrace,
        context: {
          'customer_id': id,
        },
      );
      notifyListeners();
    }
  }

  /// الحصول على العملاء النشطين
  List<Customer> getActiveCustomers() {
    return _customers.where((customer) => customer.isActive).toList();
  }

  /// الحصول على العملاء ذوي الرصيد المدين
  List<Customer> getCustomersWithDebitBalance() {
    return _customers.where((customer) => customer.balance < 0).toList();
  }

  /// الحصول على العملاء ذوي الرصيد الدائن
  List<Customer> getCustomersWithCreditBalance() {
    return _customers.where((customer) => customer.balance > 0).toList();
  }

  /// الحصول على إجمالي رصيد العملاء
  double getTotalCustomersBalance() {
    return _customers.fold(0, (sum, customer) => sum + customer.balance);
  }

  /// الحصول على العملاء من نوع شركة
  List<Customer> getCompanyCustomers() {
    return _customers.toList();
  }

  /// الحصول على العملاء من نوع فرد
  List<Customer> getIndividualCustomers() {
    return _customers.toList();
  }
}
