import 'package:flutter/material.dart';
import '../theme/index.dart';

/// Widget محسن لعرض النصوص العربية مع دعم Impeller
/// يحل مشاكل glyph atlas وfont rendering
class AkArabicText extends StatelessWidget {
  /// النص المراد عرضه
  final String text;

  /// نمط النص
  final TextStyle? style;

  /// محاذاة النص
  final TextAlign? textAlign;

  /// اتجاه النص
  final TextDirection? textDirection;

  /// عدد الأسطر الأقصى
  final int? maxLines;

  /// سلوك التجاوز
  final TextOverflow? overflow;

  /// معامل التحجيم للنص
  final double? textScaleFactor;

  /// هل النص قابل للتحديد
  final bool selectable;

  /// دالة عند النقر (للنصوص القابلة للنقر)
  final VoidCallback? onTap;

  const AkArabicText(
    this.text, {
    Key? key,
    this.style,
    this.textAlign,
    this.textDirection,
    this.maxLines,
    this.overflow,
    this.textScaleFactor,
    this.selectable = false,
    this.onTap,
  }) : super(key: key);

  /// إنشاء نص عربي بنمط العنوان الرئيسي
  const AkArabicText.headline(
    this.text, {
    Key? key,
    Color? color,
    this.textAlign,
    this.textDirection,
    this.maxLines,
    this.overflow,
    this.textScaleFactor,
    this.selectable = false,
    this.onTap,
  })  : style = null,
        super(key: key);

  /// إنشاء نص عربي بنمط العنوان الفرعي
  const AkArabicText.title(
    this.text, {
    Key? key,
    Color? color,
    this.textAlign,
    this.textDirection,
    this.maxLines,
    this.overflow,
    this.textScaleFactor,
    this.selectable = false,
    this.onTap,
  })  : style = null,
        super(key: key);

  /// إنشاء نص عربي بنمط النص العادي
  const AkArabicText.body(
    this.text, {
    Key? key,
    Color? color,
    this.textAlign,
    this.textDirection,
    this.maxLines,
    this.overflow,
    this.textScaleFactor,
    this.selectable = false,
    this.onTap,
  })  : style = null,
        super(key: key);

  /// إنشاء نص عربي بنمط التسمية
  const AkArabicText.label(
    this.text, {
    Key? key,
    Color? color,
    this.textAlign,
    this.textDirection,
    this.maxLines,
    this.overflow,
    this.textScaleFactor,
    this.selectable = false,
    this.onTap,
  })  : style = null,
        super(key: key);

  @override
  Widget build(BuildContext context) {
    // إنشاء نمط النص المحسن للعربية
    final optimizedStyle = _createOptimizedStyle(context);

    // تحديد اتجاه النص (RTL للعربية)
    final effectiveTextDirection = textDirection ?? TextDirection.rtl;

    // تحديد محاذاة النص (يمين للعربية)
    final effectiveTextAlign = textAlign ?? TextAlign.right;

    // إنشاء Widget النص المناسب
    Widget textWidget;

    if (selectable) {
      textWidget = SelectableText(
        text,
        style: optimizedStyle,
        textAlign: effectiveTextAlign,
        textDirection: effectiveTextDirection,
        maxLines: maxLines,
        textScaler: TextScaler.linear(textScaleFactor ?? 1.0),
      );
    } else {
      textWidget = Text(
        text,
        style: optimizedStyle,
        textAlign: effectiveTextAlign,
        textDirection: effectiveTextDirection,
        maxLines: maxLines,
        overflow: overflow,
        textScaler: TextScaler.linear(textScaleFactor ?? 1.0),
      );
    }

    // إضافة إمكانية النقر إذا كانت مطلوبة
    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: textWidget,
      );
    }

    return textWidget;
  }

  /// إنشاء نمط النص المحسن للعربية مع دعم Impeller
  TextStyle _createOptimizedStyle(BuildContext context) {
    // الحصول على النمط الأساسي
    TextStyle baseStyle;

    if (style != null) {
      baseStyle = style!;
    } else {
      // تحديد النمط حسب نوع النص
      final theme = Theme.of(context);
      baseStyle = theme.textTheme.bodyMedium ?? const TextStyle();
    }

    // تطبيق التحسينات للخطوط العربية
    return AppTypography.createArabicOptimizedStyle(
      fontSize: baseStyle.fontSize,
      fontWeight: baseStyle.fontWeight,
      color: baseStyle.color,
      height: baseStyle.height,
      letterSpacing: baseStyle.letterSpacing,
    );
  }

  /// دالة مساعدة لإنشاء نص عربي سريع
  static Widget quick(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    Color? color,
  }) {
    return AkArabicText(
      text,
      style: style?.copyWith(color: color) ?? TextStyle(color: color),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  /// دالة مساعدة لإنشاء نص عربي قابل للنقر
  static Widget clickable(
    String text, {
    required VoidCallback onTap,
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    Color? color,
  }) {
    return AkArabicText(
      text,
      style: style?.copyWith(color: color) ?? TextStyle(color: color),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      onTap: onTap,
    );
  }

  /// دالة مساعدة لإنشاء نص عربي قابل للتحديد
  static Widget selectableText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    Color? color,
  }) {
    return AkArabicText(
      text,
      style: style?.copyWith(color: color) ?? TextStyle(color: color),
      textAlign: textAlign,
      maxLines: maxLines,
      selectable: true,
    );
  }
}
