import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:tajer_plus/core/database/database_helper.dart';

void main() {
  // تهيئة Flutter binding للاختبارات
  TestWidgetsFlutterBinding.ensureInitialized();

  // تهيئة قاعدة بيانات الاختبار
  setUpAll(() {
    // تهيئة sqflite_common_ffi
    sqfliteFfiInit();
    // تعيين مصنع قاعدة البيانات
    databaseFactory = databaseFactoryFfi;
  });

  group('DatabaseHelper Integration Tests', () {
    late DatabaseHelper dbHelper;
    late Database db;

    // إنشاء قاعدة بيانات مؤقتة قبل كل اختبار
    setUp(() async {
      // تهيئة مساعد قاعدة البيانات
      dbHelper = DatabaseHelper();

      // إنشاء قاعدة بيانات في الذاكرة للاختبار
      db = await openDatabase(
        inMemoryDatabasePath,
        version: 1,
        onCreate: (db, version) async {
          // لا نحتاج لإنشاء الجداول هنا لأننا سنختبر ذلك
        },
      );

      // تعيين قاعدة البيانات للاختبار
      DatabaseHelper.testDatabase = db;
    });

    // إغلاق قاعدة البيانات بعد كل اختبار
    tearDown(() async {
      // إغلاق قاعدة البيانات
      await db.close();
      DatabaseHelper.testDatabase = null;
    });

    test('database should be accessible', () async {
      // التحقق من إمكانية الوصول إلى قاعدة البيانات
      final database = await dbHelper.database;
      expect(database, isNotNull, reason: 'يجب أن تكون قاعدة البيانات متاحة');
      expect(database.isOpen, isTrue,
          reason: 'يجب أن تكون قاعدة البيانات مفتوحة');
    });

    test('database should allow executing queries', () async {
      // إنشاء جدول اختبار بسيط
      await db.execute(
          'CREATE TABLE test_table (id TEXT PRIMARY KEY, name TEXT, value INTEGER)');

      // إدخال بيانات
      await db.insert('test_table', {
        'id': 'test1',
        'name': 'Test Item',
        'value': 100,
      });

      // التحقق من وجود البيانات
      final results = await db.query('test_table');
      expect(results.length, 1, reason: 'يجب أن يكون هناك عنصر واحد في الجدول');
      expect(results.first['name'], 'Test Item',
          reason: 'يجب أن يكون اسم العنصر صحيحاً');
    });

    // اختبار مباشر لقاعدة البيانات بدون استخدام DatabaseHelper.getDatabasePath
    test('direct database operations should work', () async {
      // إنشاء جدول آخر للاختبار
      await db.execute(
          'CREATE TABLE another_test_table (id TEXT PRIMARY KEY, description TEXT)');

      // إدخال بيانات
      await db.insert('another_test_table', {
        'id': 'test2',
        'description': 'Another test item',
      });

      // التحقق من وجود البيانات
      final results = await db.query('another_test_table');
      expect(results.length, 1, reason: 'يجب أن يكون هناك عنصر واحد في الجدول');
      expect(results.first['description'], 'Another test item',
          reason: 'يجب أن يكون الوصف صحيحاً');
    });
  });
}
