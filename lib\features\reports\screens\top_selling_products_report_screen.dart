import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

import '../../../core/widgets/index.dart';
import '../presenters/reports_presenter.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

/// شاشة تقرير المنتجات الأكثر مبيعًا
///
/// تعرض هذه الشاشة تقرير المنتجات الأكثر مبيعًا مع رسوم بيانية لتوضيح المبيعات
class TopSellingProductsReportScreen extends StatefulWidget {
  const TopSellingProductsReportScreen({Key? key}) : super(key: key);

  @override
  State<TopSellingProductsReportScreen> createState() =>
      _TopSellingProductsReportScreenState();
}

class _TopSellingProductsReportScreenState
    extends State<TopSellingProductsReportScreen> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _topSellingProducts = [];
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  int _limit = 10; // عدد المنتجات المراد عرضها

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final presenter = AppProviders.getLazyPresenter<ReportsPresenter>(
          () => ReportsPresenter());

      // تحميل المنتجات الأكثر مبيعًا
      _topSellingProducts = await presenter.getTopSellingProducts(
        startDate: _startDate,
        endDate: _endDate,
        limit: _limit,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'تقرير المنتجات الأكثر مبيعًا',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildReportContent(),
    );
  }

  Widget _buildReportContent() {
    if (_topSellingProducts.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات للعرض'),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDateRangeSelector(),
            const SizedBox(height: 20),
            _buildBarChart(),
            const SizedBox(height: 20),
            _buildProductsTable(),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نطاق التاريخ',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDateSelector(
                    label: 'من',
                    date: _startDate,
                    onDateSelected: (date) {
                      setState(() {
                        _startDate = date;
                        _loadData();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateSelector(
                    label: 'إلى',
                    date: _endDate,
                    onDateSelected: (date) {
                      setState(() {
                        _endDate = date;
                        _loadData();
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('عدد المنتجات:'),
                const SizedBox(width: 16),
                DropdownButton<int>(
                  value: _limit,
                  items: [5, 10, 20, 50].map((value) {
                    return DropdownMenuItem<int>(
                      value: value,
                      child: Text(value.toString()),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _limit = value;
                        _loadData();
                      });
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector({
    required String label,
    required DateTime date,
    required Function(DateTime) onDateSelected,
  }) {
    return InkWell(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date,
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
        );

        if (selectedDate != null) {
          onDateSelected(selectedDate);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.lightTextSecondary),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const AppTypography(
                fontSize: 14,
                color: AppColors.lightTextSecondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              DateFormat('yyyy/MM/dd').format(date),
              style: const AppTypography(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBarChart() {
    // تحضير بيانات الرسم البياني
    final List<BarChartGroupData> barGroups = [];

    for (int i = 0; i < _topSellingProducts.length; i++) {
      final product = _topSellingProducts[i];
      final quantity = product['quantity'] as double? ?? 0.0;

      barGroups.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              toY: quantity,
              color: _getBarColor(i),
              width: 20,
              borderRadius: BorderRadius.circular(4),
            ),
          ],
        ),
      );
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المنتجات الأكثر مبيعًا',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: barGroups.isEmpty
                  ? const Center(child: Text('لا توجد بيانات للعرض'))
                  : BarChart(
                      BarChartData(
                        alignment: BarChartAlignment.spaceAround,
                        maxY: _getMaxY() * 1.2,
                        barTouchData: BarTouchData(
                          touchTooltipData: BarTouchTooltipData(
                            getTooltipColor: (group) =>
                                AppColors.lightTextSecondary,
                            getTooltipItem: (group, groupIndex, rod, rodIndex) {
                              final product =
                                  _topSellingProducts[group.x.toInt()];
                              final productName =
                                  product['product_name'] as String? ??
                                      'منتج غير معروف';
                              final quantity =
                                  product['quantity'] as double? ?? 0.0;

                              return BarTooltipItem(
                                '$productName\n${quantity.toStringAsFixed(0)}',
                                const AppTypography(
                                  color: AppColors.lightTextSecondary,
                                  fontWeight: FontWeight.bold,
                                ),
                              );
                            },
                          ),
                        ),
                        titlesData: FlTitlesData(
                          show: true,
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              getTitlesWidget: (value, meta) {
                                if (value.toInt() >= 0 &&
                                    value.toInt() <
                                        _topSellingProducts.length) {
                                  final product =
                                      _topSellingProducts[value.toInt()];
                                  final productName =
                                      product['product_name'] as String? ??
                                          'منتج غير معروف';

                                  return Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      _shortenProductName(productName),
                                      style: const AppTypography(
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  );
                                }
                                return const SizedBox();
                              },
                            ),
                          ),
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 30,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  value.toInt().toString(),
                                  style: const AppTypography(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                );
                              },
                            ),
                          ),
                          topTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          rightTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                        ),
                        gridData: const FlGridData(
                          show: true,
                          drawVerticalLine: false,
                        ),
                        borderData: FlBorderData(
                          show: true,
                          border: Border.all(
                              color: AppColors.lightTextSecondary, width: 1),
                        ),
                        barGroups: barGroups,
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  double _getMaxY() {
    double maxY = 0;
    for (var product in _topSellingProducts) {
      final quantity = product['quantity'] as double? ?? 0.0;
      if (quantity > maxY) {
        maxY = quantity;
      }
    }
    return maxY;
  }

  Color _getBarColor(int index) {
    final colors = [
      AppColors.info,
      AppColors.success,
      AppColors.warning,
      AppColors.accent,
      AppColors.error,
      AppColors.secondary,
      AppColors.warning,
      AppColors.info,
      AppColors.accent,
      AppColors.secondary,
    ];

    return colors[index % colors.length];
  }

  String _shortenProductName(String name) {
    if (name.length <= 10) {
      return name;
    }
    return '${name.substring(0, 8)}...';
  }

  Widget _buildProductsTable() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'قائمة المنتجات الأكثر مبيعًا',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('المنتج')),
                  DataColumn(label: Text('الكمية المباعة')),
                  DataColumn(label: Text('قيمة المبيعات')),
                  DataColumn(label: Text('عدد الفواتير')),
                  DataColumn(label: Text('متوسط سعر البيع')),
                ],
                rows: _topSellingProducts.map((product) {
                  final productName =
                      product['product_name'] as String? ?? 'منتج غير معروف';
                  final quantity = product['quantity'] as double? ?? 0.0;
                  final totalSales = product['total_sales'] as double? ?? 0.0;
                  final invoiceCount = product['invoice_count'] as int? ?? 0;
                  final averagePrice =
                      quantity > 0 ? totalSales / quantity : 0.0;

                  return DataRow(
                    cells: [
                      DataCell(Text(productName)),
                      DataCell(Text(quantity.toStringAsFixed(2))),
                      DataCell(Text('${totalSales.toStringAsFixed(2)} ر.س')),
                      DataCell(Text(invoiceCount.toString())),
                      DataCell(Text('${averagePrice.toStringAsFixed(2)} ر.س')),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
