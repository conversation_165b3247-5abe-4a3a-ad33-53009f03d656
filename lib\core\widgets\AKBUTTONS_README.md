# 🎯 نظام الأزرار الموحد والشامل (AK Buttons System)

## 🎯 نظرة عامة

نظام أزرار موحد وشامل يوفر جميع أنواع الأزرار المطلوبة في التطبيق مع تصميم متناسق وأداء محسن.

## 🔘 الأزرار الأساسية

### 1. AkButton - الزر الأساسي الموحد
```dart
AkButton(
  text: 'حفظ البيانات',
  onPressed: () => saveData(),
  type: AkButtonType.primary,
  size: AkButtonSize.medium,
  icon: Icons.save,
  isLoading: isLoading,
)
```

**المميزات:**
- دعم جميع أنواع الأزرار (أساسي، ثانوي، خطر، إلخ)
- أحجام مختلفة (صغير، متوسط، كبير، كبير جداً)
- أشكال مختلفة (مستطيل، دائري، مربع، بيضاوي)
- حالات مختلفة (تفعيل، تعطيل، تحميل)
- تأثيرات تفاعلية متقدمة

### 2. AkIconButton - زر الأيقونة الموحد
```dart
AkIconButton(
  icon: Icons.edit,
  onPressed: () => editItem(),
  type: AkButtonType.info,
  size: AkButtonSize.medium,
  tooltip: 'تعديل العنصر',
  showBackground: true,
)
```

**المميزات:**
- أحجام مختلفة للأيقونات
- ألوان متنوعة حسب السياق
- إمكانية إخفاء/إظهار الخلفية
- تأثيرات تفاعلية مع التحميل الكسول

### 3. AkTextButton - زر النص الموحد
```dart
AkTextButton(
  text: 'تسجيل الدخول',
  onPressed: () => login(),
  type: AkButtonType.primary,
  icon: Icons.login,
)
```

**المميزات:**
- تصميم نظيف بدون خلفية
- ألوان متنوعة حسب السياق
- تأثيرات تفاعلية خفيفة
- دعم الأيقونات الاختيارية

## 🎨 الأزرار المتخصصة

### 3. AkSaveButton - زر الحفظ
```dart
AkSaveButton(
  onPressed: () => saveData(),
  isLoading: isLoading,
  text: 'حفظ البيانات',
)
```

### 4. AkDeleteButton - زر الحذف
```dart
AkDeleteButton(
  onPressed: () => deleteItem(),
  requireConfirmation: true,
  confirmationMessage: 'هل تريد حذف هذا العنصر نهائياً؟',
)
```

### 5. AkCancelButton - زر الإلغاء
```dart
AkCancelButton(
  text: 'إلغاء',
  autoClose: true,
)
```

### 6. AkAddButton - زر الإضافة
```dart
AkAddButton(
  onPressed: () => addNewItem(),
  text: 'إضافة عنصر جديد',
)
```

## 🎯 الأزرار العائمة

### 7. AkFloatingButton - الزر العائم الموحد
```dart
AkFloatingButton(
  icon: Icons.add,
  onPressed: () => addNewItem(),
  type: AkButtonType.primary,
  tooltip: 'إضافة عنصر جديد',
  mini: false,
)
```

**المميزات:**
- تصميم موحد لجميع الأزرار العائمة
- أحجام مختلفة (عادي، صغير)
- ألوان متنوعة حسب السياق
- تأثيرات تفاعلية متقدمة

## 🛠️ الدوال المساعدة

### AkButtons - مجموعة دوال إنشاء سريعة

```dart
// إنشاء أزرار سريعة
AkButtons.save(
  onPressed: () => saveData(),
  isLoading: isLoading,
)

AkButtons.delete(
  onPressed: () => deleteItem(),
  requireConfirmation: true,
)

AkButtons.cancel(
  text: 'إلغاء',
  autoClose: true,
)

AkButtons.add(
  onPressed: () => addNewItem(),
  text: 'إضافة',
)

AkButtons.floating(
  icon: Icons.add,
  onPressed: () => addNewItem(),
  type: AkButtonType.primary,
)
```

### مجموعات الأزرار الشائعة

```dart
// مجموعة أزرار حفظ وإلغاء
AkButtons.saveCancel(
  onSave: () => saveData(),
  onCancel: () => Navigator.pop(context),
  isLoading: isLoading,
)

// مجموعة أزرار تعديل وحذف
AkButtons.editDelete(
  onEdit: () => editItem(),
  onDelete: () => deleteItem(),
  requireDeleteConfirmation: true,
)
```

## 🎨 أنواع وأحجام الأزرار

### أنواع الأزرار (AkButtonType)
- `primary` - زر أساسي (لون أساسي)
- `secondary` - زر ثانوي (لون ثانوي)
- `success` - زر النجاح (لون أخضر)
- `warning` - زر التحذير (لون برتقالي)
- `danger` - زر الخطر (لون أحمر)
- `info` - زر المعلومات (لون أزرق)
- `transparent` - زر شفاف (بدون خلفية)

### أحجام الأزرار (AkButtonSize)
- `small` - صغير
- `medium` - متوسط
- `large` - كبير
- `extraLarge` - كبير جداً

### أشكال الأزرار (AkButtonShape)
- `rounded` - مستطيل مع زوايا مدورة
- `circular` - دائري
- `square` - مستطيل بزوايا حادة
- `pill` - بيضاوي

## 🚀 المميزات الرئيسية

### ✅ التحميل الكسول
- **مؤشرات التحميل**: يتم تحميلها عند الحاجة فقط
- **التأثيرات التفاعلية**: يتم بناؤها عند التفاعل
- **الأيقونات**: يتم تحميلها حسب الحاجة

### ✅ التكامل مع نظام الثيمات
- **الألوان**: `AppColors.*`
- **الأبعاد**: `AppDimensions.*`
- **الخطوط**: `AppTypography.*`

### ✅ التأثيرات التفاعلية
- **تأثيرات الضغط**: تصغير وتكبير
- **تأثيرات الدوران**: للأزرار العائمة
- **تأثيرات الظلال**: حسب نوع الزر

### ✅ إمكانية التخصيص
- **ألوان مخصصة**: للخلفية والنص
- **أحجام مخصصة**: للعرض والارتفاع
- **مسافات مخصصة**: للحشو الداخلي

## 📱 الاستجابة والتكيف

جميع الأزرار تتكيف تلقائياً مع:
- **حجم الشاشة**: أحجام متجاوبة
- **الوضع المظلم/الفاتح**: ألوان تكيفية
- **اتجاه النص**: دعم RTL/LTR

## 🔧 التحقق من صحة البيانات

```dart
// التحقق من حالة الزر
final isEnabled = onPressed != null && !isLoading;

// التحقق من نوع الزر
if (type == AkButtonType.danger) {
  // منطق خاص بأزرار الخطر
}
```

## 📚 أمثلة متقدمة

### زر بتخصيص كامل
```dart
AkButton(
  text: 'زر مخصص',
  onPressed: () {},
  customBackgroundColor: Colors.purple,
  customTextColor: Colors.white,
  shape: AkButtonShape.pill,
  customPadding: EdgeInsets.all(20),
  customWidth: 200,
  customHeight: 50,
)
```

### زر أيقونة بدون خلفية
```dart
AkIconButton(
  icon: Icons.favorite,
  onPressed: () => toggleFavorite(),
  showBackground: false,
  customIconColor: Colors.red,
  type: AkButtonType.transparent,
)
```

## 🎯 الاستخدام الموصى به

1. **استخدم الأزرار المتخصصة** للعمليات الشائعة
2. **استخدم الدوال المساعدة** للإنشاء السريع
3. **اتبع أنواع الأزرار** حسب السياق
4. **استخدم التلميحات** للأزرار غير الواضحة
5. **طبق التحميل الكسول** للعناصر الثقيلة

---

**`akbuttons.dart` هو المصدر الوحيد والشامل لجميع الأزرار في التطبيق!** 🚀
