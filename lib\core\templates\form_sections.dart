import 'package:flutter/material.dart';
import '../widgets/index.dart';
import '../theme/index.dart';

/// قسم نموذج عام يمكن استخدامه في أي شاشة نموذج
class FormSection extends StatelessWidget {
  /// عنوان القسم
  final String title;

  /// محتوى القسم (الحقول)
  final List<Widget> children;

  /// المسافة الداخلية للقسم
  final EdgeInsetsGeometry? padding;

  /// المسافة بين العناصر
  final double spacing;

  /// هل يتم عرض العنوان
  final bool showTitle;

  /// نمط العنوان
  final AppTypography? titleStyle;

  const FormSection({
    Key? key,
    required this.title,
    required this.children,
    this.padding,
    this.spacing = 16,
    this.showTitle = true,
    this.titleStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AkCard(
      child: Padding(
        padding: padding ?? EdgeInsets.all(AppDimensions.defaultMargin),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showTitle) ...[
              Text(
                title,
                style: titleStyle ?? Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(height: spacing),
            ],
            ...List.generate(
              children.length * 2 - 1,
              (index) => index.isEven
                  ? children[index ~/ 2]
                  : SizedBox(height: spacing),
            ),
          ],
        ),
      ),
    );
  }
}

/// قسم الخيارات المستخدم في النماذج
class OptionsFormSection extends StatelessWidget {
  /// عنوان القسم
  final String title;

  /// خيارات القسم
  final List<Widget> options;

  /// المسافة الداخلية للقسم
  final EdgeInsetsGeometry? padding;

  /// المسافة بين الخيارات
  final double spacing;

  const OptionsFormSection({
    Key? key,
    this.title = 'الخيارات',
    required this.options,
    this.padding,
    this.spacing = 8,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FormSection(
      title: title,
      padding: padding,
      spacing: spacing,
      children: options,
    );
  }
}

/// قسم المعلومات الأساسية المستخدم في النماذج
class BasicInfoFormSection extends StatelessWidget {
  /// عنوان القسم
  final String title;

  /// حقول القسم
  final List<Widget> fields;

  /// المسافة الداخلية للقسم
  final EdgeInsetsGeometry? padding;

  /// المسافة بين الحقول
  final double spacing;

  const BasicInfoFormSection({
    Key? key,
    this.title = 'المعلومات الأساسية',
    required this.fields,
    this.padding,
    this.spacing = 16,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FormSection(
      title: title,
      padding: padding,
      spacing: spacing,
      children: fields,
    );
  }
}

/// قسم معلومات الاتصال المستخدم في النماذج
class ContactInfoFormSection extends StatelessWidget {
  /// عنوان القسم
  final String title;

  /// حقول القسم
  final List<Widget> fields;

  /// المسافة الداخلية للقسم
  final EdgeInsetsGeometry? padding;

  /// المسافة بين الحقول
  final double spacing;

  const ContactInfoFormSection({
    Key? key,
    this.title = 'معلومات الاتصال',
    required this.fields,
    this.padding,
    this.spacing = 16,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FormSection(
      title: title,
      padding: padding,
      spacing: spacing,
      children: fields,
    );
  }
}

/// قسم معلومات التسعير المستخدم في نماذج المنتجات
class PricingFormSection extends StatelessWidget {
  /// عنوان القسم
  final String title;

  /// حقول القسم
  final List<Widget> fields;

  /// المسافة الداخلية للقسم
  final EdgeInsetsGeometry? padding;

  /// المسافة بين الحقول
  final double spacing;

  const PricingFormSection({
    Key? key,
    this.title = 'معلومات التسعير',
    required this.fields,
    this.padding,
    this.spacing = 16,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FormSection(
      title: title,
      padding: padding,
      spacing: spacing,
      children: fields,
    );
  }
}

/// قسم معلومات المخزون المستخدم في نماذج المنتجات
class InventoryFormSection extends StatelessWidget {
  /// عنوان القسم
  final String title;

  /// حقول القسم
  final List<Widget> fields;

  /// المسافة الداخلية للقسم
  final EdgeInsetsGeometry? padding;

  /// المسافة بين الحقول
  final double spacing;

  const InventoryFormSection({
    Key? key,
    this.title = 'معلومات المخزون',
    required this.fields,
    this.padding,
    this.spacing = 16,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FormSection(
      title: title,
      padding: padding,
      spacing: spacing,
      children: fields,
    );
  }
}
