import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/models/warehouse.dart';

import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';
import '../presenters/warehouse_presenter.dart';

/// شاشة لوحة معلومات المخزون
class InventoryDashboardScreen extends StatefulWidget {
  const InventoryDashboardScreen({Key? key}) : super(key: key);

  @override
  State<InventoryDashboardScreen> createState() =>
      _InventoryDashboardScreenState();
}

class _InventoryDashboardScreenState extends State<InventoryDashboardScreen> {
  // المقدمون
  // نستخدم _inventoryPresenter في التعليقات للإشارة إلى الاستخدام المستقبلي
  // عند تنفيذ الوظائف الفعلية
  late WarehousePresenter _warehousePresenter;

  // المستودع المحدد
  Warehouse? _selectedWarehouse;

  // نطاق التاريخ
  DateTime _fromDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _toDate = DateTime.now();

  // حالة التحميل
  bool _isLoading = false;

  // بيانات الإحصائيات
  Map<String, dynamic> _statistics = {};

  // بيانات المنتجات منخفضة المخزون
  List<Map<String, dynamic>> _lowStockProducts = [];

  // بيانات المنتجات النافدة
  List<Map<String, dynamic>> _outOfStockProducts = [];

  // بيانات المنتجات الأكثر مبيعاً
  List<Map<String, dynamic>> _topSellingProducts = [];

  @override
  void initState() {
    super.initState();

    // تهيئة المقدمين
    // في الإصدار الكامل، سنحتاج إلى تهيئة _inventoryPresenter
    // _inventoryPresenter = AppProviders.getLazyPresenter<InventoryPresenter>(() => InventoryPresenter());

    _warehousePresenter = AppProviders.getLazyPresenter<WarehousePresenter>(
        () => WarehousePresenter());

    // تحميل البيانات
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل المستودعات
      await _warehousePresenter.loadWarehouses();

      // تحميل الإحصائيات
      await _loadStatistics();

      // تحميل المنتجات منخفضة المخزون
      await _loadLowStockProducts();

      // تحميل المنتجات النافدة
      await _loadOutOfStockProducts();

      // تحميل المنتجات الأكثر مبيعاً
      await _loadTopSellingProducts();
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل الإحصائيات
  Future<void> _loadStatistics() async {
    // في الإصدار الكامل، سيتم استدعاء الدالة الفعلية مع معرف المستودع
    // if (_selectedWarehouse != null) {
    //   final statistics = await _inventoryPresenter.getInventoryStatistics(
    //     warehouseId: _selectedWarehouse!.id,
    //   );
    // }

    // بيانات تجريبية للعرض
    final statistics = {
      'total_products': 120,
      'total_value': 25000.0,
      'low_stock_count': 15,
      'out_of_stock_count': 5,
    };

    setState(() {
      _statistics = statistics;
    });
  }

  /// تحميل المنتجات منخفضة المخزون
  Future<void> _loadLowStockProducts() async {
    // في الإصدار الكامل، سيتم استدعاء الدالة الفعلية مع معرف المستودع
    // if (_selectedWarehouse != null) {
    //   final products = await _inventoryPresenter.getLowStockProducts(
    //     warehouseId: _selectedWarehouse!.id,
    //     limit: 5,
    //   );
    // }

    // بيانات تجريبية للعرض
    final products = [
      {
        'product_id': '1',
        'product_name': 'منتج 1',
        'product_code': 'SKU001',
        'quantity': 5.0,
        'min_quantity': 10.0,
      },
      {
        'product_id': '2',
        'product_name': 'منتج 2',
        'product_code': 'SKU002',
        'quantity': 8.0,
        'min_quantity': 15.0,
      },
      {
        'product_id': '3',
        'product_name': 'منتج 3',
        'product_code': 'SKU003',
        'quantity': 3.0,
        'min_quantity': 10.0,
      },
    ];

    setState(() {
      _lowStockProducts = products;
    });
  }

  /// تحميل المنتجات النافدة
  Future<void> _loadOutOfStockProducts() async {
    // في الإصدار الكامل، سيتم استدعاء الدالة الفعلية مع معرف المستودع
    // if (_selectedWarehouse != null) {
    //   final products = await _inventoryPresenter.getOutOfStockProducts(
    //     warehouseId: _selectedWarehouse!.id,
    //     limit: 5,
    //   );
    // }

    // بيانات تجريبية للعرض
    final products = [
      {
        'product_id': '4',
        'product_name': 'منتج 4',
        'product_code': 'SKU004',
        'quantity': 0.0,
        'min_quantity': 5.0,
      },
      {
        'product_id': '5',
        'product_name': 'منتج 5',
        'product_code': 'SKU005',
        'quantity': 0.0,
        'min_quantity': 8.0,
      },
    ];

    setState(() {
      _outOfStockProducts = products;
    });
  }

  /// تحميل المنتجات الأكثر مبيعاً
  Future<void> _loadTopSellingProducts() async {
    // في الإصدار الكامل، سيتم استدعاء الدالة الفعلية مع معرف المستودع
    // if (_selectedWarehouse != null) {
    //   final products = await _inventoryPresenter.getTopSellingProducts(
    //     warehouseId: _selectedWarehouse!.id,
    //     fromDate: _fromDate,
    //     toDate: _toDate,
    //     limit: 5,
    //   );
    // }

    // بيانات تجريبية للعرض
    final products = [
      {
        'product_id': '1',
        'product_name': 'منتج 1',
        'total_quantity': 50.0,
        'total_amount': 5000.0,
      },
      {
        'product_id': '2',
        'product_name': 'منتج 2',
        'total_quantity': 40.0,
        'total_amount': 4000.0,
      },
      {
        'product_id': '3',
        'product_name': 'منتج 3',
        'total_quantity': 30.0,
        'total_amount': 3000.0,
      },
      {
        'product_id': '4',
        'product_name': 'منتج 4',
        'total_quantity': 20.0,
        'total_amount': 2000.0,
      },
      {
        'product_id': '5',
        'product_name': 'منتج 5',
        'total_quantity': 10.0,
        'total_amount': 1000.0,
      },
    ];

    setState(() {
      _topSellingProducts = products;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeLayout(
      title: 'لوحة معلومات المخزون',
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadData,
          tooltip: 'تحديث',
        ),
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFilterDialog,
          tooltip: 'تصفية',
        ),
        IconButton(
          icon: const Icon(Icons.assessment),
          onPressed: _navigateToReports,
          tooltip: 'التقارير',
        ),
      ],
      body: _isLoading
          ? const Center(child: AkLoadingIndicator())
          : _buildDashboard(),
      child: Container(),
    );
  }

  /// بناء لوحة المعلومات
  Widget _buildDashboard() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildStatisticsCards(),
          const SizedBox(height: 24),
          _buildCharts(),
          const SizedBox(height: 24),
          _buildProductTables(),
        ],
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نظرة عامة على المخزون',
              style: AppTypography.lightTextTheme.headlineLarge,
            ),
            const SizedBox(height: 8),
            Text(
              _selectedWarehouse != null
                  ? 'المستودع: ${_selectedWarehouse!.name}'
                  : 'جميع المستودعات',
              style: AppTypography.lightTextTheme.titleMedium,
            ),
            const SizedBox(height: 4),
            Text(
              'الفترة: ${_formatDate(_fromDate)} - ${_formatDate(_toDate)}',
              style: AppTypography.lightTextTheme.titleSmall,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatisticsCards() {
    final totalProducts = _statistics['total_products'] ?? 0;
    final totalValue = _statistics['total_value'] ?? 0.0;
    final lowStockCount = _statistics['low_stock_count'] ?? 0;
    final outOfStockCount = _statistics['out_of_stock_count'] ?? 0;

    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildStatCard(
          title: 'إجمالي المنتجات',
          value: totalProducts.toString(),
          icon: Icons.inventory_2,
          color: AppColors.lightTextSecondary,
        ),
        _buildStatCard(
          title: 'قيمة المخزون',
          value: NumberFormat.currency(
            locale: 'ar',
            symbol: 'ر.س',
            decimalDigits: 2,
          ).format(totalValue),
          icon: Icons.attach_money,
          color: AppColors.lightTextSecondary,
        ),
        _buildStatCard(
          title: 'منتجات منخفضة المخزون',
          value: lowStockCount.toString(),
          icon: Icons.warning_amber,
          color: AppColors.lightTextSecondary,
        ),
        _buildStatCard(
          title: 'منتجات نافدة',
          value: outOfStockCount.toString(),
          icon: Icons.remove_shopping_cart,
          color: AppColors.lightTextSecondary,
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48,
              color: color,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: AppTypography.lightTextTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: AppTypography.lightTextTheme.headlineMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ) ??
                  AppTypography(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الرسوم البيانية
  Widget _buildCharts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تحليل المخزون',
          style: AppTypography.lightTextTheme.headlineMedium,
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'المنتجات الأكثر مبيعاً',
                  style: AppTypography.lightTextTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ) ??
                      const AppTypography(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 200,
                  child: _topSellingProducts.isEmpty
                      ? const Center(child: Text('لا توجد بيانات للعرض'))
                      : _buildTopSellingChart(),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء رسم بياني للمنتجات الأكثر مبيعاً
  Widget _buildTopSellingChart() {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: 100, // قيمة ثابتة للعرض التجريبي
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final product = _topSellingProducts[groupIndex];
              return BarTooltipItem(
                '${product['product_name']}\n${rod.toY.toStringAsFixed(0)}',
                const AppTypography(color: AppColors.onPrimary),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value >= 0 && value < _topSellingProducts.length) {
                  final product = _topSellingProducts[value.toInt()];
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      product['product_name'].toString().length > 10
                          ? '${product['product_name'].toString().substring(0, 8)}...'
                          : product['product_name'].toString(),
                      style: const AppTypography(
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
              reservedSize: 40,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const AppTypography(
                    color: AppColors.lightTextSecondary,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.left,
                );
              },
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(
          show: false,
        ),
        barGroups: List.generate(_topSellingProducts.length, (index) {
          final product = _topSellingProducts[index];
          final quantity = product['total_quantity'] as double;

          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: quantity,
                color: AppColors.lightTextSecondary,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
              ),
            ],
          );
        }),
        gridData: const FlGridData(
          show: true,
          drawVerticalLine: false,
        ),
      ),
    );
  }

  /// بناء جداول المنتجات
  Widget _buildProductTables() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildProductTable(
          title: 'المنتجات منخفضة المخزون',
          products: _lowStockProducts,
          emptyMessage: 'لا توجد منتجات منخفضة المخزون',
          showQuantityColor: true,
        ),
        const SizedBox(height: 24),
        _buildProductTable(
          title: 'المنتجات النافدة',
          products: _outOfStockProducts,
          emptyMessage: 'لا توجد منتجات نافدة',
          showQuantityColor: false,
        ),
      ],
    );
  }

  /// بناء جدول منتجات
  Widget _buildProductTable({
    required String title,
    required List<Map<String, dynamic>> products,
    required String emptyMessage,
    required bool showQuantityColor,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTypography.lightTextTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ) ??
                  const AppTypography(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (products.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    emptyMessage,
                    style: const AppTypography(
                        color: AppColors.lightTextSecondary),
                  ),
                ),
              )
            else
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  columns: const [
                    DataColumn(label: Text('المنتج')),
                    DataColumn(label: Text('الكود')),
                    DataColumn(label: Text('الكمية الحالية')),
                    DataColumn(label: Text('الحد الأدنى')),
                  ],
                  rows: products.map((product) {
                    final quantity = product['quantity'] as double;
                    final minQuantity = product['min_quantity'] as double;

                    return DataRow(
                      cells: [
                        DataCell(Text(product['product_name'])),
                        DataCell(Text(product['product_code'])),
                        DataCell(
                          Text(
                            quantity.toString(),
                            style: showQuantityColor
                                ? AppTypography(
                                    color: quantity <= 0
                                        ? AppColors.error
                                        : quantity < minQuantity
                                            ? AppColors.warning
                                            : AppColors.success,
                                    fontWeight: FontWeight.bold,
                                  )
                                : null,
                          ),
                        ),
                        DataCell(Text(minQuantity.toString())),
                      ],
                    );
                  }).toList(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  /// عرض مربع حوار التصفية
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية البيانات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // المستودع
            DropdownButtonFormField<Warehouse?>(
              decoration: const InputDecoration(
                labelText: 'المستودع',
                border: OutlineInputBorder(),
              ),
              value: _selectedWarehouse,
              items: [
                const DropdownMenuItem<Warehouse?>(
                  value: null,
                  child: Text('جميع المستودعات'),
                ),
                ..._warehousePresenter.warehouses.map((warehouse) {
                  return DropdownMenuItem<Warehouse?>(
                    value: warehouse,
                    child: Text(warehouse.name),
                  );
                }).toList(),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedWarehouse = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // من تاريخ
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'من تاريخ',
                prefixIcon: Icon(Icons.calendar_today),
              ),
              readOnly: true,
              controller: TextEditingController(
                text: DateFormat('yyyy-MM-dd').format(_fromDate),
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _fromDate,
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    _fromDate = date;
                  });
                }
              },
            ),
            const SizedBox(height: 16),

            // إلى تاريخ
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'إلى تاريخ',
                prefixIcon: Icon(Icons.calendar_today),
              ),
              readOnly: true,
              controller: TextEditingController(
                text: DateFormat('yyyy-MM-dd').format(_toDate),
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _toDate,
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    _toDate = date;
                  });
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _loadData();
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  /// الانتقال إلى شاشة التقارير
  void _navigateToReports() {
    // في الإصدار الكامل، سيتم استدعاء الشاشة الفعلية
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم الانتقال إلى شاشة التقارير'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
