import 'dart:convert';
import 'package:uuid/uuid.dart';
import '../../../core/models/base_model.dart';
import '../../../core/auth/models/user_role.dart';
import 'user_group.dart';
import '../../branches/models/branch.dart';

/// نموذج المستخدم المحسن
/// تم إزالة الحقول المكررة (roleName, userGroupName, branchName)
/// واستبدالها بكائنات مرتبطة يتم الحصول عليها عبر JOIN
class User extends BaseModel {
  final String username;
  final String? password; // يجب أن تكون مشفرة قبل التخزين
  final String fullName;
  final String? email;
  final String? phone;
  final String? avatar;

  // معرفات العلاقات (فقط المعرفات - بدون أسماء مكررة)
  final String? roleId;
  final String? userGroupId;
  final String? branchId;
  final String? branchAccessType; // نوع الوصول للفروع

  // الكائنات المرتبطة (يتم الحصول عليها عبر JOIN)
  final UserRole? role;
  final UserGroup? userGroup;
  final Branch? branch;

  final bool isActive;
  final DateTime? lastLogin;

  User({
    String? id,
    required this.username,
    this.password,
    required this.fullName,
    this.email,
    this.phone,
    this.avatar,
    this.roleId,
    this.role,
    this.userGroupId,
    this.userGroup,
    this.branchId,
    this.branchAccessType,
    this.branch,
    this.isActive = true,
    this.lastLogin,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا المستخدم مع استبدال الحقول المحددة بقيم جديدة
  User copyWith({
    String? id,
    String? username,
    String? password,
    String? fullName,
    String? email,
    String? phone,
    String? avatar,
    String? roleId,
    UserRole? role,
    String? userGroupId,
    UserGroup? userGroup,
    String? branchId,
    String? branchAccessType,
    Branch? branch,
    bool? isActive,
    DateTime? lastLogin,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      password: password ?? this.password,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      roleId: roleId ?? this.roleId,
      role: role ?? this.role,
      userGroupId: userGroupId ?? this.userGroupId,
      userGroup: userGroup ?? this.userGroup,
      branchId: branchId ?? this.branchId,
      branchAccessType: branchAccessType ?? this.branchAccessType,
      branch: branch ?? this.branch,
      isActive: isActive ?? this.isActive,
      lastLogin: lastLogin ?? this.lastLogin,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل المستخدم إلى Map (فقط المعرفات - بدون أسماء مكررة)
  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'username': username,
      'password': password,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'role_id': roleId,
      'user_group_id': userGroupId,
      'branch_id': branchId,
      'branch_access_type': branchAccessType,
      'is_active': isActive ? 1 : 0,
      'last_login': lastLogin?.toIso8601String(),
    });
    return map;
  }

  /// إنشاء مستخدم من Map (مع دعم البيانات المرتبطة من JOIN)
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      username: map['username'] ?? '',
      password: map['password'],
      fullName: map['full_name'] ?? '',
      email: map['email'],
      phone: map['phone'],
      avatar: map['avatar'],
      roleId: map['role_id'] ?? map['role'], // دعم الإصدارات القديمة
      role: map['role'] != null && map['role'] is Map
          ? UserRole.fromMap(map['role'])
          : null,
      userGroupId: map['user_group_id'],
      userGroup: map['user_group'] != null && map['user_group'] is Map
          ? UserGroup.fromMap(map['user_group'])
          : null,
      branchId: map['branch_id'],
      branchAccessType:
          map['branch_access_type'] ?? 'single_branch', // القيمة الافتراضية
      branch: map['branch'] != null && map['branch'] is Map
          ? Branch.fromMap(map['branch'])
          : null,
      isActive: map['is_active'] == 1,
      lastLogin:
          map['last_login'] != null ? DateTime.parse(map['last_login']) : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل المستخدم إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء مستخدم من JSON
  factory User.fromJson(String source) => User.fromMap(jsonDecode(source));

  /// إنشاء نسخة من المستخدم بدون كلمة المرور
  User withoutPassword() {
    return copyWith(password: null);
  }

  /// خصائص مساعدة للحصول على الأسماء من الكائنات المرتبطة
  /// (للتوافق مع الكود القديم)
  String? get roleName => role?.displayName ?? role?.name;
  String? get userGroupName => userGroup?.name;
  String? get branchName => branch?.name;

  /// إنشاء مستخدم مسؤول افتراضي
  factory User.defaultAdmin() {
    return User(
      username: 'admin',
      password: 'admin123', // سيتم تشفيرها في خدمة المصادقة
      fullName: 'مدير النظام',
      email: '<EMAIL>',
      roleId: 'admin',
      isActive: true,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, username: $username, fullName: $fullName, roleId: $roleId, userGroupId: $userGroupId, branchId: $branchId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
