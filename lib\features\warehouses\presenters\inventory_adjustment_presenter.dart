import 'package:flutter/material.dart';
import '../../../core/models/product.dart';
import '../../../core/models/warehouse.dart';
import '../../../core/models/inventory_adjustment.dart';
import '../../../core/models/inventory_adjustment_item.dart';
import '../../../core/services/product_service.dart';
import '../../../core/services/warehouse_service.dart';
import '../../../core/services/inventory_adjustment_service.dart';
import '../../../core/utils/error_tracker.dart';

/// مقدم تعديل المخزون
/// يتعامل مع منطق إدارة تعديلات المخزون
///
/// هذا المقدم مسؤول عن:
/// - تحميل وإدارة تعديلات المخزون (زيادة، نقصان، جرد)
/// - إضافة تعديلات جديدة للمخزون
/// - تحديث تعديلات المخزون الموجودة
/// - حذف تعديلات المخزون
/// - البحث في تعديلات المخزون
/// - تحميل المستودعات والمنتجات المرتبطة بتعديلات المخزون
class InventoryAdjustmentPresenter extends ChangeNotifier {
  final InventoryAdjustmentService _adjustmentService =
      InventoryAdjustmentService();
  final ProductService _productService = ProductService();
  final WarehouseService _warehouseService = WarehouseService();

  List<InventoryAdjustment> _adjustments = [];
  List<Product> _products = [];
  List<Warehouse> _warehouses = [];

  bool _isLoading = false;
  String? _errorMessage;

  /// الحصول على قائمة التعديلات
  List<InventoryAdjustment> get adjustments => _adjustments;

  /// الحصول على قائمة المنتجات
  List<Product> get products => _products;

  /// الحصول على قائمة المستودعات
  List<Warehouse> get warehouses => _warehouses;

  /// التحقق من حالة التحميل
  bool get isLoading => _isLoading;

  /// الحصول على رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// تحميل التعديلات
  Future<void> loadAdjustments() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // الحصول على التعديلات من خدمة تعديلات المخزون
      final adjustments = await _adjustmentService.getAllAdjustments();

      _adjustments = adjustments;
      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل التعديلات: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل التعديلات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
    }
  }

  /// تحميل المنتجات
  Future<void> loadProducts() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // الحصول على المنتجات من خدمة المنتجات
      final products = await _productService.getAllProducts();

      _products = products;
      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المنتجات: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل المنتجات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
    }
  }

  /// تحميل المستودعات
  Future<void> loadWarehouses() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // الحصول على المستودعات من خدمة المستودعات
      final warehouses = await _warehouseService.getAllWarehouses();

      _warehouses = warehouses;
      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المستودعات: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل المستودعات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
    }
  }

  /// إضافة تعديل جديد
  Future<InventoryAdjustment?> addAdjustment(
      InventoryAdjustment adjustment, List<InventoryAdjustmentItem> items,
      {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // التحقق من صحة البيانات
      if (items.isEmpty) {
        _isLoading = false;
        _errorMessage = 'يجب إضافة منتج واحد على الأقل';
        notifyListeners();
        return null;
      }

      // إنشاء رقم مرجعي إذا لم يكن موجودًا
      String referenceNumber = adjustment.referenceNumber ?? '';
      if (referenceNumber.isEmpty) {
        final now = DateTime.now();
        referenceNumber =
            'ADJ-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${_adjustments.length + 1}';
      }

      // إنشاء نسخة جديدة من التعديل مع الرقم المرجعي
      final newAdjustment =
          adjustment.copyWith(referenceNumber: referenceNumber);

      // إضافة التعديل باستخدام خدمة تعديلات المخزون
      final addedAdjustment = await _adjustmentService.addAdjustment(
        newAdjustment,
        items,
        userId: userId,
      );

      if (addedAdjustment == null) {
        _isLoading = false;
        _errorMessage = 'فشل في إضافة التعديل';
        notifyListeners();
        return null;
      }

      // تحديث قائمة التعديلات
      await loadAdjustments();

      return addedAdjustment;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء إضافة التعديل: $e';
      ErrorTracker.captureError(
        'خطأ في إضافة تعديل',
        error: e,
        stackTrace: stackTrace,
        context: {'adjustment': adjustment.toString()},
      );
      notifyListeners();
      return null;
    }
  }

  /// تحديث تعديل
  Future<bool> updateAdjustment(
      InventoryAdjustment adjustment, List<InventoryAdjustmentItem> items,
      {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // التحقق من صحة البيانات
      if (items.isEmpty) {
        _isLoading = false;
        _errorMessage = 'يجب إضافة منتج واحد على الأقل';
        notifyListeners();
        return false;
      }

      // تحديث التعديل باستخدام خدمة تعديلات المخزون
      final success = await _adjustmentService.updateAdjustment(
        adjustment,
        items,
        userId: userId,
      );

      if (success) {
        // تحديث قائمة التعديلات
        await loadAdjustments();
      } else {
        _isLoading = false;
        _errorMessage = 'فشل في تحديث التعديل';
        notifyListeners();
      }

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحديث التعديل: $e';
      ErrorTracker.captureError(
        'خطأ في تحديث تعديل',
        error: e,
        stackTrace: stackTrace,
        context: {'adjustment': adjustment.toString()},
      );
      notifyListeners();
      return false;
    }
  }

  /// حذف تعديل المخزون
  ///
  /// يقوم هذا الإجراء بحذف تعديل المخزون من قاعدة البيانات
  /// ويمكن استدعاؤه بإحدى طريقتين:
  /// 1. تمرير معرف التعديل كسلسلة نصية (String)
  /// 2. تمرير كائن التعديل نفسه (InventoryAdjustment)
  ///
  /// المعاملات:
  /// - adjustmentOrId: معرف التعديل أو كائن التعديل نفسه
  /// - userId: معرف المستخدم الذي يقوم بالحذف (اختياري)
  ///
  /// يعيد:
  /// - true: إذا تم الحذف بنجاح
  /// - false: إذا فشلت عملية الحذف
  Future<bool> deleteAdjustment(dynamic adjustmentOrId,
      {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      String adjustmentId;

      // تحديد معرف التعديل بناءً على نوع المعامل
      if (adjustmentOrId is String) {
        // إذا كان المعامل هو معرف (سلسلة نصية)
        adjustmentId = adjustmentOrId;
      } else if (adjustmentOrId is InventoryAdjustment) {
        // إذا كان المعامل هو كائن تعديل المخزون
        if (adjustmentOrId.id.isEmpty) {
          _isLoading = false;
          _errorMessage = 'معرف التعديل غير صالح';
          notifyListeners();
          return false;
        }
        adjustmentId = adjustmentOrId.id;
      } else {
        // إذا كان المعامل من نوع غير متوقع
        _isLoading = false;
        _errorMessage = 'نوع معامل غير صالح';
        notifyListeners();
        return false;
      }

      // حذف التعديل باستخدام خدمة تعديلات المخزون
      final success = await _adjustmentService.deleteAdjustment(adjustmentId,
          userId: userId);

      if (success) {
        // تحديث قائمة التعديلات
        await loadAdjustments();
      } else {
        _isLoading = false;
        _errorMessage = 'فشل في حذف التعديل';
        notifyListeners();
      }

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء حذف التعديل: $e';
      ErrorTracker.captureError(
        'خطأ في حذف تعديل',
        error: e,
        stackTrace: stackTrace,
        context: {'adjustmentOrId': adjustmentOrId.toString()},
      );
      notifyListeners();
      return false;
    }
  }

  /// الحصول على اسم المنتج بواسطة المعرف
  String getProductName(String productId) {
    final product = _products.firstWhere(
      (p) => p.id == productId,
      orElse: () => Product(
        id: productId,
        name: 'منتج غير معروف',
        barcode: '',
        minStock: 0,
        salePrice: 0,
      ),
    );
    return product.name;
  }

  /// الحصول على اسم المستودع بواسطة المعرف
  String getWarehouseName(String warehouseId) {
    final warehouse = _warehouses.firstWhere(
      (w) => w.id == warehouseId,
      orElse: () => Warehouse(
        id: warehouseId,
        name: 'مستودع غير معروف',
        code: 'UNKNOWN',
      ),
    );
    return warehouse.name;
  }

  /// الحصول على اسم نوع التعديل
  String getAdjustmentTypeName(String type) {
    switch (type) {
      case 'increase':
        return 'زيادة';
      case 'decrease':
        return 'نقصان';
      case 'inventory':
        return 'جرد';
      default:
        return 'غير معروف';
    }
  }

  /// البحث عن تعديلات المخزون
  ///
  /// يقوم هذا الإجراء بالبحث في تعديلات المخزون باستخدام نص البحث المدخل
  /// ويبحث في:
  /// - رقم المرجع (referenceNumber)
  /// - الملاحظات (notes)
  /// - اسم المستودع (warehouse name)
  ///
  /// المعاملات:
  /// - query: نص البحث المراد البحث عنه
  ///
  /// يعيد:
  /// - قائمة بتعديلات المخزون التي تطابق معايير البحث
  /// - قائمة فارغة إذا لم يتم العثور على نتائج أو حدث خطأ
  Future<List<InventoryAdjustment>> searchAdjustments(String query) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // البحث عن التعديلات باستخدام خدمة تعديلات المخزون
      final adjustments = await _adjustmentService.searchAdjustments(query);

      _isLoading = false;
      notifyListeners();

      return adjustments;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء البحث عن التعديلات: $e';
      ErrorTracker.captureError(
        'خطأ في البحث عن التعديلات',
        error: e,
        stackTrace: stackTrace,
        context: {'query': query},
      );
      notifyListeners();
      return [];
    }
  }
}
