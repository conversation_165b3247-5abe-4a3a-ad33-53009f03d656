import 'package:flutter/material.dart';
import '../theme/index.dart';
import 'akbuttons.dart';

// ═══════════════════════════════════════════════════════════════════════════════
// 📋 نظام القوائم الموحد والشامل (AK Lists System)
// ═══════════════════════════════════════════════════════════════════════════════

/// نظام شامل وموحد لجميع أنواع القوائم في تطبيق تاجر بلس
///
/// **المميزات:**
/// - تصميم موحد ومتناسق لجميع القوائم
/// - دعم كامل للوضع المظلم/الفاتح باستخدام `Theme.of(context).brightness`
/// - عدم وجود قيم صريحة - استخدام `AppColors.*`, `AppDimensions.*`, `AppTypography.*` فقط
/// - تحميل كسول للقوائم الطويلة
/// - تعليقات شاملة باللغة العربية
/// - أحجام متعددة قابلة للتخصيص
/// - دعم التحديث بالسحب والبحث
/// - قوائم متخصصة للمشروع التجاري اليمني

// ───────────────────────────────────────────────────────────────────────────────
// ● الأنواع والثوابت
// ───────────────────────────────────────────────────────────────────────────────

/// أنواع القوائم المتاحة
enum AkListType {
  /// قائمة عادية
  normal,

  /// قائمة شبكية
  grid,

  /// قائمة قابلة للبحث
  searchable,

  /// قائمة مع فئات
  categorized,
}

/// أحجام القوائم المتاحة
enum AkListSize {
  /// صغير - للقوائم المدمجة
  small,

  /// متوسط - للاستخدام العادي
  medium,

  /// كبير - للعرض الكامل
  large,
}

/// أنماط القوائم
enum AkListStyle {
  /// نمط بسيط
  simple,

  /// نمط مع بطاقات
  card,

  /// نمط مع فواصل
  divided,

  /// نمط احترافي
  professional,
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📋 القسم الأول: القائمة الأساسية الموحدة (AkListView)
// ═══════════════════════════════════════════════════════════════════════════════

/// قائمة أساسية موحدة مع تصميم متناسق
///
/// **المميزات:**
/// - دعم التحديث بالسحب
/// - حالات فارغة وخطأ وتحميل
/// - تصميم متجاوب
/// - تكامل مع نظام الحالات
/// - تحميل كسول للبيانات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkListView<Product>(
///   items: products,
///   itemBuilder: (product) => ProductCard(product: product),
///   onRefresh: () => loadProducts(),
///   emptyMessage: 'لا توجد منتجات',
///   onEmptyAction: () => Navigator.push(context, AddProductPage()),
/// )
/// ```
class AkListView<T> extends StatefulWidget {
  /// قائمة العناصر
  final List<T> items;

  /// دالة بناء العنصر
  final Widget Function(T item, int index) itemBuilder;

  /// حجم القائمة
  final AkListSize size;

  /// نمط القائمة
  final AkListStyle style;

  /// دالة التحديث بالسحب
  final Future<void> Function()? onRefresh;

  /// هل يتم عرض مؤشر التحميل
  final bool isLoading;

  /// رسالة الخطأ
  final String? errorMessage;

  /// دالة إعادة المحاولة
  final VoidCallback? onRetry;

  /// رسالة القائمة الفارغة
  final String emptyMessage;

  /// أيقونة القائمة الفارغة
  final IconData emptyIcon;

  /// نص زر الإجراء للقائمة الفارغة
  final String? emptyActionText;

  /// دالة الإجراء للقائمة الفارغة
  final VoidCallback? onEmptyAction;

  /// هل يتم عرض الفواصل
  final bool showDividers;

  /// المسافة الداخلية
  final EdgeInsetsGeometry? padding;

  /// هل يتم تقليص القائمة
  final bool shrinkWrap;

  /// خصائص التمرير
  final ScrollPhysics? physics;

  /// وحدة التحكم في التمرير
  final ScrollController? controller;

  const AkListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.size = AkListSize.medium,
    this.style = AkListStyle.simple,
    this.onRefresh,
    this.isLoading = false,
    this.errorMessage,
    this.onRetry,
    this.emptyMessage = 'لا توجد بيانات',
    this.emptyIcon = Icons.inbox_outlined,
    this.emptyActionText,
    this.onEmptyAction,
    this.showDividers = false,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.controller,
  });

  @override
  State<AkListView<T>> createState() => _AkListViewState<T>();
}

class _AkListViewState<T> extends State<AkListView<T>> {
  /// الحصول على المسافة الداخلية حسب الحجم
  EdgeInsetsGeometry _getPadding() {
    if (widget.padding != null) return widget.padding!;

    switch (widget.size) {
      case AkListSize.small:
        return EdgeInsets.all(AppDimensions.smallMargin);
      case AkListSize.medium:
        return EdgeInsets.all(AppDimensions.defaultMargin);
      case AkListSize.large:
        return EdgeInsets.all(AppDimensions.largeMargin);
    }
  }

  /// بناء عنصر القائمة مع النمط المناسب
  Widget _buildListItem(T item, int index) {
    Widget itemWidget = widget.itemBuilder(item, index);

    switch (widget.style) {
      case AkListStyle.simple:
        return itemWidget;

      case AkListStyle.card:
        return Card(
          margin: EdgeInsets.symmetric(
            horizontal: AppDimensions.smallMargin,
            vertical: AppDimensions.tinyMargin,
          ),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          ),
          child: itemWidget,
        );

      case AkListStyle.divided:
        return Column(
          children: [
            itemWidget,
            if (index < widget.items.length - 1)
              Divider(
                height: 1,
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppColors.darkBorder
                    : AppColors.lightBorder,
              ),
          ],
        );

      case AkListStyle.professional:
        return Container(
          margin: EdgeInsets.symmetric(
            horizontal: AppDimensions.smallMargin,
            vertical: AppDimensions.tinyMargin,
          ),
          padding: EdgeInsets.all(AppDimensions.defaultMargin),
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? AppColors.darkSurfaceVariant
                : AppColors.lightSurfaceVariant,
            borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
            border: Border.all(
              color: Theme.of(context).brightness == Brightness.dark
                  ? AppColors.darkBorder
                  : AppColors.lightBorder,
            ),
          ),
          child: itemWidget,
        );
    }
  }

  /// بناء القائمة الأساسية
  Widget _buildListView() {
    if (widget.showDividers && widget.style == AkListStyle.simple) {
      return ListView.separated(
        controller: widget.controller,
        itemCount: widget.items.length,
        padding: _getPadding(),
        shrinkWrap: widget.shrinkWrap,
        physics: widget.physics,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          color: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkBorder
              : AppColors.lightBorder,
        ),
        itemBuilder: (context, index) =>
            _buildListItem(widget.items[index], index),
      );
    } else {
      return ListView.builder(
        controller: widget.controller,
        itemCount: widget.items.length,
        padding: _getPadding(),
        shrinkWrap: widget.shrinkWrap,
        physics: widget.physics,
        itemBuilder: (context, index) =>
            _buildListItem(widget.items[index], index),
      );
    }
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: AppColors.primary,
          ),
          SizedBox(height: AppDimensions.defaultMargin),
          Text(
            'جاري التحميل...',
            style: AppTypography.createCustomStyle(
              fontSize: AppTypography.fontSizeMedium,
              color: Theme.of(context).brightness == Brightness.dark
                  ? AppColors.darkTextSecondary
                  : AppColors.lightTextSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildAkErrorState() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.largeMargin),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            SizedBox(height: AppDimensions.defaultMargin),
            Text(
              widget.errorMessage!,
              textAlign: TextAlign.center,
              style: AppTypography.createCustomStyle(
                fontSize: AppTypography.fontSizeMedium,
                color: isDark
                    ? AppColors.darkTextPrimary
                    : AppColors.lightTextPrimary,
              ),
            ),
            if (widget.onRetry != null) ...[
              SizedBox(height: AppDimensions.defaultMargin),
              AkButtons.retry(onPressed: widget.onRetry!),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء حالة القائمة الفارغة
  Widget _buildAkEmptyState() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.largeMargin),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              widget.emptyIcon,
              size: 64,
              color: isDark
                  ? AppColors.darkTextSecondary
                  : AppColors.lightTextSecondary,
            ),
            SizedBox(height: AppDimensions.defaultMargin),
            Text(
              widget.emptyMessage,
              textAlign: TextAlign.center,
              style: AppTypography.createCustomStyle(
                fontSize: AppTypography.fontSizeMedium,
                color: isDark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              ),
            ),
            if (widget.emptyActionText != null &&
                widget.onEmptyAction != null) ...[
              SizedBox(height: AppDimensions.defaultMargin),
              AkButtons.primary(
                text: widget.emptyActionText!,
                onPressed: widget.onEmptyAction!,
              ),
            ],
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // عرض حالة التحميل
    if (widget.isLoading) {
      return _buildLoadingState();
    }

    // عرض حالة الخطأ
    if (widget.errorMessage != null) {
      return _buildAkErrorState();
    }

    // عرض حالة القائمة الفارغة
    if (widget.items.isEmpty) {
      return _buildAkEmptyState();
    }

    // عرض القائمة مع التحديث بالسحب
    if (widget.onRefresh != null) {
      return RefreshIndicator(
        onRefresh: widget.onRefresh!,
        color: AppColors.primary,
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? AppColors.darkSurface
            : AppColors.lightSurface,
        child: _buildListView(),
      );
    }

    return _buildListView();
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🔍 القسم الثاني: القائمة القابلة للبحث (AkSearchableListView)
// ═══════════════════════════════════════════════════════════════════════════════

/// قائمة قابلة للبحث مع شريط بحث مدمج
class AkSearchableListView<T> extends StatefulWidget {
  /// قائمة العناصر
  final List<T> items;

  /// دالة بناء العنصر
  final Widget Function(T item, int index) itemBuilder;

  /// دالة البحث
  final bool Function(T item, String query) searchFilter;

  /// تلميح البحث
  final String searchHint;

  /// رسالة عدم وجود نتائج بحث
  final String noResultsMessage;

  /// باقي الخصائص مثل AkListView
  final AkListSize size;
  final AkListStyle style;
  final Future<void> Function()? onRefresh;
  final bool isLoading;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final String emptyMessage;
  final IconData emptyIcon;
  final String? emptyActionText;
  final VoidCallback? onEmptyAction;

  const AkSearchableListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    required this.searchFilter,
    this.searchHint = 'ابحث هنا...',
    this.noResultsMessage = 'لا توجد نتائج للبحث',
    this.size = AkListSize.medium,
    this.style = AkListStyle.simple,
    this.onRefresh,
    this.isLoading = false,
    this.errorMessage,
    this.onRetry,
    this.emptyMessage = 'لا توجد بيانات',
    this.emptyIcon = Icons.inbox_outlined,
    this.emptyActionText,
    this.onEmptyAction,
  });

  @override
  State<AkSearchableListView<T>> createState() =>
      _AkSearchableListViewState<T>();
}

class _AkSearchableListViewState<T> extends State<AkSearchableListView<T>> {
  final TextEditingController _searchController = TextEditingController();
  List<T> _filteredItems = [];
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void didUpdateWidget(AkSearchableListView<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      _filterItems();
    }
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterItems();
    });
  }

  void _filterItems() {
    if (_searchQuery.isEmpty) {
      _filteredItems = widget.items;
    } else {
      _filteredItems = widget.items
          .where((item) => widget.searchFilter(item, _searchQuery))
          .toList();
    }
  }

  Widget _buildSearchBar() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.all(AppDimensions.defaultMargin),
      child: TextField(
        controller: _searchController,
        style: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color:
              isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
        ),
        decoration: InputDecoration(
          hintText: widget.searchHint,
          prefixIcon: Icon(
            Icons.search,
            color: isDark
                ? AppColors.darkTextSecondary
                : AppColors.lightTextSecondary,
          ),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: isDark
                        ? AppColors.darkTextSecondary
                        : AppColors.lightTextSecondary,
                  ),
                  onPressed: () {
                    _searchController.clear();
                  },
                )
              : null,
          filled: true,
          fillColor: isDark
              ? AppColors.darkSurfaceVariant
              : AppColors.lightSurfaceVariant,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
            borderSide: BorderSide.none,
          ),
          contentPadding: EdgeInsets.symmetric(
            horizontal: AppDimensions.defaultMargin,
            vertical: AppDimensions.smallMargin,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: _searchQuery.isNotEmpty && _filteredItems.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 64,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.darkTextSecondary
                            : AppColors.lightTextSecondary,
                      ),
                      SizedBox(height: AppDimensions.defaultMargin),
                      Text(
                        widget.noResultsMessage,
                        style: AppTypography.createCustomStyle(
                          fontSize: AppTypography.fontSizeMedium,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? AppColors.darkTextSecondary
                              : AppColors.lightTextSecondary,
                        ),
                      ),
                    ],
                  ),
                )
              : AkListView<T>(
                  items: _filteredItems,
                  itemBuilder: widget.itemBuilder,
                  size: widget.size,
                  style: widget.style,
                  onRefresh: widget.onRefresh,
                  isLoading: widget.isLoading,
                  errorMessage: widget.errorMessage,
                  onRetry: widget.onRetry,
                  emptyMessage: widget.emptyMessage,
                  emptyIcon: widget.emptyIcon,
                  emptyActionText: widget.emptyActionText,
                  onEmptyAction: widget.onEmptyAction,
                ),
        ),
      ],
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🔲 القسم الثالث: القائمة الشبكية (AkGridView)
// ═══════════════════════════════════════════════════════════════════════════════

/// قائمة شبكية موحدة مع تصميم متجاوب
class AkGridView<T> extends StatefulWidget {
  /// قائمة العناصر
  final List<T> items;

  /// دالة بناء العنصر
  final Widget Function(T item, int index) itemBuilder;

  /// عدد الأعمدة (null للتلقائي)
  final int? crossAxisCount;

  /// نسبة العرض إلى الارتفاع
  final double childAspectRatio;

  /// المسافة بين العناصر
  final double spacing;

  /// باقي الخصائص
  final AkListSize size;
  final Future<void> Function()? onRefresh;
  final bool isLoading;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final String emptyMessage;
  final IconData emptyIcon;
  final String? emptyActionText;
  final VoidCallback? onEmptyAction;

  const AkGridView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.crossAxisCount,
    this.childAspectRatio = 1.0,
    this.spacing = 8.0,
    this.size = AkListSize.medium,
    this.onRefresh,
    this.isLoading = false,
    this.errorMessage,
    this.onRetry,
    this.emptyMessage = 'لا توجد بيانات',
    this.emptyIcon = Icons.inbox_outlined,
    this.emptyActionText,
    this.onEmptyAction,
  });

  @override
  State<AkGridView<T>> createState() => _AkGridViewState<T>();
}

class _AkGridViewState<T> extends State<AkGridView<T>> {
  /// الحصول على عدد الأعمدة المناسب
  int _getCrossAxisCount(BuildContext context) {
    if (widget.crossAxisCount != null) return widget.crossAxisCount!;

    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) return 4;
    if (screenWidth > 800) return 3;
    if (screenWidth > 600) return 2;
    return 1;
  }

  /// الحصول على المسافة الداخلية حسب الحجم
  EdgeInsetsGeometry _getPadding() {
    switch (widget.size) {
      case AkListSize.small:
        return EdgeInsets.all(AppDimensions.smallMargin);
      case AkListSize.medium:
        return EdgeInsets.all(AppDimensions.defaultMargin);
      case AkListSize.large:
        return EdgeInsets.all(AppDimensions.largeMargin);
    }
  }

  Widget _buildGridView() {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getCrossAxisCount(context),
        childAspectRatio: widget.childAspectRatio,
        crossAxisSpacing: widget.spacing,
        mainAxisSpacing: widget.spacing,
      ),
      itemCount: widget.items.length,
      padding: _getPadding(),
      itemBuilder: (context, index) =>
          widget.itemBuilder(widget.items[index], index),
    );
  }

  @override
  Widget build(BuildContext context) {
    // عرض حالة التحميل
    if (widget.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              color: AppColors.primary,
            ),
            SizedBox(height: AppDimensions.defaultMargin),
            Text(
              'جاري التحميل...',
              style: AppTypography.createCustomStyle(
                fontSize: AppTypography.fontSizeMedium,
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              ),
            ),
          ],
        ),
      );
    }

    // عرض حالة الخطأ
    if (widget.errorMessage != null) {
      final isDark = Theme.of(context).brightness == Brightness.dark;

      return Center(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.largeMargin),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.error,
              ),
              SizedBox(height: AppDimensions.defaultMargin),
              Text(
                widget.errorMessage!,
                textAlign: TextAlign.center,
                style: AppTypography.createCustomStyle(
                  fontSize: AppTypography.fontSizeMedium,
                  color: isDark
                      ? AppColors.darkTextPrimary
                      : AppColors.lightTextPrimary,
                ),
              ),
              if (widget.onRetry != null) ...[
                SizedBox(height: AppDimensions.defaultMargin),
                AkButtons.retry(onPressed: widget.onRetry!),
              ],
            ],
          ),
        ),
      );
    }

    // عرض حالة القائمة الفارغة
    if (widget.items.isEmpty) {
      final isDark = Theme.of(context).brightness == Brightness.dark;

      return Center(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.largeMargin),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                widget.emptyIcon,
                size: 64,
                color: isDark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              ),
              SizedBox(height: AppDimensions.defaultMargin),
              Text(
                widget.emptyMessage,
                textAlign: TextAlign.center,
                style: AppTypography.createCustomStyle(
                  fontSize: AppTypography.fontSizeMedium,
                  color: isDark
                      ? AppColors.darkTextSecondary
                      : AppColors.lightTextSecondary,
                ),
              ),
              if (widget.emptyActionText != null &&
                  widget.onEmptyAction != null) ...[
                SizedBox(height: AppDimensions.defaultMargin),
                AkButtons.primary(
                  text: widget.emptyActionText!,
                  onPressed: widget.onEmptyAction!,
                ),
              ],
            ],
          ),
        ),
      );
    }

    // عرض الشبكة مع التحديث بالسحب
    if (widget.onRefresh != null) {
      return RefreshIndicator(
        onRefresh: widget.onRefresh!,
        color: AppColors.primary,
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? AppColors.darkSurface
            : AppColors.lightSurface,
        child: _buildGridView(),
      );
    }

    return _buildGridView();
  }
}
