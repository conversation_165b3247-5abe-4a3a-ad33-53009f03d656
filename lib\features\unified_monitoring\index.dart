/// نظام المراقبة الموحد - Unified Monitoring System
/// 
/// يوفر نظام شامل لتتبع الأخطاء والنشاطات في التطبيق
/// مع واجهة موحدة لسهولة المراقبة والصيانة
/// 
/// الميزات الرئيسية:
/// - تتبع شامل للأخطاء مع السياق
/// - تسجيل تلقائي للنشاطات
/// - إحصائيات مفصلة ودقيقة
/// - واجهة مستخدم موحدة ومتسقة
/// - إدارة البيانات المحسنة
/// 
/// الاستخدام:
/// ```dart
/// // عرض شاشة المراقبة الموحدة
/// Navigator.push(
///   context,
///   MaterialPageRoute(
///     builder: (context) => const UnifiedMonitoringScreen(),
///   ),
/// );
/// 
/// // تسجيل خطأ
/// ErrorTracker.captureError(
///   'وصف الخطأ',
///   error: e,
///   stackTrace: stackTrace,
///   context: {'operation': 'someOperation'},
/// );
/// 
/// // تسجيل نشاط
/// await ErrorTracker.logActivity(
///   userId: user.id,
///   userName: user.name,
///   action: 'إضافة منتج',
///   module: 'المنتجات',
///   details: 'تم إضافة منتج جديد',
/// );
/// ```

// الشاشات
export 'screens/unified_monitoring_screen.dart';

// النظام الأساسي (متوفر من خلال core/utils/error_tracker.dart)
// export '../../../core/utils/error_tracker.dart';
