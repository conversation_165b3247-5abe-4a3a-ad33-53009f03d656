# 📊 نظام الحالات الموحد (AK States System)

نظام شامل وموحد لجميع حالات الواجهة في تطبيق تاجر بلس، مصمم خصيصاً للمشاريع التجارية اليمنية.

## 🎯 **المميزات الرئيسية**

- ✅ **تصميم موحد ومتناسق** لجميع الحالات
- ✅ **دعم كامل للوضع المظلم/الفاتح** باستخدام `Theme.of(context).brightness`
- ✅ **عدم وجود قيم صريحة** - استخدام `AppColors.*`, `AppDimensions.*`, `AppTypography.*` فقط
- ✅ **تأثيرات تفاعلية متقدمة** مع رسوم متحركة سلسة
- ✅ **دوال مساعدة سريعة** للاستخدام المباشر
- ✅ **تعليقات شاملة باللغة العربية**
- ✅ **أحجام متعددة** (صغير، متوسط، كبير)
- ✅ **دوال متخصصة للمشروع التجاري**

## 📋 **الحالات المتوفرة**

### 📭 **1. الحالة الفارغة (AkEmptyState)**
حالة فارغة موحدة مع دعم الأيقونات والرسائل المخصصة وأزرار الإجراءات.

### ❌ **2. حالة الخطأ (AkErrorState)**
حالة خطأ متقدمة مع تفاصيل الأخطاء وأزرار إعادة المحاولة.

## 🚀 **أمثلة الاستخدام**

### **الحالة الفارغة الأساسية:**
```dart
// حالة فارغة بسيطة
AkEmptyState(
  message: 'لا توجد منتجات',
  icon: Icons.inventory_2,
  onRefresh: () => loadProducts(),
  onAction: () => addProduct(),
  actionText: 'إضافة منتج',
)
```

### **حالة خطأ مع تفاصيل:**
```dart
// حالة خطأ مع إعادة المحاولة
AkErrorState(
  message: 'فشل في تحميل البيانات',
  errorDetails: 'خطأ في الاتصال بالخادم',
  onRetry: () => retryLoad(),
  showDetails: true,
)
```

### **الدوال المساعدة السريعة:**
```dart
// حالة فارغة للمنتجات
AkStates.emptyProducts(
  onRefresh: () => loadProducts(),
  onAddProduct: () => addNewProduct(),
)

// خطأ في الاتصال
AkStates.connectionError(
  onRetry: () => retryConnection(),
  onSettings: () => openNetworkSettings(),
)

// حالة فارغة للمبيعات
AkStates.emptySales(
  onRefresh: () => loadSales(),
  onNewSale: () => startNewSale(),
)
```

## 🎨 **أنواع الحالات**

| النوع | الوصف | الاستخدام |
|-------|--------|-----------|
| `empty` | حالة فارغة | عدم وجود بيانات |
| `error` | حالة خطأ | فشل في العمليات |
| `loading` | حالة تحميل | تحميل البيانات |
| `noConnection` | عدم اتصال | مشاكل الشبكة |
| `noPermission` | عدم صلاحية | قيود الوصول |

## 📏 **أحجام العرض**

| الحجم | الوصف | الاستخدام |
|-------|--------|-----------|
| `small` | صغير | مناطق محدودة |
| `medium` | متوسط | الاستخدام العادي |
| `large` | كبير | شاشات كاملة |

## 🛠️ **الدوال المساعدة المتوفرة**

### **حالات فارغة متخصصة:**
- `AkStates.emptyProducts()` - لا توجد منتجات
- `AkStates.emptyCustomers()` - لا يوجد عملاء
- `AkStates.emptySales()` - لا توجد مبيعات
- `AkStates.emptyPurchases()` - لا توجد مشتريات
- `AkStates.emptyReports()` - لا توجد تقارير
- `AkStates.emptyNotifications()` - لا توجد إشعارات
- `AkStates.lowStockEmpty()` - لا توجد منتجات منخفضة المخزون

### **حالات أخطاء متخصصة:**
- `AkStates.connectionError()` - خطأ في الاتصال
- `AkStates.serverError()` - خطأ في الخادم
- `AkStates.dataLoadError()` - خطأ في تحميل البيانات
- `AkStates.permissionError()` - خطأ في الصلاحيات
- `AkStates.paymentError()` - خطأ في الدفع

## 🎯 **أمثلة متقدمة**

### **حالة فارغة مخصصة:**
```dart
AkEmptyState(
  title: 'مرحباً بك في تاجر بلس',
  message: 'ابدأ رحلتك التجارية معنا',
  description: 'أضف منتجاتك الأولى وابدأ البيع',
  icon: Icons.store,
  customIconColor: AppColors.primary,
  size: AkStateSize.large,
  onRefresh: () => loadInitialData(),
  onAction: () => showWelcomeGuide(),
  actionText: 'دليل البداية',
)
```

### **حالة خطأ مع إجراءات متعددة:**
```dart
AkErrorState(
  title: 'خطأ في النظام',
  message: 'حدث خطأ غير متوقع',
  errorDetails: 'رمز الخطأ: ERR_500\nالوقت: ${DateTime.now()}',
  icon: Icons.bug_report,
  size: AkStateSize.medium,
  showDetails: true,
  onRetry: () => retryOperation(),
  onAction: () => contactSupport(),
  actionText: 'تواصل مع الدعم',
)
```

## 🎨 **التخصيص المتقدم**

### **ألوان مخصصة:**
```dart
AkEmptyState(
  message: 'تهانينا! مخزونك ممتاز',
  icon: Icons.check_circle,
  customIconColor: AppColors.success, // لون أخضر للنجاح
  onRefresh: () => checkStock(),
)
```

### **أحجام مختلفة:**
```dart
// حجم صغير للمناطق المحدودة
AkEmptyState(
  message: 'لا توجد إشعارات',
  size: AkStateSize.small,
  onRefresh: () => checkNotifications(),
)

// حجم كبير للشاشات الكاملة
AkErrorState(
  message: 'فشل في تحميل التطبيق',
  size: AkStateSize.large,
  onRetry: () => restartApp(),
)
```

## 📱 **أمثلة للمشروع التجاري**

### **حالات المنتجات:**
```dart
// لا توجد منتجات
AkStates.emptyProducts(
  onRefresh: () => loadProducts(),
  onAddProduct: () => Navigator.push(context, AddProductPage()),
)

// مخزون منخفض (حالة إيجابية)
AkStates.lowStockEmpty(
  onRefresh: () => checkLowStock(),
  onManageStock: () => openStockManagement(),
)
```

### **حالات المبيعات:**
```dart
// لا توجد مبيعات اليوم
AkStates.emptySales(
  onRefresh: () => loadTodaySales(),
  onNewSale: () => startPOSSession(),
)

// خطأ في عملية الدفع
AkStates.paymentError(
  onRetry: () => retryPayment(),
  onContactSupport: () => openSupportChat(),
  errorMessage: 'فشل في الاتصال بمعالج الدفع',
)
```

### **حالات العملاء:**
```dart
// لا يوجد عملاء
AkStates.emptyCustomers(
  onRefresh: () => loadCustomers(),
  onAddCustomer: () => showAddCustomerDialog(),
)
```

## 🔧 **التكامل مع الأنظمة الأخرى**

### **مع نظام الأزرار:**
```dart
AkEmptyState(
  message: 'لا توجد منتجات',
  onRefresh: () => loadProducts(),
  // يمكن إضافة أزرار مخصصة من نظام akbuttons.dart
)
```

### **مع نظام الحوارات:**
```dart
AkErrorState(
  message: 'خطأ في النظام',
  onRetry: () => retryOperation(),
  onAction: () {
    // عرض حوار تفصيلي
    AkDialogs.error(
      context: context,
      title: 'تفاصيل الخطأ',
      message: 'حدث خطأ في قاعدة البيانات',
    );
  },
  actionText: 'تفاصيل أكثر',
)
```

## 🎨 **الدعم للوضع المظلم/الفاتح**

النظام يتكيف تلقائياً مع وضع التطبيق:

```dart
// يتم التحقق تلقائياً من الوضع
final isDark = Theme.of(context).brightness == Brightness.dark;

// الألوان تتغير تلقائياً
iconColor: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
titleColor: isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
messageColor: isDark ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
```

## 🔄 **الترقية من الأنظمة القديمة**

### **من empty_state.dart:**
```dart
// ❌ القديم
EmptyState(message: 'لا توجد بيانات');

// ✅ الجديد
AkStates.emptyProducts(onRefresh: () => loadProducts());
```

### **من error_state.dart:**
```dart
// ❌ القديم
ErrorState(message: 'خطأ', onRetry: () => retry());

// ✅ الجديد
AkStates.connectionError(onRetry: () => retryConnection());
```

## 📊 **الإحصائيات**

- **عدد الحالات**: 2 نوع أساسي
- **عدد الدوال المساعدة**: 12 دالة
- **عدد الأحجام**: 3 أحجام مختلفة
- **الدعم للغات**: العربية (أساسي)
- **حجم الملف**: ~820 سطر
- **التبعيات**: لا توجد تبعيات خارجية

---

**تم تطوير هذا النظام خصيصاً لتطبيق تاجر بلس - نظام إدارة المبيعات اليمني** 🇾🇪
