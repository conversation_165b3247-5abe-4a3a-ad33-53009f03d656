import 'package:flutter/foundation.dart';
import 'roles_schema.dart';
import '../utils/error_tracker.dart';
import '../database/database_helper.dart';
import 'models/user_permission.dart';

/// مدير الصلاحيات
class PermissionsManager extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  UserPermission? _currentUserPermission;
  String? _currentUserRole;
  bool _isLoading = false;
  String? _errorMessage;

  /// الحصول على صلاحيات المستخدم الحالي
  UserPermission? get currentUserPermission => _currentUserPermission;

  /// الحصول على قائمة رموز صلاحيات المستخدم الحالي
  List<String> get currentUserPermissionCodes =>
      _currentUserPermission?.permissionCodes ?? [];

  /// الحصول على دور المستخدم الحالي
  String? get currentUserRole => _currentUserRole;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// تحميل صلاحيات المستخدم
  Future<void> loadUserPermissions(String userId) async {
    _setLoading(true);

    try {
      final db = await _databaseHelper.database;

      // الحصول على دور المستخدم
      final userRoleResult = await db.query(
        'users',
        columns: ['role_id'],
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (userRoleResult.isNotEmpty) {
        _currentUserRole = userRoleResult.first['role_id'] as String;

        // الحصول على الصلاحيات المخصصة للمستخدم
        final userPermissionsResult = await db.query(
          'user_permissions',
          where: 'user_id = ?',
          whereArgs: [userId],
        );

        if (userPermissionsResult.isNotEmpty) {
          // المستخدم لديه صلاحيات مخصصة
          _currentUserPermission =
              UserPermission.fromMap(userPermissionsResult.first);
        } else {
          // استخدام الصلاحيات الافتراضية للدور
          _loadDefaultRolePermissions(_currentUserRole!);
        }
      } else {
        _setErrorMessage('لم يتم العثور على المستخدم');
      }
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في تحميل صلاحيات المستخدم: ${e.toString()}');
      ErrorTracker.captureError(
        'فشل في تحميل صلاحيات المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {
          'userId': userId,
        },
      );
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل الصلاحيات الافتراضية للدور
  void _loadDefaultRolePermissions(String role) {
    if (RolesSchema.defaultRolePermissions.containsKey(role)) {
      final permissionCodes = RolesSchema.defaultRolePermissions[role]!;

      // إنشاء كائن صلاحيات المستخدم من رموز الصلاحيات
      _currentUserPermission = UserPermission.fromPermissionCodes(
        _getCurrentUserId() ?? 'unknown',
        permissionCodes,
      );

      notifyListeners();
    } else {
      _setErrorMessage('الدور غير موجود: $role');
    }
  }

  /// التحقق من وجود صلاحية
  bool hasPermission(String permissionCode) {
    return _currentUserPermission?.hasPermission(permissionCode) ?? false;
  }

  /// التحقق من وجود أي من الصلاحيات
  bool hasAnyPermission(List<String> permissionCodes) {
    return _currentUserPermission?.hasAnyPermission(permissionCodes) ?? false;
  }

  /// التحقق من وجود جميع الصلاحيات
  bool hasAllPermissions(List<String> permissionCodes) {
    return _currentUserPermission?.hasAllPermissions(permissionCodes) ?? false;
  }

  /// تحديث صلاحيات المستخدم
  Future<bool> updateUserPermissions(
      String userId, List<String> permissionCodes) async {
    _setLoading(true);

    try {
      final db = await _databaseHelper.database;

      // إنشاء كائن صلاحيات المستخدم
      final userPermission = UserPermission(
        userId: userId,
        permissionCodes: permissionCodes,
      );

      // تحويل الكائن إلى خريطة
      final permissionMap = userPermission.toMap();

      // التحقق من وجود صلاحيات مخصصة للمستخدم
      final userPermissionsResult = await db.query(
        'user_permissions',
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      if (userPermissionsResult.isNotEmpty) {
        // تحديث الصلاحيات الموجودة
        await db.update(
          'user_permissions',
          permissionMap,
          where: 'user_id = ?',
          whereArgs: [userId],
        );
      } else {
        // إنشاء صلاحيات جديدة
        await db.insert(
          'user_permissions',
          permissionMap,
        );
      }

      // تحديث الصلاحيات المحلية إذا كان المستخدم الحالي
      if (userId == _getCurrentUserId()) {
        _currentUserPermission = userPermission;
        notifyListeners();
      }

      return true;
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في تحديث صلاحيات المستخدم: ${e.toString()}');
      ErrorTracker.captureError(
        'فشل في تحديث صلاحيات المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {
          'userId': userId,
          'permissionCodes': permissionCodes,
        },
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث دور المستخدم
  Future<bool> updateUserRole(String userId, String role) async {
    _setLoading(true);

    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود الدور
      if (!RolesSchema.roles.containsKey(role)) {
        _setErrorMessage('الدور غير موجود: $role');
        return false;
      }

      // تحديث دور المستخدم
      await db.update(
        'users',
        {'role_id': role},
        where: 'id = ?',
        whereArgs: [userId],
      );

      // حذف الصلاحيات المخصصة للمستخدم (إذا وجدت)
      await db.delete(
        'user_permissions',
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      // تحديث الدور المحلي إذا كان المستخدم الحالي
      if (userId == _getCurrentUserId()) {
        _currentUserRole = role;
        _loadDefaultRolePermissions(role);
        notifyListeners();
      }

      return true;
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في تحديث دور المستخدم: ${e.toString()}');
      ErrorTracker.captureError(
        'فشل في تحديث دور المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {
          'userId': userId,
          'role': role,
        },
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على معرف المستخدم الحالي
  String? _getCurrentUserId() {
    // يمكن استبدال هذا بالطريقة المناسبة للحصول على معرف المستخدم الحالي
    // مثلاً من خلال مدير الجلسات أو مدير المصادقة
    return null;
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setErrorMessage(String? message) {
    _errorMessage = message;
    notifyListeners();
  }
}
