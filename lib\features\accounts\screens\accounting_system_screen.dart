import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/theme/index.dart';
import '../../../core/utils/index.dart';

import 'chart_of_accounts_screen.dart';
import 'financial_reports_screen.dart';
// تم حذف integrated_financial_reports_screen.dart - مكررة
import 'transactions_screen.dart';

/// شاشة النظام المحاسبي الرئيسية
///
/// 📊 الوظائف:
/// - عرض دليل الحسابات بشكل شجري منظم
/// - إدارة القيود المحاسبية والمعاملات
/// - عرض التقارير المالية الشاملة
/// - إدارة الفترات المالية والإقفال
/// - واجهة سهلة ومتجاوبة لجميع العمليات المحاسبية
class AccountingSystemScreen extends StatefulWidget {
  const AccountingSystemScreen({Key? key}) : super(key: key);

  @override
  State<AccountingSystemScreen> createState() => _AccountingSystemScreenState();
}

class _AccountingSystemScreenState extends State<AccountingSystemScreen> {
  @override
  void initState() {
    super.initState();
    // تمكين جميع اتجاهات الشاشة
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  @override
  void dispose() {
    // إعادة ضبط اتجاه الشاشة إلى الوضع العمودي فقط عند الخروج من الشاشة
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = Layout.isTablet();
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('النظام المحاسبي'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الصفحة
            Text(
              'النظام المحاسبي المتكامل',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
            ),
            const SizedBox(height: AppDimensions.spacing8),
            Text(
              'إدارة الحسابات والقيود المحاسبية والتقارير المالية',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.lightTextSecondary,
              ),
            ),
            const SizedBox(height: AppDimensions.spacing24),

            // بطاقات الوظائف الرئيسية
            GridView.count(
              crossAxisCount: isLandscape || isTablet ? 3 : 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: isLandscape ? 1.5 : 1.0,
              children: [
                // شجرة الحسابات
                _buildFeatureCard(
                  context,
                  title: 'شجرة الحسابات',
                  icon: Icons.account_tree,
                  description: 'إدارة هيكل الحسابات المحاسبية',
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ChartOfAccountsScreen(),
                    ),
                  ),
                ),

                // قيود اليومية
                _buildFeatureCard(
                  context,
                  title: 'قيود اليومية',
                  icon: Icons.edit_note,
                  description: 'إدارة القيود المحاسبية اليومية',
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const TransactionsScreen(),
                    ),
                  ),
                ),

                // التقارير المالية
                _buildFeatureCard(
                  context,
                  title: 'التقارير المالية',
                  icon: Icons.bar_chart,
                  description: 'عرض وطباعة التقارير المالية',
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FinancialReportsScreen(),
                    ),
                  ),
                ),

                // التقارير المالية
                _buildFeatureCard(
                  context,
                  title: 'التقارير المالية',
                  icon: Icons.analytics,
                  description: 'تقارير مالية شاملة وتحليلية',
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FinancialReportsScreen(),
                    ),
                  ),
                ),

                // الفترات المالية
                _buildFeatureCard(
                  context,
                  title: 'الفترات المالية',
                  icon: Icons.date_range,
                  description: 'إدارة الفترات المالية والإقفال',
                  onTap: () {
                    // سيتم تنفيذها لاحقاً
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('سيتم إضافة هذه الميزة قريباً'),
                      ),
                    );
                  },
                ),
              ],
            ),

            const SizedBox(height: AppDimensions.spacing24),

            // قسم الإحصائيات السريعة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الإحصائيات السريعة',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            context,
                            title: 'إجمالي الأصول',
                            value: '0.00',
                            icon: Icons.trending_up,
                            color: AppColors.success,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildStatCard(
                            context,
                            title: 'إجمالي الخصوم',
                            value: '0.00',
                            icon: Icons.trending_down,
                            color: AppColors.error,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            context,
                            title: 'الإيرادات',
                            value: '0.00',
                            icon: Icons.arrow_upward,
                            color: AppColors.success,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildStatCard(
                            context,
                            title: 'المصروفات',
                            value: '0.00',
                            icon: Icons.arrow_downward,
                            color: AppColors.warning,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة ميزة
  Widget _buildFeatureCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required String description,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return Card(
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: theme.primaryColor,
              ),
              const SizedBox(height: AppDimensions.spacing12),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              if (!isLandscape) const SizedBox(height: AppDimensions.spacing8),
              if (!isLandscape)
                Text(
                  description,
                  style: theme.textTheme.bodySmall,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء بطاقة إحصائية
  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing8),
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
