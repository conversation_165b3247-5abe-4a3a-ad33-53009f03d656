import 'package:flutter/material.dart';
import '../../../core/models/category.dart';
import '../../../core/services/category_service.dart';

class CategoryPresenter extends ChangeNotifier {
  final _categoryService = CategoryService();
  List<Category> _categories = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Category> get categories => _categories;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get root categories (categories without parent)
  List<Category> get rootCategories =>
      _categories.where((c) => c.parentId == null).toList();

  // Get subcategories for a given parent ID
  List<Category> getSubcategories(String parentId) {
    return _categories.where((c) => c.parentId == parentId).toList();
  }

  // Initialize
  Future<void> init() async {
    await loadCategories();
  }

  // Load categories
  Future<void> loadCategories({String? type}) async {
    try {
      _setLoading(true);

      // استخدام خدمة الفئات الموحدة
      _categories = await _categoryService.getAllCategories(
        type: type ?? 'product',
      );

      _error = null;
    } catch (e) {
      _error = 'Failed to load categories: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Add category
  Future<bool> addCategory(Category category) async {
    try {
      _setLoading(true);

      // التأكد من أن نوع الفئة هو منتج إذا لم يتم تحديده
      final categoryToAdd =
          category.type.isEmpty ? category.copyWith(type: 'product') : category;

      // استخدام خدمة الفئات الموحدة
      final result = await _categoryService.addCategory(categoryToAdd);

      await loadCategories();
      return result;
    } catch (e) {
      _error = 'Failed to add category: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update category
  Future<bool> updateCategory(Category category) async {
    try {
      _setLoading(true);

      // التأكد من أن نوع الفئة هو منتج إذا لم يتم تحديده
      final categoryToUpdate =
          category.type.isEmpty ? category.copyWith(type: 'product') : category;

      // استخدام خدمة الفئات الموحدة
      final result = await _categoryService.updateCategory(categoryToUpdate);

      await loadCategories();
      return result;
    } catch (e) {
      _error = 'Failed to update category: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete category
  Future<bool> deleteCategory(String id) async {
    try {
      _setLoading(true);

      // استخدام خدمة الفئات الموحدة
      final result = await _categoryService.deleteCategory(id, type: 'product');

      await loadCategories();
      return result;
    } catch (e) {
      _error = 'Failed to delete category: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Gets the full path of categories from root to the target category
  List<Category> getCategoryPath(String categoryId) {
    final List<Category> path = [];
    Category? current = categories.firstWhere((c) => c.id == categoryId);

    while (current != null) {
      path.insert(0, current);
      if (current.parentId != null) {
        current = categories.firstWhere(
          (c) => c.id == current!.parentId,
          orElse: () =>
              current!, // Return the current category if parent not found
        );
        if (current.id == current.parentId) break; // Prevent infinite loop
      } else {
        current = null;
      }
    }

    return path;
  }

  // Private methods
  void _setLoading(bool value) {
    _isLoading = value;
    Future.microtask(() => notifyListeners());
  }

  @override
  void dispose() {
    // لا حاجة لإغلاق قاعدة البيانات هنا
    super.dispose();
  }
}
