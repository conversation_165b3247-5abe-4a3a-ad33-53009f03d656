import 'dart:async';
import 'package:flutter/foundation.dart';
// تعطيل Firebase مؤقتًا
// import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../../../core/utils/error_tracker.dart';
import '../../../data/services/cloud_sync_service.dart';
import '../../../core/utils/connectivity_helper.dart';

class CloudSyncPresenter extends ChangeNotifier {
  final CloudSyncService _syncService = CloudSyncService();
  // تعطيل Firebase مؤقتًا
  // final firebase_auth.FirebaseAuth _auth = firebase_auth.FirebaseAuth.instance;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final ConnectivityHelper _connectivityHelper = ConnectivityHelper();

  bool _isInitialized = false;
  bool _isOnline = false;
  bool _isSyncing = false;
  SyncStatus _syncStatus = SyncStatus.offline;
  DateTime? _lastSyncTime;
  String? _error;
  StreamSubscription? _syncStatusSubscription;
  StreamSubscription? _authStateSubscription;
  StreamSubscription? _connectivitySubscription;
  Timer? _autoSyncTimer;
  final int _syncFailureCount = 0;
  int _maxSyncRetries = 3;
  Duration _syncInterval = const Duration(minutes: 15);

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isOnline => _isOnline;
  bool get isSyncing => _isSyncing;
  SyncStatus get syncStatus => _syncStatus;
  DateTime? get lastSyncTime => _lastSyncTime;
  String? get error => _error;
  // تعطيل Firebase مؤقتًا
  bool get isLoggedIn => true; // دائمًا مسجل الدخول في الوضع المحلي
  String? get userEmail => '<EMAIL>'; // بريد إلكتروني محلي مؤقت
  int get syncFailureCount => _syncFailureCount;
  bool get canRetrySync => _syncFailureCount < _maxSyncRetries;

  CloudSyncPresenter() {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      // تهيئة خدمة المزامنة
      await _syncService.initialize();

      // تهيئة مساعد الاتصال
      _connectivityHelper.initialize();

      // الاشتراك في تغييرات حالة المزامنة
      _syncStatusSubscription =
          _syncService.syncStatusStream.listen(_handleSyncStatusChange);

      // تعطيل Firebase مؤقتًا
      // الاشتراك في تغييرات حالة المصادقة
      // _authStateSubscription =
      //     _auth.authStateChanges().listen(_handleAuthStateChange);

      // الاشتراك في تغييرات حالة الاتصال بالإنترنت
      _connectivitySubscription = _connectivityHelper.onConnectivityChanged
          .listen(_handleConnectivityChange);

      // استرجاع وقت آخر مزامنة من التخزين الآمن
      final lastSyncTimeStr = await _secureStorage.read(key: 'last_sync_time');
      if (lastSyncTimeStr != null) {
        _lastSyncTime = DateTime.parse(lastSyncTimeStr);
      }

      // استرجاع إعدادات المزامنة من التخزين الآمن
      await _loadSyncSettings();

      // ضبط القيم الأولية
      _isInitialized = _syncService.isInitialized;
      _isOnline = await _connectivityHelper.isConnected;
      _isSyncing = _syncService.isSyncing;

      // بدء المزامنة التلقائية إذا كان المستخدم مسجل الدخول ومتصل بالإنترنت
      if (isLoggedIn && _isOnline) {
        _startAutoSync();
      }
      _syncStatus = _syncService.syncStatus;
      _lastSyncTime = _syncService.lastSyncTime;

      notifyListeners();
    } catch (e, stackTrace) {
      _handleError('Failed to initialize cloud sync', e, stackTrace);
    }
  }

  // Sign in with email and password
  Future<bool> signIn(String email, String password) async {
    try {
      _setError(null);
      final success = await _syncService.signIn(email, password);

      if (success) {
        // Save credentials for auto-login
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('cloud_sync_email', email);
        // Note: In a production app, you would use a more secure storage for the password
        await prefs.setString('cloud_sync_password', password);
      } else {
        _setError('فشل تسجيل الدخول إلى خدمة المزامنة السحابية');
      }

      return success;
    } catch (e, stackTrace) {
      _handleError('Failed to sign in to cloud sync', e, stackTrace);
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _syncService.signOut();

      // Clear saved credentials
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('cloud_sync_email');
      await prefs.remove('cloud_sync_password');

      notifyListeners();
    } catch (e, stackTrace) {
      _handleError('Failed to sign out from cloud sync', e, stackTrace);
    }
  }

  // Sync data manually
  Future<bool> syncData() async {
    try {
      _setError(null);
      final success = await _syncService.syncData();

      if (!success) {
        _setError('فشل مزامنة البيانات');
      }

      return success;
    } catch (e, stackTrace) {
      _handleError('Failed to sync data', e, stackTrace);
      return false;
    }
  }

  // Auto sign in with saved credentials
  Future<bool> autoSignIn() async {
    try {
      // تعطيل Firebase مؤقتًا
      // دائمًا مسجل الدخول في الوضع المحلي
      return true;

      // الكود الأصلي معطل مؤقتًا
      /*
      // Check if already signed in
      if (_auth.currentUser != null) {
        return true;
      }

      // Get saved credentials
      final prefs = await SharedPreferences.getInstance();
      final email = prefs.getString('cloud_sync_email');
      final password = prefs.getString('cloud_sync_password');

      if (email != null && password != null) {
        return await signIn(email, password);
      }

      return false;
      */
    } catch (e, stackTrace) {
      _handleError('Failed to auto sign in', e, stackTrace);
      return false;
    }
  }

  // Mark a record for sync
  Future<void> markForSync(String table, String id) async {
    try {
      await _syncService.markForSync(table, id);
    } catch (e, stackTrace) {
      _handleError('Failed to mark record for sync', e, stackTrace);
    }
  }

  // Event handlers
  void _handleSyncStatusChange(SyncStatus status) {
    _syncStatus = status;
    _isSyncing = status == SyncStatus.pending;
    _isOnline = status != SyncStatus.offline;
    _lastSyncTime = _syncService.lastSyncTime;
    notifyListeners();
  }

  // تعطيل Firebase مؤقتًا
  // void _handleAuthStateChange(firebase_auth.User? user) {
  //   _isOnline = user != null;
  //   notifyListeners();
  // }

  // Helper methods
  void _setError(String? errorMessage) {
    _error = errorMessage;
    notifyListeners();
  }

  void _handleError(String message, dynamic error, StackTrace stackTrace) {
    _setError(message);
    ErrorTracker.captureError(
      message,
      error: error,
      stackTrace: stackTrace,
      context: {'presenter': 'CloudSyncPresenter'},
    );
  }

  // تحميل إعدادات المزامنة من التخزين الآمن
  Future<void> _loadSyncSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // استرجاع عدد محاولات المزامنة القصوى
      final maxRetries = prefs.getInt('sync_max_retries');
      if (maxRetries != null) {
        _maxSyncRetries = maxRetries;
      }

      // استرجاع فترة المزامنة التلقائية
      final syncIntervalMinutes = prefs.getInt('sync_interval_minutes');
      if (syncIntervalMinutes != null) {
        _syncInterval = Duration(minutes: syncIntervalMinutes);
      }
    } catch (e, stackTrace) {
      _handleError('Failed to load sync settings', e, stackTrace);
    }
  }

  // معالجة تغييرات حالة الاتصال بالإنترنت
  void _handleConnectivityChange(bool isConnected) {
    _isOnline = isConnected;

    if (isConnected) {
      if (isLoggedIn && !_isSyncing) {
        _startAutoSync();
      }
    } else {
      _syncStatus = SyncStatus.offline;
      _autoSyncTimer?.cancel();
    }

    notifyListeners();
  }

  // بدء المزامنة التلقائية
  void _startAutoSync() {
    _autoSyncTimer?.cancel();

    // إنشاء مؤقت للمزامنة التلقائية
    _autoSyncTimer = Timer.periodic(_syncInterval, (timer) async {
      if (_isOnline && isLoggedIn && !_isSyncing) {
        await syncData();
      }
    });
  }

  @override
  void dispose() {
    _syncStatusSubscription?.cancel();
    _authStateSubscription?.cancel();
    _connectivitySubscription?.cancel();
    _autoSyncTimer?.cancel();
    super.dispose();
  }
}
