import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/utils/index.dart';
import '../../../core/widgets/index.dart';

import '../../../core/theme/index.dart';

class LegalScreen extends StatefulWidget {
  const LegalScreen({Key? key}) : super(key: key);

  @override
  State<LegalScreen> createState() => _LegalScreenState();
}

class _LegalScreenState extends State<LegalScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('الشروط والسياسات'),
          centerTitle: true,
          bottom: TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'الشروط والأحكام'),
              Tab(text: 'سياسة الخصوصية'),
            ],
            labelStyle: AppTypography(
              fontSize: Layout.getResponsiveFontSize(14),
              fontWeight: FontWeight.bold,
            ),
            unselectedLabelStyle: AppTypography(
              fontSize: Layout.getResponsiveFontSize(14),
            ),
            indicatorWeight: 3,
            indicatorSize: TabBarIndicatorSize.tab,
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildTermsAndConditionsTab(),
            _buildPrivacyPolicyTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildTermsAndConditionsTab() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: Layout.w(4),
          vertical: Layout.h(2),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTermsHeader(),
            SizedBox(height: Layout.h(2)),
            _buildTermsContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacyPolicyTab() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: Layout.w(4),
          vertical: Layout.h(2),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPrivacyHeader(),
            SizedBox(height: Layout.h(2)),
            _buildPrivacyContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildTermsHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الشروط والأحكام',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.lightTextSecondary,
                fontSize: Layout.getResponsiveFontSize(24),
              ),
        ),
        SizedBox(height: Layout.h(1)),
        Text(
          'آخر تحديث: 20 مايو 2025',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.lightTextSecondary,
                fontSize: Layout.getResponsiveFontSize(14),
              ),
        ),
        SizedBox(height: Layout.h(1)),
        Text(
          'يرجى قراءة هذه الشروط والأحكام بعناية قبل استخدام تطبيق تاجر بلس. باستخدامك للتطبيق، فإنك توافق على الالتزام بهذه الشروط.',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontSize: Layout.getResponsiveFontSize(16),
              ),
        ),
      ],
    );
  }

  Widget _buildPrivacyHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'سياسة الخصوصية',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.lightTextSecondary,
                fontSize: Layout.getResponsiveFontSize(24),
              ),
        ),
        SizedBox(height: Layout.h(1)),
        Text(
          'آخر تحديث: 20 مايو 2025',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.lightTextSecondary,
                fontSize: Layout.getResponsiveFontSize(14),
              ),
        ),
        SizedBox(height: Layout.h(1)),
        Text(
          'نحن في تطبيق تاجر بلس نقدر خصوصيتك ونلتزم بحماية بياناتك الشخصية. تشرح هذه السياسة كيفية جمع واستخدام وحماية معلوماتك عند استخدام تطبيقنا.',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontSize: Layout.getResponsiveFontSize(16),
              ),
        ),
      ],
    );
  }

  Widget _buildTermsContent() {
    return AkCard(
      child: Padding(
        padding: EdgeInsets.all(Layout.w(4)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              context,
              'مقدمة',
              [
                'تحدد هذه الشروط والأحكام ("الشروط") القواعد والأنظمة لاستخدام تطبيق تاجر بلس ("التطبيق").',
                'باستخدامك للتطبيق، فإنك توافق على الالتزام بهذه الشروط. إذا كنت لا توافق على أي جزء من هذه الشروط، فيرجى عدم استخدام التطبيق.',
                'يجب قراءة هذه الشروط بعناية قبل استخدام التطبيق لأول مرة.',
              ],
            ),
            _buildSection(
              context,
              'التسجيل والحسابات',
              [
                'يجب أن تكون عمرك 18 عامًا على الأقل لاستخدام هذا التطبيق.',
                'عند التسجيل، يجب عليك تقديم معلومات دقيقة وكاملة وحديثة.',
                'أنت مسؤول عن الحفاظ على سرية معلومات حسابك وكلمة المرور.',
                'أنت مسؤول عن جميع الأنشطة التي تحدث تحت حسابك.',
                'يجب إخطارنا فورًا بأي استخدام غير مصرح به لحسابك أو أي خرق أمني آخر.',
              ],
            ),
            _buildSection(
              context,
              'استخدام التطبيق',
              [
                'يجب استخدام التطبيق فقط للأغراض المشروعة وبطريقة لا تنتهك حقوق الآخرين.',
                'يجب عدم استخدام التطبيق لأي غرض غير قانوني أو غير مصرح به.',
                'يجب عدم محاولة الوصول إلى أجزاء من التطبيق لا تملك صلاحية الوصول إليها.',
                'يجب عدم التدخل في أداء التطبيق أو خوادمه أو شبكاته.',
              ],
            ),
            _buildSection(
              context,
              'المحتوى والملكية الفكرية',
              [
                'جميع المحتويات المقدمة في التطبيق، بما في ذلك النصوص والرسومات والشعارات والصور والبرمجيات، هي ملكية لتطبيق تاجر بلس أو مرخصة له.',
                'لا يجوز نسخ أو توزيع أو تعديل أو إعادة إنتاج أو بيع أو تأجير أي جزء من التطبيق دون إذن كتابي مسبق.',
                'يمنح المستخدم تطبيق تاجر بلس ترخيصًا غير حصري وغير قابل للتحويل لاستخدام المحتوى الذي يقوم بتحميله أو مشاركته على التطبيق.',
              ],
            ),
            _buildSection(
              context,
              'إخلاء المسؤولية',
              [
                'يتم توفير التطبيق "كما هو" و"كما هو متاح" دون أي ضمانات من أي نوع، صريحة أو ضمنية.',
                'لا نضمن أن التطبيق سيكون خاليًا من الأخطاء أو الفيروسات أو متاحًا بشكل مستمر أو غير منقطع.',
                'لن نكون مسؤولين عن أي أضرار مباشرة أو غير مباشرة أو عرضية أو خاصة أو تبعية تنتج عن استخدامك للتطبيق.',
              ],
            ),
            _buildSection(
              context,
              'التعديلات على الشروط',
              [
                'نحتفظ بالحق في تعديل هذه الشروط في أي وقت وفقًا لتقديرنا الخاص.',
                'سيتم إخطارك بأي تغييرات جوهرية في الشروط من خلال إشعار داخل التطبيق أو عبر البريد الإلكتروني.',
                'استمرارك في استخدام التطبيق بعد نشر التغييرات يعني موافقتك على الشروط المعدلة.',
              ],
            ),
            _buildContactSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacyContent() {
    return AkCard(
      child: Padding(
        padding: EdgeInsets.all(Layout.w(4)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              context,
              'المعلومات التي نجمعها',
              [
                'معلومات الحساب: عند إنشاء حساب، نقوم بجمع اسمك وبريدك الإلكتروني ورقم هاتفك وكلمة المرور المشفرة.',
                'بيانات الاستخدام: نجمع معلومات حول كيفية استخدامك للتطبيق، بما في ذلك الإجراءات التي تقوم بها والميزات التي تستخدمها وأوقات الدخول والخروج.',
                'بيانات الجهاز: نجمع معلومات عن جهازك مثل نوع الجهاز ونظام التشغيل والإصدار وعنوان IP والمعرفات الفريدة للجهاز.',
                'بيانات المعاملات: نحتفظ بسجلات للمعاملات التي تقوم بها داخل التطبيق، بما في ذلك المبيعات والمشتريات والمخزون والتقارير المالية.',
              ],
            ),
            _buildSection(
              context,
              'كيف نستخدم معلوماتك',
              [
                'لتوفير وتحسين خدماتنا وتجربة المستخدم وتخصيص المحتوى وفقًا لاحتياجاتك.',
                'للتواصل معك بخصوص حسابك والتحديثات والإشعارات والتغييرات في سياساتنا.',
                'لتحليل كيفية استخدام التطبيق وتحسين أدائه وتطوير ميزات جديدة.',
                'لحماية أمان وسلامة التطبيق والمستخدمين ومنع الاحتيال والأنشطة غير المصرح بها.',
              ],
            ),
            _buildSection(
              context,
              'حماية البيانات',
              [
                'نحن نتخذ تدابير أمنية مناسبة لحماية معلوماتك من الوصول غير المصرح به أو التغيير أو الإفصاح أو الإتلاف.',
                'نستخدم تقنيات التشفير المتقدمة لحماية البيانات الحساسة مثل كلمات المرور والمعلومات المالية.',
                'نقوم بمراجعة وتحديث ممارسات الأمان بانتظام لضمان حماية بياناتك.',
                'البيانات مخزنة محلياً على جهازك ولا يتم مشاركتها مع أي طرف ثالث دون موافقتك الصريحة.',
              ],
            ),
            _buildSection(
              context,
              'حقوقك',
              [
                'الوصول: يمكنك طلب نسخة من بياناتك الشخصية التي نحتفظ بها.',
                'التصحيح: يمكنك طلب تصحيح أي معلومات غير دقيقة أو غير مكتملة.',
                'الحذف: يمكنك طلب حذف بياناتك الشخصية في ظروف معينة.',
                'تقييد المعالجة: يمكنك طلب تقييد معالجة بياناتك الشخصية في ظروف معينة.',
                'سحب الموافقة: يمكنك سحب موافقتك على معالجة بياناتك في أي وقت.',
              ],
            ),
            _buildSection(
              context,
              'التغييرات في سياسة الخصوصية',
              [
                'قد نقوم بتحديث سياسة الخصوصية هذه من وقت لآخر لتعكس التغييرات في ممارساتنا.',
                'سنخطرك بأي تغييرات جوهرية من خلال إشعار داخل التطبيق أو عبر البريد الإلكتروني.',
                'نشجعك على مراجعة سياسة الخصوصية بانتظام للبقاء على اطلاع بكيفية حماية معلوماتك.',
              ],
            ),
            _buildContactSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
      BuildContext context, String title, List<String> contentItems) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.lightTextSecondary,
                fontSize: Layout.getResponsiveFontSize(18),
              ),
        ),
        SizedBox(height: Layout.h(1)),
        ...contentItems.map((item) => Padding(
              padding: EdgeInsets.only(bottom: Layout.h(1)),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.circle,
                    size: Layout.getResponsiveIconSize(8),
                    color: AppColors.lightTextSecondary,
                  ),
                  SizedBox(width: Layout.w(2)),
                  Expanded(
                    child: Text(
                      item,
                      style: AppTypography(
                        fontSize: Layout.getResponsiveFontSize(14),
                      ),
                    ),
                  ),
                ],
              ),
            )),
        SizedBox(height: Layout.h(2)),
      ],
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التواصل معنا',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.lightTextSecondary,
                fontSize: Layout.getResponsiveFontSize(18),
              ),
        ),
        SizedBox(height: Layout.h(1)),
        Padding(
          padding: EdgeInsets.only(bottom: Layout.h(1)),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.circle,
                size: Layout.getResponsiveIconSize(8),
                color: AppColors.lightTextSecondary,
              ),
              SizedBox(width: Layout.w(2)),
              Expanded(
                child: Text(
                  'إذا كانت لديك أي أسئلة أو استفسارات، يرجى التواصل معنا على:',
                  style: AppTypography(
                    fontSize: Layout.getResponsiveFontSize(14),
                  ),
                ),
              ),
            ],
          ),
        ),
        _buildContactItem(
          context,
          'البريد الإلكتروني:',
          '<EMAIL>',
          Icons.email,
          () async {
            final Uri emailUri = Uri(
              scheme: 'mailto',
              path: '<EMAIL>',
              query: 'subject=استفسار حول تطبيق تاجر بلس',
            );

            try {
              if (await canLaunchUrl(emailUri)) {
                await launchUrl(emailUri);
              } else {
                AppLogger.error('لا يمكن فتح تطبيق البريد الإلكتروني');
              }
            } catch (e) {
              AppLogger.error('خطأ في فتح البريد الإلكتروني: $e');
            }
          },
        ),
        _buildContactItem(
          context,
          'الهاتف:',
          '967770119544',
          Icons.phone,
          () async {
            final Uri phoneUri = Uri(
              scheme: 'tel',
              path: '967770119544',
            );

            try {
              if (await canLaunchUrl(phoneUri)) {
                await launchUrl(phoneUri);
              } else {
                AppLogger.error('لا يمكن فتح تطبيق الهاتف');
              }
            } catch (e) {
              AppLogger.error('خطأ في فتح تطبيق الهاتف: $e');
            }
          },
        ),
        _buildContactItem(
          context,
          'العنوان:',
          'صنعاء - شارع الستين - جوار جامعة العلوم والتكنولوجيا',
          Icons.location_on,
          () async {
            final Uri mapsUri = Uri.parse(
              'https://www.google.com/maps/search/?api=1&query=صنعاء+شارع+الستين+جامعة+العلوم+والتكنولوجيا',
            );

            try {
              if (await canLaunchUrl(mapsUri)) {
                await launchUrl(mapsUri, mode: LaunchMode.externalApplication);
              } else {
                AppLogger.error('لا يمكن فتح تطبيق الخرائط');
              }
            } catch (e) {
              AppLogger.error('خطأ في فتح تطبيق الخرائط: $e');
            }
          },
        ),
        SizedBox(height: Layout.h(2)),
      ],
    );
  }

  Widget _buildContactItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: Layout.h(1)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: Layout.h(0.5),
            horizontal: Layout.w(2),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                size: Layout.getResponsiveIconSize(20),
                color: AppColors.lightTextSecondary,
              ),
              SizedBox(width: Layout.w(2)),
              Text(
                label,
                style: AppTypography(
                  fontSize: Layout.getResponsiveFontSize(14),
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(width: Layout.w(1)),
              Expanded(
                child: Text(
                  value,
                  style: AppTypography(
                    fontSize: Layout.getResponsiveFontSize(14),
                    color: AppColors.lightTextSecondary,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
