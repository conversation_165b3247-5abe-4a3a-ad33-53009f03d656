import 'package:flutter/material.dart';
import '../../../core/models/unit.dart';
import '../presenters/unit_presenter.dart';

import 'unit_form_screen.dart';
import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';
import '../../../core/providers/app_providers.dart'; // للتحميل الكسول

class UnitsScreen extends StatefulWidget {
  final String? unitType; // إضافة نوع الوحدة

  const UnitsScreen({Key? key, this.unitType = 'product'}) : super(key: key);

  // قائمة بأنواع الوحدات المتاحة
  static const List<Map<String, String>> availableTypes = [
    {'id': 'product', 'name': 'منتجات'},
    {'id': 'service', 'name': 'خدمات'},
    {'id': 'time', 'name': 'وقت'},
    {'id': 'weight', 'name': 'وزن'},
    {'id': 'length', 'name': 'طول'},
    {'id': 'area', 'name': 'مساحة'},
    {'id': 'volume', 'name': 'حجم'},
  ];

  @override
  State<UnitsScreen> createState() => _UnitsScreenState();
}

class _UnitsScreenState extends State<UnitsScreen> {
  late UnitPresenter _presenter;
  String? _currentType; // المتغير الحالي لنوع الوحدة

  @override
  void initState() {
    super.initState();
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    _presenter = AppProviders.getUnitPresenter();
    _currentType = widget.unitType;
    _loadUnits();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_currentType != widget.unitType) {
      _currentType = widget.unitType;
      _loadUnits();
    }
  }

  // تحميل الوحدات مع مراعاة النوع
  Future<void> _loadUnits() async {
    await _presenter.loadUnits(type: _currentType);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'Units',
        actions: [
          // زر تصفية الوحدات حسب النوع
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية حسب النوع',
            onPressed: _showTypeFilterDialog,
          ),
        ],
      ),
      floatingActionButton: AkFloatingButton(
        icon: Icons.add,
        onPressed: () => _navigateToForm(context),
        tooltip: 'إضافة وحدة جديدة',
      ),
      body: ListenableBuilder(
        listenable: _presenter,
        builder: (context, child) {
          if (_presenter.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (_presenter.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error: ${_presenter.error}',
                    style: const AppTypography(color: AppColors.error),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadUnits,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final units = _presenter.units;
          if (units.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('No units found'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _navigateToForm(context),
                    child: const Text('Add Unit'),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: units.length,
            itemBuilder: (context, index) {
              final unit = units[index];
              return _buildUnitCard(context, unit);
            },
          );
        },
      ),
    );
  }

  Widget _buildUnitCard(BuildContext context, Unit unit) {
    final baseUnit = unit.baseUnitId != null
        ? _presenter.units.firstWhere(
            (u) => u.id == unit.baseUnitId,
            orElse: () => unit,
          )
        : null;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary,
          child: Text(
            unit.symbol ?? '',
            style: const AppTypography(color: AppColors.onPrimary),
          ),
        ),
        title: Text(
          unit.name,
          style: const AppTypography(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (unit.description != null)
              Text(
                unit.description!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            if (baseUnit != null)
              Text(
                '1 ${unit.symbol} = ${unit.conversionFactor} ${baseUnit.symbol}',
                style: const AppTypography(fontStyle: FontStyle.italic),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _navigateToForm(context, unit),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteConfirmation(context, unit),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _navigateToForm(BuildContext context, [Unit? unit]) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UnitFormScreen(
          unit: unit,
          unitType: widget.unitType,
        ),
      ),
    );

    if (result == true && mounted) {
      _loadUnits();
    }
  }

  // عرض مربع حوار لتصفية الوحدات حسب النوع
  void _showTypeFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية حسب النوع'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              ...UnitsScreen.availableTypes.map((type) => RadioListTile<String>(
                    title: Text(type['name']!),
                    value: type['id']!,
                    groupValue: _currentType,
                    onChanged: (value) {
                      Navigator.pop(context);
                      if (value != _currentType) {
                        setState(() {
                          _currentType = value;
                        });

                        // إعادة تحميل الوحدات بالنوع الجديد
                        _loadUnits();
                      }
                    },
                  )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Future<void> _showDeleteConfirmation(BuildContext context, Unit unit) async {
    // حفظ مرجع للسياق قبل العملية غير المتزامنة
    final scaffoldMessengerState = ScaffoldMessenger.of(context);
    final hasDependent = _presenter.units.any((u) => u.baseUnitId == unit.id);

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Unit'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to delete ${unit.name}?'),
            if (hasDependent)
              const Padding(
                padding: EdgeInsets.only(top: 8),
                child: Text(
                  'Warning: Other units depend on this unit. Deleting it may cause issues.',
                  style: AppTypography(color: AppColors.error),
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final success = await _presenter.deleteUnit(unit.id);
      if (mounted) {
        scaffoldMessengerState.showSnackBar(
          SnackBar(
            content: Text(
              success ? 'Unit deleted successfully' : 'Failed to delete unit',
            ),
            backgroundColor: success ? AppColors.success : AppColors.error,
          ),
        );
      }
    }
  }
}
