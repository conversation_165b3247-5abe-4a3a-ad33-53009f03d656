import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';

import '../presenters/warehouse_presenter.dart';
import '../presenters/inventory_presenter.dart';
import '../../../core/models/warehouse.dart';

import '../../../core/utils/index.dart';
//import '../../shared/widgets/custom_app_bar.dart';
import 'warehouse_form_screen.dart';
import 'inventory_screen.dart';
import 'inventory_management_screen.dart';
import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';

/// شاشة تفاصيل المخزن
class WarehouseDetailsScreen extends StatefulWidget {
  final Warehouse warehouse;

  const WarehouseDetailsScreen({Key? key, required this.warehouse})
      : super(key: key);

  @override
  State<WarehouseDetailsScreen> createState() => _WarehouseDetailsScreenState();
}

class _WarehouseDetailsScreenState extends State<WarehouseDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late Warehouse _warehouse;

  // استخدام التحميل الكسول
  late final WarehousePresenter _warehousePresenter;
  late final InventoryPresenter _inventoryPresenter;

  @override
  void initState() {
    super.initState();
    _warehouse = widget.warehouse;
    _tabController = TabController(length: 2, vsync: this);

    // تهيئة التحميل الكسول
    _warehousePresenter = AppProviders.getLazyPresenter<WarehousePresenter>(
        () => WarehousePresenter());
    _inventoryPresenter = AppProviders.getLazyPresenter<InventoryPresenter>(
        () => InventoryPresenter());

    // تحديد المخزن المحدد في المقدمين
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      _warehousePresenter.selectWarehouse(_warehouse);
      _inventoryPresenter.selectedWarehouse = _warehouse;
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تهيئة أدوات التخطيط
    Layout.init(context);

    // استخدام LayoutUtils لإنشاء شاشة آمنة تتكيف مع المساحة المتاحة
    return Layout.safeScreen(
      appBar: AkAppBar(
        title: _warehouse.name,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'تعديل المخزن',
            onPressed: _editWarehouse,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildWarehouseInfo(),
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'المخزون'),
              Tab(text: 'التحويلات'),
            ],
            // تحسين مظهر علامات التبويب
            labelStyle: AppTypography(
                fontSize: Layout.getResponsiveFontSize(14),
                fontWeight: FontWeight.bold),
            unselectedLabelStyle:
                AppTypography(fontSize: Layout.getResponsiveFontSize(14)),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                InventoryScreen(warehouseId: _warehouse.id),
                const InventoryManagementScreen(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingAkButton(),
    );
  }

  Widget _buildWarehouseInfo() {
    // استخدام Layout لإنشاء بطاقة آمنة تتكيف مع المساحة المتاحة
    return Layout.safeCard(
      margin: Layout.getSafeMargin(horizontal: 4, vertical: 2),
      padding: Layout.getSafePadding(horizontal: 4, vertical: 2),
      child: Layout.safeColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12.0,
        children: [
          // استخدام Layout لإنشاء صف آمن يتكيف مع المساحة المتاحة
          Layout.safeRow(
            children: [
              const Icon(Icons.warehouse, size: 24),
              // استخدام Layout لإنشاء نص آمن يتكيف مع المساحة المتاحة
              Layout.safeText(
                _warehouse.name,
                style: const AppTypography(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
              ),
              if (_warehouse.isDefault)
                Container(
                  margin: EdgeInsets.only(right: Layout.w(2)),
                  padding: EdgeInsets.symmetric(
                    horizontal: Layout.w(2),
                    vertical: Layout.h(0.5),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.lightTextSecondary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Layout.safeText(
                    'افتراضي',
                    style: AppTypography(
                      color: AppColors.lightTextSecondary,
                      fontSize: Layout.getResponsiveFontSize(12),
                    ),
                  ),
                ),
            ],
          ),
          const Divider(),
          _buildInfoRow('الرمز', _warehouse.code),
          _buildInfoRow('العنوان', _warehouse.address),
          _buildInfoRow('رقم الهاتف', _warehouse.phone),
          _buildInfoRow('المدير', _warehouse.managerName),
          _buildInfoRow('الملاحظات', _warehouse.notes),
          Layout.safeRow(
            children: [
              Layout.safeText('الحالة: '),
              Chip(
                label: Layout.safeText(
                  _warehouse.isActive ? 'نشط' : 'غير نشط',
                  style: AppTypography(
                    color: AppColors.lightTextSecondary,
                    fontSize: Layout.getResponsiveFontSize(12),
                  ),
                ),
                backgroundColor:
                    _warehouse.isActive ? AppColors.success : AppColors.error,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String? value) {
    if (value == null || value.isEmpty) return const SizedBox.shrink();

    // استخدام Layout لإنشاء صف آمن يتكيف مع المساحة المتاحة
    return Layout.safeRow(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // استخدام Layout لإنشاء نص آمن يتكيف مع المساحة المتاحة
        Layout.safeText(
          '$label: ',
          style: AppTypography(
            fontWeight: FontWeight.bold,
            fontSize: Layout.getResponsiveFontSize(14),
          ),
        ),
        Expanded(
          child: Layout.safeText(
            value,
            maxLines: 3,
            style: AppTypography(
              fontSize: Layout.getResponsiveFontSize(14),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFloatingAkButton() {
    // استخدام LayoutBuilder للتأكد من أن الزر يتكيف مع المساحة المتاحة
    return LayoutBuilder(
      builder: (context, constraints) {
        // تحديد حجم الأيقونة المناسب بناءً على حجم الشاشة
        final double iconSize = Layout.getResponsiveIconSize(24);

        return _tabController.index == 0
            ? FloatingActionButton(
                onPressed: _addInventoryAdjustment,
                tooltip: 'تعديل المخزون',
                child: Icon(Icons.edit, size: iconSize),
              )
            : FloatingActionButton(
                onPressed: _addStockTransfer,
                tooltip: 'إضافة تحويل مخزون',
                child: Icon(Icons.swap_horiz, size: iconSize),
              );
      },
    );
  }

  void _editWarehouse() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WarehouseFormScreen(warehouse: _warehouse),
      ),
    );

    if (result == true && mounted) {
      // تحديث بيانات المخزن
      await _warehousePresenter.loadWarehouses();

      final updatedWarehouse = _warehousePresenter.warehouses.firstWhere(
        (w) => w.id == _warehouse.id,
        orElse: () => _warehouse,
      );

      setState(() {
        _warehouse = updatedWarehouse;
      });
    }
  }

  void _addInventoryAdjustment() {
    // سيتم تنفيذها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Layout.safeText('سيتم تنفيذ هذه الميزة قريباً')),
    );
  }

  void _addStockTransfer() {
    // سيتم تنفيذها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Layout.safeText('سيتم تنفيذ هذه الميزة قريباً')),
    );
  }
}
