import '../../../core/database/database_service.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/models/purchase.dart';
import '../../accounts/services/accounting_integration_service.dart';
import 'package:uuid/uuid.dart';

/// خدمة إدارة فواتير المشتريات
class PurchaseInvoiceService {
  // Singleton pattern
  static final PurchaseInvoiceService _instance =
      PurchaseInvoiceService._internal();
  factory PurchaseInvoiceService() => _instance;
  PurchaseInvoiceService._internal();

  final DatabaseService _db = DatabaseService.instance;
  final AccountingIntegrationService _accountingService =
      AccountingIntegrationService();

  /// إنشاء فاتورة مشتريات جديدة
  Future<String> createInvoice(Purchase purchase, {String? userId}) async {
    try {
      AppLogger.info('بدء إنشاء فاتورة مشتريات جديدة');

      // إعداد تفاصيل الفاتورة
      final purchaseMap = purchase.toMap();
      purchaseMap['created_by'] = userId;

      // تحويل فاتورة المشتريات إلى فاتورة عامة
      final invoiceMap = {
        'id': purchase.id,
        'invoice_number': purchase.referenceNumber,
        'invoice_type': 'purchase',
        'supplier_id': purchase.supplierId,
        'warehouse_id': purchase.warehouseId,
        'date': purchase.date.toIso8601String(),
        'due_date': purchase.dueDate?.toIso8601String(),
        'status': purchase.status,
        'subtotal': purchase.subtotal,
        'discount_type':
            purchase.isDiscountPercentage ? 'percentage' : 'amount',
        'discount_value': purchase.discount,
        'tax_amount': purchase.tax,
        'total': purchase.total,
        'paid_amount': purchase.paid,
        'balance': purchase.remaining ?? (purchase.total - purchase.paid),
        'notes': purchase.notes,
        'created_at': DateTime.now().toIso8601String(),
        'created_by': userId,
        'is_deleted': 0,
      };

      // إدخال الفاتورة في جدول الفواتير الموحد
      await _db.insert(
        'invoices',
        invoiceMap,
      );

      // إدخال بنود الفاتورة في جدول بنود الفواتير الموحد
      for (final item in purchase.items) {
        final invoiceItemMap = {
          'id': const Uuid().v4(),
          'invoice_id': purchase.id,
          'product_id': item.productId,
          'description': item.productName ?? '',
          'quantity': item.quantity,
          'unit_id': item.unitId,
          'unit_price': item.price,
          'discount_type': item.isDiscountPercentage ? 'percentage' : 'amount',
          'discount_value': item.discount,
          'tax_rate': item.taxRate,
          'tax_amount': item.taxAmount,
          'subtotal': item.subtotal,
          'total': item.total,
          'created_at': DateTime.now().toIso8601String(),
          'is_deleted': 0,
        };

        await _db.insert(
          'invoice_items',
          invoiceItemMap,
        );
      }

      // إنشاء القيود المحاسبية المرتبطة بالفاتورة
      await _accountingService.handlePurchaseInvoice(purchase.toMap(),
          userId: userId);

      AppLogger.info(
          'تم إنشاء فاتورة المشتريات بنجاح مع رقم: ${purchase.referenceNumber}');

      return purchase.id;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إنشاء فاتورة مشتريات',
        error: e,
        stackTrace: stackTrace,
        context: {'purchaseData': purchase.toMap()},
      );
      rethrow;
    }
  }

  /// الحصول على فاتورة مشتريات بواسطة المعرف
  Future<Purchase?> getInvoiceById(String id) async {
    try {
      AppLogger.info('جاري البحث عن فاتورة المشتريات برقم تعريف: $id');

      // الحصول على بيانات الفاتورة
      final purchaseMap = await _db.getById(DatabaseService.tableInvoices, id);

      if (purchaseMap == null) {
        return null;
      }

      // الحصول على بنود الفاتورة
      final itemsList = await _db.query(
        DatabaseService.tableInvoiceItems,
        where: 'invoice_id = ?',
        whereArgs: [id],
      );

      final items =
          itemsList.map((item) => PurchaseItem.fromMap(item)).toList();

      return Purchase.fromMap(purchaseMap, items: items);
    } catch (e) {
      AppLogger.error('خطأ في الحصول على فاتورة المشتريات: $e');
      return null;
    }
  }

  /// الحصول على فاتورة مشتريات بواسطة رقم الفاتورة
  Future<Purchase?> getInvoiceByNumber(String referenceNumber) async {
    try {
      AppLogger.info('جاري البحث عن فاتورة المشتريات برقم: $referenceNumber');

      // الحصول على بيانات الفاتورة
      final purchaseList = await _db.query(
        DatabaseService.tableInvoices,
        where: 'invoice_number = ? AND invoice_type = "purchase"',
        whereArgs: [referenceNumber],
      );

      if (purchaseList.isEmpty) {
        return null;
      }

      final purchaseMap = purchaseList.first;

      // الحصول على بنود الفاتورة
      final itemsList = await _db.query(
        DatabaseService.tableInvoiceItems,
        where: 'invoice_id = ?',
        whereArgs: [purchaseMap['id']],
      );

      final items =
          itemsList.map((item) => PurchaseItem.fromMap(item)).toList();

      return Purchase.fromMap(purchaseMap, items: items);
    } catch (e) {
      AppLogger.error('خطأ في الحصول على فاتورة المشتريات برقم: $e');
      return null;
    }
  }

  /// تحديث حالة فاتورة المشتريات
  Future<bool> updateInvoiceStatus(String id, String status,
      {String? userId}) async {
    try {
      AppLogger.info('تحديث حالة فاتورة المشتريات رقم: $id إلى: $status');

      // تحديث حالة الفاتورة
      await _db.update(
        DatabaseService.tableInvoices,
        {'status': status},
        where: 'id = ? AND invoice_type = "purchase"',
        whereArgs: [id],
      );

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تحديث حالة فاتورة المشتريات: $e');
      return false;
    }
  }

  /// تحديث حالة الدفع لفاتورة المشتريات
  Future<bool> updatePaymentStatus(String id, String paymentType,
      {double? paid, String? userId}) async {
    try {
      AppLogger.info(
          'تحديث حالة دفع فاتورة المشتريات رقم: $id إلى: $paymentType');

      // تحديث حالة الدفع
      final updateData = <String, dynamic>{
        'payment_type': paymentType,
      };

      if (paid != null) {
        updateData['paid'] = paid;
      }

      await _db.update(
        DatabaseService.tableInvoices,
        updateData,
        where: 'id = ? AND invoice_type = "purchase"',
        whereArgs: [id],
      );

      // الحصول على الفاتورة بعد التحديث
      final purchase = await getInvoiceById(id);

      if (purchase != null && paymentType == 'paid') {
        // إنشاء قيد الدفع إذا كانت الحالة مدفوعة
        await _accountingService.handlePurchaseInvoice(purchase.toMap(),
            userId: userId);
      }

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تحديث حالة دفع فاتورة المشتريات: $e');
      return false;
    }
  }

  /// الحصول على قائمة المشتريات
  Future<List<Purchase>> getAllInvoices({
    DateTime? startDate,
    DateTime? endDate,
    String? status,
    String? supplierId,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      AppLogger.info('جاري الحصول على قائمة فواتير المشتريات');

      // بناء شروط الاستعلام
      final whereConditions = <String>[];
      final whereArgs = <dynamic>[];

      if (startDate != null) {
        whereConditions.add('date >= ?');
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereConditions.add('date <= ?');
        whereArgs.add(endDate.toIso8601String());
      }

      if (status != null) {
        whereConditions.add('status = ?');
        whereArgs.add(status);
      }

      if (supplierId != null) {
        whereConditions.add('supplier_id = ?');
        whereArgs.add(supplierId);
      }

      // تنفيذ الاستعلام
      final whereClause =
          whereConditions.isNotEmpty ? whereConditions.join(' AND ') : null;

      final purchasesList = await _db.query(
        DatabaseService.tableInvoices,
        where: whereClause != null
            ? 'invoice_type = "purchase" AND $whereClause'
            : 'invoice_type = "purchase"',
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'date DESC',
        limit: limit,
        offset: offset,
      );

      // جمع كل فواتير المشتريات مع بنودها
      final purchases = <Purchase>[];

      for (final purchaseMap in purchasesList) {
        final id = purchaseMap['id'] as String;

        // الحصول على بنود الفاتورة
        final itemsList = await _db.query(
          DatabaseService.tableInvoiceItems,
          where: 'invoice_id = ?',
          whereArgs: [id],
        );

        final items =
            itemsList.map((item) => PurchaseItem.fromMap(item)).toList();

        purchases.add(Purchase.fromMap(purchaseMap, items: items));
      }

      return purchases;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على قائمة فواتير المشتريات: $e');
      return [];
    }
  }

  /// احتساب إجمالي المشتريات للفترة
  Future<double> calculateTotalPurchases({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      AppLogger.info(
          'احتساب إجمالي المشتريات للفترة من ${startDate.toIso8601String()} إلى ${endDate.toIso8601String()}');

      // الاستعلام عن مجموع المشتريات
      final result = await _db.rawQuery('''
        SELECT SUM(total) as total_purchases
        FROM ${DatabaseService.tableInvoices}
        WHERE invoice_type = 'purchase' AND date >= ? AND date <= ? AND status != 'cancelled'
      ''', [
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ]);

      if (result.isNotEmpty && result.first['total_purchases'] != null) {
        final totalPurchases = result.first['total_purchases'] as double;
        return totalPurchases;
      }

      return 0.0;
    } catch (e) {
      AppLogger.error('خطأ في احتساب إجمالي المشتريات: $e');
      return 0.0;
    }
  }

  /// إنشاء رقم فاتورة جديد
  Future<String> generateInvoiceNumber() async {
    try {
      final today = DateTime.now();
      final year = today.year.toString().substring(2);
      final month = today.month.toString().padLeft(2, '0');
      final prefix = 'PUR-$year$month-';

      // الحصول على أعلى رقم فاتورة موجود
      final result = await _db.rawQuery('''
        SELECT invoice_number
        FROM ${DatabaseService.tableInvoices}
        WHERE invoice_type = 'purchase' AND invoice_number LIKE ?
        ORDER BY invoice_number DESC LIMIT 1
      ''', ['$prefix%']);

      int number = 1;

      if (result.isNotEmpty) {
        final lastNumber = result.first['invoice_number'] as String;
        final lastDigits = lastNumber.substring(prefix.length);
        number = int.parse(lastDigits) + 1;
      }

      return '$prefix${number.toString().padLeft(4, '0')}';
    } catch (e) {
      AppLogger.error('خطأ في إنشاء رقم فاتورة جديد: $e');
      // رقم افتراضي في حالة الخطأ
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      return 'PUR-$timestamp';
    }
  }

  /// حذف فاتورة مشتريات (مع إلغاء القيود المحاسبية المرتبطة)
  Future<bool> deleteInvoice(String id, {String? userId}) async {
    try {
      AppLogger.info('بدء حذف فاتورة المشتريات رقم: $id');

      // التحقق من وجود الفاتورة
      final purchase = await getInvoiceById(id);

      if (purchase == null) {
        return false;
      }

      // إلغاء القيود المحاسبية المرتبطة أولاً
      // يمكننا تنفيذ هذا عندما يتم تطوير دالة الإلغاء في AccountingEngine
      // await _accountingService.reverseEntriesForInvoice(id, 'purchase', userId: userId);

      // حذف بنود الفاتورة
      await _db.delete(
        'purchase_items',
        where: 'purchase_id = ?',
        whereArgs: [id],
      );

      // حذف الفاتورة
      await _db.delete(
        'purchases',
        where: 'id = ?',
        whereArgs: [id],
      );

      AppLogger.info('تم حذف فاتورة المشتريات بنجاح');

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف فاتورة مشتريات',
        error: e,
        stackTrace: stackTrace,
        context: {'purchaseId': id},
      );

      return false;
    }
  }
}
