import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/auth/models/user_role.dart';
import '../../../core/auth/roles_schema.dart';

import '../presenters/role_presenter.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

/// مربع حوار صلاحيات الدور
class RolePermissionsDialog extends StatefulWidget {
  final UserRole role;

  const RolePermissionsDialog({
    Key? key,
    required this.role,
  }) : super(key: key);

  @override
  State<RolePermissionsDialog> createState() => _RolePermissionsDialogState();
}

class _RolePermissionsDialogState extends State<RolePermissionsDialog> {
  // مقدم البيانات
  late RolePresenter _rolePresenter;

  // قائمة الصلاحيات المحددة
  late List<String> _selectedPermissions;

  // حالة التحميل
  bool _isLoading = false;

  // حالة البحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // حالة التوسيع
  final Map<String, bool> _expandedModules = {};

  @override
  void initState() {
    super.initState();

    // تهيئة المقدم
    _rolePresenter =
        AppProviders.getLazyPresenter<RolePresenter>(() => RolePresenter());

    // تهيئة الصلاحيات المحددة - سيتم تحميلها من قاعدة البيانات
    _selectedPermissions = [];

    // تهيئة حالة التوسيع
    for (final module in RolesSchema.permissions.keys) {
      _expandedModules[module] = true;
    }

    // تحميل الصلاحيات الحالية للدور
    _loadRolePermissions();
  }

  /// تحميل صلاحيات الدور من قاعدة البيانات
  Future<void> _loadRolePermissions() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // الحصول على مقدم الصلاحيات
      final permissionPresenter = AppProviders.getPermissionPresenter();

      // تحميل الصلاحيات المرتبطة بالدور
      final rolePermissions =
          await permissionPresenter.getRolePermissions(widget.role.id);

      // تحويل الصلاحيات إلى قائمة رموز
      final permissionCodes = rolePermissions
          .map((p) => p.code)
          .where((code) => code != null)
          .cast<String>()
          .toList();

      setState(() {
        _selectedPermissions = permissionCodes;
        _isLoading = false;
      });

      debugPrint(
          '🔍 تم تحميل ${permissionCodes.length} صلاحية للدور: ${widget.role.displayName}');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل صلاحيات الدور: $e');
      setState(() {
        _selectedPermissions = [];
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// حفظ الصلاحيات
  Future<void> _savePermissions() async {
    // عرض مؤشر التحميل
    setState(() {
      _isLoading = true;
    });

    try {
      // التحقق من وجود صلاحيات محددة
      if (_selectedPermissions.isEmpty) {
        _showWarningSnackBar(
            'لم يتم تحديد أي صلاحيات. هل أنت متأكد من المتابعة؟');

        // إعطاء المستخدم فرصة للتراجع
        await Future.delayed(const Duration(seconds: 2));

        // التحقق مرة أخرى إذا كان المربع الحواري لا يزال مفتوحًا
        if (!mounted) return;
      }

      // عرض رسالة التحميل
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('جاري حفظ الصلاحيات...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      // تحويل رموز الصلاحيات إلى معرفات
      final permissionPresenter = AppProviders.getPermissionPresenter();
      await permissionPresenter.loadPermissions();
      final allPermissions = permissionPresenter.permissions;

      final permissionIds = <String>[];
      for (final code in _selectedPermissions) {
        final permission = allPermissions.firstWhere(
          (p) => p.code == code,
          orElse: () => throw Exception('صلاحية غير موجودة: $code'),
        );
        permissionIds.add(permission.id);
      }

      debugPrint(
          '🔄 تحويل ${_selectedPermissions.length} رمز صلاحية إلى معرفات');
      debugPrint(
          '📋 رموز الصلاحيات: ${_selectedPermissions.take(3).join(', ')}${_selectedPermissions.length > 3 ? '...' : ''}');
      debugPrint(
          '🔗 معرفات الصلاحيات: ${permissionIds.take(3).join(', ')}${permissionIds.length > 3 ? '...' : ''}');

      // تحديث صلاحيات الدور باستخدام المعرفات
      final success = await _rolePresenter.updateRolePermissions(
        widget.role.id,
        permissionIds,
      );

      // التحقق من نجاح العملية
      if (success) {
        // عرض رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ تم حفظ الصلاحيات بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );

          // إغلاق المربع الحواري
          Navigator.pop(context, true);
        }
      } else {
        // عرض رسالة خطأ
        _showErrorSnackBar('❌ فشل في تحديث صلاحيات الدور');
      }
    } catch (e) {
      // عرض رسالة خطأ مفصلة
      _showErrorSnackBar('❌ حدث خطأ أثناء تحديث صلاحيات الدور: $e');
    } finally {
      // إخفاء مؤشر التحميل
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// عرض رسالة تحذير
  void _showWarningSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.warning,
        ),
      );
    }
  }

  /// تحديد جميع الصلاحيات
  void _selectAllPermissions() {
    setState(() {
      _selectedPermissions = [];

      for (final module in RolesSchema.permissions.entries) {
        for (final permission in module.value.keys) {
          _selectedPermissions.add(permission);
        }
      }
    });
  }

  /// إلغاء تحديد جميع الصلاحيات
  void _deselectAllPermissions() {
    setState(() {
      _selectedPermissions = [];
    });
  }

  /// تحديد جميع صلاحيات الوحدة
  void _selectModulePermissions(String module) {
    setState(() {
      for (final permission in RolesSchema.permissions[module]!.keys) {
        if (!_selectedPermissions.contains(permission)) {
          _selectedPermissions.add(permission);
        }
      }
    });
  }

  /// إلغاء تحديد جميع صلاحيات الوحدة
  void _deselectModulePermissions(String module) {
    setState(() {
      for (final permission in RolesSchema.permissions[module]!.keys) {
        _selectedPermissions.remove(permission);
      }
    });
  }

  /// تبديل حالة توسيع الوحدة
  void _toggleModuleExpansion(String module) {
    setState(() {
      _expandedModules[module] = !(_expandedModules[module] ?? false);
    });
  }

  /// تبديل حالة تحديد الصلاحية
  void _togglePermission(String permission) {
    setState(() {
      if (_selectedPermissions.contains(permission)) {
        _selectedPermissions.remove(permission);
      } else {
        _selectedPermissions.add(permission);
      }
    });
  }

  /// الحصول على اسم الوحدة بالعربية
  String _getModuleDisplayName(String module) {
    switch (module) {
      case 'dashboard':
        return 'لوحة التحكم';
      case 'sales':
        return 'المبيعات';
      case 'purchases':
        return 'المشتريات';
      case 'inventory':
        return 'المخزون';
      case 'customers':
        return 'العملاء';
      case 'suppliers':
        return 'الموردين';
      case 'expenses':
        return 'المصروفات';
      case 'users':
        return 'المستخدمين';
      case 'settings':
        return 'الإعدادات';
      case 'reports':
        return 'التقارير';
      default:
        return module;
    }
  }

  /// الحصول على أيقونة الوحدة
  IconData _getModuleIcon(String module) {
    switch (module) {
      case 'dashboard':
        return Icons.dashboard;
      case 'sales':
        return Icons.shopping_cart;
      case 'purchases':
        return Icons.store;
      case 'inventory':
        return Icons.inventory;
      case 'customers':
        return Icons.people;
      case 'suppliers':
        return Icons.local_shipping;
      case 'expenses':
        return Icons.money;
      case 'users':
        return Icons.person;
      case 'settings':
        return Icons.settings;
      case 'reports':
        return Icons.bar_chart;
      default:
        return Icons.extension;
    }
  }

  /// الحصول على لون الوحدة
  Color _getModuleColor(String module) {
    return AppColors.getModuleColor(module);
  }

  @override
  Widget build(BuildContext context) {
    // حساب عدد الصلاحيات المحددة
    final totalPermissions =
        RolesSchema.permissions.values.expand((module) => module.keys).length;
    final selectedCount = _selectedPermissions.length;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.maxFinite,
        constraints: const BoxConstraints(
          maxWidth: 800,
          maxHeight: 600,
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس مربع الحوار
            Row(
              children: [
                const Icon(
                  Icons.security,
                  color: AppColors.lightTextSecondary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'صلاحيات الدور: ${widget.role.displayName}',
                        style: const AppTypography(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'تم تحديد $selectedCount من أصل $totalPermissions صلاحية',
                        style: const AppTypography(
                          fontSize: 14,
                          color: AppColors.lightTextSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                  tooltip: 'إغلاق',
                ),
              ],
            ),
            const Divider(height: 24),

            // شريط البحث
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: AppColors.lightTextSecondary,
                border: Border.all(color: AppColors.lightSurfaceVariant),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.lightTextSecondary.withValues(alpha: 0.05),
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'بحث عن صلاحية...',
                  hintStyle:
                      const AppTypography(color: AppColors.lightTextSecondary),
                  prefixIcon:
                      const Icon(Icons.search, color: AppColors.primary),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, size: 18),
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _searchQuery = '';
                            });
                          },
                          tooltip: 'مسح البحث',
                          color: AppColors.lightTextSecondary,
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
                style: const AppTypography(fontSize: 16),
                textInputAction: TextInputAction.search,
                textAlignVertical: TextAlignVertical.center,
              ),
            ),
            const SizedBox(height: 16),

            // شريط التقدم
            LinearProgressIndicator(
              value:
                  totalPermissions > 0 ? selectedCount / totalPermissions : 0,
              backgroundColor: AppColors.lightSurfaceVariant,
              valueColor: AlwaysStoppedAnimation<Color>(
                selectedCount > 0
                    ? AppColors.primary
                    : AppColors.lightTextSecondary,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            const SizedBox(height: 8),

            // عرض نسبة الصلاحيات المحددة
            Text(
              'تم تحديد $selectedCount من أصل $totalPermissions صلاحية (${(totalPermissions > 0 ? (selectedCount / totalPermissions * 100).toStringAsFixed(0) : '0')}%)',
              style: const AppTypography(
                fontSize: 14,
                color: AppColors.lightTextSecondary,
              ),
            ),
            const SizedBox(height: 16),

            // أزرار التحديد
            Wrap(
              spacing: 8,
              runSpacing: 8,
              alignment: WrapAlignment.start,
              children: [
                ElevatedButton.icon(
                  onPressed: _selectAllPermissions,
                  icon: const Icon(Icons.select_all, size: 18),
                  label: const Text('تحديد الكل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                    foregroundColor: AppColors.primary,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _deselectAllPermissions,
                  icon: const Icon(Icons.deselect, size: 18),
                  label: const Text('إلغاء الكل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.lightSurfaceVariant,
                    foregroundColor: AppColors.lightTextSecondary,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // قائمة الصلاحيات
            Expanded(
              child: _buildPermissionsList(),
            ),

            // أزرار الإجراءات
            const Divider(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _savePermissions,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.onPrimary),
                          ),
                        )
                      : const Text('حفظ الصلاحيات'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة الصلاحيات
  Widget _buildPermissionsList() {
    // تصنيف الوحدات حسب الأهمية
    final sortedModules = _getSortedModules();

    // إذا كان هناك بحث، نعرض فقط الوحدات التي تحتوي على صلاحيات مطابقة
    final filteredModules = sortedModules.where((module) {
      final permissions = RolesSchema.permissions[module]!;

      // فلترة الصلاحيات حسب البحث
      final filteredPermissions = _searchQuery.isEmpty
          ? permissions
          : Map<String, String>.fromEntries(
              permissions.entries.where((entry) {
                final permissionKey = entry.key.toLowerCase();
                final permissionValue = entry.value.toLowerCase();
                final query = _searchQuery.toLowerCase();

                return permissionKey.contains(query) ||
                    permissionValue.contains(query);
              }).toList(),
            );

      return filteredPermissions.isNotEmpty;
    }).toList();

    if (filteredModules.isEmpty && _searchQuery.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search_off,
              size: 64,
              color: AppColors.lightTextSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد صلاحيات مطابقة لـ "$_searchQuery"',
              style: const AppTypography(
                fontSize: 16,
                color: AppColors.lightTextSecondary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            TextButton.icon(
              onPressed: () {
                _searchController.clear();
                setState(() {
                  _searchQuery = '';
                });
              },
              icon: const Icon(Icons.clear),
              label: const Text('مسح البحث'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredModules.length,
      itemBuilder: (context, index) {
        final module = filteredModules[index];
        final permissions = RolesSchema.permissions[module]!;

        // فلترة الصلاحيات حسب البحث
        final filteredPermissions = _searchQuery.isEmpty
            ? permissions
            : Map<String, String>.fromEntries(
                permissions.entries.where((entry) {
                  final permissionKey = entry.key.toLowerCase();
                  final permissionValue = entry.value.toLowerCase();
                  final query = _searchQuery.toLowerCase();

                  return permissionKey.contains(query) ||
                      permissionValue.contains(query);
                }).toList(),
              );

        return _buildModulePermissions(
          module,
          filteredPermissions,
        );
      },
    );
  }

  /// الحصول على الوحدات مرتبة حسب الأهمية
  List<String> _getSortedModules() {
    // ترتيب الوحدات حسب الأهمية
    final moduleOrder = {
      'dashboard': 1,
      'sales': 2,
      'purchases': 3,
      'inventory': 4,
      'customers': 5,
      'suppliers': 6,
      'expenses': 7,
      'reports': 8,
      'users': 9,
      'settings': 10,
    };

    // ترتيب الوحدات
    final modules = RolesSchema.permissions.keys.toList();
    modules.sort((a, b) {
      final orderA = moduleOrder[a] ?? 999;
      final orderB = moduleOrder[b] ?? 999;
      return orderA.compareTo(orderB);
    });

    return modules;
  }

  /// بناء صلاحيات الوحدة
  Widget _buildModulePermissions(
    String module,
    Map<String, String> permissions,
  ) {
    final isExpanded = _expandedModules[module] ?? false;

    // حساب عدد الصلاحيات المحددة في الوحدة
    final selectedCount = permissions.keys
        .where((permission) => _selectedPermissions.contains(permission))
        .length;

    // حساب نسبة الصلاحيات المحددة
    final percentage =
        permissions.isNotEmpty ? (selectedCount / permissions.length) : 0.0;

    // تحديد لون الوحدة
    final moduleColor = _getModuleColor(module);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: moduleColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // رأس الوحدة
          Container(
            decoration: BoxDecoration(
              color: moduleColor.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: ListTile(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              leading: CircleAvatar(
                backgroundColor: moduleColor.withValues(alpha: 0.2),
                child: Icon(
                  _getModuleIcon(module),
                  color: moduleColor,
                  size: 20,
                ),
              ),
              title: Row(
                children: [
                  Text(
                    _getModuleDisplayName(module),
                    style: const AppTypography(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: moduleColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '$selectedCount / ${permissions.length}',
                      style: AppTypography(
                        fontSize: 12,
                        color: moduleColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  // شريط التقدم
                  LinearProgressIndicator(
                    value: percentage,
                    backgroundColor: AppColors.lightSurfaceVariant,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      selectedCount > 0
                          ? moduleColor
                          : AppColors.lightTextSecondary,
                    ),
                    borderRadius: BorderRadius.circular(4),
                    minHeight: 6,
                  ),
                ],
              ),
              trailing: Wrap(
                spacing: 0,
                children: [
                  // زر تحديد الكل
                  IconButton(
                    icon: const Icon(Icons.select_all, size: 18),
                    onPressed: () => _selectModulePermissions(module),
                    tooltip: 'تحديد الكل',
                    color: moduleColor,
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(
                      minWidth: 36,
                      minHeight: 36,
                    ),
                  ),

                  // زر إلغاء تحديد الكل
                  IconButton(
                    icon: const Icon(Icons.deselect, size: 18),
                    onPressed: () => _deselectModulePermissions(module),
                    tooltip: 'إلغاء تحديد الكل',
                    color: AppColors.lightTextSecondary,
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(
                      minWidth: 36,
                      minHeight: 36,
                    ),
                  ),

                  // زر التوسيع
                  IconButton(
                    icon: Icon(
                      isExpanded ? Icons.expand_less : Icons.expand_more,
                      size: 20,
                    ),
                    onPressed: () => _toggleModuleExpansion(module),
                    tooltip: isExpanded ? 'طي' : 'توسيع',
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(
                      minWidth: 36,
                      minHeight: 36,
                    ),
                  ),
                ],
              ),
              onTap: () => _toggleModuleExpansion(module),
            ),
          ),

          // قائمة الصلاحيات
          if (isExpanded)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: permissions.length,
                itemBuilder: (context, index) {
                  final permission = permissions.keys.elementAt(index);
                  final permissionName = permissions[permission]!;
                  final isSelected = _selectedPermissions.contains(permission);

                  return InkWell(
                    onTap: () => _togglePermission(permission),
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      margin: const EdgeInsets.symmetric(vertical: 4),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? moduleColor.withValues(alpha: 0.05)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected
                              ? moduleColor.withValues(alpha: 0.3)
                              : AppColors.lightSurfaceVariant
                                  .withValues(alpha: 0.5),
                          width: 1,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Row(
                          children: [
                            // Checkbox
                            SizedBox(
                              width: 24,
                              height: 24,
                              child: Checkbox(
                                value: isSelected,
                                onChanged: (value) =>
                                    _togglePermission(permission),
                                activeColor: moduleColor,
                                checkColor: AppColors.onPrimary,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),

                            // Permission details
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Permission name
                                  Text(
                                    permissionName,
                                    style: AppTypography(
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                      fontSize: 14,
                                      color: isSelected
                                          ? moduleColor
                                          : Theme.of(context)
                                                  .textTheme
                                                  .bodyLarge
                                                  ?.color ??
                                              AppColors.lightTextPrimary,
                                    ),
                                  ),
                                  const SizedBox(height: 4),

                                  // Permission code
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(
                                        Icons.code,
                                        size: 12,
                                        color: AppColors.lightTextSecondary,
                                      ),
                                      const SizedBox(width: 4),
                                      Expanded(
                                        child: Text(
                                          permission,
                                          style: const AppTypography(
                                            fontSize: 12,
                                            color: AppColors.lightTextSecondary,
                                            fontFamily: 'monospace',
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 1,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            // Icon indicator
                            if (isSelected)
                              Icon(
                                Icons.check_circle,
                                color: moduleColor,
                                size: 16,
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
        ),
      );
    });
  }
}
