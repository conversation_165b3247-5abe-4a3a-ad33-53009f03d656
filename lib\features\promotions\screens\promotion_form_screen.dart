import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../models/promotion.dart';
import '../presenter/promotion_presenter.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

class PromotionFormScreen extends StatefulWidget {
  final Promotion? promotion;

  const PromotionFormScreen({Key? key, this.promotion}) : super(key: key);

  @override
  State<PromotionFormScreen> createState() => _PromotionFormScreenState();
}

class _PromotionFormScreenState extends State<PromotionFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _expiryDateController;
  late TextEditingController _actionTextController;

  String _colorHex = '#D32F2F'; // Color rojo por defecto
  String _iconName = 'local_offer'; // Icono por defecto
  bool _isActive = true;
  late PromotionPresenter _presenter;

  final List<Map<String, dynamic>> _predefinedColors = [
    {'name': 'أحمر', 'hex': '#D32F2F'},
    {'name': 'أزرق', 'hex': '#1976D2'},
    {'name': 'أخضر', 'hex': '#388E3C'},
    {'name': 'برتقالي', 'hex': '#F57C00'},
    {'name': 'أرجواني', 'hex': '#7B1FA2'},
    {'name': 'بنفسجي', 'hex': '#512DA8'},
    {'name': 'وردي', 'hex': '#C2185B'},
    {'name': 'رمادي', 'hex': '#616161'},
  ];

  final List<Map<String, dynamic>> _predefinedIcons = [
    {'name': 'عرض', 'value': 'local_offer'},
    {'name': 'جديد', 'value': 'new_releases'},
    {'name': 'شحن', 'value': 'local_shipping'},
    {'name': 'خصم', 'value': 'discount'},
    {'name': 'تسوق', 'value': 'shopping_cart'},
    {'name': 'مميز', 'value': 'star'},
    {'name': 'شائع', 'value': 'trending_up'},
    {'name': 'احتفال', 'value': 'celebration'},
    {'name': 'جائزة', 'value': 'emoji_events'},
    {'name': 'هدية', 'value': 'card_giftcard'},
  ];

  @override
  void initState() {
    super.initState();
    _presenter = AppProviders.getLazyPresenter<PromotionPresenter>(
        () => PromotionPresenter());

    // تهيئة القيم باستخدام البيانات الموجودة إذا كنا في وضع التعديل
    final promotion = widget.promotion;
    _titleController = TextEditingController(text: promotion?.title ?? '');
    _descriptionController =
        TextEditingController(text: promotion?.description ?? '');
    _expiryDateController =
        TextEditingController(text: promotion?.expiryDate ?? '');
    _actionTextController =
        TextEditingController(text: promotion?.actionText ?? 'تسوق الآن');

    if (promotion != null) {
      _colorHex = promotion.colorHex ?? _colorHex;
      _iconName = promotion.iconName ?? _iconName;
      _isActive = promotion.isActive;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _expiryDateController.dispose();
    _actionTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.promotion != null;

    return Scaffold(
      appBar: AkAppBar(
        title: isEditing ? 'تعديل العرض' : 'إضافة عرض جديد',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPromotionPreview(),
              const SizedBox(height: 24),
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان العرض',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال عنوان العرض';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف العرض',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال وصف العرض';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _expiryDateController,
                decoration: const InputDecoration(
                  labelText: 'تاريخ انتهاء العرض',
                  border: OutlineInputBorder(),
                  hintText: 'مثال: 31/12/2023',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال تاريخ انتهاء العرض';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _actionTextController,
                decoration: const InputDecoration(
                  labelText: 'نص الإجراء (زر العمل)',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال نص الإجراء';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              const Text(
                'اختر لون العرض:',
                style: AppTypography(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              _buildColorPicker(),
              const SizedBox(height: 24),
              const Text(
                'اختر أيقونة العرض:',
                style: AppTypography(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              _buildIconPicker(),
              const SizedBox(height: 24),
              SwitchListTile(
                title: const Text('العرض نشط'),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _savePromotion,
                  child: Text(isEditing ? 'تحديث العرض' : 'إضافة العرض'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPromotionPreview() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Color(int.parse(_colorHex.replaceAll('#', '0xff'))),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            _getIconData(_iconName),
            color: AppColors.lightTextSecondary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _titleController.text.isNotEmpty
                  ? _titleController.text
                  : 'معاينة العرض',
              style: const AppTypography(
                color: AppColors.lightTextSecondary,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorPicker() {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      children: _predefinedColors.map((color) {
        final colorValue =
            Color(int.parse(color['hex'].replaceAll('#', '0xff')));
        final isSelected = _colorHex == color['hex'];

        return GestureDetector(
          onTap: () {
            setState(() {
              _colorHex = color['hex'];
            });
          },
          child: Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: colorValue,
              borderRadius: BorderRadius.circular(10),
              border: isSelected
                  ? Border.all(color: AppColors.lightTextSecondary, width: 2)
                  : null,
            ),
            child: isSelected
                ? const Icon(Icons.check, color: AppColors.onPrimary)
                : null,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildIconPicker() {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      children: _predefinedIcons.map((iconInfo) {
        final isSelected = _iconName == iconInfo['value'];

        return GestureDetector(
          onTap: () {
            setState(() {
              _iconName = iconInfo['value'];
            });
          },
          child: Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              color: isSelected
                  ? Color(int.parse(_colorHex.replaceAll('#', '0xff')))
                  : AppColors.lightSurfaceVariant,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getIconData(iconInfo['value']),
                  color: isSelected
                      ? AppColors.onPrimary
                      : AppColors.lightSurfaceVariant,
                  size: 30,
                ),
                const SizedBox(height: 4),
                Text(
                  iconInfo['name'],
                  style: AppTypography(
                    color: isSelected
                        ? AppColors.onPrimary
                        : AppColors.lightSurfaceVariant,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'local_offer':
        return Icons.local_offer;
      case 'new_releases':
        return Icons.new_releases;
      case 'local_shipping':
        return Icons.local_shipping;
      case 'discount':
        return Icons.discount;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'star':
        return Icons.star;
      case 'trending_up':
        return Icons.trending_up;
      case 'celebration':
        return Icons.celebration;
      case 'emoji_events':
        return Icons.emoji_events;
      case 'card_giftcard':
        return Icons.card_giftcard;
      default:
        return Icons.local_offer;
    }
  }

  Future<void> _savePromotion() async {
    if (_formKey.currentState?.validate() ?? false) {
      // حفظ البيانات
      final promotion = widget.promotion != null
          ? widget.promotion!.copyWith(
              title: _titleController.text,
              description: _descriptionController.text,
              expiryDate: _expiryDateController.text,
              colorHex: _colorHex,
              iconName: _iconName,
              actionText: _actionTextController.text,
              isActive: _isActive,
            )
          : Promotion(
              title: _titleController.text,
              description: _descriptionController.text,
              expiryDate: _expiryDateController.text,
              colorHex: _colorHex,
              iconName: _iconName,
              actionText: _actionTextController.text,
              isActive: _isActive,
            );

      bool success;
      if (widget.promotion != null) {
        success = await _presenter.updatePromotion(promotion);
      } else {
        success = await _presenter.addPromotion(promotion);
      }

      if (success) {
        if (mounted) {
          Navigator.pop(context);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ: ${_presenter.error ?? 'حدث خطأ غير معروف'}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }
}
