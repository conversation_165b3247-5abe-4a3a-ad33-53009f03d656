import 'package:uuid/uuid.dart';

/// نموذج العرض الترويجي
class Promotion {
  /// معرف العرض
  final String? id;

  /// عنوان العرض
  final String title;

  /// وصف العرض
  final String description;

  /// تاريخ انتهاء العرض
  final String expiryDate;

  /// لون العرض (بصيغة hex)
  final String? colorHex;

  /// اسم الأيقونة
  final String? iconName;

  /// نص الإجراء
  final String? actionText;

  /// ترتيب العرض
  final int? displayOrder;

  /// حالة نشاط العرض
  final bool isActive;

  /// تاريخ الإنشاء
  final DateTime? createdAt;

  /// تاريخ التحديث
  final DateTime? updatedAt;

  /// إنشاء عرض ترويجي جديد
  Promotion({
    this.id,
    required this.title,
    required this.description,
    required this.expiryDate,
    this.colorHex,
    this.iconName,
    this.actionText,
    this.displayOrder,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
  });

  /// إنشاء نسخة من هذا العرض مع استبدال الحقول المحددة بقيم جديدة
  Promotion copyWith({
    String? id,
    String? title,
    String? description,
    String? expiryDate,
    String? colorHex,
    String? iconName,
    String? actionText,
    int? displayOrder,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Promotion(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      expiryDate: expiryDate ?? this.expiryDate,
      colorHex: colorHex ?? this.colorHex,
      iconName: iconName ?? this.iconName,
      actionText: actionText ?? this.actionText,
      displayOrder: displayOrder ?? this.displayOrder,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل العرض إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id ?? const Uuid().v4(),
      'title': title,
      'description': description,
      'expiry_date': expiryDate,
      'color_hex': colorHex,
      'icon_name': iconName,
      'action_text': actionText,
      'display_order': displayOrder ?? 0,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt?.toIso8601String() ?? DateTime.now().toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء عرض من خريطة
  factory Promotion.fromMap(Map<String, dynamic> map) {
    return Promotion(
      id: map['id'],
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      expiryDate: map['expiry_date'] ?? '',
      colorHex: map['color_hex'],
      iconName: map['icon_name'],
      actionText: map['action_text'],
      displayOrder: map['display_order'] as int? ?? 0,
      isActive: map['is_active'] == 1,
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  /// تحويل العرض إلى JSON
  Map<String, dynamic> toJson() => toMap();

  /// إنشاء عرض من JSON
  factory Promotion.fromJson(Map<String, dynamic> json) => Promotion.fromMap(json);

  @override
  String toString() {
    return 'Promotion(id: $id, title: $title, expiryDate: $expiryDate)';
  }
}
