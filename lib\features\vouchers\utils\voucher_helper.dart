import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../presenters/voucher_presenter.dart';
import '../../accounts/presenters/account_presenter.dart';
import '../../currencies/presenters/currency_presenter.dart';
import '../../../core/utils/index.dart';

/// مساعد للتعامل مع السندات بطريقة آمنة
class VoucherHelper {
  /// تحميل جميع البيانات المطلوبة للسندات
  static Future<VoucherData?> loadVoucherData(
    BuildContext context,
    String voucherType, {
    void Function(bool)? setLoading,
  }) async {
    return await Helpers.safeLoadData<VoucherData>(
      context,
      () async {
        // الحصول على المقدمات باستخدام التحميل الكسول
        final accountPresenter =
            AppProviders.getLazyPresenter<AccountPresenter>(
                () => AccountPresenter());
        final currencyPresenter =
            AppProviders.getLazyPresenter<CurrencyPresenter>(
                () => CurrencyPresenter());
        final voucherPresenter =
            AppProviders.getLazyPresenter<VoucherPresenter>(
                () => VoucherPresenter());

        // تحميل البيانات بالتتابع
        await accountPresenter.loadAccounts();
        await currencyPresenter.loadCurrencies();
        await voucherPresenter.loadVouchers(voucherType: voucherType);

        return VoucherData(
          accounts: accountPresenter.accounts,
          currencies: currencyPresenter.currencies,
          vouchers: voucherPresenter.vouchers
              .where((v) => v.voucherType == voucherType)
              .toList(),
          defaultCurrency: currencyPresenter.defaultCurrency,
        );
      },
      setLoading: setLoading,
      errorMessage: 'فشل في تحميل بيانات السندات',
    );
  }

  /// حفظ سند بطريقة آمنة
  static Future<bool> saveVoucher(
    BuildContext context,
    dynamic voucher, {
    bool isUpdate = false,
    String? successMessage,
    String? errorMessage,
  }) async {
    return await Helpers.safeExecute(
      context,
      () async {
        final voucherPresenter =
            AppProviders.getLazyPresenter<VoucherPresenter>(
                () => VoucherPresenter());

        bool success;
        if (isUpdate) {
          success = await voucherPresenter.updateVoucher(voucher);
        } else {
          success = await voucherPresenter.addVoucher(voucher);
        }

        if (!success) {
          throw Exception('فشل في حفظ السند');
        }
      },
      successMessage: successMessage ?? 'تم حفظ السند بنجاح',
      errorMessage: errorMessage ?? 'فشل في حفظ السند',
    );
  }

  /// حذف سند بطريقة آمنة
  static Future<bool> deleteVoucher(
    BuildContext context,
    VoucherPresenter voucherPresenter,
    ScaffoldMessengerState scaffoldMessenger,
    String voucherId, {
    String? successMessage,
    String? errorMessage,
  }) async {
    // عرض تأكيد الحذف أولاً
    final confirm = await Helpers.showConfirmDialog(
      context,
      message: 'هل تريد حذف هذا السند؟',
    );

    if (confirm != true) return false;

    try {
      final success = await voucherPresenter.deleteVoucher(voucherId);

      if (success) {
        scaffoldMessenger.showSnackBar(
          SnackBar(content: Text(successMessage ?? 'تم حذف السند بنجاح')),
        );
        return true;
      } else {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(errorMessage ?? 'فشل في حذف السند'),
            backgroundColor: Colors.red,
          ),
        );
        return false;
      }
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content:
              Text('${errorMessage ?? "فشل في حذف السند"}: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }
  }

  /// تحديث سعر الصرف بناءً على العملة المختارة
  static double updateExchangeRate(
    CurrencyPresenter currencyPresenter,
    String? currencyId,
  ) {
    if (currencyId == null) return 1.0;

    try {
      final currency = currencyPresenter.currencies.firstWhere(
        (c) => c.id == currencyId,
        orElse: () => currencyPresenter.defaultCurrency!,
      );
      return currency.exchangeRate;
    } catch (e) {
      debugPrint('خطأ في تحديث سعر الصرف: $e');
      return 1.0;
    }
  }

  /// التحقق من صحة بيانات السند
  static String? validateVoucherData({
    required String? amount,
    required String? accountId,
    required String? currencyId,
    String? secondAccountId, // للقيد المزدوج
  }) {
    if (amount == null || amount.isEmpty) {
      return 'يرجى إدخال المبلغ';
    }

    final parsedAmount = double.tryParse(amount);
    if (parsedAmount == null) {
      return 'يرجى إدخال رقم صحيح';
    }

    if (parsedAmount <= 0) {
      return 'يجب أن يكون المبلغ أكبر من صفر';
    }

    if (accountId == null) {
      return 'يرجى اختيار الحساب';
    }

    if (currencyId == null) {
      return 'يرجى اختيار العملة';
    }

    if (secondAccountId != null && secondAccountId == accountId) {
      return 'لا يمكن أن يكون حساب المدين والدائن نفس الحساب';
    }

    return null; // لا توجد أخطاء
  }

  /// تصفية السندات حسب النص المدخل
  static List<dynamic> filterVouchers(
    List<dynamic> vouchers,
    String query,
  ) {
    if (query.isEmpty) return vouchers;

    final lowerQuery = query.toLowerCase();
    return vouchers.where((voucher) {
      return voucher.referenceNumber?.toLowerCase().contains(lowerQuery) ==
              true ||
          voucher.accountName?.toLowerCase().contains(lowerQuery) == true ||
          voucher.handler?.toLowerCase().contains(lowerQuery) == true ||
          voucher.notes?.toLowerCase().contains(lowerQuery) == true;
    }).toList();
  }

  /// تنسيق تاريخ السند
  static String formatVoucherDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// تنسيق مبلغ السند
  static String formatVoucherAmount(double amount, String currencySymbol) {
    return '${amount.toStringAsFixed(2)} $currencySymbol';
  }
}

/// فئة لحفظ بيانات السندات
class VoucherData {
  final List<Map<String, dynamic>> accounts;
  final List<dynamic> currencies;
  final List<dynamic> vouchers;
  final dynamic defaultCurrency;

  VoucherData({
    required this.accounts,
    required this.currencies,
    required this.vouchers,
    this.defaultCurrency,
  });
}
