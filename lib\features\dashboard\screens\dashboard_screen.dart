import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'dart:math' as math;
import '../presenters/dashboard_presenter.dart';
import '../../../core/widgets/index.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد
import '../widgets/dashboard_header.dart';
import '../widgets/animated_chart.dart';
import '../../../core/auth/services/auth_service.dart';
import '../../users/models/user.dart';

/// شاشة لوحة المعلومات التفاعلية
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  late DashboardPresenter _dashboardPresenter;
  String _selectedPeriod = 'month';
  String _selectedChartType = 'sales';

  @override
  void initState() {
    super.initState();
    _dashboardPresenter = AppProviders.getLazyPresenter<DashboardPresenter>(
        () => DashboardPresenter());
    // استخدام ميكانيكية آمنة لتحميل البيانات بعد بناء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadData();
      }
    });
  }

  Future<void> _loadData() async {
    await _dashboardPresenter.loadDashboardData(period: _selectedPeriod);
  }

  // تم تعليق هذه الدالة لأنها غير مستخدمة
  /*
  Future<void> _reloadDataOnError() async {
    // تجنب إعادة المحاولة المتكررة باستخدام متغير للتحكم
    if (_isReloading) return;
    _isReloading = true;

    try {
      // تسجيل الخطأ في سجل الأخطاء للمطورين
      AppLogger.error(
          'خطأ في تحميل بيانات لوحة المعلومات: ${_dashboardPresenter.error}');
      ErrorTracker.captureError(
        'فشل في تحميل بيانات لوحة المعلومات',
        error: _dashboardPresenter.error ?? 'خطأ غير معروف',
        stackTrace: StackTrace.current,
        context: {'screen': 'dashboard', 'period': _selectedPeriod},
      );

      // إعادة تحميل البيانات مرة واحدة فقط
      await _loadData();
    } catch (e) {
      // التحقق من أن الشاشة لا تزال مرتبطة بشجرة العناصر
      if (!mounted) return;

      // تسجيل الخطأ الجديد
      AppLogger.error('فشل في إعادة تحميل بيانات لوحة المعلومات: $e');

      // عرض رسالة خطأ صغيرة بدلاً من مربع حوار كامل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              const Text('تعذر تحميل بعض البيانات. سيتم عرض البيانات المتاحة.'),
          duration: Duration(seconds: AppDimensions.snackBarDuration.toInt()),
          action: SnackBarAction(
            label: 'إعادة المحاولة',
            onPressed: () {
              _isReloading = false; // السماح بإعادة المحاولة عند الضغط على الزر
              _loadData();
            },
          ),
        ),
      );
    } finally {
      // إعادة تعيين متغير التحكم بعد الانتهاء
      _isReloading = false;
    }
  }
  */

  // المؤشر الحالي للشريط السفلي
  int _currentIndex = 0;
  // متغير للتحكم في إعادة تحميل البيانات لمنع التكرار
  bool _isReloading = false;

  @override
  Widget build(BuildContext context) {
    // تحديد العنوان بناءً على المؤشر الحالي (لا نستخدمه حاليًا لأننا نستخدم رأس مخصص)
    switch (_currentIndex) {
      case 0:
        // الرئيسية
        break;
      case 1:
        // المبيعات
        break;
      case 2:
        // المخزون
        break;
      case 3:
        // التقارير
        break;
      default:
      // الرئيسية
    }

    final isDarkMode = DynamicColors.isDarkMode(context);

    return Scaffold(
      // إضافة القائمة الجانبية
      drawer: const AppDrawer(),
      body: ListenableBuilder(
        listenable: _dashboardPresenter,
        builder: (context, child) {
          if (_dashboardPresenter.isLoading) {
            return Stack(
              children: [
                // خلفية متدرجة عصرية باستخدام الألوان الجديدة
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: isDarkMode
                          ? AppColors.darkGradient
                          : [
                              AppColors.lightBackground,
                              AppColors.lightSurfaceVariant,
                            ],
                    ),
                  ),
                ),
                const Center(child: AkLoadingIndicator()),
              ],
            );
          }

          // عرض الشاشة العادية حتى مع وجود خطأ
          // نعرض رسالة خطأ فقط دون محاولة إعادة التحميل التلقائي لتجنب الحلقة المتكررة
          if (_dashboardPresenter.error != null && !_isReloading) {
            // عرض رسالة خطأ صغيرة بدلاً من إعادة التحميل التلقائي
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text(
                        'تعذر تحميل بعض البيانات. اسحب للأسفل لإعادة المحاولة.'),
                    duration: Duration(
                        seconds: AppDimensions.snackBarDuration.toInt()),
                    action: SnackBarAction(
                      label: 'إعادة المحاولة',
                      onPressed: () {
                        _isReloading = false;
                        _loadData();
                      },
                    ),
                  ),
                );
              }
            });
          }

          return RefreshIndicator(
            onRefresh: _loadData,
            child: Stack(
              children: [
                // خلفية متدرجة عصرية باستخدام الألوان الجديدة
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: isDarkMode
                          ? AppColors.darkGradient
                          : [
                              AppColors.lightBackground,
                              AppColors.lightSurfaceVariant,
                            ],
                    ),
                  ),
                ),

                // محتوى الشاشة
                CustomScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  slivers: [
                    // رأس الشاشة
                    SliverAppBar(
                      expandedHeight: AppDimensions.appBarExpandedHeight,
                      floating: false,
                      pinned: true,
                      elevation: AppDimensions.elevationNone,
                      backgroundColor: AppColors.transparent,
                      // إزالة زر القائمة الجانبية - سيتم فتحها من صورة البروفايل
                      automaticallyImplyLeading: false,
                      flexibleSpace: FlexibleSpaceBar(
                        background: FutureBuilder<User?>(
                          future: AuthService().getCurrentUser(),
                          builder: (context, snapshot) {
                            final currentUser = snapshot.data;
                            return DashboardHeader(
                              userName: currentUser?.fullName ??
                                  currentUser?.username ??
                                  'مدير النظام',
                              userImage: currentUser?.avatar,
                              welcomeTitle: 'مرحباً بك في',
                              welcomeMessage: 'نظام تاجر بلس المحاسبي',
                              onUserTap: () {
                                Navigator.pushNamed(context, '/profile');
                              },
                              // إضافة دالة فتح القائمة الجانبية عند النقر على صورة البروفايل
                              onOpenDrawer: () {
                                Scaffold.of(context).openDrawer();
                              },
                              onNotificationTap: () {
                                Navigator.pushNamed(context, '/notifications');
                              },
                              unreadNotifications: 4,
                              backgroundColor:
                                  AppColors.error.withValues(alpha: 0.1),
                              textColor: DynamicColors.textPrimary(context),
                            );
                          },
                        ),
                      ),
                    ),

                    // محتوى اللوحة - استخدام النظام الموحد
                    SliverPadding(
                      //   padding: AppDimensions.screenPadding,
                      padding: AppDimensions.getResponsivePadding(
                          horizontal: 1, vertical: 1), // 1% أفقي، 2% عمودي
                      sliver: SliverList(
                        delegate: SliverChildListDelegate([
                          // محدد الفترة الزمنية
                          _buildPeriodSelector(),
                          const SizedBox(height: AppDimensions.spacing24),

                          // بطاقات الملخص
                          _buildEnhancedSummaryCards(_dashboardPresenter),
                          const SizedBox(height: AppDimensions.spacing24),

                          // الوصول السريع
                          _buildEnhancedQuickAccess(),
                          const SizedBox(height: AppDimensions.spacing24),

                          // محدد نوع الرسم البياني
                          _buildEnhancedChartSelector(),
                          const SizedBox(height: AppDimensions.spacing16),

                          // الرسم البياني
                          _buildEnhancedChart(_dashboardPresenter),
                          const SizedBox(height: AppDimensions.spacing24),

                          // أكثر المنتجات مبيعاً
                          _buildEnhancedTopProducts(_dashboardPresenter),
                          const SizedBox(height: AppDimensions.spacing24),

                          // المنتجات منخفضة المخزون
                          _buildEnhancedLowStockProducts(_dashboardPresenter),
                          const SizedBox(height: AppDimensions.spacing24),

                          // آخر العمليات
                          _buildEnhancedRecentTransactions(_dashboardPresenter),
                          const SizedBox(
                              height: AppDimensions.spacing64 +
                                  AppDimensions
                                      .spacing16), // مساحة إضافية في الأسفل
                        ]),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: _buildEnhancedBottomNavigationBar(),
      floatingActionButton: AkFloatingButton(
        icon: Icons.add,
        onPressed: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: AppColors.transparent,
            builder: (context) => _buildQuickActionsSheet(context),
          );
        },
        tooltip: 'إضافة عنصر جديد',
        type: AkButtonType.primary,
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  /// بناء محدد الفترة الزمنية مع تصميم محسن
  /// يحتوي على أزرار للاختيار بين فترات مختلفة (يوم، أسبوع، شهر، سنة)
  Widget _buildPeriodSelector() {
    return AkCard(
      // تحديد الحشو الداخلي للبطاقة باستخدام النظام الموحد
      padding: AppDimensions.buttonPaddingMedium,
      // إزالة الهامش الخارجي لجعل البطاقة تصل إلى حدود الشاشة
      margin: EdgeInsets.zero,

      child: Column(
        // محاذاة العناصر إلى اليسار (بداية النص العربي)
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صف العنوان مع الأيقونة
          Row(
            children: [
              // أيقونة التقويم بلون أساسي ديناميكي يحافظ على اللون المخصص في جميع الأوضاع
              Icon(
                Icons.date_range,
                color: DynamicColors.primaryDynamic(context),
                size: AppDimensions.iconSizeMedium,
              ),
              // مساحة صغيرة بين الأيقونة والنص
              const SizedBox(width: AppDimensions.spacing8),
              // نص العنوان مع تنسيق مخصص
              Text(
                'الفترة الزمنية',
                style: AppTypography(
                  fontSize: AppTypography.fontSizeMedium,
                  fontWeight: FontWeight.bold,
                  color: DynamicColors.textPrimary(context),
                ),
              ),
            ],
          ),
          // مساحة بين العنوان والأزرار
          const SizedBox(height: AppDimensions.spacing16),
          // حاوية الأزرار مع خلفية ملونة وزوايا دائرية
          Container(
            decoration: BoxDecoration(
              // لون خلفية متغير حسب الثيم (فاتح/مظلم)
              color: DynamicColors.surfaceVariant(context),
              // زوايا دائرية متوسطة للمظهر العصري
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            ),
            // حشو داخلي صغير للحاوية
            padding: AppDimensions.cardPaddingTiny,
            child: Row(
              children: [
                // زر اليوم مع أيقونة التقويم اليومي
                _buildEnhancedPeriodButton('اليوم', 'day', Icons.today),
                // زر الأسبوع مع أيقونة عرض الأسبوع
                _buildEnhancedPeriodButton('الأسبوع', 'week', Icons.view_week),
                // زر الشهر مع أيقونة التقويم الشهري
                _buildEnhancedPeriodButton(
                    'الشهر', 'month', Icons.calendar_month),
                // زر السنة مع أيقونة التقويم السنوي
                _buildEnhancedPeriodButton(
                    'السنة', 'year', Icons.calendar_today),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر الفترة الزمنية المحسن مع تخطيط الأيقونة فوق النص
  /// يعرض الأيقونة في الأعلى والنص في الأسفل لتصميم أكثر جاذبية
  Widget _buildEnhancedPeriodButton(String label, String value, IconData icon) {
    // تحديد حالة الاختيار الحالية للزر
    final isSelected = _selectedPeriod == value;

    return Expanded(
      child: Padding(
        // إضافة مساحة أفقية صغيرة بين الأزرار
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.spacing2),
        child: Material(
          // جعل الخلفية شفافة للتأثيرات
          color: Colors.transparent,
          child: InkWell(
            // دالة الضغط لتغيير الفترة المختارة وإعادة تحميل البيانات
            onTap: () {
              setState(() {
                _selectedPeriod = value;
              });
              _loadData();
            },
            // تحديد شكل الزوايا للتأثير التفاعلي
            borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            child: Container(
              // إضافة حشو داخلي مناسب للزر
              padding: AppDimensions.getCardPadding('small'),
              decoration: BoxDecoration(
                // تحديد لون الخلفية حسب حالة الاختيار
                color: isSelected
                    ? DynamicColors.primaryDynamic(context)
                        .withValues(alpha: 0.1)
                    : Colors.transparent,
                // إضافة حدود للزر المختار
                border: isSelected
                    ? Border.all(
                        color: DynamicColors.primaryDynamic(context),
                        width: 1.5,
                      )
                    : null,
                // تحديد شكل الزوايا
                borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
              ),
              child: Column(
                // محاذاة العناصر في الوسط
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // الأيقونة في الأعلى
                  Icon(
                    icon,
                    // تحديد لون الأيقونة حسب حالة الاختيار
                    color: isSelected
                        ? DynamicColors.primaryDynamic(context)
                        : DynamicColors.textSecondary(context),
                    // حجم الأيقونة متوسط
                    size: AppDimensions.iconSizeMedium,
                  ),
                  // مساحة صغيرة بين الأيقونة والنص
                  const SizedBox(height: AppDimensions.spacing4),
                  // النص في الأسفل
                  Text(
                    label,
                    style: AppTypography(
                      fontSize: AppTypography.fontSizeSmall,
                      // تحديد لون النص حسب حالة الاختيار
                      color: isSelected
                          ? DynamicColors.primaryDynamic(context)
                          : DynamicColors.textSecondary(context),
                      // جعل النص عريض للزر المختار
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                    // محاذاة النص في الوسط
                    textAlign: TextAlign.center,
                    // منع النص من التمدد لأكثر من سطر واحد
                    maxLines: 1,
                    // إضافة نقاط في حالة تجاوز النص للمساحة المتاحة
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء بطاقات الملخص المحسنة مع الثيمات الديناميكية العصرية
  /// تعرض إحصائيات المبيعات والمشتريات والأرباح والفواتير
  Widget _buildEnhancedSummaryCards(DashboardPresenter presenter) {
    return Column(
      // محاذاة العناصر إلى اليسار (بداية النص العربي)
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع أيقونة ديناميكية وتصميم جذاب
        Padding(
          // إضافة مساحة في الأسفل فقط
          padding: const EdgeInsets.only(bottom: AppDimensions.spacing20),
          child: Row(
            children: [
              // حاوية الأيقونة مع خلفية ملونة
              Container(
                // حشو داخلي صغير للأيقونة
                padding: AppDimensions.cardPaddingTiny,
                decoration: BoxDecoration(
                  // خلفية بلون أساسي شفاف
                  color: DynamicColors.primaryDynamic(context)
                      .withValues(alpha: 0.1),
                  // زوايا دائرية صغيرة
                  borderRadius:
                      BorderRadius.circular(AppDimensions.radiusSmall),
                ),
                child: Icon(
                  // أيقونة التحليلات
                  Icons.analytics_outlined,
                  color: DynamicColors.primaryDynamic(context),
                  size: AppDimensions.iconSizeMedium,
                ),
              ),
              // مساحة بين الأيقونة والنص
              const SizedBox(width: AppDimensions.spacing12),
              // نص العنوان مع تنسيق مخصص
              Text(
                'ملخص الأداء',
                style: AppTypography(
                  fontSize: AppTypography.fontSizeLarge,
                  fontWeight: FontWeight.bold,
                  color: DynamicColors.textPrimary(context),
                ),
              ),
            ],
          ),
        ),

        // الصف الأول - بطاقات المبيعات والمشتريات
        Row(
          children: [
            // بطاقة المبيعات - تعرض إجمالي المبيعات مع إمكانية الانتقال لصفحة المبيعات
            Expanded(
              child: AkStatsCard(
                title: 'المبيعات',
                // تنسيق القيمة مع رقمين عشريين وإضافة الريال السعودي
                value: '${presenter.totalSales.toStringAsFixed(2)} ر.س',
                subtitle: 'إجمالي المبيعات',
                icon: Icons.shopping_cart_outlined,
                // لون مخصص للمبيعات من نظام الألوان الموحد
                color: AppColors.moduleSales,
                // الانتقال إلى صفحة المبيعات عند الضغط
                onTap: () => Navigator.pushNamed(context, '/sales'),
              ),
            ),
            // مساحة بين البطاقتين
            const SizedBox(width: AppDimensions.spacing16),
            // بطاقة المشتريات - تعرض إجمالي المشتريات مع إمكانية الانتقال لصفحة المشتريات
            Expanded(
              child: AkStatsCard(
                title: 'المشتريات',
                // تنسيق القيمة مع رقمين عشريين وإضافة الريال السعودي
                value: '${presenter.totalPurchases.toStringAsFixed(2)} ر.س',
                subtitle: 'إجمالي المشتريات',
                icon: Icons.shopping_bag_outlined,
                // لون مخصص للمشتريات من نظام الألوان الموحد
                color: AppColors.modulePurchases,
                // الانتقال إلى صفحة المشتريات عند الضغط
                onTap: () => Navigator.pushNamed(context, '/purchases'),
              ),
            ),
          ],
        ),

        // مساحة بين الصفين
        const SizedBox(height: AppDimensions.spacing16),

        // الصف الثاني - بطاقات الأرباح والفواتير
        Row(
          children: [
            // بطاقة الأرباح - تعرض صافي الأرباح مع تغيير اللون حسب الربح/الخسارة
            Expanded(
              child: AkStatsCard(
                title: 'الأرباح',
                // تنسيق القيمة مع رقمين عشريين وإضافة الريال السعودي
                value: '${presenter.totalProfit.toStringAsFixed(2)} ر.س',
                subtitle: 'صافي الأرباح',
                icon: Icons.trending_up_outlined,
                // تغيير اللون حسب حالة الربح (أخضر للربح، أحمر للخسارة)
                color: presenter.totalProfit >= 0
                    ? AppColors.success
                    : AppColors.error,
                // الانتقال إلى صفحة التقارير المالية عند الضغط
                onTap: () => Navigator.pushNamed(context, '/financial-reports'),
              ),
            ),
            // مساحة بين البطاقتين
            const SizedBox(width: AppDimensions.spacing16),
            // بطاقة الفواتير - تعرض عدد الفواتير الإجمالي
            Expanded(
              child: AkStatsCard(
                title: 'الفواتير',
                // عرض عدد الفواتير كرقم صحيح
                value: presenter.invoiceCount.toString(),
                subtitle: 'عدد الفواتير',
                icon: Icons.receipt_long_outlined,
                // لون مخصص للحسابات من نظام الألوان الموحد
                color: AppColors.moduleAccounts,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء قسم الوصول السريع المحسن مع تصميم عصري
  Widget _buildEnhancedQuickAccess() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع أيقونة ديناميكية
        Padding(
          padding: const EdgeInsets.only(bottom: AppDimensions.spacing20),
          child: Row(
            children: [
              Container(
                padding: AppDimensions.cardPaddingTiny,
                decoration: BoxDecoration(
                  color: DynamicColors.primaryDynamic(context)
                      .withValues(alpha: 0.1),
                  borderRadius:
                      BorderRadius.circular(AppDimensions.radiusSmall),
                ),
                child: Icon(
                  Icons.flash_on_outlined,
                  color: DynamicColors.primaryDynamic(context),
                  size: AppDimensions.iconSizeMedium,
                ),
              ),
              const SizedBox(width: AppDimensions.spacing12),
              Text(
                'الوصول السريع',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: DynamicColors.textPrimary(context),
                ),
              ),
            ],
          ),
        ),

        // بطاقة الوصول السريع مع تصميم محسن
        AkCard(
          padding: AppDimensions.cardPaddingLarge,
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildQuickAccessButton(
                      title: 'بيع جديد',
                      icon: Icons.shopping_cart_outlined,
                      color: AppColors.moduleSales,
                      onTap: () => Navigator.pushNamed(context, '/sale-form'),
                      isDarkMode: isDarkMode,
                    ),
                  ),
                  SizedBox(width: AppDimensions.defaultSpacing),
                  Expanded(
                    child: _buildQuickAccessButton(
                      title: 'إضافة منتج',
                      icon: Icons.inventory_2_outlined,
                      color: AppColors.moduleProducts,
                      onTap: () =>
                          Navigator.pushNamed(context, '/product-form'),
                      isDarkMode: isDarkMode,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing16),
              Row(
                children: [
                  Expanded(
                    child: _buildQuickAccessButton(
                      title: 'إضافة عميل',
                      icon: Icons.person_add_outlined,
                      color: AppColors.moduleUsers,
                      onTap: () =>
                          Navigator.pushNamed(context, '/customer-form'),
                      isDarkMode: isDarkMode,
                    ),
                  ),
                  const SizedBox(width: AppDimensions.spacing16),
                  Expanded(
                    child: _buildQuickAccessButton(
                      title: 'المخزون',
                      icon: Icons.warehouse_outlined,
                      color: AppColors.moduleInventory,
                      onTap: () => Navigator.pushNamed(context, '/products'),
                      isDarkMode: isDarkMode,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء زر الوصول السريع مع تصميم عصري وأيقونة فوق النص
  /// يستخدم لعرض الإجراءات السريعة مثل (بيع جديد، إضافة منتج، إلخ)
  Widget _buildQuickAccessButton({
    required String title, // عنوان الزر
    required IconData icon, // أيقونة الزر
    required Color color, // لون الزر والأيقونة
    required VoidCallback onTap, // دالة تنفذ عند الضغط
    required bool isDarkMode, // حالة الوضع المظلم
  }) {
    return AkCard(
      // تحديد دالة الضغط للبطاقة
      onTap: onTap,
      // حشو داخلي متوسط للبطاقة
      padding: AppDimensions.cardPaddingSmall,
      // إزالة الهامش الخارجي
      margin: EdgeInsets.zero,
      child: Column(
        // محاذاة العناصر في الوسط عمودياً
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // حاوية الأيقونة مع خلفية دائرية ملونة
          Container(
            // حشو داخلي صغير للأيقونة
            padding: AppDimensions.cardPaddingMedium,
            decoration: BoxDecoration(
              // لون الخلفية مع شفافية مختلفة حسب الوضع (مظلم/فاتح)
              color: color.withValues(alpha: isDarkMode ? 0.2 : 0.1),
              // شكل دائري للحاوية
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              // لون الأيقونة نفس لون الزر
              color: color,
              // حجم أيقونة كبير للوضوح
              size: AppDimensions.iconSizeMedium,
            ),
          ),
          // مساحة بين الأيقونة والنص
          const SizedBox(height: AppDimensions.spacing8),
          // نص العنوان أسفل الأيقونة
          Text(
            title,
            style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
              // تغيير لون النص حسب الوضع (مظلم/فاتح)
              color: isDarkMode
                  ? AppColors.darkTextPrimary
                  : AppColors.lightTextPrimary,
              // وزن خط متوسط للوضوح
              fontWeight: FontWeight.w600,
            ),
            // محاذاة النص في الوسط
            textAlign: TextAlign.center,
            // منع النص من التمدد لأكثر من سطر واحد
            maxLines: 1,
            // إضافة نقاط في حالة تجاوز النص للمساحة المتاحة
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// بناء شاشة الإجراءات السريعة
  Widget _buildQuickActionsSheet(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.radiusXLarge),
          topRight: Radius.circular(AppDimensions.radiusXLarge),
        ),
      ),
      padding: AppDimensions.cardPaddingTiny,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: AppDimensions.floatingButtonSpace,
            height: AppDimensions.progressBarHeight,
            decoration: BoxDecoration(
              color: DynamicColors.textSecondary(context),
              borderRadius: BorderRadius.circular(AppDimensions.radiusTiny),
            ),
          ),
          const SizedBox(height: AppDimensions.spacing24),
          Text(
            'إجراءات سريعة',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.spacing24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAction(
                'فاتورة بيع',
                Icons.receipt,
                AppColors.info,
                () => Navigator.pushNamed(context, '/sale-form'),
              ),
              _buildQuickAction(
                'فاتورة شراء',
                Icons.shopping_bag,
                AppColors.success,
                () => Navigator.pushNamed(context, '/purchase-form'),
              ),
              _buildQuickAction(
                'إضافة منتج',
                Icons.inventory,
                // استخدام اللون الثالثي الديناميكي من الثيم الحالي
                Theme.of(context).colorScheme.tertiary,
                () => Navigator.pushNamed(context, '/product-form'),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAction(
                'إضافة عميل',
                Icons.person_add,
                AppColors.warning,
                () => Navigator.pushNamed(context, '/customer-form'),
              ),
              _buildQuickAction(
                'إضافة مورد',
                Icons.business,
                AppColors.teal,
                () => Navigator.pushNamed(context, '/supplier-form'),
              ),
              _buildQuickAction(
                'مصروفات',
                Icons.money_off,
                AppColors.error,
                () => Navigator.pushNamed(context, AppRoutes.expenses),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing24),
        ],
      ),
    );
  }

  /// بناء إجراء سريع
  Widget _buildQuickAction(
      String title, IconData icon, Color color, VoidCallback onTap) {
    return AkIconButton(
      icon: icon,
      tooltip: title,
      onPressed: onTap,
      customBackgroundColor: color.withValues(alpha: 0.1),
      customIconColor: color,
      size: AkButtonSize.small,
    );
  }

  /// بناء شريط التنقل السفلي المحسن
  Widget _buildEnhancedBottomNavigationBar() {
    return BottomAppBar(
      shape: const CircularNotchedRectangle(),
      notchMargin: AppDimensions.bottomNavNotchMargin,
      elevation: AppDimensions.elevationHigh,
      child: Container(
        height: AppDimensions.bottomNavBarHeight,
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.spacing8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildBottomNavItem(
              'الرئيسية',
              Icons.dashboard,
              0,
            ),
            _buildBottomNavItem(
              'المبيعات',
              Icons.shopping_cart,
              1,
            ),
            const SizedBox(
                width: AppDimensions.floatingButtonSpace), // مساحة للزر العائم
            _buildBottomNavItem(
              'المخزون',
              Icons.inventory,
              2,
            ),
            _buildBottomNavItem(
              'التقارير',
              Icons.bar_chart,
              3,
            ),
          ],
        ),
      ),
    );
  }

///////////////////////////////////////////////////////////
  /// بناء قسم أكثر المنتجات مبيعاً المحسن
  Widget _buildEnhancedTopProducts(DashboardPresenter presenter) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final topProducts = presenter.topProducts;

    return AkCard(
      padding: AppDimensions.cardPaddingLarge,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.trending_up,
                // استخدام اللون الأساسي الديناميكي الذي يحافظ على اللون المخصص في جميع الأوضاع
                color: DynamicColors.primaryDynamic(context),
                size: AppDimensions.iconSizeMedium,
              ),
              const SizedBox(width: AppDimensions.spacing8),
              Text(
                'أكثر المنتجات مبيعاً',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.getAdaptiveTextColor(isDark),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),
          topProducts.isEmpty
              ? const Center(
                  child: Padding(
                    padding: AppDimensions.cardPaddingXLarge,
                    child: Column(
                      children: [
                        Icon(
                          Icons.inventory_2,
                          size: AppDimensions.iconContainerSize,
                          color: AppColors.lightTextSecondary,
                        ),
                        SizedBox(height: AppDimensions.spacing16),
                        Text(
                          'لا توجد بيانات للمنتجات الأكثر مبيعاً',
                          style: AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: math.min(5, topProducts.length),
                  separatorBuilder: (context, index) => const Divider(),
                  itemBuilder: (context, index) {
                    final product = topProducts[index];
                    return AkCard(
                      onTap: () {
                        // الانتقال إلى صفحة تفاصيل المنتج
                        if (product['id'] != null) {
                          Navigator.pushNamed(
                            context,
                            '/product-details',
                            arguments: product['id'],
                          );
                        }
                      },
                      child: ListTile(
                        leading: Container(
                          width: AppDimensions.iconContainerSize,
                          height: AppDimensions.iconContainerSize,
                          decoration: BoxDecoration(
                            color: Theme.of(context)
                                .colorScheme
                                .tertiary
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(
                                AppDimensions.smallRadius),
                          ),
                          child: Center(
                            child: Text(
                              '${index + 1}',
                              style: AppTypography(
                                color: AppColors.lightTextSecondary,
                                fontWeight: FontWeight.bold,
                                fontSize: AppDimensions.largeFontSize,
                              ),
                            ),
                          ),
                        ),
                        title: Text(
                          product['name'] ?? '',
                          style: AppTypography(
                            fontWeight: FontWeight.bold,
                            color: AppColors.getAdaptiveTextColor(isDark),
                          ),
                        ),
                        subtitle: Text(
                          'الكمية المباعة: ${product['quantity']}',
                          style: AppTypography(
                            color:
                                AppColors.getAdaptiveSecondaryTextColor(isDark),
                            fontSize: AppDimensions.smallFontSize,
                          ),
                        ),
                        trailing: Text(
                          '${product['total']} ر.س',
                          style: const AppTypography(
                            fontWeight: FontWeight.bold,
                            color: AppColors.lightTextSecondary,
                          ),
                        ),
                      ),
                    );
                  },
                ),
          if (topProducts.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: AppDimensions.defaultSpacing),
              child: Center(
                child: AkTextButton(
                  text: 'عرض جميع المنتجات',
                  icon: Icons.arrow_forward,
                  onPressed: () {
                    Navigator.pushNamed(context, '/products');
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }

//////////////////////////////////////////////////
  /// بناء قسم المنتجات منخفضة المخزون المحسن
  Widget _buildEnhancedLowStockProducts(DashboardPresenter presenter) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final lowStockProducts = presenter.lowStockProducts;

    return AkCard(
      padding: AppDimensions.cardPaddingLarge,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.warning_amber,
                color: AppColors.warning,
                size: AppDimensions.iconSizeMedium,
              ),
              const SizedBox(width: AppDimensions.spacing8),
              Text(
                'منتجات منخفضة المخزون',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.getAdaptiveTextColor(isDark),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),
          lowStockProducts.isEmpty
              ? const Center(
                  child: Padding(
                    padding: AppDimensions.cardPaddingXLarge,
                    child: Column(
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: AppDimensions.iconContainerSize,
                          color: AppColors.lightTextSecondary,
                        ),
                        SizedBox(height: AppDimensions.spacing16),
                        Text(
                          'لا توجد منتجات منخفضة المخزون',
                          style: AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: math.min(5, lowStockProducts.length),
                  separatorBuilder: (context, index) => const Divider(),
                  itemBuilder: (context, index) {
                    final product = lowStockProducts[index];
                    final stockLevel = (product['quantity'] ?? 0) /
                        (product['reorder_level'] ?? 1);

                    return AkCard(
                      child: ListTile(
                        leading: Container(
                          width: AppDimensions.iconContainerSize,
                          height: AppDimensions.iconContainerSize,
                          decoration: BoxDecoration(
                            color: AppColors.warning.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(
                                AppDimensions.smallRadius),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.inventory,
                              color: AppColors.lightTextSecondary,
                            ),
                          ),
                        ),
                        title: Text(
                          product['name'] ?? '',
                          style: const AppTypography(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'المخزون الحالي: ${product['quantity']}',
                              style: AppTypography(
                                color: AppColors.lightTextSecondary,
                                fontSize: AppDimensions.smallFontSize,
                              ),
                            ),
                            const SizedBox(height: AppDimensions.spacing4),
                            LinearProgressIndicator(
                              value: stockLevel.clamp(0.0, 1.0),
                              backgroundColor: AppColors.lightSurfaceVariant,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                stockLevel < 0.3
                                    ? AppColors.error
                                    : stockLevel < 0.7
                                        ? AppColors.warning
                                        : AppColors.success,
                              ),
                            ),
                          ],
                        ),
                        trailing: Text(
                          'الحد الأدنى: ${product['reorder_level']}',
                          style: const AppTypography(
                            fontWeight: FontWeight.bold,
                            color: AppColors.lightTextSecondary,
                          ),
                        ),
                        onTap: () {
                          // الانتقال إلى صفحة تفاصيل المنتج
                          if (product['id'] != null) {
                            Navigator.pushNamed(
                              context,
                              '/product-details',
                              arguments: product['id'],
                            );
                          }
                        },
                      ),
                    );
                  },
                ),
          if (lowStockProducts.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: AppDimensions.defaultSpacing),
              child: Center(
                child: AkTextButton(
                  text: 'إنشاء طلب شراء',
                  icon: Icons.add_shopping_cart,
                  onPressed: () {
                    Navigator.pushNamed(context, '/purchase-form');
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء قسم آخر العمليات المحسن
  Widget _buildEnhancedRecentTransactions(DashboardPresenter presenter) {
    final theme = Theme.of(context);
    final recentTransactions = presenter.recentTransactions;

    return AkCard(
      padding: AppDimensions.cardPaddingLarge,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.receipt_long,
                // استخدام اللون الأساسي الديناميكي الذي يحافظ على اللون المخصص في جميع الأوضاع
                color: DynamicColors.primaryDynamic(context),
                size: AppDimensions.iconSizeMedium,
              ),
              const SizedBox(width: AppDimensions.spacing8),
              Text(
                'آخر العمليات',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),
          recentTransactions.isEmpty
              ? const Center(
                  child: Padding(
                    padding: AppDimensions.cardPaddingXLarge,
                    child: Column(
                      children: [
                        Icon(
                          Icons.receipt,
                          size: AppDimensions.iconContainerSize,
                          color: AppColors.lightTextSecondary,
                        ),
                        SizedBox(height: AppDimensions.spacing16),
                        Text(
                          'لا توجد عمليات حديثة',
                          style: AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: math.min(5, recentTransactions.length),
                  separatorBuilder: (context, index) => const Divider(),
                  itemBuilder: (context, index) {
                    final transaction = recentTransactions[index];
                    final isInvoice = transaction['type'] == 'invoice';
                    final isPurchase = transaction['type'] == 'purchase';
                    final isExpense = transaction['type'] == 'expense';

                    IconData icon;
                    Color color;
                    String title;

                    if (isInvoice) {
                      icon = Icons.receipt;
                      color = AppColors.info;
                      title = 'فاتورة بيع';
                    } else if (isPurchase) {
                      icon = Icons.shopping_bag;
                      color = AppColors.success;
                      title = 'فاتورة شراء';
                    } else if (isExpense) {
                      icon = Icons.money_off;
                      color = AppColors.error;
                      title = 'مصروفات';
                    } else {
                      icon = Icons.swap_horiz;
                      // استخدام اللون الثالثي الديناميكي للعمليات الأخرى
                      color = Theme.of(context).colorScheme.tertiary;
                      title = 'عملية أخرى';
                    }

                    return ListTile(
                      leading: Container(
                        width: AppDimensions.iconContainerSize,
                        height: AppDimensions.iconContainerSize,
                        decoration: BoxDecoration(
                          color: color.withValues(alpha: 0.1),
                          borderRadius:
                              BorderRadius.circular(AppDimensions.radiusSmall),
                        ),
                        child: Center(
                          child: Icon(
                            icon,
                            color: color,
                            size: AppDimensions.iconSizeMedium,
                          ),
                        ),
                      ),
                      title: Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: const AppTypography(
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: AppDimensions.spacing8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppDimensions.spacing8,
                              vertical: AppDimensions.spacing2,
                            ),
                            decoration: BoxDecoration(
                              color: color.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(
                                  AppDimensions.radiusMedium),
                            ),
                            child: Text(
                              '#${transaction['number'] ?? ''}',
                              style: AppTypography(
                                color: color,
                                fontSize: AppDimensions.smallFontSize,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            transaction['date'] ?? '',
                            style: AppTypography(
                              color: AppColors.lightTextSecondary,
                              fontSize: AppDimensions.smallFontSize,
                            ),
                          ),
                          if (transaction['customer_name'] != null)
                            Text(
                              'العميل: ${transaction['customer_name']}',
                              style: AppTypography(
                                color: AppColors.lightTextSecondary,
                                fontSize: AppDimensions.smallFontSize,
                              ),
                            ),
                          if (transaction['supplier_name'] != null)
                            Text(
                              'المورد: ${transaction['supplier_name']}',
                              style: AppTypography(
                                color: AppColors.lightTextSecondary,
                                fontSize: AppDimensions.smallFontSize,
                              ),
                            ),
                        ],
                      ),
                      trailing: Text(
                        '${transaction['total']} ر.س',
                        style: AppTypography(
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      onTap: () {
                        // الانتقال إلى صفحة تفاصيل العملية
                        if (transaction['id'] != null) {
                          if (isInvoice) {
                            Navigator.pushNamed(
                              context,
                              '/invoice-details',
                              arguments: transaction['id'],
                            );
                          } else if (isPurchase) {
                            Navigator.pushNamed(
                              context,
                              '/purchase-details',
                              arguments: transaction['id'],
                            );
                          } else if (isExpense) {
                            Navigator.pushNamed(
                              context,
                              '/expense-details',
                              arguments: transaction['id'],
                            );
                          }
                        }
                      },
                    );
                  },
                ),
          if (recentTransactions.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: AppDimensions.spacing16),
              child: Center(
                child: AkTextButton(
                  text: 'عرض جميع العمليات',
                  icon: Icons.arrow_forward,
                  onPressed: () {
                    Navigator.pushNamed(context, '/transactions');
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء عنصر في شريط التنقل السفلي مع ألوان ديناميكية
  /// يتكيف مع تغيير لون التطبيق الأساسي والوضع المظلم/الفاتح
  Widget _buildBottomNavItem(String label, IconData icon, int index) {
    // تحديد حالة الاختيار الحالية
    final isSelected = _currentIndex == index;
    // استخدام اللون الأساسي الديناميكي الذي يحافظ على اللون المخصص في جميع الأوضاع
    final primaryColor = DynamicColors.primaryDynamic(context);

    return InkWell(
      onTap: () {
        setState(() {
          _currentIndex = index;
        });

        // التنقل إلى الشاشات المختلفة بناءً على المؤشر
        switch (index) {
          case 1: // المبيعات
            Navigator.pushNamed(context, AppRoutes.sales);
            break;
          case 2: // المخزون
            Navigator.pushNamed(context, AppRoutes.products);
            break;
          case 3: // التقارير
            Navigator.pushNamed(context, AppRoutes.reports);
            break;
        }
      },
      borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
      child: Container(
        padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.spacing8,
            vertical: AppDimensions.spacing4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الشريط السفلي مع لون ديناميكي
            Icon(
              icon,
              // استخدام اللون الأساسي الديناميكي للعنصر المختار
              color: isSelected
                  ? primaryColor
                  : DynamicColors.textSecondary(context),
              size: AppDimensions.largeFontSize,
            ),
            const SizedBox(height: AppDimensions.spacing2),
            // نص الشريط السفلي مع لون ديناميكي
            Text(
              label,
              style: AppTypography(
                // استخدام اللون الأساسي الديناميكي للعنصر المختار
                color: isSelected
                    ? primaryColor
                    : DynamicColors.textSecondary(context),
                fontSize: AppDimensions.tinyFontSize,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                height: 0.9,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء محدد نوع الرسم البياني المحسن
  Widget _buildEnhancedChartSelector() {
    final theme = Theme.of(context);
    // final primaryColor = theme.primaryColor;
    // final isDark = theme.brightness == Brightness.dark;

    return AkCard(
      padding: AppDimensions.cardPaddingSmall,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.bar_chart,
                // استخدام اللون الأساسي الديناميكي الذي يحافظ على اللون المخصص في جميع الأوضاع
                color: DynamicColors.primaryDynamic(context),
                size: AppDimensions.iconSizeMedium,
              ),
              const SizedBox(width: AppDimensions.spacing8),
              Text(
                'نوع الرسم البياني',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing12),
          Container(
            decoration: BoxDecoration(
              color: AppColors.lightTextSecondary.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            ),
            padding: AppDimensions.cardPaddingTiny,
            child: Row(
              children: [
                _buildEnhancedChartTypeButton(
                    'المبيعات', 'sales', Icons.shopping_cart),
                _buildEnhancedChartTypeButton(
                    'المشتريات', 'purchases', Icons.shopping_bag),
                _buildEnhancedChartTypeButton(
                    'الأرباح', 'profit', Icons.trending_up),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر نوع الرسم البياني المحسن
  Widget _buildEnhancedChartTypeButton(
      String label, String value, IconData icon) {
    final isSelected = _selectedChartType == value;
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedChartType = value;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(
              vertical: AppDimensions.spacing12,
              horizontal: AppDimensions.spacing4),
          decoration: BoxDecoration(
            color: isSelected ? primaryColor : AppColors.transparent,
            borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected
                    ? AppColors.onPrimary
                    : DynamicColors.textSecondary(context),
                size: AppDimensions.smallIconSize,
              ),
              const SizedBox(width: AppDimensions.spacing4),
              Text(
                label,
                style: AppTypography(
                  color: isSelected
                      ? AppColors.onPrimary
                      : AppColors.lightSurfaceVariant,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: AppDimensions.smallFontSize,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء الرسم البياني المحسن
  Widget _buildEnhancedChart(DashboardPresenter presenter) {
    final chartData = _getChartData(presenter);
    final xLabels = _getChartLabels(presenter);
    final theme = Theme.of(context);
    //final isDark = theme.brightness == Brightness.dark;

    return AkCard(
      padding: AppDimensions.cardPaddingLarge,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // استخدام LayoutBuilder لتجنب overflow في عنوان الرسم البياني
          LayoutBuilder(
            builder: (context, constraints) {
              // إذا كان العرض أقل من 350 بكسل، استخدم Column
              if (constraints.maxWidth < 350) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // صف العنوان والأيقونة
                    Row(
                      children: [
                        Icon(
                          _getChartIcon(),
                          color: _getChartColor(),
                          size: AppDimensions.iconSizeMedium,
                        ),
                        const SizedBox(width: AppDimensions.spacing8),
                        Expanded(
                          child: Text(
                            _getChartTitle(),
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.spacing8),
                    // حاوية الفترة الزمنية
                    Container(
                      padding: AppDimensions.getResponsivePadding(
                          horizontal: 3, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getChartColor().withValues(alpha: 0.1),
                        borderRadius:
                            BorderRadius.circular(AppDimensions.radiusLarge),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.calendar_today,
                            size: AppDimensions.smallIconSize,
                            color: _getChartColor(),
                          ),
                          const SizedBox(width: AppDimensions.spacing4),
                          Text(
                            _getPeriodText(),
                            style: AppTypography(
                              color: _getChartColor(),
                              fontWeight: FontWeight.bold,
                              fontSize: AppDimensions.smallFontSize,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              }

              // للشاشات الأكبر، استخدم Row مع Flexible
              return Row(
                children: [
                  // الجزء الأيسر - العنوان والأيقونة
                  Flexible(
                    flex: 2,
                    child: Row(
                      children: [
                        Icon(
                          _getChartIcon(),
                          color: _getChartColor(),
                          size: AppDimensions.iconSizeMedium,
                        ),
                        const SizedBox(width: AppDimensions.spacing8),
                        Expanded(
                          child: Text(
                            _getChartTitle(),
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: AppDimensions.spacing8),
                  // الجزء الأيمن - الفترة الزمنية
                  Flexible(
                    flex: 1,
                    child: Container(
                      padding: AppDimensions.getResponsivePadding(
                          horizontal: 3, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getChartColor().withValues(alpha: 0.1),
                        borderRadius:
                            BorderRadius.circular(AppDimensions.radiusLarge),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.calendar_today,
                            size: AppDimensions.smallIconSize,
                            color: _getChartColor(),
                          ),
                          const SizedBox(width: AppDimensions.spacing4),
                          Flexible(
                            child: Text(
                              _getPeriodText(),
                              style: AppTypography(
                                color: _getChartColor(),
                                fontWeight: FontWeight.bold,
                                fontSize: AppDimensions.smallFontSize,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: AppDimensions.spacing20),
          chartData.isEmpty
              ? SizedBox(
                  height: AppDimensions.chartHeight,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.bar_chart,
                          size: AppDimensions.emptyStateIconSize,
                          color: AppColors.lightTextSecondary,
                        ),
                        const SizedBox(height: AppDimensions.spacing16),
                        const Text(
                          'لا توجد بيانات للعرض',
                          style: AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: AppDimensions.spacing8),
                        Text(
                          'جرب تغيير الفترة الزمنية أو نوع الرسم البياني',
                          style: AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontSize: AppDimensions.mediumFontSize,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : AnimatedBarChart(
                  data: chartData,
                  xLabels: xLabels,
                  color: _getChartColor(),
                  height: AppDimensions.chartHeight,
                  barWidth: AppDimensions.chartBarWidth,
                  topRadius: AppDimensions.chartTopRadius,
                  valueFormatter: (value) => '${value.toStringAsFixed(2)} ر.س',
                ),
          if (chartData.isNotEmpty) ...[
            const SizedBox(height: AppDimensions.spacing16),
            // استخدام LayoutBuilder للتحكم في التخطيط حسب المساحة المتاحة
            LayoutBuilder(
              builder: (context, constraints) {
                // إذا كان العرض أقل من 400 بكسل، استخدم Column بدلاً من Row
                if (constraints.maxWidth < 400) {
                  return Column(
                    children: [
                      _buildChartStat(
                        'المتوسط',
                        _calculateAverage(chartData),
                        Icons.calculate,
                        AppColors.info,
                      ),
                      const SizedBox(height: AppDimensions.spacing8),
                      _buildChartStat(
                        'الإجمالي',
                        _calculateTotal(chartData),
                        Icons.summarize,
                        AppColors.success,
                      ),
                      const SizedBox(height: AppDimensions.spacing8),
                      _buildChartStat(
                        'الأعلى',
                        _calculateMax(chartData),
                        Icons.trending_up,
                        AppColors.accent,
                      ),
                    ],
                  );
                }

                // للشاشات الأكبر، استخدم Row مع Flexible لمنع overflow
                return Row(
                  children: [
                    Flexible(
                      child: _buildChartStat(
                        'المتوسط',
                        _calculateAverage(chartData),
                        Icons.calculate,
                        AppColors.info,
                      ),
                    ),
                    SizedBox(
                        width: AppDimensions.getResponsivePadding(horizontal: 1)
                                .horizontal /
                            2),
                    Flexible(
                      child: _buildChartStat(
                        'الإجمالي',
                        _calculateTotal(chartData),
                        Icons.summarize,
                        AppColors.success,
                      ),
                    ),
                    SizedBox(
                        width: AppDimensions.getResponsivePadding(horizontal: 1)
                                .horizontal /
                            2),
                    Flexible(
                      child: _buildChartStat(
                        'الأعلى',
                        _calculateMax(chartData),
                        Icons.trending_up,
                        AppColors.accent,
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  /// بناء إحصائية للرسم البياني مع تصميم متجاوب
  Widget _buildChartStat(
      String label, double value, IconData icon, Color color) {
    return Container(
      padding: AppDimensions.getResponsivePadding(horizontal: 2, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // تقليل الحجم للحد الأدنى
        children: [
          Icon(icon, color: color, size: AppDimensions.smallIconSize),
          SizedBox(
              height:
                  AppDimensions.getResponsivePadding(vertical: 1).vertical / 2),
          Text(
            label,
            style: AppTypography(
              color: color,
              fontSize: AppDimensions.tinyFontSize,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis, // منع تجاوز النص
          ),
          Text(
            '${value.toStringAsFixed(2)} ر.س',
            style: AppTypography(
              color: color,
              fontSize: AppDimensions.smallFontSize,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis, // منع تجاوز النص
          ),
        ],
      ),
    );
  }

  /// حساب متوسط البيانات
  double _calculateAverage(List<double> data) {
    if (data.isEmpty) return 0;
    final sum = data.reduce((a, b) => a + b);
    return sum / data.length;
  }

  /// حساب إجمالي البيانات
  double _calculateTotal(List<double> data) {
    if (data.isEmpty) return 0;
    return data.reduce((a, b) => a + b);
  }

  /// حساب أقصى قيمة
  double _calculateMax(List<double> data) {
    if (data.isEmpty) return 0;
    return data.reduce((a, b) => a > b ? a : b);
  }

  /// الحصول على نص الفترة الزمنية
  String _getPeriodText() {
    switch (_selectedPeriod) {
      case 'day':
        return 'اليوم';
      case 'week':
        return 'الأسبوع';
      case 'month':
        return 'الشهر';
      case 'year':
        return 'السنة';
      default:
        return 'الشهر';
    }
  }

  /// الحصول على أيقونة الرسم البياني
  IconData _getChartIcon() {
    switch (_selectedChartType) {
      case 'sales':
        return Icons.shopping_cart;
      case 'purchases':
        return Icons.shopping_bag;
      case 'profit':
        return Icons.trending_up;
      default:
        return Icons.bar_chart;
    }
  }

  /// الحصول على عناوين المحور الأفقي
  List<String> _getChartLabels(DashboardPresenter presenter) {
    final count = presenter.salesData.length;
    final labels = <String>[];

    for (int i = 0; i < count; i++) {
      labels.add(_getChartLabel(i));
    }

    return labels;
  }

  List<double> _getChartData(DashboardPresenter presenter) {
    switch (_selectedChartType) {
      case 'sales':
        return presenter.salesData;
      case 'purchases':
        return presenter.purchasesData;
      case 'profit':
        return presenter.profitData;
      default:
        return presenter.salesData;
    }
  }

  String _getChartTitle() {
    switch (_selectedChartType) {
      case 'sales':
        return 'المبيعات';
      case 'purchases':
        return 'المشتريات';
      case 'profit':
        return 'الأرباح';
      default:
        return 'المبيعات';
    }
  }

  Color _getChartColor() {
    switch (_selectedChartType) {
      case 'sales':
        return AppColors.info;
      case 'purchases':
        return AppColors.success;
      case 'profit':
        return AppColors.accent;
      default:
        return AppColors.info;
    }
  }

  String _getChartLabel(int index) {
    switch (_selectedPeriod) {
      case 'day':
        return '${index * 2}:00';
      case 'week':
        final days = [
          'الأحد',
          'الإثنين',
          'الثلاثاء',
          'الأربعاء',
          'الخميس',
          'الجمعة',
          'السبت'
        ];
        return days[index % 7];
      case 'month':
        return '${index + 1}';
      case 'year':
        final months = [
          'يناير',
          'فبراير',
          'مارس',
          'أبريل',
          'مايو',
          'يونيو',
          'يوليو',
          'أغسطس',
          'سبتمبر',
          'أكتوبر',
          'نوفمبر',
          'ديسمبر'
        ];
        return months[index % 12];
      default:
        return index.toString();
    }
  }

  // ignore: unused_element
  double _getMaxY(List<double> data) {
    if (data.isEmpty) return 0;
    return data.reduce((value, element) => value > element ? value : element);
  }

  // ignore: unused_element
  Widget _buildTopProducts(DashboardPresenter presenter) {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أكثر المنتجات مبيعاً',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: AppDimensions.spacing16),
          presenter.topProducts.isEmpty
              ? const Center(
                  child: Padding(
                    padding: AppDimensions.cardPaddingMedium,
                    child: Text('لا توجد بيانات للعرض'),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: presenter.topProducts.length,
                  itemBuilder: (context, index) {
                    final product = presenter.topProducts[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: AppColors.infoLight,
                        child: Text(
                          '${index + 1}',
                          style: const AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(product['name'] ?? ''),
                      subtitle: Text('الكمية: ${product['quantity']}'),
                      trailing: Text(
                        '${product['total'].toStringAsFixed(2)} ر.س',
                        style: const AppTypography(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    );
                  },
                ),
        ],
      ),
    );
  }

  // ignore: unused_element
  Widget _buildLowStockProducts(DashboardPresenter presenter) {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المنتجات منخفضة المخزون',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
            ///////////////////////
          ),
          const SizedBox(height: AppDimensions.spacing16),
          presenter.lowStockProducts.isEmpty
              ? const Center(
                  child: Padding(
                    padding: AppDimensions.cardPaddingMedium,
                    child: Text('لا توجد منتجات منخفضة المخزون'),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: presenter.lowStockProducts.length,
                  itemBuilder: (context, index) {
                    final product = presenter.lowStockProducts[index];
                    return ListTile(
                      leading: const CircleAvatar(
                        backgroundColor: AppColors.error,
                        child: Icon(
                          Icons.warning,
                          color: AppColors.lightTextSecondary,
                        ),
                        /////////////////////
                      ),
                      title: Text(product['name'] ?? ''),
                      subtitle: Text(
                        'المخزون الحالي: ${product['quantity']} | الحد الأدنى: ${product['minStock']}',
                      ),
                      trailing: AkButton(
                        text: 'تعديل',
                        size: AkButtonSize.small,
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            '/product-form',
                            arguments: product['id'],
                          );
                        },
                      ),
                    );
                  },
                ),
        ],
      ),
    );
  }

  /// الحصول على لون العملية بناءً على نوعها
  // ignore: unused_element
  Color _getTransactionColor(String? type) {
    switch (type) {
      case 'sale':
        return AppColors.success;
      case 'purchase':
        return AppColors.info;
      case 'expense':
        return AppColors.error;
      case 'income':
        return AppColors.accent;
      default:
        return AppColors.secondary;
    }
  }

  /// الحصول على أيقونة العملية بناءً على نوعها
  // ignore: unused_element
  IconData _getTransactionIcon(String? type) {
    switch (type) {
      case 'sale':
        return Icons.shopping_cart;
      case 'purchase':
        return Icons.shopping_bag;
      case 'expense':
        return Icons.money_off;
      case 'income':
        return Icons.attach_money;
      default:
        return Icons.receipt;
    }
  }

  /// بناء شريط التنقل السفلي
  // ignore: unused_element
  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });

        // التنقل إلى الشاشات المختلفة بناءً على المؤشر
        switch (index) {
          case 1: // المبيعات
            Navigator.pushNamed(context, AppRoutes.sales);
            break;
          case 2: // المخزون
            Navigator.pushNamed(context, AppRoutes.products);
            break;
          case 3: // التقارير
            Navigator.pushNamed(context, AppRoutes.financialReports);
            break;
        }
      },
      type: BottomNavigationBarType.fixed,
      backgroundColor:
          Theme.of(context).bottomNavigationBarTheme.backgroundColor ??
              AppColors.lightSurface,
      selectedItemColor: DynamicColors.primaryDynamic(context),
      unselectedItemColor: Theme.of(context).unselectedWidgetColor,
      selectedLabelStyle: const AppTypography(fontWeight: FontWeight.bold),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.shopping_cart),
          label: 'المبيعات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.inventory),
          label: 'المخزون',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.bar_chart),
          label: 'التقارير',
        ),
      ],
    );
  }
}
