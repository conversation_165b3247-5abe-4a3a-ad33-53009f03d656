import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/utils/index.dart';

import '../../../core/widgets/index.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/theme/index.dart';
import '../../../core/auth/services/auth_service.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _agreeToTerms = false;

  // معرف المستخدم الافتراضي
  String? _defaultAdminId;
  final bool _isLoadingDefaultAdmin = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _handleRegister() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_agreeToTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يجب الموافقة على الشروط والأحكام'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      // سنقوم بالبحث عن المستخدم الافتراضي مباشرة عند الضغط على زر التسجيل
      try {
        // عرض مؤشر التحميل
        _showLoadingDialog();

        // البحث عن المستخدم الافتراضي
        final db = await DatabaseHelper().database;
        final List<Map<String, dynamic>> users = await db.rawQuery(
            'SELECT id FROM users WHERE is_deleted = 0 ORDER BY rowid ASC LIMIT 1');

        if (users.isEmpty) {
          // إغلاق مؤشر التحميل
          if (mounted) {
            Navigator.of(context).pop();

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('لم يتم العثور على المستخدم الافتراضي'),
                backgroundColor: AppColors.error,
              ),
            );
          }
          return;
        }

        // تعيين معرف المستخدم الافتراضي
        _defaultAdminId = users.first['id'] as String;
        AppLogger.info('تم العثور على المستخدم الافتراضي: $_defaultAdminId');
      } catch (e) {
        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في البحث عن المستخدم الافتراضي: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
        AppLogger.error('خطأ في البحث عن المستخدم الافتراضي: $e');
        return;
      }

      try {
        // تحديث المستخدم مباشرة في قاعدة البيانات
        final db = await DatabaseHelper().database;

        // تشفير كلمة المرور باستخدام الدالة الموحدة
        final String hashedPassword =
            AuthService.hashPassword(_passwordController.text);

        // تحديث بيانات المستخدم
        final int updatedRows = await db.update(
          'users',
          {
            'username': _usernameController.text,
            'password': hashedPassword,
            'full_name': _nameController.text,
            'email': _emailController.text,
            'phone': _phoneController.text,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [_defaultAdminId],
        );

        if (updatedRows == 0) {
          throw Exception('لم يتم تحديث أي صفوف. تأكد من وجود المستخدم.');
        }

        AppLogger.info(
            'تم تحديث بيانات المستخدم بنجاح: ${_usernameController.text}');

        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();
        }

        // عرض رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'تم تحديث بيانات الحساب بنجاح!',
                style: AppTypography(
                  color: Colors.white,
                ),
              ),
              backgroundColor: AppColors.success,
            ),
          );
        }

        // حفظ حالة إخفاء زر التسجيل في التخزين المحلي
        try {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('hasLoggedInBefore', true);
          AppLogger.info('✅ تم حفظ حالة إخفاء زر التسجيل بنجاح');
        } catch (e) {
          AppLogger.error('❌ خطأ في حفظ حالة إخفاء زر التسجيل: $e');
        }

        // نقل البيانات إلى شاشة تسجيل الدخول
        if (mounted) {
          // حفظ البيانات في SharedPreferences قبل الانتقال
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('temp_username', _usernameController.text);
          await prefs.setString('temp_password', _passwordController.text);

          // طباعة رسالة توضيحية للتأكد من حفظ البيانات
          AppLogger.info(
              'تم حفظ البيانات مؤقتاً: username=${_usernameController.text}, password=${_passwordController.text}');

          // التحقق من أن الشاشة لا تزال مثبتة قبل الانتقال
          if (mounted) {
            // الانتقال إلى شاشة تسجيل الدخول مع تمرير البيانات
            Navigator.of(context).pushReplacementNamed(
              AppRoutes.login,
              arguments: {
                'username': _usernameController.text,
                'password': _passwordController.text,
              },
            );
          }
        }
      } catch (e) {
        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();
        }

        // عرض رسالة خطأ
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل تحديث بيانات الحساب: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }

        AppLogger.error('خطأ في تحديث بيانات الحساب: $e');
        ErrorTracker.captureError(
          'فشل في تحديث بيانات الحساب',
          error: e,
          stackTrace: StackTrace.current,
          context: {
            'username': _usernameController.text,
            'email': _emailController.text,
            'userId': _defaultAdminId,
          },
        );
      }
    }
  }

  // عرض مؤشر التحميل
  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: AkLoadingIndicator(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إضافة حساب مدير النظام',
          style: AppTypography(
            fontSize: AppTypography.fontSizeLarge,
            fontWeight: FontWeight.w600,
            color: DynamicColors.textPrimary(context),
          ),
        ),
        centerTitle: true,
      ),
      body: _isLoadingDefaultAdmin
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const AkLoadingIndicator(),
                  SizedBox(height: AppDimensions.mediumSpacing),
                  Text(
                    'جاري تحميل بيانات المستخدم...',
                    style: AppTypography(
                      fontSize: AppTypography.fontSizeMedium,
                      color: DynamicColors.textSecondary(context),
                    ),
                  ),
                ],
              ),
            )
          : SafeArea(
              child: LayoutBuilder(builder: (context, constraints) {
                return SingleChildScrollView(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.defaultMargin,
                    vertical: AppDimensions.mediumSpacing,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildAppLogo(constraints),
                      SizedBox(height: AppDimensions.mediumSpacing),
                      Text(
                        'إضافة حساب مدير النظام',
                        style: AppTypography(
                          fontWeight: FontWeight.bold,
                          color: DynamicColors.primary,
                          fontSize: AppTypography.fontSizeLarge,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: AppDimensions.smallSpacing),
                      Text(
                        'قم بإضافة بيانات حساب مدير النظام',
                        style: AppTypography(
                          color: DynamicColors.textSecondary(context),
                          fontSize: AppTypography.fontSizeMedium,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: AppDimensions.largeSpacing),
                      AkCard(
                        child: Padding(
                          padding: EdgeInsets.all(AppDimensions.defaultMargin),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                /// حقل الاسم الكامل باستخدام AkTextInput الموحد
                                AkTextInput(
                                  controller: _nameController,
                                  label: 'الاسم الكامل',
                                  hint: 'عبدالملك حسين جابر الصماط',
                                  prefixIcon: Icons.person,
                                  isRequired: true,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال الاسم الكامل';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: AppDimensions.mediumSpacing),

                                /// حقل اسم المستخدم باستخدام AkTextInput الموحد
                                AkTextInput(
                                  controller: _usernameController,
                                  label: 'اسم المستخدم',
                                  hint: 'الصماط',
                                  prefixIcon: Icons.account_circle,
                                  isRequired: true,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال اسم المستخدم';
                                    }
                                    if (value.length < 3) {
                                      return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: AppDimensions.mediumSpacing),

                                /// حقل البريد الإلكتروني باستخدام AkTextInput الموحد
                                AkTextInput(
                                  controller: _emailController,
                                  label: 'البريد الإلكتروني',
                                  hint: '<EMAIL>',
                                  prefixIcon: Icons.email,
                                  keyboardType: TextInputType.emailAddress,
                                  isRequired: true,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال البريد الإلكتروني';
                                    }
                                    if (!RegExp(
                                            r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                        .hasMatch(value)) {
                                      return 'الرجاء إدخال بريد إلكتروني صحيح';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: AppDimensions.mediumSpacing),

                                /// حقل رقم الهاتف باستخدام AkTextInput الموحد
                                AkTextInput(
                                  controller: _phoneController,
                                  label: 'رقم الهاتف',
                                  hint: '967770119544',
                                  prefixIcon: Icons.phone,
                                  keyboardType: TextInputType.phone,
                                  isRequired: true,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال رقم الهاتف';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: AppDimensions.mediumSpacing),

                                /// حقل كلمة المرور باستخدام AkPasswordInput الموحد
                                AkPasswordInput(
                                  controller: _passwordController,
                                  label: 'كلمة المرور',
                                  hint: 'أدخل كلمة المرور',
                                  isRequired: true,
                                  showStrengthIndicator: true,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال كلمة المرور';
                                    }
                                    if (value.length < 6) {
                                      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: AppDimensions.mediumSpacing),

                                /// حقل تأكيد كلمة المرور باستخدام AkPasswordInput الموحد
                                AkPasswordInput(
                                  controller: _confirmPasswordController,
                                  label: 'تأكيد كلمة المرور',
                                  hint: 'أعد إدخال كلمة المرور',
                                  isRequired: true,
                                  showStrengthIndicator: false,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء تأكيد كلمة المرور';
                                    }
                                    if (value != _passwordController.text) {
                                      return 'كلمة المرور غير متطابقة';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: AppDimensions.largeSpacing),
                                _buildTermsRow(),
                                SizedBox(height: AppDimensions.largeSpacing),
                                _buildRegisterButton(),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: AppDimensions.largeSpacing),
                      _buildLoginRow(),
                      SizedBox(height: AppDimensions.extraLargeSpacing),
                    ],
                  ),
                );
              }),
            ),
    );
  }

  Widget _buildTermsRow() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Transform.scale(
          scale: 0.9,
          child: Checkbox(
            value: _agreeToTerms,
            activeColor: DynamicColors.primary,
            onChanged: (value) {
              setState(() {
                _agreeToTerms = value ?? false;
              });
            },
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _agreeToTerms = !_agreeToTerms;
              });
            },
            child: RichText(
              text: TextSpan(
                style: AppTypography(
                  fontSize: AppTypography.fontSizeMedium,
                  color: DynamicColors.textSecondary(context),
                ),
                children: [
                  const TextSpan(
                    text: 'أوافق على ',
                  ),
                  TextSpan(
                    text: 'الشروط والأحكام',
                    style: AppTypography(
                      color: DynamicColors.primary,
                      fontWeight: FontWeight.bold,
                      decoration: TextDecoration.underline,
                      fontSize: AppTypography.fontSizeMedium,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // فتح صفحة الشروط والأحكام
                        Navigator.of(context).pushNamed(AppRoutes.legal);
                      },
                  ),
                  const TextSpan(
                    text: ' و',
                  ),
                  TextSpan(
                    text: 'سياسة الخصوصية',
                    style: AppTypography(
                      color: DynamicColors.primary,
                      fontWeight: FontWeight.bold,
                      decoration: TextDecoration.underline,
                      fontSize: AppTypography.fontSizeMedium,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // فتح صفحة سياسة الخصوصية
                        Navigator.of(context).pushNamed(AppRoutes.legal);
                      },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء زر إنشاء الحساب باستخدام AkButton الموحد
  Widget _buildRegisterButton() {
    return AkButton(
      text: 'إنشاء حساب',
      onPressed: _agreeToTerms ? _handleRegister : null,
      type: AkButtonType.primary,
      size: AkButtonSize.medium,
      icon: Icons.person_add,
      isFullWidth: true,
    );
  }

  /// بناء صف العودة إلى تسجيل الدخول باستخدام مكونات AK الموحدة
  Widget _buildLoginRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'العودة إلى',
          style: AppTypography(
            fontSize: AppTypography.fontSizeMedium,
            color: DynamicColors.textSecondary(context),
          ),
        ),
        AkTextButton(
          text: 'شاشة تسجيل الدخول',
          onPressed: () {
            Navigator.of(context).pop();
          },
          type: AkButtonType.primary,
        ),
      ],
    );
  }

  /// بناء شعار التطبيق مع تصميم متجاوب وألوان ديناميكية
  Widget _buildAppLogo(BoxConstraints constraints) {
    /// حساب الحجم المناسب بناءً على عرض الشاشة
    final logoSize = constraints.maxWidth * 0.28; // 28% من عرض الشاشة
    final iconSize = logoSize * 0.25; // 25% من حجم الشعار

    return Center(
      child: Container(
        width: logoSize,
        height: logoSize,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              DynamicColors.primary, // لون ديناميكي يتكيف مع الثيم
              AppColors.secondary, // لون ثابت للتدرج
            ],
          ),
          borderRadius: BorderRadius.circular(AppDimensions.largeRadius),
          boxShadow: [
            BoxShadow(
              color: DynamicColors.primary.withValues(alpha: 0.15),
              blurRadius: 8,
              spreadRadius: 1,
              offset: const Offset(0, 4),
            )
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            /// الصف الأول من الأيقونات
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_cart,
                  size: iconSize,
                  color: Colors.white, // أبيض ثابت للوضوح على التدرج
                ),
                SizedBox(width: iconSize * 0.3),
                Icon(
                  Icons.store,
                  size: iconSize,
                  color: Colors.white, // أبيض ثابت للوضوح على التدرج
                ),
              ],
            ),
            SizedBox(height: iconSize * 0.5),

            /// الصف الثاني من الأيقونات
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inventory,
                  size: iconSize,
                  color: Colors.white, // أبيض ثابت للوضوح على التدرج
                ),
                SizedBox(width: iconSize * 0.3),
                Icon(
                  Icons.people,
                  size: iconSize,
                  color: Colors.white, // أبيض ثابت للوضوح على التدرج
                ),
              ],
            ),
            SizedBox(height: iconSize * 0.3),

            /// نص اسم التطبيق
            Text(
              'تاجر بلس',
              style: AppTypography(
                fontSize: iconSize * 0.6,
                fontWeight: FontWeight.bold,
                color: Colors.white, // أبيض ثابت للوضوح على التدرج
              ),
            ),
          ],
        ),
      ),
    );
  }
}
