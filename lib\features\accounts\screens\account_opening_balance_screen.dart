import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';
import '../../../core/utils/index.dart';
import '../presenters/account_presenter.dart';
import '../../../core/widgets/index.dart';
import '../../../core/providers/app_providers.dart';

class AccountOpeningBalanceScreen extends StatefulWidget {
  const AccountOpeningBalanceScreen({Key? key}) : super(key: key);

  @override
  State<AccountOpeningBalanceScreen> createState() =>
      _AccountOpeningBalanceScreenState();
}

class _AccountOpeningBalanceScreenState
    extends State<AccountOpeningBalanceScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  Map<String, dynamic>? _selectedAccount;
  DateTime _selectedDate = DateTime.now();
  bool _isDebit = true; // true للمدين، false للدائن
  bool _isLoading = false;
  bool _isSaving = false;
  List<Map<String, dynamic>> _filteredAccounts = [];
  List<Map<String, dynamic>> _openingBalances = [];
  bool _showSearchField = false;

  // استخدام التحميل الكسول
  late final AccountPresenter _accountPresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _accountPresenter = AppProviders.getLazyPresenter<AccountPresenter>(
        () => AccountPresenter());
    _loadData();
    _searchController.addListener(_filterAccounts);
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الحسابات
      await _accountPresenter.loadAccounts();
      setState(() {
        _filteredAccounts = _accountPresenter.accounts;
      });

      // تحميل أرصدة أول المدة
      _loadOpeningBalances();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل البيانات: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _filterAccounts() {
    final query = _searchController.text.toLowerCase();

    if (query.isEmpty) {
      setState(() {
        _filteredAccounts = _accountPresenter.accounts;
      });
      return;
    }

    setState(() {
      _filteredAccounts = _accountPresenter.accounts.where((account) {
        return account['name'].toString().toLowerCase().contains(query) ||
            account['code'].toString().toLowerCase().contains(query);
      }).toList();
    });
  }

  Future<void> _loadOpeningBalances() async {
    // في التطبيق الحقيقي، يجب استرداد أرصدة أول المدة من قاعدة البيانات
    // هذه مجرد بيانات وهمية للعرض
    setState(() {
      _openingBalances = [
        {
          'id': '1',
          'date': DateTime.now().subtract(const Duration(days: 30)),
          'accountName': 'الصندوق',
          'accountCode': '1001',
          'amount': 5000.0,
          'isDebit': true,
          'notes': 'رصيد افتتاحي للصندوق',
        },
        {
          'id': '2',
          'date': DateTime.now().subtract(const Duration(days: 25)),
          'accountName': 'البنك',
          'accountCode': '1002',
          'amount': 10000.0,
          'isDebit': true,
          'notes': 'رصيد افتتاحي للبنك',
        },
      ];
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveOpeningBalance() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من اختيار الحساب
    if (_selectedAccount == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار الحساب')),
      );
      return;
    }

    // عرض مربع حوار التأكيد
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحفظ'),
        content: const Text('هل تريد حفظ رصيد أول المدة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (confirm != true) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final amount = double.parse(_amountController.text);

      // في التطبيق الحقيقي، يجب حفظ رصيد أول المدة في قاعدة البيانات
      // وتحديث رصيد الحساب

      // إضافة رصيد أول المدة إلى القائمة للعرض
      setState(() {
        _openingBalances.add({
          'id': DateTime.now().millisecondsSinceEpoch.toString(),
          'date': _selectedDate,
          'accountName': _selectedAccount!['name'],
          'accountCode': _selectedAccount!['code'],
          'amount': amount,
          'isDebit': _isDebit,
          'notes': _notesController.text.isEmpty ? null : _notesController.text,
        });
      });

      // إعادة تعيين النموذج
      _resetForm();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ رصيد أول المدة بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حفظ رصيد أول المدة: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _resetForm() {
    _amountController.clear();
    _notesController.clear();
    setState(() {
      _selectedAccount = null;
      _isDebit = true;
    });
  }

  void _selectAccount(Map<String, dynamic> account) {
    setState(() {
      _selectedAccount = account;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _accountPresenter,
      builder: (context, child) {
        return Scaffold(
          appBar: AkAppBar(
            title: 'رصيد أول المدة للحسابات',
            showBackButton: true,
            actions: [
              // أيقونة البحث
              IconButton(
                icon: Icon(_showSearchField ? Icons.close : Icons.search),
                onPressed: () {
                  setState(() {
                    _showSearchField = !_showSearchField;
                    if (!_showSearchField) {
                      _searchController.clear();
                      _filterAccounts();
                    }
                  });
                },
              ),
            ],
          ),
          body: _isLoading
              ? const Center(child: AkLoadingIndicator())
              : Column(
                  children: [
                    // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث)
                    if (_showSearchField)
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: TextField(
                          controller: _searchController,
                          decoration: const InputDecoration(
                            hintText: 'بحث عن حساب...',
                            prefixIcon: Icon(Icons.search),
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            _filterAccounts();
                          },
                        ),
                      ),
                    // نموذج إضافة رصيد أول المدة
                    Expanded(
                      flex: 1,
                      child: SingleChildScrollView(
                        padding: EdgeInsets.zero,
                        child: AkCard(
                          padding: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 4.0, vertical: 4.0),
                            child: Form(
                              key: _formKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // التاريخ
                                  InkWell(
                                    onTap: () => _selectDate(context),
                                    child: InputDecorator(
                                      decoration: InputDecoration(
                                        labelText: 'التاريخ',
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                              Layout.defaultRadius),
                                        ),
                                        filled: true,
                                        prefixIcon:
                                            const Icon(Icons.calendar_today),
                                      ),
                                      child: Text(
                                        '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}',
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // الحساب
                                  _selectedAccount == null
                                      ? _buildAccountSelector()
                                      : _buildSelectedAccount(),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // المبلغ ونوع الرصيد
                                  Row(
                                    children: [
                                      // المبلغ
                                      Expanded(
                                        flex: 2,
                                        child: TextFormField(
                                          controller: _amountController,
                                          decoration: const InputDecoration(
                                            labelText: 'المبلغ',
                                            hintText: 'أدخل المبلغ',
                                            border: OutlineInputBorder(),
                                            prefixIcon:
                                                Icon(Icons.attach_money),
                                          ),
                                          keyboardType: const TextInputType
                                              .numberWithOptions(decimal: true),
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'يرجى إدخال المبلغ';
                                            }
                                            if (double.tryParse(value) ==
                                                null) {
                                              return 'يرجى إدخال رقم صحيح';
                                            }
                                            if (double.parse(value) <= 0) {
                                              return 'يجب أن يكون المبلغ أكبر من صفر';
                                            }
                                            return null;
                                          },
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      // نوع الرصيد
                                      Expanded(
                                        flex: 1,
                                        child: DropdownButtonFormField<bool>(
                                          value: _isDebit,
                                          decoration: InputDecoration(
                                            labelText: 'نوع الرصيد',
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      Layout.defaultRadius),
                                            ),
                                            filled: true,
                                            prefixIcon: const Icon(
                                                Icons.account_balance),
                                          ),
                                          items: const [
                                            DropdownMenuItem<bool>(
                                              value: true,
                                              child: Text('مدين'),
                                            ),
                                            DropdownMenuItem<bool>(
                                              value: false,
                                              child: Text('دائن'),
                                            ),
                                          ],
                                          onChanged: (value) {
                                            setState(() {
                                              _isDebit = value!;
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // الملاحظات
                                  TextFormField(
                                    controller: _notesController,
                                    decoration: const InputDecoration(
                                      labelText: 'ملاحظات',
                                      hintText: 'أدخل ملاحظات إضافية (اختياري)',
                                      border: OutlineInputBorder(),
                                      prefixIcon: Icon(Icons.note),
                                    ),
                                    maxLines: 2,
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // زر الحفظ
                                  Center(
                                    child: SizedBox(
                                      width: Layout.isTablet()
                                          ? 200
                                          : double.infinity,
                                      height: 45,
                                      child: ElevatedButton.icon(
                                        onPressed: _isSaving
                                            ? null
                                            : _saveOpeningBalance,
                                        icon: _isSaving
                                            ? const SizedBox(
                                                width: 18,
                                                height: 18,
                                                child: AkLoadingIndicator.small(
                                                    color: AppColors
                                                        .lightTextSecondary))
                                            : const Icon(Icons.save, size: 20),
                                        label: Text(
                                          'حفظ',
                                          style:
                                              AppTypography.createCustomStyle(
                                                  fontSize: 15),
                                        ),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: AppColors.primary,
                                          foregroundColor: AppColors.onPrimary,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    // جدول أرصدة أول المدة
                    Expanded(
                      flex: 1,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4.0, vertical: 4.0),
                        child: AkCard(
                          padding: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 4.0, vertical: 4.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 4.0, vertical: 2.0),
                                  child: Text(
                                    'أرصدة أول المدة',
                                    style:
                                        Theme.of(context).textTheme.titleMedium,
                                  ),
                                ),
                                Expanded(
                                  child: AdvancedDataTable(
                                    columns: const [
                                      DataColumn(label: Text('إجراءات')),
                                      DataColumn(label: Text('م')),
                                      DataColumn(label: Text('التاريخ')),
                                      DataColumn(label: Text('الحساب')),
                                      DataColumn(label: Text('الرمز')),
                                      DataColumn(label: Text('المبلغ')),
                                      DataColumn(label: Text('النوع')),
                                      DataColumn(label: Text('ملاحظات')),
                                    ],
                                    rows: _openingBalances
                                        .asMap()
                                        .entries
                                        .map((entry) {
                                      final balance = entry.value;
                                      return DataRow(
                                        cells: [
                                          DataCell(
                                            PopupMenuButton<String>(
                                              icon: const Icon(Icons.more_vert),
                                              onSelected: (value) async {
                                                if (value == 'delete') {
                                                  // حذف رصيد أول المدة بعد التأكيد
                                                  final confirm =
                                                      await showDialog<bool>(
                                                    context: context,
                                                    builder: (context) =>
                                                        AlertDialog(
                                                      title: const Text(
                                                          'تأكيد الحذف'),
                                                      content: const Text(
                                                          'هل أنت متأكد من حذف هذا الرصيد؟'),
                                                      actions: [
                                                        TextButton(
                                                          onPressed: () =>
                                                              Navigator.of(
                                                                      context)
                                                                  .pop(false),
                                                          child: const Text(
                                                              'إلغاء'),
                                                        ),
                                                        ElevatedButton(
                                                          onPressed: () =>
                                                              Navigator.of(
                                                                      context)
                                                                  .pop(true),
                                                          style: ElevatedButton
                                                              .styleFrom(
                                                            backgroundColor:
                                                                AppColors.error,
                                                          ),
                                                          child:
                                                              const Text('حذف'),
                                                        ),
                                                      ],
                                                    ),
                                                  );

                                                  if (confirm == true) {
                                                    setState(() {
                                                      _openingBalances
                                                          .removeAt(entry.key);
                                                    });
                                                  }
                                                }
                                              },
                                              itemBuilder: (context) => [
                                                const PopupMenuItem<String>(
                                                  value: 'delete',
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.delete,
                                                          color:
                                                              AppColors.error),
                                                      SizedBox(width: 8),
                                                      Text('حذف'),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          DataCell(Text('${entry.key + 1}')),
                                          DataCell(Text(
                                              '${(balance['date'] as DateTime).year}-${(balance['date'] as DateTime).month.toString().padLeft(2, '0')}-${(balance['date'] as DateTime).day.toString().padLeft(2, '0')}')),
                                          DataCell(Text(balance['accountName']
                                              as String)),
                                          DataCell(Text(balance['accountCode']
                                              as String)),
                                          DataCell(Text(
                                              (balance['amount'] as double)
                                                  .toStringAsFixed(2))),
                                          DataCell(Text(
                                              balance['isDebit'] as bool
                                                  ? 'مدين'
                                                  : 'دائن')),
                                          DataCell(Text(
                                              balance['notes'] as String? ??
                                                  '-')),
                                        ],
                                      );
                                    }).toList(),
                                    isLoading: false,
                                    showCellBorder: true,
                                    zebraPattern: true,
                                    headerBackgroundColor: AppColors.primary,
                                    headerTextColor: AppColors.onPrimary,
                                    showRowNumbers: false,
                                    emptyMessage: 'لا توجد أرصدة أول مدة',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
        );
      },
    );
  }

  Widget _buildAccountSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          child: Text(
            'اختر حساب',
            style: AppTypography.createCustomStyle(
                fontWeight: AppTypography.weightBold),
          ),
        ),
        Container(
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.lightTextSecondary),
            borderRadius: BorderRadius.circular(Layout.defaultRadius),
          ),
          child: _filteredAccounts.isEmpty
              ? const Center(child: Text('لا توجد حسابات'))
              : ListView.builder(
                  itemCount: _filteredAccounts.length,
                  itemBuilder: (context, index) {
                    final account = _filteredAccounts[index];
                    return ListTile(
                      title: Text(account['name']),
                      subtitle: Text('الرمز: ${account['code']}'),
                      trailing: Text(
                          'النوع: ${_getAccountTypeName(account['type'])}'),
                      onTap: () => _selectAccount(account),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildSelectedAccount() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.lightTextSecondary),
        borderRadius: BorderRadius.circular(Layout.defaultRadius),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الحساب المحدد: ${_selectedAccount!['name']}',
                  style: AppTypography.createCustomStyle(
                      fontWeight: AppTypography.weightBold),
                ),
                Text(
                    'الرمز: ${_selectedAccount!['code']} | النوع: ${_getAccountTypeName(_selectedAccount!['type'])}'),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              setState(() {
                _selectedAccount = null;
              });
            },
          ),
        ],
      ),
    );
  }

  String _getAccountTypeName(String type) {
    switch (type) {
      case 'asset':
        return 'أصول';
      case 'liability':
        return 'خصوم';
      case 'equity':
        return 'حقوق ملكية';
      case 'revenue':
        return 'إيرادات';
      case 'expense':
        return 'مصروفات';
      default:
        return type;
    }
  }
}
