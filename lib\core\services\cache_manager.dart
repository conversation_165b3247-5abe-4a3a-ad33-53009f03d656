import 'dart:collection';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_logger.dart';

/// مدير التخزين المؤقت
/// يستخدم لتخزين البيانات المستخدمة بشكل متكرر في الذاكرة وفي التخزين المحلي
class CacheManager {
  // Singleton pattern
  static final CacheManager _instance = CacheManager._internal();
  factory CacheManager() => _instance;
  CacheManager._internal();

  // التخزين المؤقت في الذاكرة
  final Map<String, _CacheEntry> _memoryCache = {};

  // حجم التخزين المؤقت الأقصى
  final int _maxCacheSize = 100;

  // قائمة LRU للتحكم في حجم التخزين المؤقت
  final LinkedHashMap<String, DateTime> _lruKeys =
      LinkedHashMap<String, DateTime>();

  // مدة صلاحية التخزين المؤقت الافتراضية (بالدقائق)
  final int _defaultExpiryMinutes = 30;

  // مؤشر إذا كان التخزين المؤقت مفعل
  bool _isCacheEnabled = true;

  /// تفعيل أو تعطيل التخزين المؤقت
  set isCacheEnabled(bool value) {
    _isCacheEnabled = value;
    if (!value) {
      clearMemoryCache();
    }
  }

  /// الحصول على حالة التخزين المؤقت
  bool get isCacheEnabled => _isCacheEnabled;

  /// تخزين قيمة في التخزين المؤقت
  Future<void> set<T>(String key, T value, {int? expiryMinutes}) async {
    if (!_isCacheEnabled) return;

    try {
      // تخزين في الذاكرة
      final expiryTime = DateTime.now()
          .add(Duration(minutes: expiryMinutes ?? _defaultExpiryMinutes));
      _memoryCache[key] = _CacheEntry<T>(value, expiryTime);

      // تحديث قائمة LRU
      _lruKeys[key] = DateTime.now();

      // التحقق من حجم التخزين المؤقت
      _checkCacheSize();

      // تخزين في التخزين المحلي إذا كانت القيمة قابلة للتحويل إلى JSON
      if (value is Map ||
          value is List ||
          value is String ||
          value is num ||
          value is bool) {
        final prefs = await SharedPreferences.getInstance();
        final cacheData = {
          'value': value,
          'expiry': expiryTime.millisecondsSinceEpoch,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        await prefs.setString('cache_$key', jsonEncode(cacheData));
      }
    } catch (e) {
      AppLogger.error('خطأ في تخزين القيمة في التخزين المؤقت: $e');
    }
  }

  /// الحصول على قيمة من التخزين المؤقت
  Future<T?> get<T>(String key) async {
    if (!_isCacheEnabled) return null;

    try {
      // التحقق من وجود القيمة في الذاكرة
      if (_memoryCache.containsKey(key)) {
        final cacheEntry = _memoryCache[key];

        // التحقق من صلاحية القيمة
        if (cacheEntry != null &&
            cacheEntry.expiryTime.isAfter(DateTime.now())) {
          // تحديث قائمة LRU
          _lruKeys[key] = DateTime.now();

          return cacheEntry.value as T?;
        } else {
          // حذف القيمة منتهية الصلاحية
          _memoryCache.remove(key);
          _lruKeys.remove(key);
        }
      }

      // إذا لم تكن القيمة موجودة في الذاكرة، نحاول الحصول عليها من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString('cache_$key');

      if (cachedData != null) {
        final decodedData = jsonDecode(cachedData);
        final expiryTime =
            DateTime.fromMillisecondsSinceEpoch(decodedData['expiry']);

        // التحقق من صلاحية القيمة
        if (expiryTime.isAfter(DateTime.now())) {
          final value = decodedData['value'];

          // تخزين القيمة في الذاكرة
          _memoryCache[key] = _CacheEntry<T>(value as T, expiryTime);
          _lruKeys[key] = DateTime.now();

          return value as T?;
        } else {
          // حذف القيمة منتهية الصلاحية
          await prefs.remove('cache_$key');
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على القيمة من التخزين المؤقت: $e');
    }

    return null;
  }

  /// حذف قيمة من التخزين المؤقت
  Future<void> remove(String key) async {
    try {
      // حذف من الذاكرة
      _memoryCache.remove(key);
      _lruKeys.remove(key);

      // حذف من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('cache_$key');
    } catch (e) {
      AppLogger.error('خطأ في حذف القيمة من التخزين المؤقت: $e');
    }
  }

  /// مسح التخزين المؤقت في الذاكرة
  void clearMemoryCache() {
    _memoryCache.clear();
    _lruKeys.clear();
  }

  /// مسح التخزين المؤقت بالكامل
  Future<void> clearAllCache() async {
    try {
      // مسح الذاكرة
      clearMemoryCache();

      // مسح التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('cache_'));

      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      AppLogger.error('خطأ في مسح التخزين المؤقت: $e');
    }
  }

  /// التحقق من حجم التخزين المؤقت وحذف العناصر القديمة إذا تجاوز الحد الأقصى
  void _checkCacheSize() {
    if (_lruKeys.length > _maxCacheSize) {
      // ترتيب المفاتيح حسب وقت الاستخدام
      final sortedKeys = _lruKeys.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      // حذف أقدم 20% من العناصر
      final keysToRemove = sortedKeys
          .take((_maxCacheSize * 0.2).ceil())
          .map((e) => e.key)
          .toList();

      for (final key in keysToRemove) {
        _memoryCache.remove(key);
        _lruKeys.remove(key);
      }
    }
  }

  /// تنظيف التخزين المؤقت من العناصر منتهية الصلاحية
  Future<void> cleanExpiredCache() async {
    try {
      final now = DateTime.now();

      // تنظيف الذاكرة
      final expiredMemoryKeys = _memoryCache.entries
          .where((entry) => entry.value.expiryTime.isBefore(now))
          .map((entry) => entry.key)
          .toList();

      for (final key in expiredMemoryKeys) {
        _memoryCache.remove(key);
        _lruKeys.remove(key);
      }

      // تنظيف التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('cache_'));

      for (final key in keys) {
        final cachedData = prefs.getString(key);

        if (cachedData != null) {
          try {
            final decodedData = jsonDecode(cachedData);
            final expiryTime =
                DateTime.fromMillisecondsSinceEpoch(decodedData['expiry']);

            if (expiryTime.isBefore(now)) {
              await prefs.remove(key);
            }
          } catch (e) {
            // إذا كان هناك خطأ في تحليل البيانات، نحذف العنصر
            await prefs.remove(key);
          }
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في تنظيف التخزين المؤقت: $e');
    }
  }

  /// الحصول على إحصائيات التخزين المؤقت
  Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final memorySize = _memoryCache.length;

      // حساب حجم التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      final keys =
          prefs.getKeys().where((key) => key.startsWith('cache_')).toList();

      return {
        'memorySize': memorySize,
        'diskSize': keys.length,
        'totalSize': memorySize + keys.length,
        'maxSize': _maxCacheSize,
        'enabled': _isCacheEnabled,
      };
    } catch (e) {
      AppLogger.error('خطأ في الحصول على إحصائيات التخزين المؤقت: $e');
      return {
        'error': e.toString(),
      };
    }
  }
}

/// فئة تمثل عنصر في التخزين المؤقت
class _CacheEntry<T> {
  final T value;
  final DateTime expiryTime;

  _CacheEntry(this.value, this.expiryTime);
}
