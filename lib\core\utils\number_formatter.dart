import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/index.dart';

/// Clase para dibujar un triángulo para el tooltip
class TrianglePainter extends CustomPainter {
  final Color color;
  final bool hasBorder;
  final Color borderColor;
  final double borderWidth;

  TrianglePainter({
    required this.color,
    this.hasBorder = false,
    this.borderColor = AppColors.onPrimary,
    this.borderWidth = 0.5,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Crear el path para el triángulo
    final Path path = Path();
    path.moveTo(size.width / 2, size.height);
    path.lineTo(0, 0);
    path.lineTo(size.width, 0);
    path.close();

    // Dibujar el relleno
    final Paint fillPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    canvas.drawPath(path, fillPaint);

    // Dibujar el borde si está habilitado
    if (hasBorder) {
      final Paint borderPaint = Paint()
        ..color = borderColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = borderWidth;
      canvas.drawPath(path, borderPaint);
    }
  }

  @override
  bool shouldRepaint(covariant TrianglePainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.hasBorder != hasBorder ||
        oldDelegate.borderColor != borderColor ||
        oldDelegate.borderWidth != borderWidth;
  }
}

/// Clase de utilidad para formatear números financieros de manera profesional
class NumberFormatter {
  /// Formatea un número para mostrar en la interfaz de usuario
  ///
  /// [value]: El valor numérico a formatear
  /// [decimalPlaces]: Número de decimales a mostrar (por defecto 2)
  /// [showThousandsSeparator]: Si se debe mostrar el separador de miles (por defecto true)
  /// [currencySymbol]: Símbolo de moneda opcional para añadir al número
  static String formatNumber(
    double value, {
    int decimalPlaces = 2,
    bool showThousandsSeparator = true,
    String? currencySymbol,
  }) {
    // Crear el formato según las preferencias
    final NumberFormat formatter = NumberFormat.currency(
      locale: 'ar',
      symbol: currencySymbol ?? '',
      decimalDigits: decimalPlaces,
    );

    // Si no queremos separador de miles, usamos un formato diferente
    if (!showThousandsSeparator) {
      return value.toStringAsFixed(decimalPlaces);
    }

    // Formatear el número
    return formatter.format(value).trim();
  }

  /// Formatea un número para mostrar en campos de entrada
  /// Elimina los separadores de miles para facilitar la edición
  /// Siempre muestra 2 decimales por defecto
  static String formatForInput(double value, {int decimalPlaces = 2}) {
    // Asegurar que siempre se muestren exactamente 2 decimales
    return value.toStringAsFixed(decimalPlaces);
  }

  /// Convierte un número a su representación en palabras en árabe
  static String numberToArabicWords(double number) {
    // Implementación básica para convertir números a palabras en árabe
    // Esta es una implementación simplificada, se puede expandir según necesidades

    if (number == 0) return 'صفر';

    // Separar parte entera y decimal
    int integerPart = number.toInt();
    int decimalPart = ((number - integerPart) * 100).round();

    String result = _convertToArabicWords(integerPart);

    // Añadir parte decimal si existe
    if (decimalPart > 0) {
      result += ' و ${_convertToArabicWords(decimalPart)} هللة';
    }

    return result;
  }

  // Método auxiliar para convertir números enteros a palabras en árabe
  static String _convertToArabicWords(int number) {
    // Listas de palabras para números en árabe
    final List<String> ones = [
      '',
      'واحد',
      'اثنان',
      'ثلاثة',
      'أربعة',
      'خمسة',
      'ستة',
      'سبعة',
      'ثمانية',
      'تسعة',
      'عشرة',
      'أحد عشر',
      'اثنا عشر',
      'ثلاثة عشر',
      'أربعة عشر',
      'خمسة عشر',
      'ستة عشر',
      'سبعة عشر',
      'ثمانية عشر',
      'تسعة عشر'
    ];

    final List<String> tens = [
      '',
      '',
      'عشرون',
      'ثلاثون',
      'أربعون',
      'خمسون',
      'ستون',
      'سبعون',
      'ثمانون',
      'تسعون'
    ];

    final List<String> hundreds = [
      '',
      'مائة',
      'مائتان',
      'ثلاثمائة',
      'أربعمائة',
      'خمسمائة',
      'ستمائة',
      'سبعمائة',
      'ثمانمائة',
      'تسعمائة'
    ];

    // Soporte para números grandes
    if (number == 0) {
      return 'صفر';
    }

    // Millones y miles de millones
    if (number >= 1000000000) {
      // Mil millones (مليار)
      int billions = number ~/ 1000000000;
      int remainder = number % 1000000000;

      String billionsText = billions == 1
          ? 'مليار'
          : (billions == 2
              ? 'ملياران'
              : '${_convertToArabicWords(billions)} مليارات');

      if (remainder == 0) {
        return billionsText;
      } else {
        return '$billionsText و ${_convertToArabicWords(remainder)}';
      }
    }

    if (number >= 1000000) {
      // Millones (مليون)
      int millions = number ~/ 1000000;
      int remainder = number % 1000000;

      String millionsText = millions == 1
          ? 'مليون'
          : (millions == 2
              ? 'مليونان'
              : '${_convertToArabicWords(millions)} ملايين');

      if (remainder == 0) {
        return millionsText;
      } else {
        return '$millionsText و ${_convertToArabicWords(remainder)}';
      }
    }

    // Miles
    if (number >= 1000) {
      int thousands = number ~/ 1000;
      int remainder = number % 1000;

      String thousandsText;
      if (thousands == 1) {
        thousandsText = 'ألف';
      } else if (thousands == 2) {
        thousandsText = 'ألفان';
      } else if (thousands >= 3 && thousands <= 10) {
        thousandsText = '${_convertToArabicWords(thousands)} آلاف';
      } else {
        thousandsText = '${_convertToArabicWords(thousands)} ألف';
      }

      if (remainder == 0) {
        return thousandsText;
      } else {
        return '$thousandsText و ${_convertToArabicWords(remainder)}';
      }
    }

    // Cientos
    if (number >= 100) {
      int hundredsDigit = number ~/ 100;
      int remainder = number % 100;

      if (remainder == 0) {
        return hundreds[hundredsDigit];
      } else {
        return '${hundreds[hundredsDigit]} و ${_convertToArabicWords(remainder)}';
      }
    }

    // Decenas y unidades
    if (number < 20) {
      return ones[number];
    } else {
      int onesDigit = number % 10;
      int tensDigit = number ~/ 10;

      if (onesDigit == 0) {
        return tens[tensDigit];
      } else {
        return '${ones[onesDigit]} و ${tens[tensDigit]}';
      }
    }
  }
}

/// Formateador de texto para campos de entrada que muestra números con formato financiero
class FinancialTextInputFormatter extends TextInputFormatter {
  final int decimalPlaces;

  FinancialTextInputFormatter({this.decimalPlaces = 2});

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Si el texto está vacío, permitir
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // Permitir solo dígitos y punto decimal
    if (newValue.text.contains(RegExp(r'[^\d.]'))) {
      return oldValue;
    }

    // Verificar que solo haya un punto decimal
    if (newValue.text.split('.').length > 2) {
      return oldValue;
    }

    // Si hay un punto decimal, verificar que no haya más decimales de los permitidos
    if (newValue.text.contains('.')) {
      final parts = newValue.text.split('.');
      if (parts[1].length > decimalPlaces) {
        return oldValue;
      }
    }

    return newValue;
  }
}

/// Widget personalizado para mostrar campos de entrada financieros con formato profesional
class FinancialTextField extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final String hint;
  final bool isRequired;
  final bool readOnly;
  final int decimalPlaces;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconPressed;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final FocusNode? focusNode;

  const FinancialTextField({
    Key? key,
    required this.controller,
    required this.label,
    required this.hint,
    this.isRequired = false,
    this.readOnly = false,
    this.decimalPlaces = 2,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
    this.validator,
    this.onChanged,
    this.focusNode,
  }) : super(key: key);

  @override
  State<FinancialTextField> createState() => _FinancialTextFieldState();
}

class _FinancialTextFieldState extends State<FinancialTextField> {
  late FocusNode _focusNode;
  String _numberInWords = '';
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Actualizar el texto en palabras si ya hay un valor
    _updateNumberInWords();
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus) {
      // Cuando obtiene el foco, mostrar el número sin formato para facilitar la edición
      if (widget.controller.text.isNotEmpty) {
        try {
          final numericValue = double.parse(
              widget.controller.text.replaceAll(RegExp(r'[^\d.]'), ''));
          setState(() {
            widget.controller.text = NumberFormatter.formatForInput(
                numericValue,
                decimalPlaces: 2); // Siempre usar 2 decimales
          });
          // Mostrar el tooltip con el texto en palabras
          _showTooltip();
        } catch (e) {
          // Si hay un error al parsear, dejar el texto como está
        }
      }
    } else {
      // Cuando pierde el foco, formatear el número para mostrar
      _formatDisplayValue();
      // Ocultar el tooltip
      _hideTooltip();
    }
  }

  void _formatDisplayValue() {
    if (widget.controller.text.isNotEmpty) {
      try {
        final numericValue = double.parse(widget.controller.text);
        widget.controller.text = NumberFormatter.formatNumber(
          numericValue,
          decimalPlaces: 2, // Siempre usar 2 decimales
        );
        _updateNumberInWords();
      } catch (e) {
        // Si hay un error al parsear, dejar el texto como está
      }
    }
  }

  void _updateNumberInWords() {
    if (widget.controller.text.isNotEmpty) {
      try {
        final numericValue = double.parse(
            widget.controller.text.replaceAll(RegExp(r'[^\d.]'), ''));
        setState(() {
          _numberInWords = NumberFormatter.numberToArabicWords(numericValue);
        });
      } catch (e) {
        setState(() {
          _numberInWords = '';
        });
      }
    } else {
      setState(() {
        _numberInWords = '';
      });
    }
  }

  // Mostrar el tooltip con animación
  void _showTooltip() {
    // Solo mostrar el tooltip si hay un valor válido y el campo tiene el foco
    if (_numberInWords.isEmpty || !_focusNode.hasFocus) return;

    // Remover cualquier tooltip existente
    _hideTooltip();

    // Crear un nuevo overlay con animación
    _overlayEntry = OverlayEntry(
      builder: (context) {
        return Positioned(
          child: TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeOutCubic,
            tween: Tween<double>(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Opacity(
                  opacity: value,
                  child: child,
                ),
              );
            },
            child: CompositedTransformFollower(
              link: _layerLink,
              showWhenUnlinked: false,
              offset: const Offset(
                  0, -65), // Position above the field with more space
              child: Material(
                elevation: 0,
                color: Colors.transparent, // يبقى شفاف
                child: Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 10.0),
                  decoration: BoxDecoration(
                    // Usar un color fijo en lugar del color primario del tema
                    color: AppColors.successDark
                        .withValues(alpha: 0.9), // Verde oscuro
                    borderRadius: BorderRadius.circular(20.0),
                    boxShadow: [
                      BoxShadow(
                        color:
                            AppColors.lightTextPrimary.withValues(alpha: 0.24),
                        blurRadius: 8,
                        spreadRadius: 1,
                        offset: const Offset(0, 3),
                      ),
                    ],
                    border: Border.all(
                      color: AppColors.onPrimary.withValues(alpha: 0.2),
                      width: 0.5,
                    ),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      minWidth: 100,
                      maxWidth: 300,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: double.infinity,
                          child: Text(
                            _numberInWords,
                            style: AppTypography(
                              fontSize: 14,
                              color: AppColors.onPrimary,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.3,
                              shadows: [
                                Shadow(
                                  color: AppColors.lightTextPrimary
                                      .withValues(alpha: 0.4),
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                            softWrap: true,
                          ),
                        ),
                        // Use Transform.translate instead of negative margin
                        Transform.translate(
                          offset: const Offset(0, 4), // Move down slightly
                          child: CustomPaint(
                            size: const Size(20, 10),
                            painter: TrianglePainter(
                              color: AppColors.successDark
                                  .withValues(alpha: 0.9), // Verde oscuro
                              hasBorder: true,
                              borderColor:
                                  AppColors.onPrimary.withValues(alpha: 0.2),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );

    // Añadir el overlay al contexto
    Overlay.of(context).insert(_overlayEntry!);
  }

  // Ocultar el tooltip
  void _hideTooltip() {
    // Si no hay overlay, no hacer nada
    if (_overlayEntry == null) return;

    // Simplemente remover el overlay existente
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _hideTooltip();
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CompositedTransformTarget(
          link: _layerLink,
          child: TextFormField(
            controller: widget.controller,
            focusNode: _focusNode,
            decoration: InputDecoration(
              labelText: widget.isRequired ? '${widget.label} *' : widget.label,
              hintText: widget.hint,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
              filled: true,
              prefixIcon:
                  widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
              suffixIcon: widget.suffixIcon != null
                  ? IconButton(
                      icon: Icon(widget.suffixIcon),
                      onPressed: widget.onSuffixIconPressed,
                    )
                  : null,
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            readOnly: widget.readOnly,
            inputFormatters: [
              FinancialTextInputFormatter(decimalPlaces: widget.decimalPlaces),
            ],
            validator: widget.validator,
            onChanged: (value) {
              // Actualizar el texto en palabras inmediatamente mientras se escribe
              _updateNumberInWords();
              if (_focusNode.hasFocus && _numberInWords.isNotEmpty) {
                _showTooltip();
              }
              if (widget.onChanged != null) {
                widget.onChanged!(value);
              }
            },
          ),
        ),
      ],
    );
  }
}
