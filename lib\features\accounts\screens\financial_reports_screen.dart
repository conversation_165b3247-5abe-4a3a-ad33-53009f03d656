import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:intl/intl.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/utils/error_tracker.dart';
import '../presenters/account_presenter.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';

class FinancialReportsScreen extends StatefulWidget {
  const FinancialReportsScreen({Key? key}) : super(key: key);

  @override
  State<FinancialReportsScreen> createState() => _FinancialReportsScreenState();
}

class _FinancialReportsScreenState extends State<FinancialReportsScreen>
    with SingleTickerProviderStateMixin {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  late TabController _tabController;
  bool _isLoading = true;
  Map<String, dynamic> _financialSummary = {};
  List<Map<String, dynamic>> _incomeByCategory = [];
  List<Map<String, dynamic>> _expensesByCategory = [];
  List<Map<String, dynamic>> _recentTransactions = [];
  DateTimeRange _dateRange = DateTimeRange(
    start: DateTime.now().subtract(const Duration(days: 30)),
    end: DateTime.now(),
  );

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReportData();
  }

  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final db = await _databaseHelper.database;

      // Get financial summary
      if (!mounted) return;
      final accountPresenter = AppProviders.getLazyPresenter<AccountPresenter>(
          () => AccountPresenter());
      _financialSummary = await accountPresenter.getFinancialSummary();

      // Get income by category
      final List<Map<String, dynamic>> incomeByCategory = await db.rawQuery('''
        SELECT a.name, a.type, SUM(t.amount) as total
        FROM transactions t
        JOIN accounts a ON t.account_id = a.id
        WHERE (t.transaction_type = 'income' OR t.transaction_type = 'sale')
          AND t.transaction_date BETWEEN ? AND ?
        GROUP BY a.id
        ORDER BY total DESC
      ''', [
        _dateRange.start.toIso8601String(),
        _dateRange.end.toIso8601String()
      ]);

      // Get expenses by category
      final List<Map<String, dynamic>> expensesByCategory = await db.rawQuery(
          '''
        SELECT a.name, a.type, SUM(t.amount) as total
        FROM transactions t
        JOIN accounts a ON t.account_id = a.id
        WHERE (t.transaction_type = 'expense' OR t.transaction_type = 'purchase')
          AND t.transaction_date BETWEEN ? AND ?
        GROUP BY a.id
        ORDER BY total DESC
      ''',
          [
            _dateRange.start.toIso8601String(),
            _dateRange.end.toIso8601String()
          ]);

      // Get recent transactions
      final List<Map<String, dynamic>> recentTransactions = await db.rawQuery(
          '''
        SELECT t.*,
               a1.name as account_name,
               a2.name as related_account_name
        FROM transactions t
        LEFT JOIN accounts a1 ON t.account_id = a1.id
        LEFT JOIN accounts a2 ON t.related_account_id = a2.id
        WHERE t.transaction_date BETWEEN ? AND ?
        ORDER BY t.transaction_date DESC
        LIMIT 10
      ''',
          [
            _dateRange.start.toIso8601String(),
            _dateRange.end.toIso8601String()
          ]);

      setState(() {
        _incomeByCategory = incomeByCategory;
        _expensesByCategory = expensesByCategory;
        _recentTransactions = recentTransactions;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to load financial reports',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'FinancialReportsScreen'},
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load financial reports')),
        );
      }
    }
  }

  void _showDateFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية حسب التاريخ'),
        content: SizedBox(
          width: double.maxFinite,
          child: AkDateRangeInput(
            label: 'فترة التقرير',
            onDateRangeSelected: (start, end) {
              Navigator.pop(context);
              if (start != null && end != null) {
                setState(() {
                  _dateRange = DateTimeRange(start: start, end: end);
                });
                _loadReportData();
              }
            },
            initialStartDate: _dateRange.start,
            initialEndDate: _dateRange.end,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'التقارير المالية',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showDateFilterDialog,
            tooltip: 'تصفية حسب التاريخ',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: AkLoadingIndicator())
          : Column(
              children: [
                _buildDateRangeChip(),
                _buildFinancialSummary(),
                TabBar(
                  controller: _tabController,
                  tabs: const [
                    Tab(text: 'الإيرادات'),
                    Tab(text: 'المصروفات'),
                    Tab(text: 'آخر المعاملات'),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildIncomeTab(),
                      _buildExpensesTab(),
                      _buildRecentTransactionsTab(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildDateRangeChip() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Chip(
        label: Text(
          'من ${DateFormat('yyyy/MM/dd').format(_dateRange.start)} إلى ${DateFormat('yyyy/MM/dd').format(_dateRange.end)}',
          style: AppTypography.createCustomStyle(fontSize: 12),
        ),
        deleteIcon: const Icon(Icons.edit, size: 16),
        onDeleted: _showDateFilterDialog,
      ),
    );
  }

  Widget _buildFinancialSummary() {
    final totalIncome = _financialSummary['totalIncome'] as double? ?? 0.0;
    final totalExpenses = _financialSummary['totalExpenses'] as double? ?? 0.0;
    final netProfit = _financialSummary['netProfit'] as double? ?? 0.0;
    final transactionCount = _financialSummary['transactionCount'] as int? ?? 0;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملخص مالي',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'الإيرادات',
                  '${totalIncome.toStringAsFixed(2)} ر.س',
                  Icons.arrow_downward,
                  AppColors.success,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'المصروفات',
                  '${totalExpenses.toStringAsFixed(2)} ر.س',
                  Icons.arrow_upward,
                  AppColors.error,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'صافي الربح',
                  '${netProfit.toStringAsFixed(2)} ر.س',
                  Icons.account_balance,
                  netProfit >= 0 ? AppColors.success : AppColors.error,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'عدد المعاملات',
                  '$transactionCount',
                  Icons.receipt_long,
                  AppColors.info,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppTypography.createCustomStyle(fontSize: 14),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.spacing8),
            Text(
              value,
              style: AppTypography.createCustomStyle(
                fontSize: 18,
                fontWeight: AppTypography.weightBold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIncomeTab() {
    if (_incomeByCategory.isEmpty) {
      return const Center(child: Text('لا توجد إيرادات في هذه الفترة'));
    }

    return ListView.builder(
      itemCount: _incomeByCategory.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        final category = _incomeByCategory[index];
        final name = category['name'] as String;
        final type = category['type'] as String;
        final total = category['total'] as double;

        return ListTile(
          leading: CircleAvatar(
            backgroundColor: _getAccountTypeColor(type),
            child: Text(
              name.isNotEmpty ? name[0].toUpperCase() : '?',
              style:
                  AppTypography.createCustomStyle(color: AppColors.onPrimary),
            ),
          ),
          title: Text(name),
          subtitle: Text(_getAccountTypeDisplayName(type)),
          trailing: Text(
            '${total.toStringAsFixed(2)} ر.س',
            style: AppTypography.createCustomStyle(
              fontWeight: AppTypography.weightBold,
              color: AppColors.lightTextSecondary,
            ),
          ),
        );
      },
    );
  }

  Widget _buildExpensesTab() {
    if (_expensesByCategory.isEmpty) {
      return const Center(child: Text('لا توجد مصروفات في هذه الفترة'));
    }

    return ListView.builder(
      itemCount: _expensesByCategory.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        final category = _expensesByCategory[index];
        final name = category['name'] as String;
        final type = category['type'] as String;
        final total = category['total'] as double;

        return ListTile(
          leading: CircleAvatar(
            backgroundColor: _getAccountTypeColor(type),
            child: Text(
              name.isNotEmpty ? name[0].toUpperCase() : '?',
              style:
                  AppTypography.createCustomStyle(color: AppColors.onPrimary),
            ),
          ),
          title: Text(name),
          subtitle: Text(_getAccountTypeDisplayName(type)),
          trailing: Text(
            '${total.toStringAsFixed(2)} ر.س',
            style: AppTypography.createCustomStyle(
              fontWeight: AppTypography.weightBold,
              color: AppColors.lightTextSecondary,
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentTransactionsTab() {
    if (_recentTransactions.isEmpty) {
      return const Center(child: Text('لا توجد معاملات في هذه الفترة'));
    }

    return ListView.builder(
      itemCount: _recentTransactions.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        final transaction = _recentTransactions[index];
        final transactionType = transaction['transaction_type'] as String;
        final amount = transaction['amount'] as double;
        final transactionDate = DateTime.parse(transaction['transaction_date']);

        return ListTile(
          leading: CircleAvatar(
            backgroundColor: _getTransactionTypeColor(transactionType),
            child: Icon(
              _getTransactionTypeIcon(transactionType),
              color: AppColors.lightTextSecondary,
              size: 16,
            ),
          ),
          title: Text(_getTransactionDescription(transaction)),
          subtitle: Text(DateFormat('yyyy/MM/dd').format(transactionDate)),
          trailing: Text(
            '${amount.toStringAsFixed(2)} ر.س',
            style: AppTypography.createCustomStyle(
              fontWeight: AppTypography.weightBold,
              color: _getAmountColor(transactionType),
            ),
          ),
        );
      },
    );
  }

  Color _getAccountTypeColor(String type) {
    switch (type) {
      case 'asset':
        return AppColors.info;
      case 'liability':
        return AppColors.error;
      case 'equity':
        return AppColors.accent;
      case 'revenue':
        return AppColors.success;
      case 'expense':
        return AppColors.warning;
      case 'customer':
        return AppColors.secondary;
      case 'supplier':
        return AppColors.warning;
      case 'cash':
        return AppColors.info;
      default:
        return AppColors.lightTextSecondary;
    }
  }

  String _getAccountTypeDisplayName(String type) {
    switch (type) {
      case 'asset':
        return 'أصول';
      case 'liability':
        return 'خصوم';
      case 'equity':
        return 'حقوق ملكية';
      case 'revenue':
        return 'إيرادات';
      case 'expense':
        return 'مصروفات';
      case 'customer':
        return 'عميل';
      case 'supplier':
        return 'مورد';
      case 'cash':
        return 'نقدية';
      default:
        return type;
    }
  }

  Color _getTransactionTypeColor(String type) {
    switch (type) {
      case 'income':
        return AppColors.success;
      case 'expense':
        return AppColors.error;
      case 'transfer':
        return AppColors.info;
      case 'sale':
        return AppColors.accent;
      case 'purchase':
        return AppColors.warning;
      default:
        return AppColors.secondary;
    }
  }

  IconData _getTransactionTypeIcon(String type) {
    switch (type) {
      case 'income':
        return Icons.arrow_downward;
      case 'expense':
        return Icons.arrow_upward;
      case 'transfer':
        return Icons.swap_horiz;
      case 'sale':
        return Icons.shopping_cart;
      case 'purchase':
        return Icons.store;
      default:
        return Icons.attach_money;
    }
  }

  Color _getAmountColor(String type) {
    switch (type) {
      case 'income':
      case 'sale':
        return AppColors.success;
      case 'expense':
      case 'purchase':
        return AppColors.error;
      default:
        return AppColors.info;
    }
  }

  String _getTransactionDescription(Map<String, dynamic> transaction) {
    final transactionType = transaction['transaction_type'] as String;
    final accountName = transaction['account_name'] as String? ?? 'غير معروف';
    final relatedAccountName =
        transaction['related_account_name'] as String? ?? 'غير معروف';

    switch (transactionType) {
      case 'income':
        return 'إيراد إلى $accountName';
      case 'expense':
        return 'مصروف من $accountName';
      case 'transfer':
        return 'تحويل من $accountName إلى $relatedAccountName';
      case 'sale':
        return 'مبيعات: $relatedAccountName';
      case 'purchase':
        return 'مشتريات: $relatedAccountName';
      default:
        return transactionType;
    }
  }
}
