import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

// استيراد نموذج عنصر البيع وحالة المبيع من ملفات منفصلة
import 'sale_item.dart';
import 'sale_status.dart';

/// نموذج البيع الموحد
/// تم توحيده من جميع نماذج البيع في المشروع
class Sale extends BaseModel {
  // معلومات أساسية
  final String? saleNumber;
  final String? customerId;
  final String? customerName;
  final SaleStatus status;
  final DateTime date;

  // معلومات مالية
  final double subtotal;
  final double discount;
  final bool isDiscountPercentage;
  final double tax;
  final double total;
  final double amountPaid;
  final double? amountDue;

  // معلومات الدفع
  final String paymentMethod;
  final String? paymentReference;
  final String? paymentStatus;

  // معلومات إضافية
  final String? notes;
  final String? branchId;
  final String? branchName;
  final String? warehouseId;
  final String? warehouseName;
  final List<SaleItem> items;
  final Map<String, dynamic>? metadata;

  Sale({
    String? id,
    this.saleNumber,
    this.customerId,
    this.customerName,
    this.status = SaleStatus.completed,
    DateTime? date,
    required this.subtotal,
    this.discount = 0.0,
    this.isDiscountPercentage = false,
    this.tax = 0.0,
    required this.total,
    this.amountPaid = 0.0,
    this.amountDue,
    required this.paymentMethod,
    this.paymentReference,
    this.paymentStatus,
    this.notes,
    this.branchId,
    this.branchName,
    this.warehouseId,
    this.warehouseName,
    required this.items,
    this.metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  })  : date = date ?? DateTime.now(),
        super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا البيع مع استبدال الحقول المحددة بقيم جديدة
  Sale copyWith({
    String? id,
    String? saleNumber,
    String? customerId,
    String? customerName,
    SaleStatus? status,
    DateTime? date,
    double? subtotal,
    double? discount,
    bool? isDiscountPercentage,
    double? tax,
    double? total,
    double? amountPaid,
    double? amountDue,
    String? paymentMethod,
    String? paymentReference,
    String? paymentStatus,
    String? notes,
    String? branchId,
    String? branchName,
    String? warehouseId,
    String? warehouseName,
    List<SaleItem>? items,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Sale(
      id: id ?? this.id,
      saleNumber: saleNumber ?? this.saleNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      status: status ?? this.status,
      date: date ?? this.date,
      subtotal: subtotal ?? this.subtotal,
      discount: discount ?? this.discount,
      isDiscountPercentage: isDiscountPercentage ?? this.isDiscountPercentage,
      tax: tax ?? this.tax,
      total: total ?? this.total,
      amountPaid: amountPaid ?? this.amountPaid,
      amountDue: amountDue ?? this.amountDue,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentReference: paymentReference ?? this.paymentReference,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      notes: notes ?? this.notes,
      branchId: branchId ?? this.branchId,
      branchName: branchName ?? this.branchName,
      warehouseId: warehouseId ?? this.warehouseId,
      warehouseName: warehouseName ?? this.warehouseName,
      items: items ?? this.items,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل البيع إلى Map
  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    return {
      ...map,
      'sale_number': saleNumber,
      'customer_id': customerId,
      'customer_name': customerName,
      'status': saleStatusToString(status),
      'date': date.toIso8601String(),
      'subtotal': subtotal,
      'discount': discount,
      'is_discount_percentage': isDiscountPercentage ? 1 : 0,
      'tax': tax,
      'total': total,
      'amount_paid': amountPaid,
      'amount_due': amountDue,
      'payment_method': paymentMethod,
      'payment_reference': paymentReference,
      'payment_status': paymentStatus,
      'notes': notes,
      'branch_id': branchId,
      'branch_name': branchName,
      'warehouse_id': warehouseId,
      'warehouse_name': warehouseName,
      'metadata': metadata != null ? _encodeMetadata(metadata!) : null,
    };
  }

  /// إنشاء بيع من Map
  factory Sale.fromMap(Map<String, dynamic> map, {List<SaleItem>? items}) {
    return Sale(
      id: map['id'],
      saleNumber: map['sale_number'],
      customerId: map['customer_id'],
      customerName: map['customer_name'],
      status: map['status'] != null
          ? stringToSaleStatus(map['status'])
          : SaleStatus.completed,
      date: map['date'] != null ? DateTime.parse(map['date']) : DateTime.now(),
      subtotal: map['subtotal'] is int
          ? (map['subtotal'] as int).toDouble()
          : (map['subtotal'] as double? ?? 0.0),
      discount: map['discount'] is int
          ? (map['discount'] as int).toDouble()
          : (map['discount'] as double? ?? 0.0),
      isDiscountPercentage: map['is_discount_percentage'] == 1,
      tax: map['tax'] is int
          ? (map['tax'] as int).toDouble()
          : (map['tax'] as double? ?? 0.0),
      total: map['total'] is int
          ? (map['total'] as int).toDouble()
          : (map['total'] as double? ?? 0.0),
      amountPaid: map['amount_paid'] is int
          ? (map['amount_paid'] as int).toDouble()
          : (map['amount_paid'] as double? ?? 0.0),
      amountDue: map['amount_due'] is int
          ? (map['amount_due'] as int).toDouble()
          : (map['amount_due'] as double?),
      paymentMethod: map['payment_method'] ?? 'cash',
      paymentReference: map['payment_reference'],
      paymentStatus: map['payment_status'],
      notes: map['notes'],
      branchId: map['branch_id'],
      branchName: map['branch_name'],
      warehouseId: map['warehouse_id'],
      warehouseName: map['warehouse_name'],
      items: items ?? [],
      metadata:
          map['metadata'] != null ? _decodeMetadata(map['metadata']) : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تشفير البيانات الوصفية
  static String _encodeMetadata(Map<String, dynamic> metadata) {
    try {
      return jsonEncode(metadata);
    } catch (e) {
      return '{}';
    }
  }

  /// فك تشفير البيانات الوصفية
  static Map<String, dynamic> _decodeMetadata(String metadataString) {
    try {
      return jsonDecode(metadataString) as Map<String, dynamic>;
    } catch (e) {
      return {};
    }
  }

  /// تحويل البيع إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء بيع من JSON
  factory Sale.fromJson(String source) => Sale.fromMap(jsonDecode(source));

  /// حساب المبلغ المستحق
  double calculateAmountDue() {
    return total - amountPaid;
  }

  /// التحقق مما إذا كان البيع مدفوعًا بالكامل
  bool get isPaid => amountPaid >= total;

  /// التحقق مما إذا كان البيع مكتملًا
  bool get isCompleted => status == SaleStatus.completed;

  /// التحقق مما إذا كان البيع ملغيًا
  bool get isCancelled => status == SaleStatus.cancelled;

  /// التحقق مما إذا كان البيع معلقًا
  bool get isPending => status == SaleStatus.pending;

  /// التحقق مما إذا كان البيع مسودة
  bool get isDraft => status == SaleStatus.draft;

  /// التحقق مما إذا كان البيع مرتجعًا
  bool get isReturned => status == SaleStatus.returned;

  @override
  String toString() {
    return 'Sale(id: $id, saleNumber: $saleNumber, customerName: $customerName, total: $total, status: $status)';
  }
}
