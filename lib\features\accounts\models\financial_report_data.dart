import 'dart:convert';

/// نموذج بيانات التقارير المالية
class FinancialReportData {
  /// عنوان التقرير
  final String title;

  /// وصف التقرير
  final String? description;

  /// تاريخ التقرير
  final DateTime reportDate;

  /// تاريخ بداية الفترة
  final DateTime startDate;

  /// تاريخ نهاية الفترة
  final DateTime endDate;

  /// نوع التقرير
  final String reportType;

  /// العملة المستخدمة
  final String currency;

  /// رمز العملة
  final String currencySymbol;

  /// تفاصيل التقرير
  final Map<String, dynamic> details;

  /// إجمالي التقرير
  final double total;

  /// ملاحظات التقرير
  final String? notes;

  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  FinancialReportData({
    required this.title,
    this.description,
    required this.reportDate,
    required this.startDate,
    required this.endDate,
    required this.reportType,
    required this.currency,
    required this.currencySymbol,
    required this.details,
    required this.total,
    this.notes,
    this.metadata,
  });

  /// إنشاء نسخة من هذا التقرير مع استبدال الحقول المحددة بقيم جديدة
  FinancialReportData copyWith({
    String? title,
    String? description,
    DateTime? reportDate,
    DateTime? startDate,
    DateTime? endDate,
    String? reportType,
    String? currency,
    String? currencySymbol,
    Map<String, dynamic>? details,
    double? total,
    String? notes,
    Map<String, dynamic>? metadata,
  }) {
    return FinancialReportData(
      title: title ?? this.title,
      description: description ?? this.description,
      reportDate: reportDate ?? this.reportDate,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      reportType: reportType ?? this.reportType,
      currency: currency ?? this.currency,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      details: details ?? this.details,
      total: total ?? this.total,
      notes: notes ?? this.notes,
      metadata: metadata ?? this.metadata,
    );
  }

  /// تحويل التقرير إلى Map
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'report_date': reportDate.toIso8601String(),
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'report_type': reportType,
      'currency': currency,
      'currency_symbol': currencySymbol,
      'details': details,
      'total': total,
      'notes': notes,
      'metadata': metadata,
    };
  }

  /// إنشاء تقرير من Map
  factory FinancialReportData.fromMap(Map<String, dynamic> map) {
    return FinancialReportData(
      title: map['title'] ?? '',
      description: map['description'],
      reportDate: DateTime.parse(map['report_date']),
      startDate: DateTime.parse(map['start_date']),
      endDate: DateTime.parse(map['end_date']),
      reportType: map['report_type'] ?? '',
      currency: map['currency'] ?? '',
      currencySymbol: map['currency_symbol'] ?? '',
      details: Map<String, dynamic>.from(map['details'] ?? {}),
      total: map['total']?.toDouble() ?? 0.0,
      notes: map['notes'],
      metadata: map['metadata'] != null
          ? Map<String, dynamic>.from(map['metadata'])
          : null,
    );
  }

  /// تحويل التقرير إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء تقرير من JSON
  factory FinancialReportData.fromJson(String source) =>
      FinancialReportData.fromMap(json.decode(source));

  @override
  String toString() {
    return 'FinancialReportData(title: $title, reportType: $reportType, total: $total)';
  }
}
