import 'package:flutter/material.dart';
import 'error_handler.dart';

/// مكتبة لمعالجة النماذج
class FormHandler {
  /// معالجة تقديم النموذج
  static Future<bool> handleFormSubmission<T>({
    required BuildContext context,
    required GlobalKey<FormState> formKey,
    required Future<bool> Function() submitAction,
    required String successMessage,
    required String errorMessage,
    bool popOnSuccess = true,
    T? result,
  }) async {
    // التحقق من صحة النموذج
    if (!formKey.currentState!.validate()) return false;

    try {
      // تنفيذ العملية
      final success = await submitAction();

      if (!context.mounted) return false;

      // عرض رسالة النجاح أو الخطأ
      if (success) {
        ErrorHandler.showSuccess(context, successMessage);
        
        // الرجوع إلى الشاشة السابقة إذا كان مطلوبًا
        if (popOnSuccess) {
          Navigator.pop(context, result ?? true);
        }
      } else {
        ErrorHandler.showError(context, errorMessage);
      }

      return success;
    } catch (e, stackTrace) {
      if (!context.mounted) return false;

      // عرض رسالة الخطأ
      ErrorHandler.showError(
        context,
        'حدث خطأ: $e',
        error: e,
        stackTrace: stackTrace,
      );

      return false;
    }
  }

  /// معالجة تقديم النموذج في حوار
  static Future<bool> handleDialogFormSubmission<T>({
    required BuildContext context,
    required GlobalKey<FormState> formKey,
    required Future<bool> Function() submitAction,
    required String successMessage,
    required String errorMessage,
    bool dismissOnSuccess = true,
    T? result,
  }) async {
    // التحقق من صحة النموذج
    if (!formKey.currentState!.validate()) return false;

    try {
      // تنفيذ العملية
      final success = await submitAction();

      if (!context.mounted) return false;

      // عرض رسالة النجاح أو الخطأ
      if (success) {
        ErrorHandler.showSuccess(context, successMessage);
        
        // إغلاق الحوار إذا كان مطلوبًا
        if (dismissOnSuccess) {
          Navigator.pop(context, result ?? true);
        }
      } else {
        ErrorHandler.showError(context, errorMessage);
      }

      return success;
    } catch (e, stackTrace) {
      if (!context.mounted) return false;

      // عرض رسالة الخطأ
      ErrorHandler.showError(
        context,
        'حدث خطأ: $e',
        error: e,
        stackTrace: stackTrace,
      );

      return false;
    }
  }

  /// معالجة عملية الحذف
  static Future<bool> handleDeleteOperation({
    required BuildContext context,
    required String itemName,
    required Future<bool> Function() deleteAction,
  }) async {
    return await ErrorHandler.handleDeleteOperation(
      context: context,
      itemName: itemName,
      deleteAction: deleteAction,
    );
  }

  /// معالجة عملية الاستعلام
  static Future<T?> handleQuery<T>({
    required BuildContext context,
    required Future<T> Function() queryAction,
    String? errorMessage,
    bool showLoadingIndicator = true,
  }) async {
    return await ErrorHandler.handleQuery(
      context: context,
      queryAction: queryAction,
      errorMessage: errorMessage,
      showLoadingIndicator: showLoadingIndicator,
    );
  }

  /// معالجة عملية مع عرض مؤشر التحميل
  static Future<T?> handleFutureWithLoading<T>({
    required BuildContext context,
    required Future<T> Function() action,
    String loadingMessage = 'جاري التحميل...',
    String? successMessage,
    String? errorMessage,
  }) async {
    return await ErrorHandler.handleFutureWithLoading(
      context: context,
      future: action(),
      loadingMessage: loadingMessage,
      successMessage: successMessage,
      errorMessage: errorMessage,
    );
  }

  /// التحقق من صحة النموذج
  static bool validateForm(GlobalKey<FormState> formKey) {
    return formKey.currentState?.validate() ?? false;
  }

  /// حفظ النموذج
  static void saveForm(GlobalKey<FormState> formKey) {
    formKey.currentState?.save();
  }

  /// إعادة تعيين النموذج
  static void resetForm(GlobalKey<FormState> formKey) {
    formKey.currentState?.reset();
  }
}
