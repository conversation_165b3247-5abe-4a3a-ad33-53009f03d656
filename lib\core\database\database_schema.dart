import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';
import 'package:flutter/foundation.dart';

/// فئة مخطط قاعدة البيانات
/// تحتوي على تعريفات جميع جداول قاعدة البيانات في المشروع
/// هذا الملف هو المرجع الوحيد لهيكل قاعدة البيانات في المشروع بالكامل
///
/// ملاحظات هامة:
/// 1. جميع الجداول تحتوي على حقل is_deleted للحذف الناعم
/// 2. يجب أن تتضمن جميع استعلامات القراءة شرط is_deleted = 0
/// 3. تم تنظيم الجداول في مستويات حسب اعتمادها على بعضها البعض
/// 4. تم استخدام FOREIGN KEY لتعريف العلاقات بين الجداول
/// 5. تم استخدام ON DELETE لتحديد سلوك الحذف في العلاقات
class DatabaseSchema {
  /// إنشاء جميع جداول قاعدة البيانات
  static Future<void> createAllTables(dynamic db) async {
    try {
      debugPrint('🚀 بدء إنشاء جميع جداول قاعدة البيانات...');
      AppLogger.info('إنشاء جميع جداول قاعدة البيانات...');

      // تم نقل أوامر PRAGMA إلى DatabaseHelper._initDatabase
      // لتنفيذها خارج أي معاملة وتجنب الأخطاء
      debugPrint('ℹ️ تم نقل أوامر PRAGMA إلى DatabaseHelper._initDatabase');
      AppLogger.info('تم نقل أوامر PRAGMA إلى DatabaseHelper._initDatabase');

      // تنظيم الجداول في مصفوفات حسب مستوى الاعتماد
      debugPrint('🔄 تنظيم الجداول حسب مستوى الاعتماد...');

      // المستوى 0: الجداول المستقلة التي لا تعتمد على أي جداول أخرى
      final level0Tables = [
        createSettingsTable,
        createRolesTable,
        createPermissionsTable,
        createUnitsTable,
        createCustomersTable,
        createSuppliersTable,
        createWarehousesTable,
        createPaymentMethodsTable,
        createFiscalPeriodsTable,
        createActivityLogsTable,
        createPromotionsTable,
        createBranchesTable,
        createUserGroupsTable,
        createCurrenciesTable,
      ];
      debugPrint('📋 المستوى 0: ${level0Tables.length} جداول');

      // المستوى 1: الجداول التي تعتمد على جداول المستوى 0 فقط
      final level1Tables = [
        createUsersTable,
        createRolePermissionsTable,
        createCategoriesTable,
        createInvoicesTable,
      ];
      debugPrint('📋 المستوى 1: ${level1Tables.length} جداول');

      // المستوى 2: الجداول التي تعتمد على جداول المستوى 0 و 1
      final level2Tables = [
        createProductsTable,
        createAccountsTable,
        createJournalEntriesTable,
        createInvoiceItemsTable,
        createSalesTable,
      ];
      debugPrint('📋 المستوى 2: ${level2Tables.length} جداول');

      // المستوى 3: الجداول التي تعتمد على جداول المستوى 2
      final level3Tables = [
        createInventoryTable,
        createInventoryTransactionsTable,
        createInventoryAdjustmentsTable,
        createInventoryAdjustmentItemsTable,
        createInvoiceItemsTable,
        createSaleItemsTable,
        createPaymentsTable,
        createExpensesTable,
        createJournalEntryDetailsTable,
        createTransactionsTable,
        createAccountPermissionsTable,
        createAuditLogsTable,
      ];
      debugPrint('📋 المستوى 3: ${level3Tables.length} جداول');

      // إنشاء الفهارس
      final createIndexes = [
        createDatabaseIndexes,
      ];

      // إنشاء جميع الجداول بالترتيب الصحيح
      debugPrint('🔄 بدء إنشاء جداول المستوى 0...');
      for (var createTableFn in level0Tables) {
        final tableName = _getFunctionName(createTableFn);
        debugPrint('🔄 جاري إنشاء جدول $tableName...');
        try {
          await createTableFn(db);
          debugPrint('✅ تم إنشاء جدول $tableName بنجاح');
          AppLogger.info('تم إنشاء جدول $tableName');
        } catch (e) {
          debugPrint('❌ فشل في إنشاء جدول $tableName: $e');
          rethrow;
        }
      }

      debugPrint('🔄 بدء إنشاء جداول المستوى 1...');
      for (var createTableFn in level1Tables) {
        final tableName = _getFunctionName(createTableFn);
        debugPrint('🔄 جاري إنشاء جدول $tableName...');
        try {
          await createTableFn(db);
          debugPrint('✅ تم إنشاء جدول $tableName بنجاح');
          AppLogger.info('تم إنشاء جدول $tableName');
        } catch (e) {
          debugPrint('❌ فشل في إنشاء جدول $tableName: $e');
          rethrow;
        }
      }

      debugPrint('🔄 بدء إنشاء جداول المستوى 2...');
      for (var createTableFn in level2Tables) {
        final tableName = _getFunctionName(createTableFn);
        debugPrint('🔄 جاري إنشاء جدول $tableName...');
        try {
          await createTableFn(db);
          debugPrint('✅ تم إنشاء جدول $tableName بنجاح');
          AppLogger.info('تم إنشاء جدول $tableName');
        } catch (e) {
          debugPrint('❌ فشل في إنشاء جدول $tableName: $e');
          rethrow;
        }
      }

      debugPrint('🔄 بدء إنشاء جداول المستوى 3...');
      for (var createTableFn in level3Tables) {
        final tableName = _getFunctionName(createTableFn);
        debugPrint('🔄 جاري إنشاء جدول $tableName...');
        try {
          await createTableFn(db);
          debugPrint('✅ تم إنشاء جدول $tableName بنجاح');
          AppLogger.info('تم إنشاء جدول $tableName');
        } catch (e) {
          debugPrint('❌ فشل في إنشاء جدول $tableName: $e');
          rethrow;
        }
      }

      // إنشاء الفهارس بعد إنشاء جميع الجداول
      debugPrint('🔄 بدء إنشاء الفهارس...');
      for (var createIndexFn in createIndexes) {
        try {
          await createIndexFn(db);
          debugPrint('✅ تم إنشاء الفهارس بنجاح');
          AppLogger.info('تم إنشاء الفهارس');
        } catch (e) {
          debugPrint('❌ فشل في إنشاء الفهارس: $e');
          rethrow;
        }
      }

      debugPrint('🎉 تم إنشاء جميع جداول قاعدة البيانات بنجاح');

      AppLogger.info('تم إنشاء جميع جداول قاعدة البيانات بنجاح');
    } catch (e, stackTrace) {
      debugPrint('❌❌❌ فشل في إنشاء جداول قاعدة البيانات: $e');
      debugPrint('❌❌❌ تفاصيل الخطأ: $stackTrace');
      AppLogger.error('فشل في إنشاء جداول قاعدة البيانات: $e');
      ErrorTracker.captureError(
        'فشل في إنشاء جداول قاعدة البيانات',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// استخراج اسم الدالة من مرجعها
  static String _getFunctionName(Function fn) {
    final fnName = fn.toString().split(' ')[1];
    if (fnName.startsWith('create') && fnName.endsWith('Table')) {
      return fnName.substring(6, fnName.length - 5).toLowerCase();
    }
    return fnName;
  }

  /// إنشاء جدول المستخدمين
  /// يحتوي على بيانات المستخدمين في النظام
  /// ملاحظات هامة:
  /// 1. يجب تشفير كلمة المرور قبل تخزينها باستخدام خوارزمية آمنة مثل bcrypt أو sha256
  /// 2. حقل role_id يشير إلى دور المستخدم في النظام ويحدد صلاحياته
  /// 3. حقل user_group_id يشير إلى مجموعة المستخدم ويمكن استخدامه للتحكم في الصلاحيات
  /// 4. حقل branch_id يشير إلى الفرع الذي ينتمي إليه المستخدم
  /// 5. حقل is_active يستخدم لتفعيل أو تعطيل حساب المستخدم دون حذفه
  /// 6. حقل is_deleted يستخدم للحذف الناعم بدلاً من الحذف الفعلي
  /// 7. تم إزالة الحقول المكررة (role_name, user_group_name, branch_name) لتجنب تكرار البيانات
  ///    ويتم الحصول على الأسماء عبر JOIN مع الجداول المرتبطة
  static Future<void> createUsersTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL, -- يجب تشفير كلمة المرور قبل التخزين
        full_name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        avatar TEXT,
        role_id TEXT,
        user_group_id TEXT,
        branch_id TEXT,
        branch_access_type TEXT DEFAULT 'single_branch',
        is_active INTEGER NOT NULL DEFAULT 1,
        last_login TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        created_by TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE SET NULL,
        FOREIGN KEY (user_group_id) REFERENCES user_groups (id) ON DELETE SET NULL,
        FOREIGN KEY (branch_id) REFERENCES branches (id) ON DELETE SET NULL
      )
    ''');
  }

  /// إنشاء جدول الأدوار
  static Future<void> createRolesTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS roles (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        display_name TEXT,
        description TEXT,
        is_custom INTEGER NOT NULL DEFAULT 0,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        created_by TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  /// إنشاء جدول الفئات الموحد
  /// جدول موحد لجميع أنواع الفئات في النظام (منتجات، حسابات، مصروفات، إلخ)
  /// ملاحظات هامة:
  /// 1. حقل type يحدد نوع الفئة (منتج، حساب، مصروف، إلخ)
  /// 2. حقل parent_id يشير إلى الفئة الأم في شجرة الفئات (علاقة ذاتية)
  /// 3. حقل account_type يستخدم فقط لفئات الحسابات ويحدد نوع الحساب
  /// 4. يمكن إنشاء شجرة فئات متعددة المستويات باستخدام العلاقة الذاتية
  /// 5. تم استخدام ON DELETE SET NULL للسماح بحذف الفئة الأم مع الحفاظ على الفئات الفرعية
  static Future<void> createCategoriesTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        parent_id TEXT,
        image_path TEXT,
        type TEXT NOT NULL, -- product, account, expense, etc.
        account_type TEXT, -- asset, liability, equity, revenue, expense (for account categories)
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (parent_id) REFERENCES categories (id) ON DELETE SET NULL
      )
    ''');
  }

  /// إنشاء جدول المنتجات
  /// يحتوي على بيانات المنتجات في النظام
  /// ملاحظات هامة:
  /// 1. حقل category_id يشير إلى فئة المنتج في جدول الفئات
  /// 2. حقل unit_id يشير إلى وحدة القياس الأساسية للمنتج
  /// 3. حقل quantity يمثل الكمية الإجمالية للمنتج في جميع المخازن
  /// 4. حقل min_stock يمثل الحد الأدنى للمخزون الذي يجب تنبيه المستخدم عنده
  /// 5. حقل cost_price يمثل سعر التكلفة (سعر الشراء)
  /// 6. حقل selling_price يمثل سعر البيع الافتراضي
  /// 7. تم استخدام ON DELETE SET NULL للسماح بحذف الفئة أو الوحدة مع الحفاظ على المنتج
  static Future<void> createProductsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        code TEXT,
        description TEXT,
        category_id TEXT,
        barcode TEXT,
        sku TEXT,
        unit_id TEXT,
        cost_price REAL NOT NULL DEFAULT 0.0,
        purchase_price REAL NOT NULL DEFAULT 0.0,
        selling_price REAL NOT NULL DEFAULT 0.0,
        sale_price REAL NOT NULL DEFAULT 0.0,
        quantity REAL NOT NULL DEFAULT 0.0,
        min_stock REAL DEFAULT 0.0,
        image_path TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL,
        FOREIGN KEY (unit_id) REFERENCES units (id) ON DELETE SET NULL
      )
    ''');
  }

  /// إنشاء جدول الوحدات
  static Future<void> createUnitsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS units (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        code TEXT,
        unit_type TEXT,
        description TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  /// إنشاء جدول العملاء
  static Future<void> createCustomersTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS customers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        tax_number TEXT,
        opening_balance REAL DEFAULT 0.0,
        balance REAL DEFAULT 0.0,
        credit_limit REAL DEFAULT 0.0,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  /// إنشاء جدول الموردين
  static Future<void> createSuppliersTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS suppliers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        tax_number TEXT,
        opening_balance REAL DEFAULT 0.0,
        balance REAL DEFAULT 0.0,
        account_id TEXT,
        supplier_type TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE SET NULL
      )
    ''');
  }

  /// إنشاء جدول المخازن
  /// يحتوي على بيانات المخازن في النظام
  /// ملاحظات هامة:
  /// 1. حقل manager_id يشير إلى المستخدم المسؤول عن إدارة المخزن
  /// 2. حقل branch_id يشير إلى الفرع الذي يتبع له المخزن
  /// 3. حقل is_default يحدد المخزن الافتراضي للعمليات
  /// 4. حقل capacity يمثل السعة الإجمالية للمخزن
  /// 5. حقل current_usage يمثل الاستخدام الحالي للمخزن
  static Future<void> createWarehousesTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS warehouses (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        code TEXT,
        description TEXT,
        location TEXT,
        address TEXT,
        phone TEXT,
        email TEXT,
        manager_id TEXT,
        branch_id TEXT,
        capacity REAL DEFAULT 0.0,
        current_usage REAL DEFAULT 0.0,
        is_default INTEGER NOT NULL DEFAULT 0,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (manager_id) REFERENCES users (id) ON DELETE SET NULL,
        FOREIGN KEY (branch_id) REFERENCES branches (id) ON DELETE SET NULL
      )
    ''');
  }

  /// إنشاء جدول الفواتير
  /// يحتوي على بيانات الفواتير في النظام (مبيعات، مشتريات، مرتجعات)
  /// ملاحظات هامة:
  /// 1. حقل invoice_type يحدد نوع الفاتورة (بيع، شراء، مرتجع)
  /// 2. حقل customer_id يستخدم في فواتير البيع والمرتجعات
  /// 3. حقل supplier_id يستخدم في فواتير الشراء والمرتجعات
  /// 4. حقل status يحدد حالة الفاتورة (مسودة، مؤكدة، مدفوعة، ملغاة)
  /// 5. حقل is_posted يشير إلى ما إذا كانت الفاتورة قد تم ترحيلها للحسابات
  /// 6. حقل balance يمثل المبلغ المتبقي من الفاتورة (total - paid_amount)
  static Future<void> createInvoicesTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS invoices (
        id TEXT PRIMARY KEY,
        invoice_number TEXT NOT NULL UNIQUE,
        invoice_type TEXT NOT NULL, -- sale, purchase, return
        customer_id TEXT,
        supplier_id TEXT,
        warehouse_id TEXT NOT NULL, -- المخزن إلزامي لجميع أنواع الفواتير
        date TEXT NOT NULL,
        due_date TEXT,
        status TEXT NOT NULL, -- draft, confirmed, paid, cancelled
        subtotal REAL NOT NULL DEFAULT 0.0,
        discount_type TEXT, -- percentage, amount
        discount_value REAL DEFAULT 0.0,
        tax_amount REAL DEFAULT 0.0,
        shipping_amount REAL DEFAULT 0.0,
        total REAL NOT NULL DEFAULT 0.0,
        paid_amount REAL DEFAULT 0.0,
        balance REAL DEFAULT 0.0,
        notes TEXT,
        terms_conditions TEXT,
        payment_terms TEXT,
        is_posted INTEGER NOT NULL DEFAULT 0,
        reference_number TEXT,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE RESTRICT,
        FOREIGN KEY (supplier_id) REFERENCES suppliers (id) ON DELETE RESTRICT,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id) ON DELETE RESTRICT
      )
    ''');
  }

  /// إنشاء جدول عناصر الفواتير
  /// يحتوي على تفاصيل المنتجات في كل فاتورة
  /// ملاحظات هامة:
  /// 1. كل سجل يمثل منتج واحد في فاتورة معينة
  /// 2. حقل invoice_id يربط العنصر بالفاتورة الأم
  /// 3. حقل product_id يشير إلى المنتج المباع أو المشترى
  /// 4. حقل subtotal يمثل (الكمية × سعر الوحدة) قبل الخصم والضريبة
  /// 5. حقل total يمثل المبلغ النهائي بعد الخصم والضريبة
  /// 6. تم استخدام ON DELETE CASCADE لحذف عناصر الفاتورة تلقائياً عند حذف الفاتورة
  /// 7. تم استخدام ON DELETE RESTRICT لمنع حذف المنتج إذا كان مستخدماً في فاتورة
  static Future<void> createInvoiceItemsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS invoice_items (
        id TEXT PRIMARY KEY,
        invoice_id TEXT NOT NULL,
        product_id TEXT NOT NULL,
        description TEXT,
        quantity REAL NOT NULL DEFAULT 0.0,
        unit_id TEXT,
        unit_price REAL NOT NULL DEFAULT 0.0,
        discount_type TEXT, -- percentage, amount
        discount_value REAL DEFAULT 0.0,
        tax_rate REAL DEFAULT 0.0,
        tax_amount REAL DEFAULT 0.0,
        subtotal REAL NOT NULL DEFAULT 0.0,
        total REAL NOT NULL DEFAULT 0.0,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE RESTRICT,
        FOREIGN KEY (unit_id) REFERENCES units (id) ON DELETE RESTRICT
      )
    ''');
  }

  /// إنشاء جدول المعاملات المالية
  static Future<void> createTransactionsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS transactions (
        id TEXT PRIMARY KEY,
        transaction_type TEXT NOT NULL, -- payment, receipt, expense, income, transfer
        amount REAL NOT NULL DEFAULT 0.0,
        date TEXT NOT NULL,
        reference_id TEXT, -- invoice_id, expense_id, etc.
        reference_type TEXT, -- invoice, expense, etc.
        account_id TEXT,
        customer_id TEXT,
        supplier_id TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE SET NULL,
        FOREIGN KEY (supplier_id) REFERENCES suppliers (id) ON DELETE SET NULL
      )
    ''');
  }

  /// إنشاء جدول الإعدادات
  /// يحتوي على إعدادات النظام والتطبيق
  /// ملاحظات هامة:
  /// 1. حقل category يصنف الإعدادات (company, financial, system, app, security)
  /// 2. حقل type يحدد نوع البيانات (text, number, boolean, select, file, email, phone, url)
  /// 3. حقل is_required يحدد ما إذا كان الإعداد مطلوب أم لا
  /// 4. حقل default_value يحتوي على القيمة الافتراضية
  /// 5. حقل options يحتوي على خيارات الاختيار للحقول من نوع select (JSON format)
  static Future<void> createSettingsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS settings (
        id TEXT PRIMARY KEY,
        key TEXT NOT NULL UNIQUE,
        value TEXT,
        description TEXT,
        category TEXT NOT NULL DEFAULT 'general',
        type TEXT NOT NULL DEFAULT 'text',
        is_required INTEGER NOT NULL DEFAULT 0,
        default_value TEXT,
        options TEXT,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  /// إنشاء جدول الصلاحيات
  static Future<void> createPermissionsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS permissions (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        module TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  /// إنشاء جدول صلاحيات الأدوار
  static Future<void> createRolePermissionsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS role_permissions (
        id TEXT PRIMARY KEY,
        role_id TEXT NOT NULL,
        permission_id TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE,
        FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE
      )
    ''');
  }

  /// إنشاء جدول المخزون
  /// يحتوي على الكميات الحالية للمنتجات في كل مخزن
  /// ملاحظات هامة:
  /// 1. كل سجل يمثل كمية منتج معين في مخزن معين
  /// 2. المفتاح الأساسي هو مزيج من المنتج والمخزن
  /// 3. يتم تحديث الكميات تلقائياً عند إجراء حركات المخزون
  /// 4. تم استخدام ON DELETE CASCADE لحذف سجلات المخزون عند حذف المنتج أو المخزن
  static Future<void> createInventoryTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS inventory (
        id TEXT PRIMARY KEY,
        product_id TEXT NOT NULL,
        warehouse_id TEXT NOT NULL,
        quantity REAL NOT NULL DEFAULT 0.0,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id) ON DELETE CASCADE,
        UNIQUE(product_id, warehouse_id)
      )
    ''');
  }

  /// إنشاء جدول حركات المخزون
  /// يحتوي على سجلات جميع حركات المخزون (إدخال، إخراج، تحويل، تعديل)
  /// ملاحظات هامة:
  /// 1. كل سجل يمثل حركة واحدة لمنتج معين في مخزن معين
  /// 2. حقل transaction_type يحدد نوع الحركة (إدخال، إخراج، تحويل، تعديل)
  /// 3. حقل reference_id يشير إلى المستند المرتبط بالحركة (فاتورة، أمر تحويل، إلخ)
  /// 4. حقل reference_type يحدد نوع المستند المرتبط (فاتورة بيع، فاتورة شراء، إلخ)
  /// 5. تم استخدام ON DELETE RESTRICT لمنع حذف المنتج أو المخزن إذا كان له حركات مخزون
  static Future<void> createInventoryTransactionsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS inventory_transactions (
        id TEXT PRIMARY KEY,
        product_id TEXT NOT NULL,
        warehouse_id TEXT NOT NULL,
        reference_id TEXT,
        reference_type TEXT,
        quantity REAL NOT NULL,
        transaction_type TEXT NOT NULL, -- in, out, transfer, adjustment
        date TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE RESTRICT,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id) ON DELETE RESTRICT
      )
    ''');
  }

  /// إنشاء جدول تعديلات المخزون
  /// يحتوي على سجلات تعديلات المخزون (زيادة، نقصان، جرد)
  static Future<void> createInventoryAdjustmentsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS inventory_adjustments (
        id TEXT PRIMARY KEY,
        reference_number TEXT,
        warehouse_id TEXT NOT NULL,
        adjustment_type TEXT NOT NULL, -- increase, decrease, inventory
        date TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id) ON DELETE RESTRICT
      )
    ''');
  }

  /// إنشاء جدول عناصر تعديلات المخزون
  /// يحتوي على تفاصيل المنتجات في كل تعديل مخزون
  static Future<void> createInventoryAdjustmentItemsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS inventory_adjustment_items (
        id TEXT PRIMARY KEY,
        adjustment_id TEXT NOT NULL,
        product_id TEXT NOT NULL,
        quantity REAL NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (adjustment_id) REFERENCES inventory_adjustments (id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE RESTRICT
      )
    ''');
  }

  /// إنشاء جدول المدفوعات
  static Future<void> createPaymentsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS payments (
        id TEXT PRIMARY KEY,
        payment_number TEXT NOT NULL UNIQUE,
        payment_type TEXT NOT NULL, -- payment, receipt
        amount REAL NOT NULL DEFAULT 0.0,
        date TEXT NOT NULL,
        reference_id TEXT,
        reference_type TEXT,
        payment_method_id TEXT,
        customer_id TEXT,
        supplier_id TEXT,
        account_id TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (payment_method_id) REFERENCES payment_methods (id) ON DELETE SET NULL,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE SET NULL,
        FOREIGN KEY (supplier_id) REFERENCES suppliers (id) ON DELETE SET NULL
      )
    ''');
  }

  /// إنشاء جدول طرق الدفع
  /// يحتوي على بيانات طرق الدفع المختلفة في النظام
  /// ملاحظات هامة:
  /// 1. حقل type يحدد نوع طريقة الدفع (نقدي، بنكي، إلكتروني، إلخ)
  /// 2. حقل is_default يحدد طريقة الدفع الافتراضية
  /// 3. حقل requires_approval يحدد ما إذا كانت تحتاج موافقة
  /// 4. حقول max_amount و min_amount لتحديد حدود المبالغ
  /// 5. حقل processing_fee لرسوم المعالجة
  /// 6. حقل currency لتحديد العملة المدعومة
  static Future<void> createPaymentMethodsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS payment_methods (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        code TEXT,
        description TEXT,
        type TEXT,
        is_default INTEGER NOT NULL DEFAULT 0,
        requires_approval INTEGER NOT NULL DEFAULT 0,
        max_amount REAL DEFAULT 0.0,
        min_amount REAL DEFAULT 0.0,
        processing_fee REAL DEFAULT 0.0,
        currency TEXT DEFAULT 'YER',
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  /// إنشاء جدول الحسابات
  /// يحتوي على بيانات الحسابات المالية في النظام (شجرة الحسابات)
  /// ملاحظات هامة:
  /// 1. حقل account_type يحدد نوع الحساب (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)
  /// 2. حقل parent_id يشير إلى الحساب الأب في شجرة الحسابات (علاقة ذاتية)
  /// 3. حقل category_id يشير إلى فئة الحساب في جدول الفئات
  /// 4. حقل opening_balance يمثل الرصيد الافتتاحي للحساب
  /// 5. حقل current_balance يمثل الرصيد الحالي للحساب ويتم تحديثه تلقائياً
  /// 6. تم استخدام ON DELETE SET NULL للسماح بحذف الفئة أو الحساب الأب مع الحفاظ على الحساب
  static Future<void> createAccountsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS accounts (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        code TEXT NOT NULL, -- رمز الحساب (مطلوب)
        description TEXT,
        category_id TEXT,
        parent_id TEXT,
        account_type TEXT NOT NULL, -- asset, liability, equity, revenue, expense
        type TEXT, -- للتوافق مع الكود القديم
        opening_balance REAL DEFAULT 0.0,
        current_balance REAL DEFAULT 0.0,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL,
        FOREIGN KEY (parent_id) REFERENCES accounts (id) ON DELETE SET NULL
      )
    ''');
  }

  /// إنشاء جدول المصروفات
  static Future<void> createExpensesTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS expenses (
        id TEXT PRIMARY KEY,
        expense_number TEXT NOT NULL UNIQUE,
        date TEXT NOT NULL,
        amount REAL NOT NULL DEFAULT 0.0,
        category_id TEXT,
        account_id TEXT,
        supplier_id TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL,
        FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE SET NULL,
        FOREIGN KEY (supplier_id) REFERENCES suppliers (id) ON DELETE SET NULL
      )
    ''');
  }

  /// إنشاء جدول قيود اليومية
  static Future<void> createJournalEntriesTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS journal_entries (
        id TEXT PRIMARY KEY,
        entry_number TEXT NOT NULL UNIQUE,
        date TEXT NOT NULL,
        reference_id TEXT,
        reference_type TEXT,
        notes TEXT,
        is_posted INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  /// إنشاء جدول تفاصيل قيود اليومية
  static Future<void> createJournalEntryDetailsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS journal_entry_details (
        id TEXT PRIMARY KEY,
        journal_entry_id TEXT NOT NULL,
        account_id TEXT NOT NULL,
        description TEXT,
        debit REAL DEFAULT 0.0,
        credit REAL DEFAULT 0.0,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id) ON DELETE CASCADE,
        FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE RESTRICT
      )
    ''');
  }

  /// إنشاء جدول الفترات المالية
  static Future<void> createFiscalPeriodsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS fiscal_periods (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        is_closed INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  /// إنشاء جدول العروض الترويجية
  /// يحتوي على العروض والخصومات الترويجية
  /// ملاحظات هامة:
  /// 1. يستخدم لإدارة العروض الترويجية والخصومات
  /// 2. يحتوي على تواريخ بداية ونهاية العرض
  /// 3. يمكن تطبيقه على منتجات أو فئات محددة
  /// 4. يحتوي على نوع الخصم (نسبة مئوية أو مبلغ ثابت)
  static Future<void> createPromotionsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS promotions (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        discount_type TEXT NOT NULL, -- percentage, amount
        discount_value REAL NOT NULL DEFAULT 0.0,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        min_purchase_amount REAL DEFAULT 0.0,
        max_discount_amount REAL DEFAULT 0.0,
        usage_limit INTEGER DEFAULT 0,
        used_count INTEGER DEFAULT 0,
        applicable_to TEXT, -- all, category, product
        applicable_ids TEXT, -- JSON array of category/product IDs
        display_order INTEGER DEFAULT 0,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  /// إنشاء جدول سجلات النشاطات
  /// يحتوي على سجلات نشاطات المستخدمين في النظام
  /// ملاحظات هامة:
  /// 1. يستخدم لتتبع نشاطات المستخدمين (تسجيل دخول، إضافة، تعديل، حذف)
  /// 2. يسجل المستخدم الذي قام بالنشاط وتاريخه
  /// 3. يحتوي على تفاصيل النشاط والوحدة التي تم فيها
  /// 4. يمكن استخدامه لمراجعة نشاطات المستخدمين
  static Future<void> createActivityLogsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS activity_logs (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        user_name TEXT NOT NULL,
        action TEXT NOT NULL, -- login, logout, create, update, delete, view, etc.
        module TEXT NOT NULL, -- products, customers, sales, etc.
        details TEXT,
        ip_address TEXT,
        timestamp INTEGER NOT NULL, -- Unix timestamp in milliseconds
        created_at TEXT NOT NULL
      )
    ''');
  }

  /// إنشاء جدول سجلات التدقيق
  /// يحتوي على سجلات جميع العمليات التي تتم على قاعدة البيانات
  /// ملاحظات هامة:
  /// 1. يستخدم لتتبع التغييرات التي تتم على البيانات
  /// 2. يحتفظ بالقيم القديمة والجديدة للسجلات المعدلة
  /// 3. يسجل المستخدم الذي قام بالعملية وتاريخها
  /// 4. يمكن استخدامه لاسترجاع البيانات المحذوفة أو المعدلة
  /// 5. لا يحتوي على حقل is_deleted لأنه لا يتم حذف سجلات التدقيق أبداً
  static Future<void> createAuditLogsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS audit_logs (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        action TEXT NOT NULL, -- insert, update, delete
        table_name TEXT NOT NULL,
        record_id TEXT,
        old_values TEXT, -- JSON format
        new_values TEXT, -- JSON format
        ip_address TEXT,
        timestamp TEXT NOT NULL,
        additional_info TEXT
      )
    ''');
  }

  /// إنشاء جدول صلاحيات الحسابات
  static Future<void> createAccountPermissionsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS account_permissions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        account_id TEXT NOT NULL,
        can_view INTEGER NOT NULL DEFAULT 1,
        can_edit INTEGER NOT NULL DEFAULT 0,
        can_delete INTEGER NOT NULL DEFAULT 0,
        can_create_transactions INTEGER NOT NULL DEFAULT 0,
        created_at TEXT,
        updated_at TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE
      )
    ''');
  }

  /// إنشاء جدول الفروع
  /// يحتوي على بيانات الفروع في النظام
  /// ملاحظات هامة:
  /// 1. حقل manager_id يشير إلى المستخدم المسؤول عن إدارة الفرع
  /// 2. تم إزالة حقل manager_name لتجنب تكرار البيانات - يتم الحصول عليه عبر JOIN مع جدول users
  /// 3. حقل is_main يحدد الفرع الرئيسي للشركة
  /// 4. حقل is_default يحدد الفرع الافتراضي للعمليات
  /// 5. حقل metadata يحتوي على بيانات إضافية بتنسيق JSON
  static Future<void> createBranchesTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS branches (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        code TEXT,
        description TEXT,
        address TEXT,
        phone TEXT,
        email TEXT,
        city TEXT,
        state TEXT,
        country TEXT,
        postal_code TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        is_main INTEGER NOT NULL DEFAULT 0,
        is_default INTEGER NOT NULL DEFAULT 0,
        manager_id TEXT,
        metadata TEXT,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (manager_id) REFERENCES users (id) ON DELETE SET NULL
      )
    ''');
  }

  /// إنشاء جدول مجموعات المستخدمين
  /// يحتوي على بيانات مجموعات المستخدمين في النظام
  static Future<void> createUserGroupsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS user_groups (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        permissions TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  /// إنشاء جدول العملات
  /// يحتوي على بيانات العملات المستخدمة في النظام
  /// ملاحظات هامة:
  /// 1. حقل code يحتوي على رمز العملة الدولي (ISO 4217)
  /// 2. حقل symbol يحتوي على رمز العملة للعرض
  /// 3. حقل is_default يحدد العملة الافتراضية للنظام
  /// 4. حقل exchange_rate يحتوي على سعر الصرف مقابل العملة الافتراضية
  /// 5. حقل decimal_places يحدد عدد الخانات العشرية للعملة
  static Future<void> createCurrenciesTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS currencies (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        symbol TEXT NOT NULL,
        is_default INTEGER DEFAULT 0,
        exchange_rate REAL DEFAULT 1.0,
        country TEXT,
        country_code TEXT,
        decimal_places INTEGER DEFAULT 2,
        description TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        created_by TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  /// إنشاء فهارس قاعدة البيانات للأعمدة المستخدمة في عمليات البحث المتكررة
  static Future<void> createDatabaseIndexes(dynamic db) async {
    try {
      AppLogger.info('إنشاء فهارس قاعدة البيانات...');

      // فهارس جدول المستخدمين
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_users_username ON users (username)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_users_is_deleted ON users (is_deleted)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_users_role_id ON users (role_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_users_user_group_id ON users (user_group_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_users_branch_id ON users (branch_id)');

      // فهارس جدول الفواتير
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_invoices_invoice_number ON invoices (invoice_number)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_invoices_customer_id ON invoices (customer_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_invoices_supplier_id ON invoices (supplier_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices (date)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices (status)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_invoices_is_deleted ON invoices (is_deleted)');

      // فهارس جدول عناصر الفواتير
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice_id ON invoice_items (invoice_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_invoice_items_product_id ON invoice_items (product_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_invoice_items_is_deleted ON invoice_items (is_deleted)');

      // فهارس جدول المنتجات
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_products_name ON products (name)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_products_barcode ON products (barcode)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_products_category_id ON products (category_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_products_is_deleted ON products (is_deleted)');

      // فهارس جدول العملاء
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_customers_name ON customers (name)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers (phone)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_customers_is_deleted ON customers (is_deleted)');

      // فهارس جدول الموردين
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers (name)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_suppliers_phone ON suppliers (phone)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_suppliers_is_deleted ON suppliers (is_deleted)');

      // فهارس جدول الفئات
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_categories_name ON categories (name)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories (parent_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_categories_type ON categories (type)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_categories_is_deleted ON categories (is_deleted)');

      // فهارس جدول الحسابات
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_accounts_name ON accounts (name)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_accounts_account_type ON accounts (account_type)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_accounts_parent_id ON accounts (parent_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_accounts_is_deleted ON accounts (is_deleted)');

      // فهارس جدول المخزون
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_inventory_product_id ON inventory (product_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_inventory_warehouse_id ON inventory (warehouse_id)');

      // فهارس جدول الفروع
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_branches_name ON branches (name)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_branches_is_main ON branches (is_main)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_branches_is_active ON branches (is_active)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_branches_is_deleted ON branches (is_deleted)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_branches_manager_id ON branches (manager_id)');

      // فهارس جدول مجموعات المستخدمين
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_user_groups_name ON user_groups (name)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_user_groups_is_active ON user_groups (is_active)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_user_groups_is_deleted ON user_groups (is_deleted)');

      // فهارس جدول حركات المخزون
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_inventory_transactions_product_id ON inventory_transactions (product_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_inventory_transactions_warehouse_id ON inventory_transactions (warehouse_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_inventory_transactions_date ON inventory_transactions (date)');

      // فهارس جدول المدفوعات
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_payments_payment_number ON payments (payment_number)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_payments_date ON payments (date)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_payments_customer_id ON payments (customer_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_payments_supplier_id ON payments (supplier_id)');

      // فهارس جدول الأدوار
      await db
          .execute('CREATE INDEX IF NOT EXISTS idx_roles_name ON roles (name)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_roles_is_active ON roles (is_active)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_roles_is_deleted ON roles (is_deleted)');

      // فهارس جدول الصلاحيات
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_permissions_name ON permissions (name)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_permissions_module ON permissions (module)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_permissions_is_deleted ON permissions (is_deleted)');

      // فهارس جدول ربط الصلاحيات بالأدوار
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions (role_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions (permission_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_role_permissions_is_deleted ON role_permissions (is_deleted)');

      // فهارس جدول سجلات التدقيق
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs (user_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs (action)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_audit_logs_table_name ON audit_logs (table_name)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs (timestamp)');

      // فهارس جدول المصروفات
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses (date)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_expenses_category_id ON expenses (category_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_expenses_is_deleted ON expenses (is_deleted)');

      // فهارس جدول قيود اليومية
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries (date)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_journal_entries_is_posted ON journal_entries (is_posted)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_journal_entries_is_deleted ON journal_entries (is_deleted)');

      // فهارس جدول العملات
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_currencies_code ON currencies (code)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_currencies_is_default ON currencies (is_default)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_currencies_is_active ON currencies (is_active)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_currencies_is_deleted ON currencies (is_deleted)');

      // فهارس جدول المبيعات
      await db
          .execute('CREATE INDEX IF NOT EXISTS idx_sales_date ON sales (date)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_sales_status ON sales (status)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_sales_customer_id ON sales (customer_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_sales_is_deleted ON sales (is_deleted)');

      // فهارس جدول عناصر المبيعات
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_sale_items_sale_id ON sale_items (sale_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_sale_items_product_id ON sale_items (product_id)');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_sale_items_sale_product ON sale_items (sale_id, product_id)');

      AppLogger.info('تم إنشاء فهارس قاعدة البيانات بنجاح');
    } catch (e, stackTrace) {
      AppLogger.error('فشل في إنشاء فهارس قاعدة البيانات: $e');
      ErrorTracker.captureError(
        'فشل في إنشاء فهارس قاعدة البيانات',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// إنشاء جدول المبيعات
  /// يحتوي على بيانات فواتير المبيعات
  /// ملاحظات هامة:
  /// 1. يرتبط بجدول العملاء والمخازن
  /// 2. يحتوي على حالة الفاتورة (مسودة، مؤكدة، مدفوعة، ملغية)
  /// 3. يحتوي على تفاصيل الخصومات والضرائب والشحن
  /// 4. يتم ربطه بجدول عناصر المبيعات لتفاصيل المنتجات
  static Future<void> createSalesTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS sales (
        id TEXT PRIMARY KEY,
        sale_number TEXT NOT NULL UNIQUE,
        code TEXT,
        barcode TEXT,
        customer_id TEXT,
        warehouse_id TEXT NOT NULL,
        date TEXT NOT NULL,
        due_date TEXT,
        status TEXT NOT NULL DEFAULT 'draft', -- draft, confirmed, completed, paid, cancelled
        subtotal REAL NOT NULL DEFAULT 0.0,
        discount_type TEXT, -- percentage, amount
        discount_value REAL DEFAULT 0.0,
        tax_amount REAL DEFAULT 0.0,
        shipping_amount REAL DEFAULT 0.0,
        total REAL NOT NULL DEFAULT 0.0,
        paid_amount REAL DEFAULT 0.0,
        balance REAL DEFAULT 0.0,
        notes TEXT,
        terms_conditions TEXT,
        payment_terms TEXT,
        is_posted INTEGER NOT NULL DEFAULT 0,
        reference_number TEXT,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE SET NULL,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id) ON DELETE RESTRICT
      )
    ''');
  }

  /// إنشاء جدول عناصر المبيعات
  /// يحتوي على تفاصيل المنتجات في كل فاتورة مبيعات
  /// ملاحظات هامة:
  /// 1. يرتبط بجدول المبيعات والمنتجات والوحدات
  /// 2. يحتوي على الكمية والسعر والإجمالي لكل منتج
  /// 3. يحتوي على تفاصيل الخصومات والضرائب لكل عنصر
  /// 4. يتم استخدامه لحساب إجماليات الفاتورة
  static Future<void> createSaleItemsTable(dynamic db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS sale_items (
        id TEXT PRIMARY KEY,
        sale_id TEXT NOT NULL,
        product_id TEXT NOT NULL,
        unit_id TEXT,
        quantity REAL NOT NULL DEFAULT 0.0,
        unit_price REAL NOT NULL DEFAULT 0.0,
        discount_type TEXT, -- percentage, amount
        discount_value REAL DEFAULT 0.0,
        tax_rate REAL DEFAULT 0.0,
        tax_amount REAL DEFAULT 0.0,
        total REAL NOT NULL DEFAULT 0.0,
        notes TEXT,
        created_at TEXT NOT NULL,
        created_by TEXT,
        updated_at TEXT,
        updated_by TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (sale_id) REFERENCES sales (id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE RESTRICT,
        FOREIGN KEY (unit_id) REFERENCES units (id) ON DELETE SET NULL
      )
    ''');
  }
}
