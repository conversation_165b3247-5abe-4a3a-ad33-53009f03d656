import 'package:flutter/material.dart';
import '../../../core/models/sale.dart';
import '../../../core/models/sale_item.dart';
import '../../../core/models/sale_status.dart';
import '../../../core/models/product.dart';
import '../../../core/models/customer.dart';
// import '../../../core/models/category.dart';
import '../../sales/presenters/sale_presenter.dart';
import '../../products/presenters/product_presenter.dart';
import '../../customers/presenters/customer_presenter.dart';
import '../models/cart_item.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';
import '../../../core/providers/app_providers.dart';

/// شاشة نقاط البيع (POS)
class POSScreen extends StatefulWidget {
  const POSScreen({Key? key}) : super(key: key);

  @override
  State<POSScreen> createState() => _POSScreenState();
}

class _POSScreenState extends State<POSScreen> {
  late SalePresenter _salePresenter;
  late ProductPresenter _productPresenter;
  late CustomerPresenter _customerPresenter;

  final _searchController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _discountController = TextEditingController(text: '0.0');
  final _taxController = TextEditingController(text: '0.0');
  final _paidAmountController = TextEditingController();
  final _notesController = TextEditingController();

  String? _selectedCustomerId;
  final List<CartItem> _cartItems = [];
  double _subtotal = 0.0;
  double _discount = 0.0;
  double _tax = 0.0;
  double _total = 0.0;
  double _change = 0.0;
  bool _isLoading = false;
  String _paymentMethod = 'cash'; // cash, card, bank_transfer

  // فلتر المنتجات
  String _searchQuery = '';
  String? _selectedCategoryId;
  List<Product> _filteredProducts = [];

  @override
  void initState() {
    super.initState();
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    _salePresenter =
        AppProviders.getLazyPresenter<SalePresenter>(() => SalePresenter());
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
        () => ProductPresenter());
    _customerPresenter = AppProviders.getLazyPresenter<CustomerPresenter>(
        () => CustomerPresenter());

    // تحميل البيانات باستخدام Future.microtask لتجنب استدعاء setState أثناء البناء
    Future.microtask(_loadData);

    // استمع إلى تغييرات الباركود لإضافة المنتج تلقائياً
    _barcodeController.addListener(_handleBarcodeInput);
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      await _productPresenter.loadProducts();
      await _customerPresenter.loadCustomers();
      _filterProducts();
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _filterProducts() {
    final products = _productPresenter.products;
    _filteredProducts = products.where((product) {
      // فلتر حسب الفئة
      if (_selectedCategoryId != null &&
          product.categoryId != _selectedCategoryId) {
        return false;
      }
      // فلتر حسب البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return product.name.toLowerCase().contains(query) ||
            (product.barcode?.toLowerCase().contains(query) ?? false) ||
            (product.sku?.toLowerCase().contains(query) ?? false);
      }
      return true;
    }).toList();
  }

  void _handleBarcodeInput() {
    final barcode = _barcodeController.text.trim();
    if (barcode.isNotEmpty) {
      // ابحث عن المنتج بالباركود
      final product = _productPresenter.products.firstWhere(
        (p) => p.barcode == barcode,
        orElse: () => Product(
          id: '',
          name: '',
          salePrice: 0,
          purchasePrice: 0,
          categoryId: '',
          unitId: '',
        ),
      );

      if (product.id.isNotEmpty) {
        _addProductToCart(product);
        _barcodeController.clear();
      }
    }
  }

  void _addProductToCart(Product product) {
    // تحقق إذا كان المنتج موجود بالفعل في السلة
    final existingItemIndex =
        _cartItems.indexWhere((item) => item.product.id == product.id);

    setState(() {
      if (existingItemIndex >= 0) {
        // زيادة الكمية إذا كان المنتج موجود بالفعل
        _cartItems[existingItemIndex] = _cartItems[existingItemIndex].copyWith(
          quantity: _cartItems[existingItemIndex].quantity + 1,
        );
      } else {
        // إضافة منتج جديد إلى السلة
        _cartItems.add(CartItem(
          product: product,
          quantity: 1,
        ));
      }
      _calculateTotals();
    });
  }

  void _removeItemFromCart(int index) {
    setState(() {
      _cartItems.removeAt(index);
      _calculateTotals();
    });
  }

  void _updateItemQuantity(int index, double quantity) {
    if (quantity <= 0) {
      _removeItemFromCart(index);
      return;
    }

    setState(() {
      final item = _cartItems[index];
      _cartItems[index] = item.copyWith(
        quantity: quantity,
      );
      _calculateTotals();
    });
  }

  void _calculateTotals() {
    _subtotal = _cartItems.fold(0, (sum, item) => sum + item.total);
    _discount = double.tryParse(_discountController.text) ?? 0.0;
    _tax = double.tryParse(_taxController.text) ?? 0.0;
    _total = _subtotal - _discount + _tax;

    // حساب المتبقي
    final paid = double.tryParse(_paidAmountController.text) ?? 0.0;
    _change = paid > _total ? paid - _total : 0.0;
  }

  Future<void> _completeSale() async {
    if (_cartItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا يمكن إتمام البيع. السلة فارغة!')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      // إنشاء قائمة عناصر البيع
      final saleItems = _cartItems
          .map((cartItem) => SaleItem(
                productId: cartItem.product.id,
                productName: cartItem.product.name,
                quantity: cartItem.quantity,
                price: cartItem.price,
                discount: cartItem.discount,
                isDiscountPercentage: cartItem.isDiscountPercentage,
                tax: cartItem.taxRate,
                isTaxPercentage: true,
              ))
          .toList();

      // إنشاء كائن البيع
      final sale = Sale(
        id: '',
        date: DateTime.now(),
        customerId: _selectedCustomerId,
        customerName: _selectedCustomerId != null
            ? _customerPresenter.customers
                .firstWhere(
                  (c) => c.id == _selectedCustomerId,
                  orElse: () => Customer(id: '', name: ''),
                )
                .name
            : null,
        items: saleItems,
        discount: _discount,
        isDiscountPercentage: false,
        tax: _tax,
        amountPaid: _paidAmountController.text.isEmpty
            ? _total
            : double.tryParse(_paidAmountController.text) ?? _total,
        paymentMethod: _paymentMethod,
        notes: _notesController.text,
        status: SaleStatus.completed,
        subtotal: _subtotal,
        total: _total,
      );

      final success = await _salePresenter.createSale(sale);

      // Verificar si el widget todavía está montado
      if (!mounted) return;

      if (success) {
        // طباعة الإيصال
        _printReceipt(sale);

        // إعادة تعيين الشاشة
        setState(() {
          _cartItems.clear();
          _selectedCustomerId = null;
          _discountController.text = '0.0';
          _taxController.text = '0.0';
          _paidAmountController.clear();
          _notesController.clear();
          _calculateTotals();
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إتمام عملية البيع بنجاح')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل في إتمام عملية البيع')),
        );
      }
    } catch (e) {
      // Verificar si el widget todavía está montado
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ: ${e.toString()}')),
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _printReceipt(Sale sale) {
    // سيتم تنفيذ طباعة الإيصال هنا
    // يمكن استخدام إعدادات الطابعة من شاشة إعدادات الطابعة
  }

  @override
  void dispose() {
    _searchController.dispose();
    _barcodeController.dispose();
    _discountController.dispose();
    _taxController.dispose();
    _paidAmountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AkLoadingOverlay(
      isLoading: _isLoading,
      child: Scaffold(
        appBar: AkAppBar(
          title: 'نقاط البيع',
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadData,
              tooltip: 'تحديث البيانات',
            ),
          ],
        ),
        body: Row(
          children: [
            // الجانب الأيمن - قائمة المنتجات
            Expanded(
              flex: 3,
              child: _buildProductsSection(),
            ),
            // الجانب الأيسر - سلة المشتريات والدفع
            Expanded(
              flex: 2,
              child: _buildCartSection(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductsSection() {
    return Column(
      children: [
        // شريط البحث والفلترة
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // حقل البحث
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'بحث عن منتج...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _searchQuery = '';
                              _filterProducts();
                            });
                          },
                        )
                      : null,
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                    _filterProducts();
                  });
                },
              ),
              const SizedBox(height: AppDimensions.spacing8),
              // حقل إدخال الباركود
              TextField(
                controller: _barcodeController,
                decoration: InputDecoration(
                  hintText: 'مسح الباركود...',
                  prefixIcon: const Icon(Icons.qr_code),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onSubmitted: (_) => _handleBarcodeInput(),
              ),
              const SizedBox(height: AppDimensions.spacing8),
              // فلتر الفئات
              ListenableBuilder(
                listenable: _productPresenter,
                builder: (context, child) {
                  final categories = _productPresenter.categories;
                  return DropdownButtonFormField<String?>(
                    value: _selectedCategoryId,
                    decoration: const InputDecoration(
                      labelText: 'تصفية حسب الفئة',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('جميع الفئات'),
                      ),
                      ...categories.map(
                        (category) {
                          if (category is Map<String, dynamic>) {
                            return DropdownMenuItem<String?>(
                              value: category['id']?.toString() ?? '',
                              child: Text(category['name']?.toString() ?? ''),
                            );
                          } else if (category is Product) {
                            return DropdownMenuItem<String?>(
                              value: category.id,
                              child: Text(category.name),
                            );
                          } else {
                            // Assuming it's a Category or any other type with id and name properties
                            final categoryObj = category as dynamic;
                            try {
                              return DropdownMenuItem<String?>(
                                value: categoryObj.id,
                                child: Text(categoryObj.name),
                              );
                            } catch (e) {
                              // Fallback for any other type
                              return const DropdownMenuItem<String?>(
                                value: '',
                                child: Text('غير معروف'),
                              );
                            }
                          }
                        },
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategoryId = value;
                        _filterProducts();
                      });
                    },
                  );
                },
              ),
            ],
          ),
        ),
        // عرض المنتجات
        Expanded(
          child: _filteredProducts.isEmpty
              ? const Center(child: Text('لا توجد منتجات متاحة'))
              : GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 1,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 10,
                  ),
                  itemCount: _filteredProducts.length,
                  itemBuilder: (context, index) {
                    final product = _filteredProducts[index];
                    return _buildProductCard(product);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildProductCard(Product product) {
    return InkWell(
      onTap: () => _addProductToCart(product),
      child: AkCard(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Center(
                child: product.imageUrl != null && product.imageUrl!.isNotEmpty
                    ? Image.network(
                        product.imageUrl!,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(Icons.image_not_supported,
                              size: 40);
                        },
                      )
                    : const Icon(Icons.inventory_2, size: 40),
              ),
            ),
            const SizedBox(height: AppDimensions.spacing8),
            Text(
              product.name,
              style: const AppTypography(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: AppDimensions.spacing4),
            Text(
              product.salePrice.toStringAsFixed(2),
              style: const AppTypography(color: AppColors.success),
            ),
            Text(
              'المخزون: ${product.quantity}',
              style: AppTypography(
                color: product.quantity <= 0 ? AppColors.error : AppColors.info,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartSection() {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.lightSurface,
        border: Border(
          right: BorderSide(color: AppColors.lightSurfaceVariant),
        ),
      ),
      child: Column(
        children: [
          // معلومات العميل
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ListenableBuilder(
              listenable: _customerPresenter,
              builder: (context, child) {
                final customers = _customerPresenter.customers;
                return DropdownButtonFormField<String?>(
                  value: _selectedCustomerId,
                  decoration: const InputDecoration(
                    labelText: 'العميل',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('عميل عادي'),
                    ),
                    ...customers.map(
                      (customer) => DropdownMenuItem(
                        value: customer.id,
                        child: Text(customer.name),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedCustomerId = value);
                  },
                );
              },
            ),
          ),
          // قائمة المنتجات في السلة
          Expanded(
            child: _cartItems.isEmpty
                ? const Center(child: Text('السلة فارغة'))
                : ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: _cartItems.length,
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      final item = _cartItems[index];
                      return ListTile(
                        title: Text(item.product.name),
                        subtitle: Text(
                            '${item.price.toStringAsFixed(2)} × ${item.quantity}'),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              item.total.toStringAsFixed(2),
                              style: const AppTypography(
                                  fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              icon: const Icon(Icons.remove_circle_outline),
                              onPressed: () =>
                                  _updateItemQuantity(index, item.quantity - 1),
                              color: AppColors.lightTextSecondary,
                            ),
                            Text('${item.quantity}'),
                            IconButton(
                              icon: const Icon(Icons.add_circle_outline),
                              onPressed: () =>
                                  _updateItemQuantity(index, item.quantity + 1),
                              color: AppColors.lightTextSecondary,
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () => _removeItemFromCart(index),
                              color: AppColors.lightTextSecondary,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
          ),
          // ملخص الفاتورة
          AkCard(
            margin: const EdgeInsets.all(16),
            child: Column(
              children: [
                // المجموع الفرعي
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('المجموع الفرعي:'),
                      Text(
                        _subtotal.toStringAsFixed(2),
                        style: const AppTypography(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                // الخصم
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    children: [
                      const Text('الخصم:'),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: _discountController,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            isDense: true,
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            setState(() {
                              _calculateTotals();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                // الضريبة
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    children: [
                      const Text('الضريبة:'),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: _taxController,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            isDense: true,
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            setState(() {
                              _calculateTotals();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                // الإجمالي
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'الإجمالي:',
                        style: AppTypography(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        _total.toStringAsFixed(2),
                        style: const AppTypography(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: AppColors.lightTextSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                // طريقة الدفع
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    children: [
                      const Text('طريقة الدفع:'),
                      const SizedBox(width: 8),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _paymentMethod,
                          decoration: const InputDecoration(
                            isDense: true,
                            border: OutlineInputBorder(),
                          ),
                          items: const [
                            DropdownMenuItem(
                                value: 'cash', child: Text('نقداً')),
                            DropdownMenuItem(
                                value: 'card', child: Text('بطاقة')),
                            DropdownMenuItem(
                                value: 'bank_transfer',
                                child: Text('تحويل بنكي')),
                          ],
                          onChanged: (value) {
                            setState(() => _paymentMethod = value!);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                // المبلغ المدفوع
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    children: [
                      const Text('المبلغ المدفوع:'),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: _paidAmountController,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            isDense: true,
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            setState(() {
                              _calculateTotals();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                // المتبقي
                if (_change > 0)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('المتبقي:'),
                        Text(
                          _change.toStringAsFixed(2),
                          style:
                              const AppTypography(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                // ملاحظات
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: TextField(
                    controller: _notesController,
                    decoration: const InputDecoration(
                      labelText: 'ملاحظات',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),
                ),
                // زر إتمام البيع
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: ElevatedButton.icon(
                    onPressed: _completeSale,
                    icon: const Icon(Icons.check_circle),
                    label: const Text('إتمام البيع'),
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 50),
                      backgroundColor: AppColors.success,
                      foregroundColor: AppColors.onPrimary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
