# 🧭 نظام التنقل الموحد (AK Navigation System)

نظام شامل وموحد لجميع عناصر التنقل في تطبيق تاجر بلس، مصمم خصيصاً للمشاريع التجارية اليمنية.

## 🎯 **المميزات الرئيسية**

- ✅ **تصميم موحد ومتناسق** لجميع عناصر التنقل
- ✅ **دعم كامل للوضع المظلم/الفاتح** باستخدام `Theme.of(context).brightness`
- ✅ **عدم وجود قيم صريحة** - استخدام `AppColors.*`, `AppDimensions.*`, `AppTypography.*` فقط
- ✅ **تأثيرات تفاعلية متقدمة** مع رسوم متحركة سلسة
- ✅ **دوال مساعدة سريعة** للاستخدام المباشر
- ✅ **تعليقات شاملة باللغة العربية**
- ✅ **أحجام متعددة** (صغير، متوسط، كبير)
- ✅ **دوال متخصصة للمشروع التجاري**

## 📋 **عناصر التنقل المتوفرة**

### 📑 **1. شريط التبويب (AkTabBar)**
شريط تبويب موحد مع أنواع مختلفة ودعم الأيقونات والشارات.

### 📊 **2. شريط التقدم (AkProgressBar)**
شريط تقدم متقدم مع أنواع مختلفة (خطي، دائري، خطوات، نسبة مئوية).

## 🚀 **أمثلة الاستخدام**

### **شريط التبويب الأساسي:**
```dart
// شريط تبويب بسيط
AkTabBar(
  tabs: ['المنتجات', 'العملاء', 'المبيعات'],
  onTabChanged: (index) => handleTabChange(index),
)
```

### **شريط تبويب مع أيقونات:**
```dart
// شريط تبويب مع أيقونات
AkTabBar(
  tabs: ['المنتجات', 'العملاء', 'المبيعات'],
  icons: [Icons.inventory_2, Icons.people, Icons.shopping_cart],
  type: AkTabType.withIcons,
  onTabChanged: (index) => handleTabChange(index),
)
```

### **شريط تبويب مع شارات:**
```dart
// شريط تبويب مع شارات الإشعارات
AkTabBar(
  tabs: ['الطلبات', 'الرسائل', 'الإشعارات'],
  badges: [5, 12, 3],
  type: AkTabType.withBadges,
  onTabChanged: (index) => handleTabChange(index),
)
```

### **شريط التقدم الخطي:**
```dart
// شريط تقدم خطي مع نسبة مئوية
AkProgressBar(
  value: 0.7,
  type: AkProgressType.linear,
  showPercentage: true,
  label: 'تقدم التحميل',
)
```

### **شريط التقدم الدائري:**
```dart
// شريط تقدم دائري
AkProgressBar(
  value: 0.85,
  type: AkProgressType.circular,
  showPercentage: true,
  label: 'اكتمال المهمة',
  size: AkNavigationSize.large,
)
```

### **شريط التقدم بالخطوات:**
```dart
// شريط تقدم بخطوات
AkProgressBar(
  value: 0.5,
  type: AkProgressType.stepped,
  totalSteps: 4,
  currentStep: 2,
  stepLabels: ['البيانات', 'التأكيد', 'الدفع', 'الإنجاز'],
  label: 'خطوات الطلب',
)
```

## 🎨 **أنواع شرائح التبويب**

| النوع | الوصف | الاستخدام |
|-------|--------|-----------|
| `normal` | شريط تبويب عادي | النصوص فقط |
| `withIcons` | مع أيقونات | نصوص + أيقونات |
| `withBadges` | مع شارات | نصوص + شارات الإشعارات |
| `scrollable` | قابل للتمرير | عدد كبير من التبويبات |

## 📊 **أنواع أشرطة التقدم**

| النوع | الوصف | الاستخدام |
|-------|--------|-----------|
| `linear` | شريط خطي | التقدم العادي |
| `circular` | شريط دائري | المهام المكتملة |
| `stepped` | بخطوات | العمليات متعددة المراحل |
| `percentage` | مع تفاصيل | عرض معلومات إضافية |

## 🛠️ **الدوال المساعدة المتوفرة**

### **دوال شرائح التبويب:**
- `AkNavigation.productsTabBar()` - تبويب المنتجات
- `AkNavigation.salesTabBar()` - تبويب المبيعات
- `AkNavigation.reportsTabBar()` - تبويب التقارير
- `AkNavigation.settingsTabBar()` - تبويب الإعدادات
- `AkNavigation.dashboardTabBar()` - تبويب لوحة التحكم
- `AkNavigation.posTabBar()` - تبويب نقطة البيع

### **دوال أشرطة التقدم:**
- `AkNavigation.dataLoadingProgress()` - تقدم تحميل البيانات
- `AkNavigation.fileUploadProgress()` - تقدم رفع الملفات
- `AkNavigation.backupProgress()` - تقدم النسخ الاحتياطي
- `AkNavigation.registrationSteps()` - خطوات التسجيل
- `AkNavigation.orderSteps()` - خطوات الطلب
- `AkNavigation.syncProgress()` - تقدم المزامنة

### **دوال متخصصة للمشروع التجاري:**
- `AkNavigation.dailySalesProgress()` - تقدم مبيعات اليوم
- `AkNavigation.stockLevelProgress()` - مستوى المخزون
- `AkNavigation.monthlyTargetProgress()` - تقدم الهدف الشهري

## 🎯 **أمثلة متقدمة**

### **شريط تبويب لوحة التحكم:**
```dart
AkNavigation.dashboardTabBar(
  onTabChanged: (index) => switchDashboardView(index),
  notificationCounts: [0, 5, 12, 0], // شارات الإشعارات
)
```

### **تقدم مبيعات اليوم:**
```dart
AkNavigation.dailySalesProgress(
  currentSales: 15750.0,
  targetSales: 20000.0,
  currency: 'ر.ي',
)
```

### **مستوى المخزون مع ألوان تحذيرية:**
```dart
AkNavigation.stockLevelProgress(
  currentStock: 15,
  maxStock: 100,
  productName: 'هاتف ذكي',
)
```

### **خطوات عملية الطلب:**
```dart
AkNavigation.orderSteps(
  currentStep: 2, // الخطوة الحالية
)
```

## 📏 **أحجام العناصر**

| الحجم | الوصف | الاستخدام |
|-------|--------|-----------|
| `small` | صغير | مناطق محدودة |
| `medium` | متوسط | الاستخدام العادي |
| `large` | كبير | شاشات رئيسية |

## 🎨 **التخصيص المتقدم**

### **ألوان مخصصة:**
```dart
AkTabBar(
  tabs: ['تبويب 1', 'تبويب 2'],
  activeColor: AppColors.success,
  inactiveColor: AppColors.textSecondary,
  onTabChanged: (index) => handleTab(index),
)
```

### **أحجام مختلفة:**
```dart
// شريط تبويب كبير لنقطة البيع
AkTabBar(
  tabs: ['المنتجات', 'السلة'],
  size: AkNavigationSize.large,
  onTabChanged: (index) => handlePOS(index),
)

// شريط تقدم صغير للمناطق المحدودة
AkProgressBar(
  value: 0.6,
  size: AkNavigationSize.small,
  type: AkProgressType.linear,
)
```

## 📱 **التوافق والاستجابة**

- ✅ **متوافق مع جميع أحجام الشاشات**
- ✅ **يتكيف مع اتجاه الشاشة**
- ✅ **يدعم الخطوط العربية**
- ✅ **متوافق مع إعدادات إمكانية الوصول**

## 🎨 **الدعم للوضع المظلم/الفاتح**

النظام يتكيف تلقائياً مع وضع التطبيق:

```dart
// يتم التحقق تلقائياً من الوضع
final isDark = Theme.of(context).brightness == Brightness.dark;

// الألوان تتغير تلقائياً
activeColor: isDark ? AppColors.darkPrimary : AppColors.lightPrimary,
backgroundColor: isDark ? AppColors.darkSurface : AppColors.lightSurface,
```

## 🔄 **الترقية من الأنظمة القديمة**

### **من enhanced_ui_components.dart:**
```dart
// ❌ القديم
EnhancedUIComponents.intelligentCard(...)

// ✅ الجديد
AkTabBar(tabs: [...], onTabChanged: (index) => handleTab(index))
```

## 📊 **الإحصائيات**

- **عدد عناصر التنقل**: 2 نوع أساسي
- **عدد الدوال المساعدة**: 15 دالة
- **عدد الأنواع**: 8 أنواع مختلفة
- **عدد الأحجام**: 3 أحجام
- **الدعم للغات**: العربية (أساسي)
- **حجم الملف**: ~1000 سطر
- **التبعيات**: لا توجد تبعيات خارجية

---

**تم تطوير هذا النظام خصيصاً لتطبيق تاجر بلس - نظام إدارة المبيعات اليمني** 🇾🇪
