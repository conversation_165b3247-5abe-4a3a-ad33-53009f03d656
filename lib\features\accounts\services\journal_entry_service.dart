import 'package:uuid/uuid.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';

import '../../users/services/activity_log_service.dart';
import '../../../core/database/database_service.dart';

/// خدمة إدارة قيود اليومية
class JournalEntryService {
  static final JournalEntryService _instance = JournalEntryService._internal();
  factory JournalEntryService() => _instance;
  JournalEntryService._internal();

  final DatabaseService _db = DatabaseService.instance;
  final ActivityLogService _activityLogService = ActivityLogService();

  /// إنشاء قيد محاسبي جديد
  Future<String> createJournalEntry({
    required DateTime date,
    required String reference,
    required String description,
    required List<Map<String, dynamic>> lines,
    String? userId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final String entryId = const Uuid().v4();
      final journalEntry = {
        'id': entryId,
        'date': date.toIso8601String(),
        'reference': reference,
        'description': description,
        'created_by': userId,
        'metadata': metadata,
        'is_deleted': 0,
      };

      // حفظ رأس القيد
      await _db.insert('journal_entries', journalEntry);

      // حفظ تفاصيل القيد
      for (var line in lines) {
        final entryLine = {
          'id': const Uuid().v4(),
          'journal_entry_id': entryId,
          'account_id': line['accountId'],
          'description': line['description'] ?? description,
          'debit': line['debit'] ?? 0.0,
          'credit': line['credit'] ?? 0.0,
          'amount': line['amount'] ?? 0.0,
          'is_deleted': 0,
        };
        await _db.insert('journal_entry_lines', entryLine);
      }

      // تسجيل النشاط
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'create',
        module: 'journal_entry',
        details: 'إنشاء قيد محاسبي: $reference',
      );

      AppLogger.info('تم إنشاء القيد المحاسبي بنجاح: $reference');
      return entryId;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إنشاء القيد المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'reference': reference},
      );
      rethrow;
    }
  }

  /// إلغاء قيد محاسبي (وضع علامة محذوف)
  Future<bool> cancelJournalEntry(String entryId, {String? userId}) async {
    try {
      // تحديث حالة القيد المحاسبي إلى محذوف
      await _db.update(
        'journal_entries',
        {'is_deleted': 1},
        where: 'id = ?',
        whereArgs: [entryId],
      );

      // تسجيل النشاط
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'delete',
        module: 'journal_entry',
        details: 'إلغاء قيد محاسبي: $entryId',
      );

      AppLogger.info('تم إلغاء القيد المحاسبي بنجاح: $entryId');
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إلغاء القيد المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'entryId': entryId},
      );
      return false;
    }
  }

  /// الحصول على قيد محاسبي بناءً على المعرف
  Future<Map<String, dynamic>?> getJournalEntryById(String entryId) async {
    try {
      final entries = await _db.query(
        'journal_entries',
        where: 'id = ? AND is_deleted = 0',
        whereArgs: [entryId],
      );

      if (entries.isEmpty) return null;

      final entry = entries.first;
      final lines = await _db.query(
        'journal_entry_lines',
        where: 'journal_entry_id = ? AND is_deleted = 0',
        whereArgs: [entryId],
      );

      final result = Map<String, dynamic>.from(entry);
      result['lines'] = lines;
      return result;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على القيد المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'entryId': entryId},
      );
      return null;
    }
  }

  /// الحصول على القيود المحاسبية لفترة زمنية معينة
  Future<List<Map<String, dynamic>>> getJournalEntriesForPeriod({
    required DateTime startDate,
    required DateTime endDate,
    int limit = 100,
    int offset = 0,
  }) async {
    try {
      final entries = await _db.query(
        'journal_entries',
        where: 'date >= ? AND date <= ? AND is_deleted = 0',
        whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
        orderBy: 'date DESC',
        limit: limit,
        offset: offset,
      );

      return entries;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على القيود المحاسبية للفترة',
        error: e,
        stackTrace: stackTrace,
        context: {
          'startDate': startDate.toIso8601String(),
          'endDate': endDate.toIso8601String(),
        },
      );
      return [];
    }
  }
}
