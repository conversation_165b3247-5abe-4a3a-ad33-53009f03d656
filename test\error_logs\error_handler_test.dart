import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/utils/error_handler.dart';

void main() {
  group('ErrorHandler Tests', () {
    testWidgets('يعرض رسالة خطأ في شريط Snackbar', (WidgetTester tester) async {
      // بناء تطبيق بسيط مع Scaffold
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorHandler.showError(
                      context,
                      'رسالة خطأ تجريبية',
                      error: Exception('خطأ للاختبار'),
                    );
                  },
                  child: const Text('عرض خطأ'),
                );
              },
            ),
          ),
        ),
      );

      // النقر على الزر لعرض الخطأ
      await tester.tap(find.text('عرض خطأ'));
      await tester.pump();
      
      // التحقق من ظهور رسالة الخطأ في Snackbar
      expect(find.text('رسالة خطأ تجريبية'), findsOneWidget);
      expect(find.text('إغلاق'), findsOneWidget);
    });

    testWidgets('يعرض رسالة نجاح في شريط Snackbar', (WidgetTester tester) async {
      // بناء تطبيق بسيط مع Scaffold
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorHandler.showSuccess(
                      context,
                      'تمت العملية بنجاح',
                    );
                  },
                  child: const Text('عرض نجاح'),
                );
              },
            ),
          ),
        ),
      );

      // النقر على الزر لعرض رسالة النجاح
      await tester.tap(find.text('عرض نجاح'));
      await tester.pump();
      
      // التحقق من ظهور رسالة النجاح في Snackbar
      expect(find.text('تمت العملية بنجاح'), findsOneWidget);
    });

    testWidgets('يعرض حوار خطأ', (WidgetTester tester) async {
      // بناء تطبيق بسيط مع Scaffold
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    await ErrorHandler.showErrorDialog(
                      context,
                      'عنوان الخطأ',
                      'تفاصيل الخطأ',
                      error: Exception('خطأ للاختبار'),
                    );
                  },
                  child: const Text('عرض حوار خطأ'),
                );
              },
            ),
          ),
        ),
      );

      // النقر على الزر لعرض حوار الخطأ
      await tester.tap(find.text('عرض حوار خطأ'));
      await tester.pumpAndSettle();
      
      // التحقق من ظهور حوار الخطأ
      expect(find.text('عنوان الخطأ'), findsOneWidget);
      expect(find.text('تفاصيل الخطأ'), findsOneWidget);
    });

    testWidgets('يعرض مؤشر تحميل أثناء تنفيذ عملية مستقبلية', (WidgetTester tester) async {
      // بناء تطبيق بسيط مع Scaffold
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    await ErrorHandler.handleFutureWithLoading(
                      context: context,
                      future: Future.delayed(const Duration(milliseconds: 100)),
                      loadingMessage: 'جاري التحميل...',
                      successMessage: 'تمت العملية بنجاح',
                    );
                  },
                  child: const Text('تنفيذ عملية'),
                );
              },
            ),
          ),
        ),
      );

      // النقر على الزر لتنفيذ العملية
      await tester.tap(find.text('تنفيذ عملية'));
      await tester.pump();
      
      // التحقق من ظهور مؤشر التحميل
      expect(find.text('جاري التحميل...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      
      // انتظار انتهاء العملية
      await tester.pumpAndSettle();
      
      // التحقق من ظهور رسالة النجاح
      expect(find.text('تمت العملية بنجاح'), findsOneWidget);
    });
  });
}
