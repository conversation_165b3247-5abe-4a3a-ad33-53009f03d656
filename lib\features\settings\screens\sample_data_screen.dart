import 'package:flutter/material.dart';
import '../../../core/database/sample_data_initializer.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';

import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';

/// شاشة تهيئة البيانات التجريبية
/// تسمح للمستخدم بتهيئة البيانات التجريبية عند الطلب
class SampleDataScreen extends StatefulWidget {
  const SampleDataScreen({Key? key}) : super(key: key);

  @override
  State<SampleDataScreen> createState() => _SampleDataScreenState();
}

class _SampleDataScreenState extends State<SampleDataScreen> {
  bool _isLoading = false;
  bool _isInitialized = false;
  String _statusMessage = '';
  double _progress = 0.0;
  final List<Map<String, dynamic>> _initializationSteps = [
    {'name': 'تهيئة الفئات', 'isCompleted': false, 'hasError': false},
    {'name': 'تهيئة وحدات القياس', 'isCompleted': false, 'hasError': false},
    {'name': 'تهيئة المنتجات', 'isCompleted': false, 'hasError': false},
    {'name': 'تهيئة العملاء', 'isCompleted': false, 'hasError': false},
    {'name': 'تهيئة الموردين', 'isCompleted': false, 'hasError': false},
    {'name': 'تهيئة المستودعات', 'isCompleted': false, 'hasError': false},
    {'name': 'تهيئة شجرة الحسابات', 'isCompleted': false, 'hasError': false},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'تهيئة البيانات التجريبية',
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 20),
            _buildProgressSection(),
            const SizedBox(height: 20),
            _buildInitializationStepsList(),
            const Spacer(),
            _buildAkButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return const Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'البيانات التجريبية',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'تسمح لك هذه الشاشة بتهيئة بيانات تجريبية للتطبيق، مثل الفئات والمنتجات والعملاء والموردين وشجرة الحسابات المحاسبية.',
              style: AppTypography(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              'ملاحظة: سيتم تخطي تهيئة أي بيانات موجودة بالفعل في قاعدة البيانات.',
              style: AppTypography(
                fontSize: 14,
                fontStyle: FontStyle.italic,
                color: AppColors.lightTextSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التقدم: ${(_progress * 100).toStringAsFixed(0)}%',
          style: const AppTypography(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: _progress,
          backgroundColor: AppColors.lightSurfaceVariant,
          valueColor: const AlwaysStoppedAnimation<Color>(
            AppColors.primary,
          ),
          minHeight: 10,
          borderRadius: BorderRadius.circular(5),
        ),
        const SizedBox(height: 8),
        Text(
          _statusMessage,
          style: const AppTypography(
            fontSize: 14,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildInitializationStepsList() {
    return Expanded(
      child: Card(
        elevation: 2,
        child: ListView.builder(
          itemCount: _initializationSteps.length,
          itemBuilder: (context, index) {
            final step = _initializationSteps[index];
            return ListTile(
              leading: _buildStepIcon(step),
              title: Text(step['name']),
              trailing: step['hasError']
                  ? const Icon(Icons.error, color: AppColors.error)
                  : null,
            );
          },
        ),
      ),
    );
  }

  Widget _buildStepIcon(Map<String, dynamic> step) {
    if (step['isCompleted']) {
      return const Icon(Icons.check_circle, color: AppColors.success);
    } else if (step['hasError']) {
      return const Icon(Icons.error, color: AppColors.error);
    } else {
      return const Icon(Icons.circle_outlined,
          color: AppColors.lightTextSecondary);
    }
  }

  Widget _buildAkButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _initializeSampleData,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        backgroundColor: _isInitialized ? AppColors.success : null,
      ),
      child: _isLoading
          ? const AkLoadingIndicator(color: AppColors.onPrimary)
          : Text(
              _isInitialized
                  ? 'تم تهيئة البيانات بنجاح'
                  : 'تهيئة البيانات التجريبية',
              style: const AppTypography(fontSize: 16),
            ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مساعدة'),
        content: const Text(
          'تسمح لك هذه الشاشة بتهيئة بيانات تجريبية للتطبيق. '
          'هذه البيانات مفيدة للاختبار والتجربة قبل إدخال البيانات الفعلية.\n\n'
          'تشمل البيانات التجريبية:\n'
          '- فئات المنتجات والمصروفات\n'
          '- وحدات القياس\n'
          '- منتجات تجريبية\n'
          '- عملاء وموردين\n'
          '- مستودعات\n'
          '- شجرة الحسابات المحاسبية\n\n'
          'ملاحظة: لن يتم استبدال أي بيانات موجودة بالفعل في قاعدة البيانات.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Future<void> _initializeSampleData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري تهيئة البيانات التجريبية...';
      _progress = 0.0;
      // إعادة تعيين حالة الخطوات
      for (final step in _initializationSteps) {
        step['isCompleted'] = false;
        step['hasError'] = false;
      }
    });

    try {
      final sampleDataInitializer = SampleDataInitializer();

      // تهيئة البيانات التجريبية خطوة بخطوة
      await _executeInitializationStep(
          0, () => sampleDataInitializer.createSampleCategories());
      await _executeInitializationStep(
          1, () => sampleDataInitializer.createSampleUnits());
      await _executeInitializationStep(
          2, () => sampleDataInitializer.createSampleProducts());
      await _executeInitializationStep(
          3, () => sampleDataInitializer.createSampleCustomers());
      await _executeInitializationStep(
          4, () => sampleDataInitializer.createSampleSuppliers());
      await _executeInitializationStep(
          5, () => sampleDataInitializer.createSampleWarehouses());
      await _executeInitializationStep(
          6, () => sampleDataInitializer.initializeAccountingChartOfAccounts());

      setState(() {
        _isLoading = false;
        _isInitialized = true;
        _statusMessage = 'تم تهيئة البيانات التجريبية بنجاح';
        _progress = 1.0;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تهيئة البيانات التجريبية بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تهيئة البيانات التجريبية',
        error: e,
        stackTrace: stackTrace,
      );

      setState(() {
        _isLoading = false;
        _statusMessage = 'فشل في تهيئة البيانات التجريبية: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تهيئة البيانات التجريبية: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _executeInitializationStep(
      int stepIndex, Future<dynamic> Function() stepFunction) async {
    final step = _initializationSteps[stepIndex];

    setState(() {
      _statusMessage = 'جاري ${step['name']}...';
      _progress = stepIndex / _initializationSteps.length;
    });

    try {
      await stepFunction();
      setState(() {
        step['isCompleted'] = true;
      });
    } catch (e) {
      AppLogger.error('خطأ في ${step['name']}: $e');
      setState(() {
        step['hasError'] = true;
      });
      rethrow;
    }
  }
}
