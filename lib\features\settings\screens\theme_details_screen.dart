import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';

/// شاشة تفاصيل الثيم - عرض شامل لجميع العناصر والطبقات
/// 🎨 عرض جميع مكونات الثيم بالتفصيل
/// 🔍 فحص التوافق والإمكانية الوصول
/// 📊 إحصائيات وتحليل الثيم
/// 🎭 معاينة جميع الحالات والتفاعلات
class ThemeDetailsScreen extends StatefulWidget {
  const ThemeDetailsScreen({Key? key}) : super(key: key);

  @override
  State<ThemeDetailsScreen> createState() => _ThemeDetailsScreenState();
}

class _ThemeDetailsScreenState extends State<ThemeDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ThemeManager _themeManager;
  Map<String, dynamic>? _themeInfo;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadThemeDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadThemeDetails() {
    _themeManager =
        AppProviders.getLazyPresenter<ThemeManager>(() => ThemeManager());
    _themeInfo = _themeManager.getCurrentThemeInfo();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'تفاصيل الثيم',
        showBackButton: true,
        backgroundColor: DynamicColors.surface(context),
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: DynamicColors.textSecondary(context),
          indicatorColor: AppColors.primary,
          tabs: const [
            Tab(
              icon: Icon(
                Icons.palette,
                size: AppDimensions.iconSizeMedium,
              ),
              text: 'الألوان',
            ),
            Tab(
              icon: Icon(
                Icons.text_fields,
                size: AppDimensions.iconSizeMedium,
              ),
              text: 'النصوص',
            ),
            Tab(
              icon: Icon(
                Icons.smart_button,
                size: AppDimensions.iconSizeMedium,
              ),
              text: 'المكونات',
            ),
            Tab(
              icon: Icon(
                Icons.analytics,
                size: AppDimensions.iconSizeMedium,
              ),
              text: 'التحليل',
            ),
          ],
        ),
      ),
      backgroundColor: DynamicColors.background(context),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildColorsTab(),
          _buildTextTab(),
          _buildComponentsTab(),
          _buildAnalysisTab(),
        ],
      ),
    );
  }

  Widget _buildColorsTab() {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: AppDimensions.defaultPadding,
      child: AkColumn(
        spacing: AkSpacingSize.large,
        children: [
          _buildSimpleColorSection('الألوان الأساسية', [
            ('أساسي', theme.colorScheme.primary),
            ('ثانوي', theme.colorScheme.secondary),
            ('سطح', theme.colorScheme.surface),
            ('خلفية', theme.colorScheme.surface),
          ]),
          _buildSimpleColorSection('ألوان الحالة', [
            ('خطأ', theme.colorScheme.error),
            ('نجاح', AppColors.success),
            ('تحذير', AppColors.warning),
            ('معلومات', AppColors.info),
          ]),
        ],
      ),
    );
  }

  Widget _buildSimpleColorSection(String title, List<(String, Color)> colors) {
    return AkColumn(
      spacing: AkSpacingSize.medium,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: DynamicColors.textPrimary(context),
                fontSize: AppDimensions.titleFontSize,
              ),
        ),
        Wrap(
          spacing: AppDimensions.spacing8,
          runSpacing: AppDimensions.spacing8,
          children: colors
              .map((colorData) => _buildColorCard(colorData.$1, colorData.$2))
              .toList(),
        ),
      ],
    );
  }

  Widget _buildColorCard(String label, Color color) {
    final luminance = color.computeLuminance();
    final textColor = luminance > 0.5
        ? AppColors.lightTextPrimary
        : AppColors.darkTextPrimary;

    return Container(
      width: 120,
      height: 80,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(
          color: DynamicColors.border(context).withValues(alpha: 0.2),
        ),
      ),
      child: AkColumn(
        spacing: AkSpacingSize.tiny,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: textColor,
                  fontWeight: FontWeight.bold,
                  fontSize: AppDimensions.mediumFontSize,
                ),
          ),
          Text(
            '#${color.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: textColor.withValues(alpha: 0.8),
                  fontFamily: 'monospace',
                  fontSize: AppDimensions.smallFontSize,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextTab() {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: AppDimensions.defaultPadding,
      child: AkColumn(
        spacing: AkSpacingSize.large,
        children: [
          _buildTextColorSection('النصوص الأساسية', [
            ('نص أساسي', theme.colorScheme.onSurface),
            ('نص ثانوي', theme.colorScheme.onSurface.withValues(alpha: 0.7)),
            ('نص مساعد', theme.colorScheme.onSurface.withValues(alpha: 0.5)),
            ('نص معطل', theme.colorScheme.onSurface.withValues(alpha: 0.3)),
          ]),
          _buildTextColorSection('النصوص الخاصة', [
            ('رابط', AppColors.info),
            ('خطأ', theme.colorScheme.error),
            ('نجاح', AppColors.success),
            ('تحذير', AppColors.warning),
          ]),
          _buildTextSamplesSection(),
        ],
      ),
    );
  }

  Widget _buildTextColorSection(String title, List<(String, Color)> colors) {
    return AkColumn(
      spacing: AkSpacingSize.medium,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: DynamicColors.textPrimary(context),
                fontSize: AppDimensions.titleFontSize,
              ),
        ),
        ...colors.map((colorData) => Padding(
              padding: const EdgeInsets.only(bottom: AppDimensions.spacing8),
              child: _buildTextColorRow(colorData.$1, colorData.$2),
            )),
      ],
    );
  }

  Widget _buildTextColorRow(String label, Color color) {
    return AkCard(
      padding: AppDimensions.defaultPadding,
      child: AkRow(
        spacing: AkSpacingSize.medium,
        children: [
          Container(
            width: AppDimensions.iconSizeSmall,
            height: AppDimensions.iconSizeSmall,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: DynamicColors.border(context).withValues(alpha: 0.3),
              ),
            ),
          ),
          Expanded(
            child: AkColumn(
              spacing: AkSpacingSize.tiny,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: DynamicColors.textPrimary(context),
                        fontSize: AppDimensions.mediumFontSize,
                      ),
                ),
                Text(
                  '#${color.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                        color: DynamicColors.textSecondary(context),
                        fontSize: AppDimensions.smallFontSize,
                      ),
                ),
              ],
            ),
          ),
          Text(
            'نص تجريبي',
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: AppDimensions.mediumFontSize,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextSamplesSection() {
    return AkColumn(
      spacing: AkSpacingSize.medium,
      children: [
        Text(
          'عينات النصوص',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: DynamicColors.textPrimary(context),
                fontSize: AppDimensions.titleFontSize,
              ),
        ),
        AkCard(
          padding: AppDimensions.defaultPadding,
          child: AkColumn(
            spacing: AkSpacingSize.small,
            children: [
              Text(
                'عنوان كبير',
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      color: DynamicColors.textPrimary(context),
                      fontSize: AppDimensions.headingFontSize,
                    ),
              ),
              Text(
                'عنوان متوسط',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: DynamicColors.textPrimary(context),
                      fontSize: AppDimensions.titleFontSize,
                    ),
              ),
              Text(
                'عنوان صغير',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: DynamicColors.textPrimary(context),
                      fontSize: AppDimensions.largeFontSize,
                    ),
              ),
              Text(
                'نص أساسي',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: DynamicColors.textPrimary(context),
                      fontSize: AppDimensions.mediumFontSize,
                    ),
              ),
              Text(
                'نص ثانوي',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: DynamicColors.textSecondary(context),
                      fontSize: AppDimensions.defaultFontSize,
                    ),
              ),
              Text(
                'نص صغير',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: DynamicColors.textSecondary(context),
                      fontSize: AppDimensions.smallFontSize,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildComponentsTab() {
    return SingleChildScrollView(
      padding: AppDimensions.defaultPadding,
      child: AkColumn(
        spacing: AkSpacingSize.large,
        children: [
          _buildButtonsSection(),
          _buildInputsSection(),
          _buildCardsSection(),
        ],
      ),
    );
  }

  Widget _buildButtonsSection() {
    return AkColumn(
      spacing: AkSpacingSize.medium,
      children: [
        Text(
          'الأزرار',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: DynamicColors.textPrimary(context),
                fontSize: AppDimensions.titleFontSize,
              ),
        ),
        Wrap(
          spacing: AppDimensions.spacing12,
          runSpacing: AppDimensions.spacing12,
          children: [
            AkButton(
              text: 'زر أساسي',
              icon: Icons.check,
              onPressed: () {},
              type: AkButtonType.primary,
            ),
            AkButton(
              text: 'زر ثانوي',
              icon: Icons.edit,
              onPressed: () {},
              type: AkButtonType.secondary,
            ),
            AkTextButton(
              text: 'زر نصي',
              icon: Icons.info,
              onPressed: () {},
              type: AkButtonType.info,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInputsSection() {
    return AkColumn(
      spacing: AkSpacingSize.medium,
      children: [
        Text(
          'حقول الإدخال',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: DynamicColors.textPrimary(context),
                fontSize: AppDimensions.titleFontSize,
              ),
        ),
        const AkTextInput(
          label: 'حقل نصي عادي',
          hint: 'أدخل النص هنا',
          prefixIcon: Icons.text_fields,
        ),
        const AkPasswordInput(
          label: 'حقل كلمة المرور',
          hint: 'أدخل كلمة المرور',
          showStrengthIndicator: false,
        ),
      ],
    );
  }

  Widget _buildCardsSection() {
    return AkColumn(
      spacing: AkSpacingSize.medium,
      children: [
        Text(
          'البطاقات',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: DynamicColors.textPrimary(context),
                fontSize: AppDimensions.titleFontSize,
              ),
        ),
        const AkInfoCard(
          title: 'بطاقة ذكية',
          subtitle:
              'هذه بطاقة ذكية تتكيف مع الثيم الحالي وتضمن التوافق اللوني المثالي.',
          icon: Icons.info_outline,
        ),
      ],
    );
  }

  Widget _buildAnalysisTab() {
    if (_themeInfo == null) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primary,
        ),
      );
    }

    return SingleChildScrollView(
      padding: AppDimensions.defaultPadding,
      child: AkColumn(
        spacing: AkSpacingSize.large,
        children: [
          _buildThemeInfoCard(),
          _buildSimpleAnalysisCard(),
        ],
      ),
    );
  }

  Widget _buildThemeInfoCard() {
    return AkCard(
      padding: AppDimensions.defaultPadding,
      child: AkColumn(
        spacing: AkSpacingSize.medium,
        children: [
          Text(
            'معلومات الثيم',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: DynamicColors.textPrimary(context),
                  fontSize: AppDimensions.titleFontSize,
                ),
          ),
          _buildInfoRow('اسم الثيم', _themeInfo!['themeName'] ?? 'غير محدد'),
          _buildInfoRow('مفتاح الثيم', _themeInfo!['themeKey'] ?? 'غير محدد'),
          _buildInfoRow(
              'اللون الأساسي', _themeInfo!['primaryColor'] ?? 'غير محدد'),
          _buildInfoRow('الوضع الداكن',
              _themeInfo!['isDarkMode'] == true ? 'مفعل' : 'معطل'),
        ],
      ),
    );
  }

  Widget _buildSimpleAnalysisCard() {
    return AkCard(
      padding: AppDimensions.defaultPadding,
      child: AkColumn(
        spacing: AkSpacingSize.medium,
        children: [
          Text(
            'تحليل الثيم',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: DynamicColors.textPrimary(context),
                  fontSize: AppDimensions.titleFontSize,
                ),
          ),
          _buildInfoRow(
              'نوع الثيم', _themeManager.isDarkMode ? 'داكن' : 'فاتح'),
          _buildInfoRow('اللون الأساسي', _themeManager.colorThemeName),
          _buildInfoRow(
              'الوضع', _themeManager.isSystemMode ? 'تلقائي' : 'يدوي'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.spacing8),
      child: AkRow(
        spacing: AkSpacingSize.medium,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: DynamicColors.textPrimary(context),
                    fontSize: AppDimensions.mediumFontSize,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DynamicColors.textSecondary(context),
                    fontSize: AppDimensions.defaultFontSize,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
