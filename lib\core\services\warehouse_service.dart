import '../database/database_service.dart';
import '../models/warehouse.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';

/// خدمة للعمليات المتعلقة بالمستودعات
class WarehouseService {
  // نمط Singleton
  static final WarehouseService _instance = WarehouseService._internal();
  factory WarehouseService() => _instance;
  WarehouseService._internal();

  final DatabaseService _db = DatabaseService.instance;

  /// الحصول على جميع المستودعات
  Future<List<Warehouse>> getAllWarehouses({
    bool includeInactive = false,
    String? searchQuery,
  }) async {
    try {
      AppLogger.info('الحصول على جميع المستودعات');

      // بناء شرط WHERE
      String whereClause = 'w.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (!includeInactive) {
        whereClause += ' AND w.is_active = 1';
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause += ' AND (w.name LIKE ? OR w.code LIKE ?)';
        whereArgs.addAll(['%$searchQuery%', '%$searchQuery%']);
      }

      // استعلام قاعدة البيانات مع JOIN للحصول على اسم المدير
      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          w.*,
          u.full_name as manager_name
        FROM ${DatabaseService.tableWarehouses} w
        LEFT JOIN ${DatabaseService.tableUsers} u ON w.manager_id = u.id
        WHERE $whereClause
        ORDER BY w.is_default DESC, w.name ASC
      ''', whereArgs);

      // تحويل إلى كائنات Warehouse
      return List.generate(maps.length, (i) {
        return Warehouse.fromMap(maps[i]);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع المستودعات',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على مستودع بواسطة المعرف
  Future<Warehouse?> getWarehouseById(String id) async {
    try {
      AppLogger.info('الحصول على مستودع بواسطة المعرف: $id');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          w.*,
          u.full_name as manager_name
        FROM ${DatabaseService.tableWarehouses} w
        LEFT JOIN ${DatabaseService.tableUsers} u ON w.manager_id = u.id
        WHERE w.id = ? AND w.is_deleted = 0
      ''', [id]);

      if (maps.isEmpty) {
        return null;
      }

      return Warehouse.fromMap(maps.first);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على مستودع بواسطة المعرف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return null;
    }
  }

  /// الحصول على المستودع الافتراضي
  Future<Warehouse?> getDefaultWarehouse() async {
    try {
      AppLogger.info('الحصول على المستودع الافتراضي');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          w.*,
          u.full_name as manager_name
        FROM ${DatabaseService.tableWarehouses} w
        LEFT JOIN ${DatabaseService.tableUsers} u ON w.manager_id = u.id
        WHERE w.is_default = 1 AND w.is_deleted = 0 AND w.is_active = 1
        LIMIT 1
      ''');

      if (maps.isEmpty) {
        // إذا لم يتم العثور على مستودع افتراضي، نحاول الحصول على أول مستودع نشط
        final List<Map<String, dynamic>> activeMaps = await _db.rawQuery('''
          SELECT
            w.*,
            u.full_name as manager_name
          FROM ${DatabaseService.tableWarehouses} w
          LEFT JOIN ${DatabaseService.tableUsers} u ON w.manager_id = u.id
          WHERE w.is_deleted = 0 AND w.is_active = 1
          ORDER BY w.name ASC
          LIMIT 1
        ''');

        if (activeMaps.isEmpty) {
          return null;
        }

        return Warehouse.fromMap(activeMaps.first);
      }

      return Warehouse.fromMap(maps.first);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على المستودع الافتراضي',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// إضافة مستودع جديد
  Future<bool> addWarehouse(Warehouse warehouse, {String? userId}) async {
    try {
      AppLogger.info('إضافة مستودع جديد: ${warehouse.name}');

      // التحقق من عدم وجود مستودع بنفس الكود
      final existingWarehouse = await _getWarehouseByCode(warehouse.code ?? '');
      if (existingWarehouse != null) {
        AppLogger.warning('كود المستودع موجود بالفعل: ${warehouse.code}');
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        final warehouseMap = warehouse.toMap();

        // تعيين created_by إذا تم توفيره
        if (userId != null) {
          warehouseMap['created_by'] = userId;
        }

        // إذا كان هذا المستودع افتراضيًا، نقوم بإلغاء تعيين المستودع الافتراضي السابق
        if (warehouse.isDefault) {
          await txn.update(
            DatabaseService.tableWarehouses,
            {'is_default': 0},
            where: 'is_default = 1',
          );
        }

        // إضافة المستودع
        await txn.insert(DatabaseService.tableWarehouses, warehouseMap);

        // إذا لم يكن هناك مستودعات أخرى، نجعل هذا المستودع افتراضيًا
        if (!warehouse.isDefault) {
          final count = await _getWarehousesCount(txn);
          if (count == 1) {
            await txn.update(
              DatabaseService.tableWarehouses,
              {'is_default': 1},
              where: 'id = ?',
              whereArgs: [warehouse.id],
            );
          }
        }

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة مستودع',
        error: e,
        stackTrace: stackTrace,
        context: {'warehouse': warehouse.toString()},
      );
      return false;
    }
  }

  /// تحديث مستودع موجود
  Future<bool> updateWarehouse(Warehouse warehouse, {String? userId}) async {
    try {
      AppLogger.info('تحديث مستودع: ${warehouse.name}');

      // التحقق من عدم وجود مستودع آخر بنفس الكود
      final existingWarehouse = await _getWarehouseByCode(warehouse.code ?? '');
      if (existingWarehouse != null && existingWarehouse.id != warehouse.id) {
        AppLogger.warning('كود المستودع موجود بالفعل: ${warehouse.code}');
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        final warehouseMap = warehouse.toMap();

        // تعيين updated_at و updated_by
        warehouseMap['updated_at'] = DateTime.now().toIso8601String();
        if (userId != null) {
          warehouseMap['updated_by'] = userId;
        }

        // إذا كان هذا المستودع افتراضيًا، نقوم بإلغاء تعيين المستودع الافتراضي السابق
        if (warehouse.isDefault) {
          await txn.update(
            DatabaseService.tableWarehouses,
            {'is_default': 0},
            where: 'is_default = 1 AND id != ?',
            whereArgs: [warehouse.id],
          );
        } else {
          // التأكد من وجود مستودع افتراضي آخر قبل إلغاء تعيين هذا المستودع كافتراضي
          final defaultCount =
              await _getDefaultWarehousesCount(txn, excludeId: warehouse.id);
          if (defaultCount == 0) {
            // لا يمكن إلغاء تعيين المستودع الافتراضي الوحيد
            warehouseMap['is_default'] = 1;
          }
        }

        // تحديث المستودع
        await txn.update(
          DatabaseService.tableWarehouses,
          warehouseMap,
          where: 'id = ?',
          whereArgs: [warehouse.id],
        );

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث مستودع',
        error: e,
        stackTrace: stackTrace,
        context: {'warehouse': warehouse.toString()},
      );
      return false;
    }
  }

  /// حذف مستودع (حذف منطقي)
  Future<bool> deleteWarehouse(String id, {String? userId}) async {
    try {
      AppLogger.info('حذف مستودع: $id');

      // التحقق من عدم وجود مخزون في المستودع
      final inventoryCount = await _getInventoryCountByWarehouse(id);
      if (inventoryCount > 0) {
        AppLogger.warning('لا يمكن حذف المستودع لأنه يحتوي على مخزون');
        return false;
      }

      // التحقق من أن المستودع ليس افتراضيًا
      final warehouse = await getWarehouseById(id);
      if (warehouse == null) {
        return false;
      }

      if (warehouse.isDefault) {
        // التحقق من وجود مستودع آخر يمكن تعيينه كافتراضي
        final otherWarehouses = await getAllWarehouses();
        if (otherWarehouses.length <= 1) {
          AppLogger.warning('لا يمكن حذف المستودع الافتراضي الوحيد');
          return false;
        }
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        final now = DateTime.now().toIso8601String();

        // إذا كان المستودع افتراضيًا، نقوم بتعيين مستودع آخر كافتراضي
        if (warehouse.isDefault) {
          // الحصول على مستودع آخر نشط
          final List<Map<String, dynamic>> otherWarehouseMaps = await txn.query(
            DatabaseService.tableWarehouses,
            where: 'id != ? AND is_deleted = 0 AND is_active = 1',
            whereArgs: [id],
            orderBy: 'name ASC',
            limit: 1,
          );

          if (otherWarehouseMaps.isNotEmpty) {
            final otherWarehouseId = otherWarehouseMaps.first['id'];
            await txn.update(
              DatabaseService.tableWarehouses,
              {'is_default': 1, 'updated_at': now},
              where: 'id = ?',
              whereArgs: [otherWarehouseId],
            );
          }
        }

        // حذف المستودع (حذف منطقي)
        await txn.update(
          DatabaseService.tableWarehouses,
          {
            'is_deleted': 1,
            'is_active': 0,
            'is_default': 0,
            'updated_at': now,
            'updated_by': userId,
          },
          where: 'id = ?',
          whereArgs: [id],
        );

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف مستودع',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// إنشاء المستودع الافتراضي إذا لم يكن موجودًا (تم تعطيله - يتم في BasicDataInitializer)
  Future<bool> createDefaultWarehouseIfNotExists({String? userId}) async {
    AppLogger.info('تم تعطيل إنشاء المستودع الافتراضي في WarehouseService');
    AppLogger.info('يتم تهيئة المستودعات في BasicDataInitializer فقط');

    // لا نقوم بأي عملية هنا لتجنب التداخل مع BasicDataInitializer
    return true;
  }

  /// الحصول على مستودع بواسطة الكود
  Future<Warehouse?> _getWarehouseByCode(String code) async {
    try {
      final List<Map<String, dynamic>> maps = await _db.query(
        DatabaseService.tableWarehouses,
        where: 'code = ? AND is_deleted = 0',
        whereArgs: [code],
      );

      if (maps.isEmpty) {
        return null;
      }

      return Warehouse.fromMap(maps.first);
    } catch (e) {
      AppLogger.error('فشل في الحصول على مستودع بواسطة الكود: $e');
      return null;
    }
  }

  /// الحصول على عدد المستودعات
  Future<int> _getWarehousesCount(dynamic db) async {
    try {
      final result = db != null
          ? await db.rawQuery(
              'SELECT COUNT(*) as count FROM ${DatabaseService.tableWarehouses} WHERE is_deleted = 0',
            )
          : await _db.rawQuery(
              'SELECT COUNT(*) as count FROM ${DatabaseService.tableWarehouses} WHERE is_deleted = 0',
            );

      return result.first['count'] as int;
    } catch (e) {
      AppLogger.error('فشل في الحصول على عدد المستودعات: $e');
      return 0;
    }
  }

  /// الحصول على عدد المستودعات الافتراضية (باستثناء مستودع معين)
  Future<int> _getDefaultWarehousesCount(dynamic db,
      {String? excludeId}) async {
    try {
      String whereClause = 'is_default = 1 AND is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (excludeId != null) {
        whereClause += ' AND id != ?';
        whereArgs.add(excludeId);
      }

      final result = db != null
          ? await db.rawQuery(
              'SELECT COUNT(*) as count FROM ${DatabaseService.tableWarehouses} WHERE $whereClause',
              whereArgs,
            )
          : await _db.rawQuery(
              'SELECT COUNT(*) as count FROM ${DatabaseService.tableWarehouses} WHERE $whereClause',
              whereArgs,
            );

      return result.first['count'] as int;
    } catch (e) {
      AppLogger.error('فشل في الحصول على عدد المستودعات الافتراضية: $e');
      return 0;
    }
  }

  /// الحصول على عدد المخزون في مستودع معين
  Future<int> _getInventoryCountByWarehouse(String warehouseId) async {
    try {
      final result = await _db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseService.tableInventory} WHERE warehouse_id = ? AND is_deleted = 0',
        [warehouseId],
      );

      return result.first['count'] as int;
    } catch (e) {
      AppLogger.error('فشل في الحصول على عدد المخزون في المستودع: $e');
      return 0;
    }
  }
}
