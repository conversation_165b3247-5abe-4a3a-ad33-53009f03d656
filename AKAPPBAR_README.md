# 📱 نظام شريط التطبيق الموحد (AK AppBar System)

نظام شامل وموحد لجميع أشرطة التطبيق في تطبيق تاجر بلس، مصمم خصيصاً للمشاريع التجارية اليمنية.

## 🎯 **المميزات الرئيسية**

- ✅ **تصميم موحد ومتناسق** لجميع أشرطة التطبيق
- ✅ **دعم كامل للوضع المظلم/الفاتح** باستخدام `Theme.of(context).brightness`
- ✅ **عدم وجود قيم صريحة** - استخدام `AppColors.*`, `AppDimensions.*`, `AppTypography.*` فقط
- ✅ **تأثيرات تفاعلية متقدمة** مع رسوم متحركة سلسة
- ✅ **دوال مساعدة سريعة** للاستخدام المباشر
- ✅ **تعليقات شاملة باللغة العربية**
- ✅ **أحجام متعددة** (صغير، متوسط، كبير)
- ✅ **دوال متخصصة للمشروع التجاري**

## 📋 **أشرطة التطبيق المتوفرة**

### 📱 **1. شريط التطبيق الأساسي (AkAppBar)**
شريط التطبيق الأساسي الموحد مع تصميم متناسق وألوان ذكية.

### 🔍 **2. شريط التطبيق مع البحث (AkSearchAppBar)**
شريط التطبيق مع البحث المدمج والتأثيرات الانتقالية المتقدمة.

## 🚀 **أمثلة الاستخدام**

### **شريط التطبيق الأساسي:**
```dart
// شريط تطبيق بسيط
AkAppBar(
  title: 'إدارة المنتجات',
  showBackButton: true,
  actions: [
    IconButton(icon: Icon(Icons.search), onPressed: () {}),
    IconButton(icon: Icon(Icons.more_vert), onPressed: () {}),
  ],
)
```

### **شريط التطبيق مع البحث:**
```dart
// شريط تطبيق مع بحث متقدم
AkSearchAppBar(
  title: 'البحث في المنتجات',
  searchHint: 'ابحث عن منتج بالاسم أو الباركود...',
  onSearch: (query) => searchProducts(query),
  searchDelay: 300, // تأخير البحث بالميلي ثانية
)
```

### **شريط التطبيق مع أحجام مختلفة:**
```dart
// شريط تطبيق كبير لنقطة البيع
AkAppBar(
  title: 'نقطة البيع',
  size: AkAppBarSize.large,
  actions: [
    IconButton(icon: Icon(Icons.people), onPressed: () {}),
    IconButton(icon: Icon(Icons.settings), onPressed: () {}),
  ],
)

// شريط تطبيق صغير للمناطق المحدودة
AkAppBar(
  title: 'الإعدادات',
  size: AkAppBarSize.small,
  showBackButton: true,
)
```

## 🎨 **أنواع أشرطة التطبيق**

| النوع | الوصف | الاستخدام |
|-------|--------|-----------|
| `normal` | شريط تطبيق عادي | الاستخدام العام |
| `search` | مع بحث مدمج | صفحات البحث |
| `tabbed` | مع تبويبات | التنقل بين الأقسام |
| `sliver` | قابل للطي | القوائم الطويلة |

## 📏 **أحجام أشرطة التطبيق**

| الحجم | الارتفاع | الاستخدام |
|-------|----------|-----------|
| `small` | 48px | مناطق محدودة |
| `medium` | 56px | الاستخدام العادي |
| `large` | 72px | شاشات رئيسية |

## 🛠️ **الدوال المساعدة المتوفرة**

### **دوال أشرطة التطبيق الأساسية:**
- `AkAppBars.dashboard()` - شريط لوحة التحكم
- `AkAppBars.products()` - شريط إدارة المنتجات
- `AkAppBars.sales()` - شريط المبيعات
- `AkAppBars.pos()` - شريط نقطة البيع
- `AkAppBars.reports()` - شريط التقارير
- `AkAppBars.settings()` - شريط الإعدادات
- `AkAppBars.profile()` - شريط الملف الشخصي

### **دوال أشرطة البحث:**
- `AkAppBars.productSearch()` - البحث في المنتجات
- `AkAppBars.customerSearch()` - البحث في العملاء

### **دوال متخصصة للمشروع التجاري:**
- `AkAppBars.inventory()` - شريط إدارة المخزون
- `AkAppBars.customers()` - شريط إدارة العملاء
- `AkAppBars.purchases()` - شريط المشتريات

## 🎯 **أمثلة متقدمة**

### **شريط لوحة التحكم:**
```dart
AkAppBars.dashboard(
  onMenuPressed: () => openDrawer(),
  actions: [
    IconButton(
      icon: Badge(
        label: Text('5'),
        child: Icon(Icons.notifications),
      ),
      onPressed: () => openNotifications(),
    ),
  ],
)
```

### **شريط إدارة المنتجات:**
```dart
AkAppBars.products(
  onBackPressed: () => Navigator.pop(context),
  onAddProduct: () => Navigator.push(context, AddProductPage()),
  onSearch: () => Navigator.push(context, ProductSearchPage()),
)
```

### **شريط نقطة البيع:**
```dart
AkAppBars.pos(
  onCustomers: () => openCustomersList(),
  onSettings: () => openPOSSettings(),
)
```

### **شريط البحث في المنتجات:**
```dart
AkAppBars.productSearch(
  onSearch: (query) {
    // البحث الفوري في المنتجات
    productBloc.add(SearchProducts(query));
  },
  onBackPressed: () => Navigator.pop(context),
)
```

### **شريط إدارة المخزون:**
```dart
AkAppBars.inventory(
  onLowStock: () => showLowStockProducts(),
  onAddStock: () => openAddStockDialog(),
)
```

## 🎨 **التخصيص المتقدم**

### **ألوان مخصصة:**
```dart
AkAppBar(
  title: 'شريط مخصص',
  backgroundColor: AppColors.success,
  foregroundColor: AppColors.onPrimary,
  actions: [...],
)
```

### **شريط مع تبويبات:**
```dart
AkAppBar(
  title: 'التقارير',
  bottom: TabBar(
    tabs: [
      Tab(text: 'المبيعات'),
      Tab(text: 'المشتريات'),
      Tab(text: 'الأرباح'),
    ],
  ),
)
```

### **بحث مع إعدادات مخصصة:**
```dart
AkSearchAppBar(
  title: 'البحث المتقدم',
  searchHint: 'ابحث بالاسم، الباركود، أو الفئة...',
  searchDelay: 500, // تأخير نصف ثانية
  initialSearchActive: true, // البحث نشط من البداية
  onSearch: (query) => performAdvancedSearch(query),
)
```

## 📱 **التوافق والاستجابة**

- ✅ **متوافق مع جميع أحجام الشاشات**
- ✅ **يتكيف مع اتجاه الشاشة**
- ✅ **يدعم الخطوط العربية**
- ✅ **متوافق مع إعدادات إمكانية الوصول**
- ✅ **دعم شريط الحالة الذكي**

## 🎨 **الدعم للوضع المظلم/الفاتح**

النظام يتكيف تلقائياً مع وضع التطبيق:

```dart
// يتم التحقق تلقائياً من الوضع
final isDark = Theme.of(context).brightness == Brightness.dark;

// الألوان تتغير تلقائياً
backgroundColor: isDark ? AppColors.darkSurface : AppColors.lightSurface,
foregroundColor: AppColors.getTextColorForBackground(backgroundColor),
```

## 🔄 **الترقية من الأنظمة القديمة**

### **من CustomAppBar:**
```dart
// ❌ القديم
CustomAppBar(
  title: 'المنتجات',
  showBackButton: true,
  actions: [...],
)

// ✅ الجديد
AkAppBar(
  title: 'المنتجات',
  showBackButton: true,
  actions: [...],
)
```

### **من UnifiedAppBar:**
```dart
// ❌ القديم
UnifiedAppBar(
  title: 'لوحة التحكم',
  showBackButton: false,
  onMenuPressed: () => openMenu(),
)

// ✅ الجديد
AkAppBars.dashboard(
  onMenuPressed: () => openMenu(),
)
```

## 🔧 **التكامل مع الأنظمة الأخرى**

### **مع نظام التنقل:**
```dart
AkAppBar(
  title: 'المنتجات',
  bottom: AkTabBar(
    tabs: ['جميع المنتجات', 'مخزون منخفض'],
    onTabChanged: (index) => handleTabChange(index),
  ),
)
```

### **مع نظام الحوارات:**
```dart
AkAppBars.products(
  onAddProduct: () {
    AkDialogs.confirmSave(
      context: context,
      onConfirm: () => addNewProduct(),
    );
  },
)
```

## 📊 **الإحصائيات**

- **عدد أشرطة التطبيق**: 2 نوع أساسي
- **عدد الدوال المساعدة**: 13 دالة
- **عدد الأنواع**: 4 أنواع مختلفة
- **عدد الأحجام**: 3 أحجام
- **الدعم للغات**: العربية (أساسي)
- **حجم الملف**: ~877 سطر
- **التبعيات**: لا توجد تبعيات خارجية

---

**تم تطوير هذا النظام خصيصاً لتطبيق تاجر بلس - نظام إدارة المبيعات اليمني** 🇾🇪
