import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/utils/index.dart';
import '../../../core/widgets/index.dart';
import '../../../core/models/product.dart';
import '../presenters/product_presenter.dart';
import '../../warehouses/presenters/warehouse_presenter.dart';
import '../../../core/theme/index.dart';

class ProductOpeningBalanceScreen extends StatefulWidget {
  const ProductOpeningBalanceScreen({Key? key}) : super(key: key);

  @override
  State<ProductOpeningBalanceScreen> createState() =>
      _ProductOpeningBalanceScreenState();
}

class _ProductOpeningBalanceScreenState
    extends State<ProductOpeningBalanceScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _costPriceController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  String? _selectedWarehouseId;
  Product? _selectedProduct;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  bool _isSaving = false;
  List<Product> _filteredProducts = [];
  List<Map<String, dynamic>> _openingBalances = [];
  bool _showSearchField = false;

  // استخدام التحميل الكسول
  late final WarehousePresenter _warehousePresenter;
  late final ProductPresenter _productPresenter;

  @override
  void initState() {
    super.initState();
    _warehousePresenter = AppProviders.getLazyPresenter<WarehousePresenter>(
        () => WarehousePresenter());
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
        () => ProductPresenter());
    _loadData();
    _searchController.addListener(_filterProducts);
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل المخازن
      await _warehousePresenter.loadWarehouses();

      // تعيين المخزن الافتراضي
      if (_warehousePresenter.warehouses.isNotEmpty &&
          _selectedWarehouseId == null) {
        setState(() {
          _selectedWarehouseId = _warehousePresenter.warehouses.first.id;
        });
      }

      // تحميل المنتجات
      if (!mounted) return;

      await _productPresenter.loadProducts();

      if (!mounted) return;
      setState(() {
        _filteredProducts = _productPresenter.products;
      });

      // تحميل أرصدة أول المدة
      _loadOpeningBalances();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل البيانات: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _filterProducts() {
    final query = _searchController.text.toLowerCase();

    if (query.isEmpty) {
      setState(() {
        _filteredProducts = _productPresenter.products;
      });
      return;
    }

    setState(() {
      _filteredProducts = _productPresenter.products.where((product) {
        return product.name.toLowerCase().contains(query) ||
            (product.barcode?.toLowerCase().contains(query) ?? false) ||
            (product.sku?.toLowerCase().contains(query) ?? false);
      }).toList();
    });
  }

  Future<void> _loadOpeningBalances() async {
    // في التطبيق الحقيقي، يجب استرداد أرصدة أول المدة من قاعدة البيانات
    // هذه مجرد بيانات وهمية للعرض
    setState(() {
      _openingBalances = [
        {
          'id': '1',
          'date': DateTime.now().subtract(const Duration(days: 30)),
          'warehouseName': 'المخزن الرئيسي',
          'productName': 'لابتوب HP',
          'quantity': 10.0,
          'costPrice': 1500.0,
          'totalCost': 15000.0,
        },
        {
          'id': '2',
          'date': DateTime.now().subtract(const Duration(days: 25)),
          'warehouseName': 'مخزن الفرع الأول',
          'productName': 'طابعة Canon',
          'quantity': 5.0,
          'costPrice': 800.0,
          'totalCost': 4000.0,
        },
      ];
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: AppTheme.lightTheme, // استخدام الثيم المخصص
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveOpeningBalance() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من اختيار المخزن والمنتج
    if (_selectedWarehouseId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار المخزن')),
      );
      return;
    }

    if (_selectedProduct == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار المنتج')),
      );
      return;
    }

    // عرض مربع حوار التأكيد
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحفظ'),
        content: const Text('هل تريد حفظ رصيد أول المدة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (confirm != true) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      if (!mounted) return;

      final warehouse = _warehousePresenter.warehouses.firstWhere(
        (w) => w.id == _selectedWarehouseId,
        orElse: () => throw Exception('المخزن غير موجود'),
      );

      final quantity = double.parse(_quantityController.text);
      final costPrice = double.parse(_costPriceController.text);

      // في التطبيق الحقيقي، يجب حفظ رصيد أول المدة في قاعدة البيانات
      // وتحديث رصيد المنتج في المخزن المحدد

      // إضافة رصيد أول المدة إلى القائمة للعرض
      setState(() {
        _openingBalances.add({
          'id': DateTime.now().millisecondsSinceEpoch.toString(),
          'date': _selectedDate,
          'warehouseName': warehouse.name,
          'productName': _selectedProduct!.name,
          'quantity': quantity,
          'costPrice': costPrice,
          'totalCost': quantity * costPrice,
        });
      });

      // إعادة تعيين النموذج
      _resetForm();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ رصيد أول المدة بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حفظ رصيد أول المدة: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _resetForm() {
    _quantityController.clear();
    _costPriceController.clear();
    _notesController.clear();
    setState(() {
      _selectedProduct = null;
    });
  }

  void _selectProduct(Product product) {
    setState(() {
      _selectedProduct = product;
      _costPriceController.text = product.purchasePrice.toString();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _quantityController.dispose();
    _costPriceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'رصيد أول المدة للمنتجات',
        showBackButton: true,
        actions: [
          // أيقونة البحث
          IconButton(
            icon: Icon(_showSearchField ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                  _filterProducts();
                }
              });
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث)
                if (_showSearchField)
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: TextField(
                      controller: _searchController,
                      decoration: const InputDecoration(
                        hintText: 'بحث عن منتج...',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        _filterProducts();
                      },
                    ),
                  ),
                // نموذج إضافة رصيد أول المدة
                Expanded(
                  flex: 1,
                  child: SingleChildScrollView(
                    padding: EdgeInsets.zero,
                    child: AkCard(
                      padding: EdgeInsets.zero,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4.0, vertical: 4.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // المخزن والتاريخ
                              Row(
                                children: [
                                  // المخزن
                                  Expanded(
                                    flex: 2,
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedWarehouseId,
                                      decoration: InputDecoration(
                                        labelText: 'المخزن *',
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                              Layout.defaultRadius),
                                        ),
                                        filled: true,
                                        prefixIcon: const Icon(Icons.warehouse),
                                      ),
                                      items: _warehousePresenter.warehouses
                                          .map((warehouse) {
                                        return DropdownMenuItem<String>(
                                          value: warehouse.id,
                                          child: Text(warehouse.name),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedWarehouseId = value;
                                        });
                                      },
                                      validator: (value) {
                                        if (value == null) {
                                          return 'يرجى اختيار المخزن';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  // التاريخ
                                  Expanded(
                                    flex: 1,
                                    child: InkWell(
                                      onTap: () => _selectDate(context),
                                      child: InputDecorator(
                                        decoration: InputDecoration(
                                          labelText: 'التاريخ',
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(
                                                Layout.defaultRadius),
                                          ),
                                          filled: true,
                                          prefixIcon:
                                              const Icon(Icons.calendar_today),
                                        ),
                                        child: Text(
                                          '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}',
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: AppDimensions.spacing4),

                              // المنتج
                              _selectedProduct == null
                                  ? _buildProductSelector()
                                  : _buildSelectedProduct(),
                              const SizedBox(height: AppDimensions.spacing4),

                              // الكمية وسعر التكلفة
                              Row(
                                children: [
                                  // الكمية
                                  Expanded(
                                    flex: 1,
                                    child: TextFormField(
                                      controller: _quantityController,
                                      decoration: const InputDecoration(
                                        labelText: 'الكمية',
                                        hintText: 'أدخل الكمية',
                                        border: OutlineInputBorder(),
                                        prefixIcon: Icon(Icons.inventory_2),
                                      ),
                                      keyboardType:
                                          const TextInputType.numberWithOptions(
                                              decimal: true),
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'يرجى إدخال الكمية';
                                        }
                                        if (double.tryParse(value) == null) {
                                          return 'يرجى إدخال رقم صحيح';
                                        }
                                        if (double.parse(value) <= 0) {
                                          return 'يجب أن تكون الكمية أكبر من صفر';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  // سعر التكلفة
                                  Expanded(
                                    flex: 1,
                                    child: TextFormField(
                                      controller: _costPriceController,
                                      decoration: const InputDecoration(
                                        labelText: 'سعر التكلفة',
                                        hintText: 'أدخل سعر التكلفة',
                                        border: OutlineInputBorder(),
                                        prefixIcon: Icon(Icons.attach_money),
                                      ),
                                      keyboardType:
                                          const TextInputType.numberWithOptions(
                                              decimal: true),
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'يرجى إدخال سعر التكلفة';
                                        }
                                        if (double.tryParse(value) == null) {
                                          return 'يرجى إدخال رقم صحيح';
                                        }
                                        if (double.parse(value) <= 0) {
                                          return 'يجب أن يكون سعر التكلفة أكبر من صفر';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: AppDimensions.spacing4),

                              // الملاحظات
                              TextFormField(
                                controller: _notesController,
                                decoration: const InputDecoration(
                                  labelText: 'ملاحظات',
                                  hintText: 'أدخل ملاحظات إضافية (اختياري)',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.note),
                                ),
                                maxLines: 2,
                              ),
                              const SizedBox(height: AppDimensions.spacing4),

                              // زر الحفظ
                              Center(
                                child: SizedBox(
                                  width:
                                      Layout.isTablet() ? 200 : double.infinity,
                                  height: 45,
                                  child: ElevatedButton.icon(
                                    onPressed:
                                        _isSaving ? null : _saveOpeningBalance,
                                    icon: _isSaving
                                        ? const SizedBox(
                                            width: 18,
                                            height: 18,
                                            child: CircularProgressIndicator(
                                                color: AppColors
                                                    .lightTextSecondary,
                                                strokeWidth: 2))
                                        : const Icon(Icons.save, size: 20),
                                    label: const Text(
                                      'حفظ',
                                      style: AppTypography(fontSize: 15),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.primary,
                                      foregroundColor: AppColors.onPrimary,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // جدول أرصدة أول المدة
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 4.0, vertical: 4.0),
                    child: AkCard(
                      padding: EdgeInsets.zero,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4.0, vertical: 4.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4.0, vertical: 2.0),
                              child: Text(
                                'أرصدة أول المدة',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                            ),
                            Expanded(
                              child: AdvancedDataTable(
                                columns: const [
                                  DataColumn(label: Text('إجراءات')),
                                  DataColumn(label: Text('م')),
                                  DataColumn(label: Text('التاريخ')),
                                  DataColumn(label: Text('المخزن')),
                                  DataColumn(label: Text('المنتج')),
                                  DataColumn(label: Text('الكمية')),
                                  DataColumn(label: Text('سعر التكلفة')),
                                  DataColumn(label: Text('الإجمالي')),
                                ],
                                rows: _openingBalances
                                    .asMap()
                                    .entries
                                    .map((entry) {
                                  final balance = entry.value;
                                  return DataRow(
                                    cells: [
                                      DataCell(
                                        PopupMenuButton<String>(
                                          icon: const Icon(Icons.more_vert),
                                          onSelected: (value) async {
                                            if (value == 'delete') {
                                              // حذف رصيد أول المدة بعد التأكيد
                                              final confirm =
                                                  await showDialog<bool>(
                                                context: context,
                                                builder: (context) =>
                                                    AlertDialog(
                                                  title:
                                                      const Text('تأكيد الحذف'),
                                                  content: const Text(
                                                      'هل أنت متأكد من حذف هذا الرصيد؟'),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () =>
                                                          Navigator.of(context)
                                                              .pop(false),
                                                      child:
                                                          const Text('إلغاء'),
                                                    ),
                                                    ElevatedButton(
                                                      onPressed: () =>
                                                          Navigator.of(context)
                                                              .pop(true),
                                                      style: ElevatedButton
                                                          .styleFrom(
                                                        backgroundColor:
                                                            AppColors.error,
                                                      ),
                                                      child: const Text('حذف'),
                                                    ),
                                                  ],
                                                ),
                                              );

                                              if (confirm == true) {
                                                setState(() {
                                                  _openingBalances
                                                      .removeAt(entry.key);
                                                });
                                              }
                                            }
                                          },
                                          itemBuilder: (context) => [
                                            const PopupMenuItem<String>(
                                              value: 'delete',
                                              child: Row(
                                                children: [
                                                  Icon(Icons.delete,
                                                      color: AppColors.error),
                                                  SizedBox(width: 8),
                                                  Text('حذف'),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      DataCell(Text('${entry.key + 1}')),
                                      DataCell(Text(
                                          '${(balance['date'] as DateTime).year}-${(balance['date'] as DateTime).month.toString().padLeft(2, '0')}-${(balance['date'] as DateTime).day.toString().padLeft(2, '0')}')),
                                      DataCell(Text(
                                          balance['warehouseName'] as String)),
                                      DataCell(Text(
                                          balance['productName'] as String)),
                                      DataCell(Text(
                                          (balance['quantity'] as double)
                                              .toString())),
                                      DataCell(Text(
                                          (balance['costPrice'] as double)
                                              .toStringAsFixed(2))),
                                      DataCell(Text(
                                          (balance['totalCost'] as double)
                                              .toStringAsFixed(2))),
                                    ],
                                  );
                                }).toList(),
                                isLoading: false,
                                showCellBorder: true,
                                zebraPattern: true,
                                headerBackgroundColor: AppColors.primary,
                                headerTextColor: AppColors.onPrimary,
                                showRowNumbers: false,
                                emptyMessage: 'لا توجد أرصدة أول مدة',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildProductSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          child: Text(
            'اختر منتج',
            style: AppTypography(fontWeight: FontWeight.bold),
          ),
        ),
        Container(
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.lightTextSecondary),
            borderRadius: BorderRadius.circular(Layout.defaultRadius),
          ),
          child: _filteredProducts.isEmpty
              ? const Center(child: Text('لا توجد منتجات'))
              : ListView.builder(
                  itemCount: _filteredProducts.length,
                  itemBuilder: (context, index) {
                    final product = _filteredProducts[index];
                    return ListTile(
                      title: Text(product.name),
                      subtitle: Text(
                          'الباركود: ${product.barcode ?? 'غير محدد'} | SKU: ${product.sku ?? 'غير محدد'}'),
                      trailing: Text(
                          'سعر التكلفة: ${product.purchasePrice.toStringAsFixed(2)}'),
                      onTap: () => _selectProduct(product),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildSelectedProduct() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.lightTextSecondary),
        borderRadius: BorderRadius.circular(Layout.defaultRadius),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'المنتج المحدد: ${_selectedProduct!.name}',
                  style: const AppTypography(fontWeight: FontWeight.bold),
                ),
                Text(
                    'الباركود: ${_selectedProduct!.barcode ?? 'غير محدد'} | SKU: ${_selectedProduct!.sku ?? 'غير محدد'}'),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              setState(() {
                _selectedProduct = null;
              });
            },
          ),
        ],
      ),
    );
  }
}
