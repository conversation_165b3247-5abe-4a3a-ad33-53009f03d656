import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import 'package:tajer_plus/core/database/basic_data_initializer.dart';
import 'package:tajer_plus/core/database/database_schema.dart';
import 'package:tajer_plus/core/auth/roles_schema.dart';

void main() {
  // تهيئة sqflite_ffi للاختبارات
  setUpAll(() {
    // تهيئة sqflite_ffi
    sqfliteFfiInit();
    // تعيين databaseFactory إلى databaseFactoryFfi
    databaseFactory = databaseFactoryFfi;
  });

  group('ImprovedBasicDataInitializer Integration Tests', () {
    late Database db;
    late String dbPath;

    setUp(() async {
      // إنشاء قاعدة بيانات مؤقتة للاختبار
      final databasesPath = await getDatabasesPath();
      dbPath = join(databasesPath, 'test_basic_data_initializer.db');

      // حذف قاعدة البيانات إذا كانت موجودة
      final file = File(dbPath);
      if (await file.exists()) {
        await file.delete();
      }

      // إنشاء قاعدة البيانات
      db = await openDatabase(
        dbPath,
        version: 1,
        onCreate: (db, version) async {
          // إنشاء الجداول الأساسية للاختبار
          await DatabaseSchema.createAllTables(db);
        },
      );
    });

    tearDown(() async {
      // إغلاق قاعدة البيانات
      await db.close();

      // حذف قاعدة البيانات بعد الانتهاء من الاختبار
      final file = File(dbPath);
      if (await file.exists()) {
        await file.delete();
      }
    });

    test('initializeBasicData should create all required data', () async {
      // تنفيذ تهيئة البيانات الأساسية
      await BasicDataInitializer.initialize(db);

      // التحقق من وجود الصلاحيات
      final permissions =
          await db.query('permissions', where: 'is_deleted = 0');
      expect(permissions, isNotEmpty);
      expect(
          permissions.length,
          greaterThanOrEqualTo(
              RolesSchema.permissions.values.expand((e) => e.values).length));

      // التحقق من وجود الأدوار
      final roles = await db.query('roles', where: 'is_deleted = 0');
      expect(roles, isNotEmpty);
      expect(roles.length, greaterThanOrEqualTo(RolesSchema.roles.length));

      // التحقق من وجود روابط الصلاحيات بالأدوار
      final rolePermissions =
          await db.query('role_permissions', where: 'is_deleted = 0');
      expect(rolePermissions, isNotEmpty);

      // التحقق من وجود مجموعات المستخدمين
      final userGroups = await db.query('user_groups', where: 'is_deleted = 0');
      expect(userGroups, isNotEmpty);
      expect(
          userGroups.length,
          greaterThanOrEqualTo(
              2)); // على الأقل مجموعة المدراء ومجموعة المستخدمين

      // التحقق من وجود المستخدم الافتراضي
      final users = await db.query('users',
          where: 'username = ? AND is_deleted = 0', whereArgs: ['admin']);
      expect(users, isNotEmpty);
      expect(users.length, 1);
      expect(users.first['username'], 'admin');
    });

    test('initializeBasicData should handle duplicate permission names',
        () async {
      // إنشاء صلاحية بنفس الاسم مرتين
      await db.insert('permissions', {
        'id': 'test-permission-1',
        'name': 'اختبار تكرار الصلاحيات',
        'description': 'صلاحية للاختبار',
        'module': 'test',
        'created_at': DateTime.now().toIso8601String(),
        'is_deleted': 0,
      });

      // محاولة إنشاء صلاحية بنفس الاسم مرة أخرى - يجب أن تفشل
      try {
        await db.insert('permissions', {
          'id': 'test-permission-2',
          'name': 'اختبار تكرار الصلاحيات',
          'description': 'صلاحية للاختبار 2',
          'module': 'test',
          'created_at': DateTime.now().toIso8601String(),
          'is_deleted': 0,
        });

        // هذا يجب أن يفشل بسبب قيد UNIQUE على عمود name
        fail('يجب أن تفشل عملية الإدخال بسبب قيد UNIQUE');
      } catch (e) {
        // نتوقع أن تفشل العملية بسبب قيد UNIQUE
        expect(e, isA<DatabaseException>());
        expect(e.toString(), contains('UNIQUE constraint failed'));
      }

      // الآن نختبر أن ImprovedBasicDataInitializer يتعامل مع هذه الحالة بشكل صحيح
      await BasicDataInitializer.initialize(db);

      // التحقق من أن الصلاحية الأصلية لا تزال موجودة
      final permissions = await db.query(
        'permissions',
        where: 'name = ? AND is_deleted = 0',
        whereArgs: ['اختبار تكرار الصلاحيات'],
      );
      expect(permissions, isNotEmpty);
      expect(permissions.length, 1);
      expect(permissions.first['id'], 'test-permission-1');
    });

    test('initializeBasicData should be idempotent', () async {
      // تنفيذ تهيئة البيانات الأساسية مرتين
      await BasicDataInitializer.initialize(db);

      // حفظ عدد السجلات بعد التهيئة الأولى
      final permissionsCount1 = Sqflite.firstIntValue(await db
          .rawQuery('SELECT COUNT(*) FROM permissions WHERE is_deleted = 0'));
      final rolesCount1 = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM roles WHERE is_deleted = 0'));
      final userGroupsCount1 = Sqflite.firstIntValue(await db
          .rawQuery('SELECT COUNT(*) FROM user_groups WHERE is_deleted = 0'));
      final usersCount1 = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM users WHERE is_deleted = 0'));

      // تنفيذ التهيئة مرة ثانية
      await BasicDataInitializer.initialize(db);

      // التحقق من أن عدد السجلات لم يتغير
      final permissionsCount2 = Sqflite.firstIntValue(await db
          .rawQuery('SELECT COUNT(*) FROM permissions WHERE is_deleted = 0'));
      final rolesCount2 = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM roles WHERE is_deleted = 0'));
      final userGroupsCount2 = Sqflite.firstIntValue(await db
          .rawQuery('SELECT COUNT(*) FROM user_groups WHERE is_deleted = 0'));
      final usersCount2 = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM users WHERE is_deleted = 0'));

      expect(permissionsCount2, permissionsCount1);
      expect(rolesCount2, rolesCount1);
      expect(userGroupsCount2, userGroupsCount1);
      expect(usersCount2, usersCount1);
    });
  });
}
