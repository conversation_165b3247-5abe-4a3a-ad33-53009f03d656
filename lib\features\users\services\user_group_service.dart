import '../../../core/database/database_helper.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../models/user_group.dart';

/// خدمة مجموعات المستخدمين
class UserGroupService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع مجموعات المستخدمين
  Future<List<UserGroup>> getAllGroups({bool includeInactive = false}) async {
    try {
      // التأكد من أن قاعدة البيانات مفتوحة بصلاحيات القراءة والكتابة
      final db = await _databaseHelper.database;

      // بناء شرط WHERE
      String whereClause = 'is_deleted = 0';

      if (!includeInactive) {
        whereClause += ' AND is_active = 1';
      }

      try {
        // محاولة استخدام استعلام SQL مباشر بدلاً من query
        final List<Map<String, dynamic>> maps = await db.rawQuery(
          'SELECT * FROM user_groups WHERE $whereClause ORDER BY name ASC',
        );

        return List.generate(maps.length, (i) {
          return UserGroup.fromMap(maps[i]);
        });
      } catch (queryError) {
        // إذا فشل الاستعلام، نحاول استعلامًا أبسط
        AppLogger.info('ℹ️ تبديل إلى استعلام أبسط (آلية حماية): $queryError');

        try {
          // استعلام أبسط بدون شروط
          final List<Map<String, dynamic>> simpleMaps =
              await db.rawQuery('SELECT * FROM user_groups');

          // تصفية النتائج يدويًا
          final filteredMaps = simpleMaps.where((map) {
            final isDeleted = map['is_deleted'] == 1;
            final isInactive = map['is_active'] == 0;

            if (!includeInactive) {
              return !isDeleted && !isInactive;
            } else {
              return !isDeleted;
            }
          }).toList();

          return List.generate(filteredMaps.length, (i) {
            return UserGroup.fromMap(filteredMaps[i]);
          });
        } catch (e) {
          // إذا فشل الاستعلام الثاني أيضًا، نعيد قائمة فارغة
          AppLogger.error('فشل الاستعلام البسيط أيضًا: $e');
          return [];
        }
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على مجموعات المستخدمين',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('فشل في الحصول على مجموعات المستخدمين: $e');
      return [];
    }
  }

  /// الحصول على مجموعة بواسطة المعرف
  Future<UserGroup?> getGroupById(String id) async {
    try {
      final db = await _databaseHelper.database;

      try {
        // استخدام استعلام SQL مباشر بدلاً من query
        final List<Map<String, dynamic>> maps = await db.rawQuery(
          'SELECT * FROM user_groups WHERE id = ? AND is_deleted = 0 LIMIT 1',
          [id],
        );

        if (maps.isNotEmpty) {
          return UserGroup.fromMap(maps.first);
        }
        return null;
      } catch (queryError) {
        // إذا فشل الاستعلام، نحاول استعلامًا أبسط
        AppLogger.info('ℹ️ تبديل إلى استعلام أبسط (آلية حماية): $queryError');

        // استعلام أبسط بدون معاملات
        final List<Map<String, dynamic>> simpleMaps = await db.rawQuery(
          'SELECT * FROM user_groups WHERE id = "$id" AND is_deleted = 0 LIMIT 1',
        );

        if (simpleMaps.isNotEmpty) {
          return UserGroup.fromMap(simpleMaps.first);
        }
        return null;
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على مجموعة المستخدمين',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      AppLogger.error('فشل في الحصول على مجموعة المستخدمين: $e');
      return null;
    }
  }

  /// إضافة مجموعة جديدة
  Future<bool> addGroup(UserGroup group) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم وجود مجموعة بنفس الاسم
      final List<Map<String, dynamic>> existingGroups = await db.query(
        'user_groups',
        where: 'name = ? AND is_deleted = 0',
        whereArgs: [group.name],
        limit: 1,
      );

      if (existingGroups.isNotEmpty) {
        AppLogger.warning('مجموعة بنفس الاسم موجودة بالفعل: ${group.name}');
        return false;
      }

      await db.insert('user_groups', group.toMap());
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة مجموعة المستخدمين',
        error: e,
        stackTrace: stackTrace,
        context: {'group': group.toString()},
      );
      return false;
    }
  }

  /// تحديث مجموعة موجودة
  Future<bool> updateGroup(UserGroup group) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود المجموعة
      final existingGroup = await getGroupById(group.id);
      if (existingGroup == null) {
        AppLogger.warning('المجموعة غير موجودة: ${group.id}');
        return false;
      }

      // التحقق من عدم وجود مجموعة أخرى بنفس الاسم
      final List<Map<String, dynamic>> existingGroups = await db.query(
        'user_groups',
        where: 'name = ? AND id != ? AND is_deleted = 0',
        whereArgs: [group.name, group.id],
        limit: 1,
      );

      if (existingGroups.isNotEmpty) {
        AppLogger.warning('مجموعة بنفس الاسم موجودة بالفعل: ${group.name}');
        return false;
      }

      final updatedMap = group.toMap();
      updatedMap['updated_at'] = DateTime.now().toIso8601String();

      await db.update(
        'user_groups',
        updatedMap,
        where: 'id = ?',
        whereArgs: [group.id],
      );
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث مجموعة المستخدمين',
        error: e,
        stackTrace: stackTrace,
        context: {'group': group.toString()},
      );
      return false;
    }
  }

  /// حذف مجموعة (حذف منطقي)
  Future<bool> deleteGroup(String id) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود المجموعة
      final group = await getGroupById(id);
      if (group == null) {
        AppLogger.warning('محاولة حذف مجموعة غير موجودة: $id');
        return false;
      }

      // التحقق من أن المجموعة ليست من المجموعات الأساسية
      if (_isSystemGroup(group.name)) {
        AppLogger.error('⛔ محاولة حذف مجموعة أساسية غير مسموحة: ${group.name}');
        return false;
      }

      // التحقق من أن المجموعة ليست مستخدمة من قبل أي مستخدم
      final usersWithGroup = await db.query(
        'users',
        where: 'user_group_id = ? AND is_deleted = 0',
        whereArgs: [id],
      );

      if (usersWithGroup.isNotEmpty) {
        AppLogger.error(
            '⛔ لا يمكن حذف المجموعة لأنها مستخدمة من قبل ${usersWithGroup.length} مستخدم');
        return false;
      }

      // التحقق من أن المجموعة ليست المجموعة الوحيدة المتبقية
      final allGroups = await getAllGroups(includeInactive: true);
      if (allGroups.length <= 1) {
        AppLogger.error('⛔ لا يمكن حذف المجموعة الوحيدة المتبقية في النظام');
        return false;
      }

      await db.update(
        'user_groups',
        {
          'is_deleted': 1,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      AppLogger.info('✅ تم حذف المجموعة بنجاح: ${group.name}');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في حذف مجموعة المستخدمين: $e');
      ErrorTracker.captureError(
        'فشل في حذف مجموعة المستخدمين',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// التحقق مما إذا كانت المجموعة من المجموعات الأساسية
  bool _isSystemGroup(String groupName) {
    final lowerName = groupName.toLowerCase();

    // قائمة بأسماء المجموعات الأساسية
    final systemGroupNames = [
      'مدراء النظام',
      'system administrators',
      'admins',
      'مدراء',
      'managers',
      'محاسبون',
      'accountants',
      'أمناء الصندوق',
      'cashiers',
      'مستخدمون',
      'users',
      'مسؤولو المخازن',
      'inventory managers',
      'مسؤولو المبيعات',
      'sales managers',
      'مسؤولو المشتريات',
      'purchase managers',
    ];

    return systemGroupNames
        .any((name) => lowerName.contains(name.toLowerCase()));
  }

  /// تغيير حالة نشاط المجموعة
  Future<bool> toggleGroupStatus(String id, bool isActive) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود المجموعة
      final group = await getGroupById(id);
      if (group == null) {
        AppLogger.warning('محاولة تغيير حالة مجموعة غير موجودة: $id');
        return false;
      }

      // إذا كانت العملية هي تعطيل المجموعة (isActive = false)
      if (!isActive) {
        // التحقق من أن المجموعة ليست من المجموعات الأساسية
        if (_isSystemGroup(group.name)) {
          AppLogger.error(
              '⛔ محاولة تعطيل مجموعة أساسية غير مسموحة: ${group.name}');
          return false;
        }

        // التحقق من أن المجموعة ليست مستخدمة من قبل أي مستخدم
        final usersWithGroup = await db.query(
          'users',
          where: 'user_group_id = ? AND is_deleted = 0 AND is_active = 1',
          whereArgs: [id],
        );

        if (usersWithGroup.isNotEmpty) {
          AppLogger.warning(
              '⚠️ تعطيل المجموعة سيؤثر على ${usersWithGroup.length} مستخدم نشط');
        }

        // التحقق من أن المجموعة ليست المجموعة النشطة الوحيدة المتبقية
        final activeGroups = await getAllGroups(includeInactive: false);
        if (activeGroups.length <= 1 && activeGroups.any((g) => g.id == id)) {
          AppLogger.error(
              '⛔ لا يمكن تعطيل المجموعة النشطة الوحيدة المتبقية في النظام');
          return false;
        }
      }

      await db.update(
        'user_groups',
        {
          'is_active': isActive ? 1 : 0,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      AppLogger.info(
          '✅ تم ${isActive ? 'تفعيل' : 'تعطيل'} المجموعة بنجاح: ${group.name}');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في تغيير حالة مجموعة المستخدمين: $e');
      ErrorTracker.captureError(
        'فشل في تغيير حالة مجموعة المستخدمين',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id, 'isActive': isActive},
      );
      return false;
    }
  }
}
