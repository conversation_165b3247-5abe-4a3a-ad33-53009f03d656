import 'package:sqflite/sqflite.dart';
import 'package:sqflite/sqlite_api.dart'; // Importar para ConflictAlgorithm
import '../database/database_helper.dart';
import '../models/supplier.dart';
import '../utils/app_logger.dart';

/// خدمة الموردين
class SupplierService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع الموردين
  Future<List<Supplier>> getSuppliers() async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'suppliers',
        where: 'is_deleted = ?',
        whereArgs: [0],
      );

      return List.generate(maps.length, (i) {
        return Supplier.fromMap(maps[i]);
      });
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الموردين', error: e);
      return [];
    }
  }

  /// الحصول على مورد بواسطة المعرف
  Future<Supplier?> getSupplierById(String id) async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'suppliers',
        where: 'id = ? AND is_deleted = ?',
        whereArgs: [id, 0],
      );

      if (maps.isNotEmpty) {
        return Supplier.fromMap(maps.first);
      }

      return null;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على المورد', error: e);
      return null;
    }
  }

  /// إضافة مورد جديد
  Future<Supplier> addSupplier(Supplier supplier) async {
    try {
      final db = await _databaseHelper.database;

      final map = supplier.toMap();

      await db.insert(
        'suppliers',
        map,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      return supplier;
    } catch (e) {
      AppLogger.error('خطأ في إضافة المورد', error: e);
      rethrow;
    }
  }

  /// تحديث مورد
  Future<Supplier> updateSupplier(Supplier supplier) async {
    try {
      final db = await _databaseHelper.database;

      final map = supplier.toMap();

      await db.update(
        'suppliers',
        map,
        where: 'id = ?',
        whereArgs: [supplier.id],
      );

      return supplier;
    } catch (e) {
      AppLogger.error('خطأ في تحديث المورد', error: e);
      rethrow;
    }
  }

  /// حذف مورد (حذف منطقي)
  Future<bool> deleteSupplier(String id) async {
    try {
      final db = await _databaseHelper.database;

      await db.update(
        'suppliers',
        {
          'is_deleted': 1,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      return true;
    } catch (e) {
      AppLogger.error('خطأ في حذف المورد', error: e);
      return false;
    }
  }

  /// البحث عن الموردين
  Future<List<Supplier>> searchSuppliers(String query) async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'suppliers',
        where:
            'is_deleted = ? AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)',
        whereArgs: [0, '%$query%', '%$query%', '%$query%'],
      );

      return List.generate(maps.length, (i) {
        return Supplier.fromMap(maps[i]);
      });
    } catch (e) {
      AppLogger.error('خطأ في البحث عن الموردين', error: e);
      return [];
    }
  }

  /// الحصول على الموردين النشطين
  Future<List<Supplier>> getActiveSuppliers() async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'suppliers',
        where: 'is_deleted = ? AND is_active = ?',
        whereArgs: [0, 1],
      );

      return List.generate(maps.length, (i) {
        return Supplier.fromMap(maps[i]);
      });
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الموردين النشطين', error: e);
      return [];
    }
  }

  /// الحصول على الموردين ذوي الرصيد المدين
  Future<List<Supplier>> getSuppliersWithDebitBalance() async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'suppliers',
        where: 'is_deleted = ? AND balance < ?',
        whereArgs: [0, 0],
      );

      return List.generate(maps.length, (i) {
        return Supplier.fromMap(maps[i]);
      });
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الموردين ذوي الرصيد المدين', error: e);
      return [];
    }
  }

  /// الحصول على الموردين ذوي الرصيد الدائن
  Future<List<Supplier>> getSuppliersWithCreditBalance() async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'suppliers',
        where: 'is_deleted = ? AND balance > ?',
        whereArgs: [0, 0],
      );

      return List.generate(maps.length, (i) {
        return Supplier.fromMap(maps[i]);
      });
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الموردين ذوي الرصيد الدائن', error: e);
      return [];
    }
  }

  /// الحصول على إجمالي رصيد الموردين
  Future<double> getTotalSuppliersBalance() async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery(
        'SELECT SUM(balance) as total FROM suppliers WHERE is_deleted = 0',
      );

      if (result.isNotEmpty) {
        final total = result.first['total'];
        if (total != null) {
          return total is int ? (total).toDouble() : total as double;
        }
      }

      return 0.0;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على إجمالي رصيد الموردين', error: e);
      return 0.0;
    }
  }
}
