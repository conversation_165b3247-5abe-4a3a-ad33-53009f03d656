import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart' as intl;
import '../../../core/theme/index.dart';

/// مكون جدول متزامن للحسابات
/// يضمن أن رؤوس الأعمدة تتحرك مع الصفوف عند التمرير أفقيًا
/// كما يضمن تزامن جميع الصفوف عند التمرير أفقيًا
/// مما يحل مشكلة اختلاط البيانات عند التمرير
class AccountsSynchronizedTable extends StatefulWidget {
  final List<Map<String, dynamic>> accounts;
  final Function(Map<String, dynamic>) onEdit;
  final Function(Map<String, dynamic>) onDelete;
  final Function(Map<String, dynamic>) onPrint;

  const AccountsSynchronizedTable({
    Key? key,
    required this.accounts,
    required this.onEdit,
    required this.onDelete,
    required this.onPrint,
  }) : super(key: key);

  @override
  State<AccountsSynchronizedTable> createState() =>
      _AccountsSynchronizedTableState();
}

class _AccountsSynchronizedTableState extends State<AccountsSynchronizedTable> {
  // متحكم التمرير الأفقي المشترك لكل الجدول
  final ScrollController _horizontalScrollController = ScrollController();

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);
    final bool isDark = theme.brightness == Brightness.dark;

    // التحقق من وجود حسابات
    if (widget.accounts.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد حسابات للعرض',
          style:
              AppTypography(fontSize: 16, color: AppColors.lightTextSecondary),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.lightBorder, width: 1),
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: AppColors.lightShadow,
            blurRadius: 5,
            spreadRadius: 1,
            offset: Offset(0, 2),
          ),
        ],
      ),
      margin: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // عنوان الجدول
          Container(
            padding: const EdgeInsets.all(8),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color:
                  isDark ? AppColors.lightSurfaceVariant : AppColors.errorLight,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(7),
                topRight: Radius.circular(7),
              ),
            ),
            child: const Text(
              'جدول الحسابات',
              style: AppTypography(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: AppColors.error,
              ),
            ),
          ),

          // رأس الجدول
          Container(
            color:
                isDark ? AppColors.lightSurfaceVariant : AppColors.errorLight,
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              controller: _horizontalScrollController,
              child: Row(
                children: [
                  // الإجراءات
                  Container(
                    width: 100,
                    alignment: Alignment.center,
                    child: const Text(
                      'الإجراءات',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                  // الرقم التسلسلي
                  Container(
                    width: 60,
                    alignment: Alignment.center,
                    child: const Text(
                      '#',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                  // اسم الحساب
                  Container(
                    width: 200,
                    alignment: Alignment.center,
                    child: const Text(
                      'اسم الحساب',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                  // الحساب الأب
                  Container(
                    width: 200,
                    alignment: Alignment.center,
                    child: const Text(
                      'الحساب الأب',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                  // رمز الحساب
                  Container(
                    width: 120,
                    alignment: Alignment.center,
                    child: const Text(
                      'رمز الحساب',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                  // نوع الحساب
                  Container(
                    width: 120,
                    alignment: Alignment.center,
                    child: const Text(
                      'نوع الحساب',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                  // الرصيد
                  Container(
                    width: 120,
                    alignment: Alignment.center,
                    child: const Text(
                      'الرصيد',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                  // رقم الهاتف
                  Container(
                    width: 150,
                    alignment: Alignment.center,
                    child: const Text(
                      'رقم الهاتف',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                  // ملاحظات
                  Container(
                    width: 200,
                    alignment: Alignment.center,
                    child: const Text(
                      'ملاحظات',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                  // الحالة
                  Container(
                    width: 100,
                    alignment: Alignment.center,
                    child: const Text(
                      'الحالة',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // محتوى الجدول
          Expanded(
            child: ListView.builder(
              itemCount: widget.accounts.length,
              itemBuilder: (context, index) {
                final account = widget.accounts[index];
                final bool isEven = index % 2 == 0;
                final Color rowColor = isEven
                    ? (isDark
                        ? AppColors.lightSurfaceVariant
                        : AppColors.onPrimary)
                    : (isDark
                        ? AppColors.lightSurfaceVariant
                        : AppColors.lightSurfaceVariant);

                return Container(
                  color: rowColor,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    controller: _horizontalScrollController,
                    child: Row(
                      children: [
                        // الإجراءات
                        Container(
                          width: 100,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          alignment: Alignment.center,
                          child: _buildActionsCell(account),
                        ),
                        // الرقم التسلسلي
                        Container(
                          width: 60,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          alignment: Alignment.center,
                          child: _buildIdCell(index),
                        ),
                        // اسم الحساب
                        Container(
                          width: 200,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          alignment: Alignment.centerRight,
                          child: _buildNameCell(account),
                        ),
                        // الحساب الأب
                        Container(
                          width: 200,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          alignment: Alignment.centerRight,
                          child: _buildParentCell(account),
                        ),
                        // رمز الحساب
                        Container(
                          width: 120,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          alignment: Alignment.center,
                          child: _buildCodeCell(account),
                        ),
                        // نوع الحساب
                        Container(
                          width: 120,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          alignment: Alignment.center,
                          child: _buildTypeCell(account),
                        ),
                        // الرصيد
                        Container(
                          width: 120,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          alignment: Alignment.center,
                          child: _buildBalanceCell(account),
                        ),
                        // رقم الهاتف
                        Container(
                          width: 150,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          alignment: Alignment.center,
                          child: _buildPhoneCell(account),
                        ),
                        // ملاحظات
                        Container(
                          width: 200,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          alignment: Alignment.centerRight,
                          child: _buildNotesCell(account),
                        ),
                        // الحالة
                        Container(
                          width: 100,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          alignment: Alignment.center,
                          child: _buildStatusCell(account),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // بناء خلية الإجراءات
  Widget _buildActionsCell(Map<String, dynamic> account) {
    final String phone = account['phone'] as String? ?? '';

    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, color: AppColors.lightTextSecondary),
      tooltip: 'الإجراءات',
      onSelected: (String value) {
        switch (value) {
          case 'edit':
            widget.onEdit(account);
            break;
          case 'delete':
            widget.onDelete(account);
            break;
          case 'print':
            widget.onPrint(account);
            break;
          case 'call':
            if (phone.isNotEmpty) {
              _makePhoneCall(phone);
            }
            break;
        }
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        const PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, color: AppColors.info, size: 20),
              SizedBox(width: 8),
              Text('تعديل'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: AppColors.error, size: 20),
              SizedBox(width: 8),
              Text('حذف'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'print',
          child: Row(
            children: [
              Icon(Icons.print, color: AppColors.accent, size: 20),
              SizedBox(width: 8),
              Text('طباعة'),
            ],
          ),
        ),
        if (phone.isNotEmpty)
          const PopupMenuItem<String>(
            value: 'call',
            child: Row(
              children: [
                Icon(Icons.phone, color: AppColors.success, size: 20),
                SizedBox(width: 8),
                Text('اتصال بالرقم'),
              ],
            ),
          ),
      ],
    );
  }

  // بناء خلية الرقم التسلسلي
  Widget _buildIdCell(int index) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: AppColors.lightSurfaceVariant,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          index.toString(),
          style: const AppTypography(
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  // بناء خلية اسم الحساب
  Widget _buildNameCell(Map<String, dynamic> account) {
    final String name = account['name'] as String? ?? '';

    return Align(
      alignment: Alignment.centerRight,
      child: Text(
        name,
        style: const AppTypography(
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  // بناء خلية الحساب الأب
  Widget _buildParentCell(Map<String, dynamic> account) {
    final String parentName = account['parent_name'] as String? ?? '';
    final String parentCode = account['parent_code'] as String? ?? '';

    return Align(
      alignment: Alignment.centerRight,
      child: parentName.isNotEmpty
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.subdirectory_arrow_right,
                    size: 14, color: AppColors.lightTextSecondary),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    parentName,
                    overflow: TextOverflow.ellipsis,
                    style: const AppTypography(
                      color: AppColors.lightTextSecondary,
                    ),
                  ),
                ),
                if (parentCode.isNotEmpty) ...[
                  const SizedBox(width: 4),
                  Text(
                    '#$parentCode',
                    style: const AppTypography(
                      fontSize: 10,
                      color: AppColors.lightTextSecondary,
                    ),
                  ),
                ],
              ],
            )
          : const Text('-',
              style: AppTypography(color: AppColors.lightTextSecondary)),
    );
  }

  // بناء خلية الكود
  Widget _buildCodeCell(Map<String, dynamic> account) {
    final String code = account['code'] as String? ?? '';

    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.infoLight,
          borderRadius: BorderRadius.circular(4),
          ////////////////////////////////////////////////////
          border: Border.all(color: AppColors.infoLight),
        ),
        child: Text(
          code,
          style: const AppTypography(color: AppColors.infoDark),
        ),
      ),
    );
  }

  // بناء خلية النوع
  Widget _buildTypeCell(Map<String, dynamic> account) {
    final String type = account['account_type'] as String? ?? '';
    final bool isMainAccount = type == 'main';
    final Color typeColor =
        isMainAccount ? AppColors.accent : AppColors.lightTextSecondary;
    final String displayText = isMainAccount ? 'رئيسي' : 'فرعي';
    final IconData typeIcon =
        isMainAccount ? Icons.account_tree : Icons.subdirectory_arrow_right;

    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: typeColor.withValues(alpha: 0.12),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: typeColor.withValues(alpha: 0.4)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              typeIcon,
              size: 16,
              color: typeColor,
            ),
            const SizedBox(width: 4),
            Text(
              displayText,
              style: AppTypography(
                color: typeColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء خلية الرصيد
  Widget _buildBalanceCell(Map<String, dynamic> account) {
    final dynamic balanceValue = account['balance'];
    final double balance = balanceValue is int
        ? balanceValue.toDouble()
        : (balanceValue as double? ?? 0.0);
    final bool isPositive = balance >= 0;
    final Color balanceColor = isPositive ? AppColors.success : AppColors.error;

    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: balanceColor.withValues(alpha: 0.08),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: balanceColor.withValues(alpha: 0.2)),
        ),
        child: Text(
          _formatBalance(balance),
          style: AppTypography(
            fontWeight: FontWeight.bold,
            color: isPositive ? AppColors.successDark : AppColors.errorDark,
          ),
        ),
      ),
    );
  }

  // بناء خلية الهاتف
  Widget _buildPhoneCell(Map<String, dynamic> account) {
    final String phone = account['phone'] as String? ?? '';

    return Center(
      child: phone.isNotEmpty
          ? InkWell(
              onTap: () => _makePhoneCall(phone),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.phone, size: 16, color: AppColors.info),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      phone,
                      style: const AppTypography(
                        color: AppColors.info,
                        decoration: TextDecoration.underline,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            )
          : const Text('-',
              style: AppTypography(color: AppColors.lightTextSecondary)),
    );
  }

  // بناء خلية الملاحظات
  Widget _buildNotesCell(Map<String, dynamic> account) {
    final String notes = account['notes'] as String? ?? '';

    return Align(
      alignment: Alignment.centerRight,
      child: notes.isNotEmpty
          ? Tooltip(
              message: notes,
              child: Text(
                notes,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
                style:
                    const AppTypography(color: AppColors.lightSurfaceVariant),
              ),
            )
          : const Text('-',
              style: AppTypography(color: AppColors.lightTextSecondary)),
    );
  }

  // بناء خلية الحالة
  Widget _buildStatusCell(Map<String, dynamic> account) {
    final bool isActive = account['is_active'] == 1;

    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isActive
              ? AppColors.success.withValues(alpha: 0.12)
              : AppColors.error.withValues(alpha: 0.12),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isActive ? Icons.check_circle : Icons.cancel,
              size: 16,
              color: isActive ? AppColors.success : AppColors.error,
            ),
            const SizedBox(width: 4),
            Text(
              isActive ? 'نشط' : 'غير نشط',
              style: AppTypography(
                color: isActive ? AppColors.successDark : AppColors.errorDark,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // تنسيق الرصيد
  String _formatBalance(double balance) {
    final formatter = intl.NumberFormat.currency(
      symbol: '', // بدون رمز العملة
      decimalDigits: 2,
    );

    return formatter.format(balance);
  }

  // فتح مكالمة هاتفية
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      throw 'لا يمكن الاتصال بالرقم $phoneNumber';
    }
  }
}
