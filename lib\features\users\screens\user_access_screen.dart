import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/theme/index.dart';
import '../presenters/user_presenter.dart';
import '../presenters/role_presenter.dart';
import 'access_level.dart';
//import '../../../core/theme/index.dart';

/// شاشة إدارة وصول المستخدمين
/// تتيح للمدير تعيين مستويات الوصول للمستخدمين بطريقة سهلة
class UserAccessScreen extends StatefulWidget {
  static const String routeName = '/user-access';

  const UserAccessScreen({Key? key}) : super(key: key);

  @override
  State<UserAccessScreen> createState() => _UserAccessScreenState();
}

class _UserAccessScreenState extends State<UserAccessScreen> {
  bool _isLoading = true;
  String? _error;

  // قائمة المستخدمين
  List<UserWithAccess> _users = [];

  // الوظائف المتاحة في النظام
  final List<JobFunction> _jobFunctions = [
    JobFunction(
      id: 'sales',
      name: 'المبيعات',
      icon: Icons.point_of_sale,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المبيعات والفواتير والعملاء',
    ),
    JobFunction(
      id: 'inventory',
      name: 'المخزون',
      icon: Icons.inventory_2,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المنتجات والمخزون والمستودعات',
    ),
    JobFunction(
      id: 'purchases',
      name: 'المشتريات',
      icon: Icons.shopping_cart,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المشتريات والموردين',
    ),
    JobFunction(
      id: 'finance',
      name: 'المالية',
      icon: Icons.account_balance,
      color: AppColors.lightTextSecondary,
      description: 'إدارة الحسابات والمصروفات والإيرادات',
    ),
    JobFunction(
      id: 'reports',
      name: 'التقارير',
      icon: Icons.bar_chart,
      color: AppColors.lightTextSecondary,
      description: 'عرض وطباعة التقارير المختلفة',
    ),
    JobFunction(
      id: 'settings',
      name: 'الإعدادات',
      icon: Icons.settings,
      color: AppColors.lightTextSecondary,
      description: 'إدارة إعدادات النظام والمستخدمين',
    ),
  ];

  // مستويات الوصول المتاحة
  List<AccessLevel> _accessLevels = [];

  // استخدام التحميل الكسول
  late final UserPresenter _userPresenter;
  late final RolePresenter _rolePresenter;

  @override
  void initState() {
    super.initState();
    _userPresenter =
        AppProviders.getLazyPresenter<UserPresenter>(() => UserPresenter());
    _rolePresenter =
        AppProviders.getLazyPresenter<RolePresenter>(() => RolePresenter());

    // تحميل البيانات بعد بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // تحميل المستخدمين
      await _userPresenter.loadData();

      // تحميل الأدوار
      await _rolePresenter.loadRoles();

      // تحويل الأدوار إلى مستويات وصول
      _accessLevels = _convertRolesToAccessLevels(_rolePresenter.roles);

      // تحويل المستخدمين إلى مستخدمين مع وصول
      _users =
          _convertUsersToUsersWithAccess(_userPresenter.users, _accessLevels);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'حدث خطأ أثناء تحميل البيانات: $e';
      });
    }
  }

  /// تحويل الأدوار إلى مستويات وصول
  List<AccessLevel> _convertRolesToAccessLevels(List<dynamic> roles) {
    // سيتم تنفيذها لاحقاً
    // هذه محاكاة للبيانات
    return [
      AccessLevel(
        id: 'admin',
        name: 'مدير النظام',
        isCustom: false,
        functionLevels: {
          'sales': AccessLevelType.full,
          'inventory': AccessLevelType.full,
          'purchases': AccessLevelType.full,
          'finance': AccessLevelType.full,
          'reports': AccessLevelType.full,
          'settings': AccessLevelType.full,
        },
      ),
      AccessLevel(
        id: 'sales_manager',
        name: 'مدير مبيعات',
        isCustom: true,
        functionLevels: {
          'sales': AccessLevelType.full,
          'inventory': AccessLevelType.view,
          'purchases': AccessLevelType.none,
          'finance': AccessLevelType.view,
          'reports': AccessLevelType.view,
          'settings': AccessLevelType.none,
        },
      ),
      AccessLevel(
        id: 'accountant',
        name: 'محاسب',
        isCustom: true,
        functionLevels: {
          'sales': AccessLevelType.view,
          'inventory': AccessLevelType.view,
          'purchases': AccessLevelType.view,
          'finance': AccessLevelType.full,
          'reports': AccessLevelType.full,
          'settings': AccessLevelType.none,
        },
      ),
    ];
  }

  /// تحويل المستخدمين إلى مستخدمين مع وصول
  List<UserWithAccess> _convertUsersToUsersWithAccess(
      List<dynamic> users, List<AccessLevel> accessLevels) {
    // سيتم تنفيذها لاحقاً
    // هذه محاكاة للبيانات
    return [
      UserWithAccess(
        id: '1',
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '**********',
        isActive: true,
        accessLevel: accessLevels[0], // مدير النظام
      ),
      UserWithAccess(
        id: '2',
        name: 'محمد علي',
        email: '<EMAIL>',
        phone: '0123456788',
        isActive: true,
        accessLevel: accessLevels[1], // مدير مبيعات
      ),
      UserWithAccess(
        id: '3',
        name: 'سارة أحمد',
        email: '<EMAIL>',
        phone: '0123456787',
        isActive: true,
        accessLevel: accessLevels[2], // محاسب
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('وصول المستخدمين'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const AppTypography(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_users.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: AppColors.lightTextSecondary,
            ),
            SizedBox(height: 16),
            Text(
              'لا يوجد مستخدمين',
              style: AppTypography(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _users.length,
      itemBuilder: (context, index) {
        final user = _users[index];
        return _buildUserCard(user);
      },
    );
  }

  /// بناء بطاقة المستخدم
  Widget _buildUserCard(UserWithAccess user) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المستخدم
          ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.primary,
              child: Text(
                user.name.substring(0, 1),
                style: const AppTypography(
                  color: AppColors.lightTextSecondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              user.name,
              style: const AppTypography(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(user.email),
                Text(user.phone),
              ],
            ),
            trailing: Switch(
              value: user.isActive,
              onChanged: (value) {
                // تغيير حالة المستخدم
              },
            ),
          ),

          // مستوى الوصول
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      'مستوى الوصول:',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: DropdownButton<String>(
                        value: user.accessLevel.id,
                        isExpanded: true,
                        onChanged: (value) {
                          // تغيير مستوى الوصول
                        },
                        items: _accessLevels.map((level) {
                          return DropdownMenuItem<String>(
                            value: level.id,
                            child: Text(level.name),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // عرض ملخص الوصول
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _jobFunctions.map((jobFunction) {
                    final accessLevelType =
                        user.accessLevel.functionLevels[jobFunction.id] ??
                            AccessLevelType.none;

                    return Chip(
                      avatar: Icon(
                        jobFunction.icon,
                        color: jobFunction.color,
                        size: 16,
                      ),
                      label: Text(
                        '${jobFunction.name}: ${_getAccessLevelName(accessLevelType)}',
                        style: AppTypography(
                          fontSize: 12,
                          color: _getAccessLevelColor(accessLevelType),
                        ),
                      ),
                      backgroundColor: _getAccessLevelColor(accessLevelType)
                          .withValues(alpha: 0.12),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم مستوى الوصول
  String _getAccessLevelName(AccessLevelType type) {
    switch (type) {
      case AccessLevelType.none:
        return 'لا وصول';
      case AccessLevelType.view:
        return 'عرض فقط';
      case AccessLevelType.edit:
        return 'تعديل';
      case AccessLevelType.full:
        return 'وصول كامل';
    }
  }

  /// الحصول على لون مستوى الوصول
  Color _getAccessLevelColor(AccessLevelType type) {
    switch (type) {
      case AccessLevelType.none:
        return AppColors.lightTextSecondary;
      case AccessLevelType.view:
        return AppColors.info;
      case AccessLevelType.edit:
        return AppColors.warning;
      case AccessLevelType.full:
        return AppColors.success;
    }
  }
}

/// نموذج المستخدم مع الوصول
class UserWithAccess {
  final String id;
  final String name;
  final String email;
  final String phone;
  final bool isActive;
  final AccessLevel accessLevel;

  UserWithAccess({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.isActive,
    required this.accessLevel,
  });
}
