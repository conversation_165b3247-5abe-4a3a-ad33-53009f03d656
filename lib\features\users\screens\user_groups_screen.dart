import 'package:flutter/material.dart';

import '../models/user_group.dart';
import '../presenters/user_presenter.dart';
import 'user_group_form_screen.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';
import '../../../core/providers/app_providers.dart'; // للتحميل الكسول

/// شاشة إدارة مجموعات المستخدمين
class UserGroupsScreen extends StatefulWidget {
  const UserGroupsScreen({Key? key}) : super(key: key);

  @override
  State<UserGroupsScreen> createState() => _UserGroupsScreenState();
}

class _UserGroupsScreenState extends State<UserGroupsScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchField = false;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // 🚀 تحميل البيانات عند بدء الشاشة باستخدام التحميل الكسول
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AppProviders.getUserPresenter().loadGroups();
    });

    // إضافة مستمع للبحث
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'إدارة مجموعات المستخدمين',
        showBackButton: true,
        actions: [
          // أيقونة البحث
          IconButton(
            icon: Icon(_showSearchField ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                  _searchQuery = '';
                }
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث)
          if (_showSearchField)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'بحث عن مجموعة...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),

          // خيار عرض المجموعات غير النشطة
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: ListenableBuilder(
              listenable: AppProviders.getUserPresenter(),
              builder: (context, child) {
                final presenter = AppProviders.getUserPresenter();
                return SwitchListTile(
                  title: const Text('عرض المجموعات غير النشطة'),
                  value: presenter.includeInactive,
                  onChanged: (value) => presenter.toggleIncludeInactive(value),
                  dense: true,
                  contentPadding: EdgeInsets.zero,
                );
              },
            ),
          ),

          // قائمة المجموعات
          Expanded(
            child: ListenableBuilder(
              listenable: AppProviders.getUserPresenter(),
              builder: (context, child) {
                final presenter = AppProviders.getUserPresenter();
                if (presenter.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (presenter.errorMessage != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline,
                            size: 48, color: AppColors.error),
                        const SizedBox(height: 16),
                        Text(presenter.errorMessage!),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => presenter.loadGroups(),
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                final groups = _filterGroups(presenter.groups);
                if (groups.isEmpty) {
                  if (_searchQuery.isNotEmpty) {
                    return const Center(
                      child: Text('لا يوجد مجموعات مطابقة لبحثك'),
                    );
                  }
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text('لا يوجد مجموعات'),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => _navigateToForm(context),
                          child: const Text('إضافة مجموعة'),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(8.0),
                  itemCount: groups.length,
                  itemBuilder: (context, index) {
                    final group = groups[index];
                    return _buildGroupCard(context, group, presenter);
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: AkFloatingButton(
        icon: Icons.add,
        onPressed: () => _navigateToForm(context),
        tooltip: 'إضافة مجموعة',
      ),
    );
  }

  /// تصفية المجموعات حسب استعلام البحث
  List<UserGroup> _filterGroups(List<UserGroup> groups) {
    if (_searchQuery.isEmpty) {
      return groups;
    }

    final query = _searchQuery.toLowerCase();
    return groups.where((group) {
      return group.name.toLowerCase().contains(query) ||
          (group.description?.toLowerCase() ?? '').contains(query);
    }).toList();
  }

  /// بناء بطاقة المجموعة
  Widget _buildGroupCard(
      BuildContext context, UserGroup group, UserPresenter presenter) {
    return AkCard(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          child: const Icon(Icons.group),
        ),
        title: Text(
          group.name,
          style: AppTypography(
            fontWeight: FontWeight.bold,
            color: group.isActive
                ? Theme.of(context).textTheme.bodyLarge?.color
                : AppColors.lightTextSecondary,
          ),
        ),
        subtitle: (group.description != null && group.description!.isNotEmpty)
            ? Text(
                group.description!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              )
            : null,
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة تغيير حالة النشاط
            IconButton(
              icon: Icon(
                group.isActive ? Icons.toggle_on : Icons.toggle_off,
                color: group.isActive
                    ? AppColors.success
                    : AppColors.lightTextSecondary,
              ),
              onPressed: () {
                presenter.toggleGroupStatus(group.id, !group.isActive);
              },
            ),
            // قائمة الخيارات
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              onSelected: (value) =>
                  _handleMenuItemSelected(value, group, presenter),
              itemBuilder: (context) => [
                const PopupMenuItem<String>(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: AppColors.error),
                      SizedBox(width: 8),
                      Text('حذف', style: AppTypography(color: AppColors.error)),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          _navigateToForm(context, group: group);
        },
      ),
    );
  }

  /// التعامل مع اختيار عنصر من القائمة
  void _handleMenuItemSelected(
      String value, UserGroup group, UserPresenter presenter) {
    switch (value) {
      case 'edit':
        _navigateToForm(context, group: group);
        break;
      case 'delete':
        _showDeleteConfirmationDialog(context, group, presenter);
        break;
    }
  }

  /// عرض مربع حوار تأكيد الحذف
  void _showDeleteConfirmationDialog(
      BuildContext context, UserGroup group, UserPresenter presenter) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المجموعة: ${group.name}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.onPrimary,
            ),
            onPressed: () async {
              // حفظ مرجع للـ context قبل العملية غير المتزامنة
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              Navigator.pop(context);

              final success = await presenter.deleteGroup(group.id);

              if (!mounted) return;

              if (success) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(content: Text('تم حذف المجموعة بنجاح')),
                );
              } else {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(content: Text('فشل في حذف المجموعة')),
                );
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// الانتقال إلى شاشة نموذج المجموعة
  void _navigateToForm(BuildContext context, {UserGroup? group}) {
    // حفظ مرجع للـ presenter قبل الانتقال
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    final presenter = AppProviders.getUserPresenter();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserGroupFormScreen(group: group),
      ),
    ).then((_) {
      // إعادة تحميل البيانات عند العودة من شاشة النموذج
      if (mounted) {
        presenter.loadGroups();
      }
    });
  }
}
