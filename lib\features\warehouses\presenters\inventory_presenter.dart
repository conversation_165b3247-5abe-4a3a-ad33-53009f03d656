import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../../core/models/inventory.dart';
import '../../../core/models/inventory_transaction.dart';
import '../../../core/models/product.dart';
import '../../../core/models/warehouse.dart';
import '../../../core/models/stock_transfer.dart';
import '../../../core/services/inventory_service.dart';
import '../../../core/services/product_service.dart';
import '../../../core/services/warehouse_service.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/database/database_service.dart';
import '../models/inventory_count.dart';

/// مقدم المخزون
/// يتعامل مع منطق إدارة المخزون
class InventoryPresenter extends ChangeNotifier {
  final InventoryService _inventoryService = InventoryService();
  final WarehouseService _warehouseService = WarehouseService();
  final ProductService _productService = ProductService();
  final _db = DatabaseService.instance;

  List<Inventory> _inventoryItems = [];
  List<Map<String, dynamic>> _inventoryItemsDetailed =
      []; // مخزون المنتجات مع تفاصيل إضافية
  List<InventoryTransaction> _transactions = [];
  List<Warehouse> _warehouses = [];
  List<Product> _products = [];
  List<StockTransfer> _stockTransfers = [];
  final List<InventoryCount> _inventoryCounts = [];

  Warehouse? _selectedWarehouse;

  bool _isLoading = false;
  String? _errorMessage;

  /// الحصول على قائمة عناصر المخزون
  List<Inventory> get inventoryItems => _inventoryItems;

  /// الحصول على قائمة عناصر المخزون مع تفاصيل إضافية
  List<Map<String, dynamic>> get inventoryItemsDetailed =>
      _inventoryItemsDetailed;

  /// الحصول على قائمة حركات المخزون
  List<InventoryTransaction> get transactions => _transactions;

  /// الحصول على قائمة المستودعات
  List<Warehouse> get warehouses => _warehouses;

  /// الحصول على قائمة المنتجات
  List<Product> get products => _products;

  /// الحصول على قائمة تحويلات المخزون
  List<StockTransfer> get stockTransfers => _stockTransfers;

  /// الحصول على قائمة عمليات الجرد
  List<InventoryCount> get inventoryCounts => _inventoryCounts;

  /// الحصول على المستودع المحدد
  Warehouse? get selectedWarehouse => _selectedWarehouse;

  /// تعيين المستودع المحدد
  set selectedWarehouse(Warehouse? warehouse) {
    _selectedWarehouse = warehouse;
    notifyListeners();

    if (warehouse != null) {
      loadWarehouseInventory(warehouse.id);
    }
  }

  /// التحقق من حالة التحميل
  bool get isLoading => _isLoading;

  /// الحصول على رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// تحميل المستودعات
  Future<void> loadWarehouses() async {
    try {
      _isLoading = true;
      notifyListeners();

      final warehouses = await _warehouseService.getAllWarehouses();
      _warehouses = warehouses;

      // إذا لم يكن هناك مستودع محدد، نختار المستودع الافتراضي
      if (_selectedWarehouse == null && _warehouses.isNotEmpty) {
        final defaultWarehouse = _warehouses.firstWhere(
          (w) => w.isDefault,
          orElse: () => _warehouses.first,
        );
        _selectedWarehouse = defaultWarehouse;
      }

      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المستودعات: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل المستودعات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
    }
  }

  /// تحميل المنتجات
  Future<void> loadProducts() async {
    try {
      _isLoading = true;
      notifyListeners();

      final products = await _productService.getAllProducts();
      _products = products;

      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المنتجات: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل المنتجات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
    }
  }

  /// تحميل مخزون المستودع
  Future<void> loadWarehouseInventory(String warehouseId) async {
    try {
      _isLoading = true;
      notifyListeners();

      final inventory =
          await _inventoryService.getWarehouseInventory(warehouseId);
      _inventoryItems = inventory;

      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المخزون: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل مخزون المستودع',
        error: e,
        stackTrace: stackTrace,
        context: {'warehouseId': warehouseId},
      );
      notifyListeners();
    }
  }

  /// تحميل مخزون المنتجات مع تفاصيل إضافية
  Future<void> loadInventoryDetailed() async {
    if (_selectedWarehouse == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      // إنشاء فهارس للجداول إذا لم تكن موجودة لتحسين أداء الاستعلام
      await _createInventoryIndexes();

      // بناء استعلام SQL للحصول على مخزون المنتجات مع معلومات المنتج
      // تم تحسين الاستعلام باستخدام الفهارس المناسبة
      final results = await _db.rawQuery('''
        SELECT
          i.product_id,
          p.name as product_name,
          p.barcode,
          p.code,
          p.min_quantity as min_stock,
          i.quantity,
          p.sale_price,
          p.purchase_price,
          c.name as category_name
        FROM
          inventory i
        JOIN
          products p ON i.product_id = p.id
        LEFT JOIN
          categories c ON p.category_id = c.id
        WHERE
          i.warehouse_id = ?
          AND p.is_deleted = 0
        ORDER BY
          p.name ASC
      ''', [_selectedWarehouse!.id]);

      _inventoryItemsDetailed = results;
      _errorMessage = null;
    } catch (e, stackTrace) {
      _errorMessage = 'فشل تحميل المخزون: ${e.toString()}';
      ErrorTracker.captureError(
        'خطأ في تحميل مخزون المنتجات مع تفاصيل إضافية',
        error: e,
        stackTrace: stackTrace,
        context: {'warehouseId': _selectedWarehouse?.id},
      );
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// إنشاء فهارس للجداول المتعلقة بالمخزون لتحسين أداء الاستعلامات
  Future<void> _createInventoryIndexes() async {
    try {
      // فهرس للمخزون على حقل warehouse_id
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_inventory_warehouse_id
        ON inventory (warehouse_id)
      ''');

      // فهرس للمخزون على حقل product_id
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_inventory_product_id
        ON inventory (product_id)
      ''');

      // فهرس مركب للمخزون على حقلي warehouse_id و product_id
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_inventory_warehouse_product
        ON inventory (warehouse_id, product_id)
      ''');

      // فهرس لحركات المخزون على حقل warehouse_id
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_inventory_transactions_warehouse_id
        ON inventory_transactions (warehouse_id)
      ''');

      // فهرس لحركات المخزون على حقل product_id
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_inventory_transactions_product_id
        ON inventory_transactions (product_id)
      ''');

      // فهرس لحركات المخزون على حقل transaction_date
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_inventory_transactions_date
        ON inventory_transactions (transaction_date)
      ''');

      // فهرس مركب لحركات المخزون على حقول warehouse_id و product_id و transaction_date
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_inventory_transactions_warehouse_product_date
        ON inventory_transactions (warehouse_id, product_id, transaction_date)
      ''');

      // فهرس لتحويلات المخزون على حقل source_warehouse_id
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_stock_transfers_source_warehouse_id
        ON stock_transfers (source_warehouse_id)
      ''');

      // فهرس لتحويلات المخزون على حقل destination_warehouse_id
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_stock_transfers_destination_warehouse_id
        ON stock_transfers (destination_warehouse_id)
      ''');

      // فهرس لتحويلات المخزون على حقل date
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_stock_transfers_date
        ON stock_transfers (date)
      ''');

      // فهرس لتحويلات المخزون على حقل status
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_stock_transfers_status
        ON stock_transfers (status)
      ''');

      // فهرس للمنتجات على حقل min_quantity لتحسين استعلامات المنتجات منخفضة المخزون
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_products_min_quantity
        ON products (min_quantity)
      ''');

      // فهرس للمنتجات على حقل category_id لتحسين استعلامات تصنيف المنتجات
      await _db.rawQuery('''
        CREATE INDEX IF NOT EXISTS idx_products_category_id
        ON products (category_id)
      ''');
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في إنشاء فهارس المخزون',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// تحميل حركات المخزون للمنتج
  Future<void> loadProductTransactions(String productId,
      {String? warehouseId}) async {
    try {
      _isLoading = true;
      notifyListeners();

      final transactions = await _inventoryService.getProductTransactions(
        productId,
        warehouseId: warehouseId,
      );
      _transactions = transactions;

      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل حركات المخزون: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل حركات المخزون للمنتج',
        error: e,
        stackTrace: stackTrace,
        context: {'productId': productId, 'warehouseId': warehouseId},
      );
      notifyListeners();
    }
  }

  /// تعديل كمية المخزون
  Future<bool> adjustInventory(
    String productId,
    String warehouseId,
    double quantity,
    InventoryTransactionType type, {
    String? referenceId,
    String? notes,
    String? userId,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      final success = await _inventoryService.updateProductQuantity(
        productId,
        warehouseId,
        quantity,
        type,
        referenceId: referenceId,
        notes: notes,
        userId: userId,
      );

      if (success) {
        // تحديث المخزون
        await loadWarehouseInventory(warehouseId);
      } else {
        _errorMessage = 'فشل في تعديل كمية المخزون';
      }

      _isLoading = false;
      notifyListeners();

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تعديل كمية المخزون: $e';
      ErrorTracker.captureError(
        'خطأ في تعديل كمية المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {
          'productId': productId,
          'warehouseId': warehouseId,
          'quantity': quantity.toString(),
          'type': type.toString(),
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// نقل المنتج بين المستودعات
  Future<bool> transferProduct(
    String productId,
    String fromWarehouseId,
    String toWarehouseId,
    double quantity, {
    String? referenceId,
    String? notes,
    String? userId,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      final success = await _inventoryService.transferProduct(
        productId,
        fromWarehouseId,
        toWarehouseId,
        quantity,
        referenceId: referenceId,
        notes: notes,
        userId: userId,
      );

      if (success) {
        // تحديث المخزون إذا كان المستودع المحدد هو أحد المستودعات المعنية
        if (_selectedWarehouse != null &&
            (_selectedWarehouse!.id == fromWarehouseId ||
                _selectedWarehouse!.id == toWarehouseId)) {
          await loadWarehouseInventory(_selectedWarehouse!.id);
        }
      } else {
        _errorMessage = 'فشل في نقل المنتج بين المستودعات';
      }

      _isLoading = false;
      notifyListeners();

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء نقل المنتج بين المستودعات: $e';
      ErrorTracker.captureError(
        'خطأ في نقل المنتج بين المستودعات',
        error: e,
        stackTrace: stackTrace,
        context: {
          'productId': productId,
          'fromWarehouseId': fromWarehouseId,
          'toWarehouseId': toWarehouseId,
          'quantity': quantity.toString(),
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// الحصول على اسم المنتج بواسطة المعرف
  String getProductName(String productId) {
    final product = _products.firstWhere(
      (p) => p.id == productId,
      orElse: () => Product(id: productId, name: 'منتج غير معروف', code: ''),
    );
    return product.name;
  }

  /// الحصول على اسم المستودع بواسطة المعرف
  String getWarehouseName(String warehouseId) {
    final warehouse = _warehouses.firstWhere(
      (w) => w.id == warehouseId,
      orElse: () =>
          Warehouse(id: warehouseId, name: 'مستودع غير معروف', code: ''),
    );
    return warehouse.name;
  }

  /// الحصول على المستودع بواسطة المعرف
  Warehouse? getWarehouseById(String warehouseId) {
    try {
      return _warehouses.firstWhere((w) => w.id == warehouseId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على المنتج بواسطة المعرف
  Product? getProductById(String productId) {
    try {
      return _products.firstWhere((p) => p.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// تحميل المنتجات منخفضة المخزون
  /// تم تحسين الاستعلام باستخدام الفهارس المناسبة
  Future<List<Map<String, dynamic>>> loadLowStockProducts() async {
    if (_selectedWarehouse == null) return [];

    try {
      // التأكد من وجود الفهارس لتحسين أداء الاستعلام
      await _createInventoryIndexes();

      // بناء استعلام SQL للحصول على المنتجات منخفضة المخزون
      // تم تحسين الاستعلام باستخدام الفهارس المناسبة
      // تم تحسين الاستعلام بإضافة EXPLAIN QUERY PLAN للتحقق من استخدام الفهارس

      // استعلام محسن يستخدم الفهارس الجديدة
      final results = await _db.rawQuery('''
        SELECT
          i.product_id,
          p.name as product_name,
          p.barcode,
          p.code,
          p.min_quantity as min_stock,
          i.quantity,
          p.sale_price,
          p.purchase_price,
          c.name as category_name,
          (i.quantity / p.min_quantity * 100) as stock_percentage
        FROM
          inventory i
        JOIN
          products p ON i.product_id = p.id AND p.is_deleted = 0 AND p.min_quantity > 0
        LEFT JOIN
          categories c ON p.category_id = c.id
        WHERE
          i.warehouse_id = ?
          AND i.quantity <= p.min_quantity
        ORDER BY
          (i.quantity / p.min_quantity) ASC, p.name ASC
      ''', [_selectedWarehouse!.id]);

      // تحويل النتائج لتضمين معلومات إضافية
      final enhancedResults = results.map((item) {
        // حساب النسبة المئوية للمخزون
        final quantity = item['quantity'] as double? ?? 0.0;
        final minStock = item['min_stock'] as double? ?? 1.0;
        final stockPercentage = (quantity / minStock * 100).toInt();

        // تحديد حالة المخزون
        String stockStatus;
        if (quantity <= 0) {
          stockStatus = 'نافد';
        } else if (quantity <= minStock * 0.5) {
          stockStatus = 'منخفض جداً';
        } else {
          stockStatus = 'منخفض';
        }

        return {
          ...item,
          'stock_percentage': stockPercentage,
          'stock_status': stockStatus,
        };
      }).toList();

      return enhancedResults;
    } catch (e, stackTrace) {
      _errorMessage = 'فشل تحميل المنتجات منخفضة المخزون: ${e.toString()}';
      ErrorTracker.captureError(
        'خطأ في تحميل المنتجات منخفضة المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {'warehouseId': _selectedWarehouse?.id},
      );
      return [];
    }
  }

  /// تحميل تحويلات المخزون
  /// تم تحسين الاستعلام باستخدام الفهارس المناسبة
  Future<void> loadStockTransfers({
    String? status,
    String? type,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      // التأكد من وجود الفهارس لتحسين أداء الاستعلام
      await _createInventoryIndexes();

      // بناء استعلام SQL
      String query = '''
        SELECT * FROM stock_transfers
        WHERE is_deleted = 0
      ''';

      List<dynamic> args = [];

      // إضافة شرط الحالة إذا كان موجوداً
      if (status != null) {
        query += ' AND status = ?';
        args.add(status);
      }

      // إضافة شرط النوع إذا كان موجوداً
      if (type != null) {
        query += ' AND type = ?';
        args.add(type);
      }

      // إضافة شرط المخزن إذا كان موجوداً
      if (_selectedWarehouse?.id != null) {
        query +=
            ' AND (source_warehouse_id = ? OR destination_warehouse_id = ?)';
        args.add(_selectedWarehouse!.id);
        args.add(_selectedWarehouse!.id);
      }

      query += ' ORDER BY date DESC';

      // تنفيذ الاستعلام
      final results = await _db.rawQuery(query, args);

      // تحويل النتائج إلى كائنات StockTransfer
      _stockTransfers =
          results.map((item) => StockTransfer.fromMap(item)).toList();

      // تحميل عناصر كل تحويل باستخدام استعلام واحد لكل تحويل
      for (var i = 0; i < _stockTransfers.length; i++) {
        final transferId = _stockTransfers[i].id;
        // تم إزالة فحص null لأن المعرف لا يمكن أن يكون null في النموذج الجديد
        {
          final itemsResults = await _db.rawQuery('''
            SELECT * FROM stock_transfer_items
            WHERE transfer_id = ? AND is_deleted = 0
            ORDER BY id ASC
          ''', [transferId]);

          final items = itemsResults
              .map((item) => StockTransferItem.fromMap(item))
              .toList();
          _stockTransfers[i] = _stockTransfers[i].copyWith(items: items);
        }
      }

      _errorMessage = null;
    } catch (e, stackTrace) {
      _errorMessage = 'فشل تحميل تحويلات المخزون: ${e.toString()}';
      ErrorTracker.captureError(
        'خطأ في تحميل تحويلات المخزون',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// إنشاء تحويل مخزون جديد
  Future<bool> createStockTransfer(StockTransfer transfer) async {
    _isLoading = true;
    notifyListeners();

    try {
      // إنشاء رقم مرجعي إذا لم يكن موجوداً
      String reference = transfer.reference ?? '';
      if (reference.isEmpty) {
        // إنشاء رقم مرجعي جديد
        final timestamp =
            DateTime.now().millisecondsSinceEpoch.toString().substring(0, 10);
        reference = 'TRF-$timestamp';
      }

      // إنشاء معرف جديد إذا لم يكن موجوداً
      final id = transfer.id.isEmpty ? const Uuid().v4() : transfer.id;

      // إعداد بيانات التحويل
      final now = DateTime.now().toIso8601String();
      final transferData = {
        ...transfer.toMap(),
        'id': id,
        'reference': reference,
        'created_at': now,
        'updated_at': now,
      };

      // تنفيذ استعلام SQL لإضافة التحويل
      await _db.rawQuery('''
        INSERT INTO stock_transfers (
          id, source_warehouse_id, destination_warehouse_id, date, status, type,
          notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      ''', [
        transferData['id'],
        transferData['source_warehouse_id'],
        transferData['destination_warehouse_id'],
        transferData['date'],
        transferData['status'],
        transferData['type'],
        transferData['notes'],
        transferData['created_at'],
        transferData['updated_at'],
      ]);

      // إضافة عناصر التحويل
      if (transfer.items != null) {
        for (var item in transfer.items!) {
          final itemId = const Uuid().v4();
          await _db.rawQuery('''
            INSERT INTO stock_transfer_items (
              id, transfer_id, product_id, product_name, quantity, unit_id,
              unit_name, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          ''', [
            itemId,
            id,
            item.productId,
            item.productName,
            item.quantity,
            item.unitId,
            item.unitName,
            now,
            now,
          ]);

          // تحديث المخزون في المستودعات
          if (transfer.status == 'completed') {
            // تقليل الكمية من المستودع المصدر
            await _updateInventoryQuantity(
              transfer.sourceWarehouseId,
              item.productId,
              -item.quantity,
            );

            // زيادة الكمية في المستودع الوجهة
            await _updateInventoryQuantity(
              transfer.destinationWarehouseId,
              item.productId,
              item.quantity,
            );
          }
        }
      }

      // إعادة تحميل التحويلات
      await loadStockTransfers();

      _errorMessage = null;
      return true;
    } catch (e, stackTrace) {
      _errorMessage = 'فشل إنشاء تحويل المخزون: ${e.toString()}';
      ErrorTracker.captureError(
        'خطأ في إنشاء تحويل المخزون',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحديث كمية المخزون باستخدام استعلام SQL مباشر
  Future<bool> _updateInventoryQuantity(
    String warehouseId,
    String productId,
    double quantityChange,
  ) async {
    try {
      // التحقق من وجود المنتج في المخزون
      final existingInventory = await _db.rawQuery(
        'SELECT * FROM inventory WHERE warehouse_id = ? AND product_id = ?',
        [warehouseId, productId],
      );

      final now = DateTime.now().toIso8601String();

      if (existingInventory.isEmpty) {
        // إذا لم يكن المنتج موجوداً في المخزون، نقوم بإضافته
        await _db.rawQuery(
          '''
          INSERT INTO inventory (
            warehouse_id, product_id, quantity, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?)
          ''',
          [warehouseId, productId, quantityChange, now, now],
        );
      } else {
        // إذا كان المنتج موجوداً، نقوم بتحديث الكمية
        await _db.rawQuery(
          '''
          UPDATE inventory
          SET quantity = quantity + ?, updated_at = ?
          WHERE warehouse_id = ? AND product_id = ?
          ''',
          [quantityChange, now, warehouseId, productId],
        );
      }

      // تسجيل حركة المخزون
      final transactionId = const Uuid().v4();
      await _db.rawQuery(
        '''
        INSERT INTO inventory_transactions (
          id, warehouse_id, product_id, quantity, type, date, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''',
        [
          transactionId,
          warehouseId,
          productId,
          quantityChange.abs(),
          quantityChange > 0 ? 'in' : 'out',
          now,
          now,
          now,
        ],
      );

      return true;
    } catch (e, stackTrace) {
      _errorMessage = 'فشل تحديث كمية المخزون: ${e.toString()}';
      ErrorTracker.captureError(
        'خطأ في تحديث كمية المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {
          'warehouseId': warehouseId,
          'productId': productId,
          'quantityChange': quantityChange.toString(),
        },
      );
      return false;
    }
  }
}
