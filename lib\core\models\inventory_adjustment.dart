import 'package:uuid/uuid.dart';
import 'base_model.dart';
import 'inventory_adjustment_item.dart';

/// نموذج تعديل المخزون
class InventoryAdjustment extends BaseModel {
  final String? referenceNumber;
  final DateTime date;
  final String warehouseId;
  final String adjustmentType; // 'increase', 'decrease', 'inventory'
  final String? notes;
  final List<InventoryAdjustmentItem> items;

  InventoryAdjustment({
    String? id,
    this.referenceNumber,
    required this.date,
    required this.warehouseId,
    required this.adjustmentType,
    this.notes,
    this.items = const [],
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا التعديل مع استبدال الحقول المحددة بقيم جديدة
  InventoryAdjustment copyWith({
    String? id,
    String? referenceNumber,
    DateTime? date,
    String? warehouseId,
    String? adjustmentType,
    String? notes,
    List<InventoryAdjustmentItem>? items,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return InventoryAdjustment(
      id: id ?? this.id,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      date: date ?? this.date,
      warehouseId: warehouseId ?? this.warehouseId,
      adjustmentType: adjustmentType ?? this.adjustmentType,
      notes: notes ?? this.notes,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل التعديل إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'reference_number': referenceNumber,
      'date': date.toIso8601String(),
      'warehouse_id': warehouseId,
      'adjustment_type': adjustmentType,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء تعديل من Map
  factory InventoryAdjustment.fromMap(Map<String, dynamic> map) {
    return InventoryAdjustment(
      id: map['id'],
      referenceNumber: map['reference_number'],
      date: map['date'] != null ? DateTime.parse(map['date']) : DateTime.now(),
      warehouseId: map['warehouse_id'],
      adjustmentType: map['adjustment_type'],
      notes: map['notes'],
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  @override
  String toString() {
    return 'InventoryAdjustment(id: $id, referenceNumber: $referenceNumber, date: $date, adjustmentType: $adjustmentType)';
  }
}
