import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج المورد
/// تم توحيده من جميع نماذج المورد في المشروع
class Supplier extends BaseModel {
  // معلومات أساسية
  final String name;
  final String? code;
  final String? taxNumber;
  final bool isActive;

  // معلومات الاتصال
  final String? phone;
  final String? email;
  final String? address;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;

  // معلومات مالية
  final double balance;
  final double? openingBalance;
  final String? paymentTerms;
  final String? accountId;

  // معلومات إضافية
  final String? notes;
  final Map<String, dynamic>? metadata;

  Supplier({
    String? id,
    required this.name,
    this.code,
    this.taxNumber,
    this.isActive = true,
    this.phone,
    this.email,
    this.address,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.balance = 0.0,
    this.openingBalance,
    this.paymentTerms,
    this.accountId,
    this.notes,
    this.metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا المورد مع استبدال الحقول المحددة بقيم جديدة
  Supplier copyWith({
    String? id,
    String? name,
    String? code,
    String? taxNumber,
    bool? isActive,
    String? phone,
    String? email,
    String? address,
    String? city,
    String? state,
    String? country,
    String? postalCode,
    double? balance,
    double? openingBalance,
    String? paymentTerms,
    String? accountId,
    String? notes,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Supplier(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      taxNumber: taxNumber ?? this.taxNumber,
      isActive: isActive ?? this.isActive,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      balance: balance ?? this.balance,
      openingBalance: openingBalance ?? this.openingBalance,
      paymentTerms: paymentTerms ?? this.paymentTerms,
      accountId: accountId ?? this.accountId,
      notes: notes ?? this.notes,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل المورد إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'tax_number': taxNumber,
      'is_active': isActive ? 1 : 0,
      'phone': phone,
      'email': email,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'postal_code': postalCode,
      'balance': balance,
      'opening_balance': openingBalance,
      'payment_terms': paymentTerms,
      'account_id': accountId,
      'notes': notes,
      'metadata': metadata != null ? _encodeMetadata(metadata!) : null,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء مورد من Map
  factory Supplier.fromMap(Map<String, dynamic> map) {
    return Supplier(
      id: map['id'],
      name: map['name'] ?? '',
      code: map['code'],
      taxNumber: map['tax_number'],
      isActive: map['is_active'] == 1 || map['is_active'] == true,
      phone: map['phone'],
      email: map['email'],
      address: map['address'],
      city: map['city'],
      state: map['state'],
      country: map['country'],
      postalCode: map['postal_code'],
      balance: map['balance'] is int
          ? (map['balance'] as int).toDouble()
          : (map['balance'] as double? ?? 0.0),
      openingBalance: map['opening_balance'] != null
          ? (map['opening_balance'] is int
              ? (map['opening_balance'] as int).toDouble()
              : map['opening_balance'] as double)
          : null,
      paymentTerms: map['payment_terms'],
      accountId: map['account_id'],
      notes: map['notes'],
      metadata:
          map['metadata'] != null ? _decodeMetadata(map['metadata']) : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1 || map['is_deleted'] == true,
    );
  }

  /// تشفير البيانات الوصفية
  static String _encodeMetadata(Map<String, dynamic> metadata) {
    try {
      return jsonEncode(metadata);
    } catch (e) {
      return '{}';
    }
  }

  /// فك تشفير البيانات الوصفية
  static Map<String, dynamic> _decodeMetadata(String metadataString) {
    try {
      return jsonDecode(metadataString) as Map<String, dynamic>;
    } catch (e) {
      return {};
    }
  }

  /// تحويل المورد إلى JSON
  String toJson() {
    return jsonEncode(toMap());
  }

  /// إنشاء مورد من JSON
  factory Supplier.fromJson(String source) {
    return Supplier.fromMap(jsonDecode(source));
  }

  @override
  String toString() {
    return 'Supplier(id: $id, name: $name, phone: $phone, email: $email, balance: $balance)';
  }
}
