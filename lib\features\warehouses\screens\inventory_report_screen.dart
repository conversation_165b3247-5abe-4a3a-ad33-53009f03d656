import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/models/inventory_transaction.dart';
import '../../../core/models/product.dart';
import '../../../core/models/warehouse.dart';

import '../../../core/widgets/index.dart';
import '../../products/presenters/product_presenter.dart';
import '../presenters/warehouse_presenter.dart';
import '../../../core/theme/index.dart';

/// شاشة تقارير المخزون المحسنة
class InventoryReportScreen extends StatefulWidget {
  const InventoryReportScreen({Key? key}) : super(key: key);

  @override
  State<InventoryReportScreen> createState() => _InventoryReportScreenState();
}

class _InventoryReportScreenState extends State<InventoryReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  // المقدمون
  late ProductPresenter _productPresenter;
  late WarehousePresenter _warehousePresenter;

  // المستودع المحدد
  Warehouse? _selectedWarehouse;
  String? _selectedWarehouseId;

  // المنتج المحدد
  Product? _selectedProduct;

  // نطاق التاريخ
  DateTime? _fromDate;
  DateTime? _toDate;

  // نوع التقرير المحدد
  String _selectedReportType =
      'current_stock'; // 'current_stock', 'transactions', 'product_movement'

  // حالة التحميل
  bool _isLoading = false;

  // بيانات التقرير
  List<Map<String, dynamic>> _reportData = [];

  // مؤشر للعنصر المحدد في الرسم البياني
  int _touchedIndex = -1;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // تهيئة المقدمين
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
        () => ProductPresenter());
    _warehousePresenter = AppProviders.getLazyPresenter<WarehousePresenter>(
        () => WarehousePresenter());

    // تحميل البيانات
    _loadData();

    // تعيين الاستماع لتغيير علامة التبويب
    _tabController.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// معالجة تغيير علامة التبويب
  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      setState(() {
        switch (_tabController.index) {
          case 0:
            _selectedReportType = 'current_stock';
            break;
          case 1:
            _selectedReportType = 'transactions';
            break;
          case 2:
            _selectedReportType = 'product_movement';
            break;
        }
      });
      _generateReport();
    }
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    await _warehousePresenter.loadWarehouses();
    await _productPresenter.loadProducts();

    setState(() {
      _isLoading = false;
    });

    // توليد التقرير الافتراضي
    _generateReport();
  }

  /// توليد التقرير
  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
      _reportData = [];
    });

    try {
      switch (_selectedReportType) {
        case 'current_stock':
          await _generateCurrentStockReport();
          break;
        case 'transactions':
          await _generateTransactionsReport();
          break;
        case 'product_movement':
          await _generateProductMovementReport();
          break;
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء توليد التقرير: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// توليد تقرير المخزون الحالي
  Future<void> _generateCurrentStockReport() async {
    // في الإصدار الكامل، سيتم استدعاء الدالة الفعلية مع معرفات المستودع والمنتج
    // final stockData = await _inventoryPresenter.getCurrentStock(
    //   warehouseId: _selectedWarehouse?.id,
    //   productId: _selectedProduct?.id,
    // );

    // بيانات تجريبية للعرض
    final stockData = [
      {
        'product_id': '1',
        'warehouse_id': '1',
        'quantity': 100.0,
        'last_updated': DateTime.now().toIso8601String(),
      },
      {
        'product_id': '2',
        'warehouse_id': '1',
        'quantity': 50.0,
        'last_updated': DateTime.now().toIso8601String(),
      },
      {
        'product_id': '3',
        'warehouse_id': '2',
        'quantity': 75.0,
        'last_updated': DateTime.now().toIso8601String(),
      },
    ];

    // تحويل البيانات إلى النموذج المطلوب
    _reportData = stockData.map((item) {
      final productId = item['product_id'] as String;
      final warehouseId = item['warehouse_id'] as String;
      final quantity = item['quantity'] as double;
      final lastUpdated = item['last_updated'] != null
          ? DateTime.parse(item['last_updated'] as String)
          : null;

      // الحصول على المنتج والمستودع
      final product = _productPresenter.getProductById(productId);
      // استخدام الطريقة المناسبة للحصول على المستودع
      final warehouse = _warehousePresenter.warehouses.firstWhere(
        (w) => w.id == warehouseId,
        orElse: () => Warehouse(id: warehouseId, name: 'غير معروف'),
      );

      return {
        'product': product,
        'warehouse': warehouse,
        'quantity': quantity,
        'last_updated': lastUpdated,
      };
    }).toList();
  }

  /// توليد تقرير الحركات
  Future<void> _generateTransactionsReport() async {
    // في الإصدار الكامل، سيتم استدعاء الدالة الفعلية مع معرفات المستودع والمنتج
    // final transactions = await _inventoryPresenter.getInventoryTransactions(
    //   warehouseId: _selectedWarehouse?.id,
    //   productId: _selectedProduct?.id,
    //   fromDate: _fromDate,
    //   toDate: _toDate,
    // );

    // بيانات تجريبية للعرض
    final transactions = [
      {
        'id': '1',
        'product_id': '1',
        'warehouse_id': '1',
        'transaction_type': 'in',
        'quantity': 100.0,
        'transaction_date': DateTime.now().subtract(const Duration(days: 10)),
        'reference_id': 'PO-001',
      },
      {
        'id': '2',
        'product_id': '1',
        'warehouse_id': '1',
        'transaction_type': 'out',
        'quantity': 20.0,
        'transaction_date': DateTime.now().subtract(const Duration(days: 5)),
        'reference_id': 'SO-001',
      },
      {
        'id': '3',
        'product_id': '2',
        'warehouse_id': '1',
        'transaction_type': 'in',
        'quantity': 50.0,
        'transaction_date': DateTime.now().subtract(const Duration(days: 3)),
        'reference_id': 'PO-002',
      },
    ];

    // تحويل البيانات إلى النموذج المطلوب
    _reportData = transactions.map((item) {
      final productId = item['product_id'] as String;
      final warehouseId = item['warehouse_id'] as String;
      final transactionType = item['transaction_type'] as String;
      final quantity = item['quantity'] as double;
      final transactionDate = item['transaction_date'] as DateTime;
      final referenceId = item['reference_id'] as String?;

      // الحصول على المنتج والمستودع
      final product = _productPresenter.getProductById(productId);
      // استخدام الطريقة المناسبة للحصول على المستودع
      final warehouse = _warehousePresenter.warehouses.firstWhere(
        (w) => w.id == warehouseId,
        orElse: () => Warehouse(id: warehouseId, name: 'غير معروف'),
      );

      // إنشاء كائن المعاملة - تعديل لتجنب مشكلة نوع البيانات
      final transaction = InventoryTransaction(
        id: item['id'] as String,
        productId: productId,
        warehouseId: warehouseId,
        // استخدام القيم المناسبة لنوع المعاملة
        transactionType: transactionType == 'in'
            ? InventoryTransactionType.purchase
            : InventoryTransactionType.sale,
        quantity: quantity,
        transactionDate: transactionDate,
        referenceId: referenceId,
      );

      return {
        'transaction': transaction,
        'product': product,
        'warehouse': warehouse,
      };
    }).toList();
  }

  /// توليد تقرير حركة المنتج
  Future<void> _generateProductMovementReport() async {
    if (_selectedProduct == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار منتج لعرض حركته'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // بيانات تجريبية للعرض
    final movements = [
      {
        'date': DateTime.now().subtract(const Duration(days: 10)),
        'type': 'رصيد افتتاحي',
        'quantity_in': 100.0,
        'quantity_out': 0.0,
        'balance': 100.0,
        'reference': 'INIT-001',
      },
      {
        'date': DateTime.now().subtract(const Duration(days: 7)),
        'type': 'مبيعات',
        'quantity_in': 0.0,
        'quantity_out': 20.0,
        'balance': 80.0,
        'reference': 'SO-001',
      },
      {
        'date': DateTime.now().subtract(const Duration(days: 5)),
        'type': 'مشتريات',
        'quantity_in': 50.0,
        'quantity_out': 0.0,
        'balance': 130.0,
        'reference': 'PO-001',
      },
      {
        'date': DateTime.now().subtract(const Duration(days: 2)),
        'type': 'مبيعات',
        'quantity_in': 0.0,
        'quantity_out': 30.0,
        'balance': 100.0,
        'reference': 'SO-002',
      },
    ];

    _reportData = movements;
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  /// طباعة التقرير
  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة التقرير...'),
      ),
    );
    // سيتم تنفيذ طباعة التقرير في الإصدار القادم
  }

  /// تصدير التقرير
  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير...'),
      ),
    );
    // سيتم تنفيذ تصدير التقرير في الإصدار القادم
  }

  @override
  Widget build(BuildContext context) {
    return SafeLayout(
      title: 'تقارير المخزون',
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _generateReport,
          tooltip: 'تحديث',
        ),
        IconButton(
          icon: const Icon(Icons.print),
          onPressed: _printReport,
          tooltip: 'طباعة',
        ),
        IconButton(
          icon: const Icon(Icons.save_alt),
          onPressed: _exportReport,
          tooltip: 'تصدير',
        ),
      ],
      body: Column(
        children: [
          _buildFilters(),
          _buildTabs(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildCurrentStockTab(),
                      _buildTransactionsTab(),
                      _buildProductMovementTab(),
                    ],
                  ),
          ),
        ],
      ),
      child: Container(),
    );
  }

  /// بناء مرشحات البحث
  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    labelText: 'بحث',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    // تنفيذ البحث
                    _generateReport();
                  },
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: _showFilterDialog,
                icon: const Icon(Icons.filter_list),
                label: const Text('تصفية'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                ),
              ),
            ],
          ),
          if (_selectedWarehouse != null ||
              _selectedProduct != null ||
              _fromDate != null ||
              _toDate != null)
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  if (_selectedWarehouse != null)
                    Chip(
                      label: Text('المستودع: ${_selectedWarehouse!.name}'),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () {
                        setState(() {
                          _selectedWarehouse = null;
                          _selectedWarehouseId = null;
                        });
                        _generateReport();
                      },
                    ),
                  if (_selectedProduct != null)
                    Chip(
                      label: Text('المنتج: ${_selectedProduct!.name}'),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () {
                        setState(() {
                          _selectedProduct = null;
                        });
                        _generateReport();
                      },
                    ),
                  if (_fromDate != null)
                    Chip(
                      label: Text('من: ${_formatDate(_fromDate!)}'),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () {
                        setState(() {
                          _fromDate = null;
                        });
                        _generateReport();
                      },
                    ),
                  if (_toDate != null)
                    Chip(
                      label: Text('إلى: ${_formatDate(_toDate!)}'),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () {
                        setState(() {
                          _toDate = null;
                        });
                        _generateReport();
                      },
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// عرض مربع حوار الفلترة
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية التقرير'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اختيار المستودع
                AkDropdownInput<String>(
                  label: 'المستودع',
                  value: _selectedWarehouseId,
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('جميع المستودعات'),
                    ),
                    ..._warehousePresenter.warehouses
                        .map((warehouse) => DropdownMenuItem(
                              value: warehouse.id,
                              child: Text(warehouse.name),
                            ))
                        .toList(),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedWarehouseId = value;
                      _selectedWarehouse = value != null
                          ? _warehousePresenter.warehouses.firstWhere(
                              (w) => w.id == value,
                              orElse: () => Warehouse(
                                  id: value, name: 'مستودع غير معروف'),
                            )
                          : null;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // اختيار المنتج
                AkDropdownInput<String>(
                  label: 'المنتج',
                  value: _selectedProduct?.id,
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('جميع المنتجات'),
                    ),
                    ..._productPresenter.products
                        .map((product) => DropdownMenuItem(
                              value: product.id,
                              child: Text(product.name),
                            ))
                        .toList(),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedProduct = value != null
                          ? _productPresenter.products.firstWhere(
                              (p) => p.id == value,
                              orElse: () =>
                                  Product(id: value, name: 'منتج غير معروف'),
                            )
                          : null;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // اختيار نطاق التاريخ
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'من تاريخ',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        controller: TextEditingController(
                          text: _fromDate != null
                              ? '${_fromDate!.day}/${_fromDate!.month}/${_fromDate!.year}'
                              : '',
                        ),
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _fromDate ?? DateTime.now(),
                            firstDate: DateTime(2000),
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            setState(() {
                              _fromDate = date;
                            });
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'إلى تاريخ',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        controller: TextEditingController(
                          text: _toDate != null
                              ? '${_toDate!.day}/${_toDate!.month}/${_toDate!.year}'
                              : '',
                        ),
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _toDate ?? DateTime.now(),
                            firstDate: DateTime(2000),
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            setState(() {
                              _toDate = date;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _generateReport();
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  /// بناء علامات التبويب
  Widget _buildTabs() {
    return TabBar(
      controller: _tabController,
      tabs: const [
        Tab(text: 'المخزون الحالي'),
        Tab(text: 'حركات المخزون'),
        Tab(text: 'حركة المنتج'),
      ],
    );
  }

  /// بناء علامة تبويب المخزون الحالي
  Widget _buildCurrentStockTab() {
    if (_reportData.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات للعرض'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInventoryChart(_productPresenter.products),
          const SizedBox(height: 20),
          _buildInventoryTable(_productPresenter.products),
        ],
      ),
    );
  }

  /// بناء رسم بياني لتوزيع المخزون حسب الفئة
  Widget _buildInventoryChart(List<Product> products) {
    // تجميع البيانات للرسم البياني
    final Map<String, double> categoryData = {};

    for (var product in products) {
      String categoryName = 'بدون تصنيف';
      if (product.categoryId != null && product.categoryId!.isNotEmpty) {
        final name = _productPresenter.getCategoryName(product.categoryId!);
        if (name.isNotEmpty) {
          categoryName = name;
        }
      }
      final quantity = product.quantity;

      if (categoryData.containsKey(categoryName)) {
        categoryData[categoryName] = categoryData[categoryName]! + quantity;
      } else {
        categoryData[categoryName] = quantity;
      }
    }

    // تحويل البيانات إلى قطاعات الرسم البياني
    final List<PieChartSectionData> sections = [];
    final List<Color> colors = [
      AppColors.info,
      AppColors.success,
      AppColors.warning,
      AppColors.accent,
      AppColors.error,
      AppColors.teal,
      AppColors.amber,
      AppColors.indigo,
    ];

    int colorIndex = 0;
    categoryData.forEach((category, value) {
      final isTouched = sections.length == _touchedIndex;
      final fontSize = isTouched ? 20.0 : 16.0;
      final radius = isTouched ? 110.0 : 100.0;

      sections.add(
        PieChartSectionData(
          color: colors[colorIndex % colors.length],
          value: value,
          title: '${value.toInt()}',
          radius: radius,
          titleStyle: AppTypography(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: AppColors.lightTextSecondary,
          ),
        ),
      );

      colorIndex++;
    });

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع المخزون حسب الفئة',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: sections.isEmpty
                  ? const Center(child: Text('لا توجد بيانات للعرض'))
                  : Row(
                      children: [
                        Expanded(
                          child: PieChart(
                            PieChartData(
                              pieTouchData: PieTouchData(
                                touchCallback:
                                    (FlTouchEvent event, pieTouchResponse) {
                                  setState(() {
                                    if (!event.isInterestedForInteractions ||
                                        pieTouchResponse == null ||
                                        pieTouchResponse.touchedSection ==
                                            null) {
                                      _touchedIndex = -1;
                                      return;
                                    }
                                    _touchedIndex = pieTouchResponse
                                        .touchedSection!.touchedSectionIndex;
                                  });
                                },
                              ),
                              borderData: FlBorderData(show: false),
                              sectionsSpace: 2,
                              centerSpaceRadius: 0,
                              sections: sections,
                            ),
                          ),
                        ),
                        if (categoryData.isNotEmpty)
                          SizedBox(
                            width: 150,
                            child: _buildLegend(categoryData, colors),
                          ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مفتاح الرسم البياني
  Widget _buildLegend(Map<String, double> data, List<Color> colors) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: data.entries.map((entry) {
        final index = data.keys.toList().indexOf(entry.key);
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Container(
                width: 16,
                height: 16,
                color: colors[index % colors.length],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  entry.key,
                  style: const AppTypography(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// بناء علامة تبويب حركات المخزون
  Widget _buildTransactionsTab() {
    if (_reportData.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات للعرض'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: _buildInventoryTable(_productPresenter.products),
    );
  }

  /// بناء علامة تبويب حركة المنتج
  Widget _buildProductMovementTab() {
    if (_selectedProduct == null) {
      return const Center(
        child: Text('يرجى اختيار منتج لعرض حركته'),
      );
    }

    if (_reportData.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات للعرض'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'حركة المنتج: ${_selectedProduct!.name}',
            style: const AppTypography(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildInventoryTable(_productPresenter.products),
        ],
      ),
    );
  }

  /// بناء جدول المخزون
  Widget _buildInventoryTable(List<Product> products) {
    final columns = [
      const FinancialTableColumn(
        title: 'الباركود',
        field: 'barcode',
        alignment: ColumnAlignment.center,
      ),
      const FinancialTableColumn(
        title: 'اسم المنتج',
        field: 'name',
        alignment: ColumnAlignment.start,
      ),
      const FinancialTableColumn(
        title: 'التصنيف',
        field: 'category',
        alignment: ColumnAlignment.start,
      ),
      const FinancialTableColumn(
        title: 'الوحدة',
        field: 'unit',
        alignment: ColumnAlignment.center,
      ),
      const FinancialTableColumn(
        title: 'الكمية',
        field: 'quantity',
        alignment: ColumnAlignment.center,
        isNumeric: true,
      ),
      const FinancialTableColumn(
        title: 'الحد الأدنى',
        field: 'min_quantity',
        alignment: ColumnAlignment.center,
        isNumeric: true,
      ),
      const FinancialTableColumn(
        title: 'الحالة',
        field: 'status',
        alignment: ColumnAlignment.center,
      ),
    ];

    final data = products.map((product) {
      return {
        'id': product.id,
        'barcode': product.barcode ?? '',
        'name': product.name,
        'category': _productPresenter.getCategoryName(product.categoryId ?? ''),
        'unit': _productPresenter.getUnitName(product.unitId ?? ''),
        'quantity': product.quantity,
        'min_quantity': product.minStock,
        'status': _getProductStatus(product),
      };
    }).toList();

    return FinancialDataTable(
      columns: columns,
      data: data,
      customCellBuilder: (context, rowIndex, columnIndex, value, field) {
        if (field == 'status') {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(value),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              value,
              style: const AppTypography(color: AppColors.onPrimary),
            ),
          );
        } else if (field == 'quantity') {
          final product = products[rowIndex];
          final color = product.quantity <= product.minStock
              ? AppColors.error
              : AppColors.lightTextPrimary;

          return Text(
            value.toString(),
            style: AppTypography(
              color: color,
              fontWeight: product.quantity <= product.minStock
                  ? FontWeight.bold
                  : FontWeight.normal,
            ),
          );
        }
        return null;
      },
      showTotals: true,
      totalColumns: const ['quantity'],
    );
  }

  /// الحصول على حالة المنتج
  String _getProductStatus(Product product) {
    if (product.quantity <= 0) {
      return 'نافد';
    } else if (product.quantity <= product.minStock) {
      return 'منخفض';
    } else {
      return 'متوفر';
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(String status) {
    switch (status) {
      case 'نافد':
        return AppColors.error;
      case 'منخفض':
        return AppColors.warning;
      case 'متوفر':
        return AppColors.success;
      default:
        return AppColors.secondary;
    }
  }
}
