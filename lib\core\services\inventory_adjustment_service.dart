import '../database/database_service.dart';
import '../models/inventory_adjustment.dart';
import '../models/inventory_adjustment_item.dart';
import '../models/inventory_transaction.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';
import 'inventory_service.dart';

/// خدمة للعمليات المتعلقة بتعديلات المخزون
class InventoryAdjustmentService {
  // نمط Singleton
  static final InventoryAdjustmentService _instance =
      InventoryAdjustmentService._internal();
  factory InventoryAdjustmentService() => _instance;
  InventoryAdjustmentService._internal();

  final DatabaseService _db = DatabaseService.instance;
  final InventoryService _inventoryService = InventoryService();

  /// الحصول على جميع تعديلات المخزون
  Future<List<InventoryAdjustment>> getAllAdjustments({
    String? warehouseId,
    DateTime? fromDate,
    DateTime? toDate,
    String? adjustmentType,
    bool includeDeleted = false,
    int? limit,
    int? offset,
  }) async {
    try {
      AppLogger.info('الحصول على جميع تعديلات المخزون');

      // بناء شرط WHERE
      String whereClause = includeDeleted ? '1=1' : 'a.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (warehouseId != null) {
        whereClause += ' AND a.warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      if (fromDate != null) {
        whereClause += ' AND a.date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND a.date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      if (adjustmentType != null) {
        whereClause += ' AND a.adjustment_type = ?';
        whereArgs.add(adjustmentType);
      }

      // استعلام قاعدة البيانات
      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT a.*
        FROM ${DatabaseService.tableInventoryAdjustments} a
        WHERE $whereClause
        ORDER BY a.date DESC, a.created_at DESC
        ${limit != null ? 'LIMIT $limit' : ''}
        ${offset != null ? 'OFFSET $offset' : ''}
      ''', whereArgs);

      // تحويل إلى كائنات InventoryAdjustment
      final List<InventoryAdjustment> adjustments = [];
      for (final map in maps) {
        // الحصول على عناصر التعديل
        final items = await getAdjustmentItems(map['id']);

        // إنشاء كائن التعديل مع العناصر
        final adjustment = InventoryAdjustment.fromMap(map);
        adjustments.add(adjustment.copyWith(items: items));
      }

      return adjustments;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع تعديلات المخزون',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على تعديل مخزون بواسطة المعرف
  Future<InventoryAdjustment?> getAdjustmentById(String id) async {
    try {
      AppLogger.info('الحصول على تعديل مخزون بواسطة المعرف: $id');

      final List<Map<String, dynamic>> maps = await _db.query(
        DatabaseService.tableInventoryAdjustments,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isEmpty) {
        return null;
      }

      // الحصول على عناصر التعديل
      final items = await getAdjustmentItems(id);

      // إنشاء كائن التعديل مع العناصر
      final adjustment = InventoryAdjustment.fromMap(maps.first);
      return adjustment.copyWith(items: items);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على تعديل مخزون بواسطة المعرف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return null;
    }
  }

  /// الحصول على عناصر تعديل المخزون
  Future<List<InventoryAdjustmentItem>> getAdjustmentItems(
      String adjustmentId) async {
    try {
      AppLogger.info('الحصول على عناصر تعديل المخزون: $adjustmentId');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          i.*,
          p.name as product_name,
          p.code as product_code
        FROM ${DatabaseService.tableInventoryAdjustmentItems} i
        JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        WHERE i.adjustment_id = ? AND i.is_deleted = 0
        ORDER BY i.id
      ''', [adjustmentId]);

      // تحويل إلى كائنات InventoryAdjustmentItem
      return List.generate(maps.length, (i) {
        final map = maps[i];

        // إضافة معلومات المنتج إلى الخريطة
        if (map['product_name'] != null) {
          map['product'] = {
            'id': map['product_id'],
            'name': map['product_name'],
            'code': map['product_code'],
          };
        }

        return InventoryAdjustmentItem.fromMap(map);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على عناصر تعديل المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {'adjustmentId': adjustmentId},
      );
      return [];
    }
  }

  /// إضافة تعديل مخزون جديد
  Future<InventoryAdjustment?> addAdjustment(
      InventoryAdjustment adjustment, List<InventoryAdjustmentItem> items,
      {String? userId}) async {
    try {
      AppLogger.info('إضافة تعديل مخزون جديد');

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<InventoryAdjustment?>((txn) async {
        // إضافة التعديل
        final adjustmentMap = adjustment.toMap();

        // تعيين created_by إذا تم توفيره
        if (userId != null) {
          adjustmentMap['created_by'] = userId;
        }

        await txn.insert(
            DatabaseService.tableInventoryAdjustments, adjustmentMap);

        // إضافة عناصر التعديل
        for (final item in items) {
          final itemMap = item.copyWith(adjustmentId: adjustment.id).toMap();
          await txn.insert(
              DatabaseService.tableInventoryAdjustmentItems, itemMap);
        }

        // تحديث المخزون
        await _updateInventory(adjustment, items, userId: userId);

        // إرجاع التعديل مع العناصر
        return adjustment.copyWith(items: items);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة تعديل مخزون',
        error: e,
        stackTrace: stackTrace,
        context: {'adjustment': adjustment.toString()},
      );
      return null;
    }
  }

  /// تحديث تعديل مخزون موجود
  Future<bool> updateAdjustment(
      InventoryAdjustment adjustment, List<InventoryAdjustmentItem> items,
      {String? userId}) async {
    try {
      AppLogger.info('تحديث تعديل مخزون: ${adjustment.id}');

      // الحصول على التعديل الحالي وعناصره
      final currentAdjustment = await getAdjustmentById(adjustment.id);
      if (currentAdjustment == null) {
        AppLogger.warning('التعديل غير موجود: ${adjustment.id}');
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        // إلغاء تأثير التعديل الحالي على المخزون
        await _reverseInventoryUpdate(
            currentAdjustment, currentAdjustment.items);

        // تحديث التعديل
        final adjustmentMap = adjustment.toMap();

        // تعيين updated_at و updated_by
        adjustmentMap['updated_at'] = DateTime.now().toIso8601String();
        if (userId != null) {
          adjustmentMap['updated_by'] = userId;
        }

        await txn.update(
          DatabaseService.tableInventoryAdjustments,
          adjustmentMap,
          where: 'id = ?',
          whereArgs: [adjustment.id],
        );

        // حذف عناصر التعديل الحالية
        await txn.delete(
          DatabaseService.tableInventoryAdjustmentItems,
          where: 'adjustment_id = ?',
          whereArgs: [adjustment.id],
        );

        // إضافة عناصر التعديل الجديدة
        for (final item in items) {
          final itemMap = item.copyWith(adjustmentId: adjustment.id).toMap();
          await txn.insert(
              DatabaseService.tableInventoryAdjustmentItems, itemMap);
        }

        // تحديث المخزون
        await _updateInventory(adjustment, items, userId: userId);

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث تعديل مخزون',
        error: e,
        stackTrace: stackTrace,
        context: {'adjustment': adjustment.toString()},
      );
      return false;
    }
  }

  /// حذف تعديل مخزون (حذف منطقي)
  ///
  /// يقوم بحذف تعديل المخزون من قاعدة البيانات بشكل منطقي (soft delete)
  /// عن طريق تعيين حقل is_deleted إلى 1
  /// كما يقوم بإلغاء تأثير التعديل على المخزون
  ///
  /// المعاملات:
  /// - id: معرف التعديل المراد حذفه
  /// - userId: معرف المستخدم الذي يقوم بالحذف (اختياري)
  ///
  /// يعيد:
  /// - true: إذا تم الحذف بنجاح
  /// - false: إذا فشلت عملية الحذف
  Future<bool> deleteAdjustment(String id, {String? userId}) async {
    try {
      AppLogger.info('حذف تعديل مخزون: $id');

      // الحصول على التعديل وعناصره
      final adjustment = await getAdjustmentById(id);
      if (adjustment == null) {
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        // إلغاء تأثير التعديل على المخزون
        await _reverseInventoryUpdate(adjustment, adjustment.items);

        final now = DateTime.now().toIso8601String();

        // حذف التعديل (حذف منطقي)
        await txn.update(
          DatabaseService.tableInventoryAdjustments,
          {
            'is_deleted': 1,
            'updated_at': now,
            'updated_by': userId,
          },
          where: 'id = ?',
          whereArgs: [id],
        );

        // حذف عناصر التعديل (حذف منطقي)
        await txn.update(
          DatabaseService.tableInventoryAdjustmentItems,
          {
            'is_deleted': 1,
            'updated_at': now,
          },
          where: 'adjustment_id = ?',
          whereArgs: [id],
        );

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف تعديل مخزون',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// تحديث المخزون
  Future<void> _updateInventory(
      InventoryAdjustment adjustment, List<InventoryAdjustmentItem> items,
      {String? userId}) async {
    try {
      // تحديد نوع الحركة بناءً على نوع التعديل
      InventoryTransactionType transactionType;
      switch (adjustment.adjustmentType) {
        case 'increase':
          transactionType = InventoryTransactionType.adjustment;
          break;
        case 'decrease':
          transactionType = InventoryTransactionType.adjustment;
          break;
        case 'inventory':
          transactionType = InventoryTransactionType.count;
          break;
        default:
          transactionType = InventoryTransactionType.adjustment;
      }

      // إجراء تعديل لكل منتج
      for (final item in items) {
        double quantity = item.quantity;

        // تعديل الكمية بناءً على نوع التعديل
        if (adjustment.adjustmentType == 'decrease') {
          quantity = -quantity; // جعل الكمية سالبة للنقصان
        }

        // تحديث المخزون
        await _inventoryService.updateProductQuantity(
          item.productId,
          adjustment.warehouseId,
          quantity,
          transactionType,
          referenceId: adjustment.id,
          referenceType: 'adjustment',
          notes: adjustment.notes,
          userId: userId,
        );
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {'adjustmentId': adjustment.id},
      );
      rethrow;
    }
  }

  /// إلغاء تأثير التعديل على المخزون
  Future<void> _reverseInventoryUpdate(
    InventoryAdjustment adjustment,
    List<InventoryAdjustmentItem> items,
  ) async {
    try {
      // تحديد نوع الحركة بناءً على نوع التعديل
      InventoryTransactionType transactionType;
      switch (adjustment.adjustmentType) {
        case 'increase':
          transactionType = InventoryTransactionType.adjustment;
          break;
        case 'decrease':
          transactionType = InventoryTransactionType.adjustment;
          break;
        case 'inventory':
          transactionType = InventoryTransactionType.count;
          break;
        default:
          transactionType = InventoryTransactionType.adjustment;
      }

      // إلغاء تأثير التعديل لكل منتج
      for (final item in items) {
        double quantity = item.quantity;

        // عكس الكمية بناءً على نوع التعديل
        if (adjustment.adjustmentType == 'increase') {
          quantity = -quantity; // جعل الكمية سالبة لإلغاء الزيادة
        } else if (adjustment.adjustmentType == 'decrease') {
          // لا تغيير في الكمية لإلغاء النقصان (ستكون موجبة)
        } else if (adjustment.adjustmentType == 'inventory') {
          // في حالة الجرد، نحتاج إلى الحصول على الكمية السابقة
          // هذا معقد ويتطلب سجل للكميات السابقة
          // لذلك نتجاهله في هذا المثال
          continue;
        }

        // تحديث المخزون
        await _inventoryService.updateProductQuantity(
          item.productId,
          adjustment.warehouseId,
          quantity,
          transactionType,
          referenceId: adjustment.id,
          referenceType: 'adjustment_reverse',
          notes: 'إلغاء تأثير التعديل: ${adjustment.referenceNumber}',
        );
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إلغاء تأثير التعديل على المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {'adjustmentId': adjustment.id},
      );
      rethrow;
    }
  }

  /// البحث عن تعديلات المخزون
  ///
  /// يقوم بالبحث في تعديلات المخزون في قاعدة البيانات باستخدام نص البحث المدخل
  /// ويبحث في:
  /// - رقم المرجع (reference_number)
  /// - الملاحظات (notes)
  /// - اسم المستودع (warehouse name)
  ///
  /// المعاملات:
  /// - query: نص البحث المراد البحث عنه
  ///
  /// يعيد:
  /// - قائمة بتعديلات المخزون التي تطابق معايير البحث
  /// - قائمة فارغة إذا لم يتم العثور على نتائج أو حدث خطأ
  Future<List<InventoryAdjustment>> searchAdjustments(String query) async {
    try {
      AppLogger.info('البحث عن تعديلات المخزون: $query');

      // تنظيف وإعداد معايير البحث
      final searchTerm = query.trim().toLowerCase();
      if (searchTerm.isEmpty) {
        return await getAllAdjustments();
      }

      // بناء استعلام البحث
      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT a.*
        FROM ${DatabaseService.tableInventoryAdjustments} a
        LEFT JOIN ${DatabaseService.tableWarehouses} w ON a.warehouse_id = w.id
        WHERE a.is_deleted = 0 AND (
          a.reference_number LIKE ? OR
          a.notes LIKE ? OR
          w.name LIKE ?
        )
        ORDER BY a.date DESC, a.created_at DESC
      ''', ['%$searchTerm%', '%$searchTerm%', '%$searchTerm%']);

      // تحويل إلى كائنات InventoryAdjustment
      final List<InventoryAdjustment> adjustments = [];
      for (final map in maps) {
        // الحصول على عناصر التعديل
        final items = await getAdjustmentItems(map['id']);

        // إنشاء كائن التعديل مع العناصر
        final adjustment = InventoryAdjustment.fromMap(map);
        adjustments.add(adjustment.copyWith(items: items));
      }

      return adjustments;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في البحث عن تعديلات المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {'query': query},
      );
      return [];
    }
  }
}
