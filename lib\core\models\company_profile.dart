import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج ملف الشركة الموحد
class CompanyProfile extends BaseModel {
  // معلومات أساسية
  final String name;
  final String? logo;
  final String? slogan;
  final String? description;
  
  // معلومات الاتصال
  final String? address;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;
  final String? phone;
  final String? email;
  final String? website;
  
  // معلومات قانونية
  final String? taxNumber;
  final String? commercialRegister;
  final String? legalName;
  
  // معلومات العملة
  final String? currency;
  final String? currencySymbol;
  final bool currencySymbolOnLeft;
  final int decimalPlaces;
  final String? thousandsSeparator;
  final String? decimalSeparator;
  
  // معلومات الفواتير
  final String? receiptHeader;
  final String? receiptFooter;
  final String? invoiceTerms;
  final String? invoiceNotes;
  
  // معلومات إضافية
  final Map<String, dynamic>? metadata;

  CompanyProfile({
    String? id,
    required this.name,
    this.logo,
    this.slogan,
    this.description,
    this.address,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.phone,
    this.email,
    this.website,
    this.taxNumber,
    this.commercialRegister,
    this.legalName,
    this.currency,
    this.currencySymbol,
    this.currencySymbolOnLeft = false,
    this.decimalPlaces = 2,
    this.thousandsSeparator = ',',
    this.decimalSeparator = '.',
    this.receiptHeader,
    this.receiptFooter,
    this.invoiceTerms,
    this.invoiceNotes,
    this.metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من ملف الشركة مع استبدال الحقول المحددة بقيم جديدة
  CompanyProfile copyWith({
    String? id,
    String? name,
    String? logo,
    String? slogan,
    String? description,
    String? address,
    String? city,
    String? state,
    String? country,
    String? postalCode,
    String? phone,
    String? email,
    String? website,
    String? taxNumber,
    String? commercialRegister,
    String? legalName,
    String? currency,
    String? currencySymbol,
    bool? currencySymbolOnLeft,
    int? decimalPlaces,
    String? thousandsSeparator,
    String? decimalSeparator,
    String? receiptHeader,
    String? receiptFooter,
    String? invoiceTerms,
    String? invoiceNotes,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return CompanyProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      logo: logo ?? this.logo,
      slogan: slogan ?? this.slogan,
      description: description ?? this.description,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      website: website ?? this.website,
      taxNumber: taxNumber ?? this.taxNumber,
      commercialRegister: commercialRegister ?? this.commercialRegister,
      legalName: legalName ?? this.legalName,
      currency: currency ?? this.currency,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      currencySymbolOnLeft: currencySymbolOnLeft ?? this.currencySymbolOnLeft,
      decimalPlaces: decimalPlaces ?? this.decimalPlaces,
      thousandsSeparator: thousandsSeparator ?? this.thousandsSeparator,
      decimalSeparator: decimalSeparator ?? this.decimalSeparator,
      receiptHeader: receiptHeader ?? this.receiptHeader,
      receiptFooter: receiptFooter ?? this.receiptFooter,
      invoiceTerms: invoiceTerms ?? this.invoiceTerms,
      invoiceNotes: invoiceNotes ?? this.invoiceNotes,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل ملف الشركة إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'logo': logo,
      'slogan': slogan,
      'description': description,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'postal_code': postalCode,
      'phone': phone,
      'email': email,
      'website': website,
      'tax_number': taxNumber,
      'commercial_register': commercialRegister,
      'legal_name': legalName,
      'currency': currency,
      'currency_symbol': currencySymbol,
      'currency_symbol_on_left': currencySymbolOnLeft ? 1 : 0,
      'decimal_places': decimalPlaces,
      'thousands_separator': thousandsSeparator,
      'decimal_separator': decimalSeparator,
      'receipt_header': receiptHeader,
      'receipt_footer': receiptFooter,
      'invoice_terms': invoiceTerms,
      'invoice_notes': invoiceNotes,
      'metadata': metadata != null ? jsonEncode(metadata) : null,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء ملف شركة من Map
  factory CompanyProfile.fromMap(Map<String, dynamic> map) {
    return CompanyProfile(
      id: map['id'],
      name: map['name'] ?? '',
      logo: map['logo'],
      slogan: map['slogan'],
      description: map['description'],
      address: map['address'],
      city: map['city'],
      state: map['state'],
      country: map['country'],
      postalCode: map['postal_code'],
      phone: map['phone'],
      email: map['email'],
      website: map['website'],
      taxNumber: map['tax_number'],
      commercialRegister: map['commercial_register'],
      legalName: map['legal_name'],
      currency: map['currency'],
      currencySymbol: map['currency_symbol'],
      currencySymbolOnLeft: map['currency_symbol_on_left'] == 1,
      decimalPlaces: map['decimal_places'] ?? 2,
      thousandsSeparator: map['thousands_separator'] ?? ',',
      decimalSeparator: map['decimal_separator'] ?? '.',
      receiptHeader: map['receipt_header'],
      receiptFooter: map['receipt_footer'],
      invoiceTerms: map['invoice_terms'],
      invoiceNotes: map['invoice_notes'],
      metadata: map['metadata'] != null
          ? jsonDecode(map['metadata'])
          : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل ملف الشركة إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء ملف شركة من JSON
  factory CompanyProfile.fromJson(String source) =>
      CompanyProfile.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'CompanyProfile(id: $id, name: $name, taxNumber: $taxNumber)';
  }
}
