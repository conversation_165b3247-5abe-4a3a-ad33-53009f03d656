import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';
import '../../../core/theme/index.dart';

/// خدمة تحسين الصور
/// تقوم هذه الخدمة بتحسين الصور وضغطها لتقليل حجمها وتحسين الأداء
class ImageOptimizerService {
  // Singleton pattern
  static final ImageOptimizerService _instance =
      ImageOptimizerService._internal();
  factory ImageOptimizerService() => _instance;
  ImageOptimizerService._internal();

  /// مجلد تخزين الصور
  String? _imagesDirectory;

  /// معرف فريد للصور
  final _uuid = const Uuid();

  /// تهيئة خدمة تحسين الصور
  Future<void> init() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _imagesDirectory = '${appDir.path}/images';

      // إنشاء مجلد الصور إذا لم يكن موجوداً
      final directory = Directory(_imagesDirectory!);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      AppLogger.info('تم تهيئة خدمة تحسين الصور');
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تهيئة خدمة تحسين الصور',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'init_image_optimizer_service'},
      );
    }
  }

  /// حفظ صورة محسنة
  /// يقوم بضغط الصورة وتحويلها إلى تنسيق WebP وحفظها في مجلد الصور
  /// ويعيد مسار الصورة المحسنة
  Future<String?> saveOptimizedImage(
    File imageFile, {
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      if (_imagesDirectory == null) {
        await init();
      }

      // إنشاء اسم فريد للصورة
      final fileName = '${_uuid.v4()}.webp';
      final targetPath = '$_imagesDirectory/$fileName';

      // ضغط الصورة وتحويلها إلى تنسيق WebP
      final result = await FlutterImageCompress.compressAndGetFile(
        imageFile.path,
        targetPath,
        quality: quality,
        format: CompressFormat.webp,
        minWidth: maxWidth ?? 800,
        minHeight: maxHeight ?? 600,
      );

      if (result == null) {
        throw Exception('فشل في ضغط الصورة');
      }

      AppLogger.info('تم حفظ الصورة المحسنة: $targetPath');

      return result.path;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حفظ الصورة المحسنة',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'save_optimized_image'},
      );
      return null;
    }
  }

  /// تحميل صورة محسنة
  /// يقوم بتحميل الصورة من المسار المحدد
  /// ويعيد ويدجت لعرض الصورة
  Widget loadOptimizedImage(
    String? imagePath, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    if (imagePath == null || imagePath.isEmpty) {
      return _getPlaceholder(width, height);
    }

    try {
      if (imagePath.startsWith('http')) {
        // صورة من الإنترنت
        return _loadNetworkImage(imagePath, width, height, fit);
      } else {
        // صورة محلية
        return _loadLocalImage(imagePath, width, height, fit);
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحميل الصورة المحسنة',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'load_optimized_image', 'imagePath': imagePath},
      );
      return _getPlaceholder(width, height);
    }
  }

  /// الحصول على صورة بديلة في حالة عدم وجود الصورة
  Widget _getPlaceholder(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: AppColors.lightSurfaceVariant,
      child: Icon(
        Icons.image,
        color: AppColors.lightTextSecondary,
        size: (width ?? 100) / 2,
      ),
    );
  }

  /// تحميل صورة محلية
  Widget _loadLocalImage(
      String path, double? width, double? height, BoxFit fit) {
    return Image.file(
      File(path),
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        ErrorTracker.captureError(
          'فشل في تحميل الصورة المحلية',
          error: error,
          stackTrace: stackTrace ?? StackTrace.current,
          context: {'operation': 'load_local_image', 'imagePath': path},
        );
        return _getPlaceholder(width, height);
      },
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
    );
  }

  /// تحميل صورة من الإنترنت
  Widget _loadNetworkImage(
      String url, double? width, double? height, BoxFit fit) {
    return Image.network(
      url,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          return child;
        }
        return Container(
          width: width,
          height: height,
          color: AppColors.lightSurfaceVariant,
          child: Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                  : null,
            ),
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        ErrorTracker.captureError(
          'فشل في تحميل الصورة من الإنترنت',
          error: error,
          stackTrace: stackTrace ?? StackTrace.current,
          context: {'operation': 'load_network_image', 'url': url},
        );
        return _getPlaceholder(width, height);
      },
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
    );
  }

  /// حذف صورة
  Future<bool> deleteImage(String imagePath) async {
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        await file.delete();
        AppLogger.info('تم حذف الصورة: $imagePath');
        return true;
      }
      return false;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف الصورة',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'delete_image', 'imagePath': imagePath},
      );
      return false;
    }
  }

  /// تنظيف الصور غير المستخدمة
  Future<int> cleanupUnusedImages(List<String> usedImagePaths) async {
    try {
      if (_imagesDirectory == null) {
        await init();
      }

      final directory = Directory(_imagesDirectory!);
      if (!await directory.exists()) {
        return 0;
      }

      final files = await directory.list().toList();
      int deletedCount = 0;

      for (var file in files) {
        if (file is File) {
          final filePath = file.path;
          if (!usedImagePaths.contains(filePath)) {
            await file.delete();
            deletedCount++;
          }
        }
      }

      AppLogger.info('تم حذف $deletedCount صورة غير مستخدمة');

      return deletedCount;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تنظيف الصور غير المستخدمة',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'cleanup_unused_images'},
      );
      return 0;
    }
  }
}
