# 📊 أمثلة استخدام الجدول الموحد (UnifiedDataTable)

## 🎯 نظرة عامة

الجدول الموحد `UnifiedDataTable` هو ودجت شامل ومرن يحل محل جميع أنواع الجداول المكررة في المشروع. يوفر:

- **تصميم موحد** مع دعم الثيمات
- **بحث وترتيب متقدم**
- **صفوف إجماليات تلقائية**
- **أنماط متعددة** (عصري، كلاسيكي، بسيط)
- **تمرير أفقي وعمودي**
- **حالات فارغة وتحميل**

## 🔧 الاستخدام الأساسي

### مثال بسيط للمنتجات:

```dart
import '../core/widgets/index.dart';

class ProductsTableExample extends StatelessWidget {
  final List<Product> products;

  const ProductsTableExample({super.key, required this.products});

  @override
  Widget build(BuildContext context) {
    return UnifiedDataTable<Product>(
      data: products,
      showSearchBar: true,
      showRowNumbers: true,
      columns: [
        UnifiedTableColumn<Product>(
          title: 'رقم المنتج',
          getValue: (product) => product.code ?? '',
          sortable: true,
        ),
        UnifiedTableColumn<Product>(
          title: 'الاسم',
          getValue: (product) => product.name,
          sortable: true,
        ),
        UnifiedTableColumn<Product>(
          title: 'الفئة',
          getValue: (product) => product.categoryName ?? '',
          sortable: true,
        ),
        UnifiedTableColumn<Employee>(
          title: 'الراتب',
          getValue: (employee) => employee.basicSalary,
          numeric: true,
          sortable: true,
          showTotal: true,
          formatTotal: (total) => '${total.toStringAsFixed(2)} ر.ي',
          buildCell: (employee, value) => DataCell(
            Text(
              '${value} ر.ي',
              style: const AppTypography(
                fontWeight: FontWeight.w600,
                color: AppColors.success,
              ),
            ),
          ),
        ),
        UnifiedTableColumn<Employee>(
          title: 'الحالة',
          getValue: (employee) => employee.status,
          buildCell: (employee, value) => DataCell(
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getStatusColor(employee.status),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getStatusText(employee.status),
                style: const AppTypography(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
        UnifiedTableColumn<Product>(
          title: 'الإجراءات',
          sortable: false,
          buildCell: (product, value) => DataCell(
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit, size: 18),
                  onPressed: () => _editProduct(product),
                  tooltip: 'تعديل',
                ),
                IconButton(
                  icon: const Icon(Icons.delete, size: 18, color: AppColors.error),
                  onPressed: () => _deleteProduct(product),
                  tooltip: 'حذف',
                ),
              ],
            ),
          ),
        ),
      ],
      onRowTap: (product, index) {
        // عرض تفاصيل المنتج
        _showProductDetails(product);
      },
      customFilter: (product, query) {
        return product.name.toLowerCase().contains(query.toLowerCase()) ||
               (product.code?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
               (product.categoryName?.toLowerCase().contains(query.toLowerCase()) ?? false);
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return AppColors.success;
      case 'inactive':
        return AppColors.error;
      case 'pending':
        return AppColors.warning;
      default:
        return AppColors.secondary;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'نشط';
      case 'inactive':
        return 'غير نشط';
      case 'pending':
        return 'معلق';
      default:
        return status;
    }
  }

  void _editProduct(Product product) {
    // منطق تعديل المنتج
  }

  void _deleteProduct(Product product) {
    // منطق حذف المنتج
  }

  void _showProductDetails(Product product) {
    // عرض تفاصيل المنتج
  }
}
```

## 💰 مثال للجداول المالية:

```dart
class FinancialTableExample extends StatelessWidget {
  final List<Transaction> transactions;

  const FinancialTableExample({super.key, required this.transactions});

  @override
  Widget build(BuildContext context) {
    return UnifiedDataTable<Transaction>(
      data: transactions,
      style: TableStyle.modern,
      showSearchBar: true,
      showTotals: true,
      alternatingRows: true,
      columns: [
        UnifiedTableColumn<Transaction>(
          title: 'التاريخ',
          getValue: (transaction) => transaction.date,
          buildCell: (transaction, value) => DataCell(
            Text(DateFormat('yyyy/MM/dd').format(value)),
          ),
          sortable: true,
        ),
        UnifiedTableColumn<Transaction>(
          title: 'الوصف',
          getValue: (transaction) => transaction.description,
          sortable: true,
        ),
        UnifiedTableColumn<Transaction>(
          title: 'المدين',
          getValue: (transaction) => transaction.debitAmount,
          numeric: true,
          showTotal: true,
          formatTotal: (total) => NumberFormat('#,##0.00').format(total),
          buildCell: (transaction, value) => DataCell(
            Text(
              value > 0 ? NumberFormat('#,##0.00').format(value) : '-',
              style: AppTypography(
                color: value > 0 ? AppColors.error : AppColors.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        UnifiedTableColumn<Transaction>(
          title: 'الدائن',
          getValue: (transaction) => transaction.creditAmount,
          numeric: true,
          showTotal: true,
          formatTotal: (total) => NumberFormat('#,##0.00').format(total),
          buildCell: (transaction, value) => DataCell(
            Text(
              value > 0 ? NumberFormat('#,##0.00').format(value) : '-',
              style: AppTypography(
                color: value > 0 ? AppColors.success : AppColors.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        UnifiedTableColumn<Transaction>(
          title: 'الرصيد',
          getValue: (transaction) => transaction.balance,
          numeric: true,
          buildCell: (transaction, value) => DataCell(
            Text(
              NumberFormat('#,##0.00').format(value),
              style: AppTypography(
                color: value >= 0 ? AppColors.success : AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
      customFilter: (transaction, query) {
        return transaction.description.toLowerCase().contains(query.toLowerCase()) ||
               transaction.reference?.toLowerCase().contains(query.toLowerCase()) == true;
      },
    );
  }
}
```

## 📱 مثال للجداول المتجاوبة:

```dart
class ResponsiveTableExample extends StatelessWidget {
  final List<Product> products;

  const ResponsiveTableExample({super.key, required this.products});

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 1024;
    final isTablet = MediaQuery.of(context).size.width > 768;

    return UnifiedDataTable<Product>(
      data: products,
      height: isDesktop ? null : 400,
      showSearchBar: true,
      showRowNumbers: isDesktop,
      style: isDesktop ? TableStyle.modern : TableStyle.minimal,
      columns: [
        UnifiedTableColumn<Product>(
          title: 'الصورة',
          sortable: false,
          buildCell: (product, value) => DataCell(
            CircleAvatar(
              radius: 20,
              backgroundImage: product.imageUrl != null 
                  ? NetworkImage(product.imageUrl!) 
                  : null,
              child: product.imageUrl == null 
                  ? const Icon(Icons.inventory_2) 
                  : null,
            ),
          ),
        ),
        UnifiedTableColumn<Product>(
          title: 'اسم المنتج',
          getValue: (product) => product.name,
          sortable: true,
        ),
        if (isTablet) ...[
          UnifiedTableColumn<Product>(
            title: 'الفئة',
            getValue: (product) => product.category,
            sortable: true,
          ),
        ],
        UnifiedTableColumn<Product>(
          title: 'السعر',
          getValue: (product) => product.price,
          numeric: true,
          sortable: true,
          buildCell: (product, value) => DataCell(
            Text(
              '${value} ر.ي',
              style: const AppTypography(
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
          ),
        ),
        if (isDesktop) ...[
          UnifiedTableColumn<Product>(
            title: 'المخزون',
            getValue: (product) => product.stock,
            numeric: true,
            sortable: true,
            buildCell: (product, value) => DataCell(
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStockColor(value),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  value.toString(),
                  style: const AppTypography(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Color _getStockColor(int stock) {
    if (stock > 50) return AppColors.success;
    if (stock > 10) return AppColors.warning;
    return AppColors.error;
  }
}
```

## 🎨 الأنماط المتاحة:

### 1. النمط العصري (Modern):
```dart
UnifiedDataTable(
  style: TableStyle.modern, // حواف مدورة وظلال
  // ...
)
```

### 2. النمط الكلاسيكي (Classic):
```dart
UnifiedDataTable(
  style: TableStyle.classic, // حدود بسيطة
  // ...
)
```

### 3. النمط البسيط (Minimal):
```dart
UnifiedDataTable(
  style: TableStyle.minimal, // بدون حدود
  // ...
)
```

## 🔍 البحث والترتيب:

```dart
UnifiedDataTable<Product>(
  showSearchBar: true,
  searchHint: 'ابحث عن منتج...',
  customFilter: (product, query) {
    return product.name.toLowerCase().contains(query.toLowerCase()) ||
           (product.code?.contains(query) ?? false);
  },
  // ...
)
```

## 📊 الإجماليات:

```dart
UnifiedTableColumn<Transaction>(
  title: 'المبلغ',
  getValue: (transaction) => transaction.amount,
  showTotal: true,
  formatTotal: (total) => '${total.toStringAsFixed(2)} ر.ي',
  // ...
)
```

---

**الجدول الموحد يوفر حلاً شاملاً لجميع احتياجات الجداول في التطبيق مع ضمان التناسق والمرونة.**
