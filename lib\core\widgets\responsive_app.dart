import 'package:flutter/material.dart';

/// تطبيق متوافق مع مختلف أحجام الشاشات
class ResponsiveApp extends StatelessWidget {
  final String title;
  final ThemeData? theme;
  final ThemeData? darkTheme;
  final ThemeMode? themeMode;
  final Widget? home;
  final Map<String, WidgetBuilder>? routes;
  final String? initialRoute;
  final GlobalKey<NavigatorState>? navigatorKey;
  final List<NavigatorObserver>? navigatorObservers;
  final Locale? locale;
  final Iterable<LocalizationsDelegate<dynamic>>? localizationsDelegates;
  final Locale? Function(List<Locale>?, Iterable<Locale>)? localeListResolutionCallback;
  final Locale? Function(Locale?, Iterable<Locale>)? localeResolutionCallback;
  final Iterable<Locale> supportedLocales;
  final bool debugShowMaterialGrid;
  final bool showPerformanceOverlay;
  final bool checkerboardRasterCacheImages;
  final bool checkerboardOffscreenLayers;
  final bool showSemanticsDebugger;
  final bool debugShowCheckedModeBanner;
  final Widget Function(BuildContext, Widget?)? builder;
  final String? restorationScopeId;
  final ScrollBehavior? scrollBehavior;
  final Route<dynamic>? Function(RouteSettings)? onGenerateRoute;
  final Route<dynamic>? Function(RouteSettings)? onUnknownRoute;

  const ResponsiveApp({
    Key? key,
    this.title = '',
    this.theme,
    this.darkTheme,
    this.themeMode,
    this.home,
    this.routes,
    this.initialRoute,
    this.navigatorKey,
    this.navigatorObservers,
    this.locale,
    this.localizationsDelegates,
    this.localeListResolutionCallback,
    this.localeResolutionCallback,
    this.supportedLocales = const <Locale>[Locale('en', 'US')],
    this.debugShowMaterialGrid = false,
    this.showPerformanceOverlay = false,
    this.checkerboardRasterCacheImages = false,
    this.checkerboardOffscreenLayers = false,
    this.showSemanticsDebugger = false,
    this.debugShowCheckedModeBanner = false,
    this.builder,
    this.restorationScopeId,
    this.scrollBehavior,
    this.onGenerateRoute,
    this.onUnknownRoute,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: title,
      theme: theme?.copyWith(
        // إضافة تخصيصات للثيم تجعل التطبيق أكثر استجابة
        textTheme: theme?.textTheme.copyWith(
          // تعديل أحجام الخطوط لتناسب مختلف الشاشات
          titleLarge: theme?.textTheme.titleLarge?.copyWith(
            overflow: TextOverflow.ellipsis,
          ),
          titleMedium: theme?.textTheme.titleMedium?.copyWith(
            overflow: TextOverflow.ellipsis,
          ),
          bodyLarge: theme?.textTheme.bodyLarge?.copyWith(
            overflow: TextOverflow.ellipsis,
          ),
          bodyMedium: theme?.textTheme.bodyMedium?.copyWith(
            overflow: TextOverflow.ellipsis,
          ),
        ),
        // تحسين قابلية عرض البطاقات
        cardTheme: theme?.cardTheme.copyWith(
          margin: const EdgeInsets.all(8.0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        // جعل الأزرار قابلة للتكيف
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ),
      darkTheme: darkTheme,
      themeMode: themeMode,
      home: home,
      routes: routes ?? const {},
      initialRoute: initialRoute,
      navigatorKey: navigatorKey,
      navigatorObservers: navigatorObservers ?? const <NavigatorObserver>[],
      locale: locale,
      localizationsDelegates: localizationsDelegates,
      localeListResolutionCallback: localeListResolutionCallback,
      localeResolutionCallback: localeResolutionCallback,
      supportedLocales: supportedLocales,
      debugShowMaterialGrid: debugShowMaterialGrid,
      showPerformanceOverlay: showPerformanceOverlay,
      checkerboardRasterCacheImages: checkerboardRasterCacheImages,
      checkerboardOffscreenLayers: checkerboardOffscreenLayers,
      showSemanticsDebugger: showSemanticsDebugger,
      debugShowCheckedModeBanner: debugShowCheckedModeBanner,
      onGenerateRoute: onGenerateRoute,
      onUnknownRoute: onUnknownRoute,
      // إضافة مستمع عام للتخطيط للتعامل مع التجاوزات تلقائيًا
      builder: (context, child) {
        // إضافة MediaQuery لإدارة التخطيط بشكل أفضل
        final mediaQuery = MediaQuery.of(context);
        final restrictedMedia = MediaQuery(
          // تعديل أحجام الخط حسب حجم الشاشة
          data: mediaQuery.copyWith(
            textScaler: TextScaler.linear(_getResponsiveTextScale(mediaQuery.size.width)),
          ),
          child: Builder(
            builder: (context) {
              // استدعاء باني مخصص إذا تم توفيره
              if (builder != null) {
                return builder!(context, child);
              }
              return child ?? const SizedBox();
            },
          ),
        );
        
        // إضافة غلاف للتعامل مع تجاوز الخط والعناصر
        return LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              // السماح بالتمرير الأفقي إذا كان العرض غير كافٍ
              scrollDirection: Axis.horizontal,
              physics: constraints.maxWidth < 360
                  ? const ClampingScrollPhysics()
                  : const NeverScrollableScrollPhysics(),
              child: Container(
                // تحديد الحد الأدنى للعرض لمنع التسكير المفرط
                constraints: BoxConstraints(
                  minWidth: 360,
                  maxWidth: constraints.maxWidth,
                ),
                child: restrictedMedia,
              ),
            );
          },
        );
      },
      restorationScopeId: restorationScopeId,
      scrollBehavior: scrollBehavior ?? const MaterialScrollBehavior(),
    );
  }

  // تحديد مقياس النص المناسب حسب عرض الشاشة
  double _getResponsiveTextScale(double width) {
    if (width < 320) {
      return 0.8; // شاشات صغيرة جدًا
    } else if (width < 480) {
      return 0.9; // شاشات صغيرة
    } else if (width < 600) {
      return 1.0; // شاشات متوسطة
    } else {
      return 1.1; // شاشات كبيرة
    }
  }
} 