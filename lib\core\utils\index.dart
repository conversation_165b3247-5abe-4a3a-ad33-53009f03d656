/// نظام الأدوات - Utils System
///
/// يوفر نقطة وصول موحدة لجميع الأدوات المساعدة في التطبيق
/// مع تنظيم محسن وتجنب التكرار
///
/// الميزات الرئيسية:
/// - نظام التحقق من صحة البيانات
/// - نظام المساعدات العامة
/// - نظام التخطيط والاستجابة
/// - نظام تتبع الأخطاء والنشاطات
///
/// الاستخدام:
/// ```dart
/// import 'package:tajer_plus/core/utils/index.dart';
///
/// // التحقق من صحة البيانات
/// validator: Validators.required('اسم المستخدم')
///
/// // المساعدات العامة
/// Helpers.showSuccess(context, 'تم الحفظ بنجاح');
///
/// // التخطيط والاستجابة
/// Layout.init(context);
/// final width = Layout.w(50);
///
/// // تتبع الأخطاء
/// ErrorTracker.captureError('خطأ', error: e, stackTrace: st);
/// ```

// ========== الأنظمة الأساسية ==========

/// نظام التحقق من صحة البيانات
/// يجمع جميع أنواع التحقق في مكان واحد
export 'validators.dart';

/// نظام المساعدات العامة
/// يجمع جميع الدوال المساعدة في مكان واحد
export 'helpers.dart';

/// نظام التخطيط والاستجابة
/// يجمع جميع أدوات التخطيط والاستجابة في مكان واحد
export 'layout.dart';

// ========== الأنظمة الأساسية المحسنة ==========

/// نظام تتبع الأخطاء والنشاطات الموحد
export 'error_tracker.dart';

/// نظام معالجة الأخطاء
export 'error_handler.dart';

/// نظام التسجيل
export 'app_logger.dart';

/// نظام الرسائل
export 'app_messages.dart';

// ========== الأنظمة المتخصصة ==========

/// معالج الأخطاء الشامل
export 'global_error_handler.dart';

/// معالج النماذج (محسن)
export 'form_handlers.dart';

/// مساعد الاتصال
export 'connectivity_helper.dart';

/// منسق دورة حياة التطبيق
export 'app_lifecycle_manager.dart';

/// منسق التخطيط الآمن
export 'safe_layout_manager_widget.dart';

/// منسق تنسيق الأرقام
export 'number_formatter.dart';

// ========== الأنظمة القديمة تم حذفها ==========
// تم حذف الملفات المكررة التالية وتوحيدها في الأنظمة الجديدة:
// ❌ form_validators.dart → ✅ validators.dart
// ❌ input_validators.dart → ✅ validators.dart
// ❌ app_helpers.dart → ✅ helpers.dart
// ❌ context_helper.dart → ✅ helpers.dart
// ❌ layout_utils.dart → ✅ layout.dart
// ❌ responsive_helper.dart → ✅ layout.dart

// ========== دليل الترقية ==========

/// دليل الاستخدام الجديد:
///
/// ✅ **النظام الجديد المبسط!**
///
/// 1. **التحقق من صحة البيانات**:
///    ✅ الجديد: Validators.required('الاسم')(value)
///    ✅ المركب: Validators.combine([...])
///
/// 2. **المساعدات العامة**:
///    ✅ الجديد: Helpers.showSuccess(context, 'رسالة')
///    ✅ الآمن: Helpers.safeExecute(context, () async {...})
///
/// 3. **التخطيط والاستجابة**:
///    ✅ الجديد: Layout.w(50)
///    ✅ التهيئة: Layout.init(context)
///
/// 4. **العناصر الآمنة**:
///    ✅ الجديد: Layout.safeText('نص')
///    ✅ الأزرار: Layout.safeButton(label: 'زر', onPressed: () {})
///
/// 5. **الثيمات**:
///    ✅ الجديد: AppTheme.lightTheme

// ========== الفوائد من التوحيد ==========

/// الفوائد المحققة:
///
/// ✅ **تم تحقيق جميع الأهداف!**
///
/// 1. **تقليل التكرار**: حذف 6 ملفات مكررة → 3 ملفات بسيطة
/// 2. **تقليل حجم التطبيق**: توفير ~60% من الكود المكرر
/// 3. **سهولة الصيانة**: كود منظم وواضح مع تعليقات عربية
/// 4. **تحسين الأداء**: تقليل استهلاك الذاكرة وسرعة التحميل
/// 5. **سهولة التطوير**: أسماء بسيطة وواضحة
/// 6. **تحسين التجربة**: عناصر متجاوبة وآمنة
/// 7. **استخدام الثيم المخصص**: بدلاً من Theme.of(context).copyWith()
/// 8. **إزالة الارتباك**: أسماء بسيطة بدون "unified"

// ========== إرشادات الاستخدام ==========

/// إرشادات للمطورين:
///
/// ✅ **النظام الجديد البسيط!**
///
/// 1. **للتحقق**: استخدم Validators مع combine() للتحقق المركب
/// 2. **للمساعدات**: استخدم Helpers للعمليات الآمنة
/// 3. **للتخطيط**: استخدم Layout للعناصر المتجاوبة
/// 4. **للثيمات**: استخدم AppTheme.lightTheme بدلاً من Theme.of(context).copyWith()
/// 5. **للاستيراد**: استخدم import 'package:tajer_plus/core/utils/index.dart';
///
/// مثال شامل:
/// ```dart
/// class MyForm extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     Layout.init(context);
///
///     return Layout.safeColumn(
///       children: [
///         Layout.safeTextField(
///           labelText: 'البريد الإلكتروني',
///           validator: Validators.combine([
///             Validators.required('البريد الإلكتروني'),
///             Validators.email(),
///           ]),
///         ),
///         Layout.safeButton(
///           label: 'حفظ',
///           onPressed: () async {
///             await Helpers.safeExecute(
///               context,
///               () async {
///                 // كود الحفظ
///               },
///               successMessage: 'تم الحفظ بنجاح',
///             );
///           },
///         ),
///       ],
///     );
///   }
/// }
/// ```
