import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';
import '../../../core/utils/app_logger.dart';
import '../models/setup_state.dart';

/// ويدجت لعرض تقدم تهيئة قاعدة البيانات
/// يعرض شريط تقدم مع رسائل توضيحية لكل خطوة
class DatabaseInitializationProgress extends StatelessWidget {
  /// حالة الإعداد
  final SetupState setupState;

  /// المنشئ
  const DatabaseInitializationProgress({
    Key? key,
    required this.setupState,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: setupState,
      builder: (context, _) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // عنوان الخطوة الحالية
            Text(
              _getStepTitle(setupState.step),
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // شريط التقدم
            LinearProgressIndicator(
              value: setupState.progress,
              minHeight: 10,
              borderRadius: BorderRadius.circular(5),
            ),
            const SizedBox(height: 16),

            // نسبة التقدم
            Text(
              '${(setupState.progress * 100).toInt()}%',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            // رسالة الخطوة الحالية
            Text(
              setupState.message,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // عرض الخطأ إذا وجد
            if (setupState.error != null && setupState.error!.isNotEmpty)
              _buildErrorWidget(context),

            // عرض زر إعادة المحاولة إذا كانت هناك مشكلة
            if (setupState.step == SetupStep.error) _buildRetryButton(context),

            // عرض زر الانتقال إلى الشاشة الرئيسية إذا اكتمل الإعداد
            if (setupState.step == SetupStep.completed)
              _buildContinueButton(context),
          ],
        );
      },
    );
  }

  /// بناء ويدجت الخطأ
  Widget _buildErrorWidget(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightTextSecondary,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.error),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            'حدث خطأ أثناء الإعداد',
            style: Theme.of(context)
                .textTheme
                .titleMedium!
                .copyWith(color: AppColors.error),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            setupState.error ?? 'خطأ غير معروف',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء زر إعادة المحاولة
  Widget _buildRetryButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: ElevatedButton.icon(
        onPressed: () {
          // إعادة تعيين حالة الإعداد
          setupState.resetState();

          // إعادة تشغيل الإعداد
          // يجب تنفيذ هذا من خلال الشاشة الأم
          AppLogger.info('طلب إعادة محاولة الإعداد');
        },
        icon: const Icon(Icons.refresh),
        label: const Text('إعادة المحاولة'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
    );
  }

  /// بناء زر الانتقال إلى الشاشة الرئيسية
  Widget _buildContinueButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: ElevatedButton.icon(
        onPressed: () {
          // الانتقال إلى الشاشة الرئيسية
          // يجب تنفيذ هذا من خلال الشاشة الأم
          AppLogger.info('طلب الانتقال إلى الشاشة الرئيسية');
        },
        icon: const Icon(Icons.check_circle),
        label: const Text('الانتقال إلى الشاشة الرئيسية'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.success,
          foregroundColor: AppColors.onPrimary,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
    );
  }

  /// الحصول على عنوان الخطوة الحالية
  String _getStepTitle(SetupStep step) {
    switch (step) {
      case SetupStep.checkingExistingDatabase:
        return 'التحقق من قاعدة البيانات';
      case SetupStep.creatingDatabase:
        return 'إنشاء قاعدة البيانات';
      case SetupStep.creatingTables:
        return 'إنشاء جداول قاعدة البيانات';
      case SetupStep.initializingBasicData:
        return 'تهيئة البيانات الأساسية';
      case SetupStep.initializingSettings:
        return 'تهيئة إعدادات النظام';
      case SetupStep.finalizingSetup:
        return 'إنهاء الإعداد';
      case SetupStep.completed:
        return 'تم اكتمال الإعداد بنجاح';
      case SetupStep.error:
        return 'حدث خطأ أثناء الإعداد';
    }
  }
}
