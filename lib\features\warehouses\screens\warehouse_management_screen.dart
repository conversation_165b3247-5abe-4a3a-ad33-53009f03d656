import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/index.dart';
import '../presenters/warehouse_presenter.dart';
import '../../../core/models/warehouse.dart';
import 'warehouse_form_screen.dart';
import '../../../core/theme/index.dart';

/// شاشة إدارة المخازن
class WarehouseManagementScreen extends StatefulWidget {
  const WarehouseManagementScreen({Key? key}) : super(key: key);

  @override
  State<WarehouseManagementScreen> createState() =>
      _WarehouseManagementScreenState();
}

class _WarehouseManagementScreenState extends State<WarehouseManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  late final WarehousePresenter _presenter;

  @override
  void initState() {
    super.initState();
    _presenter = AppProviders.getLazyPresenter<WarehousePresenter>(
        () => WarehousePresenter());
    // استخدام Future.microtask لتحميل البيانات بعد بناء الواجهة
    Future.microtask(_loadData);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await _presenter.loadWarehouses();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المخازن'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: SafeLayout(
        child: ListenableBuilder(
          listenable: _presenter,
          builder: (context, child) {
            if (_presenter.isLoading) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (_presenter.errorMessage != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: AppColors.error,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _presenter.errorMessage!,
                      style: const AppTypography(color: AppColors.error),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _loadData,
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة المحاولة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.lightTextSecondary,
                      ),
                    ),
                  ],
                ),
              );
            }

            if (_presenter.warehouses.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.inventory,
                      color: AppColors.lightTextSecondary,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'لا توجد مخازن',
                      style: AppTypography(color: AppColors.lightTextSecondary),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _addWarehouse,
                      icon: const Icon(Icons.add),
                      label: const Text('إضافة مخزن'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.lightTextSecondary,
                      ),
                    ),
                  ],
                ),
              );
            }

            // استخدام SizedBox مع ارتفاع محدد بدلاً من Column مع Expanded
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // شريط البحث
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _searchController,
                          decoration: const InputDecoration(
                            hintText: 'بحث عن مخزن...',
                            prefixIcon: Icon(Icons.search),
                            border: OutlineInputBorder(),
                          ),
                          onChanged: _searchWarehouses,
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        onPressed: _addWarehouse,
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة مخزن'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.lightTextSecondary,
                        ),
                      ),
                    ],
                  ),
                ),

                // جدول المخازن - استخدام SizedBox مع ارتفاع محدد
                SizedBox(
                  height:
                      MediaQuery.of(context).size.height - 200, // ارتفاع محدد
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: _buildWarehousesTable(_presenter.warehouses),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// بناء جدول المخازن
  Widget _buildWarehousesTable(List<Warehouse> warehouses) {
    final columns = [
      const FinancialTableColumn(
        title: 'الاسم',
        field: 'name',
        alignment: ColumnAlignment.start,
      ),
      const FinancialTableColumn(
        title: 'الرمز',
        field: 'code',
        alignment: ColumnAlignment.center,
      ),
      const FinancialTableColumn(
        title: 'العنوان',
        field: 'address',
        alignment: ColumnAlignment.start,
      ),
      const FinancialTableColumn(
        title: 'افتراضي',
        field: 'is_default',
        alignment: ColumnAlignment.center,
      ),
      const FinancialTableColumn(
        title: 'الحالة',
        field: 'is_active',
        alignment: ColumnAlignment.center,
      ),
      const FinancialTableColumn(
        title: 'الإجراءات',
        field: 'actions',
        alignment: ColumnAlignment.center,
      ),
    ];

    final data = warehouses.map((warehouse) {
      return {
        'id': warehouse.id,
        'name': warehouse.name,
        'code': warehouse.code,
        'address': warehouse.address,
        'is_default': warehouse.isDefault ? 'نعم' : 'لا',
        'is_active': warehouse.isActive ? 'نشط' : 'غير نشط',
        'actions': warehouse,
      };
    }).toList();

    return FinancialDataTable(
      columns: columns,
      data: data,
      onRowTap: (index) => _editWarehouse(warehouses[index]),
      customCellBuilder: (context, rowIndex, columnIndex, value, field) {
        if (field == 'actions') {
          final warehouse = value as Warehouse;
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, color: AppColors.info),
                onPressed: () => _editWarehouse(warehouse),
                tooltip: 'تعديل',
              ),
              IconButton(
                icon: const Icon(Icons.delete, color: AppColors.error),
                onPressed: () => _deleteWarehouse(warehouse),
                tooltip: 'حذف',
              ),
            ],
          );
        } else if (field == 'is_default' || field == 'is_active') {
          return Icon(
            value == 'نعم' ? Icons.check_circle : Icons.cancel,
            color: value == 'نعم' ? AppColors.success : AppColors.error,
          );
        }
        return null;
      },
    );
  }

  /// إضافة مخزن جديد
  void _addWarehouse() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const WarehouseFormScreen(),
      ),
    );

    if (result == true) {
      _loadData();
    }
  }

  /// تعديل مخزن
  void _editWarehouse(Warehouse warehouse) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WarehouseFormScreen(warehouse: warehouse),
      ),
    );

    if (result == true) {
      _loadData();
    }
  }

  /// حذف مخزن
  void _deleteWarehouse(Warehouse warehouse) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('حذف مخزن'),
        content: Text('هل أنت متأكد من حذف المخزن "${warehouse.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              // إغلاق مربع الحوار أولاً
              Navigator.pop(dialogContext);

              // تنفيذ عملية الحذف بشكل منفصل
              _performDeleteWarehouse(warehouse);
            },
            child:
                const Text('حذف', style: AppTypography(color: AppColors.error)),
          ),
        ],
      ),
    );
  }

  /// تنفيذ عملية حذف المخزن
  Future<void> _performDeleteWarehouse(Warehouse warehouse) async {
    final success = await _presenter.deleteWarehouse(warehouse.id);

    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_presenter.errorMessage ?? 'فشل في حذف المخزن'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  /// البحث عن مخازن
  void _searchWarehouses(String query) {
    if (query.isEmpty) {
      _loadData();
      return;
    }

    // تنفيذ البحث بشكل غير متزامن
    _performSearch(query);
  }

  /// تنفيذ عملية البحث
  Future<void> _performSearch(String query) async {
    final warehouses = await _presenter.searchWarehouses(query);

    if (mounted) {
      setState(() {
        // لا حاجة لإعادة تعيين _presenter هنا
        _presenter.warehouses.clear();
        _presenter.warehouses.addAll(warehouses);
      });
    }
  }
}
