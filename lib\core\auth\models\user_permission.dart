import 'dart:convert';

/// نموذج صلاحية المستخدم
/// يستخدم لتخزين صلاحيات المستخدم المحددة
class UserPermission {
  final String userId;
  final List<String> permissionCodes; // رموز الصلاحيات المسموح بها
  final DateTime createdAt;
  final DateTime? updatedAt;

  /// إنشاء صلاحية مستخدم جديدة
  UserPermission({
    required this.userId,
    required this.permissionCodes,
    DateTime? createdAt,
    this.updatedAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// إنشاء نسخة جديدة من صلاحية المستخدم مع تحديث بعض القيم
  UserPermission copyWith({
    String? userId,
    List<String>? permissionCodes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserPermission(
      userId: userId ?? this.userId,
      permissionCodes: permissionCodes ?? this.permissionCodes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من وجود صلاحية محددة
  bool hasPermission(String permissionCode) {
    return permissionCodes.contains(permissionCode);
  }

  /// التحقق من وجود أي من الصلاحيات المحددة
  bool hasAnyPermission(List<String> codes) {
    return codes.any((code) => permissionCodes.contains(code));
  }

  /// التحقق من وجود جميع الصلاحيات المحددة
  bool hasAllPermissions(List<String> codes) {
    return codes.every((code) => permissionCodes.contains(code));
  }

  /// إضافة صلاحية
  UserPermission addPermission(String permissionCode) {
    if (!permissionCodes.contains(permissionCode)) {
      final newCodes = List<String>.from(permissionCodes)..add(permissionCode);
      return copyWith(permissionCodes: newCodes, updatedAt: DateTime.now());
    }
    return this;
  }

  /// إضافة مجموعة من الصلاحيات
  UserPermission addPermissions(List<String> codes) {
    final newCodes = List<String>.from(permissionCodes);
    bool changed = false;

    for (final code in codes) {
      if (!newCodes.contains(code)) {
        newCodes.add(code);
        changed = true;
      }
    }

    return changed
        ? copyWith(permissionCodes: newCodes, updatedAt: DateTime.now())
        : this;
  }

  /// إزالة صلاحية
  UserPermission removePermission(String permissionCode) {
    if (permissionCodes.contains(permissionCode)) {
      final newCodes = List<String>.from(permissionCodes)
        ..remove(permissionCode);
      return copyWith(permissionCodes: newCodes, updatedAt: DateTime.now());
    }
    return this;
  }

  /// إزالة مجموعة من الصلاحيات
  UserPermission removePermissions(List<String> codes) {
    final newCodes = List<String>.from(permissionCodes);
    bool changed = false;

    for (final code in codes) {
      if (newCodes.contains(code)) {
        newCodes.remove(code);
        changed = true;
      }
    }

    return changed
        ? copyWith(permissionCodes: newCodes, updatedAt: DateTime.now())
        : this;
  }

  /// تحويل النموذج إلى خريطة (لقاعدة البيانات)
  Map<String, dynamic> toMap() {
    return {
      'user_id': userId,
      'permission_codes': jsonEncode(permissionCodes),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء صلاحية مستخدم من خريطة (من قاعدة البيانات)
  factory UserPermission.fromMap(Map<String, dynamic> map) {
    List<String> codes = [];

    // استخراج رموز الصلاحيات من الخريطة
    if (map['permission_codes'] != null) {
      try {
        final dynamic decodedCodes = jsonDecode(map['permission_codes']);
        if (decodedCodes is List) {
          codes = List<String>.from(decodedCodes);
        }
      } catch (e) {
        // في حالة فشل فك الترميز، نستخدم قائمة فارغة
      }
    }

    return UserPermission(
      userId: map['user_id'] as String,
      permissionCodes: codes,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  /// تحويل النموذج إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء نموذج من JSON
  factory UserPermission.fromJson(String source) =>
      UserPermission.fromMap(jsonDecode(source));

  /// إنشاء صلاحية مستخدم من قائمة رموز الصلاحيات
  factory UserPermission.fromPermissionCodes(
      String userId, List<String> codes) {
    return UserPermission(
      userId: userId,
      permissionCodes: List<String>.from(codes),
    );
  }

  @override
  String toString() {
    return 'UserPermission(userId: $userId, permissionCodes: $permissionCodes)';
  }
}
