import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

// استيراد BasePresenter الموحد
import 'base_presenter.dart';
import '../utils/app_logger.dart';

// استيراد جميع الـ Presenters
import '../../features/products/presenters/product_presenter.dart';
import '../../features/categories/presenters/category_presenter.dart';
import '../../features/units/presenters/unit_presenter.dart';
import '../../features/customers/presenters/customer_presenter.dart';
import '../../features/sales/presenters/sale_presenter.dart';
import '../../features/suppliers/presenters/supplier_presenter.dart';
import '../../features/accounts/presenters/account_presenter.dart';
import '../../features/purchases/presenters/purchase_presenter.dart';
import '../../features/warehouses/presenters/warehouse_presenter.dart';
import '../../features/expenses/presenters/expense_presenter.dart';
import '../../features/warehouses/presenters/inventory_transfer_presenter.dart';
import '../../features/warehouses/presenters/inventory_adjustment_presenter.dart';
import '../../features/warehouses/presenters/inventory_presenter.dart';
import '../../features/simple_ties/presenters/simple_tie_presenter.dart';
import '../../features/pos/presenters/pos_presenter.dart';
import '../../features/promotions/presenter/promotion_presenter.dart';
import '../../features/vouchers/presenters/voucher_presenter.dart';
import '../../features/currencies/presenters/currency_presenter.dart';
import '../../features/settings/presenters/settings_presenter.dart';
import '../../features/users/presenters/user_presenter.dart';
import '../../features/users/presenters/permission_presenter.dart';
import '../../features/users/presenters/role_presenter.dart';
import '../../features/accounts/presenters/journal_entry_presenter.dart';
import '../../features/dashboard/presenters/dashboard_presenter.dart';
import '../../features/reports/presenters/reports_presenter.dart';

/// 🏗️ مدير موحد لجميع الـ Providers في التطبيق مع دعم التحميل الكسول
///
/// هذا الكلاس يدير جميع الـ Presenters المطلوبة لشاشات التطبيق المختلفة:
///
/// 📱 **الشاشات المدعومة:**
/// - 🏠 شاشة لوحة المعلومات (Dashboard)
/// - 🔐 شاشات إدارة المستخدمين والصلاحيات
/// - 💰 شاشات النظام المحاسبي والمالي
/// - 🛍️ شاشات المبيعات والمشتريات
/// - 📦 شاشات إدارة المخزون والمنتجات
/// - 💸 شاشات المصروفات والعمليات المالية
/// - 📊 شاشات التقارير والإحصائيات
/// - ⚙️ شاشات الإعدادات والتكوين
///
/// يساعد في تنظيم وإدارة الـ providers بشكل أفضل وتحسين الأداء
class AppProviders {
  /// 🔧 الحصول على قائمة الـ providers الأساسية فقط
  /// هذه الـ providers ضرورية لبدء تشغيل التطبيق وتشمل:
  /// - مقدم لوحة المعلومات (Dashboard Presenter)
  /// - مقدم الإعدادات (Settings Presenter)
  /// ملاحظة: ThemeManager يتم تهيئته في main.dart ويُحفظ في النظام الكسول
  static List<SingleChildWidget> getCoreProviders() {
    return [
      // 🏠 مقدم لوحة المعلومات - الشاشة الرئيسية
      ChangeNotifierProvider(create: (_) => DashboardPresenter()),

      // ⚙️ مقدم الإعدادات - شاشة الإعدادات العامة
      ChangeNotifierProvider(create: (_) => SettingsPresenter()),
    ];
  }

  /// 🔄 الحصول على الـ providers الكسولة - يتم تحميلها عند الحاجة فقط
  ///
  /// ⚠️ **تحذير:** هذه الدالة تحمل جميع الـ Presenters فور استدعائها!
  /// لا تستخدمها إلا في حالات خاصة جداً. استخدم getLazyPresenter() بدلاً من ذلك.
  ///
  /// **للاستخدام العادي:** استخدم getMinimalProviders() + getLazyPresenter()
  @Deprecated(
      'هذه الدالة تحمل جميع الـ Presenters فور استدعائها. استخدم getLazyPresenter() بدلاً من ذلك')
  static List<SingleChildWidget> getLazyProviders() {
    // ⚠️ هذه الدالة تحمل جميع الـ Presenters فور استدعائها!
    // تم إهمالها لصالح النهج الكسول الحقيقي
    return [
      // تحميل كسول للـ presenters الثانوية
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<ProductPresenter>(
          () => ProductPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<CategoryPresenter>(
          () => CategoryPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<UnitPresenter>(
          () => UnitPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<CustomerPresenter>(
          () => CustomerPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<SalePresenter>(
          () => SalePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<SupplierPresenter>(
          () => SupplierPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<AccountPresenter>(
          () => AccountPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<PurchasePresenter>(
          () => PurchasePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<WarehousePresenter>(
          () => WarehousePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<InventoryTransferPresenter>(
          () => InventoryTransferPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<InventoryAdjustmentPresenter>(
          () => InventoryAdjustmentPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<InventoryPresenter>(
          () => InventoryPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<SimpleTiePresenter>(
          () => SimpleTiePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<PromotionPresenter>(
          () => PromotionPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<VoucherPresenter>(
          () => VoucherPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<CurrencyPresenter>(
          () => CurrencyPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<UserPresenter>(
          () => UserPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<PermissionPresenter>(
          () => PermissionPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<RolePresenter>(
          () => RolePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<JournalEntryPresenter>(
          () => JournalEntryPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<ExpensePresenter>(
          () => ExpensePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<ReportsPresenter>(
          () => ReportsPresenter(),
        ),
      ),
    ];
  }

  /// 🔗 الحصول على الـ providers المعقدة (ProxyProviders)
  ///
  /// هذه الدالة تحتوي على الـ Presenters التي تعتمد على presenters أخرى.
  /// يتم استخدام ProxyProvider لحقن التبعيات بشكل صحيح.
  ///
  /// **الـ Presenters المعقدة:**
  /// - 🛒 POSPresenter: يعتمد على ProductPresenter و CustomerPresenter
  static List<SingleChildWidget> getComplexProviders() {
    return [
      // 🛒 مقدم نقطة البيع - يحتاج إلى مقدم المنتجات والعملاء
      ProxyProvider2<ProductPresenter, CustomerPresenter, POSPresenter>(
        update: (_, productPresenter, customerPresenter, previous) =>
            previous ??
            POSPresenter(
              productPresenter: productPresenter,
              customerPresenter: customerPresenter,
            ),
      ),
    ];
  }

  /// 🌟 الحصول على جميع الـ providers (أساسية + كسولة + معقدة)
  ///
  /// هذه الدالة تجمع جميع الـ providers في التطبيق.
  ///
  /// ⚠️ **تحذير:** استخدام هذه الدالة قد يؤثر على أداء التطبيق
  /// لأنها تحمل جميع الـ Presenters مرة واحدة.
  /// يُنصح باستخدام getMinimalProviders() بدلاً من ذلك.
  static List<SingleChildWidget> getAllProviders() {
    return getAllProvidersLegacy();
  }

  /// ⭐ الحصول على الـ providers الأساسية فقط (محسن للأداء)
  ///
  /// 🚀 **نظام تحميل كسول حقيقي - يحمل فقط ما هو ضروري للبدء**
  ///
  /// يحتوي على الـ Presenters الأساسية فقط:
  /// - ThemeManager (ضروري للثيمات)
  /// - DashboardPresenter (الشاشة الرئيسية)
  /// - SettingsPresenter (الإعدادات الأساسية)
  ///
  /// جميع الـ Presenters الأخرى يتم تحميلها كسولياً عند الحاجة
  static List<SingleChildWidget> getMinimalProviders() {
    return [
      ...getCoreProviders(),
      // لا نحمل أي presenters إضافية هنا!
      // سيتم تحميلها كسولياً عند الحاجة
    ];
  }

  /// 🔄 الحصول على presenter محدد بشكل كسول
  /// يستخدم هذا في الشاشات التي تحتاج presenter معين
  static T getLazyPresenter<T extends ChangeNotifier>(T Function() factory) {
    return LazyPresenterManager.getOrCreate<T>(factory);
  }

  /// 📝 تسجيل presenter مُهيأ مسبقاً في النظام الكسول
  /// يستخدم لحفظ presenters تم تهيئتها في main.dart
  static void registerLazyPresenter<T extends ChangeNotifier>(T instance) {
    LazyPresenterManager.registerInstance<T>(instance);
  }

  /// �️ دوال مساعدة للحصول على presenters شائعة الاستخدام

  /// 📦 الحصول على ProductPresenter
  static ProductPresenter getProductPresenter() {
    return getLazyPresenter<ProductPresenter>(() => ProductPresenter());
  }

  /// 👥 الحصول على CustomerPresenter
  static CustomerPresenter getCustomerPresenter() {
    return getLazyPresenter<CustomerPresenter>(() => CustomerPresenter());
  }

  /// 💼 الحصول على SupplierPresenter
  static SupplierPresenter getSupplierPresenter() {
    return getLazyPresenter<SupplierPresenter>(() => SupplierPresenter());
  }

  /// 📊 الحصول على SalePresenter
  static SalePresenter getSalePresenter() {
    return getLazyPresenter<SalePresenter>(() => SalePresenter());
  }

  /// 💰 الحصول على AccountPresenter
  static AccountPresenter getAccountPresenter() {
    return getLazyPresenter<AccountPresenter>(() => AccountPresenter());
  }

  /// 📊 الحصول على ReportsPresenter
  static ReportsPresenter getReportsPresenter() {
    return getLazyPresenter<ReportsPresenter>(() => ReportsPresenter());
  }

  /// 🏢 الحصول على WarehousePresenter
  static WarehousePresenter getWarehousePresenter() {
    return getLazyPresenter<WarehousePresenter>(() => WarehousePresenter());
  }

  /// 💸 الحصول على ExpensePresenter
  static ExpensePresenter getExpensePresenter() {
    return getLazyPresenter<ExpensePresenter>(() => ExpensePresenter());
  }

  /// 📋 الحصول على CategoryPresenter
  static CategoryPresenter getCategoryPresenter() {
    return getLazyPresenter<CategoryPresenter>(() => CategoryPresenter());
  }

  /// 📎 الحصول على UnitPresenter
  static UnitPresenter getUnitPresenter() {
    return getLazyPresenter<UnitPresenter>(() => UnitPresenter());
  }

  /// 💰 الحصول على PurchasePresenter
  static PurchasePresenter getPurchasePresenter() {
    return getLazyPresenter<PurchasePresenter>(() => PurchasePresenter());
  }

  /// 📦 الحصول على InventoryPresenter
  static InventoryPresenter getInventoryPresenter() {
    return getLazyPresenter<InventoryPresenter>(() => InventoryPresenter());
  }

  /// 🔄 الحصول على InventoryTransferPresenter
  static InventoryTransferPresenter getInventoryTransferPresenter() {
    return getLazyPresenter<InventoryTransferPresenter>(
        () => InventoryTransferPresenter());
  }

  /// ⚙️ الحصول على InventoryAdjustmentPresenter
  static InventoryAdjustmentPresenter getInventoryAdjustmentPresenter() {
    return getLazyPresenter<InventoryAdjustmentPresenter>(
        () => InventoryAdjustmentPresenter());
  }

  /// 👥 الحصول على UserPresenter
  static UserPresenter getUserPresenter() {
    return getLazyPresenter<UserPresenter>(() => UserPresenter());
  }

  /// 🔐 الحصول على PermissionPresenter
  static PermissionPresenter getPermissionPresenter() {
    return getLazyPresenter<PermissionPresenter>(() => PermissionPresenter());
  }

  /// 🏆 الحصول على RolePresenter
  static RolePresenter getRolePresenter() {
    return getLazyPresenter<RolePresenter>(() => RolePresenter());
  }

  /// 💱 الحصول على CurrencyPresenter
  static CurrencyPresenter getCurrencyPresenter() {
    return getLazyPresenter<CurrencyPresenter>(() => CurrencyPresenter());
  }

  /// 🎫 الحصول على VoucherPresenter
  static VoucherPresenter getVoucherPresenter() {
    return getLazyPresenter<VoucherPresenter>(() => VoucherPresenter());
  }

  /// 🎯 الحصول على PromotionPresenter
  static PromotionPresenter getPromotionPresenter() {
    return getLazyPresenter<PromotionPresenter>(() => PromotionPresenter());
  }

  /// 🔗 الحصول على SimpleTiePresenter
  static SimpleTiePresenter getSimpleTiePresenter() {
    return getLazyPresenter<SimpleTiePresenter>(() => SimpleTiePresenter());
  }

  /// 📝 الحصول على JournalEntryPresenter
  static JournalEntryPresenter getJournalEntryPresenter() {
    return getLazyPresenter<JournalEntryPresenter>(
        () => JournalEntryPresenter());
  }

  /// 🛒 الحصول على POSPresenter
  static POSPresenter getPOSPresenter() {
    return getLazyPresenter<POSPresenter>(() => POSPresenter(
          productPresenter: getProductPresenter(),
          customerPresenter: getCustomerPresenter(),
        ));
  }

  /// �🚀 الحصول على جميع الـ providers (للاستخدام في حالات خاصة فقط)
  ///
  /// ⚠️ **تحذير:** هذه الدالة تحمل جميع الـ Presenters مرة واحدة
  /// وقد تؤثر على أداء التطبيق. استخدمها فقط عند الضرورة القصوى.
  static List<SingleChildWidget> getAllProvidersLegacy() {
    return [
      ...getCoreProviders(),

      // ═══════════════════════════════════════════════════════════════
      // 🔐 مقدمات إدارة المستخدمين والصلاحيات
      // ═══════════════════════════════════════════════════════════════
      ChangeNotifierProvider(create: (_) => PermissionPresenter()),
      ChangeNotifierProvider(create: (_) => RolePresenter()),
      ChangeNotifierProvider(create: (_) => UserPresenter()),

      // ═══════════════════════════════════════════════════════════════
      // 💰 مقدمات النظام المحاسبي والمالي
      // ═══════════════════════════════════════════════════════════════
      ChangeNotifierProvider(create: (_) => AccountPresenter()),
      ChangeNotifierProvider(create: (_) => JournalEntryPresenter()),
      ChangeNotifierProvider(create: (_) => CurrencyPresenter()),
      ChangeNotifierProvider(create: (_) => VoucherPresenter()),

      // ═══════════════════════════════════════════════════════════════
      // 🛍️ مقدمات إدارة المبيعات والمشتريات
      // ═══════════════════════════════════════════════════════════════
      ChangeNotifierProvider(create: (_) => SalePresenter()),
      ChangeNotifierProvider(create: (_) => PurchasePresenter()),
      ChangeNotifierProvider(create: (_) => CustomerPresenter()),
      ChangeNotifierProvider(create: (_) => SupplierPresenter()),

      // ═══════════════════════════════════════════════════════════════
      // 📦 مقدمات إدارة المخزون والمنتجات
      // ═══════════════════════════════════════════════════════════════
      ChangeNotifierProvider(create: (_) => ProductPresenter()),
      ChangeNotifierProvider(create: (_) => CategoryPresenter()),
      ChangeNotifierProvider(create: (_) => UnitPresenter()),
      ChangeNotifierProvider(create: (_) => WarehousePresenter()),
      ChangeNotifierProvider(create: (_) => InventoryPresenter()),
      ChangeNotifierProvider(create: (_) => InventoryTransferPresenter()),
      ChangeNotifierProvider(create: (_) => InventoryAdjustmentPresenter()),

      // ═══════════════════════════════════════════════════════════════
      // 💸 مقدمات إدارة المصروفات والعمليات المالية
      // ═══════════════════════════════════════════════════════════════
      ChangeNotifierProvider(create: (_) => ExpensePresenter()),
      ChangeNotifierProvider(create: (_) => SimpleTiePresenter()),
      ChangeNotifierProvider(create: (_) => PromotionPresenter()),

      // ═══════════════════════════════════════════════════════════════
      // 📊 مقدمات التقارير والإحصائيات
      // ═══════════════════════════════════════════════════════════════
      ChangeNotifierProvider(create: (_) => ReportsPresenter()),

      ...getComplexProviders(),
    ];
  }

  /// 📊 الحصول على إحصائيات الأداء واستخدام الذاكرة
  static Map<String, dynamic> getPerformanceStats() {
    final stats = LazyPresenterManager.getMemoryStats();
    final loadedCount = LazyPresenterManager.loadedCount;

    return {
      'lazy_loading_enabled': true,
      'core_providers_count':
          2, // DashboardPresenter, SettingsPresenter (ThemeManager في النظام الكسول)
      'loaded_presenters_count': loadedCount,
      'memory_usage_estimate': '${loadedCount * 50}KB',
      'performance_improvement': loadedCount < 10
          ? 'ممتاز'
          : loadedCount < 20
              ? 'جيد'
              : 'يحتاج تحسين',
      'detailed_stats': stats,
    };
  }

  /// 🧠 تنظيف ذكي للذاكرة - يزيل الـ presenters غير المستخدمة
  static void smartCleanup() {
    LazyPresenterManager.cleanupOldPresenters(
      maxAge: const Duration(minutes: 30), // تنظيف الـ presenters القديمة
    );
    AppLogger.info('🧠 تم تنظيف الذاكرة بشكل ذكي');
  }

  /// 🔄 إعادة تشغيل presenter محدد
  static void restartPresenter<T extends ChangeNotifier>(T Function() factory) {
    LazyPresenterManager.restart<T>(factory);
    AppLogger.info('🔄 تم إعادة تشغيل ${T.toString()}');
  }

  /// تنظيف الموارد عند إغلاق التطبيق
  static void dispose() {
    LazyPresenterManager.clearAll();
    AppLogger.info('🗑️ تم تنظيف جميع الموارد');
  }
}

/// مدير التحميل الكسول المحسن للـ Presenters
class LazyPresenterManager {
  static final Map<Type, dynamic> _instances = {};
  static final Map<Type, bool> _isLoading = {};
  static final Map<Type, DateTime> _creationTimes = {};

  /// الحصول على presenter بشكل كسول أو إنشاؤه إذا لم يكن موجوداً
  static T getOrCreate<T>(T Function() factory) {
    if (!_instances.containsKey(T)) {
      // منع إنشاء متعدد للـ presenter نفسه
      if (_isLoading[T] == true) {
        // انتظار حتى ينتهي الإنشاء مع timeout
        int attempts = 0;
        while (_isLoading[T] == true && attempts < 100) {
          attempts++;
          // انتظار قصير جداً
          Future.delayed(const Duration(milliseconds: 10));
        }

        if (_instances.containsKey(T)) {
          return _instances[T] as T;
        }
      }

      _isLoading[T] = true;
      try {
        final instance = factory();
        _instances[T] = instance;
        _creationTimes[T] = DateTime.now();

        // تهيئة الـ presenter إذا كان يحتوي على دالة init
        if (instance is BasePresenter) {
          instance.init().catchError((e) {
            // تسجيل الخطأ بدون إيقاف التطبيق
            AppLogger.error('خطأ في تهيئة ${T.toString()}: $e');
          });
        }

        return instance;
      } finally {
        _isLoading[T] = false;
      }
    }
    return _instances[T] as T;
  }

  /// الحصول على presenter بشكل كسول (للتوافق مع الكود القديم)
  static T getLazyPresenter<T>(T Function() factory) {
    return getOrCreate<T>(factory);
  }

  /// تسجيل instance مُهيأ مسبقاً
  static void registerInstance<T>(T instance) {
    if (!_instances.containsKey(T)) {
      _instances[T] = instance;
      _creationTimes[T] = DateTime.now();
    }
  }

  /// التحقق من وجود presenter
  static bool exists<T>() {
    return _instances.containsKey(T);
  }

  /// الحصول على عدد الـ presenters المحملة
  static int get loadedCount => _instances.length;

  /// الحصول على قائمة بأنواع الـ presenters المحملة
  static List<Type> get loadedTypes => _instances.keys.toList();

  /// تنظيف جميع الـ instances
  static void clearAll() {
    // تنظيف الـ presenters التي تحتاج dispose
    for (var instance in _instances.values) {
      if (instance is ChangeNotifier) {
        instance.dispose();
      }
    }
    _instances.clear();
    _isLoading.clear();
    _creationTimes.clear();
  }

  /// تنظيف presenter محدد
  static void clear<T>() {
    final instance = _instances[T];
    if (instance is ChangeNotifier) {
      instance.dispose();
    }
    _instances.remove(T);
    _isLoading.remove(T);
    _creationTimes.remove(T);
  }

  /// إحصائيات استخدام الذاكرة المحسنة
  static Map<String, dynamic> getMemoryStats() {
    final now = DateTime.now();
    final stats = <String, dynamic>{
      'loaded_presenters': loadedCount,
      'types': loadedTypes.map((t) => t.toString()).toList(),
      'memory_usage_estimate': '${loadedCount * 50}KB',
      'creation_times': {},
      'uptime_minutes': {},
    };

    // إضافة معلومات وقت الإنشاء ومدة التشغيل
    for (final entry in _creationTimes.entries) {
      final type = entry.key.toString();
      final creationTime = entry.value;
      final uptime = now.difference(creationTime);

      stats['creation_times'][type] = creationTime.toIso8601String();
      stats['uptime_minutes'][type] = uptime.inMinutes;
    }

    return stats;
  }

  /// تنظيف الـ presenters القديمة (التي لم تستخدم لفترة طويلة)
  static void cleanupOldPresenters(
      {Duration maxAge = const Duration(hours: 1)}) {
    final now = DateTime.now();
    final toRemove = <Type>[];

    for (final entry in _creationTimes.entries) {
      final type = entry.key;
      final creationTime = entry.value;

      if (now.difference(creationTime) > maxAge) {
        toRemove.add(type);
      }
    }

    for (final type in toRemove) {
      final instance = _instances[type];
      if (instance is ChangeNotifier) {
        instance.dispose();
      }
      _instances.remove(type);
      _isLoading.remove(type);
      _creationTimes.remove(type);

      AppLogger.info('تم تنظيف presenter قديم: ${type.toString()}');
    }
  }

  /// إعادة تشغيل presenter محدد
  static void restart<T>(T Function() factory) {
    clear<T>();
    getOrCreate<T>(factory);
  }
}

/// Widget wrapper للتحميل الكسول للـ Presenters
/// يوفر طريقة سهلة لاستخدام التحميل الكسول في الشاشات
class LazyProviderWrapper<T extends ChangeNotifier> extends StatefulWidget {
  final T Function() presenterFactory;
  final Widget Function(BuildContext context, T presenter) builder;
  final String? presenterName;

  const LazyProviderWrapper({
    Key? key,
    required this.presenterFactory,
    required this.builder,
    this.presenterName,
  }) : super(key: key);

  @override
  State<LazyProviderWrapper<T>> createState() => _LazyProviderWrapperState<T>();
}

class _LazyProviderWrapperState<T extends ChangeNotifier>
    extends State<LazyProviderWrapper<T>> {
  late T _presenter;

  @override
  void initState() {
    super.initState();
    _presenter = LazyPresenterManager.getOrCreate<T>(widget.presenterFactory);

    if (widget.presenterName != null) {
      AppLogger.info('🚀 تم تحميل ${widget.presenterName} بشكل كسول');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _presenter,
      builder: (context, child) => widget.builder(context, _presenter),
    );
  }
}
