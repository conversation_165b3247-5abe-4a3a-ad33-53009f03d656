import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/index.dart';
import '../../../core/models/sale.dart';
import '../../../core/models/sale_item.dart';
import '../../../core/models/sale_status.dart';

/// مربع حوار تفاصيل البيع
class SaleDetailsDialog extends StatelessWidget {
  final Sale sale;

  const SaleDetailsDialog({
    Key? key,
    required this.sale,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('تفاصيل الفاتورة #${sale.id.substring(0, 8)}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الفاتورة
            _buildInfoCard(
              title: 'معلومات الفاتورة',
              children: [
                _buildInfoRow('رقم الفاتورة:', sale.id.substring(0, 8)),
                _buildInfoRow('التاريخ:',
                    DateFormat('yyyy-MM-dd HH:mm').format(sale.createdAt)),
                _buildInfoRow('الحالة:', getSaleStatusName(sale.status)),
                if (sale.notes != null && sale.notes!.isNotEmpty)
                  _buildInfoRow('ملاحظات:', sale.notes!),
              ],
            ),
            const SizedBox(height: 16),

            // معلومات العميل
            _buildInfoCard(
              title: 'معلومات العميل',
              children: [
                _buildInfoRow('العميل:', sale.customerName ?? 'عميل نقدي'),
                if (sale.customerId != null)
                  _buildInfoRow('رقم العميل:', sale.customerId!),
              ],
            ),
            const SizedBox(height: 16),

            // معلومات الدفع
            _buildInfoCard(
              title: 'معلومات الدفع',
              children: [
                _buildInfoRow(
                    'طريقة الدفع:', _getPaymentMethodName(sale.paymentMethod)),
                _buildInfoRow(
                    'المبلغ المدفوع:', sale.amountPaid.toStringAsFixed(2)),
                _buildInfoRow('المبلغ المتبقي:',
                    (sale.total - sale.amountPaid).toStringAsFixed(2)),
              ],
            ),
            const SizedBox(height: 16),

            // عناصر الفاتورة
            _buildItemsCard(),
            const SizedBox(height: 16),

            // ملخص الفاتورة
            _buildSummaryCard(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('إغلاق'),
        ),
        ElevatedButton.icon(
          onPressed: () {
            // TODO: طباعة الفاتورة
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('جاري طباعة الفاتورة...'),
              ),
            );
          },
          icon: const Icon(Icons.print),
          label: const Text('طباعة'),
        ),
      ],
    );
  }

  /// بناء بطاقة معلومات
  Widget _buildInfoCard({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTypography.lightTextTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ) ??
                  const AppTypography(fontWeight: FontWeight.bold),
            ),
            const Divider(),
            ...children,
          ],
        ),
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const AppTypography(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة العناصر
  Widget _buildItemsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'عناصر الفاتورة',
              style: AppTypography.lightTextTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ) ??
                  const AppTypography(fontWeight: FontWeight.bold),
            ),
            const Divider(),

            // عناوين الجدول
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  Expanded(
                    flex: 4,
                    child: Text(
                      'الصنف',
                      style: AppTypography(fontWeight: FontWeight.bold),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'الكمية',
                      style: AppTypography(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'السعر',
                      style: AppTypography(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'المجموع',
                      style: AppTypography(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),
            ),

            // عناصر الفاتورة
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: sale.items.length,
              itemBuilder: (context, index) {
                final item = sale.items[index];
                return _buildItemRow(item);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صف عنصر
  Widget _buildItemRow(SaleItem item) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 4,
            child: Text(
              item.productName ?? 'غير معروف',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              item.quantity.toString(),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              item.price.toStringAsFixed(2),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              item.total.toStringAsFixed(2),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الملخص
  Widget _buildSummaryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الفاتورة',
              style: AppTypography.lightTextTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ) ??
                  const AppTypography(fontWeight: FontWeight.bold),
            ),
            const Divider(),

            // المجموع الفرعي
            _buildSummaryRow(
                'المجموع الفرعي:', sale.subtotal.toStringAsFixed(2)),

            // الخصم
            if (sale.discount > 0)
              _buildSummaryRow(
                'الخصم:',
                '${sale.discount.toStringAsFixed(2)} ${sale.isDiscountPercentage ? '%' : ''}',
              ),

            // الضريبة
            _buildSummaryRow('الضريبة:', sale.tax.toStringAsFixed(2)),

            const Divider(),

            // الإجمالي
            _buildSummaryRow(
              'الإجمالي:',
              sale.total.toStringAsFixed(2),
              isBold: true,
            ),

            // المبلغ المدفوع
            _buildSummaryRow(
                'المبلغ المدفوع:', sale.amountPaid.toStringAsFixed(2)),

            // المبلغ المتبقي أو الباقي
            _buildRemainingAmountRow(sale.total - sale.amountPaid),
          ],
        ),
      ),
    );
  }

  /// بناء صف ملخص
  Widget _buildSummaryRow(
    String label,
    String value, {
    bool isBold = false,
    Color? textColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTypography(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: AppTypography(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف المبلغ المتبقي
  Widget _buildRemainingAmountRow(double remainingAmount) {
    if (remainingAmount > 0) {
      return _buildSummaryRow(
        'المبلغ المتبقي:',
        remainingAmount.toStringAsFixed(2),
        textColor: AppColors.error,
      );
    } else if (remainingAmount < 0) {
      return _buildSummaryRow(
        'الباقي:',
        (-remainingAmount).toStringAsFixed(2),
        textColor: AppColors.success,
      );
    } else {
      return _buildSummaryRow(
        'المبلغ المتبقي:',
        '0.00',
        textColor: AppColors.lightTextPrimary,
      );
    }
  }

  // تم تعليق هذه الدالة لأنها غير مستخدمة
  /*
  String _getStatusName(String status) {
    switch (status) {
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'draft':
        return 'مسودة';
      case 'returned':
        return 'مرتجع';
      default:
        return status;
    }
  }
  */

  /// الحصول على اسم طريقة الدفع
  String _getPaymentMethodName(String method) {
    switch (method) {
      case 'cash':
        return 'نقداً';
      case 'credit_card':
        return 'بطاقة ائتمان';
      case 'debit_card':
        return 'بطاقة خصم';
      case 'bank_transfer':
        return 'تحويل بنكي';
      case 'check':
        return 'شيك';
      case 'credit':
        return 'آجل';
      case 'other':
        return 'أخرى';
      default:
        return method;
    }
  }
}
