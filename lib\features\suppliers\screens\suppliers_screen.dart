import 'package:flutter/material.dart';
import '../../../core/models/supplier.dart';
import '../presenters/supplier_presenter.dart';

import 'supplier_form_screen.dart';
import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';
import '../../../core/providers/app_providers.dart'; // للتحميل الكسول

class SuppliersScreen extends StatefulWidget {
  const SuppliersScreen({Key? key}) : super(key: key);

  @override
  State<SuppliersScreen> createState() => _SuppliersScreenState();
}

class _SuppliersScreenState extends State<SuppliersScreen> {
  late SupplierPresenter _presenter;
  final _searchController = TextEditingController();
  bool _showSearchField = false; // متغير لإظهار/إخفاء حقل البحث

  @override
  void initState() {
    super.initState();
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    _presenter = AppProviders.getLazyPresenter<SupplierPresenter>(
      () => SupplierPresenter(),
    );
    _presenter.loadSuppliers();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    _presenter.searchSuppliers(_searchController.text);
  }

  /// عرض رسالة في شريط Snackbar
  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'إدارة الموردين',
        actions: [
          // أيقونة البحث (نفس آلية UsersScreen)
          IconButton(
            icon: Icon(_showSearchField ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                }
              });
            },
          ),
          // أيقونة الإضافة
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'إضافة مورد جديد',
            onPressed: () => _navigateToForm(context),
          ),
        ],
      ),
      floatingActionButton: AkFloatingButton(
        icon: Icons.add,
        onPressed: () => _navigateToForm(context),
        tooltip: 'إضافة مورد جديد',
      ),
      body: Column(
        children: [
          // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث - نفس آلية UsersScreen)
          if (_showSearchField)
            Container(
              padding: AppDimensions.getResponsivePadding(
                  horizontal: 4, vertical: 2),
              child: AkSearchInput(
                controller: _searchController,
                hint: 'بحث في الموردين (الاسم، الهاتف، البريد الإلكتروني)...',
                onChanged: (value) {
                  // منطق البحث سيتم تطبيقه تلقائياً عبر المستمع في initState
                },
                onClear: () {
                  _searchController.clear();
                },
              ),
            ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const SizedBox(height: 8),
                ListenableBuilder(
                  listenable: _presenter,
                  builder: (context, child) {
                    // Nota: Esta funcionalidad no está implementada en el presenter actual
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
          Expanded(
            child: ListenableBuilder(
              listenable: _presenter,
              builder: (context, child) {
                if (_presenter.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (_presenter.errorMessage != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Error: ${_presenter.errorMessage}',
                          style: const AppTypography(color: AppColors.error),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => _presenter.loadSuppliers(),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                final suppliers = _presenter.suppliers;
                if (suppliers.isEmpty) {
                  if (_searchController.text.isNotEmpty) {
                    return const Center(
                      child: Text('No suppliers found matching your search'),
                    );
                  }
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text('No suppliers found'),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => _navigateToForm(context),
                          child: const Text('Add Supplier'),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: suppliers.length,
                  itemBuilder: (context, index) {
                    final supplier = suppliers[index];
                    return _buildSupplierCard(context, supplier);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSupplierCard(BuildContext context, Supplier supplier) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: supplier.isActive
              ? AppColors.primary
              : AppColors.lightTextSecondary,
          child: Text(
            supplier.name[0].toUpperCase(),
            style: const AppTypography(color: AppColors.onPrimary),
          ),
        ),
        title: Text(
          supplier.name,
          style: AppTypography(
            fontWeight: FontWeight.bold,
            color: supplier.isActive ? null : AppColors.lightTextSecondary,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (supplier.phone != null) Text(supplier.phone!),
            if (supplier.email != null) Text(supplier.email!),
            if (supplier.address != null)
              Text(
                supplier.address!,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _navigateToForm(context, supplier),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteConfirmation(context, supplier),
            ),
          ],
        ),
        onTap: () => _navigateToForm(context, supplier),
      ),
    );
  }

  Future<void> _navigateToForm(BuildContext context,
      [Supplier? supplier]) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SupplierFormScreen(supplier: supplier),
      ),
    );

    if (result == true) {
      _presenter.loadSuppliers();
    }
  }

  Future<void> _showDeleteConfirmation(
      BuildContext context, Supplier supplier) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Supplier'),
        content: Text('Are you sure you want to delete ${supplier.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final success = await _presenter.deleteSupplier(supplier.id);
      if (mounted) {
        _showSnackBar(
          success
              ? 'Supplier deleted successfully'
              : 'Failed to delete supplier',
          isError: !success,
        );
      }
    }
  }
}
