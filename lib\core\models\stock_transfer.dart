import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج تحويل المخزون الموحد
/// تم توحيده من جميع نماذج تحويل المخزون في المشروع
class StockTransfer extends BaseModel {
  // معلومات أساسية
  final String transferId;
  final String? reference;
  final DateTime transferDate;
  
  // معلومات المستودعات
  final String sourceWarehouseId;
  final String sourceWarehouseName;
  final String destinationWarehouseId;
  final String destinationWarehouseName;
  
  // معلومات الحالة
  final String status; // draft, pending, completed, cancelled
  final bool isApproved;
  final String? approvedBy;
  final DateTime? approvedAt;
  
  // معلومات إضافية
  final String? notes;
  final String? reason;
  final Map<String, dynamic>? metadata;
  final List<StockTransferItem>? items;

  StockTransfer({
    String? id,
    required this.transferId,
    this.reference,
    required this.transferDate,
    required this.sourceWarehouseId,
    required this.sourceWarehouseName,
    required this.destinationWarehouseId,
    required this.destinationWarehouseName,
    this.status = 'draft',
    this.isApproved = false,
    this.approvedBy,
    this.approvedAt,
    this.notes,
    this.reason,
    this.metadata,
    this.items,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا التحويل مع استبدال الحقول المحددة بقيم جديدة
  StockTransfer copyWith({
    String? id,
    String? transferId,
    String? reference,
    DateTime? transferDate,
    String? sourceWarehouseId,
    String? sourceWarehouseName,
    String? destinationWarehouseId,
    String? destinationWarehouseName,
    String? status,
    bool? isApproved,
    String? approvedBy,
    DateTime? approvedAt,
    String? notes,
    String? reason,
    Map<String, dynamic>? metadata,
    List<StockTransferItem>? items,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return StockTransfer(
      id: id ?? this.id,
      transferId: transferId ?? this.transferId,
      reference: reference ?? this.reference,
      transferDate: transferDate ?? this.transferDate,
      sourceWarehouseId: sourceWarehouseId ?? this.sourceWarehouseId,
      sourceWarehouseName: sourceWarehouseName ?? this.sourceWarehouseName,
      destinationWarehouseId:
          destinationWarehouseId ?? this.destinationWarehouseId,
      destinationWarehouseName:
          destinationWarehouseName ?? this.destinationWarehouseName,
      status: status ?? this.status,
      isApproved: isApproved ?? this.isApproved,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      notes: notes ?? this.notes,
      reason: reason ?? this.reason,
      metadata: metadata ?? this.metadata,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل تحويل المخزون إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'transfer_id': transferId,
      'reference': reference,
      'transfer_date': transferDate.toIso8601String(),
      'source_warehouse_id': sourceWarehouseId,
      'source_warehouse_name': sourceWarehouseName,
      'destination_warehouse_id': destinationWarehouseId,
      'destination_warehouse_name': destinationWarehouseName,
      'status': status,
      'is_approved': isApproved ? 1 : 0,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
      'notes': notes,
      'reason': reason,
      'metadata': metadata != null ? jsonEncode(metadata) : null,
      'items': items?.map((item) => item.toMap()).toList(),
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء تحويل مخزون من Map
  factory StockTransfer.fromMap(Map<String, dynamic> map) {
    return StockTransfer(
      id: map['id'],
      transferId: map['transfer_id'] ?? '',
      reference: map['reference'],
      transferDate: map['transfer_date'] != null
          ? DateTime.parse(map['transfer_date'])
          : DateTime.now(),
      sourceWarehouseId: map['source_warehouse_id'] ?? '',
      sourceWarehouseName: map['source_warehouse_name'] ?? '',
      destinationWarehouseId: map['destination_warehouse_id'] ?? '',
      destinationWarehouseName: map['destination_warehouse_name'] ?? '',
      status: map['status'] ?? 'draft',
      isApproved: map['is_approved'] == 1 || map['is_approved'] == true,
      approvedBy: map['approved_by'],
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'])
          : null,
      notes: map['notes'],
      reason: map['reason'],
      metadata: map['metadata'] != null
          ? (map['metadata'] is String
              ? jsonDecode(map['metadata'])
              : map['metadata'] as Map<String, dynamic>)
          : null,
      items: map['items'] != null
          ? List<StockTransferItem>.from(
              (map['items'] as List).map((x) => StockTransferItem.fromMap(x)))
          : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1 || map['is_deleted'] == true,
    );
  }

  /// تحويل تحويل المخزون إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء تحويل مخزون من JSON
  factory StockTransfer.fromJson(String source) =>
      StockTransfer.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'StockTransfer(id: $id, transferId: $transferId, status: $status)';
  }
}

/// نموذج عنصر تحويل المخزون
class StockTransferItem {
  final String id;
  final String productId;
  final String productName;
  final String? productCode;
  final String? productSku;
  final String? unitId;
  final String? unitName;
  final double quantity;
  final double? cost;
  final String? notes;
  final bool isTransferred;
  final DateTime? transferredAt;

  StockTransferItem({
    String? id,
    required this.productId,
    required this.productName,
    this.productCode,
    this.productSku,
    this.unitId,
    this.unitName,
    required this.quantity,
    this.cost,
    this.notes,
    this.isTransferred = false,
    this.transferredAt,
  }) : id = id ?? const Uuid().v4();

  /// إنشاء نسخة من هذا العنصر مع استبدال الحقول المحددة بقيم جديدة
  StockTransferItem copyWith({
    String? id,
    String? productId,
    String? productName,
    String? productCode,
    String? productSku,
    String? unitId,
    String? unitName,
    double? quantity,
    double? cost,
    String? notes,
    bool? isTransferred,
    DateTime? transferredAt,
  }) {
    return StockTransferItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      productSku: productSku ?? this.productSku,
      unitId: unitId ?? this.unitId,
      unitName: unitName ?? this.unitName,
      quantity: quantity ?? this.quantity,
      cost: cost ?? this.cost,
      notes: notes ?? this.notes,
      isTransferred: isTransferred ?? this.isTransferred,
      transferredAt: transferredAt ?? this.transferredAt,
    );
  }

  /// تحويل عنصر تحويل المخزون إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'product_name': productName,
      'product_code': productCode,
      'product_sku': productSku,
      'unit_id': unitId,
      'unit_name': unitName,
      'quantity': quantity,
      'cost': cost,
      'notes': notes,
      'is_transferred': isTransferred ? 1 : 0,
      'transferred_at': transferredAt?.toIso8601String(),
    };
  }

  /// إنشاء عنصر تحويل مخزون من Map
  factory StockTransferItem.fromMap(Map<String, dynamic> map) {
    return StockTransferItem(
      id: map['id'] ?? const Uuid().v4(),
      productId: map['product_id'] ?? '',
      productName: map['product_name'] ?? '',
      productCode: map['product_code'],
      productSku: map['product_sku'],
      unitId: map['unit_id'],
      unitName: map['unit_name'],
      quantity: map['quantity'] is int
          ? (map['quantity'] as int).toDouble()
          : (map['quantity'] as double? ?? 0.0),
      cost: map['cost'] is int
          ? (map['cost'] as int).toDouble()
          : (map['cost'] as double?),
      notes: map['notes'],
      isTransferred:
          map['is_transferred'] == 1 || map['is_transferred'] == true,
      transferredAt: map['transferred_at'] != null
          ? DateTime.parse(map['transferred_at'])
          : null,
    );
  }

  /// تحويل عنصر تحويل المخزون إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء عنصر تحويل مخزون من JSON
  factory StockTransferItem.fromJson(String source) =>
      StockTransferItem.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'StockTransferItem(id: $id, productName: $productName, quantity: $quantity)';
  }
}
