import 'package:uuid/uuid.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/database/database_service.dart';
import '../../../core/models/currency.dart';
import '../../../core/providers/base_presenter.dart';

/// مقدم العملات
class CurrencyPresenter extends BaseListPresenter<Currency> {
  final DatabaseService _db = DatabaseService.instance;

  // Getters
  List<Currency> get currencies => items;

  // تنفيذ الدوال المطلوبة من BaseListPresenter
  @override
  Future<List<Currency>> loadItemsFromSource() async {
    final result = await _db.query(
      'currencies',
      where: 'is_deleted = 0',
      orderBy: 'is_default DESC, name ASC',
    );

    final currencies = result.map((map) => Currency.fromMap(map)).toList();

    // إذا لم تكن هناك عملات، قم بإنشاء العملة الافتراضية
    if (currencies.isEmpty) {
      await _createDefaultCurrency();
      // إعادة تحميل العملات بعد إنشاء العملة الافتراضية
      final newResult = await _db.query(
        'currencies',
        where: 'is_deleted = 0',
        orderBy: 'is_default DESC, name ASC',
      );
      return newResult.map((map) => Currency.fromMap(map)).toList();
    }

    return currencies;
  }

  @override
  bool matchesSearch(Currency item, String query) {
    final lowerQuery = query.toLowerCase();
    return item.name.toLowerCase().contains(lowerQuery) ||
        item.code.toLowerCase().contains(lowerQuery) ||
        (item.symbol?.toLowerCase().contains(lowerQuery) ?? false);
  }

  @override
  Future<void> init() async {
    await loadItems();
  }

  // الحصول على العملة الافتراضية
  Currency? get defaultCurrency {
    try {
      return items.firstWhere((currency) => currency.isDefault);
    } catch (e) {
      return items.isNotEmpty ? items.first : null;
    }
  }

  // تحميل العملات (للتوافق مع الكود القديم)
  Future<void> loadCurrencies() async {
    await loadItems();
  }

  // إنشاء العملة الافتراضية (تم تعطيله - يتم في BasicDataInitializer)
  Future<void> _createDefaultCurrency() async {
    AppLogger.info('تم تعطيل إنشاء العملة الافتراضية في CurrencyPresenter');
    AppLogger.info('يتم تهيئة العملات في BasicDataInitializer فقط');

    // لا نقوم بأي عملية هنا لتجنب التداخل مع BasicDataInitializer
    return;
  }

  // إضافة عملة جديدة
  Future<bool> addCurrency(Currency currency) async {
    try {
      // إنشاء معرف جديد إذا لم يكن موجودًا
      final String id = currency.id.isEmpty ? const Uuid().v4() : currency.id;

      // تحديث العملة بالمعرف
      final updatedCurrency = currency.copyWith(
        id: id,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // إذا كانت العملة الجديدة هي الافتراضية، قم بإلغاء تعيين العملة الافتراضية السابقة
      if (updatedCurrency.isDefault) {
        await _db.update(
          'currencies',
          {'is_default': 0, 'updated_at': DateTime.now().toIso8601String()},
          where: 'is_default = 1',
        );
      }

      // حفظ العملة في قاعدة البيانات
      await _db.insert('currencies', updatedCurrency.toMap());

      // إعادة تحميل العملات لتحديث القائمة
      await loadCurrencies();

      return true;
    } catch (e, stackTrace) {
      _handleError('فشل في إضافة العملة', e, stackTrace);
      return false;
    }
  }

  // تعديل عملة
  Future<bool> updateCurrency(Currency currency) async {
    try {
      // الحصول على العملة القديمة
      final oldCurrencyIndex = items.indexWhere((c) => c.id == currency.id);
      if (oldCurrencyIndex == -1) {
        throw Exception('العملة غير موجودة');
      }

      final oldCurrency = items[oldCurrencyIndex];

      // تحديث العملة بتاريخ التحديث
      final updatedCurrency = currency.copyWith(
        updatedAt: DateTime.now(),
      );

      // إذا كانت العملة المحدثة هي الافتراضية وكانت العملة القديمة ليست افتراضية
      if (updatedCurrency.isDefault && !oldCurrency.isDefault) {
        await _db.update(
          'currencies',
          {'is_default': 0, 'updated_at': DateTime.now().toIso8601String()},
          where: 'is_default = 1',
        );
      }

      // تحديث العملة في قاعدة البيانات
      await _db.update(
        'currencies',
        updatedCurrency.toMap(),
        where: 'id = ?',
        whereArgs: [updatedCurrency.id],
      );

      // إعادة تحميل العملات لتحديث القائمة
      await loadCurrencies();

      return true;
    } catch (e, stackTrace) {
      _handleError('فشل في تعديل العملة', e, stackTrace);
      return false;
    }
  }

  // حذف عملة
  Future<bool> deleteCurrency(String id) async {
    try {
      // الحصول على العملة
      final currencyIndex = items.indexWhere((c) => c.id == id);
      if (currencyIndex == -1) {
        throw Exception('العملة غير موجودة');
      }

      final currency = items[currencyIndex];

      // لا يمكن حذف العملة الافتراضية
      if (currency.isDefault) {
        throw Exception('لا يمكن حذف العملة الافتراضية');
      }

      // حذف العملة من قاعدة البيانات (حذف منطقي)
      await _db.update(
        'currencies',
        {'is_deleted': 1, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [id],
      );

      // إعادة تحميل العملات لتحديث القائمة
      await loadCurrencies();

      return true;
    } catch (e, stackTrace) {
      _handleError('فشل في حذف العملة', e, stackTrace);
      return false;
    }
  }

  // تعيين العملة الافتراضية
  Future<bool> setDefaultCurrency(String id) async {
    try {
      // الحصول على العملة
      final currencyIndex = items.indexWhere((c) => c.id == id);
      if (currencyIndex == -1) {
        throw Exception('العملة غير موجودة');
      }

      // إلغاء تعيين العملة الافتراضية السابقة
      await _db.update(
        'currencies',
        {'is_default': 0, 'updated_at': DateTime.now().toIso8601String()},
        where: 'is_default = 1',
      );

      // تعيين العملة الجديدة كافتراضية
      await _db.update(
        'currencies',
        {'is_default': 1, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [id],
      );

      // تحديث القائمة
      await loadCurrencies();

      return true;
    } catch (e, stackTrace) {
      _handleError('فشل في تعيين العملة الافتراضية', e, stackTrace);
      return false;
    }
  }

  // تحويل العملة
  double convertCurrency(
      double amount, String fromCurrencyId, String toCurrencyId) {
    try {
      if (fromCurrencyId == toCurrencyId) {
        return amount;
      }

      final fromCurrency = items.firstWhere((c) => c.id == fromCurrencyId);
      final toCurrency = items.firstWhere((c) => c.id == toCurrencyId);

      // التحويل إلى العملة المحلية ثم إلى العملة المطلوبة
      final amountInLocalCurrency = amount * fromCurrency.exchangeRate;
      return amountInLocalCurrency / toCurrency.exchangeRate;
    } catch (e, stackTrace) {
      _handleError('فشل في تحويل العملة', e, stackTrace);
      return amount;
    }
  }

  // معالجة الأخطاء
  void _handleError(String message, dynamic error, StackTrace stackTrace) {
    setErrorMessage('$message: ${error.toString()}');
    setLoading(false);

    AppLogger.error('$message: ${error.toString()}');
    ErrorTracker.captureError(
      message,
      error: error,
      stackTrace: stackTrace,
      context: {'presenter': 'CurrencyPresenter'},
    );
  }
}
