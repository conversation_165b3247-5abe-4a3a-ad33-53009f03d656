import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/index.dart';
import '../../../core/models/expense.dart';
import '../../../core/models/category.dart';

import '../../../core/utils/app_logger.dart';
import '../../../core/widgets/index.dart';
import '../../../core/providers/app_providers.dart';
import '../presenters/expense_presenter.dart';

import 'expense_category_screen.dart';
import 'expense_form_screen.dart';

/// شاشة إدارة المصروفات
class ExpenseScreen extends StatefulWidget {
  const ExpenseScreen({Key? key}) : super(key: key);

  @override
  State<ExpenseScreen> createState() => _ExpenseScreenState();
}

class _ExpenseScreenState extends State<ExpenseScreen> {
  final TextEditingController _searchController = TextEditingController();
  late ExpensePresenter _presenter;

  // فلاتر
  DateTime? _startDate;
  DateTime? _endDate;
  String? _filterCategoryId;

  @override
  void initState() {
    super.initState();
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    _presenter = AppProviders.getExpensePresenter();
    _searchController.addListener(_onSearchChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  void _onSearchChanged() {
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    try {
      // تحميل فئات المصروفات
      await _presenter.loadExpenseCategories();

      // تحميل المصروفات
      await _presenter.loadExpenses(
        searchQuery:
            _searchController.text.isNotEmpty ? _searchController.text : null,
        startDate: _startDate,
        endDate: _endDate,
        categoryId: _filterCategoryId,
      );
    } catch (e) {
      AppLogger.error('فشل في تحميل البيانات: $e');
    }
  }

  /// إضافة مصروف جديد
  Future<void> _addExpense() async {
    try {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const ExpenseFormScreen(),
        ),
      );

      if (result == true) {
        await _loadData();
      }
    } catch (e) {
      AppLogger.error('فشل في إضافة المصروف: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في إضافة المصروف: $e')),
        );
      }
    }
  }

  /// تحديث مصروف
  Future<void> _updateExpense(Expense expense) async {
    try {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ExpenseFormScreen(expense: expense),
        ),
      );

      if (result == true) {
        await _loadData();
      }
    } catch (e) {
      AppLogger.error('فشل في تحديث المصروف: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحديث المصروف: $e')),
        );
      }
    }
  }

  /// حذف مصروف
  Future<void> _deleteExpense(String id) async {
    try {
      // عرض مربع حوار للتأكيد
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: const Text('هل أنت متأكد من حذف هذا المصروف؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('حذف'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        final success = await _presenter.deleteExpense(id);

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف المصروف بنجاح')),
          );
        }
      }
    } catch (e) {
      AppLogger.error('فشل في حذف المصروف: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حذف المصروف: $e')),
        );
      }
    }
  }

  /// تحرير مصروف
  Future<void> _editExpense(Expense expense) async {
    await _updateExpense(expense);
  }

  /// عرض مربع حوار الفلترة
  Future<void> _showFilterDialog() async {
    String? tempCategoryId = _filterCategoryId;
    DateTime? tempStartDate = _startDate;
    DateTime? tempEndDate = _endDate;

    await showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('تصفية المصروفات'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // فئة المصروف
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'الفئة',
                      ),
                      value: tempCategoryId,
                      items: [
                        const DropdownMenuItem<String>(
                          value: null,
                          child: Text('جميع الفئات'),
                        ),
                        ..._presenter.categories
                            .map((category) => DropdownMenuItem<String>(
                                  value: category.id,
                                  child: Text(category.name),
                                ))
                            .toList(),
                      ],
                      onChanged: (value) {
                        setState(() {
                          tempCategoryId = value;
                        });
                      },
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                    // تاريخ البداية
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'من تاريخ',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      controller: TextEditingController(
                        text: tempStartDate != null
                            ? '${tempStartDate!.day}/${tempStartDate!.month}/${tempStartDate!.year}'
                            : '',
                      ),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: tempStartDate ?? DateTime.now(),
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            tempStartDate = date;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                    // تاريخ النهاية
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'إلى تاريخ',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      controller: TextEditingController(
                        text: tempEndDate != null
                            ? '${tempEndDate!.day}/${tempEndDate!.month}/${tempEndDate!.year}'
                            : '',
                      ),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: tempEndDate ?? DateTime.now(),
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            tempEndDate = date;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _filterCategoryId = tempCategoryId;
                      _startDate = tempStartDate;
                      _endDate = tempEndDate;
                    });
                    Navigator.of(context).pop();
                    _loadData();
                  },
                  child: const Text('تطبيق'),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _filterCategoryId = null;
                      _startDate = null;
                      _endDate = null;
                      tempCategoryId = null;
                      tempStartDate = null;
                      tempEndDate = null;
                    });
                    Navigator.of(context).pop();
                    _loadData();
                  },
                  child: const Text('إعادة تعيين'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المصروفات'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.category),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ExpenseCategoryScreen(),
                ),
              );
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: ListenableBuilder(
        listenable: _presenter,
        builder: (context, child) {
          if (_presenter.isLoading) {
            return const AkLoadingIndicator();
          }

          final expenses = _presenter.expenses;
          final categories = _presenter.categories;

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // زر إضافة مصروف جديد
                ElevatedButton.icon(
                  onPressed: _addExpense,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة مصروف جديد'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.onPrimary,
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                  ),
                ),
                const SizedBox(height: AppDimensions.spacing24),
                // بحث
                TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    labelText: 'بحث',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    _loadData();
                  },
                ),
                const SizedBox(height: AppDimensions.spacing16),
                // قائمة المصروفات
                Text(
                  'المصروفات (${expenses.length})',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: AppDimensions.spacing8),
                Expanded(
                  child: expenses.isEmpty
                      ? const AkEmptyState(
                          message: 'لا توجد مصروفات',
                          icon: Icons.money_off,
                        )
                      : ListView.builder(
                          itemCount: expenses.length,
                          itemBuilder: (context, index) {
                            final expense = expenses[index];
                            final category = categories.firstWhere(
                              (c) => c.id == expense.categoryId,
                              orElse: () =>
                                  Category(name: 'غير محدد', type: 'expense'),
                            );

                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                title: Text('${expense.amount} ر.س'),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(expense.description ?? ''),
                                    Text(
                                      'التاريخ: ${DateFormat('yyyy-MM-dd').format(expense.expenseDate)}',
                                      style: const AppTypography(fontSize: 12),
                                    ),
                                  ],
                                ),
                                leading: const CircleAvatar(
                                  backgroundColor: AppColors.primary,
                                  child: Icon(Icons.money_off,
                                      color: AppColors.onPrimary),
                                ),
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Chip(
                                      label: Text(category.name),
                                      backgroundColor: AppColors.infoLight,
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.edit,
                                          color: AppColors.primary),
                                      onPressed: () => _editExpense(expense),
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.delete,
                                          color: AppColors.error),
                                      onPressed: () =>
                                          _deleteExpense(expense.id),
                                    ),
                                  ],
                                ),
                                isThreeLine: true,
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
