import 'dart:async';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart'; // إضافة استيراد debugPrint
import '../../../core/database/database_helper.dart';
import '../../../core/database/database_schema.dart';
import '../../../core/services/session_manager.dart';
import '../../../core/services/setup_flag_service.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/auth/permission_validator.dart';
import '../models/setup_state.dart';

/// مدير عملية الإعداد
/// يستخدم لإدارة عملية إعداد النظام بشكل متسلسل ومنظم
/// يقوم بتنفيذ الخطوات المختلفة في عملية الإعداد وتحديث حالة الإعداد
class SetupManager {
  /// حالة الإعداد
  final SetupState _setupState;

  /// مساعد قاعدة البيانات
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// قاعدة البيانات
  Database? _db;

  /// المنشئ
  SetupManager(this._setupState);

  /// بدء عملية الإعداد
  Future<bool> startSetup() async {
    try {
      debugPrint('🚀 بدء عملية الإعداد في SetupManager.startSetup()');

      // 1. التحقق من قاعدة البيانات الحالية
      debugPrint('1️⃣ جاري التحقق من قاعدة البيانات الحالية...');
      await _checkExistingDatabase();
      debugPrint('✅ تم التحقق من قاعدة البيانات الحالية بنجاح');

      // 2. إنشاء قاعدة البيانات
      debugPrint('2️⃣ جاري إنشاء قاعدة البيانات...');
      await _createDatabase();
      debugPrint('✅ تم إنشاء قاعدة البيانات بنجاح');

      // 3. إنشاء الجداول
      debugPrint('3️⃣ جاري إنشاء الجداول...');
      await _createTables();
      debugPrint('✅ تم إنشاء الجداول بنجاح');

      // 4. تهيئة البيانات الأساسية
      debugPrint('4️⃣ جاري تهيئة البيانات الأساسية...');
      await _initializeBasicData();
      debugPrint('✅ تم تهيئة البيانات الأساسية بنجاح');

      // 5. تهيئة الإعدادات
      debugPrint('5️⃣ جاري تهيئة الإعدادات...');
      await _initializeSettings();
      debugPrint('✅ تم تهيئة الإعدادات بنجاح');

      // 6. إنهاء الإعداد
      debugPrint('6️⃣ جاري إنهاء الإعداد...');
      await _finalizeSetup();
      debugPrint('✅ تم إنهاء الإعداد بنجاح');

      // 7. اكتمال الإعداد
      debugPrint('7️⃣ اكتمال الإعداد...');
      _setupState.updateState(
        step: SetupStep.completed,
        progress: 1.0,
        message: 'تم اكتمال الإعداد بنجاح!',
      );
      debugPrint('🎉 تم اكتمال عملية الإعداد بنجاح!');

      return true;
    } catch (e, stackTrace) {
      // تسجيل الخطأ
      debugPrint('❌❌❌ فشل في عملية الإعداد: $e');
      debugPrint('❌❌❌ تفاصيل الخطأ: $stackTrace');
      AppLogger.error('❌ فشل في عملية الإعداد: $e');
      ErrorTracker.captureError(
        'فشل في عملية الإعداد',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'setup_manager.startSetup'},
      );

      // تحديث حالة الإعداد
      _setupState.updateState(
        step: SetupStep.error,
        progress: 0.0,
        message: 'حدث خطأ أثناء الإعداد',
        error: e.toString(),
      );

      return false;
    }
  }

  /// التحقق من قاعدة البيانات الحالية
  Future<void> _checkExistingDatabase() async {
    // التحقق من صحة تعريفات الصلاحيات والأدوار
    _setupState.updateState(
      step: SetupStep.checkingExistingDatabase,
      progress: 0.02,
      message: 'جاري التحقق من صحة تعريفات الصلاحيات والأدوار...',
    );

    final isPermissionsValid = PermissionValidator.validateAll();
    if (!isPermissionsValid) {
      AppLogger.error('❌ فشل التحقق من صحة تعريفات الصلاحيات والأدوار');
      throw Exception('فشل التحقق من صحة تعريفات الصلاحيات والأدوار');
    }

    AppLogger.info('✅ تم التحقق من صحة تعريفات الصلاحيات والأدوار بنجاح');

    // التحقق من وجود قاعدة البيانات
    _setupState.updateState(
      step: SetupStep.checkingExistingDatabase,
      progress: 0.05,
      message: 'جاري التحقق من قاعدة البيانات الحالية...',
    );

    // التحقق من وجود ملف قاعدة البيانات
    final databaseExists = await _databaseHelper.databaseFileExists();

    if (databaseExists) {
      AppLogger.info('✅ ملف قاعدة البيانات موجود بالفعل');

      // إغلاق قاعدة البيانات إذا كانت مفتوحة
      await _databaseHelper.closeDatabase();

      _setupState.updateState(
        step: SetupStep.checkingExistingDatabase,
        progress: 0.1,
        message: 'جاري حذف قاعدة البيانات القديمة للإعداد من جديد...',
      );

      // حذف ملف قاعدة البيانات القديمة للإعداد من جديد
      final dbPath = await _databaseHelper.getDatabasePath();
      final dbFile = File(dbPath);
      if (await dbFile.exists()) {
        await dbFile.delete();
        AppLogger.info('✅ تم حذف ملف قاعدة البيانات القديمة بنجاح');
      }
    } else {
      AppLogger.info('⚠️ ملف قاعدة البيانات غير موجود، سيتم إنشاؤه');
    }
  }

  /// إنشاء قاعدة البيانات
  Future<void> _createDatabase() async {
    _setupState.updateState(
      step: SetupStep.creatingDatabase,
      progress: 0.2,
      message: 'جاري إنشاء قاعدة البيانات...',
    );

    // إنشاء قاعدة البيانات والجداول
    _db = await _databaseHelper.createDatabaseAndTables();

    if (_db == null) {
      throw Exception('فشل في إنشاء قاعدة البيانات');
    }

    AppLogger.info('✅ تم إنشاء قاعدة البيانات والجداول بنجاح');
  }

  /// إنشاء الجداول
  Future<void> _createTables() async {
    debugPrint('🔄 بدء دالة _createTables()');
    _setupState.updateState(
      step: SetupStep.creatingTables,
      progress: 0.3,
      message: 'جاري التحقق من جداول قاعدة البيانات...',
    );

    if (_db == null) {
      debugPrint('❌ خطأ: قاعدة البيانات غير مهيأة (_db is null)');
      throw Exception('قاعدة البيانات غير مهيأة');
    }

    debugPrint('🔍 جاري التحقق من وجود جدول users...');
    // التحقق من وجود الجداول الأساسية
    final tableExists = await _db!.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='users'");

    debugPrint('📊 نتيجة الاستعلام عن جدول users: $tableExists');

    if (tableExists.isEmpty) {
      debugPrint('⚠️ جدول users غير موجود، جاري إنشاء الجداول...');
      _setupState.updateState(
        step: SetupStep.creatingTables,
        progress: 0.4,
        message: 'جاري إنشاء جداول قاعدة البيانات...',
      );

      try {
        debugPrint('🔄 محاولة إنشاء الجداول داخل معاملة...');
        // إنشاء جميع الجداول في معاملة واحدة
        await _db!.transaction((txn) async {
          debugPrint('🔄 بدء معاملة إنشاء الجداول...');
          await DatabaseSchema.createAllTables(txn);
          debugPrint('✅ تم إنشاء الجداول داخل المعاملة بنجاح');
        });
        debugPrint('✅ تم إكمال معاملة إنشاء الجداول بنجاح');
        AppLogger.info('✅ تم إنشاء جداول قاعدة البيانات بنجاح');
      } catch (e, stackTrace) {
        debugPrint('❌❌❌ خطأ في إنشاء جداول قاعدة البيانات: $e');
        debugPrint('❌❌❌ تفاصيل الخطأ: $stackTrace');
        AppLogger.error('❌ خطأ في إنشاء جداول قاعدة البيانات: $e');
        ErrorTracker.captureError(
          'فشل في إنشاء جداول قاعدة البيانات',
          error: e,
          stackTrace: stackTrace,
        );

        // إعادة محاولة إنشاء الجداول بطريقة أخرى
        _setupState.updateState(
          step: SetupStep.creatingTables,
          progress: 0.45,
          message: 'جاري إعادة محاولة إنشاء الجداول...',
        );

        debugPrint('🔄 إعادة محاولة إنشاء الجداول بدون معاملة...');
        // محاولة إنشاء الجداول مباشرة بدون معاملة
        await DatabaseSchema.createAllTables(_db!);
        debugPrint('✅ تم إنشاء الجداول بدون معاملة بنجاح (المحاولة الثانية)');
        AppLogger.info(
            '✅ تم إنشاء جداول قاعدة البيانات بنجاح (المحاولة الثانية)');
      }
    } else {
      debugPrint('✅ جداول قاعدة البيانات موجودة بالفعل');
      AppLogger.info('✅ جداول قاعدة البيانات موجودة بالفعل');
    }

    debugPrint('✅ انتهاء دالة _createTables() بنجاح');
  }

  /// تهيئة البيانات الأساسية
  Future<void> _initializeBasicData() async {
    _setupState.updateState(
      step: SetupStep.initializingBasicData,
      progress: 0.6,
      message: 'جاري تهيئة البيانات الأساسية...',
    );

    if (_db == null) {
      throw Exception('قاعدة البيانات غير مهيأة');
    }

    try {
      AppLogger.info('🔍 جاري تهيئة البيانات الأساسية...');

      // تهيئة جميع البيانات الأساسية
      _setupState.updateState(
        step: SetupStep.initializingBasicData,
        progress: 0.65,
        message: 'جاري تهيئة البيانات الأساسية...',
      );

      // استخدام دالة تهيئة البيانات الأساسية من DatabaseHelper
      final success = await _databaseHelper.initializeBasicData();

      if (success) {
        AppLogger.info('✅ تم تهيئة البيانات الأساسية بنجاح');

        _setupState.updateState(
          step: SetupStep.initializingBasicData,
          progress: 0.75,
          message: 'تم تهيئة البيانات الأساسية بنجاح',
        );
      } else {
        AppLogger.warning('⚠️ فشل في تهيئة البيانات الأساسية');
        throw Exception('فشل في تهيئة البيانات الأساسية');
      }
    } catch (e, stackTrace) {
      AppLogger.error('❌ خطأ في تهيئة البيانات الأساسية: $e');
      ErrorTracker.captureError(
        'فشل في تهيئة البيانات الأساسية',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'initialize_basic_data_in_setup_manager'},
      );
      rethrow;
    }
  }

  /// تهيئة الإعدادات
  Future<void> _initializeSettings() async {
    _setupState.updateState(
      step: SetupStep.initializingSettings,
      progress: 0.8,
      message: 'جاري تهيئة إعدادات النظام...',
    );

    if (_db == null) {
      throw Exception('قاعدة البيانات غير مهيأة');
    }

    // التحقق من وجود الجداول الأساسية
    final tableCount = await _db!.rawQuery(
        "SELECT count(*) as count FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
    final count = Sqflite.firstIntValue(tableCount) ?? 0;
    AppLogger.info('✅ قاعدة البيانات تحتوي على $count جدول');

    if (count < 10) {
      // عدد الجداول المتوقع على الأقل
      AppLogger.warning(
          '⚠️ عدد الجداول أقل من المتوقع، قد تكون هناك مشكلة في قاعدة البيانات');

      _setupState.updateState(
        step: SetupStep.initializingSettings,
        progress: 0.85,
        message: 'جاري التحقق من سلامة قاعدة البيانات...',
      );

      // التحقق من سلامة قاعدة البيانات
      final integrityCheck = await _db!.rawQuery('PRAGMA integrity_check');
      final isIntegrityOk = integrityCheck.isNotEmpty &&
          integrityCheck.first.containsKey('integrity_check') &&
          integrityCheck.first['integrity_check'] == 'ok';

      if (!isIntegrityOk) {
        AppLogger.error(
            '❌ فشل في التحقق من سلامة قاعدة البيانات: $integrityCheck');
        throw Exception('فشل في التحقق من سلامة قاعدة البيانات');
      }
    }

    AppLogger.info('✅ تم التحقق من سلامة قاعدة البيانات بنجاح');
  }

  /// إنهاء الإعداد
  Future<void> _finalizeSetup() async {
    _setupState.updateState(
      step: SetupStep.finalizingSetup,
      progress: 0.9,
      message: 'جاري إنهاء الإعداد...',
    );

    // تحديث حالة الإعداد
    await SessionManager.setSetupCompleted(true);
    await SessionManager.setFirstLaunch(false);
    await SetupFlagService.setSetupCompleted(true);
    await SetupFlagService.setFirstLaunch(false);

    AppLogger.info('✅ تم إكمال إعداد النظام بنجاح!');
  }
}
