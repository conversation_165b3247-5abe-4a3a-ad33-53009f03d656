import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/index.dart';
import '../presenters/inventory_transfer_presenter.dart';
import '../../../core/models/product.dart';
import '../../../core/models/warehouse.dart';
import '../../../core/models/inventory_transfer.dart';
import '../../../core/models/inventory_transfer_item.dart';
import '../../../core/theme/index.dart';

/// شاشة نموذج تحويل المخزون
class InventoryTransferFormScreen extends StatefulWidget {
  final InventoryTransfer? transfer;
  final bool isReadOnly;

  const InventoryTransferFormScreen({
    Key? key,
    this.transfer,
    this.isReadOnly = false,
  }) : super(key: key);

  @override
  State<InventoryTransferFormScreen> createState() =>
      _InventoryTransferFormScreenState();
}

class _InventoryTransferFormScreenState
    extends State<InventoryTransferFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _referenceNumberController =
      TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  DateTime _date = DateTime.now();
  String? _fromWarehouseId;
  String? _toWarehouseId;
  String _status = 'pending';

  final List<InventoryTransferItem> _items = [];

  bool _isLoading = false;

  late InventoryTransferPresenter _presenter;

  @override
  void initState() {
    super.initState();
    _presenter = AppProviders.getLazyPresenter<InventoryTransferPresenter>(
        () => InventoryTransferPresenter());

    _loadData();

    if (widget.transfer != null) {
      _referenceNumberController.text = widget.transfer!.referenceNumber ?? '';
      _date = widget.transfer!.date;
      _fromWarehouseId = widget.transfer!.sourceWarehouseId;
      _toWarehouseId = widget.transfer!.destinationWarehouseId;
      _status = widget.transfer!.status;
      _notesController.text = widget.transfer!.notes ?? '';

      _loadTransferItems();
    }
  }

  @override
  void dispose() {
    _referenceNumberController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await _presenter.loadWarehouses();
    await _presenter.loadProducts();
  }

  /// تحميل عناصر التحويل
  Future<void> _loadTransferItems() async {
    if (widget.transfer != null) {
      setState(() {
        _items.clear();
        _items.addAll(widget.transfer!.items);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.transfer != null;
    final isReadOnly = widget.isReadOnly;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isReadOnly
              ? 'عرض تحويل'
              : isEditing
                  ? 'تعديل تحويل'
                  : 'إضافة تحويل',
        ),
      ),
      body: SafeLayout(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات التحويل
              AkCard(
                type: AkCardType.elevated,
                size: AkCardSize.large,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات التحويل',
                      style: AppTypography.createCustomStyle(
                        fontSize: AppTypography.fontSizeLarge,
                        fontWeight: AppTypography.weightBold,
                      ),
                    ),
                    SizedBox(height: AppDimensions.defaultMargin),

                    // رقم المرجع
                    AkTextInput(
                      label: 'رقم المرجع',
                      controller: _referenceNumberController,
                      readOnly: isReadOnly,
                      prefixIcon: Icons.receipt_long,
                    ),
                    SizedBox(height: AppDimensions.defaultMargin),

                    // التاريخ - استخدام AkDateRangeInput للتاريخ الواحد
                    AkDateRangeInput(
                      label: 'التاريخ',
                      hint: 'اختر تاريخ التحويل',
                      initialStartDate: _date,
                      initialEndDate: _date, // نفس التاريخ للبداية والنهاية
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2100),
                      onDateRangeSelected: isReadOnly
                          ? null
                          : (startDate, endDate) {
                              setState(() {
                                _date = startDate ?? DateTime.now();
                              });
                            },
                      isRequired: true,
                      readOnly: isReadOnly,
                      prefixIcon: Icons.calendar_today,
                    ),
                    SizedBox(height: AppDimensions.defaultMargin),

                    // من مخزن
                    AkDropdownInput<String>(
                      label: 'من مخزن',
                      value: _fromWarehouseId,
                      items: _presenter.warehouses
                          .map((warehouse) => DropdownMenuItem(
                                value: warehouse.id,
                                child: Text(warehouse.name),
                              ))
                          .toList(),
                      onChanged: (value) {
                        if (!isReadOnly) {
                          setState(() {
                            _fromWarehouseId = value;
                          });
                        }
                      },
                      isRequired: true,
                      prefixIcon: Icons.warehouse,
                      readOnly: isReadOnly,
                    ),
                    SizedBox(height: AppDimensions.defaultMargin),

                    // إلى مخزن
                    AkDropdownInput<String>(
                      label: 'إلى مخزن',
                      value: _toWarehouseId,
                      items: _presenter.warehouses
                          .map((warehouse) => DropdownMenuItem(
                                value: warehouse.id,
                                child: Text(warehouse.name),
                              ))
                          .toList(),
                      onChanged: (value) {
                        if (!isReadOnly) {
                          setState(() {
                            _toWarehouseId = value;
                          });
                        }
                      },
                      isRequired: true,
                      prefixIcon: Icons.warehouse_outlined,
                      readOnly: isReadOnly,
                    ),
                    SizedBox(height: AppDimensions.defaultMargin),

                    // الحالة
                    AkDropdownInput<String>(
                      label: 'الحالة',
                      value: _status,
                      items: const [
                        DropdownMenuItem(
                          value: 'pending',
                          child: Text('قيد الانتظار'),
                        ),
                        DropdownMenuItem(
                          value: 'completed',
                          child: Text('مكتمل'),
                        ),
                        DropdownMenuItem(
                          value: 'cancelled',
                          child: Text('ملغي'),
                        ),
                      ],
                      onChanged: (value) {
                        if (!isReadOnly) {
                          setState(() {
                            _status = value!;
                          });
                        }
                      },
                      isRequired: true,
                      prefixIcon: Icons.info_outline,
                      readOnly: isReadOnly,
                    ),
                    SizedBox(height: AppDimensions.defaultMargin),

                    // ملاحظات
                    AkTextInput(
                      label: 'ملاحظات',
                      controller: _notesController,
                      maxLines: 3,
                      readOnly: isReadOnly,
                      prefixIcon: Icons.note_alt_outlined,
                    ),
                  ],
                ),
              ),

              SizedBox(height: AppDimensions.largeMargin),

              // عناصر التحويل
              AkCard(
                type: AkCardType.elevated,
                size: AkCardSize.large,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'عناصر التحويل',
                          style: AppTypography.createCustomStyle(
                            fontSize: AppTypography.fontSizeLarge,
                            fontWeight: AppTypography.weightBold,
                          ),
                        ),
                        if (!isReadOnly)
                          AkButton(
                            text: 'إضافة منتج',
                            onPressed: _addItem,
                            icon: Icons.add,
                            type: AkButtonType.primary,
                            size: AkButtonSize.small,
                          ),
                      ],
                    ),
                    SizedBox(height: AppDimensions.defaultMargin),

                    // جدول العناصر
                    _buildItemsTable(),
                  ],
                ),
              ),

              SizedBox(height: AppDimensions.largeMargin),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const AkBackButton(
                    text: 'رجوع',
                    autoClose: true,
                  ),
                  if (!isReadOnly) ...[
                    SizedBox(width: AppDimensions.defaultMargin),
                    AkSaveButton(
                      onPressed: _saveTransfer,
                      isLoading: _isLoading,
                      text: isEditing ? 'تحديث' : 'حفظ',
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء جدول العناصر
  Widget _buildItemsTable() {
    if (_items.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.defaultMargin),
          child: Text(
            'لا توجد عناصر',
            style: AppTypography.createCustomStyle(
              color: AppColors.lightTextSecondary,
              fontSize: AppTypography.fontSizeMedium,
            ),
          ),
        ),
      );
    }

    final columns = [
      const FinancialTableColumn(
        title: 'المنتج',
        field: 'product_name',
        alignment: ColumnAlignment.start,
      ),
      const FinancialTableColumn(
        title: 'الكمية',
        field: 'quantity',
        alignment: ColumnAlignment.center,
        isNumeric: true,
      ),
      const FinancialTableColumn(
        title: 'الإجراءات',
        field: 'actions',
        alignment: ColumnAlignment.center,
      ),
    ];

    final data = _items.map((item) {
      return {
        'id': item.id,
        'product_name': _presenter.getProductName(item.productId),
        'quantity': item.quantity,
        'actions': item,
      };
    }).toList();

    return FinancialDataTable(
      columns: columns,
      data: data,
      customCellBuilder: (context, rowIndex, columnIndex, value, field) {
        if (field == 'actions' && !widget.isReadOnly) {
          final item = value as InventoryTransferItem;
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AkIconButton(
                icon: Icons.edit,
                onPressed: () => _editItem(item),
                type: AkButtonType.info,
                size: AkButtonSize.small,
                tooltip: 'تعديل',
                showBackground: false,
              ),
              SizedBox(width: AppDimensions.smallMargin),
              AkIconButton(
                icon: Icons.delete,
                onPressed: () => _deleteItem(item),
                type: AkButtonType.danger,
                size: AkButtonSize.small,
                tooltip: 'حذف',
                showBackground: false,
              ),
            ],
          );
        }
        return null;
      },
    );
  }

  /// إضافة عنصر
  void _addItem() {
    showDialog(
      context: context,
      builder: (context) => _ItemDialog(
        warehouses: _presenter.warehouses,
        products: _presenter.products,
        onSave: (productId, quantity) {
          setState(() {
            _items.add(InventoryTransferItem(
              productId: productId,
              quantity: quantity,
              transferId: widget.transfer?.id ?? '',
            ));
          });
        },
      ),
    );
  }

  /// تعديل عنصر
  void _editItem(InventoryTransferItem item) {
    showDialog(
      context: context,
      builder: (context) => _ItemDialog(
        warehouses: _presenter.warehouses,
        products: _presenter.products,
        productId: item.productId,
        quantity: item.quantity,
        onSave: (productId, quantity) {
          setState(() {
            final index = _items.indexOf(item);
            _items[index] = InventoryTransferItem(
              id: item.id,
              productId: productId,
              quantity: quantity,
              transferId: item.transferId,
            );
          });
        },
      ),
    );
  }

  /// حذف عنصر
  void _deleteItem(InventoryTransferItem item) {
    setState(() {
      _items.remove(item);
    });
  }

  /// حفظ التحويل
  void _saveTransfer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إضافة منتج واحد على الأقل'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    if (_fromWarehouseId == _toWarehouseId) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن التحويل إلى نفس المخزن'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final transfer = InventoryTransfer(
      id: widget.transfer?.id,
      referenceNumber: _referenceNumberController.text.isEmpty
          ? null
          : _referenceNumberController.text,
      date: _date,
      sourceWarehouseId: _fromWarehouseId!,
      destinationWarehouseId: _toWarehouseId!,
      status: _status,
      notes: _notesController.text.isEmpty ? null : _notesController.text,
      items: _items,
    );

    bool success;

    try {
      if (widget.transfer == null) {
        // إضافة تحويل جديد
        final addedTransfer = await _presenter.addTransfer(transfer, _items);
        success = addedTransfer != null;
      } else {
        // تحديث تحويل موجود
        success = await _presenter.updateTransfer(transfer, _items);
      }

      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;

      if (success) {
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_presenter.errorMessage ?? 'فشل في حفظ التحويل'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}

/// مربع حوار إضافة/تعديل عنصر
class _ItemDialog extends StatefulWidget {
  final List<Warehouse> warehouses;
  final List<Product> products;
  final String? productId;
  final double? quantity;
  final Function(String, double) onSave;

  const _ItemDialog({
    Key? key,
    required this.warehouses,
    required this.products,
    this.productId,
    this.quantity,
    required this.onSave,
  }) : super(key: key);

  @override
  State<_ItemDialog> createState() => _ItemDialogState();
}

class _ItemDialogState extends State<_ItemDialog> {
  String? _productId;
  final TextEditingController _quantityController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _productId = widget.productId;
    _quantityController.text = widget.quantity?.toString() ?? '1.0';
  }

  @override
  void dispose() {
    _quantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.productId == null ? 'إضافة منتج' : 'تعديل منتج',
        style: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeLarge,
          fontWeight: AppTypography.weightBold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // المنتج
          AkDropdownInput<String>(
            label: 'المنتج',
            value: _productId,
            items: widget.products
                .map((product) => DropdownMenuItem(
                      value: product.id,
                      child: Text(product.name),
                    ))
                .toList(),
            onChanged: (value) {
              setState(() {
                _productId = value;
              });
            },
            isRequired: true,
            prefixIcon: Icons.inventory_2,
          ),
          SizedBox(height: AppDimensions.defaultMargin),

          // الكمية - حقل محسن للكميات
          AkTextInput(
            label: 'الكمية',
            hint: 'أدخل الكمية المطلوبة',
            controller: _quantityController,
            keyboardType: const TextInputType.numberWithOptions(
              decimal: true,
              signed: false,
            ),
            isRequired: true,
            prefixIcon: Icons.inventory_outlined,
            suffixText: 'قطعة',
            validator: (value) {
              // تحقق شامل من صحة الكمية
              if (value == null || value.trim().isEmpty) {
                return 'الرجاء إدخال الكمية';
              }

              final quantity = double.tryParse(value.trim());
              if (quantity == null) {
                return 'الرجاء إدخال رقم صحيح';
              }

              if (quantity <= 0) {
                return 'يجب أن تكون الكمية أكبر من صفر';
              }

              if (quantity > 999999) {
                return 'الكمية كبيرة جداً (الحد الأقصى: 999,999)';
              }

              // التحقق من عدد الأرقام العشرية (حد أقصى 3 أرقام)
              final decimalParts = value.split('.');
              if (decimalParts.length > 1 && decimalParts[1].length > 3) {
                return 'الحد الأقصى 3 أرقام عشرية';
              }

              return null;
            },
            onChanged: (value) {
              // تنسيق تلقائي للكمية أثناء الكتابة
              if (value.isNotEmpty) {
                final quantity = double.tryParse(value);
                if (quantity != null) {
                  // يمكن إضافة تنسيق إضافي هنا إذا لزم الأمر
                }
              }
            },
          ),
        ],
      ),
      actions: [
        AkCancelButton(
          onPressed: () => Navigator.pop(context),
        ),
        AkSaveButton(
          onPressed: () {
            if (_productId == null) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('يجب اختيار منتج'),
                  backgroundColor: AppColors.error,
                ),
              );
              return;
            }

            final quantity = double.tryParse(_quantityController.text);
            if (quantity == null || quantity <= 0) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('يجب إدخال كمية صحيحة'),
                  backgroundColor: AppColors.error,
                ),
              );
              return;
            }

            widget.onSave(_productId!, quantity);
            Navigator.pop(context);
          },
          text: 'حفظ',
        ),
      ],
    );
  }
}
