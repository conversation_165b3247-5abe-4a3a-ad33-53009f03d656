import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج وحدة القياس الموحد
/// تم توحيده من جميع نماذج الوحدات في المشروع
class Unit extends BaseModel {
  // معلومات أساسية
  final String name;
  final String? symbol;
  final String? abbreviation;
  final String? description;

  // معلومات الوحدة
  final bool isBase;
  final double conversionFactor;
  final String? baseUnitId;
  final String? baseUnitName; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات

  // معلومات التصنيف
  final String type; // نوع الوحدة (product, service, weight, length, etc.)

  // معلومات الحالة
  final bool isActive;

  Unit({
    String? id,
    required this.name,
    this.symbol,
    this.abbreviation,
    this.description,
    this.isBase = false,
    this.conversionFactor = 1.0,
    this.baseUnitId,
    this.baseUnitName,
    this.type = 'product',
    this.isActive = true,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذه الوحدة مع استبدال الحقول المحددة بقيم جديدة
  Unit copyWith({
    String? id,
    String? name,
    String? symbol,
    String? abbreviation,
    String? description,
    bool? isBase,
    double? conversionFactor,
    String? baseUnitId,
    String? baseUnitName,
    String? type,
    bool? isActive,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Unit(
      id: id ?? this.id,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      abbreviation: abbreviation ?? this.abbreviation,
      description: description ?? this.description,
      isBase: isBase ?? this.isBase,
      conversionFactor: conversionFactor ?? this.conversionFactor,
      baseUnitId: baseUnitId ?? this.baseUnitId,
      baseUnitName: baseUnitName ?? this.baseUnitName,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل وحدة القياس إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'symbol': symbol,
      'abbreviation': abbreviation,
      'description': description,
      'is_base': isBase ? 1 : 0,
      'conversion_factor': conversionFactor,
      'base_unit_id': baseUnitId,
      'type': type,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء وحدة قياس من Map
  factory Unit.fromMap(Map<String, dynamic> map) {
    return Unit(
      id: map['id'],
      name: map['name'] ?? '',
      symbol: map['symbol'],
      abbreviation: map['abbreviation'] ?? map['symbol'],
      description: map['description'],
      isBase: map['is_base'] == 1 || map['is_base_unit'] == 1,
      conversionFactor: map['conversion_factor'] is int
          ? (map['conversion_factor'] as int).toDouble()
          : (map['conversion_factor'] as double? ?? 1.0),
      baseUnitId: map['base_unit_id'],
      baseUnitName: map['base_unit_name'],
      type: map['type'] ?? 'product',
      isActive: map['is_active'] == null || map['is_active'] == 1,
      createdAt: map['created_at'] != null
          ? (map['created_at'] is String
              ? DateTime.parse(map['created_at'])
              : DateTime.fromMillisecondsSinceEpoch(map['created_at']))
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt: map['updated_at'] != null
          ? (map['updated_at'] is String
              ? DateTime.parse(map['updated_at'])
              : DateTime.fromMillisecondsSinceEpoch(map['updated_at']))
          : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل وحدة القياس إلى Map
  Map<String, dynamic> toJson() {
    return toMap();
  }

  /// إنشاء وحدة قياس من JSON
  factory Unit.fromJson(String source) {
    return Unit.fromMap(jsonDecode(source));
  }

  /// التحقق مما إذا كانت الوحدة هي وحدة أساسية
  bool get isBaseUnit => isBase;

  @override
  String toString() {
    return 'Unit(id: $id, name: $name, symbol: $symbol, type: $type, isBase: $isBase)';
  }
}
