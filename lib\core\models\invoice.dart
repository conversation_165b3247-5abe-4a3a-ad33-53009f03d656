import 'package:uuid/uuid.dart';
import 'dart:convert';
import 'base_model.dart';
import 'invoice_item.dart';
import 'customer.dart';
import 'supplier.dart';

/// أنواع الفواتير
enum InvoiceType {
  sale,
  purchase,
  saleReturn,
  purchaseReturn,
  quotation,
  proforma,
}

/// حالات الفاتورة
enum InvoiceStatus {
  draft,
  confirmed,
  partiallyPaid,
  paid,
  cancelled,
  refunded,
  pending,
}

/// نموذج الفاتورة الموحد
/// تم توحيده من جميع نماذج الفواتير في المشروع
class Invoice extends BaseModel {
  // معلومات أساسية
  final String invoiceNumber;
  final InvoiceType invoiceType;
  final DateTime date;
  final DateTime? dueDate;
  final InvoiceStatus status;

  // معلومات العميل/المورد
  final String? customerId;
  final Customer? customer; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final String? customerName; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final String? supplierId;
  final Supplier? supplier; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final String? supplierName; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات

  // معلومات المستودع
  final String warehouseId;
  final String? warehouseName; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات

  // معلومات مالية
  final double subtotal;
  final String? discountType; // percentage, amount
  final double discountValue;
  final bool isDiscountPercentage;
  final double taxAmount;
  final double taxRate;
  final double shippingAmount;
  final double total;
  final double paidAmount;
  final double balance;

  // معلومات الدفع
  final String? paymentMethod;
  final String? paymentStatus;
  final String? paymentTerms;

  // معلومات إضافية
  final String? notes;
  final String? termsConditions;
  final bool isPosted;
  final String? referenceNumber;
  final Map<String, dynamic>? metadata;

  // بنود الفاتورة
  final List<InvoiceItem> items;

  Invoice({
    String? id,
    required this.invoiceNumber,
    required this.invoiceType,
    required this.date,
    this.dueDate,
    this.status = InvoiceStatus.draft,
    this.customerId,
    this.customer,
    this.customerName,
    this.supplierId,
    this.supplier,
    this.supplierName,
    required this.warehouseId,
    this.warehouseName,
    this.subtotal = 0.0,
    this.discountType,
    this.discountValue = 0.0,
    this.isDiscountPercentage = false,
    this.taxAmount = 0.0,
    this.taxRate = 0.0,
    this.shippingAmount = 0.0,
    this.total = 0.0,
    this.paidAmount = 0.0,
    this.balance = 0.0,
    this.paymentMethod,
    this.paymentStatus,
    this.paymentTerms,
    this.notes,
    this.termsConditions,
    this.isPosted = false,
    this.referenceNumber,
    this.metadata,
    this.items = const [],
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذه الفاتورة مع استبدال الحقول المحددة بقيم جديدة
  Invoice copyWith({
    String? id,
    String? invoiceNumber,
    InvoiceType? invoiceType,
    DateTime? date,
    DateTime? dueDate,
    InvoiceStatus? status,
    String? customerId,
    Customer? customer,
    String? customerName,
    String? supplierId,
    Supplier? supplier,
    String? supplierName,
    String? warehouseId,
    String? warehouseName,
    double? subtotal,
    String? discountType,
    double? discountValue,
    bool? isDiscountPercentage,
    double? taxAmount,
    double? taxRate,
    double? shippingAmount,
    double? total,
    double? paidAmount,
    double? balance,
    String? paymentMethod,
    String? paymentStatus,
    String? paymentTerms,
    String? notes,
    String? termsConditions,
    bool? isPosted,
    String? referenceNumber,
    Map<String, dynamic>? metadata,
    List<InvoiceItem>? items,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      invoiceType: invoiceType ?? this.invoiceType,
      date: date ?? this.date,
      dueDate: dueDate ?? this.dueDate,
      status: status ?? this.status,
      customerId: customerId ?? this.customerId,
      customer: customer ?? this.customer,
      customerName: customerName ?? this.customerName,
      supplierId: supplierId ?? this.supplierId,
      supplier: supplier ?? this.supplier,
      supplierName: supplierName ?? this.supplierName,
      warehouseId: warehouseId ?? this.warehouseId,
      warehouseName: warehouseName ?? this.warehouseName,
      subtotal: subtotal ?? this.subtotal,
      discountType: discountType ?? this.discountType,
      discountValue: discountValue ?? this.discountValue,
      isDiscountPercentage: isDiscountPercentage ?? this.isDiscountPercentage,
      taxAmount: taxAmount ?? this.taxAmount,
      taxRate: taxRate ?? this.taxRate,
      shippingAmount: shippingAmount ?? this.shippingAmount,
      total: total ?? this.total,
      paidAmount: paidAmount ?? this.paidAmount,
      balance: balance ?? this.balance,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentTerms: paymentTerms ?? this.paymentTerms,
      notes: notes ?? this.notes,
      termsConditions: termsConditions ?? this.termsConditions,
      isPosted: isPosted ?? this.isPosted,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      metadata: metadata ?? this.metadata,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل الفاتورة إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'invoice_type': invoiceType.toString().split('.').last,
      'date': date.toIso8601String(),
      'due_date': dueDate?.toIso8601String(),
      'status': status.toString().split('.').last,
      'customer_id': customerId,
      'customer_name': customerName,
      'supplier_id': supplierId,
      'supplier_name': supplierName,
      'warehouse_id': warehouseId,
      'warehouse_name': warehouseName,
      'subtotal': subtotal,
      'discount_type': discountType,
      'discount_value': discountValue,
      'is_discount_percentage': isDiscountPercentage ? 1 : 0,
      'tax_amount': taxAmount,
      'tax_rate': taxRate,
      'shipping_amount': shippingAmount,
      'total': total,
      'paid_amount': paidAmount,
      'balance': balance,
      'payment_method': paymentMethod,
      'payment_status': paymentStatus,
      'payment_terms': paymentTerms,
      'notes': notes,
      'terms_conditions': termsConditions,
      'is_posted': isPosted ? 1 : 0,
      'reference_number': referenceNumber,
      'metadata': metadata != null ? jsonEncode(metadata) : null,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء فاتورة من Map
  factory Invoice.fromMap(Map<String, dynamic> map,
      [List<InvoiceItem>? items]) {
    return Invoice(
      id: map['id'],
      invoiceNumber: map['invoice_number'],
      invoiceType: _parseInvoiceType(map['invoice_type']),
      date: map['date'] != null ? DateTime.parse(map['date']) : DateTime.now(),
      dueDate: map['due_date'] != null ? DateTime.parse(map['due_date']) : null,
      status: _parseInvoiceStatus(map['status']),
      customerId: map['customer_id'],
      customerName: map['customer_name'],
      customer: map['customer'] != null
          ? Customer.fromMap(
              map['customer'] is String
                  ? jsonDecode(map['customer'])
                  : map['customer'],
            )
          : null,
      supplierId: map['supplier_id'],
      supplierName: map['supplier_name'],
      supplier: map['supplier'] != null
          ? Supplier.fromMap(
              map['supplier'] is String
                  ? jsonDecode(map['supplier'])
                  : map['supplier'],
            )
          : null,
      warehouseId: map['warehouse_id'],
      warehouseName: map['warehouse_name'],
      subtotal: map['subtotal'] is int
          ? (map['subtotal'] as int).toDouble()
          : (map['subtotal'] as double? ?? 0.0),
      discountType: map['discount_type'],
      discountValue: map['discount_value'] is int
          ? (map['discount_value'] as int).toDouble()
          : (map['discount_value'] as double? ?? 0.0),
      isDiscountPercentage: map['is_discount_percentage'] == 1,
      taxAmount: map['tax_amount'] is int
          ? (map['tax_amount'] as int).toDouble()
          : (map['tax_amount'] as double? ?? 0.0),
      taxRate: map['tax_rate'] is int
          ? (map['tax_rate'] as int).toDouble()
          : (map['tax_rate'] as double? ?? 0.0),
      shippingAmount: map['shipping_amount'] is int
          ? (map['shipping_amount'] as int).toDouble()
          : (map['shipping_amount'] as double? ?? 0.0),
      total: map['total'] is int
          ? (map['total'] as int).toDouble()
          : (map['total'] as double? ?? 0.0),
      paidAmount: map['paid_amount'] is int
          ? (map['paid_amount'] as int).toDouble()
          : (map['paid_amount'] as double? ?? 0.0),
      balance: map['balance'] is int
          ? (map['balance'] as int).toDouble()
          : (map['balance'] as double? ?? 0.0),
      paymentMethod: map['payment_method'],
      paymentStatus: map['payment_status'],
      paymentTerms: map['payment_terms'],
      notes: map['notes'],
      termsConditions: map['terms_conditions'],
      isPosted: map['is_posted'] == 1,
      referenceNumber: map['reference_number'],
      metadata: map['metadata'] != null
          ? (map['metadata'] is String
              ? jsonDecode(map['metadata'])
              : map['metadata'] as Map<String, dynamic>)
          : null,
      items: items ?? [],
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحليل نوع الفاتورة من النص
  static InvoiceType _parseInvoiceType(String? typeString) {
    switch (typeString) {
      case 'sale':
        return InvoiceType.sale;
      case 'purchase':
        return InvoiceType.purchase;
      case 'saleReturn':
        return InvoiceType.saleReturn;
      case 'purchaseReturn':
        return InvoiceType.purchaseReturn;
      case 'quotation':
        return InvoiceType.quotation;
      case 'proforma':
        return InvoiceType.proforma;
      default:
        return InvoiceType.sale;
    }
  }

  /// تحليل حالة الفاتورة من النص
  static InvoiceStatus _parseInvoiceStatus(String? statusString) {
    switch (statusString) {
      case 'draft':
        return InvoiceStatus.draft;
      case 'confirmed':
        return InvoiceStatus.confirmed;
      case 'partiallyPaid':
        return InvoiceStatus.partiallyPaid;
      case 'paid':
        return InvoiceStatus.paid;
      case 'cancelled':
        return InvoiceStatus.cancelled;
      case 'refunded':
        return InvoiceStatus.refunded;
      case 'pending':
        return InvoiceStatus.pending;
      default:
        return InvoiceStatus.draft;
    }
  }

  /// إنشاء فاتورة فارغة
  factory Invoice.empty({
    required InvoiceType invoiceType,
    required String warehouseId,
  }) {
    return Invoice(
      invoiceNumber: '',
      invoiceType: invoiceType,
      date: DateTime.now(),
      warehouseId: warehouseId,
    );
  }

  @override
  String toString() {
    return 'Invoice(id: $id, invoiceNumber: $invoiceNumber, type: $invoiceType, total: $total, status: $status)';
  }
}
