import 'package:uuid/uuid.dart';
import '../../../core/database/database_service.dart';
import '../../../core/utils/app_logger.dart';
import '../models/promotion.dart';

/// خدمة العروض الترويجية
class PromotionService {
  // نمط Singleton
  static final PromotionService _instance = PromotionService._internal();
  factory PromotionService() => _instance;
  PromotionService._internal();

  final DatabaseService _db = DatabaseService.instance;

  /// الحصول على جميع العروض النشطة
  Future<List<Promotion>> getActivePromotions() async {
    try {
      final List<Map<String, dynamic>> maps = await _db.query(
        'promotions',
        where: 'is_active = ? AND is_deleted = ?',
        whereArgs: [1, 0],
        orderBy: 'display_order ASC',
      );

      return List.generate(maps.length, (i) {
        return Promotion.fromMap(maps[i]);
      });
    } catch (e) {
      AppLogger.error('فشل في الحصول على العروض النشطة', error: e);
      return [];
    }
  }

  /// الحصول على جميع العروض
  Future<List<Promotion>> getAllPromotions() async {
    try {
      final List<Map<String, dynamic>> maps = await _db.query(
        'promotions',
        where: 'is_deleted = ?',
        whereArgs: [0],
        orderBy: 'display_order ASC',
      );

      return List.generate(maps.length, (i) {
        return Promotion.fromMap(maps[i]);
      });
    } catch (e) {
      AppLogger.error('فشل في الحصول على جميع العروض', error: e);
      return [];
    }
  }

  /// الحصول على عرض بواسطة المعرف
  Future<Promotion?> getPromotionById(String id) async {
    try {
      final List<Map<String, dynamic>> maps = await _db.query(
        'promotions',
        where: 'id = ? AND is_deleted = ?',
        whereArgs: [id, 0],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return Promotion.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      AppLogger.error('فشل في الحصول على العرض', error: e);
      return null;
    }
  }

  /// إضافة عرض جديد
  Future<String> insertPromotion(Promotion promotion) async {
    try {
      final map = promotion.toMap();

      // التأكد من وجود معرف
      final id = map['id'] ?? const Uuid().v4();
      map['id'] = id;

      await _db.insert('promotions', map);
      return id;
    } catch (e) {
      AppLogger.error('فشل في إضافة العرض', error: e);
      return '';
    }
  }

  /// تحديث عرض موجود
  Future<bool> updatePromotion(Promotion promotion) async {
    try {
      final map = promotion.toMap();
      map['updated_at'] = DateTime.now().toIso8601String();

      final rowsAffected = await _db.update(
        'promotions',
        map,
        where: 'id = ?',
        whereArgs: [promotion.id],
      );

      return rowsAffected > 0;
    } catch (e) {
      AppLogger.error('فشل في تحديث العرض', error: e);
      return false;
    }
  }

  /// حذف عرض (حذف منطقي)
  Future<bool> deletePromotion(String id) async {
    try {
      final rowsAffected = await _db.update(
        'promotions',
        {
          'is_deleted': 1,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      return rowsAffected > 0;
    } catch (e) {
      AppLogger.error('فشل في حذف العرض', error: e);
      return false;
    }
  }
}
