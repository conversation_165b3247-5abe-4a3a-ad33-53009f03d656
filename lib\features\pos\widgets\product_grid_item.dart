import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';
import '../../../core/models/product.dart';

/// واجهة عنصر المنتج في الشبكة
class ProductGridItem extends StatelessWidget {
  final Product product;
  final VoidCallback onTap;

  const ProductGridItem({
    Key? key,
    required this.product,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.lightTextSecondary,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: AppColors.lightTextSecondary.withValues(alpha: 0.2),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // صورة المنتج
            Expanded(
              flex: 3,
              child: ClipRRect(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(8)),
                child: product.imageUrl != null
                    ? Image.network(
                        product.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: AppColors.lightTextSecondary,
                            child: const Icon(
                              Icons.image_not_supported,
                              size: 50,
                              color: AppColors.lightTextSecondary,
                            ),
                          );
                        },
                      )
                    : Container(
                        color: AppColors.lightTextSecondary,
                        child: Icon(
                          product.isService
                              ? Icons.miscellaneous_services
                              : Icons.inventory_2,
                          size: 50,
                          color: AppColors.lightTextSecondary,
                        ),
                      ),
              ),
            ),

            // معلومات المنتج
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // اسم المنتج
                    Text(
                      product.name,
                      style: AppTypography.lightTextTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ) ??
                          const AppTypography(fontWeight: FontWeight.bold),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // الكود أو الباركود
                    if (product.code != null || product.barcode != null)
                      Text(
                        product.code ?? product.barcode!,
                        style: AppTypography.lightTextTheme.bodySmall,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                    // السعر
                    Text(
                      product.salePrice.toStringAsFixed(2),
                      style: AppTypography.lightTextTheme.titleMedium?.copyWith(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ) ??
                          const AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
