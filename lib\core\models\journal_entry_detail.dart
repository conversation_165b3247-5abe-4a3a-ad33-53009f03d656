import 'package:uuid/uuid.dart';
import 'base_model.dart';
import 'account.dart';

/// نموذج تفاصيل القيد المحاسبي
class JournalEntryDetail extends BaseModel {
  final String journalEntryId;
  final String accountId;
  final Account? account; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final String? description;
  final double debit;
  final double credit;
  final String? currencyId;
  final double exchangeRate;
  final String? reference;

  JournalEntryDetail({
    String? id,
    required this.journalEntryId,
    required this.accountId,
    this.account,
    this.description,
    this.debit = 0.0,
    this.credit = 0.0,
    this.currencyId,
    this.exchangeRate = 1.0,
    this.reference,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: null,
          updatedAt: updatedAt,
          updatedBy: null,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا التفصيل مع استبدال الحقول المحددة بقيم جديدة
  JournalEntryDetail copyWith({
    String? id,
    String? journalEntryId,
    String? accountId,
    Account? account,
    String? description,
    double? debit,
    double? credit,
    String? currencyId,
    double? exchangeRate,
    String? reference,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
  }) {
    return JournalEntryDetail(
      id: id ?? this.id,
      journalEntryId: journalEntryId ?? this.journalEntryId,
      accountId: accountId ?? this.accountId,
      account: account ?? this.account,
      description: description ?? this.description,
      debit: debit ?? this.debit,
      credit: credit ?? this.credit,
      currencyId: currencyId ?? this.currencyId,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      reference: reference ?? this.reference,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل تفاصيل القيد المحاسبي إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'journal_entry_id': journalEntryId,
      'account_id': accountId,
      'description': description,
      'debit': debit,
      'credit': credit,
      'currency_id': currencyId,
      'exchange_rate': exchangeRate,
      'reference': reference,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء تفاصيل قيد محاسبي من Map
  factory JournalEntryDetail.fromMap(Map<String, dynamic> map) {
    return JournalEntryDetail(
      id: map['id'],
      journalEntryId: map['journal_entry_id'],
      accountId: map['account_id'],
      account: map['account'] != null ? Account.fromMap(map['account']) : null,
      description: map['description'],
      debit: map['debit'] is int
          ? (map['debit'] as int).toDouble()
          : (map['debit'] as double? ?? 0.0),
      credit: map['credit'] is int
          ? (map['credit'] as int).toDouble()
          : (map['credit'] as double? ?? 0.0),
      currencyId: map['currency_id'],
      exchangeRate: map['exchange_rate'] is int
          ? (map['exchange_rate'] as int).toDouble()
          : (map['exchange_rate'] as double? ?? 1.0),
      reference: map['reference'],
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      isDeleted: map['is_deleted'] == 1,
    );
  }

  @override
  String toString() {
    return 'JournalEntryDetail(id: $id, accountId: $accountId, debit: $debit, credit: $credit)';
  }
}
