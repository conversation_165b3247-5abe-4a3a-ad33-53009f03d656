import 'package:sqflite/sqflite.dart';
import '../database/database_service.dart';
import '../models/inventory.dart';
import '../models/inventory_transaction.dart';
import '../models/product.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';
import 'warehouse_service.dart';

/// خدمة للعمليات المتعلقة بالمخزون
class InventoryService {
  // نمط Singleton
  static final InventoryService _instance = InventoryService._internal();
  factory InventoryService() => _instance;
  InventoryService._internal();

  final DatabaseService _db = DatabaseService.instance;
  final WarehouseService _warehouseService = WarehouseService();

  /// الحصول على مخزون المنتج في جميع المستودعات
  Future<List<Inventory>> getProductInventory(String productId) async {
    try {
      AppLogger.info('الحصول على مخزون المنتج: $productId');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          i.*,
          p.name as product_name,
          p.code as product_code,
          w.name as warehouse_name,
          w.code as warehouse_code
        FROM ${DatabaseService.tableInventory} i
        JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        JOIN ${DatabaseService.tableWarehouses} w ON i.warehouse_id = w.id
        WHERE i.product_id = ? AND i.is_deleted = 0 AND p.is_deleted = 0 AND w.is_deleted = 0
        ORDER BY w.name ASC
      ''', [productId]);

      // تحويل إلى كائنات Inventory
      return List.generate(maps.length, (i) {
        final map = maps[i];

        // إضافة معلومات المنتج والمستودع إلى الخريطة
        if (map['product_name'] != null) {
          map['product'] = {
            'id': map['product_id'],
            'name': map['product_name'],
            'code': map['product_code'],
          };
        }

        if (map['warehouse_name'] != null) {
          map['warehouse'] = {
            'id': map['warehouse_id'],
            'name': map['warehouse_name'],
            'code': map['warehouse_code'],
          };
        }

        return Inventory.fromMap(map);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على مخزون المنتج',
        error: e,
        stackTrace: stackTrace,
        context: {'productId': productId},
      );
      return [];
    }
  }

  /// الحصول على مخزون المستودع
  Future<List<Inventory>> getWarehouseInventory(String warehouseId,
      {String? searchQuery}) async {
    try {
      AppLogger.info('الحصول على مخزون المستودع: $warehouseId');

      // بناء شرط WHERE
      String whereClause =
          'i.warehouse_id = ? AND i.is_deleted = 0 AND p.is_deleted = 0';
      List<dynamic> whereArgs = [warehouseId];

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause +=
            ' AND (p.name LIKE ? OR p.code LIKE ? OR p.barcode LIKE ?)';
        whereArgs
            .addAll(['%$searchQuery%', '%$searchQuery%', '%$searchQuery%']);
      }

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          i.*,
          p.name as product_name,
          p.code as product_code,
          p.barcode as product_barcode,
          p.sale_price as product_sale_price,
          p.purchase_price as product_purchase_price,
          w.name as warehouse_name,
          w.code as warehouse_code
        FROM ${DatabaseService.tableInventory} i
        JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        JOIN ${DatabaseService.tableWarehouses} w ON i.warehouse_id = w.id
        WHERE $whereClause
        ORDER BY p.name ASC
      ''', whereArgs);

      // تحويل إلى كائنات Inventory
      return List.generate(maps.length, (i) {
        final map = maps[i];

        // إضافة معلومات المنتج والمستودع إلى الخريطة
        if (map['product_name'] != null) {
          map['product'] = {
            'id': map['product_id'],
            'name': map['product_name'],
            'code': map['product_code'],
            'barcode': map['product_barcode'],
            'sale_price': map['product_sale_price'],
            'purchase_price': map['product_purchase_price'],
          };
        }

        if (map['warehouse_name'] != null) {
          map['warehouse'] = {
            'id': map['warehouse_id'],
            'name': map['warehouse_name'],
            'code': map['warehouse_code'],
          };
        }

        return Inventory.fromMap(map);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على مخزون المستودع',
        error: e,
        stackTrace: stackTrace,
        context: {'warehouseId': warehouseId},
      );
      return [];
    }
  }

  /// الحصول على مخزون منتج في مستودع معين
  Future<Inventory?> getProductInventoryInWarehouse(
      String productId, String warehouseId) async {
    try {
      AppLogger.info(
          'الحصول على مخزون المنتج في المستودع: $productId, $warehouseId');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          i.*,
          p.name as product_name,
          p.code as product_code,
          w.name as warehouse_name,
          w.code as warehouse_code
        FROM ${DatabaseService.tableInventory} i
        JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        JOIN ${DatabaseService.tableWarehouses} w ON i.warehouse_id = w.id
        WHERE i.product_id = ? AND i.warehouse_id = ? AND i.is_deleted = 0
        LIMIT 1
      ''', [productId, warehouseId]);

      if (maps.isEmpty) {
        return null;
      }

      final map = maps.first;

      // إضافة معلومات المنتج والمستودع إلى الخريطة
      if (map['product_name'] != null) {
        map['product'] = {
          'id': map['product_id'],
          'name': map['product_name'],
          'code': map['product_code'],
        };
      }

      if (map['warehouse_name'] != null) {
        map['warehouse'] = {
          'id': map['warehouse_id'],
          'name': map['warehouse_name'],
          'code': map['warehouse_code'],
        };
      }

      return Inventory.fromMap(map);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على مخزون المنتج في المستودع',
        error: e,
        stackTrace: stackTrace,
        context: {'productId': productId, 'warehouseId': warehouseId},
      );
      return null;
    }
  }

  /// الحصول على كمية المنتج في مستودع معين
  Future<double> getProductQuantityInWarehouse(
      String productId, String warehouseId) async {
    try {
      AppLogger.info(
          'الحصول على كمية المنتج في المستودع: $productId, $warehouseId');

      final inventory =
          await getProductInventoryInWarehouse(productId, warehouseId);

      return inventory?.quantity ?? 0.0;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على كمية المنتج في المستودع',
        error: e,
        stackTrace: stackTrace,
        context: {'productId': productId, 'warehouseId': warehouseId},
      );
      return 0.0;
    }
  }

  /// الحصول على إجمالي كمية المنتج في جميع المستودعات
  Future<double> getTotalProductQuantity(String productId) async {
    try {
      AppLogger.info('الحصول على إجمالي كمية المنتج: $productId');

      final result = await _db.rawQuery('''
        SELECT SUM(quantity) as total_quantity
        FROM ${DatabaseService.tableInventory}
        WHERE product_id = ? AND is_deleted = 0
      ''', [productId]);

      if (result.isEmpty || result.first['total_quantity'] == null) {
        return 0.0;
      }

      final totalQuantity = result.first['total_quantity'];

      return totalQuantity is int
          ? (totalQuantity).toDouble()
          : (totalQuantity as double? ?? 0.0);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على إجمالي كمية المنتج',
        error: e,
        stackTrace: stackTrace,
        context: {'productId': productId},
      );
      return 0.0;
    }
  }

  /// تحديث كمية المنتج في المستودع
  Future<bool> updateProductQuantity(
    String productId,
    String warehouseId,
    double quantity,
    InventoryTransactionType transactionType, {
    String? referenceId,
    String? referenceType,
    String? unitId,
    double? unitCost,
    DateTime? expiryDate,
    String? batchNumber,
    String? notes,
    String? userId,
  }) async {
    try {
      AppLogger.info(
          'تحديث كمية المنتج في المستودع: $productId, $warehouseId, $quantity');

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        // التحقق من وجود المنتج في المخزون
        final inventory =
            await _getProductInventoryInWarehouse(txn, productId, warehouseId);

        if (inventory == null) {
          // إنشاء سجل مخزون جديد
          final newInventory = Inventory(
            productId: productId,
            warehouseId: warehouseId,
            quantity: quantity,
          );

          await txn.insert(
              DatabaseService.tableInventory, newInventory.toMap());
        } else {
          // تحديث سجل المخزون الموجود
          final newQuantity = inventory.quantity + quantity;

          // لا يمكن أن تكون الكمية سالبة
          if (newQuantity < 0) {
            AppLogger.warning(
                'لا يمكن أن تكون كمية المخزون سالبة: $productId, $warehouseId, $newQuantity');
            return false;
          }

          // تحديث تاريخ آخر عملية بناءً على نوع الحركة
          Map<String, dynamic> updateData = {
            'quantity': newQuantity,
            'updated_at': DateTime.now().toIso8601String(),
          };

          switch (transactionType) {
            case InventoryTransactionType.purchase:
            case InventoryTransactionType.returnIn:
              updateData['last_purchase_date'] =
                  DateTime.now().toIso8601String();
              break;
            case InventoryTransactionType.sale:
            case InventoryTransactionType.returnOut:
              updateData['last_sale_date'] = DateTime.now().toIso8601String();
              break;
            case InventoryTransactionType.count:
              updateData['last_count_date'] = DateTime.now().toIso8601String();
              break;
            default:
              break;
          }

          await txn.update(
            DatabaseService.tableInventory,
            updateData,
            where: 'product_id = ? AND warehouse_id = ?',
            whereArgs: [productId, warehouseId],
          );
        }

        // إنشاء سجل حركة المخزون
        final transaction = InventoryTransaction(
          transactionType: transactionType,
          referenceId: referenceId,
          referenceType: referenceType,
          productId: productId,
          warehouseId: warehouseId,
          quantity: quantity,
          unitId: unitId,
          unitCost: unitCost,
          expiryDate: expiryDate,
          batchNumber: batchNumber,
          notes: notes,
          createdBy: userId,
        );

        await txn.insert(
            DatabaseService.tableInventoryTransactions, transaction.toMap());

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث كمية المنتج في المستودع',
        error: e,
        stackTrace: stackTrace,
        context: {
          'productId': productId,
          'warehouseId': warehouseId,
          'quantity': quantity,
          'transactionType': transactionType.toString(),
        },
      );
      return false;
    }
  }

  /// نقل المنتج بين المستودعات
  Future<bool> transferProduct(
    String productId,
    String fromWarehouseId,
    String toWarehouseId,
    double quantity, {
    String? referenceId,
    String? notes,
    String? userId,
  }) async {
    try {
      AppLogger.info(
          'نقل المنتج بين المستودعات: $productId, من $fromWarehouseId إلى $toWarehouseId, الكمية: $quantity');

      // التحقق من أن الكمية موجبة
      if (quantity <= 0) {
        AppLogger.warning('يجب أن تكون كمية النقل موجبة: $quantity');
        return false;
      }

      // التحقق من أن المستودعات مختلفة
      if (fromWarehouseId == toWarehouseId) {
        AppLogger.warning('لا يمكن نقل المنتج إلى نفس المستودع');
        return false;
      }

      // التحقق من وجود كمية كافية في المستودع المصدر
      final sourceQuantity =
          await getProductQuantityInWarehouse(productId, fromWarehouseId);
      if (sourceQuantity < quantity) {
        AppLogger.warning(
            'لا توجد كمية كافية في المستودع المصدر: $sourceQuantity < $quantity');
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        // إنشاء معرف مرجعي للنقل إذا لم يتم توفيره
        final transferId =
            referenceId ?? DateTime.now().millisecondsSinceEpoch.toString();

        // خفض الكمية من المستودع المصدر
        final sourceTransaction = InventoryTransaction(
          transactionType: InventoryTransactionType.transfer,
          referenceId: transferId,
          referenceType: 'transfer_out',
          productId: productId,
          warehouseId: fromWarehouseId,
          quantity: -quantity,
          notes: notes,
          createdBy: userId,
        );

        await txn.insert(DatabaseService.tableInventoryTransactions,
            sourceTransaction.toMap());

        // تحديث المخزون في المستودع المصدر
        final sourceInventory = await _getProductInventoryInWarehouse(
            txn, productId, fromWarehouseId);
        if (sourceInventory == null) {
          // هذا لا ينبغي أن يحدث لأننا تحققنا من الكمية مسبقًا
          return false;
        }

        await txn.update(
          DatabaseService.tableInventory,
          {
            'quantity': sourceInventory.quantity - quantity,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'product_id = ? AND warehouse_id = ?',
          whereArgs: [productId, fromWarehouseId],
        );

        // زيادة الكمية في المستودع الهدف
        final targetTransaction = InventoryTransaction(
          transactionType: InventoryTransactionType.transfer,
          referenceId: transferId,
          referenceType: 'transfer_in',
          productId: productId,
          warehouseId: toWarehouseId,
          quantity: quantity,
          notes: notes,
          createdBy: userId,
        );

        await txn.insert(DatabaseService.tableInventoryTransactions,
            targetTransaction.toMap());

        // تحديث المخزون في المستودع الهدف
        final targetInventory = await _getProductInventoryInWarehouse(
            txn, productId, toWarehouseId);

        if (targetInventory == null) {
          // إنشاء سجل مخزون جديد
          final newInventory = Inventory(
            productId: productId,
            warehouseId: toWarehouseId,
            quantity: quantity,
          );

          await txn.insert(
              DatabaseService.tableInventory, newInventory.toMap());
        } else {
          // تحديث سجل المخزون الموجود
          await txn.update(
            DatabaseService.tableInventory,
            {
              'quantity': targetInventory.quantity + quantity,
              'updated_at': DateTime.now().toIso8601String(),
            },
            where: 'product_id = ? AND warehouse_id = ?',
            whereArgs: [productId, toWarehouseId],
          );
        }

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في نقل المنتج بين المستودعات',
        error: e,
        stackTrace: stackTrace,
        context: {
          'productId': productId,
          'fromWarehouseId': fromWarehouseId,
          'toWarehouseId': toWarehouseId,
          'quantity': quantity,
        },
      );
      return false;
    }
  }

  /// الحصول على حركات المخزون للمنتج
  Future<List<InventoryTransaction>> getProductTransactions(
    String productId, {
    DateTime? fromDate,
    DateTime? toDate,
    InventoryTransactionType? type,
    String? warehouseId,
    int? limit,
    int? offset,
  }) async {
    try {
      AppLogger.info('الحصول على حركات المخزون للمنتج: $productId');

      // بناء شرط WHERE
      String whereClause = 't.product_id = ? AND t.is_deleted = 0';
      List<dynamic> whereArgs = [productId];

      if (fromDate != null) {
        whereClause += ' AND t.transaction_date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND t.transaction_date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      if (type != null) {
        whereClause += ' AND t.transaction_type = ?';
        whereArgs.add(type.toString().split('.').last);
      }

      if (warehouseId != null) {
        whereClause += ' AND t.warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          t.*,
          p.name as product_name,
          p.code as product_code,
          w.name as warehouse_name,
          w.code as warehouse_code,
          u.name as unit_name,
          u.abbreviation as unit_abbreviation
        FROM ${DatabaseService.tableInventoryTransactions} t
        JOIN ${DatabaseService.tableProducts} p ON t.product_id = p.id
        JOIN ${DatabaseService.tableWarehouses} w ON t.warehouse_id = w.id
        LEFT JOIN ${DatabaseService.tableUnits} u ON t.unit_id = u.id
        WHERE $whereClause
        ORDER BY t.transaction_date DESC, t.created_at DESC
        ${limit != null ? 'LIMIT $limit' : ''}
        ${offset != null ? 'OFFSET $offset' : ''}
      ''', whereArgs);

      // تحويل إلى كائنات InventoryTransaction
      return List.generate(maps.length, (i) {
        final map = maps[i];

        // إضافة معلومات المنتج والمستودع والوحدة إلى الخريطة
        if (map['product_name'] != null) {
          map['product'] = {
            'id': map['product_id'],
            'name': map['product_name'],
            'code': map['product_code'],
          };
        }

        if (map['warehouse_name'] != null) {
          map['warehouse'] = {
            'id': map['warehouse_id'],
            'name': map['warehouse_name'],
            'code': map['warehouse_code'],
          };
        }

        if (map['unit_name'] != null) {
          map['unit'] = {
            'id': map['unit_id'],
            'name': map['unit_name'],
            'abbreviation': map['unit_abbreviation'],
          };
        }

        return InventoryTransaction.fromMap(map);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على حركات المخزون للمنتج',
        error: e,
        stackTrace: stackTrace,
        context: {'productId': productId},
      );
      return [];
    }
  }

  /// الحصول على مخزون منتج في مستودع معين (داخل معاملة)
  Future<Inventory?> _getProductInventoryInWarehouse(
    Transaction txn,
    String productId,
    String warehouseId,
  ) async {
    try {
      final List<Map<String, dynamic>> maps = await txn.query(
        DatabaseService.tableInventory,
        where: 'product_id = ? AND warehouse_id = ? AND is_deleted = 0',
        whereArgs: [productId, warehouseId],
      );

      if (maps.isEmpty) {
        return null;
      }

      return Inventory.fromMap(maps.first);
    } catch (e) {
      AppLogger.error(
          'فشل في الحصول على مخزون المنتج في المستودع (داخل معاملة): $e');
      return null;
    }
  }

  /// إنشاء سجلات المخزون الأولية للمنتج في جميع المستودعات
  Future<bool> initializeProductInventory(Product product,
      {String? userId}) async {
    try {
      AppLogger.info('إنشاء سجلات المخزون الأولية للمنتج: ${product.name}');

      // الحصول على جميع المستودعات النشطة
      final warehouses = await _warehouseService.getAllWarehouses();

      if (warehouses.isEmpty) {
        // لا توجد مستودعات - يجب تهيئة المستودعات في BasicDataInitializer أولاً
        AppLogger.warning('لا توجد مستودعات متاحة لإنشاء سجلات المخزون');
        AppLogger.info('يجب تهيئة المستودعات في BasicDataInitializer أولاً');
        return false;
      }

      // إنشاء سجلات المخزون
      return await _createInventoryRecords(product, warehouses);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إنشاء سجلات المخزون الأولية للمنتج',
        error: e,
        stackTrace: stackTrace,
        context: {'product': product.toString()},
      );
      return false;
    }
  }

  /// إنشاء سجلات المخزون للمنتج في المستودعات
  Future<bool> _createInventoryRecords(
      dynamic product, List<dynamic> warehouses) async {
    try {
      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        for (final warehouse in warehouses) {
          // التحقق من عدم وجود سجل مخزون للمنتج في المستودع
          final inventory = await _getProductInventoryInWarehouse(
              txn, product.id, warehouse.id);

          if (inventory == null) {
            // إنشاء سجل مخزون جديد
            final newInventory = Inventory(
              productId: product.id,
              warehouseId: warehouse.id,
              quantity: 0.0,
              minQuantity: product.minStock,
              maxQuantity: product.maxStock,
            );

            await txn.insert(
                DatabaseService.tableInventory, newInventory.toMap());
          }
        }

        return true;
      });
    } catch (e) {
      AppLogger.error('فشل في إنشاء سجلات المخزون للمنتج في المستودعات: $e');
      return false;
    }
  }

  /// الحصول على المخزون الحالي
  Future<List<Map<String, dynamic>>> getCurrentStock({
    String? warehouseId,
    String? productId,
  }) async {
    try {
      AppLogger.info('الحصول على المخزون الحالي');

      // بناء شرط WHERE
      String whereClause =
          'i.is_deleted = 0 AND p.is_deleted = 0 AND w.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (warehouseId != null) {
        whereClause += ' AND i.warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      if (productId != null) {
        whereClause += ' AND i.product_id = ?';
        whereArgs.add(productId);
      }

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          i.*,
          p.name as product_name,
          p.code as product_code,
          p.barcode as product_barcode,
          p.sale_price as product_sale_price,
          p.purchase_price as product_purchase_price,
          w.name as warehouse_name,
          w.code as warehouse_code
        FROM ${DatabaseService.tableInventory} i
        JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        JOIN ${DatabaseService.tableWarehouses} w ON i.warehouse_id = w.id
        WHERE $whereClause
        ORDER BY p.name ASC
      ''', whereArgs);

      // تحويل إلى قائمة من الخرائط
      return maps.map((map) {
        return {
          'product_id': map['product_id'],
          'warehouse_id': map['warehouse_id'],
          'quantity': map['quantity'] is int
              ? (map['quantity'] as int).toDouble()
              : (map['quantity'] as double? ?? 0.0),
          'min_quantity': map['min_quantity'] is int
              ? (map['min_quantity'] as int).toDouble()
              : (map['min_quantity'] as double? ?? 0.0),
          'max_quantity': map['max_quantity'] is int
              ? (map['max_quantity'] as int).toDouble()
              : (map['max_quantity'] as double? ?? 0.0),
          'product_name': map['product_name'],
          'product_code': map['product_code'],
          'product_barcode': map['product_barcode'],
          'product_sale_price': map['product_sale_price'],
          'product_purchase_price': map['product_purchase_price'],
          'warehouse_name': map['warehouse_name'],
          'warehouse_code': map['warehouse_code'],
          'last_updated': map['updated_at'],
        };
      }).toList();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على المخزون الحالي',
        error: e,
        stackTrace: stackTrace,
        context: {
          'warehouseId': warehouseId,
          'productId': productId,
        },
      );
      return [];
    }
  }

  /// الحصول على حركات المخزون
  Future<List<InventoryTransaction>> getInventoryTransactions({
    String? warehouseId,
    String? productId,
    DateTime? fromDate,
    DateTime? toDate,
    String? type,
    int? limit,
    int? offset,
  }) async {
    try {
      AppLogger.info('الحصول على حركات المخزون');

      // بناء شرط WHERE
      String whereClause = 't.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (warehouseId != null) {
        whereClause += ' AND t.warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      if (productId != null) {
        whereClause += ' AND t.product_id = ?';
        whereArgs.add(productId);
      }

      if (fromDate != null) {
        whereClause += ' AND t.transaction_date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND t.transaction_date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      if (type != null) {
        whereClause += ' AND t.transaction_type = ?';
        whereArgs.add(type);
      }

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          t.*,
          p.name as product_name,
          p.code as product_code,
          w.name as warehouse_name,
          w.code as warehouse_code,
          u.name as unit_name,
          u.abbreviation as unit_abbreviation
        FROM ${DatabaseService.tableInventoryTransactions} t
        JOIN ${DatabaseService.tableProducts} p ON t.product_id = p.id
        JOIN ${DatabaseService.tableWarehouses} w ON t.warehouse_id = w.id
        LEFT JOIN ${DatabaseService.tableUnits} u ON t.unit_id = u.id
        WHERE $whereClause
        ORDER BY t.transaction_date DESC, t.created_at DESC
        ${limit != null ? 'LIMIT $limit' : ''}
        ${offset != null ? 'OFFSET $offset' : ''}
      ''', whereArgs);

      // تحويل إلى كائنات InventoryTransaction
      return List.generate(maps.length, (i) {
        final map = maps[i];

        // إضافة معلومات المنتج والمستودع والوحدة إلى الخريطة
        if (map['product_name'] != null) {
          map['product'] = {
            'id': map['product_id'],
            'name': map['product_name'],
            'code': map['product_code'],
          };
        }

        if (map['warehouse_name'] != null) {
          map['warehouse'] = {
            'id': map['warehouse_id'],
            'name': map['warehouse_name'],
            'code': map['warehouse_code'],
          };
        }

        if (map['unit_name'] != null) {
          map['unit'] = {
            'id': map['unit_id'],
            'name': map['unit_name'],
            'abbreviation': map['unit_abbreviation'],
          };
        }

        return InventoryTransaction.fromMap(map);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على حركات المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {
          'warehouseId': warehouseId,
          'productId': productId,
          'fromDate': fromDate?.toIso8601String(),
          'toDate': toDate?.toIso8601String(),
          'type': type,
        },
      );
      return [];
    }
  }

  /// الحصول على حركة منتج
  Future<List<Map<String, dynamic>>> getProductMovement({
    required String productId,
    String? warehouseId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      AppLogger.info('الحصول على حركة المنتج: $productId');

      // بناء شرط WHERE
      String whereClause = 't.product_id = ? AND t.is_deleted = 0';
      List<dynamic> whereArgs = [productId];

      if (warehouseId != null) {
        whereClause += ' AND t.warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      if (fromDate != null) {
        whereClause += ' AND t.transaction_date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND t.transaction_date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      final List<Map<String, dynamic>> transactions = await _db.rawQuery('''
        SELECT
          t.*,
          w.name as warehouse_name
        FROM ${DatabaseService.tableInventoryTransactions} t
        JOIN ${DatabaseService.tableWarehouses} w ON t.warehouse_id = w.id
        WHERE $whereClause
        ORDER BY t.transaction_date ASC, t.created_at ASC
      ''', whereArgs);

      // حساب الرصيد التراكمي
      double balance = 0.0;
      final List<Map<String, dynamic>> movements = [];

      for (final transaction in transactions) {
        final quantity = transaction['quantity'] is int
            ? (transaction['quantity'] as int).toDouble()
            : (transaction['quantity'] as double? ?? 0.0);

        balance += quantity;

        movements.add({
          'date': DateTime.parse(transaction['transaction_date']),
          'warehouse_id': transaction['warehouse_id'],
          'warehouse_name': transaction['warehouse_name'],
          'type': transaction['transaction_type'],
          'quantity': quantity,
          'balance': balance,
          'reference': transaction['reference_id'],
          'notes': transaction['notes'],
        });
      }

      return movements;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على حركة المنتج',
        error: e,
        stackTrace: stackTrace,
        context: {
          'productId': productId,
          'warehouseId': warehouseId,
          'fromDate': fromDate?.toIso8601String(),
          'toDate': toDate?.toIso8601String(),
        },
      );
      return [];
    }
  }

  /// الحصول على إحصائيات المخزون
  Future<Map<String, dynamic>> getInventoryStatistics({
    String? warehouseId,
  }) async {
    try {
      AppLogger.info('الحصول على إحصائيات المخزون');

      // بناء شرط WHERE
      String whereClause = 'i.is_deleted = 0 AND p.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (warehouseId != null) {
        whereClause += ' AND i.warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      // إجمالي عدد المنتجات
      final totalProductsResult = await _db.rawQuery('''
        SELECT COUNT(DISTINCT i.product_id) as total_products
        FROM ${DatabaseService.tableInventory} i
        JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        WHERE $whereClause
      ''', whereArgs);

      final totalProducts =
          totalProductsResult.first['total_products'] as int? ?? 0;

      // إجمالي قيمة المخزون
      final totalValueResult = await _db.rawQuery('''
        SELECT SUM(i.quantity * p.purchase_price) as total_value
        FROM ${DatabaseService.tableInventory} i
        JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        WHERE $whereClause
      ''', whereArgs);

      final totalValue =
          totalValueResult.first['total_value'] as double? ?? 0.0;

      // عدد المنتجات منخفضة المخزون
      final lowStockResult = await _db.rawQuery('''
        SELECT COUNT(*) as low_stock_count
        FROM ${DatabaseService.tableInventory} i
        JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        WHERE $whereClause AND i.quantity < i.min_quantity AND i.min_quantity > 0
      ''', whereArgs);

      final lowStockCount =
          lowStockResult.first['low_stock_count'] as int? ?? 0;

      // عدد المنتجات النافدة
      final outOfStockResult = await _db.rawQuery('''
        SELECT COUNT(*) as out_of_stock_count
        FROM ${DatabaseService.tableInventory} i
        JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        WHERE $whereClause AND i.quantity = 0
      ''', whereArgs);

      final outOfStockCount =
          outOfStockResult.first['out_of_stock_count'] as int? ?? 0;

      return {
        'total_products': totalProducts,
        'total_value': totalValue,
        'low_stock_count': lowStockCount,
        'out_of_stock_count': outOfStockCount,
      };
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على إحصائيات المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {
          'warehouseId': warehouseId,
        },
      );
      return {
        'total_products': 0,
        'total_value': 0.0,
        'low_stock_count': 0,
        'out_of_stock_count': 0,
      };
    }
  }

  /// الحصول على المنتجات منخفضة المخزون
  Future<List<Map<String, dynamic>>> getLowStockProducts({
    String? warehouseId,
    int? limit,
    int? offset,
  }) async {
    try {
      AppLogger.info('الحصول على المنتجات منخفضة المخزون');

      // بناء شرط WHERE
      String whereClause =
          'i.is_deleted = 0 AND p.is_deleted = 0 AND i.quantity < i.min_quantity AND i.min_quantity > 0';
      List<dynamic> whereArgs = [];

      if (warehouseId != null) {
        whereClause += ' AND i.warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          i.*,
          p.name as product_name,
          p.code as product_code,
          p.barcode as product_barcode,
          p.sale_price as product_sale_price,
          p.purchase_price as product_purchase_price,
          w.name as warehouse_name,
          w.code as warehouse_code
        FROM ${DatabaseService.tableInventory} i
        JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        JOIN ${DatabaseService.tableWarehouses} w ON i.warehouse_id = w.id
        WHERE $whereClause
        ORDER BY (i.min_quantity - i.quantity) DESC
        ${limit != null ? 'LIMIT $limit' : ''}
        ${offset != null ? 'OFFSET $offset' : ''}
      ''', whereArgs);

      // تحويل إلى قائمة من الخرائط
      return maps.map((map) {
        return {
          'product_id': map['product_id'],
          'warehouse_id': map['warehouse_id'],
          'quantity': map['quantity'] is int
              ? (map['quantity'] as int).toDouble()
              : (map['quantity'] as double? ?? 0.0),
          'min_quantity': map['min_quantity'] is int
              ? (map['min_quantity'] as int).toDouble()
              : (map['min_quantity'] as double? ?? 0.0),
          'max_quantity': map['max_quantity'] is int
              ? (map['max_quantity'] as int).toDouble()
              : (map['max_quantity'] as double? ?? 0.0),
          'product_name': map['product_name'],
          'product_code': map['product_code'],
          'product_barcode': map['product_barcode'],
          'product_sale_price': map['product_sale_price'],
          'product_purchase_price': map['product_purchase_price'],
          'warehouse_name': map['warehouse_name'],
          'warehouse_code': map['warehouse_code'],
          'last_updated': map['updated_at'],
        };
      }).toList();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على المنتجات منخفضة المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {
          'warehouseId': warehouseId,
        },
      );
      return [];
    }
  }

  /// الحصول على المنتجات النافدة
  Future<List<Map<String, dynamic>>> getOutOfStockProducts({
    String? warehouseId,
    int? limit,
    int? offset,
  }) async {
    try {
      AppLogger.info('الحصول على المنتجات النافدة');

      // بناء شرط WHERE
      String whereClause =
          'i.is_deleted = 0 AND p.is_deleted = 0 AND i.quantity = 0';
      List<dynamic> whereArgs = [];

      if (warehouseId != null) {
        whereClause += ' AND i.warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          i.*,
          p.name as product_name,
          p.code as product_code,
          p.barcode as product_barcode,
          p.sale_price as product_sale_price,
          p.purchase_price as product_purchase_price,
          w.name as warehouse_name,
          w.code as warehouse_code
        FROM ${DatabaseService.tableInventory} i
        JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        JOIN ${DatabaseService.tableWarehouses} w ON i.warehouse_id = w.id
        WHERE $whereClause
        ORDER BY p.name ASC
        ${limit != null ? 'LIMIT $limit' : ''}
        ${offset != null ? 'OFFSET $offset' : ''}
      ''', whereArgs);

      // تحويل إلى قائمة من الخرائط
      return maps.map((map) {
        return {
          'product_id': map['product_id'],
          'warehouse_id': map['warehouse_id'],
          'quantity': map['quantity'] is int
              ? (map['quantity'] as int).toDouble()
              : (map['quantity'] as double? ?? 0.0),
          'min_quantity': map['min_quantity'] is int
              ? (map['min_quantity'] as int).toDouble()
              : (map['min_quantity'] as double? ?? 0.0),
          'max_quantity': map['max_quantity'] is int
              ? (map['max_quantity'] as int).toDouble()
              : (map['max_quantity'] as double? ?? 0.0),
          'product_name': map['product_name'],
          'product_code': map['product_code'],
          'product_barcode': map['product_barcode'],
          'product_sale_price': map['product_sale_price'],
          'product_purchase_price': map['product_purchase_price'],
          'warehouse_name': map['warehouse_name'],
          'warehouse_code': map['warehouse_code'],
          'last_updated': map['updated_at'],
        };
      }).toList();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على المنتجات النافدة',
        error: e,
        stackTrace: stackTrace,
        context: {
          'warehouseId': warehouseId,
        },
      );
      return [];
    }
  }

  /// الحصول على المنتجات الأكثر مبيعاً
  Future<List<Map<String, dynamic>>> getTopSellingProducts({
    String? warehouseId,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
  }) async {
    try {
      AppLogger.info('الحصول على المنتجات الأكثر مبيعاً');

      // بناء شرط WHERE
      String whereClause = 't.is_deleted = 0 AND t.transaction_type = ?';
      List<dynamic> whereArgs = ['sale'];

      if (warehouseId != null) {
        whereClause += ' AND t.warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      if (fromDate != null) {
        whereClause += ' AND t.transaction_date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND t.transaction_date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          t.product_id,
          p.name as product_name,
          p.code as product_code,
          p.barcode as product_barcode,
          p.sale_price as product_sale_price,
          p.purchase_price as product_purchase_price,
          SUM(ABS(t.quantity)) as total_quantity,
          COUNT(t.id) as transaction_count
        FROM ${DatabaseService.tableInventoryTransactions} t
        JOIN ${DatabaseService.tableProducts} p ON t.product_id = p.id
        WHERE $whereClause
        GROUP BY t.product_id
        ORDER BY total_quantity DESC
        ${limit != null ? 'LIMIT $limit' : ''}
      ''', whereArgs);

      // تحويل إلى قائمة من الخرائط
      return maps.map((map) {
        return {
          'product_id': map['product_id'],
          'product_name': map['product_name'],
          'product_code': map['product_code'],
          'product_barcode': map['product_barcode'],
          'product_sale_price': map['product_sale_price'],
          'product_purchase_price': map['product_purchase_price'],
          'total_quantity': map['total_quantity'] is int
              ? (map['total_quantity'] as int).toDouble()
              : (map['total_quantity'] as double? ?? 0.0),
          'transaction_count': map['transaction_count'] as int? ?? 0,
        };
      }).toList();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على المنتجات الأكثر مبيعاً',
        error: e,
        stackTrace: stackTrace,
        context: {
          'warehouseId': warehouseId,
          'fromDate': fromDate?.toIso8601String(),
          'toDate': toDate?.toIso8601String(),
        },
      );
      return [];
    }
  }
}
