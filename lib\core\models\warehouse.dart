import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج المستودع الموحد
/// تم توحيده من جميع نماذج المستودعات في المشروع
class Warehouse extends BaseModel {
  // معلومات أساسية
  final String name;
  final String? code;
  final String? description;

  // معلومات الموقع
  final String? address;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;
  final String? location;

  // معلومات الاتصال
  final String? phone;
  final String? email;

  // معلومات المدير
  final String? managerId;
  final String? managerName; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات

  // معلومات إضافية
  final String? notes;
  final String? branchId;
  final String? branchName;

  // حالة المستودع
  final bool isActive;
  final bool isDefault;

  Warehouse({
    String? id,
    required this.name,
    this.code,
    this.description,
    this.address,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.location,
    this.phone,
    this.email,
    this.managerId,
    this.managerName,
    this.notes,
    this.branchId,
    this.branchName,
    this.isActive = true,
    this.isDefault = false,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا المستودع مع استبدال الحقول المحددة بقيم جديدة
  Warehouse copyWith({
    String? id,
    String? name,
    String? code,
    String? description,
    String? address,
    String? city,
    String? state,
    String? country,
    String? postalCode,
    String? location,
    String? phone,
    String? email,
    String? managerId,
    String? managerName,
    String? notes,
    String? branchId,
    String? branchName,
    bool? isActive,
    bool? isDefault,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Warehouse(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      description: description ?? this.description,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      location: location ?? this.location,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      managerId: managerId ?? this.managerId,
      managerName: managerName ?? this.managerName,
      notes: notes ?? this.notes,
      branchId: branchId ?? this.branchId,
      branchName: branchName ?? this.branchName,
      isActive: isActive ?? this.isActive,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل المستودع إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'description': description,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'postal_code': postalCode,
      'location': location,
      'phone': phone,
      'email': email,
      'manager_id': managerId,
      'manager_name': managerName,
      'notes': notes,
      'branch_id': branchId,
      'branch_name': branchName,
      'is_active': isActive ? 1 : 0,
      'is_default': isDefault ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء مستودع من Map
  factory Warehouse.fromMap(Map<String, dynamic> map) {
    return Warehouse(
      id: map['id'],
      name: map['name'] ?? '',
      code: map['code'],
      description: map['description'],
      address: map['address'],
      city: map['city'],
      state: map['state'],
      country: map['country'],
      postalCode: map['postal_code'],
      location: map['location'],
      phone: map['phone'],
      email: map['email'],
      managerId: map['manager_id'],
      managerName: map['manager_name'],
      notes: map['notes'],
      branchId: map['branch_id'],
      branchName: map['branch_name'],
      isActive: map['is_active'] == 1,
      isDefault: map['is_default'] == 1,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// إنشاء مستودع فارغ
  factory Warehouse.empty() {
    return Warehouse(
      name: '',
    );
  }

  @override
  String toString() {
    return 'Warehouse(id: $id, name: $name, code: $code, isDefault: $isDefault)';
  }
}
