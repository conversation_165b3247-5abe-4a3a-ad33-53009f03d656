import 'package:flutter/material.dart';

/// أنواع مستويات الوصول
enum AccessLevelType {
  none,
  view,
  edit,
  full,
}

/// نموذج مستوى الوصول
class AccessLevel {
  final String id;
  final String name;
  final bool isCustom;
  final Map<String, AccessLevelType> functionLevels;
  
  AccessLevel({
    required this.id,
    required this.name,
    required this.isCustom,
    required this.functionLevels,
  });
}

/// نموذج وظيفة النظام
class JobFunction {
  final String id;
  final String name;
  final IconData icon;
  final Color color;
  final String description;
  
  JobFunction({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    required this.description,
  });
}
