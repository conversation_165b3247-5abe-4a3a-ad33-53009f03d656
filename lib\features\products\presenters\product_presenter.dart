import 'dart:async';
import '../../../core/models/product.dart';
import '../../../core/models/category.dart';
import '../../../core/models/unit.dart';
import '../../../core/models/product_unit.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/database/database_service.dart';
import '../../../core/services/category_service.dart';
import '../../../core/providers/base_presenter.dart';

class ProductPresenter extends BaseListPresenter<Product> {
  final _db = DatabaseService.instance;
  final _categoryService = CategoryService();

  List<Category> _categories = [];
  List<Unit> _units = [];
  final Map<String, String> _categoriesMap = {}; // للتوافق مع الكود القديم
  final Map<String, String> _unitsMap = {}; // للتوافق مع الكود القديم
  String? _selectedCategory;
  bool _showLowStock = false;

  // Getters
  List<Product> get products => items; // استخدام items من BaseListPresenter
  List<Category> get categories => _categories;
  List<Unit> get units => _units;
  Map<String, String> get categoriesMap => _categoriesMap;
  Map<String, String> get unitsMap => _unitsMap;
  String? get selectedCategory => _selectedCategory;
  bool get showLowStock => _showLowStock;

  // تنفيذ الدوال المطلوبة من BaseListPresenter
  @override
  Future<List<Product>> loadItemsFromSource() async {
    // استخدام استعلامات SQL مباشرة بدلاً من خدمة المنتجات
    if (_showLowStock) {
      // الحصول على المنتجات منخفضة المخزون
      return await getLowStockProducts();
    } else if (_selectedCategory != null) {
      // الحصول على المنتجات حسب الفئة
      final results = await _db.rawQuery('''
        SELECT * FROM products
        WHERE is_deleted = 0
        AND category_id = ?
        ORDER BY name ASC
      ''', [_selectedCategory]);
      return results.map((item) => Product.fromMap(item)).toList();
    } else {
      // الحصول على جميع المنتجات
      final results = await _db.rawQuery('''
        SELECT * FROM products
        WHERE is_deleted = 0
        AND is_active = 1
        ORDER BY name ASC
      ''');
      return results.map((item) => Product.fromMap(item)).toList();
    }
  }

  @override
  bool matchesSearch(Product item, String query) {
    final lowerQuery = query.toLowerCase();
    return item.name.toLowerCase().contains(lowerQuery) ||
        (item.barcode?.toLowerCase().contains(lowerQuery) ?? false) ||
        (item.sku?.toLowerCase().contains(lowerQuery) ?? false);
  }

  // Initialize
  @override
  Future<void> init() async {
    await Future.wait([
      loadItems(), // استخدام loadItems من BaseListPresenter
      loadCategories(),
      loadUnits(),
    ]);

    // تحميل الفئات والوحدات للتوافق مع الكود القديم
    await _loadCategoriesMap();
    await _loadUnitsMap();
  }

  // Load products (للتوافق مع الكود القديم)
  Future<void> loadProducts() async {
    await loadItems();
  }

  /// تحميل خريطة الفئات (للتوافق مع الكود القديم)
  Future<void> _loadCategoriesMap() async {
    try {
      // استخدام استعلام SQL مباشر للحصول على الفئات
      final categories = await _db.rawQuery('''
        SELECT id, name FROM categories
        WHERE is_deleted = 0
        ORDER BY name ASC
      ''');

      _categoriesMap.clear();
      for (final category in categories) {
        _categoriesMap[category['id'] as String] = category['name'] as String;
      }
    } catch (e) {
      AppLogger.error('ProductPresenter._loadCategoriesMap: $e');
    }
  }

  /// تحميل خريطة الوحدات (للتوافق مع الكود القديم)
  Future<void> _loadUnitsMap() async {
    try {
      // استخدام استعلام SQL مباشر للحصول على الوحدات
      final units = await _db.rawQuery('''
        SELECT id, name FROM units
        WHERE is_deleted = 0
        ORDER BY name ASC
      ''');

      _unitsMap.clear();
      for (final unit in units) {
        _unitsMap[unit['id'] as String] = unit['name'] as String;
      }
    } catch (e) {
      AppLogger.error('ProductPresenter._loadUnitsMap: $e');
    }
  }

  // Get product by ID
  Product? getProductById(String id) {
    try {
      // البحث في قائمة المنتجات المحملة مسبقًا
      try {
        return items.firstWhere((p) => p.id == id);
      } catch (e) {
        // إذا لم يتم العثور على المنتج في القائمة المحملة، نعيد null
        return null;
      }
    } catch (e) {
      setErrorMessage('Failed to get product by ID: $e');
      AppLogger.error('خطأ في الحصول على المنتج بواسطة المعرف: $e');
      return null;
    }
  }

  // Get product by ID (async version)
  Future<Product?> getProductByIdAsync(String id) async {
    try {
      // استخدام استعلام SQL مباشر
      final results = await _db.rawQuery('''
        SELECT * FROM products
        WHERE id = ? AND is_deleted = 0
      ''', [id]);

      if (results.isNotEmpty) {
        return Product.fromMap(results.first);
      }
      return null;
    } catch (e) {
      setErrorMessage('Failed to get product: $e');
      AppLogger.error('خطأ في الحصول على المنتج: $e');
      return null;
    }
  }

  // Add product
  Future<bool> addProduct(Product product) async {
    try {
      setLoading(true);

      // إعداد بيانات المنتج للإدخال
      final now = DateTime.now().toIso8601String();
      final Map<String, dynamic> productData = {
        ...product.toMap(),
        'createdAt': now,
        'updatedAt': now,
      };

      // تنفيذ استعلام SQL لإضافة المنتج
      await _db.rawQuery('''
        INSERT INTO ${DatabaseService.tableProducts} (
          id, name, description, barcode, sku, price, costPrice, quantity,
          categoryId, unitId, imageUrl, isActive, minStock, maxStock,
          supplierId, metadata, hasExpiry, expiryDate, createdAt, updatedAt, isDeleted
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', [
        productData['id'],
        productData['name'],
        productData['description'],
        productData['barcode'],
        productData['sku'],
        productData['price'],
        productData['costPrice'],
        productData['quantity'],
        productData['categoryId'],
        productData['unitId'],
        productData['imageUrl'],
        productData['isActive'] ? 1 : 0,
        productData['minStock'],
        productData['maxStock'] == double.infinity
            ? null
            : productData['maxStock'],
        productData['supplierId'],
        productData['metadata']?.toString(),
        productData['hasExpiry'] ? 1 : 0,
        productData['expiryDate'],
        productData['createdAt'],
        productData['updatedAt'],
        0, // isDeleted
      ]);

      // إعادة تحميل المنتجات
      await loadProducts();
      return true;
    } catch (e) {
      setErrorMessage('Failed to add product: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Update product
  Future<bool> updateProduct(Product product) async {
    try {
      setLoading(true);

      // إعداد بيانات المنتج للتحديث
      final now = DateTime.now().toIso8601String();
      final Map<String, dynamic> productData = {
        ...product.toMap(),
        'updatedAt': now,
      };

      // تنفيذ استعلام SQL لتحديث المنتج
      await _db.rawQuery('''
        UPDATE ${DatabaseService.tableProducts} SET
          name = ?, description = ?, barcode = ?, sku = ?, price = ?,
          costPrice = ?, quantity = ?, categoryId = ?, unitId = ?,
          imageUrl = ?, isActive = ?, minStock = ?, maxStock = ?,
          supplierId = ?, metadata = ?, hasExpiry = ?, expiryDate = ?, updatedAt = ?
        WHERE id = ? AND isDeleted = 0
        ''', [
        productData['name'],
        productData['description'],
        productData['barcode'],
        productData['sku'],
        productData['price'],
        productData['costPrice'],
        productData['quantity'],
        productData['categoryId'],
        productData['unitId'],
        productData['imageUrl'],
        productData['isActive'] ? 1 : 0,
        productData['minStock'],
        productData['maxStock'] == double.infinity
            ? null
            : productData['maxStock'],
        productData['supplierId'],
        productData['metadata']?.toString(),
        productData['hasExpiry'] ? 1 : 0,
        productData['expiryDate'],
        productData['updatedAt'],
        productData['id'], // WHERE id = ?
      ]);

      // إعادة تحميل المنتجات
      await loadProducts();
      return true;
    } catch (e) {
      setErrorMessage('Failed to update product: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Delete product
  Future<bool> deleteProduct(String id) async {
    try {
      setLoading(true);

      // تنفيذ استعلام SQL لحذف المنتج (حذف ناعم)
      final now = DateTime.now().toIso8601String();
      await _db.rawQuery('''
        UPDATE ${DatabaseService.tableProducts} SET
          isDeleted = 1, updatedAt = ?
        WHERE id = ?
        ''', [now, id]);

      // إعادة تحميل المنتجات
      await loadProducts();
      return true;
    } catch (e) {
      setErrorMessage('Failed to delete product: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Update product quantity
  Future<bool> updateProductQuantity(String id, int quantity) async {
    try {
      setLoading(true);

      // تنفيذ استعلام SQL لتحديث كمية المنتج
      final now = DateTime.now().toIso8601String();
      await _db.rawQuery('''
        UPDATE ${DatabaseService.tableProducts} SET
          quantity = ?, updatedAt = ?
        WHERE id = ? AND isDeleted = 0
        ''', [quantity, now, id]);

      // إعادة تحميل المنتجات
      await loadProducts();
      return true;
    } catch (e) {
      setErrorMessage('Failed to update product quantity: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Search products
  Future<void> searchProducts(String query) async {
    setSearchQuery(query); // استخدام setSearchQuery من BaseListPresenter
    await loadItems();
  }

  // Filter by category
  Future<void> filterByCategory(String? category) async {
    _selectedCategory = category;
    await loadItems();
  }

  // Toggle low stock filter
  Future<void> toggleLowStockFilter(bool value) async {
    _showLowStock = value;
    await loadItems();
  }

  // Get low stock products
  Future<List<Product>> getLowStockProducts() async {
    try {
      // تنفيذ استعلام SQL للحصول على المنتجات منخفضة المخزون - تحديث لاستخدام أسماء الحقول الجديدة
      final results = await _db.rawQuery('''
        SELECT * FROM products
        WHERE is_deleted = 0
        AND min_stock IS NOT NULL
        AND quantity <= min_stock
        ORDER BY name ASC
      ''');

      // تحويل النتائج إلى كائنات Product
      return results.map((item) => Product.fromMap(item)).toList();
    } catch (e) {
      setErrorMessage('Failed to get low stock products: $e');
      AppLogger.error('خطأ في الحصول على المنتجات منخفضة المخزون: $e');
      return [];
    }
  }

  /// تحميل الفئات
  Future<void> loadCategories({String? type}) async {
    try {
      // استخدام خدمة الفئات الموحدة
      _categories = await _categoryService.getAllCategories(
        type: type ?? 'product',
      );

      notifyListeners();
    } catch (e) {
      setErrorMessage('Failed to load categories: $e');
      AppLogger.error('خطأ في تحميل الفئات: $e');
    }
  }

  /// تحميل الوحدات
  Future<void> loadUnits({String? type}) async {
    try {
      // بناء استعلام SQL
      String query = 'SELECT * FROM units WHERE is_deleted = 0';
      List<dynamic> args = [];

      // إضافة شرط النوع إذا كان موجودًا
      if (type != null && type.isNotEmpty) {
        query += ' AND unit_type = ?';
        args.add(type);
      } else {
        query += ' AND unit_type = "product"';
      }

      query += ' ORDER BY name';

      // تنفيذ الاستعلام
      final results = await _db.rawQuery(query, args);

      // تحويل النتائج إلى كائنات Unit
      _units = results.map((item) => Unit.fromMap(item)).toList();
      notifyListeners();
    } catch (e) {
      setErrorMessage('Failed to load units: $e');
      AppLogger.error('خطأ في تحميل الوحدات: $e');
    }
  }

  /// الحصول على اسم الفئة
  String getCategoryName(String categoryId) {
    try {
      // أولاً نحاول من قائمة الفئات
      final category = _categories.firstWhere(
        (c) => c.id == categoryId,
        orElse: () => Category(name: 'غير معروف', type: 'product'),
      );

      if (category.name != 'غير معروف') {
        return category.name;
      }

      // ثم نحاول من خريطة الفئات (للتوافق مع الكود القديم)
      return _categoriesMap[categoryId] ?? 'غير معروف';
    } catch (e) {
      return 'غير معروف';
    }
  }

  /// الحصول على اسم الوحدة
  String getUnitName(String unitId) {
    try {
      // أولاً نحاول من قائمة الوحدات
      final unit = _units.firstWhere(
        (u) => u.id == unitId,
        orElse: () => Unit(name: 'غير معروف', symbol: ''),
      );

      if (unit.name != 'غير معروف') {
        return unit.name;
      }

      // ثم نحاول من خريطة الوحدات (للتوافق مع الكود القديم)
      return _unitsMap[unitId] ?? 'غير معروف';
    } catch (e) {
      return 'غير معروف';
    }
  }

  /// الحصول على الوحدات الثانوية للمنتج
  Future<List<ProductUnit>> getProductSecondaryUnits(String productId) async {
    try {
      // تنفيذ استعلام SQL للحصول على الوحدات الثانوية للمنتج
      final results = await _db.rawQuery('''
        SELECT * FROM product_secondary_units
        WHERE productId = ? AND isDeleted = 0
        ORDER BY isDefault DESC, unitName ASC
      ''', [productId]);

      // تحويل النتائج إلى كائنات ProductUnit
      return results.map((item) => ProductUnit.fromMap(item)).toList();
    } catch (e) {
      setErrorMessage('Failed to get product secondary units: $e');
      AppLogger.error('خطأ في الحصول على الوحدات الثانوية للمنتج: $e');
      return [];
    }
  }

  /// البحث عن منتج بواسطة الباركود
  Product? findProductByBarcode(String barcode) {
    try {
      if (barcode.isEmpty) return null;

      return items.firstWhere(
        (product) => product.barcode == barcode,
        orElse: () => Product(
          id: '',
          name: '',
          salePrice: 0,
          purchasePrice: 0,
          categoryId: '',
          unitId: '',
        ),
      );
    } catch (e) {
      setErrorMessage('Failed to find product by barcode: $e');
      AppLogger.error('خطأ في البحث عن منتج بواسطة الباركود: $e');
      return null;
    }
  }
}
