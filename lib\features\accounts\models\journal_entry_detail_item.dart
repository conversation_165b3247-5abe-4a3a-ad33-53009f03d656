/// نموذج بيانات تفاصيل قيد اليومية
class JournalEntryDetailItem {
  final String? id;
  final String? accountId;
  final String? accountCode; // رمز الحساب
  final String? accountName; // اسم الحساب
  final double? debit;
  final double? credit;
  final String? description;
  final String? currencyId;
  final String? currencyCode; // رمز العملة
  final double? exchangeRate;

  JournalEntryDetailItem({
    this.id,
    this.accountId,
    this.accountCode,
    this.accountName,
    this.debit,
    this.credit,
    this.description,
    this.currencyId,
    this.currencyCode,
    this.exchangeRate,
  });

  JournalEntryDetailItem copyWith({
    String? id,
    String? accountId,
    String? accountCode,
    String? accountName,
    double? debit,
    double? credit,
    String? description,
    String? currencyId,
    String? currencyCode,
    double? exchangeRate,
  }) {
    return JournalEntryDetailItem(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      accountCode: accountCode ?? this.accountCode,
      accountName: accountName ?? this.accountName,
      debit: debit ?? this.debit,
      credit: credit ?? this.credit,
      description: description ?? this.description,
      currencyId: currencyId ?? this.currencyId,
      currencyCode: currencyCode ?? this.currencyCode,
      exchangeRate: exchangeRate ?? this.exchangeRate,
    );
  }

  /// تحويل النموذج إلى Map لتخزينه في قاعدة البيانات
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'account_id': accountId,
      'account_code': accountCode,
      'account_name': accountName,
      'description': description,
      'debit': debit,
      'credit': credit,
      'currency_id': currencyId,
      'currency_code': currencyCode,
      'exchange_rate': exchangeRate,
    };
  }

  /// إنشاء نموذج من بيانات JSON
  factory JournalEntryDetailItem.fromJson(Map<String, dynamic> json) {
    return JournalEntryDetailItem(
      id: json['id'],
      accountId: json['account_id'],
      accountCode: json['account_code'],
      accountName: json['account_name'],
      description: json['description'],
      debit: json['debit'] is int
          ? (json['debit'] as int).toDouble()
          : json['debit'],
      credit: json['credit'] is int
          ? (json['credit'] as int).toDouble()
          : json['credit'],
      currencyId: json['currency_id'],
      currencyCode: json['currency_code'],
      exchangeRate: json['exchange_rate'] is int
          ? (json['exchange_rate'] as int).toDouble()
          : json['exchange_rate'],
    );
  }
}