import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج العميل
/// تم توحيده من جميع نماذج العميل في المشروع
class Customer extends BaseModel {
  // معلومات أساسية
  final String name;
  final String? code;
  final String? taxNumber;
  final bool isActive;
  final bool isCompany;

  // معلومات الاتصال
  final String? phone;
  final String? email;
  final String? address;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;
  final String? contactPerson;
  final String? contactPhone;

  // معلومات مالية
  final double balance;
  final double? creditLimit;
  final String? paymentTerms;
  final double discountPercentage;
  final String? accountId;

  // معلومات تصنيفية
  final String? customerType;
  final String? customerGroup;

  // معلومات إضافية
  final String? notes;
  final Map<String, dynamic>? metadata;

  Customer({
    String? id,
    required this.name,
    this.code,
    this.taxNumber,
    this.isActive = true,
    this.isCompany = false,
    this.phone,
    this.email,
    this.address,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.contactPerson,
    this.contactPhone,
    this.balance = 0.0,
    this.creditLimit,
    this.paymentTerms,
    this.discountPercentage = 0.0,
    this.accountId,
    this.customerType,
    this.customerGroup,
    this.notes,
    this.metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا العميل مع استبدال الحقول المحددة بقيم جديدة
  Customer copyWith({
    String? id,
    String? name,
    String? code,
    String? taxNumber,
    bool? isActive,
    bool? isCompany,
    String? phone,
    String? email,
    String? address,
    String? city,
    String? state,
    String? country,
    String? postalCode,
    String? contactPerson,
    String? contactPhone,
    double? balance,
    double? creditLimit,
    String? paymentTerms,
    double? discountPercentage,
    String? accountId,
    String? customerType,
    String? customerGroup,
    String? notes,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      taxNumber: taxNumber ?? this.taxNumber,
      isActive: isActive ?? this.isActive,
      isCompany: isCompany ?? this.isCompany,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      contactPerson: contactPerson ?? this.contactPerson,
      contactPhone: contactPhone ?? this.contactPhone,
      balance: balance ?? this.balance,
      creditLimit: creditLimit ?? this.creditLimit,
      paymentTerms: paymentTerms ?? this.paymentTerms,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      accountId: accountId ?? this.accountId,
      customerType: customerType ?? this.customerType,
      customerGroup: customerGroup ?? this.customerGroup,
      notes: notes ?? this.notes,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل العميل إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'tax_number': taxNumber,
      'is_active': isActive ? 1 : 0,
      'is_company': isCompany ? 1 : 0,
      'phone': phone,
      'email': email,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'postal_code': postalCode,
      'contact_person': contactPerson,
      'contact_phone': contactPhone,
      'balance': balance,
      'credit_limit': creditLimit,
      'payment_terms': paymentTerms,
      'discount_percentage': discountPercentage,
      'account_id': accountId,
      'customer_type': customerType,
      'customer_group': customerGroup,
      'notes': notes,
      'metadata': metadata != null ? _encodeMetadata(metadata!) : null,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء عميل من Map
  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map['id'],
      name: map['name'] ?? '',
      code: map['code'],
      taxNumber: map['tax_number'],
      isActive: map['is_active'] == 1 || map['is_active'] == true,
      isCompany: map['is_company'] == 1 || map['is_company'] == true,
      phone: map['phone'],
      email: map['email'],
      address: map['address'],
      city: map['city'],
      state: map['state'],
      country: map['country'],
      postalCode: map['postal_code'],
      contactPerson: map['contact_person'],
      contactPhone: map['contact_phone'],
      balance: map['balance'] is int
          ? (map['balance'] as int).toDouble()
          : (map['balance'] as double? ?? 0.0),
      creditLimit: map['credit_limit'] != null
          ? (map['credit_limit'] is int
              ? (map['credit_limit'] as int).toDouble()
              : map['credit_limit'] as double)
          : null,
      paymentTerms: map['payment_terms'],
      discountPercentage: map['discount_percentage'] is int
          ? (map['discount_percentage'] as int).toDouble()
          : (map['discount_percentage'] as double? ?? 0.0),
      accountId: map['account_id'],
      customerType: map['customer_type'],
      customerGroup: map['customer_group'],
      notes: map['notes'],
      metadata:
          map['metadata'] != null ? _decodeMetadata(map['metadata']) : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1 || map['is_deleted'] == true,
    );
  }

  /// تشفير البيانات الوصفية
  static String _encodeMetadata(Map<String, dynamic> metadata) {
    try {
      return jsonEncode(metadata);
    } catch (e) {
      return '{}';
    }
  }

  /// فك تشفير البيانات الوصفية
  static Map<String, dynamic> _decodeMetadata(String metadataString) {
    try {
      return jsonDecode(metadataString) as Map<String, dynamic>;
    } catch (e) {
      return {};
    }
  }

  /// تحويل العميل إلى JSON
  String toJson() {
    return jsonEncode(toMap());
  }

  /// إنشاء عميل من JSON
  factory Customer.fromJson(String source) {
    return Customer.fromMap(jsonDecode(source));
  }

  @override
  String toString() {
    return 'Customer(id: $id, name: $name, phone: $phone, email: $email, balance: $balance)';
  }
}
