import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج الدفع الموحد
/// تم توحيده من جميع نماذج الدفع في المشروع
class Payment extends BaseModel {
  // معلومات أساسية
  final String method;
  final double amount;
  final String? referenceNumber;
  
  // معلومات المرتبطة
  final String? invoiceId;
  final String? invoiceNumber;
  final String? customerId;
  final String? customerName;
  final String? supplierId;
  final String? supplierName;
  
  // معلومات الحالة
  final String status; // completed, pending, cancelled, refunded
  final DateTime date;
  
  // معلومات إضافية
  final String? notes;
  final Map<String, dynamic>? metadata;

  Payment({
    String? id,
    required this.method,
    required this.amount,
    this.referenceNumber,
    this.invoiceId,
    this.invoiceNumber,
    this.customerId,
    this.customerName,
    this.supplierId,
    this.supplierName,
    this.status = 'completed',
    DateTime? date,
    this.notes,
    this.metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : 
    date = date ?? DateTime.now(),
    super(
      id: id ?? const Uuid().v4(),
      createdAt: createdAt ?? DateTime.now(),
      createdBy: createdBy,
      updatedAt: updatedAt,
      updatedBy: updatedBy,
      isDeleted: isDeleted,
    );

  /// إنشاء نسخة من هذا الدفع مع استبدال الحقول المحددة بقيم جديدة
  Payment copyWith({
    String? id,
    String? method,
    double? amount,
    String? referenceNumber,
    String? invoiceId,
    String? invoiceNumber,
    String? customerId,
    String? customerName,
    String? supplierId,
    String? supplierName,
    String? status,
    DateTime? date,
    String? notes,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Payment(
      id: id ?? this.id,
      method: method ?? this.method,
      amount: amount ?? this.amount,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      invoiceId: invoiceId ?? this.invoiceId,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      supplierId: supplierId ?? this.supplierId,
      supplierName: supplierName ?? this.supplierName,
      status: status ?? this.status,
      date: date ?? this.date,
      notes: notes ?? this.notes,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل الدفع إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'method': method,
      'amount': amount,
      'reference_number': referenceNumber,
      'invoice_id': invoiceId,
      'invoice_number': invoiceNumber,
      'customer_id': customerId,
      'customer_name': customerName,
      'supplier_id': supplierId,
      'supplier_name': supplierName,
      'status': status,
      'date': date.toIso8601String(),
      'notes': notes,
      'metadata': metadata != null ? jsonEncode(metadata) : null,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء دفع من Map
  factory Payment.fromMap(Map<String, dynamic> map) {
    return Payment(
      id: map['id'],
      method: map['method'] ?? '',
      amount: map['amount'] is int
          ? (map['amount'] as int).toDouble()
          : (map['amount'] as double? ?? 0.0),
      referenceNumber: map['reference_number'],
      invoiceId: map['invoice_id'],
      invoiceNumber: map['invoice_number'],
      customerId: map['customer_id'],
      customerName: map['customer_name'],
      supplierId: map['supplier_id'],
      supplierName: map['supplier_name'],
      status: map['status'] ?? 'completed',
      date: map['date'] != null ? DateTime.parse(map['date']) : DateTime.now(),
      notes: map['notes'],
      metadata: map['metadata'] != null
          ? (map['metadata'] is String
              ? jsonDecode(map['metadata'])
              : map['metadata'] as Map<String, dynamic>)
          : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل الدفع إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء دفع من JSON
  factory Payment.fromJson(String source) =>
      Payment.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'Payment(id: $id, method: $method, amount: $amount, status: $status)';
  }
}

/// أنواع طرق الدفع
class PaymentMethods {
  static const String cash = 'cash';
  static const String creditCard = 'credit_card';
  static const String debitCard = 'debit_card';
  static const String bankTransfer = 'bank_transfer';
  static const String check = 'check';
  static const String credit = 'credit';
  static const String other = 'other';

  /// الحصول على قائمة طرق الدفع
  static List<String> get values => [
        cash,
        creditCard,
        debitCard,
        bankTransfer,
        check,
        credit,
        other,
      ];

  /// الحصول على اسم طريقة الدفع بالعربية
  static String getLocalizedName(String method) {
    switch (method) {
      case cash:
        return 'نقدي';
      case creditCard:
        return 'بطاقة ائتمان';
      case debitCard:
        return 'بطاقة خصم';
      case bankTransfer:
        return 'تحويل بنكي';
      case check:
        return 'شيك';
      case credit:
        return 'آجل';
      case other:
        return 'أخرى';
      default:
        return method;
    }
  }
}
