import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../core/database/database_service.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../models/journal_entry.dart';
import '../models/journal_entry_detail.dart';

/// مقدم القيود المحاسبية
/// يقوم بإدارة عرض وتحميل القيود المحاسبية
class JournalEntryPresenter extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService.instance;
  final List<JournalEntry> _journalEntries = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<JournalEntry> get journalEntries => _journalEntries;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  /// تحميل القيود المحاسبية
  Future<void> loadJournalEntries({
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
    String? status,
    String? type,
  }) async {
    _setLoading(true);
    try {
      final db = await _databaseService.database;

      // بناء استعلام SQL
      String query = '''
        SELECT je.*,
               SUM(jel.debit) as total_debit,
               SUM(jel.credit) as total_credit
        FROM journal_entries je
        LEFT JOIN journal_entry_lines jel ON je.id = jel.journal_entry_id
        WHERE je.is_deleted = 0
      ''';

      final List<dynamic> args = [];

      // إضافة شروط البحث
      if (startDate != null) {
        query += ' AND je.date >= ?';
        args.add(DateFormat('yyyy-MM-dd').format(startDate));
      }

      if (endDate != null) {
        query += ' AND je.date <= ?';
        args.add(DateFormat('yyyy-MM-dd').format(endDate));
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query += ' AND (je.reference LIKE ? OR je.description LIKE ?)';
        args.add('%$searchQuery%');
        args.add('%$searchQuery%');
      }

      if (status != null && status.isNotEmpty) {
        query += ' AND je.status = ?';
        args.add(status);
      }

      if (type != null && type.isNotEmpty) {
        query += ' AND je.type = ?';
        args.add(type);
      }

      // إضافة تجميع وترتيب
      query += ' GROUP BY je.id ORDER BY je.date DESC, je.reference DESC';

      // تنفيذ الاستعلام
      final results = await db.rawQuery(query, args);

      // تحويل النتائج إلى كائنات JournalEntry
      _journalEntries.clear();
      for (final row in results) {
        final entry = JournalEntry.fromJson(row);

        // تحميل تفاصيل القيد
        final detailsQuery = await db.query(
          'journal_entry_lines',
          where: 'journal_entry_id = ? AND is_deleted = 0',
          whereArgs: [entry.id],
        );

        final details = detailsQuery
            .map((detail) => JournalEntryDetail.fromMap(detail))
            .toList();

        _journalEntries.add(entry.copyWith(details: details));
      }

      _setLoading(false);
      notifyListeners();
    } catch (e, stackTrace) {
      _handleError('فشل في تحميل القيود المحاسبية', e, stackTrace);
    }
  }

  /// الحصول على قيد محاسبي بواسطة المعرف
  Future<JournalEntry?> getJournalEntryById(String id) async {
    try {
      final db = await _databaseService.database;

      final results = await db.query(
        'journal_entries',
        where: 'id = ? AND is_deleted = 0',
        whereArgs: [id],
      );

      if (results.isEmpty) {
        return null;
      }

      final entry = JournalEntry.fromJson(results.first);

      // تحميل تفاصيل القيد
      final detailsQuery = await db.query(
        'journal_entry_lines',
        where: 'journal_entry_id = ? AND is_deleted = 0',
        whereArgs: [entry.id],
      );

      final details = detailsQuery
          .map((detail) => JournalEntryDetail.fromMap(detail))
          .toList();

      return entry.copyWith(details: details);
    } catch (e, stackTrace) {
      _handleError('فشل في الحصول على القيد المحاسبي', e, stackTrace);
      return null;
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// معالجة الأخطاء
  void _handleError(String message, dynamic error, StackTrace stackTrace) {
    _errorMessage = '$message: $error';
    _isLoading = false;
    AppLogger.error('$message: $error');
    ErrorTracker.captureError(
      message,
      error: error,
      stackTrace: stackTrace,
    );
    notifyListeners();
  }
}
