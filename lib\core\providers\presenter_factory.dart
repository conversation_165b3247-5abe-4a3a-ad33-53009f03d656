import 'base_presenter.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';

/// مصنع موحد لإنشاء الـ Presenters
/// يوفر طريقة موحدة لإنشاء وإدارة جميع الـ presenters في التطبيق
class PresenterFactory {
  static final Map<Type, BasePresenter Function()> _factories = {};
  static final Map<Type, BasePresenter> _singletons = {};
  static final Map<Type, bool> _isSingleton = {};
  static bool _initialized = false;

  /// تهيئة المصنع مع تسجيل جميع الـ presenters
  static void initialize() {
    if (_initialized) return;

    AppLogger.info('تهيئة PresenterFactory...');

    // تسجيل جميع الـ presenters
    _registerAllPresenters();

    _initialized = true;
    AppLogger.info('تم تهيئة PresenterFactory بنجاح');
  }

  /// تسجيل جميع الـ presenters
  static void _registerAllPresenters() {
    // يمكن إضافة تسجيل الـ presenters هنا في المستقبل
    AppLogger.info('تم تسجيل جميع الـ presenters');
  }

  /// تسجيل مصنع presenter
  static void register<T extends BasePresenter>(
    T Function() factory, {
    bool singleton = false,
  }) {
    _factories[T] = factory;
    _isSingleton[T] = singleton;

    AppLogger.info(
        'تم تسجيل presenter: ${T.toString()}, singleton: $singleton');
  }

  /// إنشاء presenter
  static T create<T extends BasePresenter>() {
    final factory = _factories[T];
    if (factory == null) {
      throw Exception('لم يتم تسجيل مصنع للـ presenter: ${T.toString()}');
    }

    // إذا كان singleton وموجود مسبقاً، أرجعه
    if (_isSingleton[T] == true && _singletons.containsKey(T)) {
      return _singletons[T] as T;
    }

    try {
      final presenter = factory() as T;

      // إذا كان singleton، احفظه
      if (_isSingleton[T] == true) {
        _singletons[T] = presenter;
      }

      AppLogger.info('تم إنشاء presenter: ${T.toString()}');
      return presenter;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إنشاء presenter: ${T.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// الحصول على presenter موجود (للـ singletons فقط)
  static T? get<T extends BasePresenter>() {
    if (_isSingleton[T] != true) {
      return null;
    }
    return _singletons[T] as T?;
  }

  /// التحقق من وجود presenter
  static bool exists<T extends BasePresenter>() {
    return _singletons.containsKey(T);
  }

  /// تنظيف presenter محدد
  static void dispose<T extends BasePresenter>() {
    final presenter = _singletons[T];
    if (presenter != null) {
      presenter.dispose();
      _singletons.remove(T);
      AppLogger.info('تم تنظيف presenter: ${T.toString()}');
    }
  }

  /// تنظيف جميع الـ presenters
  static void disposeAll() {
    for (final presenter in _singletons.values) {
      presenter.dispose();
    }
    _singletons.clear();
    AppLogger.info('تم تنظيف جميع الـ presenters');
  }

  /// إعادة تشغيل presenter
  static T restart<T extends BasePresenter>() {
    dispose<T>();
    return create<T>();
  }

  /// الحصول على إحصائيات المصنع
  static Map<String, dynamic> getStats() {
    return {
      'registered_types': _factories.keys.map((t) => t.toString()).toList(),
      'singleton_types': _isSingleton.entries
          .where((e) => e.value)
          .map((e) => e.key.toString())
          .toList(),
      'active_singletons': _singletons.keys.map((t) => t.toString()).toList(),
      'total_registered': _factories.length,
      'total_singletons': _singletons.length,
    };
  }

  /// تسجيل جميع الـ presenters الافتراضية
  static void registerDefaults() {
    // سيتم إضافة التسجيلات هنا لاحقاً
    AppLogger.info('تم تسجيل الـ presenters الافتراضية');
  }
}

/// Mixin لتسهيل استخدام المصنع
mixin PresenterFactoryMixin {
  /// إنشاء presenter
  T createPresenter<T extends BasePresenter>() {
    return PresenterFactory.create<T>();
  }

  /// الحصول على presenter موجود
  T? getPresenter<T extends BasePresenter>() {
    return PresenterFactory.get<T>();
  }

  /// الحصول على presenter أو إنشاؤه
  T getOrCreatePresenter<T extends BasePresenter>() {
    return getPresenter<T>() ?? createPresenter<T>();
  }
}

/// Decorator لإضافة وظائف إضافية للـ presenters
abstract class PresenterDecorator<T extends BasePresenter>
    extends BasePresenter {
  final T _presenter;

  PresenterDecorator(this._presenter);

  T get presenter => _presenter;

  @override
  Future<void> init() => _presenter.init();

  @override
  Future<void> refresh() => _presenter.refresh();

  @override
  void dispose() {
    _presenter.dispose();
    super.dispose();
  }
}

/// Decorator للتحميل الكسول
class LazyPresenterDecorator<T extends BasePresenter>
    extends PresenterDecorator<T> {
  bool _isInitialized = false;

  LazyPresenterDecorator(super.presenter);

  @override
  Future<void> init() async {
    if (!_isInitialized) {
      await super.init();
      _isInitialized = true;
    }
  }

  bool get isInitialized => _isInitialized;
}

/// Decorator للتخزين المؤقت
class CachedPresenterDecorator<T extends BasePresenter>
    extends PresenterDecorator<T> {
  final Map<String, dynamic> _cache = {};
  final Duration cacheTimeout;

  CachedPresenterDecorator(super.presenter,
      {this.cacheTimeout = const Duration(minutes: 5)});

  /// حفظ في التخزين المؤقت
  void cache(String key, dynamic value) {
    _cache[key] = {
      'value': value,
      'timestamp': DateTime.now(),
    };
  }

  /// الحصول من التخزين المؤقت
  R? getCached<R>(String key) {
    final cached = _cache[key];
    if (cached == null) return null;

    final timestamp = cached['timestamp'] as DateTime;
    if (DateTime.now().difference(timestamp) > cacheTimeout) {
      _cache.remove(key);
      return null;
    }

    return cached['value'] as R?;
  }

  /// مسح التخزين المؤقت
  void clearCache() {
    _cache.clear();
  }
}

/// Decorator لمراقبة الأداء
class PerformancePresenterDecorator<T extends BasePresenter>
    extends PresenterDecorator<T> {
  final Map<String, List<Duration>> _performanceMetrics = {};

  PerformancePresenterDecorator(super.presenter);

  /// تنفيذ عملية مع مراقبة الأداء
  Future<R> measurePerformance<R>(
      String operation, Future<R> Function() action) async {
    final stopwatch = Stopwatch()..start();

    try {
      final result = await action();
      return result;
    } finally {
      stopwatch.stop();
      _recordMetric(operation, stopwatch.elapsed);
    }
  }

  void _recordMetric(String operation, Duration duration) {
    _performanceMetrics.putIfAbsent(operation, () => []).add(duration);

    // الاحتفاظ بآخر 100 قياس فقط
    final metrics = _performanceMetrics[operation]!;
    if (metrics.length > 100) {
      metrics.removeAt(0);
    }
  }

  /// الحصول على إحصائيات الأداء
  Map<String, Map<String, dynamic>> getPerformanceStats() {
    final stats = <String, Map<String, dynamic>>{};

    for (final entry in _performanceMetrics.entries) {
      final operation = entry.key;
      final durations = entry.value;

      if (durations.isNotEmpty) {
        final total = durations.fold<Duration>(Duration.zero, (a, b) => a + b);
        final average =
            Duration(microseconds: total.inMicroseconds ~/ durations.length);
        final min = durations.reduce((a, b) => a < b ? a : b);
        final max = durations.reduce((a, b) => a > b ? a : b);

        stats[operation] = {
          'count': durations.length,
          'total_ms': total.inMilliseconds,
          'average_ms': average.inMilliseconds,
          'min_ms': min.inMilliseconds,
          'max_ms': max.inMilliseconds,
        };
      }
    }

    return stats;
  }
}
