import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/index.dart';
import '../../../core/models/sale.dart';
import '../../../core/models/sale_status.dart';
import '../../../core/models/product.dart';
import '../presenters/sale_presenter.dart';
import '../../products/presenters/product_presenter.dart';
import '../../customers/presenters/customer_presenter.dart';
import 'sale_form_screen.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

class SaleDetailsScreen extends StatefulWidget {
  final String saleId;

  const SaleDetailsScreen({Key? key, required this.saleId}) : super(key: key);

  @override
  State<SaleDetailsScreen> createState() => _SaleDetailsScreenState();
}

class _SaleDetailsScreenState extends State<SaleDetailsScreen> {
  late SalePresenter _salePresenter;
  late ProductPresenter _productPresenter;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _salePresenter =
        AppProviders.getLazyPresenter<SalePresenter>(() => SalePresenter());
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
        () => ProductPresenter());
    // تحميل البيانات باستخدام Future.microtask لتجنب استدعاء setState أثناء البناء
    Future.microtask(_loadSale);
  }

  Future<void> _loadSale() async {
    setState(() => _isLoading = true);
    await _salePresenter.loadSale(widget.saleId);
    await _productPresenter.loadProducts();
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'Sale Details',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _navigateToEdit,
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _confirmDelete,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListenableBuilder(
              listenable: _salePresenter,
              builder: (context, child) {
                final sale = _salePresenter.currentSale;

                if (sale == null) {
                  return const Center(
                    child: Text('Sale not found'),
                  );
                }

                return SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStatusBadge(sale.status),
                      const SizedBox(height: 16),
                      _buildBasicInfo(sale),
                      const SizedBox(height: 24),
                      _buildItemsList(sale),
                      const SizedBox(height: 24),
                      _buildTotals(sale),
                      const SizedBox(height: 24),
                      if (sale.notes != null && sale.notes!.isNotEmpty)
                        _buildNotes(sale.notes!),
                    ],
                  ),
                );
              },
            ),
    );
  }

  Widget _buildStatusBadge(SaleStatus status) {
    final color = _getStatusColor(status);
    final statusText = _getSaleStatusText(status);

    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          statusText,
          style: AppTypography(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfo(Sale sale) {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sale Information',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const Divider(),
          _buildInfoRow('Sale ID', sale.id.substring(0, 8)),
          _buildInfoRow('Date',
              '${sale.date.day}/${sale.date.month}/${sale.date.year} ${sale.date.hour}:${sale.date.minute.toString().padLeft(2, '0')}'),
          if (sale.customerId != null)
            FutureBuilder(
                future: _getCustomerName(sale.customerId!),
                builder: (context, snapshot) {
                  return _buildInfoRow(
                      'Customer', snapshot.data ?? 'Loading...');
                }),
        ],
      ),
    );
  }

  Widget _buildItemsList(Sale sale) {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Items',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              Text(
                '${sale.items.length} items',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          const Divider(),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: sale.items.length,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              final item = sale.items[index];
              final product = _getProductById(item.productId);

              return ListTile(
                title: Text(product?.name ?? 'Unknown Product'),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Quantity: ${item.quantity}'),
                    Text('Price: ${item.price}'),
                    if (item.discount > 0) Text('Discount: ${item.discount}'),
                  ],
                ),
                trailing: Text(
                  '${item.total}',
                  style: const AppTypography(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTotals(Sale sale) {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Totals',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const Divider(),
          _buildTotalRow('Subtotal', sale.subtotal),
          _buildTotalRow('Discount', sale.discount, isNegative: true),
          _buildTotalRow('Tax', sale.tax),
          const Divider(),
          _buildTotalRow('Total', sale.total, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildNotes(String notes) {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notes',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const Divider(),
          Text(notes),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const AppTypography(
              color: AppColors.lightTextSecondary,
            ),
          ),
          Text(
            value,
            style: const AppTypography(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount,
      {bool isTotal = false, bool isNegative = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTypography(
              fontWeight: isTotal ? FontWeight.bold : null,
            ),
          ),
          Text(
            '${isNegative && amount > 0 ? '-' : ''}${amount.toStringAsFixed(2)}',
            style: AppTypography(
              fontWeight: isTotal ? FontWeight.bold : null,
              fontSize: isTotal ? 18 : null,
              color: isNegative && amount > 0 ? AppColors.error : null,
            ),
          ),
        ],
      ),
    );
  }

  Future<String> _getCustomerName(String customerId) async {
    try {
      final customerPresenter =
          AppProviders.getLazyPresenter<CustomerPresenter>(
              () => CustomerPresenter());
      await customerPresenter.loadCustomer(customerId);

      if (!mounted) return 'Customer #$customerId';

      return customerPresenter.selectedCustomer?.name ??
          'Customer #$customerId';
    } catch (e) {
      return 'Customer #$customerId';
    }
  }

  Product? _getProductById(String productId) {
    try {
      return _productPresenter.products.firstWhere((p) => p.id == productId);
    } catch (e) {
      return null;
    }
  }

  Color _getStatusColor(SaleStatus status) {
    switch (status) {
      case SaleStatus.completed:
        return AppColors.success;
      case SaleStatus.draft:
        return AppColors.warning;
      case SaleStatus.pending:
        return AppColors.info;
      case SaleStatus.cancelled:
        return AppColors.error;
      case SaleStatus.returned:
        return AppColors.accent;
    }
  }

  String _getSaleStatusText(SaleStatus status) {
    switch (status) {
      case SaleStatus.completed:
        return 'مكتملة';
      case SaleStatus.draft:
        return 'مسودة';
      case SaleStatus.pending:
        return 'قيد الانتظار';
      case SaleStatus.cancelled:
        return 'ملغاة';
      case SaleStatus.returned:
        return 'مسترجع';
    }
  }

  Future<void> _navigateToEdit() async {
    final sale = _salePresenter.currentSale;
    if (sale == null) return;

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SaleFormScreen(sale: sale),
      ),
    );

    if (result == true) {
      _loadSale();
    }
  }

  Future<void> _confirmDelete() async {
    final sale = _salePresenter.currentSale;
    if (sale == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Sale'),
          content: const Text('Are you sure you want to delete this sale?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );

    if (confirmed == true && mounted) {
      final success = await _salePresenter.deleteSale(sale.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success ? 'Sale deleted successfully' : 'Failed to delete sale',
            ),
            backgroundColor: success ? AppColors.success : AppColors.error,
          ),
        );

        if (success) {
          Navigator.pop(context, true);
        }
      }
    }
  }
}
