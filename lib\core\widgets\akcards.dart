import 'package:flutter/material.dart';
import '../theme/index.dart';
import 'akbuttons.dart'; // استيراد نظام الأزرار
//import 'akbuttons.dart';

// ═══════════════════════════════════════════════════════════════════════════════
// 🎯 نظام البطاقات الموحد والشامل (AK Cards System)
// ═══════════════════════════════════════════════════════════════════════════════

/// **📋 فهرس البطاقات:**
///
/// **🔘 القسم الأول: البطاقات الأساسية** (الأسطر 50-800)
/// ├── 1. AkCard                 - البطاقة الأساسية الموحدة
/// ├── 2. AkInfoCard             - بطاقة المعلومات
/// └── 3. AkActionCard           - بطاقة مع أزرار إجراءات
///
/// **📊 القسم الثاني: البطاقات المتخصصة** (الأسطر 800-1400)
/// ├── 4. AkStatsCard            - بطاقة الإحصائيات
/// ├── 5. AkProductCard          - بطاقة المنتجات
/// └── 6. AkCustomerCard         - بطاقة العملاء
///
/// **🎯 أمثلة الاستخدام:**
/// ```dart
/// // بطاقة أساسية
/// AkCard(
///   child: Text('محتوى البطاقة'),
///   size: AkCardSize.medium,
///   type: AkCardType.elevated,
/// )
///
/// // بطاقة إحصائيات
/// AkStatsCard(
///   title: 'المبيعات',
///   value: '25,480 ر.ي',
///   icon: Icons.shopping_cart,
///   onTap: () {},
/// )
///
/// // بطاقة منتج
/// AkProductCard(
///   product: product,
///   onTap: () {},
///   showActions: true,
/// )
/// ```

// ═══════════════════════════════════════════════════════════════════════════════
// 🔧 الأنواع والثوابت
// ═══════════════════════════════════════════════════════════════════════════════

/// أنواع البطاقات
enum AkCardType {
  /// بطاقة مسطحة (بدون ظل)
  flat,

  /// بطاقة مرفوعة (مع ظل)
  elevated,

  /// بطاقة محددة (مع حدود)
  outlined,

  /// بطاقة متدرجة (مع خلفية متدرجة)
  gradient,
}

/// أحجام البطاقات
enum AkCardSize {
  /// صغير
  small,

  /// متوسط
  medium,

  /// كبير
  large,

  /// كبير جداً
  extraLarge,
}

/// أشكال البطاقات
enum AkCardShape {
  /// مستطيل مع زوايا مدورة
  rounded,

  /// مربع
  square,

  /// دائري الزوايا
  circular,

  /// مستطيل بزوايا حادة
  sharp,
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🔘 القسم الأول: البطاقات الأساسية
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● 1. البطاقة الأساسية الموحدة (AkCard)
// ───────────────────────────────────────────────────────────────────────────────

/// البطاقة الأساسية الموحدة لجميع أنواع البطاقات
///
/// **المميزات:**
/// - دعم جميع أنواع البطاقات (مسطحة، مرفوعة، محددة، متدرجة)
/// - أحجام مختلفة (صغير، متوسط، كبير)
/// - أشكال مختلفة (مستطيل، مربع، دائري)
/// - تأثيرات تفاعلية مع التحميل الكسول
/// - دعم كامل للوضع المظلم/الفاتح
/// - تكامل مع نظام الثيمات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkCard(
///   child: Text('محتوى البطاقة'),
///   type: AkCardType.elevated,
///   size: AkCardSize.medium,
///   shape: AkCardShape.rounded,
///   onTap: () => handleTap(),
/// )
/// ```
class AkCard extends StatefulWidget {
  /// محتوى البطاقة
  final Widget child;

  /// نوع البطاقة
  final AkCardType type;

  /// حجم البطاقة
  final AkCardSize size;

  /// شكل البطاقة
  final AkCardShape shape;

  /// دالة تنفذ عند الضغط
  final VoidCallback? onTap;

  /// لون خلفية مخصص
  final Color? customBackgroundColor;

  /// لون حدود مخصص
  final Color? customBorderColor;

  /// ألوان التدرج المخصصة
  final List<Color>? gradientColors;

  /// هل البطاقة يأخذ العرض الكامل
  final bool isFullWidth;

  /// هل البطاقة يأخذ الارتفاع الكامل
  final bool isFullHeight;

  /// الحشو الداخلي
  final EdgeInsetsGeometry? padding;

  /// الهامش الخارجي
  final EdgeInsetsGeometry? margin;

  /// نص التلميح
  final String? tooltip;

  const AkCard({
    super.key,
    required this.child,
    this.type = AkCardType.elevated,
    this.size = AkCardSize.medium,
    this.shape = AkCardShape.rounded,
    this.onTap,
    this.customBackgroundColor,
    this.customBorderColor,
    this.gradientColors,
    this.isFullWidth = false,
    this.isFullHeight = false,
    this.padding,
    this.margin,
    this.tooltip,
  });

  @override
  State<AkCard> createState() => _AkCardState();
}

class _AkCardState extends State<AkCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// الحصول على الارتفاع حسب النوع والحجم
  double _getElevation() {
    switch (widget.type) {
      case AkCardType.flat:
        return 0;
      case AkCardType.elevated:
        switch (widget.size) {
          case AkCardSize.small:
            return AppDimensions.elevationLow;
          case AkCardSize.medium:
            return AppDimensions.elevationMedium;
          case AkCardSize.large:
            return AppDimensions.elevationHigh;
          case AkCardSize.extraLarge:
            return AppDimensions.elevationExtraHigh;
        }
      case AkCardType.outlined:
        return AppDimensions.elevationLow;
      case AkCardType.gradient:
        return AppDimensions.elevationMedium;
    }
  }

  /// الحصول على نصف قطر الزوايا حسب الشكل والحجم
  double _getBorderRadius() {
    switch (widget.shape) {
      case AkCardShape.rounded:
        switch (widget.size) {
          case AkCardSize.small:
            return AppDimensions.smallRadius;
          case AkCardSize.medium:
            return AppDimensions.mediumRadius;
          case AkCardSize.large:
            return AppDimensions.largeRadius;
          case AkCardSize.extraLarge:
            return AppDimensions.extraLargeRadius;
        }
      case AkCardShape.square:
        return 0;
      case AkCardShape.circular:
        return AppDimensions.circularRadius;
      case AkCardShape.sharp:
        return 0;
    }
  }

  /// الحصول على الحشو الداخلي حسب الحجم
  EdgeInsetsGeometry _getPadding() {
    if (widget.padding != null) return widget.padding!;

    switch (widget.size) {
      case AkCardSize.small:
        return AppDimensions.smallPadding;
      case AkCardSize.medium:
        return AppDimensions.defaultPadding;
      case AkCardSize.large:
        return AppDimensions.largePadding;
      case AkCardSize.extraLarge:
        return AppDimensions.extraLargePadding;
    }
  }

  /// الحصول على الهامش الخارجي حسب الحجم
  EdgeInsetsGeometry _getMargin() {
    if (widget.margin != null) return widget.margin!;

    switch (widget.size) {
      case AkCardSize.small:
        return EdgeInsets.all(AppDimensions.smallMargin);
      case AkCardSize.medium:
        return EdgeInsets.all(AppDimensions.defaultMargin);
      case AkCardSize.large:
        return EdgeInsets.all(AppDimensions.largeMargin);
      case AkCardSize.extraLarge:
        return EdgeInsets.all(AppDimensions.extraLargeMargin);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // تحديد الألوان حسب الوضع
    final backgroundColor = widget.customBackgroundColor ??
        (isDark ? AppColors.darkSurface : AppColors.lightSurface);
    final borderColor = widget.customBorderColor ??
        (isDark ? AppColors.darkBorder : AppColors.lightBorder);

    Widget card = AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.isFullWidth ? double.infinity : null,
            height: widget.isFullHeight ? double.infinity : null,
            margin: _getMargin(),
            decoration: _buildDecoration(backgroundColor, borderColor, isDark),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(_getBorderRadius()),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: widget.onTap,
                  onTapDown: widget.onTap != null
                      ? (_) => _animationController.forward()
                      : null,
                  onTapUp: widget.onTap != null
                      ? (_) => _animationController.reverse()
                      : null,
                  onTapCancel: widget.onTap != null
                      ? () => _animationController.reverse()
                      : null,
                  borderRadius: BorderRadius.circular(_getBorderRadius()),
                  child: Padding(
                    padding: _getPadding(),
                    child: widget.child,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );

    // إضافة التلميح إذا كان متوفراً
    if (widget.tooltip != null) {
      card = Tooltip(
        message: widget.tooltip!,
        child: card,
      );
    }

    return card;
  }

  /// بناء تصميم البطاقة حسب النوع
  BoxDecoration _buildDecoration(
      Color backgroundColor, Color borderColor, bool isDark) {
    switch (widget.type) {
      case AkCardType.flat:
        return BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        );

      case AkCardType.elevated:
        return BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          boxShadow: [
            BoxShadow(
              color: isDark ? AppColors.darkShadow : AppColors.lightShadow,
              blurRadius: _getElevation() * 2,
              spreadRadius: 0,
              offset: Offset(0, _getElevation()),
            ),
          ],
        );

      case AkCardType.outlined:
        return BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          border: Border.all(
            color: borderColor,
            width: 1,
          ),
        );

      case AkCardType.gradient:
        return BoxDecoration(
          gradient: LinearGradient(
            colors: widget.gradientColors ??
                [
                  isDark ? AppColors.darkSurface : AppColors.lightSurface,
                  isDark
                      ? AppColors.darkSurfaceVariant
                      : AppColors.lightSurfaceVariant,
                ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          boxShadow: [
            BoxShadow(
              color: isDark ? AppColors.darkShadow : AppColors.lightShadow,
              blurRadius: _getElevation() * 2,
              spreadRadius: 0,
              offset: Offset(0, _getElevation()),
            ),
          ],
        );
    }
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● 2. بطاقة المعلومات (AkInfoCard)
// ───────────────────────────────────────────────────────────────────────────────

/// بطاقة معلومات موحدة مع تصميم متناسق
///
/// **المميزات:**
/// - تصميم موحد لعرض المعلومات
/// - دعم الأيقونات والعناوين الفرعية
/// - ألوان متنوعة حسب السياق
/// - تأثيرات تفاعلية
/// - دعم كامل للوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkInfoCard(
///   title: 'معلومات المنتج',
///   subtitle: 'تفاصيل إضافية',
///   icon: Icons.info,
///   onTap: () => showDetails(),
/// )
/// ```
class AkInfoCard extends StatelessWidget {
  /// العنوان الرئيسي
  final String title;

  /// العنوان الفرعي (اختياري)
  final String? subtitle;

  /// الوصف (اختياري)
  final String? description;

  /// الأيقونة (اختيارية)
  final IconData? icon;

  /// لون الأيقونة
  final Color? iconColor;

  /// دالة تنفذ عند الضغط
  final VoidCallback? onTap;

  /// نوع البطاقة
  final AkCardType type;

  /// حجم البطاقة
  final AkCardSize size;

  /// شكل البطاقة
  final AkCardShape shape;

  /// لون خلفية مخصص
  final Color? customBackgroundColor;

  /// هل تظهر سهم التنقل
  final bool showArrow;

  const AkInfoCard({
    super.key,
    required this.title,
    this.subtitle,
    this.description,
    this.icon,
    this.iconColor,
    this.onTap,
    this.type = AkCardType.elevated,
    this.size = AkCardSize.medium,
    this.shape = AkCardShape.circular,
    this.customBackgroundColor,
    this.showArrow = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AkCard(
      type: type,
      size: size,
      shape: shape,
      onTap: onTap,
      customBackgroundColor: customBackgroundColor,
      tooltip: description ?? title,
      child: Row(
        children: [
          // الأيقونة
          if (icon != null) ...[
            Container(
              padding: EdgeInsets.all(AppDimensions.smallMargin),
              decoration: BoxDecoration(
                color: (iconColor ?? AppColors.primary).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.smallRadius),
              ),
              child: Icon(
                icon,
                size: _getIconSize(),
                color: iconColor ?? AppColors.primary,
              ),
            ),
            SizedBox(width: AppDimensions.defaultSpacing),
          ],

          // المحتوى النصي
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // العنوان الرئيسي
                Text(
                  title,
                  style: AppTypography.createCustomStyle(
                    fontSize: _getTitleFontSize(),
                    fontWeight: AppTypography.weightMedium,
                    color: isDark
                        ? AppColors.darkTextPrimary
                        : AppColors.lightTextPrimary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                // العنوان الفرعي
                if (subtitle != null) ...[
                  SizedBox(height: AppDimensions.tinySpacing),
                  Text(
                    subtitle!,
                    style: AppTypography.createCustomStyle(
                      fontSize: _getSubtitleFontSize(),
                      fontWeight: AppTypography.weightRegular,
                      color: isDark
                          ? AppColors.darkTextSecondary
                          : AppColors.lightTextSecondary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                // الوصف
                if (description != null) ...[
                  SizedBox(height: AppDimensions.tinySpacing),
                  Text(
                    description!,
                    style: AppTypography.createCustomStyle(
                      fontSize: _getDescriptionFontSize(),
                      fontWeight: AppTypography.weightLight,
                      color: isDark
                          ? AppColors.darkTextHint
                          : AppColors.lightTextHint,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),

          // سهم التنقل
          if (showArrow && onTap != null) ...[
            SizedBox(width: AppDimensions.smallSpacing),
            Icon(
              Icons.arrow_forward_ios,
              size: _getArrowSize(),
              color: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
            ),
          ],
        ],
      ),
    );
  }

  /// الحصول على حجم الأيقونة حسب حجم البطاقة
  double _getIconSize() {
    switch (size) {
      case AkCardSize.small:
        return AppDimensions.smallIconSize;
      case AkCardSize.medium:
        return AppDimensions.mediumIconSize;
      case AkCardSize.large:
        return AppDimensions.largeIconSize;
      case AkCardSize.extraLarge:
        return AppDimensions.extraLargeIconSize;
    }
  }

  /// الحصول على حجم خط العنوان حسب حجم البطاقة
  double _getTitleFontSize() {
    switch (size) {
      case AkCardSize.small:
        return AppTypography.fontSizeSmall;
      case AkCardSize.medium:
        return AppTypography.fontSizeMedium;
      case AkCardSize.large:
        return AppTypography.fontSizeLarge;
      case AkCardSize.extraLarge:
        return AppTypography.fontSizeLarge;
    }
  }

  /// الحصول على حجم خط العنوان الفرعي حسب حجم البطاقة
  double _getSubtitleFontSize() {
    switch (size) {
      case AkCardSize.small:
        return AppTypography.fontSizeSmall;
      case AkCardSize.medium:
        return AppTypography.fontSizeSmall;
      case AkCardSize.large:
        return AppTypography.fontSizeMedium;
      case AkCardSize.extraLarge:
        return AppTypography.fontSizeLarge;
    }
  }

  /// الحصول على حجم خط الوصف حسب حجم البطاقة
  double _getDescriptionFontSize() {
    switch (size) {
      case AkCardSize.small:
        return AppTypography.fontSizeSmall;
      case AkCardSize.medium:
        return AppTypography.fontSizeSmall;
      case AkCardSize.large:
        return AppTypography.fontSizeSmall;
      case AkCardSize.extraLarge:
        return AppTypography.fontSizeMedium;
    }
  }

  /// الحصول على حجم سهم التنقل حسب حجم البطاقة
  double _getArrowSize() {
    switch (size) {
      case AkCardSize.small:
        return AppDimensions.tinyIconSize;
      case AkCardSize.medium:
        return AppDimensions.smallIconSize;
      case AkCardSize.large:
        return AppDimensions.smallIconSize;
      case AkCardSize.extraLarge:
        return AppDimensions.mediumIconSize;
    }
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● 3. بطاقة الإجراءات (AkActionCard)
// ───────────────────────────────────────────────────────────────────────────────

/// بطاقة مع أزرار إجراءات موحدة
///
/// **المميزات:**
/// - تصميم موحد للبطاقات مع أزرار
/// - دعم أزرار متعددة مع تخطيط مرن
/// - تكامل مع نظام الأزرار الموحد (akbuttons.dart)
/// - تأثيرات تفاعلية
/// - دعم كامل للوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkActionCard(
///   title: 'إعدادات الحساب',
///   subtitle: 'إدارة معلومات الحساب',
///   icon: Icons.settings,
///   actions: [
///     AkButtons.edit(onPressed: () => editAccount()),
///     AkButtons.delete(onPressed: () => deleteAccount()),
///   ],
/// )
/// ```
class AkActionCard extends StatelessWidget {
  /// العنوان الرئيسي
  final String title;

  /// العنوان الفرعي (اختياري)
  final String? subtitle;

  /// الوصف (اختياري)
  final String? description;

  /// الأيقونة (اختيارية)
  final IconData? icon;

  /// لون الأيقونة
  final Color? iconColor;

  /// قائمة الأزرار
  final List<Widget> actions;

  /// نوع البطاقة
  final AkCardType type;

  /// حجم البطاقة
  final AkCardSize size;

  /// شكل البطاقة
  final AkCardShape shape;

  /// لون خلفية مخصص
  final Color? customBackgroundColor;

  /// اتجاه ترتيب الأزرار
  final Axis actionsDirection;

  /// محاذاة الأزرار
  final MainAxisAlignment actionsAlignment;

  const AkActionCard({
    super.key,
    required this.title,
    required this.actions,
    this.subtitle,
    this.description,
    this.icon,
    this.iconColor,
    this.type = AkCardType.elevated,
    this.size = AkCardSize.medium,
    this.shape = AkCardShape.rounded,
    this.customBackgroundColor,
    this.actionsDirection = Axis.horizontal,
    this.actionsAlignment = MainAxisAlignment.end,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AkCard(
      type: type,
      size: size,
      shape: shape,
      customBackgroundColor: customBackgroundColor,
      tooltip: description ?? title,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // المحتوى الرئيسي
          Row(
            children: [
              // الأيقونة
              if (icon != null) ...[
                Container(
                  padding: EdgeInsets.all(AppDimensions.smallMargin),
                  decoration: BoxDecoration(
                    color:
                        (iconColor ?? AppColors.primary).withValues(alpha: 0.1),
                    borderRadius:
                        BorderRadius.circular(AppDimensions.smallRadius),
                  ),
                  child: Icon(
                    icon,
                    size: _getIconSize(),
                    color: iconColor ?? AppColors.primary,
                  ),
                ),
                SizedBox(width: AppDimensions.defaultSpacing),
              ],

              // المحتوى النصي
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // العنوان الرئيسي
                    Text(
                      title,
                      style: AppTypography.createCustomStyle(
                        fontSize: _getTitleFontSize(),
                        fontWeight: AppTypography.weightMedium,
                        color: isDark
                            ? AppColors.darkTextPrimary
                            : AppColors.lightTextPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // العنوان الفرعي
                    if (subtitle != null) ...[
                      SizedBox(height: AppDimensions.tinySpacing),
                      Text(
                        subtitle!,
                        style: AppTypography.createCustomStyle(
                          fontSize: _getSubtitleFontSize(),
                          fontWeight: AppTypography.weightRegular,
                          color: isDark
                              ? AppColors.darkTextSecondary
                              : AppColors.lightTextSecondary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],

                    // الوصف
                    if (description != null) ...[
                      SizedBox(height: AppDimensions.tinySpacing),
                      Text(
                        description!,
                        style: AppTypography.createCustomStyle(
                          fontSize: _getDescriptionFontSize(),
                          fontWeight: AppTypography.weightLight,
                          color: isDark
                              ? AppColors.darkTextHint
                              : AppColors.lightTextHint,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          // الأزرار
          if (actions.isNotEmpty) ...[
            SizedBox(height: AppDimensions.defaultSpacing),
            _buildActions(),
          ],
        ],
      ),
    );
  }

  /// بناء قسم الأزرار
  Widget _buildActions() {
    if (actionsDirection == Axis.horizontal) {
      return Row(
        mainAxisAlignment: actionsAlignment,
        children: actions
            .map((action) => Padding(
                  padding: EdgeInsets.only(left: AppDimensions.smallSpacing),
                  child: action,
                ))
            .toList(),
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: actions
            .map((action) => Padding(
                  padding: EdgeInsets.only(bottom: AppDimensions.smallSpacing),
                  child: action,
                ))
            .toList(),
      );
    }
  }

  /// الحصول على حجم الأيقونة حسب حجم البطاقة
  double _getIconSize() {
    switch (size) {
      case AkCardSize.small:
        return AppDimensions.smallIconSize;
      case AkCardSize.medium:
        return AppDimensions.mediumIconSize;
      case AkCardSize.large:
        return AppDimensions.largeIconSize;
      case AkCardSize.extraLarge:
        return AppDimensions.extraLargeIconSize;
    }
  }

  /// الحصول على حجم خط العنوان حسب حجم البطاقة
  double _getTitleFontSize() {
    switch (size) {
      case AkCardSize.small:
        return AppTypography.fontSizeSmall;
      case AkCardSize.medium:
        return AppTypography.fontSizeMedium;
      case AkCardSize.large:
        return AppTypography.fontSizeLarge;
      case AkCardSize.extraLarge:
        return AppTypography.fontSizeLarge;
    }
  }

  /// الحصول على حجم خط العنوان الفرعي حسب حجم البطاقة
  double _getSubtitleFontSize() {
    switch (size) {
      case AkCardSize.small:
        return AppTypography.fontSizeSmall;
      case AkCardSize.medium:
        return AppTypography.fontSizeSmall;
      case AkCardSize.large:
        return AppTypography.fontSizeMedium;
      case AkCardSize.extraLarge:
        return AppTypography.fontSizeLarge;
    }
  }

  /// الحصول على حجم خط الوصف حسب حجم البطاقة
  double _getDescriptionFontSize() {
    switch (size) {
      case AkCardSize.small:
        return AppTypography.fontSizeSmall;
      case AkCardSize.medium:
        return AppTypography.fontSizeSmall;
      case AkCardSize.large:
        return AppTypography.fontSizeSmall;
      case AkCardSize.extraLarge:
        return AppTypography.fontSizeMedium;
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📊 القسم الثاني: البطاقات المتخصصة
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● 4. بطاقة الإحصائيات (AkStatsCard)
// ───────────────────────────────────────────────────────────────────────────────

/// بطاقة إحصائيات موحدة مع تصميم عصري
///
/// **المميزات:**
/// - تصميم موحد لعرض الإحصائيات
/// - دعم مؤشرات الاتجاه (صعود/هبوط)
/// - ألوان متنوعة حسب السياق
/// - تأثيرات تفاعلية متقدمة
/// - دعم كامل للوضع المظلم/الفاتح
/// - تحميل كسول للعناصر الثقيلة
///
/// **مثال الاستخدام:**
/// ```dart
/// AkStatsCard(
///   title: 'المبيعات',
///   value: '25,480 ر.ي',
///   subtitle: 'هذا الشهر',
///   icon: Icons.shopping_cart,
///   showTrend: true,
///   trendValue: 12.5,
///   isPositiveTrend: true,
///   onTap: () => showSalesDetails(),
/// )
/// ```
class AkStatsCard extends StatefulWidget {
  /// العنوان الرئيسي
  final String title;

  /// القيمة الإحصائية
  final String value;

  /// العنوان الفرعي (اختياري)
  final String? subtitle;

  /// الأيقونة
  final IconData icon;

  /// لون الأيقونة والبطاقة
  final Color? color;

  /// دالة تنفذ عند الضغط
  final VoidCallback? onTap;

  /// هل تظهر مؤشر الاتجاه
  final bool showTrend;

  /// قيمة الاتجاه (نسبة مئوية)
  final double? trendValue;

  /// هل الاتجاه إيجابي
  final bool isPositiveTrend;

  /// نوع البطاقة
  final AkCardType type;

  /// حجم البطاقة
  final AkCardSize size;

  /// شكل البطاقة
  final AkCardShape shape;

  /// لون خلفية مخصص
  final Color? customBackgroundColor;

  const AkStatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.subtitle,
    this.color,
    this.onTap,
    this.showTrend = false,
    this.trendValue,
    this.isPositiveTrend = true,
    this.type = AkCardType.elevated,
    this.size = AkCardSize.medium,
    this.shape = AkCardShape.rounded,
    this.customBackgroundColor,
  });

  @override
  State<AkStatsCard> createState() => _AkStatsCardState();
}

class _AkStatsCardState extends State<AkStatsCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // بدء الرسوم المتحركة
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final cardColor = widget.color ?? AppColors.primary;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: AkCard(
              type: widget.type,
              size: widget.size,
              shape: widget.shape,
              onTap: widget.onTap,
              customBackgroundColor: widget.customBackgroundColor,
              tooltip: widget.subtitle ?? widget.title,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // الصف العلوي - الأيقونة والعنوان
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // العنوان
                      Expanded(
                        child: Text(
                          widget.title,
                          style: AppTypography.createCustomStyle(
                            fontSize: _getTitleFontSize(),
                            fontWeight: AppTypography.weightMedium,
                            color: isDark
                                ? AppColors.darkTextSecondary
                                : AppColors.lightTextSecondary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      // الأيقونة مع خلفية ملونة
                      Container(
                        padding: EdgeInsets.all(_getIconPadding()),
                        decoration: BoxDecoration(
                          color: cardColor.withValues(alpha: 0.1),
                          borderRadius:
                              BorderRadius.circular(AppDimensions.smallRadius),
                        ),
                        child: Icon(
                          widget.icon,
                          size: _getIconSize(),
                          color: cardColor,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: AppDimensions.defaultSpacing),

                  // القيمة الإحصائية
                  Text(
                    widget.value,
                    style: AppTypography.createCustomStyle(
                      fontSize: _getValueFontSize(),
                      fontWeight: AppTypography.weightBold,
                      color: isDark
                          ? AppColors.darkTextPrimary
                          : AppColors.lightTextPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  // العنوان الفرعي ومؤشر الاتجاه
                  if (widget.subtitle != null || widget.showTrend) ...[
                    SizedBox(height: AppDimensions.smallSpacing),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // العنوان الفرعي
                        if (widget.subtitle != null)
                          Expanded(
                            child: Text(
                              widget.subtitle!,
                              style: AppTypography.createCustomStyle(
                                fontSize: _getSubtitleFontSize(),
                                fontWeight: AppTypography.weightRegular,
                                color: isDark
                                    ? AppColors.darkTextHint
                                    : AppColors.lightTextHint,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),

                        // مؤشر الاتجاه
                        if (widget.showTrend && widget.trendValue != null)
                          _buildTrendIndicator(isDark),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء مؤشر الاتجاه مع التحميل الكسول
  Widget _buildTrendIndicator(bool isDark) {
    final trendColor =
        widget.isPositiveTrend ? AppColors.success : AppColors.error;
    final trendIcon =
        widget.isPositiveTrend ? Icons.trending_up : Icons.trending_down;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppDimensions.smallSpacing,
        vertical: AppDimensions.tinySpacing,
      ),
      decoration: BoxDecoration(
        color: trendColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.tinyRadius),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            trendIcon,
            size: _getTrendIconSize(),
            color: trendColor,
          ),
          SizedBox(width: AppDimensions.tinySpacing),
          Text(
            '${widget.trendValue!.abs().toStringAsFixed(1)}%',
            style: AppTypography.createCustomStyle(
              fontSize: _getTrendFontSize(),
              fontWeight: AppTypography.weightMedium,
              color: trendColor,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على حجم خط العنوان حسب حجم البطاقة
  double _getTitleFontSize() {
    switch (widget.size) {
      case AkCardSize.small:
        return AppTypography.fontSizeSmall;
      case AkCardSize.medium:
        return AppTypography.fontSizeSmall;
      case AkCardSize.large:
        return AppTypography.fontSizeMedium;
      case AkCardSize.extraLarge:
        return AppTypography.fontSizeMedium;
    }
  }

  /// الحصول على حجم خط القيمة حسب حجم البطاقة
  double _getValueFontSize() {
    switch (widget.size) {
      case AkCardSize.small:
        return AppTypography.fontSizeMedium;
      case AkCardSize.medium:
        return AppTypography.fontSizeLarge;
      case AkCardSize.large:
        return AppTypography.fontSizeLarge;
      case AkCardSize.extraLarge:
        return AppTypography.fontSizeLarge;
    }
  }

  /// الحصول على حجم خط العنوان الفرعي حسب حجم البطاقة
  double _getSubtitleFontSize() {
    switch (widget.size) {
      case AkCardSize.small:
        return AppTypography.fontSizeSmall;
      case AkCardSize.medium:
        return AppTypography.fontSizeSmall;
      case AkCardSize.large:
        return AppTypography.fontSizeSmall;
      case AkCardSize.extraLarge:
        return AppTypography.fontSizeMedium;
    }
  }

  /// الحصول على حجم الأيقونة حسب حجم البطاقة
  double _getIconSize() {
    switch (widget.size) {
      case AkCardSize.small:
        return AppDimensions.smallIconSize;
      case AkCardSize.medium:
        return AppDimensions.mediumIconSize;
      case AkCardSize.large:
        return AppDimensions.largeIconSize;
      case AkCardSize.extraLarge:
        return AppDimensions.extraLargeIconSize;
    }
  }

  /// الحصول على حشو الأيقونة حسب حجم البطاقة
  double _getIconPadding() {
    switch (widget.size) {
      case AkCardSize.small:
        return AppDimensions.smallMargin;
      case AkCardSize.medium:
        return AppDimensions.smallMargin;
      case AkCardSize.large:
        return AppDimensions.defaultMargin;
      case AkCardSize.extraLarge:
        return AppDimensions.defaultMargin;
    }
  }

  /// الحصول على حجم أيقونة الاتجاه حسب حجم البطاقة
  double _getTrendIconSize() {
    switch (widget.size) {
      case AkCardSize.small:
        return AppDimensions.tinyIconSize;
      case AkCardSize.medium:
        return AppDimensions.smallIconSize;
      case AkCardSize.large:
        return AppDimensions.smallIconSize;
      case AkCardSize.extraLarge:
        return AppDimensions.mediumIconSize;
    }
  }

  /// الحصول على حجم خط الاتجاه حسب حجم البطاقة
  double _getTrendFontSize() {
    switch (widget.size) {
      case AkCardSize.small:
        return AppTypography.fontSizeSmall;
      case AkCardSize.medium:
        return AppTypography.fontSizeSmall;
      case AkCardSize.large:
        return AppTypography.fontSizeSmall;
      case AkCardSize.extraLarge:
        return AppTypography.fontSizeSmall;
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🛠️ الدوال المساعدة والمُنشئات السريعة
// ═══════════════════════════════════════════════════════════════════════════════

/// مجموعة من الدوال المساعدة لإنشاء البطاقات بسرعة
class AkCards {
  AkCards._(); // منع إنشاء كائنات من هذه الفئة

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال إنشاء سريعة للبطاقات الشائعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// إنشاء بطاقة أساسية سريعة
  static Widget basic({
    required Widget child,
    VoidCallback? onTap,
    AkCardType type = AkCardType.elevated,
    AkCardSize size = AkCardSize.medium,
    AkCardShape shape = AkCardShape.rounded,
    Color? customBackgroundColor,
    String? tooltip,
  }) {
    return AkCard(
      type: type,
      size: size,
      shape: shape,
      onTap: onTap,
      customBackgroundColor: customBackgroundColor,
      tooltip: tooltip,
      child: child,
    );
  }

  /// إنشاء بطاقة معلومات سريعة
  static Widget info({
    required String title,
    String? subtitle,
    String? description,
    IconData? icon,
    Color? iconColor,
    VoidCallback? onTap,
    AkCardSize size = AkCardSize.medium,
    bool showArrow = true,
  }) {
    return AkInfoCard(
      title: title,
      subtitle: subtitle,
      description: description,
      icon: icon,
      iconColor: iconColor,
      onTap: onTap,
      size: size,
      showArrow: showArrow,
    );
  }

  /// إنشاء بطاقة إحصائيات سريعة
  static Widget stats({
    required String title,
    required String value,
    required IconData icon,
    String? subtitle,
    Color? color,
    VoidCallback? onTap,
    bool showTrend = false,
    double? trendValue,
    bool isPositiveTrend = true,
    AkCardSize size = AkCardSize.medium,
  }) {
    return AkStatsCard(
      title: title,
      value: value,
      icon: icon,
      subtitle: subtitle,
      color: color,
      onTap: onTap,
      showTrend: showTrend,
      trendValue: trendValue,
      isPositiveTrend: isPositiveTrend,
      size: size,
    );
  }

  /// إنشاء بطاقة إجراءات سريعة
  static Widget action({
    required String title,
    required List<Widget> actions,
    String? subtitle,
    String? description,
    IconData? icon,
    Color? iconColor,
    AkCardSize size = AkCardSize.medium,
    Axis actionsDirection = Axis.horizontal,
    MainAxisAlignment actionsAlignment = MainAxisAlignment.end,
  }) {
    return AkActionCard(
      title: title,
      actions: actions,
      subtitle: subtitle,
      description: description,
      icon: icon,
      iconColor: iconColor,
      size: size,
      actionsDirection: actionsDirection,
      actionsAlignment: actionsAlignment,
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال إنشاء بطاقات متخصصة للمشروع
  // ───────────────────────────────────────────────────────────────────────────────

  /// إنشاء بطاقة مبيعات سريعة
  static Widget sales({
    required String value,
    String subtitle = 'إجمالي المبيعات',
    VoidCallback? onTap,
    bool showTrend = true,
    double? trendValue,
    bool isPositiveTrend = true,
    AkCardSize size = AkCardSize.medium,
  }) {
    return AkStatsCard(
      title: 'المبيعات',
      value: value,
      subtitle: subtitle,
      icon: Icons.shopping_cart_outlined,
      color: AppColors.success,
      onTap: onTap,
      showTrend: showTrend,
      trendValue: trendValue,
      isPositiveTrend: isPositiveTrend,
      size: size,
    );
  }

  /// إنشاء بطاقة مشتريات سريعة
  static Widget purchases({
    required String value,
    String subtitle = 'إجمالي المشتريات',
    VoidCallback? onTap,
    bool showTrend = true,
    double? trendValue,
    bool isPositiveTrend = true,
    AkCardSize size = AkCardSize.medium,
  }) {
    return AkStatsCard(
      title: 'المشتريات',
      value: value,
      subtitle: subtitle,
      icon: Icons.shopping_bag_outlined,
      color: AppColors.warning,
      onTap: onTap,
      showTrend: showTrend,
      trendValue: trendValue,
      isPositiveTrend: isPositiveTrend,
      size: size,
    );
  }

  /// إنشاء بطاقة أرباح سريعة
  static Widget profit({
    required String value,
    String subtitle = 'صافي الأرباح',
    VoidCallback? onTap,
    bool showTrend = true,
    double? trendValue,
    bool isPositiveTrend = true,
    AkCardSize size = AkCardSize.medium,
  }) {
    return AkStatsCard(
      title: 'الأرباح',
      value: value,
      subtitle: subtitle,
      icon: Icons.trending_up_outlined,
      color: isPositiveTrend ? AppColors.success : AppColors.error,
      onTap: onTap,
      showTrend: showTrend,
      trendValue: trendValue,
      isPositiveTrend: isPositiveTrend,
      size: size,
    );
  }

  /// إنشاء بطاقة عملاء سريعة
  static Widget customers({
    required String value,
    String subtitle = 'إجمالي العملاء',
    VoidCallback? onTap,
    bool showTrend = false,
    double? trendValue,
    bool isPositiveTrend = true,
    AkCardSize size = AkCardSize.medium,
  }) {
    return AkStatsCard(
      title: 'العملاء',
      value: value,
      subtitle: subtitle,
      icon: Icons.people_outlined,
      color: AppColors.info,
      onTap: onTap,
      showTrend: showTrend,
      trendValue: trendValue,
      isPositiveTrend: isPositiveTrend,
      size: size,
    );
  }

  /// إنشاء بطاقة منتجات سريعة
  static Widget products({
    required String value,
    String subtitle = 'إجمالي المنتجات',
    VoidCallback? onTap,
    bool showTrend = false,
    double? trendValue,
    bool isPositiveTrend = true,
    AkCardSize size = AkCardSize.medium,
  }) {
    return AkStatsCard(
      title: 'المنتجات',
      value: value,
      subtitle: subtitle,
      icon: Icons.inventory_2_outlined,
      color: AppColors.primary,
      onTap: onTap,
      showTrend: showTrend,
      trendValue: trendValue,
      isPositiveTrend: isPositiveTrend,
      size: size,
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● بطاقات متقدمة للمشروع التجاري
  // ───────────────────────────────────────────────────────────────────────────────

  /// إنشاء بطاقة فاتورة سريعة
  static Widget invoice({
    required String invoiceNumber,
    required String customerName,
    required String amount,
    required String date,
    VoidCallback? onTap,
    VoidCallback? onPrint,
    VoidCallback? onShare,
    bool isPaid = false,
    AkCardSize size = AkCardSize.medium,
  }) {
    return AkActionCard(
      title: 'فاتورة #$invoiceNumber',
      subtitle: customerName,
      description: 'التاريخ: $date\nالمبلغ: $amount',
      icon: Icons.receipt_long,
      iconColor: isPaid ? AppColors.success : AppColors.warning,
      size: size,
      actions: [
        if (onPrint != null)
          AkIconButton(
            icon: Icons.print,
            onPressed: onPrint,
            type: AkButtonType.info,
            size: AkButtonSize.small,
            showBackground: false,
            tooltip: 'طباعة',
          ),
        if (onShare != null)
          AkIconButton(
            icon: Icons.share,
            onPressed: onShare,
            type: AkButtonType.primary,
            size: AkButtonSize.small,
            showBackground: false,
            tooltip: 'مشاركة',
          ),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: AppDimensions.smallMargin,
            vertical: AppDimensions.tinyMargin,
          ),
          decoration: BoxDecoration(
            color: isPaid ? AppColors.success : AppColors.warning,
            borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          ),
          child: Text(
            isPaid ? 'مدفوعة' : 'غير مدفوعة',
            style: AppTypography.createCustomStyle(
              fontSize: AppTypography.fontSizeSmall,
              color: AppColors.lightBackground,
              fontWeight: AppTypography.weightMedium,
            ),
          ),
        ),
      ],
    );
  }

  /// إنشاء بطاقة منتج سريعة
  static Widget product({
    required String name,
    required String price,
    required String stock,
    String? category,
    String? imageUrl,
    VoidCallback? onTap,
    VoidCallback? onEdit,
    VoidCallback? onAddToCart,
    bool isLowStock = false,
    AkCardSize size = AkCardSize.medium,
  }) {
    return AkActionCard(
      title: name,
      subtitle: category,
      description: 'السعر: $price\nالمخزون: $stock',
      icon: Icons.inventory_2,
      iconColor: isLowStock ? AppColors.error : AppColors.primary,
      size: size,
      actions: [
        if (onEdit != null)
          AkIconButton(
            icon: Icons.edit,
            onPressed: onEdit,
            type: AkButtonType.info,
            size: AkButtonSize.small,
            showBackground: false,
            tooltip: 'تعديل',
          ),
        if (onAddToCart != null)
          AkIconButton(
            icon: Icons.add_shopping_cart,
            onPressed: onAddToCart,
            type: AkButtonType.success,
            size: AkButtonSize.small,
            showBackground: false,
            tooltip: 'إضافة للسلة',
          ),
        if (isLowStock)
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.smallMargin,
              vertical: AppDimensions.tinyMargin,
            ),
            decoration: BoxDecoration(
              color: AppColors.error,
              borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
            ),
            child: Text(
              'مخزون منخفض',
              style: AppTypography.createCustomStyle(
                fontSize: AppTypography.fontSizeSmall,
                color: AppColors.lightBackground,
                fontWeight: AppTypography.weightMedium,
              ),
            ),
          ),
      ],
    );
  }

  /// إنشاء بطاقة عميل سريعة
  static Widget customer({
    required String name,
    required String phone,
    String? email,
    String? address,
    String? totalPurchases,
    VoidCallback? onTap,
    VoidCallback? onCall,
    VoidCallback? onHistory,
    bool isVip = false,
    AkCardSize size = AkCardSize.medium,
  }) {
    return AkActionCard(
      title: name,
      subtitle: phone,
      description: email != null ? 'البريد: $email' : null,
      icon: Icons.person,
      iconColor: isVip ? AppColors.warning : AppColors.info,
      size: size,
      actions: [
        if (onCall != null)
          AkIconButton(
            icon: Icons.phone,
            onPressed: onCall,
            type: AkButtonType.success,
            size: AkButtonSize.small,
            showBackground: false,
            tooltip: 'اتصال',
          ),
        if (onHistory != null)
          AkIconButton(
            icon: Icons.history,
            onPressed: onHistory,
            type: AkButtonType.info,
            size: AkButtonSize.small,
            showBackground: false,
            tooltip: 'التاريخ',
          ),
        if (isVip)
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.smallMargin,
              vertical: AppDimensions.tinyMargin,
            ),
            decoration: BoxDecoration(
              color: AppColors.warning,
              borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
            ),
            child: Text(
              'VIP',
              style: AppTypography.createCustomStyle(
                fontSize: AppTypography.fontSizeSmall,
                color: AppColors.lightBackground,
                fontWeight: AppTypography.weightBold,
              ),
            ),
          ),
      ],
    );
  }

  /// إنشاء بطاقة تقرير سريعة
  static Widget report({
    required String title,
    required String period,
    required String summary,
    VoidCallback? onTap,
    VoidCallback? onExport,
    VoidCallback? onShare,
    VoidCallback? onPrint,
    IconData icon = Icons.analytics,
    Color? color,
    AkCardSize size = AkCardSize.medium,
  }) {
    return AkActionCard(
      title: title,
      subtitle: period,
      description: summary,
      icon: icon,
      iconColor: color ?? AppColors.info,
      size: size,
      actions: [
        if (onExport != null)
          AkIconButton(
            icon: Icons.file_download,
            onPressed: onExport,
            type: AkButtonType.info,
            size: AkButtonSize.small,
            showBackground: false,
            tooltip: 'تصدير',
          ),
        if (onShare != null)
          AkIconButton(
            icon: Icons.share,
            onPressed: onShare,
            type: AkButtonType.primary,
            size: AkButtonSize.small,
            showBackground: false,
            tooltip: 'مشاركة',
          ),
        if (onPrint != null)
          AkIconButton(
            icon: Icons.print,
            onPressed: onPrint,
            type: AkButtonType.secondary,
            size: AkButtonSize.small,
            showBackground: false,
            tooltip: 'طباعة',
          ),
      ],
    );
  }

  /// إنشاء بطاقة تنبيه سريعة
  static Widget alert({
    required String title,
    required String message,
    VoidCallback? onTap,
    VoidCallback? onDismiss,
    IconData icon = Icons.warning,
    Color? color,
    bool isUrgent = false,
    AkCardSize size = AkCardSize.medium,
  }) {
    return AkActionCard(
      title: title,
      description: message,
      icon: icon,
      iconColor: color ?? (isUrgent ? AppColors.error : AppColors.warning),
      size: size,
      actions: [
        if (onDismiss != null)
          AkIconButton(
            icon: Icons.close,
            onPressed: onDismiss,
            type: AkButtonType.secondary,
            size: AkButtonSize.small,
            showBackground: false,
            tooltip: 'إغلاق',
          ),
        if (isUrgent)
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.smallMargin,
              vertical: AppDimensions.tinyMargin,
            ),
            decoration: BoxDecoration(
              color: AppColors.error,
              borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
            ),
            child: Text(
              'عاجل',
              style: AppTypography.createCustomStyle(
                fontSize: AppTypography.fontSizeSmall,
                color: AppColors.lightBackground,
                fontWeight: AppTypography.weightBold,
              ),
            ),
          ),
      ],
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● مجموعات بطاقات متخصصة
  // ───────────────────────────────────────────────────────────────────────────────

  /// إنشاء مجموعة بطاقات لوحة التحكم
  static Widget dashboardStats({
    required String salesValue,
    required String purchasesValue,
    required String profitValue,
    required String customersValue,
    VoidCallback? onSalesTap,
    VoidCallback? onPurchasesTap,
    VoidCallback? onProfitTap,
    VoidCallback? onCustomersTap,
    bool showTrends = true,
    double? salesTrend,
    double? purchasesTrend,
    double? profitTrend,
    double? customersTrend,
  }) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: AppDimensions.smallMargin,
      mainAxisSpacing: AppDimensions.smallMargin,
      children: [
        sales(
          value: salesValue,
          onTap: onSalesTap,
          showTrend: showTrends,
          trendValue: salesTrend,
        ),
        purchases(
          value: purchasesValue,
          onTap: onPurchasesTap,
          showTrend: showTrends,
          trendValue: purchasesTrend,
        ),
        profit(
          value: profitValue,
          onTap: onProfitTap,
          showTrend: showTrends,
          trendValue: profitTrend,
          isPositiveTrend: (profitTrend ?? 0) >= 0,
        ),
        customers(
          value: customersValue,
          onTap: onCustomersTap,
          showTrend: showTrends,
          trendValue: customersTrend,
        ),
      ],
    );
  }

  /// إنشاء قائمة بطاقات الفواتير الأخيرة
  static Widget recentInvoices({
    required List<Map<String, dynamic>> invoices,
    VoidCallback? onViewAll,
    Function(String)? onInvoiceTap,
    Function(String)? onPrintInvoice,
    Function(String)? onShareInvoice,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الفواتير الأخيرة',
              style: AppTypography.createCustomStyle(
                fontSize: AppTypography.fontSizeLarge,
                fontWeight: AppTypography.weightBold,
              ),
            ),
            if (onViewAll != null)
              AkTextButton(
                text: 'عرض الكل',
                onPressed: onViewAll,
                type: AkButtonType.primary,
                size: AkButtonSize.small,
              ),
          ],
        ),
        SizedBox(height: AppDimensions.smallMargin),
        ...invoices.take(5).map((invoice) => Padding(
              padding: EdgeInsets.only(bottom: AppDimensions.smallMargin),
              child: AkCards.invoice(
                invoiceNumber: invoice['number'] ?? '',
                customerName: invoice['customer'] ?? '',
                amount: invoice['amount'] ?? '',
                date: invoice['date'] ?? '',
                isPaid: invoice['isPaid'] ?? false,
                onTap: onInvoiceTap != null
                    ? () => onInvoiceTap(invoice['id'] ?? '')
                    : null,
                onPrint: onPrintInvoice != null
                    ? () => onPrintInvoice(invoice['id'] ?? '')
                    : null,
                onShare: onShareInvoice != null
                    ? () => onShareInvoice(invoice['id'] ?? '')
                    : null,
                size: AkCardSize.small,
              ),
            )),
      ],
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🔄 القسم السادس: البطاقات المتقدمة (منقولة من features/shared/widgets)
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● بطاقة الإحصائيات الموحدة (AkStatisticsCard)
// ───────────────────────────────────────────────────────────────────────────────

/// بطاقة إحصائيات موحدة ومحسنة
/// تعرض إحصائية واحدة مع أيقونة ولون مميز
///
/// **المميزات:**
/// - تصميم موحد للإحصائيات
/// - دعم كامل للوضع المظلم/الفاتح
/// - ألوان تفاعلية وأيقونات مخصصة
/// - تأثيرات تفاعلية عند اللمس
///
/// **مثال الاستخدام:**
/// ```dart
/// AkStatisticsCard(
///   title: 'إجمالي المبيعات',
///   value: '25,480 ر.ي',
///   icon: Icons.trending_up,
///   color: AppColors.success,
///   onTap: () => showSalesDetails(),
/// )
/// ```
class AkStatisticsCard extends StatelessWidget {
  /// عنوان الإحصائية
  final String title;

  /// قيمة الإحصائية
  final String value;

  /// أيقونة الإحصائية
  final IconData icon;

  /// لون الإحصائية
  final Color color;

  /// دالة عند النقر
  final VoidCallback? onTap;

  /// حجم البطاقة
  final AkCardSize size;

  const AkStatisticsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
    this.size = AkCardSize.medium,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // تحديد الأحجام حسب حجم البطاقة
    final padding = _getPadding();
    final iconSize = _getIconSize();
    final titleFontSize = _getTitleFontSize();
    final valueFontSize = _getValueFontSize();

    return AkCard(
      type: AkCardType.elevated,
      size: size,
      onTap: onTap,
      child: Padding(
        padding: padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: AppTypography.createCustomStyle(
                      fontSize: titleFontSize,
                      fontWeight: AppTypography.weightMedium,
                      color: isDark
                          ? AppColors.darkTextSecondary
                          : AppColors.lightTextSecondary,
                    ),
                  ),
                ),
                Icon(
                  icon,
                  color: color,
                  size: iconSize,
                ),
              ],
            ),
            SizedBox(height: AppDimensions.smallSpacing),
            Text(
              value,
              style: AppTypography.createCustomStyle(
                fontSize: valueFontSize,
                fontWeight: AppTypography.weightBold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على المسافة الداخلية حسب الحجم
  EdgeInsets _getPadding() {
    switch (size) {
      case AkCardSize.small:
        return EdgeInsets.all(AppDimensions.smallMargin);
      case AkCardSize.medium:
        return EdgeInsets.all(AppDimensions.defaultMargin);
      case AkCardSize.large:
        return EdgeInsets.all(AppDimensions.largeMargin);
      case AkCardSize.extraLarge:
        return EdgeInsets.all(AppDimensions.extraLargeMargin);
    }
  }

  /// الحصول على حجم الأيقونة حسب الحجم
  double _getIconSize() {
    switch (size) {
      case AkCardSize.small:
        return AppDimensions.mediumIconSize;
      case AkCardSize.medium:
        return AppDimensions.largeIconSize;
      case AkCardSize.large:
        return AppDimensions.extraLargeIconSize;
      case AkCardSize.extraLarge:
        return AppDimensions.extraLargeIconSize * 1.2;
    }
  }

  /// الحصول على حجم خط العنوان حسب الحجم
  double _getTitleFontSize() {
    switch (size) {
      case AkCardSize.small:
        return AppDimensions.smallFontSize;
      case AkCardSize.medium:
        return AppDimensions.defaultFontSize;
      case AkCardSize.large:
        return AppDimensions.mediumFontSize;
      case AkCardSize.extraLarge:
        return AppDimensions.largeFontSize;
    }
  }

  /// الحصول على حجم خط القيمة حسب الحجم
  double _getValueFontSize() {
    switch (size) {
      case AkCardSize.small:
        return AppDimensions.mediumFontSize;
      case AkCardSize.medium:
        return AppDimensions.largeFontSize;
      case AkCardSize.large:
        return AppDimensions.titleFontSize;
      case AkCardSize.extraLarge:
        return AppDimensions.headingFontSize;
    }
  }
}
