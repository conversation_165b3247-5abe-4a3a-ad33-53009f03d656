import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_logger.dart';
import 'setup_flag_service.dart';

/// مدير الجلسات للتحكم في حالة تسجيل الدخول وحالة الإعداد الأولي
class SessionManager {
  static const String _keyIsFirstLaunch = 'is_first_launch';
  static const String _keyIsLoggedIn = 'is_logged_in';
  static const String _keyUserId = 'user_id';
  static const String _keyUsername = 'username';
  static const String _keyUserRole = 'user_role';
  static const String _keyBranchId = 'branch_id';
  static const String _keySetupCompleted = 'setup_completed';

  /// حفظ حالة التشغيل الأول
  static Future<bool> setFirstLaunch(bool isFirstLaunch) async {
    try {
      // حفظ في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool(_keyIsFirstLaunch, isFirstLaunch);

      // للتأكد من أن البيانات تم حفظها بنجاح
      if (result) {
        AppLogger.info(
            '✅ تم حفظ حالة التشغيل الأول في SharedPreferences بنجاح: $isFirstLaunch');
      } else {
        AppLogger.error(
            '❌ فشل في حفظ حالة التشغيل الأول في SharedPreferences: $isFirstLaunch');
      }

      // حفظ في ملف محلي كطريقة بديلة
      final fileResult = await SetupFlagService.setFirstLaunch(isFirstLaunch);
      if (!fileResult) {
        AppLogger.error(
            '❌ فشل في حفظ حالة التشغيل الأول في الملف المحلي: $isFirstLaunch');
      }

      // التحقق من أن البيانات تم حفظها بالفعل
      final bool savedValue = prefs.getBool(_keyIsFirstLaunch) ?? true;
      AppLogger.info(
          '🔍 التحقق من حفظ بيانات التشغيل الأول في SharedPreferences: $savedValue');

      // التحقق من حالة الملف المحلي
      final bool fileValue = await SetupFlagService.isFirstLaunch();
      if (fileValue != isFirstLaunch) {
        AppLogger.warning(
            '⚠️ تناقض بين SharedPreferences والملف المحلي: SharedPreferences=$savedValue, ملف محلي=$fileValue');
        // محاولة إعادة حفظ الملف المحلي
        await SetupFlagService.setFirstLaunch(isFirstLaunch);
      }

      return result && fileResult;
    } catch (e) {
      AppLogger.error('❌❌ خطأ في حفظ حالة التشغيل الأول: $e');

      // محاولة الحفظ في الملف المحلي فقط في حالة فشل SharedPreferences
      try {
        return await SetupFlagService.setFirstLaunch(isFirstLaunch);
      } catch (fileError) {
        AppLogger.error(
            '❌❌ خطأ في حفظ حالة التشغيل الأول في الملف المحلي: $fileError');
        return false;
      }
    }
  }

  /// التحقق مما إذا كان هذا هو التشغيل الأول للتطبيق
  static Future<bool> isFirstLaunch() async {
    try {
      // محاولة القراءة من SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final prefsResult = prefs.getBool(_keyIsFirstLaunch);

      // القراءة من الملف المحلي
      final fileResult = await SetupFlagService.isFirstLaunch();

      // تسجيل القيم للتشخيص
      AppLogger.info(
          '🔍 قراءة حالة التشغيل الأول من SharedPreferences: ${prefsResult ?? "غير محدد"}');
      AppLogger.info(
          '🔍 قراءة حالة التشغيل الأول من الملف المحلي: $fileResult');

      // إذا كانت القيمة محددة في SharedPreferences، نستخدمها
      if (prefsResult != null) {
        // إذا كان هناك تناقض بين SharedPreferences والملف المحلي، نقوم بمزامنتهما
        if (prefsResult != fileResult) {
          AppLogger.warning(
              '⚠️ تناقض بين SharedPreferences والملف المحلي، مزامنة البيانات...');
          await SetupFlagService.setFirstLaunch(prefsResult);
        }
        return prefsResult;
      }

      // إذا لم تكن القيمة محددة في SharedPreferences، نستخدم قيمة الملف المحلي ونحدث SharedPreferences
      await prefs.setBool(_keyIsFirstLaunch, fileResult);
      AppLogger.info(
          '✅ تم تحديث SharedPreferences من الملف المحلي: $fileResult');

      return fileResult;
    } catch (e) {
      AppLogger.error('❌❌ خطأ في قراءة حالة التشغيل الأول: $e');

      // في حالة الخطأ، نحاول القراءة من الملف المحلي
      try {
        return await SetupFlagService.isFirstLaunch();
      } catch (fileError) {
        AppLogger.error(
            '❌❌ خطأ في قراءة حالة التشغيل الأول من الملف المحلي: $fileError');
        return true; // نفترض أنه التشغيل الأول في حالة حدوث خطأ
      }
    }
  }

  /// حفظ حالة اكتمال الإعداد الأولي
  static Future<bool> setSetupCompleted(bool isCompleted) async {
    try {
      // حفظ في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool(_keySetupCompleted, isCompleted);

      // للتأكد من أن البيانات تم حفظها بنجاح
      if (result) {
        AppLogger.info(
            '✅ تم حفظ حالة الإعداد في SharedPreferences بنجاح: $isCompleted');
      } else {
        AppLogger.error(
            '❌ فشل في حفظ حالة الإعداد في SharedPreferences: $isCompleted');
      }

      // حفظ في ملف محلي كطريقة بديلة
      final fileResult = await SetupFlagService.setSetupCompleted(isCompleted);
      if (!fileResult) {
        AppLogger.error(
            '❌ فشل في حفظ حالة الإعداد في الملف المحلي: $isCompleted');
      }

      // التحقق من أن البيانات تم حفظها بالفعل
      final bool savedValue = prefs.getBool(_keySetupCompleted) ?? false;
      AppLogger.info(
          '🔍 التحقق من حفظ البيانات في SharedPreferences: $savedValue');

      return result && fileResult;
    } catch (e) {
      AppLogger.error('❌❌ خطأ في حفظ حالة الإعداد: $e');

      // محاولة الحفظ في الملف المحلي فقط في حالة فشل SharedPreferences
      try {
        return await SetupFlagService.setSetupCompleted(isCompleted);
      } catch (fileError) {
        AppLogger.error(
            '❌❌ خطأ في حفظ حالة الإعداد في الملف المحلي: $fileError');
        return false;
      }
    }
  }

  /// التحقق مما إذا كان الإعداد الأولي قد اكتمل
  static Future<bool> isSetupCompleted() async {
    try {
      // محاولة القراءة من SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getBool(_keySetupCompleted) ?? false;

      // للتأكد من أن البيانات تم قراءتها بنجاح
      AppLogger.info('🔍 قراءة حالة الإعداد من SharedPreferences: $result');

      if (result) {
        return true;
      }

      // إذا لم يتم العثور على القيمة في SharedPreferences، نحاول القراءة من الملف المحلي
      final fileResult = await SetupFlagService.isSetupCompleted();
      AppLogger.info('🔍 قراءة حالة الإعداد من الملف المحلي: $fileResult');

      // إذا وجدنا القيمة في الملف المحلي، نقوم بتحديث SharedPreferences
      if (fileResult) {
        await prefs.setBool(_keySetupCompleted, true);
        AppLogger.info('✅ تم تحديث SharedPreferences من الملف المحلي');
      }

      return fileResult;
    } catch (e) {
      AppLogger.error('❌❌ خطأ في قراءة حالة الإعداد من SharedPreferences: $e');

      // محاولة القراءة من الملف المحلي فقط في حالة فشل SharedPreferences
      try {
        return await SetupFlagService.isSetupCompleted();
      } catch (fileError) {
        AppLogger.error(
            '❌❌ خطأ في قراءة حالة الإعداد من الملف المحلي: $fileError');
        return false;
      }
    }
  }

  /// تسجيل دخول المستخدم وحفظ بياناته
  static Future<bool> login(String userId, String username, String role,
      {String? branchId}) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ بيانات المستخدم
      bool success = true;
      success = success && await prefs.setBool(_keyIsLoggedIn, true);
      success = success && await prefs.setString(_keyUserId, userId);
      success = success && await prefs.setString(_keyUsername, username);
      success = success && await prefs.setString(_keyUserRole, role);

      // حفظ معرف الفرع إذا تم تمريره
      if (branchId != null && branchId.isNotEmpty) {
        success = success && await prefs.setString(_keyBranchId, branchId);
        AppLogger.info('✅ تم حفظ معرف الفرع: $branchId');
      }

      // عند تسجيل الدخول، نعتبر أن الإعداد الأولي قد اكتمل
      // استخدام الدالة المحسنة لحفظ حالة الإعداد
      final setupSuccess = await setSetupCompleted(true);

      // لم يعد التشغيل الأول
      // استخدام الدالة المحسنة لحفظ حالة التشغيل الأول
      final firstLaunchSuccess = await setFirstLaunch(false);

      // حفظ حالة الإعداد في الملف المحلي أيضًا
      await SetupFlagService.setSetupCompleted(true);
      await SetupFlagService.setFirstLaunch(false);

      if (success && setupSuccess && firstLaunchSuccess) {
        AppLogger.info('✅ تم تسجيل الدخول وحفظ البيانات بنجاح: $username');

        // التحقق من أن البيانات تم حفظها بالفعل
        final bool setupCompletedValue =
            prefs.getBool(_keySetupCompleted) ?? false;
        final bool firstLaunchValue = prefs.getBool(_keyIsFirstLaunch) ?? true;
        AppLogger.info('🔍 التحقق من البيانات بعد الحفظ:');
        AppLogger.info('   - حالة الإعداد: $setupCompletedValue');
        AppLogger.info('   - التشغيل الأول: $firstLaunchValue');

        // التحقق من الملف المحلي أيضًا
        final bool fileSetupCompleted =
            await SetupFlagService.isSetupCompleted();
        final bool fileFirstLaunch = await SetupFlagService.isFirstLaunch();
        AppLogger.info('🔍 التحقق من البيانات في الملف المحلي:');
        AppLogger.info('   - حالة الإعداد: $fileSetupCompleted');
        AppLogger.info('   - التشغيل الأول: $fileFirstLaunch');
      } else {
        AppLogger.error('❌ فشل في تسجيل الدخول وحفظ البيانات: $username');
        AppLogger.error('   - نجاح تسجيل الدخول: $success');
        AppLogger.error('   - نجاح حفظ حالة الإعداد: $setupSuccess');
        AppLogger.error(
            '   - نجاح حفظ حالة التشغيل الأول: $firstLaunchSuccess');
      }

      return success && setupSuccess && firstLaunchSuccess;
    } catch (e) {
      AppLogger.error('❌❌ خطأ في تسجيل الدخول: $e');

      // محاولة حفظ حالة الإعداد والتشغيل الأول في الملف المحلي
      try {
        await SetupFlagService.setSetupCompleted(true);
        await SetupFlagService.setFirstLaunch(false);
        AppLogger.info(
            '✅ تم حفظ حالة الإعداد والتشغيل الأول في الملف المحلي بعد فشل تسجيل الدخول');
      } catch (fileError) {
        AppLogger.error(
            '❌❌ خطأ في حفظ حالة الإعداد والتشغيل الأول في الملف المحلي: $fileError');
      }

      return false;
    }
  }

  /// تسجيل خروج المستخدم
  static Future<void> logout() async {
    try {
      // حفظ في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyIsLoggedIn, false);

      // لا نقوم بحذف بيانات المستخدم لتسهيل إعادة تسجيل الدخول
      AppLogger.info('✅ تم تسجيل الخروج بنجاح');

      // التحقق من أن البيانات تم حفظها بالفعل
      final bool isLoggedIn = prefs.getBool(_keyIsLoggedIn) ?? false;
      AppLogger.info('🔍 التحقق من حالة تسجيل الدخول بعد الخروج: $isLoggedIn');

      // التأكد من أن حالة الإعداد والتشغيل الأول لم تتغير
      final bool isSetupCompleted = prefs.getBool(_keySetupCompleted) ?? false;
      final bool isFirstLaunch = prefs.getBool(_keyIsFirstLaunch) ?? true;
      AppLogger.info('🔍 التحقق من البيانات بعد تسجيل الخروج:');
      AppLogger.info('   - حالة الإعداد: $isSetupCompleted');
      AppLogger.info('   - التشغيل الأول: $isFirstLaunch');
    } catch (e) {
      AppLogger.error('❌❌ خطأ في تسجيل الخروج: $e');
    }
  }

  /// التحقق مما إذا كان المستخدم مسجل الدخول
  static Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bool isLoggedIn = prefs.getBool(_keyIsLoggedIn) ?? false;

      AppLogger.info('🔍 التحقق من حالة تسجيل الدخول: $isLoggedIn');

      return isLoggedIn;
    } catch (e) {
      AppLogger.error('❌❌ خطأ في التحقق من حالة تسجيل الدخول: $e');
      return false;
    }
  }

  /// الحصول على معرف المستخدم الحالي
  static Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyUserId);
  }

  /// الحصول على اسم المستخدم الحالي
  static Future<String?> getUsername() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyUsername);
  }

  /// الحصول على دور المستخدم الحالي
  static Future<String?> getUserRole() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyUserRole);
  }

  /// الحصول على معرف الفرع الحالي
  static Future<String?> getBranchId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyBranchId);
  }

  /// إعادة تعيين جميع بيانات الجلسة (لاستخدامها عند إعادة تعيين التطبيق)
  static Future<bool> resetSession() async {
    try {
      // مسح بيانات SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      // مسح بيانات الملفات المحلية
      final fileResult = await SetupFlagService.resetAll();

      AppLogger.info('✅ تم إعادة تعيين جميع بيانات الجلسة بنجاح');

      return fileResult;
    } catch (e) {
      AppLogger.error('❌❌ خطأ في إعادة تعيين بيانات الجلسة: $e');
      return false;
    }
  }
}
