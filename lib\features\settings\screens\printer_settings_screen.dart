import 'dart:convert';
import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/utils/index.dart';
import '../../../core/widgets/index.dart';
import '../presenters/settings_presenter.dart';
import '../../../core/theme/index.dart';

/// شاشة إعدادات الطابعة
class PrinterSettingsScreen extends StatefulWidget {
  const PrinterSettingsScreen({Key? key}) : super(key: key);

  @override
  State<PrinterSettingsScreen> createState() => _PrinterSettingsScreenState();
}

class _PrinterSettingsScreenState extends State<PrinterSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _printerNameController = TextEditingController();
  final TextEditingController _printerAddressController =
      TextEditingController();
  final TextEditingController _paperWidthController = TextEditingController();
  final TextEditingController _paperHeightController = TextEditingController();
  final TextEditingController _marginTopController = TextEditingController();
  final TextEditingController _marginBottomController = TextEditingController();
  final TextEditingController _marginLeftController = TextEditingController();
  final TextEditingController _marginRightController = TextEditingController();
  final TextEditingController _headerTextController = TextEditingController();
  final TextEditingController _footerTextController = TextEditingController();
  final TextEditingController _printerProfileNameController =
      TextEditingController();
  final TextEditingController _printerCommandsController =
      TextEditingController();

  String _selectedPrinterType = 'thermal';
  String _selectedConnectionType = 'bluetooth';
  String _selectedPaperSize = 'a4';
  String _selectedReportType = 'invoice';
  bool _isDefaultPrinter = true;
  bool _printLogo = true;
  bool _printQRCode = true;
  bool _printBarcode = true;
  bool _printDateTime = true;
  bool _printOperator = true;
  bool _printTaxDetails = true;
  bool _useCustomTemplate = false;
  bool _isLoading = false;

  // قائمة بملفات تعريف الطابعات
  List<Map<String, dynamic>> _printerProfiles = [];

  final List<Map<String, dynamic>> _printerTypes = [
    {'value': 'thermal', 'label': 'طابعة حرارية'},
    {'value': 'laser', 'label': 'طابعة ليزر'},
    {'value': 'inkjet', 'label': 'طابعة حبر'},
    {'value': 'dot_matrix', 'label': 'طابعة نقطية'},
    {'value': 'pos', 'label': 'طابعة نقاط البيع (POS)'},
    {'value': 'mobile', 'label': 'طابعة محمولة'},
    {'value': 'other', 'label': 'أخرى'},
  ];

  final List<Map<String, dynamic>> _connectionTypes = [
    {'value': 'bluetooth', 'label': 'بلوتوث'},
    {'value': 'wifi', 'label': 'واي فاي'},
    {'value': 'usb', 'label': 'USB'},
    {'value': 'network', 'label': 'شبكة'},
    {'value': 'serial', 'label': 'منفذ تسلسلي (Serial)'},
    {'value': 'parallel', 'label': 'منفذ متوازي (Parallel)'},
    {'value': 'cloud', 'label': 'طباعة سحابية'},
    {'value': 'other', 'label': 'أخرى'},
  ];

  final List<Map<String, dynamic>> _paperSizes = [
    {'value': 'a4', 'label': 'A4 (210×297 مم)'},
    {'value': 'a5', 'label': 'A5 (148×210 مم)'},
    {'value': 'a6', 'label': 'A6 (105×148 مم)'},
    {'value': 'letter', 'label': 'Letter (216×279 مم)'},
    {'value': 'legal', 'label': 'Legal (216×356 مم)'},
    {'value': '80mm', 'label': 'حراري 80 مم'},
    {'value': '58mm', 'label': 'حراري 58 مم'},
    {'value': '76mm', 'label': 'حراري 76 مم'},
    {'value': '57mm', 'label': 'حراري 57 مم'},
    {'value': 'custom', 'label': 'مخصص'},
  ];

  final List<Map<String, dynamic>> _reportTypes = [
    {'value': 'invoice', 'label': 'فاتورة بيع'},
    {'value': 'receipt', 'label': 'إيصال استلام'},
    {'value': 'payment', 'label': 'سند صرف'},
    {'value': 'purchase', 'label': 'فاتورة شراء'},
    {'value': 'return', 'label': 'فاتورة مرتجع'},
    {'value': 'inventory', 'label': 'تقرير مخزون'},
    {'value': 'financial', 'label': 'تقرير مالي'},
    {'value': 'customer', 'label': 'كشف حساب عميل'},
    {'value': 'supplier', 'label': 'كشف حساب مورد'},
    {'value': 'barcode', 'label': 'طباعة باركود'},
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل إعدادات الطابعة
  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final settings = AppProviders.getLazyPresenter<SettingsPresenter>(
          () => SettingsPresenter());
      await settings.loadSettings();

      // تعبئة الحقول بالإعدادات المحفوظة - إعدادات الطابعة الأساسية
      _printerNameController.text = settings.getSetting('printer_name') ?? '';
      _printerAddressController.text =
          settings.getSetting('printer_address') ?? '';
      _selectedPrinterType = settings.getSetting('printer_type') ?? 'thermal';
      _selectedConnectionType =
          settings.getSetting('connection_type') ?? 'bluetooth';
      _selectedPaperSize = settings.getSetting('paper_size') ?? 'a4';
      _paperWidthController.text = settings.getSetting('paper_width') ?? '';
      _paperHeightController.text = settings.getSetting('paper_height') ?? '';

      // تعبئة إعدادات الهوامش
      _marginTopController.text = settings.getSetting('margin_top') ?? '10';
      _marginBottomController.text =
          settings.getSetting('margin_bottom') ?? '10';
      _marginLeftController.text = settings.getSetting('margin_left') ?? '10';
      _marginRightController.text = settings.getSetting('margin_right') ?? '10';

      // تعبئة إعدادات المحتوى
      _headerTextController.text = settings.getSetting('header_text') ?? '';
      _footerTextController.text = settings.getSetting('footer_text') ?? '';
      _isDefaultPrinter = settings.getSetting('is_default_printer') == 'true';
      _printLogo = settings.getSetting('print_logo') == 'true';
      _printQRCode = settings.getSetting('print_qr_code') == 'true';

      // تعبئة الإعدادات الجديدة
      _printBarcode = settings.getSetting('print_barcode') == 'true';
      _printDateTime = settings.getSetting('print_date_time') == 'true';
      _printOperator = settings.getSetting('print_operator') == 'true';
      _printTaxDetails = settings.getSetting('print_tax_details') == 'true';

      // تعبئة إعدادات التقارير
      _selectedReportType = settings.getSetting('report_type') ?? 'invoice';
      _useCustomTemplate = settings.getSetting('use_custom_template') == 'true';

      // تعبئة إعدادات القالب المخصص
      _printerProfileNameController.text =
          settings.getSetting('printer_profile_name') ?? '';
      _printerCommandsController.text =
          settings.getSetting('printer_commands') ?? '';

      // تحميل ملفات تعريف الطابعات
      _loadPrinterProfiles(settings);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل الإعدادات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تحميل ملفات تعريف الطابعات
  void _loadPrinterProfiles(SettingsPresenter settings) {
    try {
      _printerProfiles = [];

      // الحصول على جميع المفاتيح المخزنة
      final allSettings = settings.getAllSettings();

      // البحث عن ملفات تعريف الطابعات
      for (var key in allSettings.keys) {
        if (key.startsWith('printer_profile_')) {
          try {
            // تحويل القيمة من JSON إلى Map
            final profileData = jsonDecode(allSettings[key]!);
            if (profileData is Map<String, dynamic>) {
              _printerProfiles.add(profileData);
            }
          } catch (e) {
            // تجاهل الملفات غير الصالحة
            AppLogger.error('خطأ في تحميل ملف تعريف الطابعة: $e');
          }
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل ملفات تعريف الطابعات: $e');
    }
  }

  @override
  void dispose() {
    _printerNameController.dispose();
    _printerAddressController.dispose();
    _paperWidthController.dispose();
    _paperHeightController.dispose();
    _marginTopController.dispose();
    _marginBottomController.dispose();
    _marginLeftController.dispose();
    _marginRightController.dispose();
    _headerTextController.dispose();
    _footerTextController.dispose();
    _printerProfileNameController.dispose();
    _printerCommandsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AkAppBar(
        title: 'إعدادات الطابعة',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات الطابعة
                    AkCard(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'معلومات الطابعة',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 16),
                            // اسم الطابعة
                            AkTextInput(
                              controller: _printerNameController,
                              label: 'اسم الطابعة',
                              hint: 'أدخل اسم الطابعة',
                              prefixIcon: Icons.print,
                            ),
                            const SizedBox(height: 16),
                            // نوع الطابعة
                            DropdownButtonFormField<String>(
                              value: _selectedPrinterType,
                              decoration: InputDecoration(
                                labelText: 'نوع الطابعة',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(
                                      Layout.defaultRadius),
                                ),
                                prefixIcon: const Icon(Icons.print),
                              ),
                              items: _printerTypes.map((type) {
                                return DropdownMenuItem<String>(
                                  value: type['value'],
                                  child: Text(type['label']),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedPrinterType = value!;
                                });
                              },
                            ),
                            const SizedBox(height: 16),
                            // نوع الاتصال
                            DropdownButtonFormField<String>(
                              value: _selectedConnectionType,
                              decoration: InputDecoration(
                                labelText: 'نوع الاتصال',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(
                                      Layout.defaultRadius),
                                ),
                                prefixIcon: const Icon(Icons.wifi),
                              ),
                              items: _connectionTypes.map((type) {
                                return DropdownMenuItem<String>(
                                  value: type['value'],
                                  child: Text(type['label']),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedConnectionType = value!;
                                });
                              },
                            ),
                            const SizedBox(height: 16),
                            // عنوان الطابعة
                            AkTextInput(
                              controller: _printerAddressController,
                              label: 'عنوان الطابعة',
                              hint: _getAddressHint(),
                              prefixIcon: _getAddressIcon(),
                            ),
                            const SizedBox(height: 16),
                            // الطابعة الافتراضية
                            SwitchListTile(
                              title: const Text('الطابعة الافتراضية'),
                              subtitle: const Text(
                                  'استخدام هذه الطابعة كطابعة افتراضية للنظام'),
                              value: _isDefaultPrinter,
                              onChanged: (value) {
                                setState(() {
                                  _isDefaultPrinter = value;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // إعدادات الورق
                    AkCard(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'إعدادات الورق',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 16),
                            // حجم الورق
                            DropdownButtonFormField<String>(
                              value: _selectedPaperSize,
                              decoration: InputDecoration(
                                labelText: 'حجم الورق',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(
                                      Layout.defaultRadius),
                                ),
                                prefixIcon: const Icon(Icons.description),
                              ),
                              items: _paperSizes.map((size) {
                                return DropdownMenuItem<String>(
                                  value: size['value'],
                                  child: Text(size['label']),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedPaperSize = value!;
                                  // تعبئة الأبعاد تلقائياً حسب الحجم المختار
                                  _setPaperDimensions(value);
                                });
                              },
                            ),
                            const SizedBox(height: 16),
                            // أبعاد الورق المخصصة (تظهر فقط عند اختيار حجم مخصص)
                            if (_selectedPaperSize == 'custom')
                              Row(
                                children: [
                                  Expanded(
                                    child: AkTextInput(
                                      controller: _paperWidthController,
                                      label: 'العرض (مم)',
                                      hint: 'أدخل عرض الورق',
                                      prefixIcon: Icons.width_normal,
                                      keyboardType: TextInputType.number,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: AkTextInput(
                                      controller: _paperHeightController,
                                      label: 'الطول (مم)',
                                      hint: 'أدخل طول الورق',
                                      prefixIcon: Icons.height,
                                      keyboardType: TextInputType.number,
                                    ),
                                  ),
                                ],
                              ),
                            if (_selectedPaperSize == 'custom')
                              const SizedBox(height: 16),
                            // الهوامش
                            Text(
                              'الهوامش (مم)',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: AkTextInput(
                                    controller: _marginTopController,
                                    label: 'أعلى',
                                    hint: '10',
                                    keyboardType: TextInputType.number,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: AkTextInput(
                                    controller: _marginBottomController,
                                    label: 'أسفل',
                                    hint: '10',
                                    keyboardType: TextInputType.number,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: AkTextInput(
                                    controller: _marginLeftController,
                                    label: 'يسار',
                                    hint: '10',
                                    keyboardType: TextInputType.number,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: AkTextInput(
                                    controller: _marginRightController,
                                    label: 'يمين',
                                    hint: '10',
                                    keyboardType: TextInputType.number,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // إعدادات المحتوى
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'إعدادات المحتوى',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 16),
                            // نص الترويسة
                            AkTextInput(
                              controller: _headerTextController,
                              label: 'نص الترويسة',
                              hint: 'أدخل نص الترويسة',
                              maxLines: 3,
                            ),
                            const SizedBox(height: 16),
                            // نص التذييل
                            AkTextInput(
                              controller: _footerTextController,
                              label: 'نص التذييل',
                              hint: 'أدخل نص التذييل',
                              maxLines: 3,
                            ),
                            const SizedBox(height: 16),
                            // طباعة الشعار
                            SwitchListTile(
                              title: const Text('طباعة الشعار'),
                              subtitle: const Text(
                                  'عرض شعار الشركة في المستندات المطبوعة'),
                              value: _printLogo,
                              onChanged: (value) {
                                setState(() {
                                  _printLogo = value;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                            // طباعة رمز QR
                            SwitchListTile(
                              title: const Text('طباعة رمز QR'),
                              subtitle: const Text(
                                  'عرض رمز QR في المستندات المطبوعة'),
                              value: _printQRCode,
                              onChanged: (value) {
                                setState(() {
                                  _printQRCode = value;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                            // طباعة الباركود
                            SwitchListTile(
                              title: const Text('طباعة الباركود'),
                              subtitle: const Text(
                                  'عرض الباركود في المستندات المطبوعة'),
                              value: _printBarcode,
                              onChanged: (value) {
                                setState(() {
                                  _printBarcode = value;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                            // طباعة التاريخ والوقت
                            SwitchListTile(
                              title: const Text('طباعة التاريخ والوقت'),
                              subtitle: const Text(
                                  'عرض التاريخ والوقت في المستندات المطبوعة'),
                              value: _printDateTime,
                              onChanged: (value) {
                                setState(() {
                                  _printDateTime = value;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                            // طباعة اسم المستخدم
                            SwitchListTile(
                              title: const Text('طباعة اسم المستخدم'),
                              subtitle: const Text(
                                  'عرض اسم المستخدم الذي أنشأ المستند'),
                              value: _printOperator,
                              onChanged: (value) {
                                setState(() {
                                  _printOperator = value;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                            // طباعة تفاصيل الضريبة
                            SwitchListTile(
                              title: const Text('طباعة تفاصيل الضريبة'),
                              subtitle: const Text(
                                  'عرض تفاصيل الضريبة في المستندات المطبوعة'),
                              value: _printTaxDetails,
                              onChanged: (value) {
                                setState(() {
                                  _printTaxDetails = value;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // إعدادات التقارير
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'إعدادات التقارير',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 16),
                            // نوع التقرير
                            DropdownButtonFormField<String>(
                              value: _selectedReportType,
                              decoration: InputDecoration(
                                labelText: 'نوع التقرير',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(
                                      Layout.defaultRadius),
                                ),
                                prefixIcon: const Icon(Icons.description),
                              ),
                              items: _reportTypes.map((type) {
                                return DropdownMenuItem<String>(
                                  value: type['value'],
                                  child: Text(type['label']),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedReportType = value!;
                                });
                              },
                            ),
                            const SizedBox(height: 16),
                            // استخدام قالب مخصص
                            SwitchListTile(
                              title: const Text('استخدام قالب مخصص'),
                              subtitle: const Text(
                                  'استخدام قالب مخصص للتقارير والمستندات'),
                              value: _useCustomTemplate,
                              onChanged: (value) {
                                setState(() {
                                  _useCustomTemplate = value;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                            if (_useCustomTemplate) ...[
                              const SizedBox(height: 16),
                              // اسم ملف تعريف الطابعة
                              AkTextInput(
                                controller: _printerProfileNameController,
                                label: 'اسم ملف التعريف',
                                hint: 'أدخل اسماً لملف تعريف الطابعة',
                                prefixIcon: Icons.badge,
                              ),
                              const SizedBox(height: 16),
                              // أوامر الطابعة المخصصة
                              AkTextInput(
                                controller: _printerCommandsController,
                                label: 'أوامر الطابعة المخصصة',
                                hint: 'أدخل أوامر الطابعة المخصصة (ESC/POS)',
                                prefixIcon: Icons.code,
                                maxLines: 5,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'ملاحظة: استخدم أوامر ESC/POS للطابعات الحرارية أو أوامر مخصصة حسب نوع الطابعة.',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      fontStyle: FontStyle.italic,
                                    ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // أزرار الإجراءات
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _saveSettings,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: _isLoading
                                ? const CircularProgressIndicator(
                                    color: AppColors.onPrimary)
                                : const Text('حفظ الإعدادات'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        OutlinedButton(
                          onPressed: _isLoading ? null : _testPrinter,
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: const Text('اختبار الطابعة'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  /// الحصول على تلميح عنوان الطابعة حسب نوع الاتصال
  String _getAddressHint() {
    switch (_selectedConnectionType) {
      case 'bluetooth':
        return 'عنوان MAC للطابعة (مثال: 00:11:22:33:44:55)';
      case 'wifi':
      case 'network':
        return 'عنوان IP للطابعة (مثال: *************)';
      case 'usb':
        return 'اسم جهاز USB';
      default:
        return 'أدخل عنوان الطابعة';
    }
  }

  /// الحصول على أيقونة عنوان الطابعة حسب نوع الاتصال
  IconData _getAddressIcon() {
    switch (_selectedConnectionType) {
      case 'bluetooth':
        return Icons.bluetooth;
      case 'wifi':
        return Icons.wifi;
      case 'network':
        return Icons.lan;
      case 'usb':
        return Icons.usb;
      default:
        return Icons.link;
    }
  }

  /// تعيين أبعاد الورق حسب الحجم المختار
  void _setPaperDimensions(String paperSize) {
    switch (paperSize) {
      case 'a4':
        _paperWidthController.text = '210';
        _paperHeightController.text = '297';
        break;
      case 'a5':
        _paperWidthController.text = '148';
        _paperHeightController.text = '210';
        break;
      case 'letter':
        _paperWidthController.text = '216';
        _paperHeightController.text = '279';
        break;
      case '80mm':
        _paperWidthController.text = '80';
        _paperHeightController.text = '297'; // طول قابل للتعديل
        break;
      case '58mm':
        _paperWidthController.text = '58';
        _paperHeightController.text = '297'; // طول قابل للتعديل
        break;
      case 'custom':
        // لا تغيير في القيم الحالية
        break;
    }
  }

  /// حفظ إعدادات الطابعة
  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final settings = AppProviders.getLazyPresenter<SettingsPresenter>(
          () => SettingsPresenter());

      // حفظ إعدادات الطابعة الأساسية
      await settings.saveSetting('printer_name', _printerNameController.text);
      await settings.saveSetting(
          'printer_address', _printerAddressController.text);
      await settings.saveSetting('printer_type', _selectedPrinterType);
      await settings.saveSetting('connection_type', _selectedConnectionType);
      await settings.saveSetting('paper_size', _selectedPaperSize);
      await settings.saveSetting('paper_width', _paperWidthController.text);
      await settings.saveSetting('paper_height', _paperHeightController.text);

      // حفظ إعدادات الهوامش
      await settings.saveSetting('margin_top', _marginTopController.text);
      await settings.saveSetting('margin_bottom', _marginBottomController.text);
      await settings.saveSetting('margin_left', _marginLeftController.text);
      await settings.saveSetting('margin_right', _marginRightController.text);

      // حفظ إعدادات المحتوى
      await settings.saveSetting('header_text', _headerTextController.text);
      await settings.saveSetting('footer_text', _footerTextController.text);
      await settings.saveSetting(
          'is_default_printer', _isDefaultPrinter.toString());
      await settings.saveSetting('print_logo', _printLogo.toString());
      await settings.saveSetting('print_qr_code', _printQRCode.toString());

      // حفظ الإعدادات الجديدة
      await settings.saveSetting('print_barcode', _printBarcode.toString());
      await settings.saveSetting('print_date_time', _printDateTime.toString());
      await settings.saveSetting('print_operator', _printOperator.toString());
      await settings.saveSetting(
          'print_tax_details', _printTaxDetails.toString());

      // حفظ إعدادات التقارير
      await settings.saveSetting('report_type', _selectedReportType);
      await settings.saveSetting(
          'use_custom_template', _useCustomTemplate.toString());

      // حفظ إعدادات القالب المخصص إذا كان مفعلاً
      if (_useCustomTemplate) {
        await settings.saveSetting(
            'printer_profile_name', _printerProfileNameController.text);
        await settings.saveSetting(
            'printer_commands', _printerCommandsController.text);
      }

      // حفظ ملف تعريف الطابعة إذا كان مخصصاً
      if (_useCustomTemplate && _printerProfileNameController.text.isNotEmpty) {
        final printerProfile = {
          'name': _printerProfileNameController.text,
          'printer_type': _selectedPrinterType,
          'connection_type': _selectedConnectionType,
          'printer_address': _printerAddressController.text,
          'paper_size': _selectedPaperSize,
          'paper_width': _paperWidthController.text,
          'paper_height': _paperHeightController.text,
          'commands': _printerCommandsController.text,
          'created_at': DateTime.now().toIso8601String(),
        };

        // حفظ ملف التعريف كسلسلة JSON
        await settings.saveSetting(
          'printer_profile_${_printerProfileNameController.text}',
          jsonEncode(printerProfile),
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ إعدادات الطابعة بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حفظ الإعدادات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// اختبار الطابعة
  Future<void> _testPrinter() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // حفظ الإعدادات الحالية أولاً
      await _saveSettings();

      // إنشاء محتوى صفحة الاختبار
      final testContent = _createTestPage();

      // محاكاة عملية الطباعة
      await Future.delayed(const Duration(seconds: 2));

      // عرض معاينة للطباعة
      if (mounted) {
        _showPrintPreview(testContent);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في اختبار الطابعة: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// إنشاء محتوى صفحة اختبار الطابعة
  String _createTestPage() {
    final now = DateTime.now();
    final dateStr = '${now.year}/${now.month}/${now.day}';
    final timeStr = '${now.hour}:${now.minute}:${now.second}';

    return '''
===========================================
            صفحة اختبار الطابعة
===========================================
اسم الطابعة: ${_printerNameController.text}
نوع الطابعة: ${_getPrinterTypeLabel(_selectedPrinterType)}
نوع الاتصال: ${_getConnectionTypeLabel(_selectedConnectionType)}
حجم الورق: ${_getPaperSizeLabel(_selectedPaperSize)}

التاريخ: $dateStr
الوقت: $timeStr
===========================================
            اختبار الخط والأحرف
===========================================
أبجد هوز حطي كلمن سعفص قرشت ثخذ ضظغ
ABCDEFGHIJKLMNOPQRSTUVWXYZ
abcdefghijklmnopqrstuvwxyz
0123456789
!@#\$%^&*()_+-=[]{}|;':",./<>?
===========================================
              اختبار الترويسة
===========================================
${_headerTextController.text}

===========================================
              اختبار التذييل
===========================================
${_footerTextController.text}

===========================================
          تم إنشاء هذه الصفحة بواسطة
                تاجر بلس
===========================================
''';
  }

  /// عرض معاينة الطباعة
  void _showPrintPreview(String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معاينة صفحة الاختبار'),
        content: Container(
          width: double.maxFinite,
          height: 400,
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.lightTextSecondary),
            borderRadius: BorderRadius.circular(8),
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: SelectableText(
              content,
              style: const AppTypography(
                fontFamily: 'Courier',
                fontSize: 12,
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // هنا يمكن إضافة كود لإرسال المحتوى إلى الطابعة الفعلية
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                    content: Text('تم إرسال صفحة اختبار إلى الطابعة')),
              );
            },
            child: const Text('طباعة'),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم نوع الطابعة
  String _getPrinterTypeLabel(String value) {
    final type = _printerTypes.firstWhere(
      (type) => type['value'] == value,
      orElse: () => {'value': value, 'label': value},
    );
    return type['label'] as String;
  }

  /// الحصول على اسم نوع الاتصال
  String _getConnectionTypeLabel(String value) {
    final type = _connectionTypes.firstWhere(
      (type) => type['value'] == value,
      orElse: () => {'value': value, 'label': value},
    );
    return type['label'] as String;
  }

  /// الحصول على اسم حجم الورق
  String _getPaperSizeLabel(String value) {
    final size = _paperSizes.firstWhere(
      (size) => size['value'] == value,
      orElse: () => {'value': value, 'label': value},
    );
    return size['label'] as String;
  }
}
