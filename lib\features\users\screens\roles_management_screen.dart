import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
// تم حذف features/shared/widgets/ - استخدم الأنظمة الموحدة
import '../../../core/auth/models/user_role.dart';

//import '../../../core/widgets/app_bar_widget.dart';
import '../../../core/widgets/index.dart';
import '../presenters/role_presenter.dart';
import '../widgets/role_form_dialog.dart';
import '../widgets/role_permissions_dialog.dart';
import '../../../core/theme/index.dart';

/// شاشة إدارة الأدوار
class RolesManagementScreen extends StatefulWidget {
  const RolesManagementScreen({Key? key}) : super(key: key);

  @override
  State<RolesManagementScreen> createState() => _RolesManagementScreenState();
}

class _RolesManagementScreenState extends State<RolesManagementScreen> {
  // مقدم البيانات
  late RolePresenter _rolePresenter;

  // حالة البحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _showSearchField = false;

  // حالة التحميل
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // تهيئة المقدم
    _rolePresenter =
        AppProviders.getLazyPresenter<RolePresenter>(() => RolePresenter());

    // إضافة مستمع للبحث
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });

    // تحميل البيانات بعد بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _initializeRoles();
    });
  }

  /// تهيئة الأدوار وضمان عرضها بشكل صحيح
  Future<void> _initializeRoles() async {
    try {
      // عرض مؤشر التحميل
      setState(() {
        _isLoading = true;
      });

      // تحميل الأدوار من قاعدة البيانات
      await _rolePresenter.loadRoles();

      // التحقق من وجود أدوار
      final roles = _rolePresenter.roles;
      if (roles.isEmpty) {
        // إذا لم تكن هناك أدوار، نقوم بإعادة تعيين الأدوار الافتراضية
        await _rolePresenter.resetDefaultRoles();
        _showSuccessSnackBar('تم تهيئة الأدوار الافتراضية');
        return;
      }

      // التحقق من صحة عرض الأدوار
      await _validateRolesDisplay(roles);
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تهيئة الأدوار: $e');
    } finally {
      // إخفاء مؤشر التحميل
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// التحقق من صحة عرض الأدوار
  Future<void> _validateRolesDisplay(List<UserRole> roles) async {
    try {
      // التحقق من أن الألوان تظهر بشكل صحيح
      bool needsReset = false;

      // فحص الأدوار الافتراضية للتأكد من أن الألوان تعمل
      final defaultRoles = roles.where((role) => !role.isCustom).toList();

      // التحقق من وجود الأدوار الأساسية
      final hasOwnerRole = defaultRoles.any((role) =>
          role.name.toLowerCase().contains('owner') ||
          role.name.toLowerCase().contains('مالك'));

      final hasAdminRole = defaultRoles.any((role) =>
          role.name.toLowerCase().contains('admin') ||
          role.name.toLowerCase().contains('مدير'));

      // إذا كانت الأدوار الأساسية غير موجودة، نقوم بإعادة تعيين الأدوار الافتراضية
      if (!hasOwnerRole || !hasAdminRole) {
        needsReset = true;
      } else {
        // فحص عينة من الأدوار للتأكد من أن الألوان تعمل
        for (final role in defaultRoles) {
          final color = _getRoleColor(role);
          if (color == AppColors.secondary) {
            // إذا كان لون الدور الافتراضي هو اللون الثانوي، فهذا يعني أن الألوان لا تعمل بشكل صحيح
            needsReset = true;
            break;
          }
        }
      }

      // إذا كانت الألوان لا تعمل بشكل صحيح، نقوم بإعادة تعيين الأدوار الافتراضية
      if (needsReset) {
        await _rolePresenter.resetDefaultRoles();
        _showSuccessSnackBar('تم تحديث الأدوار لعرض الألوان بشكل صحيح');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء التحقق من صحة عرض الأدوار: $e');
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _rolePresenter.loadRoles();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تحميل البيانات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// إضافة دور جديد
  Future<void> _addRole() async {
    final result = await showDialog<UserRole?>(
      context: context,
      builder: (context) => const RoleFormDialog(),
    );

    if (result != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final success = await _rolePresenter.createRole(result);

        if (success) {
          _showSuccessSnackBar('تم إضافة الدور بنجاح');
        } else {
          _showErrorSnackBar('فشل إضافة الدور');
        }
      } catch (e) {
        _showErrorSnackBar('حدث خطأ أثناء إضافة الدور: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تعديل دور
  Future<void> _editRole(UserRole role) async {
    // لا يمكن تعديل الأدوار الافتراضية
    if (!role.isCustom) {
      _showErrorSnackBar('لا يمكن تعديل الأدوار الافتراضية');
      return;
    }

    final result = await showDialog<UserRole?>(
      context: context,
      builder: (context) => RoleFormDialog(role: role),
    );

    if (result != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final success = await _rolePresenter.updateRole(result);

        if (success) {
          _showSuccessSnackBar('تم تحديث الدور بنجاح');
        } else {
          _showErrorSnackBar('فشل تحديث الدور');
        }
      } catch (e) {
        _showErrorSnackBar('حدث خطأ أثناء تحديث الدور: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// حذف دور
  Future<void> _deleteRole(UserRole role) async {
    // لا يمكن حذف الأدوار الافتراضية
    if (!role.isCustom) {
      _showErrorSnackBar('لا يمكن حذف الأدوار الافتراضية');
      return;
    }

    final confirmed = await AkConfirmDialog.show(
      context: context,
      title: 'حذف الدور',
      content: 'هل أنت متأكد من حذف الدور ${role.displayName}؟',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
      type: AkDialogType.danger,
      icon: Icons.delete_forever,
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final success = await _rolePresenter.deleteRole(role.id);

        if (success) {
          _showSuccessSnackBar('تم حذف الدور بنجاح');
        } else {
          _showErrorSnackBar('فشل حذف الدور');
        }
      } catch (e) {
        _showErrorSnackBar('حدث خطأ أثناء حذف الدور: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// إدارة صلاحيات الدور
  Future<void> _manageRolePermissions(UserRole role) async {
    await showDialog(
      context: context,
      builder: (context) => RolePermissionsDialog(role: role),
    );

    // إعادة تحميل البيانات بعد تحديث الصلاحيات
    _loadData();
  }

  /// الحصول على الأدوار المفلترة
  List<UserRole> _getFilteredRoles() {
    if (_searchQuery.isEmpty) {
      return _rolePresenter.roles;
    }

    return _rolePresenter.roles.where((role) {
      final name = role.name.toLowerCase();
      final displayName = role.displayName.toLowerCase();
      final description = role.description?.toLowerCase() ?? '';
      final permissionsCount = role.permissions.length.toString();
      final query = _searchQuery.toLowerCase();

      return name.contains(query) ||
          displayName.contains(query) ||
          description.contains(query) ||
          permissionsCount.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //////////////////////////////////////////
      /*  appBar: AppBarWidget(
        title: 'إدارة الأدوار',
        actions: [
          // زر إعادة تعيين الأدوار الافتراضية
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'إعادة تعيين الأدوار الافتراضية',
            onPressed: _resetDefaultRoles,
          ),
        ],
      ), */
      appBar: AkAppBar(
        title: 'إدارة الأدوار',
        actions: [
          // أيقونة البحث
          IconButton(
            icon: Icon(_showSearchField ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                  _searchQuery = '';
                }
              });
            },
            tooltip: _showSearchField ? 'إغلاق البحث' : 'البحث',
          ),
          // أيقونة إعادة تعيين الأدوار الافتراضية
          IconButton(
            onPressed: _resetDefaultRoles,
            icon: const Icon(Icons.refresh),
            tooltip: 'إعادة تعيين الأدوار الافتراضية',
          ),
        ],
      ),
      /////////////////////////
      body: _buildContent(),
      floatingActionButton: AkFloatingButton(
        icon: Icons.add,
        onPressed: _addRole,
        tooltip: 'إضافة دور جديد',
        type: AkButtonType.primary,
      ),
    );
  }

  /// إعادة تعيين الأدوار الافتراضية
  Future<void> _resetDefaultRoles() async {
    // عرض رسالة تأكيد مفصلة
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning_amber_rounded,
                color: AppColors.warning, size: 28),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                'إعادة تعيين الأدوار الافتراضية',
                style: AppTypography(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'هذا الإجراء سيقوم بـ:',
              style: AppTypography(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            _buildResetWarningItem('حذف جميع التعديلات المخصصة على الصلاحيات'),
            _buildResetWarningItem(
                'إعادة تطبيق الصلاحيات الافتراضية من النظام'),
            _buildResetWarningItem('استعادة جميع الأدوار إلى حالتها الأصلية'),
            _buildResetWarningItem('إصلاح أي مشاكل في الصلاحيات'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.warning.withValues(alpha: 0.3),
                ),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info_outline, color: AppColors.warning, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'لا يمكن التراجع عن هذا الإجراء',
                      style: AppTypography(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.warning,
              foregroundColor: AppColors.onPrimary,
            ),
            child: const Text('تأكيد الإعادة'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _performRoleReset();
    }
  }

  /// بناء عنصر تحذير في قائمة إعادة التعيين
  Widget _buildResetWarningItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 6,
            height: 6,
            decoration: const BoxDecoration(
              color: AppColors.warning,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: const AppTypography(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// تنفيذ عملية إعادة تعيين الأدوار
  Future<void> _performRoleReset() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // عرض مؤشر تقدم مفصل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(
                'جاري إعادة تعيين الأدوار الافتراضية...',
                style: AppTypography(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                'يرجى الانتظار، قد تستغرق هذه العملية بضع ثوانٍ',
                style: AppTypography(
                  fontSize: 14,
                  color: AppColors.lightTextSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );

      // تنفيذ إعادة التعيين
      final success = await _rolePresenter.resetDefaultRoles();

      // إغلاق مؤشر التقدم
      if (mounted) {
        Navigator.pop(context);
      }

      if (success) {
        // إعادة تحميل البيانات
        await _initializeRoles();

        _showSuccessSnackBar('✅ تم إعادة تعيين الأدوار الافتراضية بنجاح\n'
            'تم استعادة جميع الصلاحيات إلى حالتها الأصلية');
      } else {
        _showErrorSnackBar('❌ فشل في إعادة تعيين الأدوار الافتراضية');
      }
    } catch (e) {
      // إغلاق مؤشر التقدم في حالة الخطأ
      if (mounted) {
        Navigator.pop(context);
      }

      _showErrorSnackBar('❌ حدث خطأ أثناء إعادة تعيين الأدوار: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    if (_isLoading) {
      return const AkLoadingIndicator();
    }

    return CustomScrollView(
      slivers: [
        // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث)
        if (_showSearchField)
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: DynamicColors.surface(context),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryContainer.withValues(alpha: 0.05),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: AkSearchInput(
                controller: _searchController,
                hint: 'البحث في الأدوار (الاسم، الوصف، عدد الصلاحيات)...',
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
                onClear: () {
                  setState(() {
                    _searchController.clear();
                    _searchQuery = '';
                  });
                },
              ),
            ),
          ),

        // شريط الإحصائيات
        SliverToBoxAdapter(
          child: _buildStatsSection(),
        ),

        // قائمة الأدوار
        _buildRolesSliverList(),
      ],
    );
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatsSection() {
    final roles = _getFilteredRoles();
    final defaultRoles = roles.where((role) => !role.isCustom).toList();
    final customRoles = roles.where((role) => role.isCustom).toList();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DynamicColors.surface(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DynamicColors.shadow(context).withValues(alpha: 0.08),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: DynamicColors.shadow(context).withValues(alpha: 0.04),
            spreadRadius: 0,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: DynamicColors.onSurface(context).withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: DynamicColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.analytics_outlined,
                  color: DynamicColors.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'إحصائيات الأدوار',
                style: AppTypography(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: DynamicColors.onSurface(context),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: DynamicColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'المجموع: ${roles.length}',
                  style: AppTypography(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: DynamicColors.primary,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // البطاقات الإحصائية الأفقية مع التمرير
          SizedBox(
            height: 85, // ارتفاع أصغر للبطاقات
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 4),
              itemCount: 4, // عدد البطاقات
              itemBuilder: (context, index) {
                // تحديد بيانات كل بطاقة
                switch (index) {
                  case 0:
                    return _buildHorizontalStatCard(
                      title: 'إجمالي الأدوار',
                      subtitle: 'جميع الأدوار في النظام',
                      count: roles.length,
                      icon: Icons.groups_rounded,
                      gradientColors: [
                        DynamicColors.primary,
                        DynamicColors.primary.withValues(alpha: 0.8),
                      ],
                      isFirst: true,
                    );
                  case 1:
                    return _buildHorizontalStatCard(
                      title: 'الأدوار الافتراضية',
                      subtitle: 'أدوار النظام الأساسية',
                      count: defaultRoles.length,
                      icon: Icons.shield_rounded,
                      gradientColors: [
                        AppColors.success,
                        AppColors.success.withValues(alpha: 0.8),
                      ],
                    );
                  case 2:
                    return _buildHorizontalStatCard(
                      title: 'الأدوار المخصصة',
                      subtitle: 'أدوار مخصصة للمؤسسة',
                      count: customRoles.length,
                      icon: Icons.person_add_alt_1_rounded,
                      gradientColors: [
                        AppColors.info,
                        AppColors.info.withValues(alpha: 0.8),
                      ],
                    );
                  case 3:
                    return _buildHorizontalStatCard(
                      title: 'إجمالي الصلاحيات',
                      subtitle: 'جميع الصلاحيات المتاحة',
                      count: roles.fold<int>(
                          0, (sum, role) => sum + role.permissions.length),
                      icon: Icons.security_rounded,
                      gradientColors: [
                        AppColors.warning,
                        AppColors.warning.withValues(alpha: 0.8),
                      ],
                      isLast: true,
                    );
                  default:
                    return const SizedBox.shrink();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية أفقية محسنة
  Widget _buildHorizontalStatCard({
    required String title,
    required String subtitle,
    required int count,
    required IconData icon,
    required List<Color> gradientColors,
    bool isFirst = false,
    bool isLast = false,
  }) {
    return Container(
      width: 150, // عرض أصغر للبطاقة
      margin: EdgeInsets.only(
        left: isFirst ? 12 : 6,
        right: isLast ? 12 : 6,
      ),
      child: TweenAnimationBuilder<double>(
        duration: Duration(milliseconds: 500 + (count * 20).clamp(0, 200)),
        tween: Tween(begin: 0.0, end: 1.0),
        curve: Curves.easeOutBack,
        builder: (context, value, child) {
          return Transform.scale(
            scale: 0.95 + (0.05 * value),
            child: Opacity(
              opacity: value.clamp(0.0, 1.0),
              child: Container(
                height: 75, // ارتفاع أصغر للبطاقات الأفقية
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: gradientColors,
                    stops: const [0.0, 1.0],
                  ),
                  borderRadius: BorderRadius.circular(14),
                  boxShadow: [
                    BoxShadow(
                      color: gradientColors.first.withValues(alpha: 0.25),
                      spreadRadius: 0,
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                    BoxShadow(
                      color: gradientColors.first.withValues(alpha: 0.1),
                      spreadRadius: 0,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(14),
                    onTap: () {
                      // يمكن إضافة وظيفة عند النقر على البطاقة
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // الصف العلوي: الأيقونة والعدد
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // الأيقونة
                              Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Icon(
                                  icon,
                                  color: Colors.white,
                                  size: 14,
                                ),
                              ),

                              // العدد مع انيميشن
                              TweenAnimationBuilder<int>(
                                duration: const Duration(milliseconds: 800),
                                tween: IntTween(begin: 0, end: count),
                                curve: Curves.easeOutCubic,
                                builder: (context, animatedCount, child) {
                                  return Text(
                                    '$animatedCount',
                                    style: const AppTypography(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),

                          const Spacer(),

                          // النصوص
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                title,
                                style: const AppTypography(
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 1),
                              Text(
                                subtitle,
                                style: AppTypography(
                                  fontSize: 8,
                                  color: Colors.white.withValues(alpha: 0.8),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// بناء قائمة الأدوار كـ Sliver
  Widget _buildRolesSliverList() {
    final roles = _getFilteredRoles();

    if (roles.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: AkEmptyState(
            message: _searchQuery.isEmpty
                ? 'لا يوجد أدوار'
                : 'لا يوجد أدوار تطابق البحث "$_searchQuery"',
            icon: _searchQuery.isEmpty ? Icons.people : Icons.search_off,
            onRefresh: _searchQuery.isEmpty ? _loadData : null,
          ),
        ),
      );
    }

    // تقسيم الأدوار إلى مجموعات
    final defaultRoles = roles.where((role) => !role.isCustom).toList();
    final customRoles = roles.where((role) => role.isCustom).toList();

    return SliverList(
      delegate: SliverChildListDelegate([
        const SizedBox(height: 16),

        // الأدوار الافتراضية
        if (defaultRoles.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _buildSectionHeader(
              title: 'الأدوار الافتراضية',
              icon: Icons.shield,
              color: AppColors.lightTextSecondary,
              count: defaultRoles.length,
            ),
          ),
          const SizedBox(height: 8),
          ...defaultRoles
              .map((role) => Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    child: _buildRoleCard(role),
                  ))
              .toList(),
          const SizedBox(height: 24),
        ],

        // الأدوار المخصصة
        if (customRoles.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _buildSectionHeader(
              title: 'الأدوار المخصصة',
              icon: Icons.person_add,
              color: AppColors.lightTextSecondary,
              count: customRoles.length,
            ),
          ),
          const SizedBox(height: 8),
          ...customRoles
              .map((role) => Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    child: _buildRoleCard(role),
                  ))
              .toList(),
        ],

        // مساحة إضافية في الأسفل
        const SizedBox(height: 100),
      ]),
    );
  }

  /// بناء رأس القسم
  Widget _buildSectionHeader({
    required String title,
    required IconData icon,
    required Color color,
    required int count,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: const AppTypography(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            '$count',
            style: AppTypography(
              fontSize: 14,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const Spacer(),
        if (title == 'الأدوار المخصصة')
          TextButton.icon(
            onPressed: _addRole,
            icon: const Icon(Icons.add, size: 18),
            label: const Text('إضافة دور'),
            style: TextButton.styleFrom(
              foregroundColor: color,
            ),
          ),
      ],
    );
  }

  /// بناء بطاقة الدور
  Widget _buildRoleCard(UserRole role) {
    // تحديد لون الدور
    final Color roleColor = _getRoleColor(role);

    // تحديد أيقونة الدور
    final IconData roleIcon = _getRoleIcon(role);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: role.isCustom
              ? AppColors.lightSurfaceVariant
              : roleColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                // أيقونة الدور
                CircleAvatar(
                  backgroundColor: roleColor.withValues(alpha: 0.1),
                  radius: 24,
                  child: Icon(
                    roleIcon,
                    color: roleColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // معلومات الدور
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // اسم الدور
                      Text(
                        role.displayName,
                        style: const AppTypography(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      // الاسم التقني
                      Text(
                        'الاسم التقني: ${role.name}',
                        style: AppTypography(
                          fontSize: 14,
                          color: DynamicColors.onSurface(context)
                              .withValues(alpha: 0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

                // شارة الدور المخصص
                if (role.isCustom)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.info.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                          color: AppColors.info.withValues(alpha: 0.3)),
                    ),
                    child: const Text(
                      'مخصص',
                      style: AppTypography(
                        fontSize: 12,
                        color: AppColors.info,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                else
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: roleColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border:
                          Border.all(color: roleColor.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      'افتراضي',
                      style: AppTypography(
                        fontSize: 12,
                        color: roleColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 16),
            const Divider(height: 1),
            const SizedBox(height: 16),

            // معلومات الصلاحيات
            Row(
              children: [
                // عدد الصلاحيات
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'عدد الصلاحيات',
                        style: AppTypography(
                          fontSize: 14,
                          color: DynamicColors.onSurface(context)
                              .withValues(alpha: 0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${role.permissions.length}',
                        style: const AppTypography(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

                // أزرار الإجراءات
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // زر إدارة الصلاحيات
                    ElevatedButton.icon(
                      onPressed: () => _manageRolePermissions(role),
                      icon: const Icon(Icons.security, size: 18),
                      label: const Text('إدارة الصلاحيات'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: roleColor,
                        foregroundColor: AppColors.onPrimary,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // زر التعديل (فقط للأدوار المخصصة)
                    if (role.isCustom)
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () => _editRole(role),
                        tooltip: 'تعديل',
                        style: IconButton.styleFrom(
                          backgroundColor: AppColors.infoLight,
                          foregroundColor: AppColors.info,
                        ),
                      ),

                    // زر الحذف (فقط للأدوار المخصصة)
                    if (role.isCustom)
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _deleteRole(role),
                        tooltip: 'حذف',
                        style: IconButton.styleFrom(
                          backgroundColor: AppColors.errorLight,
                          foregroundColor: AppColors.error,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون الدور
  Color _getRoleColor(UserRole role) {
    // تعريف قاموس للألوان حسب نوع الدور باستخدام الألوان الديناميكية
    final Map<String, Color> roleColors = {
      // أدوار الإدارة
      'owner': DynamicColors.primaryDark,
      'مالك': DynamicColors.primaryDark,
      'admin': DynamicColors.primary,
      'مدير': DynamicColors.primary,
      'administrator': DynamicColors.primary,
      'مدير النظام': DynamicColors.primary,

      // أدوار الإشراف
      'manager': DynamicColors.primaryLight,
      'مشرف': DynamicColors.primaryLight,
      'supervisor': DynamicColors.primaryLight,

      // أدوار المحاسبة
      'accountant': AppColors.accent,
      'محاسب': AppColors.accent,
      'finance': AppColors.accent,
      'مالية': AppColors.accent,

      // أدوار الكاشير
      'cashier': AppColors.success,
      'كاشير': AppColors.success,
      'أمين صندوق': AppColors.success,

      // أدوار المبيعات
      'sales': AppColors.warning,
      'مبيعات': AppColors.warning,
      'salesperson': AppColors.warning,
      'مندوب مبيعات': AppColors.warning,

      // أدوار المخزون
      'inventory': AppColors.info,
      'مخزون': AppColors.info,
      'stock': AppColors.info,
      'مخازن': AppColors.info,
      'warehouse': AppColors.info,

      // أدوار المستخدمين العاديين
      'user': AppColors.secondary,
      'مستخدم': AppColors.secondary,
      'viewer': AppColors.secondary,
      'مشاهد': AppColors.secondary,
    };

    // تحويل اسم الدور واسم العرض إلى حروف صغيرة للمقارنة
    final roleName = role.name.toLowerCase();
    final roleDisplayName = role.displayName.toLowerCase();

    // البحث عن اللون المناسب في القاموس
    for (final entry in roleColors.entries) {
      final key = entry.key.toLowerCase();
      if (roleName.contains(key) || roleDisplayName.contains(key)) {
        return entry.value;
      }
    }

    // استخدام لون مختلف للأدوار المخصصة
    if (role.isCustom) {
      return DynamicColors.primaryLight;
    }

    // لون افتراضي للأدوار غير المعروفة
    return AppColors.secondary;
  }

  /// الحصول على أيقونة الدور
  IconData _getRoleIcon(UserRole role) {
    // تعريف قاموس للأيقونات حسب نوع الدور
    final Map<String, IconData> roleIcons = {
      // أدوار الإدارة
      'owner': Icons.admin_panel_settings,
      'مالك': Icons.admin_panel_settings,
      'admin': Icons.admin_panel_settings,
      'مدير': Icons.admin_panel_settings,
      'administrator': Icons.admin_panel_settings,
      'مدير النظام': Icons.admin_panel_settings,

      // أدوار الإشراف
      'manager': Icons.manage_accounts,
      'مشرف': Icons.manage_accounts,
      'supervisor': Icons.manage_accounts,

      // أدوار المحاسبة
      'accountant': Icons.account_balance,
      'محاسب': Icons.account_balance,
      'finance': Icons.account_balance,
      'مالية': Icons.account_balance,

      // أدوار الكاشير
      'cashier': Icons.point_of_sale,
      'كاشير': Icons.point_of_sale,
      'أمين صندوق': Icons.point_of_sale,

      // أدوار المبيعات
      'sales': Icons.shopping_cart,
      'مبيعات': Icons.shopping_cart,
      'salesperson': Icons.shopping_cart,
      'مندوب مبيعات': Icons.shopping_cart,

      // أدوار المخزون
      'inventory': Icons.inventory,
      'مخزون': Icons.inventory,
      'stock': Icons.inventory_2,
      'مخازن': Icons.inventory_2,
      'warehouse': Icons.warehouse,

      // أدوار المستخدمين العاديين
      'user': Icons.person,
      'مستخدم': Icons.person,
      'viewer': Icons.visibility,
      'مشاهد': Icons.visibility,
    };

    // تحويل اسم الدور واسم العرض إلى حروف صغيرة للمقارنة
    final roleName = role.name.toLowerCase();
    final roleDisplayName = role.displayName.toLowerCase();

    // البحث عن الأيقونة المناسبة في القاموس
    for (final entry in roleIcons.entries) {
      final key = entry.key.toLowerCase();
      if (roleName.contains(key) || roleDisplayName.contains(key)) {
        return entry.value;
      }
    }

    // استخدام أيقونة مختلفة للأدوار المخصصة
    if (role.isCustom) {
      return Icons.person_add;
    }

    // أيقونة افتراضية للأدوار غير المعروفة
    return Icons.person;
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
        ),
      );
    });
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.success,
        ),
      );
    });
  }
}
