import '../../../core/database/database_service.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/models/sale.dart';
import '../../../core/models/sale_item.dart';
import '../../accounts/services/accounting_integration_service.dart';

/// خدمة إدارة فواتير المبيعات
class InvoiceService {
  // Singleton pattern
  static final InvoiceService _instance = InvoiceService._internal();
  factory InvoiceService() => _instance;
  InvoiceService._internal();

  final DatabaseService _db = DatabaseService.instance;
  final AccountingIntegrationService _accountingService =
      AccountingIntegrationService();

  /// إنشاء فاتورة مبيعات جديدة
  Future<String> createInvoice(Sale invoice, {String? userId}) async {
    try {
      AppLogger.info('بدء إنشاء فاتورة مبيعات جديدة');

      // إعداد تفاصيل الفاتورة
      final invoiceMap = invoice.toMap();
      invoiceMap['created_by'] = userId;

      // إدخال الفاتورة في قاعدة البيانات
      await _db.insert(
        'sales_invoices',
        invoiceMap,
      );

      // إدخال بنود الفاتورة في قاعدة البيانات
      for (final item in invoice.items) {
        final itemMap = item.toMap();
        itemMap['invoice_id'] = invoice.id;

        await _db.insert(
          'sales_invoice_items',
          itemMap,
        );
      }

      // إنشاء القيود المحاسبية المرتبطة بالفاتورة
      await _accountingService.handleSalesInvoice(invoiceMap, userId: userId);

      AppLogger.info(
          'تم إنشاء فاتورة المبيعات بنجاح مع رقم: ${invoice.saleNumber}');

      return invoice.id;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إنشاء فاتورة مبيعات',
        error: e,
        stackTrace: stackTrace,
        context: {'invoiceData': invoice.toMap()},
      );
      rethrow;
    }
  }

  /// الحصول على فاتورة مبيعات بواسطة المعرف
  Future<Sale?> getInvoiceById(String id) async {
    try {
      AppLogger.info('جاري البحث عن فاتورة المبيعات برقم تعريف: $id');

      // الحصول على بيانات الفاتورة
      final invoiceMap = await _db.getById('sales_invoices', id);

      if (invoiceMap == null) {
        return null;
      }

      // الحصول على بنود الفاتورة
      final itemsList = await _db.query(
        'sales_invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [id],
      );

      final items = itemsList.map((item) => SaleItem.fromMap(item)).toList();

      return Sale.fromMap(invoiceMap, items: items);
    } catch (e) {
      AppLogger.error('خطأ في الحصول على فاتورة المبيعات: $e');
      return null;
    }
  }

  /// الحصول على فاتورة مبيعات بواسطة رقم الفاتورة
  Future<Sale?> getInvoiceByNumber(String invoiceNumber) async {
    try {
      AppLogger.info('جاري البحث عن فاتورة المبيعات برقم: $invoiceNumber');

      // الحصول على بيانات الفاتورة
      final invoiceList = await _db.query(
        'sales_invoices',
        where: 'sale_number = ?',
        whereArgs: [invoiceNumber],
      );

      if (invoiceList.isEmpty) {
        return null;
      }

      final invoiceMap = invoiceList.first;

      // الحصول على بنود الفاتورة
      final itemsList = await _db.query(
        'sales_invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [invoiceMap['id']],
      );

      final items = itemsList.map((item) => SaleItem.fromMap(item)).toList();

      return Sale.fromMap(invoiceMap, items: items);
    } catch (e) {
      AppLogger.error('خطأ في الحصول على فاتورة المبيعات برقم: $e');
      return null;
    }
  }

  /// تحديث حالة فاتورة المبيعات
  Future<bool> updateInvoiceStatus(String id, String status,
      {String? userId}) async {
    try {
      AppLogger.info('تحديث حالة فاتورة المبيعات رقم: $id إلى: $status');

      // تحديث حالة الفاتورة
      await _db.update(
        'sales_invoices',
        {'status': status},
        where: 'id = ?',
        whereArgs: [id],
      );

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تحديث حالة فاتورة المبيعات: $e');
      return false;
    }
  }

  /// تحديث حالة الدفع لفاتورة المبيعات
  Future<bool> updatePaymentStatus(String id, String paymentStatus,
      {String? paymentMethod, String? userId}) async {
    try {
      AppLogger.info(
          'تحديث حالة دفع فاتورة المبيعات رقم: $id إلى: $paymentStatus');

      // تحديث حالة الدفع
      final updateData = {
        'payment_status': paymentStatus,
        'payment_method': paymentMethod,
      };

      // حذف القيم الفارغة
      updateData.removeWhere((key, value) => value == null);

      await _db.update(
        'sales_invoices',
        updateData,
        where: 'id = ?',
        whereArgs: [id],
      );

      // الحصول على الفاتورة بعد التحديث
      final invoice = await getInvoiceById(id);

      if (invoice != null && paymentStatus == 'paid') {
        // إنشاء قيد القبض إذا كانت الحالة مدفوعة
        final invoiceMap = invoice.toMap();
        await _accountingService.handleSalesInvoice(invoiceMap, userId: userId);
      }

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تحديث حالة دفع فاتورة المبيعات: $e');
      return false;
    }
  }

  /// الحصول على قائمة الفواتير
  Future<List<Sale>> getAllInvoices({
    DateTime? startDate,
    DateTime? endDate,
    String? status,
    String? customerId,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      AppLogger.info('جاري الحصول على قائمة فواتير المبيعات');

      // بناء شروط الاستعلام
      final whereConditions = <String>[];
      final whereArgs = <dynamic>[];

      if (startDate != null) {
        whereConditions.add('date >= ?');
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereConditions.add('date <= ?');
        whereArgs.add(endDate.toIso8601String());
      }

      if (status != null) {
        whereConditions.add('status = ?');
        whereArgs.add(status);
      }

      if (customerId != null) {
        whereConditions.add('customer_id = ?');
        whereArgs.add(customerId);
      }

      // تنفيذ الاستعلام
      final whereClause =
          whereConditions.isNotEmpty ? whereConditions.join(' AND ') : null;

      final invoicesList = await _db.query(
        'sales_invoices',
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'date DESC',
        limit: limit,
        offset: offset,
      );

      // جمع كل فواتير المبيعات مع بنودها
      final invoices = <Sale>[];

      for (final invoiceMap in invoicesList) {
        final id = invoiceMap['id'] as String;

        // الحصول على بنود الفاتورة
        final itemsList = await _db.query(
          'sales_invoice_items',
          where: 'invoice_id = ?',
          whereArgs: [id],
        );

        final items = itemsList.map((item) => SaleItem.fromMap(item)).toList();

        invoices.add(Sale.fromMap(invoiceMap, items: items));
      }

      return invoices;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على قائمة فواتير المبيعات: $e');
      return [];
    }
  }

  /// احتساب إجمالي المبيعات للفترة
  Future<double> calculateTotalSales({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      AppLogger.info(
          'احتساب إجمالي المبيعات للفترة من ${startDate.toIso8601String()} إلى ${endDate.toIso8601String()}');

      // الاستعلام عن مجموع المبيعات
      final result = await _db.rawQuery('''
        SELECT SUM(total) as total_sales
        FROM sales_invoices
        WHERE date >= ? AND date <= ? AND status != 'cancelled'
      ''', [
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ]);

      if (result.isNotEmpty && result.first['total_sales'] != null) {
        final totalSales = result.first['total_sales'] as double;
        return totalSales;
      }

      return 0.0;
    } catch (e) {
      AppLogger.error('خطأ في احتساب إجمالي المبيعات: $e');
      return 0.0;
    }
  }

  /// إنشاء رقم فاتورة جديد
  Future<String> generateInvoiceNumber() async {
    try {
      final today = DateTime.now();
      final year = today.year.toString().substring(2);
      final month = today.month.toString().padLeft(2, '0');
      final prefix = 'INV-$year$month-';

      // الحصول على أعلى رقم فاتورة موجود
      final result = await _db.rawQuery('''
        SELECT sale_number
        FROM sales_invoices
        WHERE sale_number LIKE ?
        ORDER BY sale_number DESC LIMIT 1
      ''', ['$prefix%']);

      int number = 1;

      if (result.isNotEmpty) {
        final lastNumber = result.first['sale_number'] as String;
        final lastDigits = lastNumber.substring(prefix.length);
        number = int.parse(lastDigits) + 1;
      }

      return '$prefix${number.toString().padLeft(4, '0')}';
    } catch (e) {
      AppLogger.error('خطأ في إنشاء رقم فاتورة جديد: $e');
      // رقم افتراضي في حالة الخطأ
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      return 'INV-$timestamp';
    }
  }

  /// حذف فاتورة مبيعات (مع إلغاء القيود المحاسبية المرتبطة)
  Future<bool> deleteInvoice(String id, {String? userId}) async {
    try {
      AppLogger.info('بدء حذف فاتورة المبيعات رقم: $id');

      // التحقق من وجود الفاتورة
      final invoice = await getInvoiceById(id);

      if (invoice == null) {
        return false;
      }

      // إلغاء القيود المحاسبية المرتبطة أولاً
      // يمكننا تنفيذ هذا عندما يتم تطوير دالة الإلغاء في AccountingEngine
      await _accountingService.reverseEntriesForInvoice(id, 'sales',
          userId: userId);

      // حذف بنود الفاتورة
      await _db.delete(
        'sales_invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [id],
      );

      // حذف الفاتورة
      await _db.delete(
        'sales_invoices',
        where: 'id = ?',
        whereArgs: [id],
      );

      AppLogger.info('تم حذف فاتورة المبيعات بنجاح');

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف فاتورة مبيعات',
        error: e,
        stackTrace: stackTrace,
        context: {'invoiceId': id},
      );

      return false;
    }
  }
}
