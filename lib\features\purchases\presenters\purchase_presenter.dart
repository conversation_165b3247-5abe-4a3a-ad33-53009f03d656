import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../../core/database/database_service.dart';
import '../../../core/models/purchase.dart';
import '../../../core/models/supplier.dart';

import '../../../core/models/warehouse.dart';
import '../../../core/utils/error_tracker.dart';

/// مقدم المشتريات - يدير عمليات المشتريات والتفاعل مع قاعدة البيانات
class PurchasePresenter with ChangeNotifier {
  final DatabaseService _db;
  List<Purchase> _purchases = [];
  String? _error;
  bool _isLoading = false;
  String? _searchQuery;
  String? _selectedStatus;
  DateTimeRange? _dateRange;

  PurchasePresenter({DatabaseService? db})
      : _db = db ?? DatabaseService.instance;

  // Getters
  List<Purchase> get purchases => _purchases;
  String? get error => _error;
  bool get isLoading => _isLoading;
  String? get searchQuery => _searchQuery;
  String? get selectedStatus => _selectedStatus;
  DateTimeRange? get dateRange => _dateRange;

  // Setters
  set searchQuery(String? query) {
    _searchQuery = query;
    loadPurchases();
  }

  set selectedStatus(String? status) {
    _selectedStatus = status;
    loadPurchases();
  }

  set dateRange(DateTimeRange? range) {
    _dateRange = range;
    loadPurchases();
  }

  /// تهيئة المقدم
  Future<void> init() async {
    await loadPurchases();
  }

  /// تحميل فواتير المشتريات
  Future<void> loadPurchases() async {
    _setLoading(true);
    _error = null;

    try {
      final db = await _db.database;
      String whereClause = 'isDeleted = 0';
      List<dynamic> whereArgs = [];

      if (_searchQuery != null && _searchQuery!.isNotEmpty) {
        whereClause += ' AND (reference_number LIKE ? OR notes LIKE ?)';
        whereArgs.addAll(['%$_searchQuery%', '%$_searchQuery%']);
      }

      if (_selectedStatus != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(_selectedStatus);
      }

      if (_dateRange != null) {
        whereClause += ' AND date >= ?';
        whereArgs.add(_dateRange!.start.toIso8601String());

        whereClause += ' AND date <= ?';
        whereArgs.add(_dateRange!.end.toIso8601String());
      }

      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseService.tableInvoices,
        where: 'invoice_type = "purchase" AND $whereClause',
        whereArgs: whereArgs,
        orderBy: 'date DESC',
      );

      _purchases = [];
      for (var map in maps) {
        // الحصول على عناصر الفاتورة
        final itemsMaps = await db.query(
          DatabaseService.tableInvoiceItems,
          where: 'invoice_id = ?',
          whereArgs: [map['id']],
        );

        final items =
            itemsMaps.map((itemMap) => PurchaseItem.fromMap(itemMap)).toList();

        // الحصول على بيانات المورد والمستودع إذا كانت متوفرة
        Supplier? supplier;
        Warehouse? warehouse;

        if (map['supplier_id'] != null) {
          final supplierMaps = await db.query(
            DatabaseService.tableSuppliers,
            where: 'id = ?',
            whereArgs: [map['supplier_id']],
            limit: 1,
          );

          if (supplierMaps.isNotEmpty) {
            supplier = Supplier.fromMap(supplierMaps.first);
          }
        }

        if (map['warehouse_id'] != null) {
          final warehouseMaps = await db.query(
            DatabaseService.tableWarehouses,
            where: 'id = ?',
            whereArgs: [map['warehouse_id']],
            limit: 1,
          );

          if (warehouseMaps.isNotEmpty) {
            warehouse = Warehouse.fromMap(warehouseMaps.first);
          }
        }

        // إنشاء كائن الفاتورة مع البيانات المرتبطة
        final purchase = Purchase.fromMap({
          ...map,
          'items': items.map((item) => item.toMap()).toList(),
          'supplier': supplier?.toMap(),
          'warehouse': warehouse?.toMap(),
        });

        _purchases.add(purchase);
      }

      notifyListeners();
    } catch (e, stackTrace) {
      _setError('فشل تحميل فواتير المشتريات: ${e.toString()}');
      ErrorTracker.captureError(
        'Failed to load purchases',
        error: e,
        stackTrace: stackTrace,
        context: {'presenter': 'PurchasePresenter'},
      );
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على فاتورة مشتريات بواسطة المعرف
  Future<Purchase?> getPurchaseById(String id) async {
    try {
      final db = await _db.database;
      final maps = await db.query(
        DatabaseService.tableInvoices,
        where: 'id = ? AND invoice_type = "purchase"',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isEmpty) return null;

      // الحصول على عناصر الفاتورة
      final itemsMaps = await db.query(
        DatabaseService.tableInvoiceItems,
        where: 'invoice_id = ?',
        whereArgs: [id],
      );

      final items =
          itemsMaps.map((itemMap) => PurchaseItem.fromMap(itemMap)).toList();

      // الحصول على بيانات المورد والمستودع إذا كانت متوفرة
      Supplier? supplier;
      Warehouse? warehouse;

      if (maps.first['supplier_id'] != null) {
        final supplierMaps = await db.query(
          DatabaseService.tableSuppliers,
          where: 'id = ?',
          whereArgs: [maps.first['supplier_id']],
          limit: 1,
        );

        if (supplierMaps.isNotEmpty) {
          supplier = Supplier.fromMap(supplierMaps.first);
        }
      }

      if (maps.first['warehouse_id'] != null) {
        final warehouseMaps = await db.query(
          DatabaseService.tableWarehouses,
          where: 'id = ?',
          whereArgs: [maps.first['warehouse_id']],
          limit: 1,
        );

        if (warehouseMaps.isNotEmpty) {
          warehouse = Warehouse.fromMap(warehouseMaps.first);
        }
      }

      // إنشاء كائن الفاتورة مع البيانات المرتبطة
      return Purchase.fromMap({
        ...maps.first,
        'items': items.map((item) => item.toMap()).toList(),
        'supplier': supplier?.toMap(),
        'warehouse': warehouse?.toMap(),
      });
    } catch (e, stackTrace) {
      _setError('فشل الحصول على فاتورة المشتريات: ${e.toString()}');
      ErrorTracker.captureError(
        'Failed to get purchase by ID',
        error: e,
        stackTrace: stackTrace,
        context: {'presenter': 'PurchasePresenter', 'purchaseId': id},
      );
      return null;
    }
  }

  /// إنشاء رقم مرجعي جديد للفاتورة
  Future<String> _generateReferenceNumber() async {
    try {
      final db = await _db.database;
      final result = await db.rawQuery('''
        SELECT MAX(CAST(SUBSTR(invoice_number, 4) AS INTEGER)) as max_number
        FROM invoices
        WHERE invoice_type = 'purchase' AND invoice_number LIKE 'PO-%'
      ''');

      int lastNumber = 0;
      if (result.isNotEmpty && result.first['max_number'] != null) {
        lastNumber = int.parse(result.first['max_number'].toString());
      }

      return 'PO-${(lastNumber + 1).toString().padLeft(5, '0')}';
    } catch (e) {
      // في حالة الفشل، استخدم الطابع الزمني
      return 'PO-${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// تعيين رسالة الخطأ
  void _setError(String message) {
    _error = message;
    notifyListeners();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// إضافة فاتورة مشتريات جديدة
  Future<String?> addPurchase(Purchase purchase) async {
    _setLoading(true);
    _error = null;

    try {
      // إنشاء رقم مرجعي للفاتورة إذا لم يكن موجوداً
      final referenceNumber =
          purchase.referenceNumber ?? await _generateReferenceNumber();

      // إنشاء نسخة من الفاتورة مع الرقم المرجعي
      // إنشاء نسخة من الفاتورة مع الرقم المرجعي ومعرف جديد إذا لم يكن موجوداً
      final String id = purchase.id.isEmpty ? const Uuid().v4() : purchase.id;
      final now = DateTime.now();
      final purchaseWithRef = purchase.copyWith(
        referenceNumber: referenceNumber,
        id: id,
      );

      // حفظ الفاتورة في قاعدة البيانات
      final db = await _db.database;
      final purchaseMap = purchaseWithRef.toMap();

      // إضافة الطوابع الزمنية للماب الذي سيتم حفظه في قاعدة البيانات
      purchaseMap['created_at'] = now.toIso8601String();
      purchaseMap['updated_at'] = now.toIso8601String();

      // إزالة البيانات التي سيتم تخزينها بشكل منفصل
      purchaseMap.remove('items');
      purchaseMap.remove('metadata');

      // تحويل فاتورة المشتريات إلى فاتورة عامة
      final invoiceMap = {
        'id': purchaseWithRef.id,
        'invoice_number': purchaseWithRef.referenceNumber,
        'invoice_type': 'purchase',
        'supplier_id': purchaseWithRef.supplierId,
        'warehouse_id': purchaseWithRef.warehouseId,
        'date': purchaseWithRef.date.toIso8601String(),
        'due_date': purchaseWithRef.dueDate?.toIso8601String(),
        'status': purchaseWithRef.status,
        'subtotal': purchaseWithRef.subtotal,
        'discount_type':
            purchaseWithRef.isDiscountPercentage ? 'percentage' : 'amount',
        'discount_value': purchaseWithRef.discount,
        'tax_amount': purchaseWithRef.tax,
        'total': purchaseWithRef.total,
        'paid_amount': purchaseWithRef.paid,
        'balance': purchaseWithRef.remaining ??
            (purchaseWithRef.total - purchaseWithRef.paid),
        'notes': purchaseWithRef.notes,
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
        'is_deleted': 0,
      };

      // حفظ الفاتورة في جدول الفواتير الموحد
      await db.insert(DatabaseService.tableInvoices, invoiceMap);

      // حفظ عناصر الفاتورة في جدول بنود الفواتير الموحد
      for (var item in purchaseWithRef.items) {
        final invoiceItemMap = {
          'id': const Uuid().v4(),
          'invoice_id': purchaseWithRef.id,
          'product_id': item.productId,
          'description': item.productName ?? '',
          'quantity': item.quantity,
          'unit_id': item.unitId,
          'unit_price': item.price,
          'discount_type': item.isDiscountPercentage ? 'percentage' : 'amount',
          'discount_value': item.discount,
          'tax_rate': item.taxRate,
          'tax_amount': item.taxAmount,
          'subtotal': item.subtotal,
          'total': item.total,
          'created_at': now.toIso8601String(),
          'updated_at': now.toIso8601String(),
          'is_deleted': 0,
        };

        await db.insert(DatabaseService.tableInvoiceItems, invoiceItemMap);
      }

      // تحديث المخزون
      await _updateInventory(purchaseWithRef);

      // إعادة تحميل الفواتير
      await loadPurchases();

      return purchaseWithRef.id;
    } catch (e, stackTrace) {
      _setError('فشل إضافة فاتورة المشتريات: ${e.toString()}');
      ErrorTracker.captureError(
        'Failed to add purchase',
        error: e,
        stackTrace: stackTrace,
        context: {'presenter': 'PurchasePresenter'},
      );
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث المخزون بناءً على فاتورة المشتريات
  Future<void> _updateInventory(Purchase purchase) async {
    // لا تقم بتحديث المخزون إذا كانت الفاتورة ملغاة
    if (purchase.status == 'cancelled') return;

    final db = await _db.database;

    // تحديث المخزون لكل عنصر في الفاتورة
    for (var item in purchase.items) {
      try {
        // الحصول على المنتج
        final productMaps = await db.query(
          DatabaseService.tableProducts,
          where: 'id = ?',
          whereArgs: [item.productId],
          limit: 1,
        );

        if (productMaps.isNotEmpty) {
          final productMap = productMaps.first;
          final currentQuantity = productMap['quantity'] as int? ?? 0;

          // زيادة الكمية في المخزون
          await db.update(
            DatabaseService.tableProducts,
            {
              'quantity': currentQuantity + item.quantity,
              'updated_at': DateTime.now().toIso8601String(),
            },
            where: 'id = ?',
            whereArgs: [item.productId],
          );
        }
      } catch (e, stackTrace) {
        ErrorTracker.captureError(
          'Failed to update inventory for product',
          error: e,
          stackTrace: stackTrace,
          context: {
            'presenter': 'PurchasePresenter',
            'productId': item.productId,
            'quantity': item.quantity,
          },
        );
      }
    }
  }

  /// إلغاء تأثير فاتورة المشتريات على المخزون
  Future<void> _revertInventoryChanges(Purchase purchase) async {
    final db = await _db.database;

    // إلغاء تأثير كل عنصر في الفاتورة على المخزون
    for (var item in purchase.items) {
      try {
        // الحصول على المنتج
        final productMaps = await db.query(
          DatabaseService.tableProducts,
          where: 'id = ?',
          whereArgs: [item.productId],
          limit: 1,
        );

        if (productMaps.isNotEmpty) {
          final productMap = productMaps.first;
          final currentQuantity = productMap['quantity'] as int? ?? 0;

          // تقليل الكمية في المخزون (مع التأكد من عدم وجود كميات سالبة)
          await db.update(
            DatabaseService.tableProducts,
            {
              'quantity': (currentQuantity - item.quantity) < 0
                  ? 0
                  : (currentQuantity - item.quantity),
              'updated_at': DateTime.now().toIso8601String(),
            },
            where: 'id = ?',
            whereArgs: [item.productId],
          );
        }
      } catch (e, stackTrace) {
        ErrorTracker.captureError(
          'Failed to revert inventory changes for product',
          error: e,
          stackTrace: stackTrace,
          context: {
            'presenter': 'PurchasePresenter',
            'productId': item.productId,
            'quantity': item.quantity,
          },
        );
      }
    }
  }

  /// تحديث فاتورة مشتريات
  Future<bool> updatePurchase(Purchase purchase) async {
    _setLoading(true);
    _error = null;

    try {
      final db = await _db.database;

      // الحصول على الفاتورة الحالية
      final currentPurchase = await getPurchaseById(purchase.id);
      if (currentPurchase == null) {
        _setError('فاتورة المشتريات غير موجودة');
        return false;
      }

      // إلغاء تأثير الفاتورة الحالية على المخزون
      await _revertInventoryChanges(currentPurchase);

      // تحديث الفاتورة في قاعدة البيانات
      final now = DateTime.now();

      // تحويل فاتورة المشتريات إلى فاتورة عامة
      final invoiceMap = {
        'invoice_number': purchase.referenceNumber,
        'supplier_id': purchase.supplierId,
        'warehouse_id': purchase.warehouseId,
        'date': purchase.date.toIso8601String(),
        'due_date': purchase.dueDate?.toIso8601String(),
        'status': purchase.status,
        'subtotal': purchase.subtotal,
        'discount_type':
            purchase.isDiscountPercentage ? 'percentage' : 'amount',
        'discount_value': purchase.discount,
        'tax_amount': purchase.tax,
        'total': purchase.total,
        'paid_amount': purchase.paid,
        'balance': purchase.remaining ?? (purchase.total - purchase.paid),
        'notes': purchase.notes,
        'updated_at': now.toIso8601String(),
      };

      // تحديث الفاتورة في جدول الفواتير الموحد
      await db.update(
        DatabaseService.tableInvoices,
        invoiceMap,
        where: 'id = ? AND invoice_type = "purchase"',
        whereArgs: [purchase.id],
      );

      // حذف عناصر الفاتورة الحالية
      await db.delete(
        DatabaseService.tableInvoiceItems,
        where: 'invoice_id = ?',
        whereArgs: [purchase.id],
      );

      // إضافة عناصر الفاتورة الجديدة
      for (var item in purchase.items) {
        final invoiceItemMap = {
          'id': const Uuid().v4(),
          'invoice_id': purchase.id,
          'product_id': item.productId,
          'description': item.productName ?? '',
          'quantity': item.quantity,
          'unit_id': item.unitId,
          'unit_price': item.price,
          'discount_type': item.isDiscountPercentage ? 'percentage' : 'amount',
          'discount_value': item.discount,
          'tax_rate': item.taxRate,
          'tax_amount': item.taxAmount,
          'subtotal': item.subtotal,
          'total': item.total,
          'created_at': now.toIso8601String(),
          'updated_at': now.toIso8601String(),
          'is_deleted': 0,
        };

        await db.insert(DatabaseService.tableInvoiceItems, invoiceItemMap);
      }

      // تحديث المخزون
      await _updateInventory(purchase);

      // إعادة تحميل الفواتير
      await loadPurchases();

      return true;
    } catch (e, stackTrace) {
      _setError('فشل تحديث فاتورة المشتريات: ${e.toString()}');
      ErrorTracker.captureError(
        'Failed to update purchase',
        error: e,
        stackTrace: stackTrace,
        context: {'presenter': 'PurchasePresenter', 'purchaseId': purchase.id},
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تغيير حالة فاتورة المشتريات
  Future<bool> changePurchaseStatus(String id, String status) async {
    _setLoading(true);
    _error = null;

    try {
      final db = await _db.database;

      // الحصول على الفاتورة الحالية
      final currentPurchase = await getPurchaseById(id);
      if (currentPurchase == null) {
        _setError('فاتورة المشتريات غير موجودة');
        return false;
      }

      // إذا كانت الحالة الجديدة هي "ملغاة"، قم بإلغاء تأثير الفاتورة على المخزون
      if (status == 'cancelled' && currentPurchase.status != 'cancelled') {
        await _revertInventoryChanges(currentPurchase);
      }

      // إذا كانت الحالة الجديدة هي "مكتملة" والحالة الحالية هي "ملغاة"، قم بتحديث المخزون
      if (status == 'completed' && currentPurchase.status == 'cancelled') {
        await _updateInventory(currentPurchase);
      }

      // تحديث حالة الفاتورة
      await db.update(
        DatabaseService.tableInvoices,
        {
          'status': status,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ? AND invoice_type = "purchase"',
        whereArgs: [id],
      );

      // إعادة تحميل الفواتير
      await loadPurchases();

      return true;
    } catch (e, stackTrace) {
      _setError('فشل تغيير حالة فاتورة المشتريات: ${e.toString()}');
      ErrorTracker.captureError(
        'Failed to change purchase status',
        error: e,
        stackTrace: stackTrace,
        context: {
          'presenter': 'PurchasePresenter',
          'purchaseId': id,
          'status': status
        },
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف فاتورة مشتريات
  Future<bool> deletePurchase(String id) async {
    _setLoading(true);
    _error = null;

    try {
      final db = await _db.database;

      // الحصول على الفاتورة الحالية
      final currentPurchase = await getPurchaseById(id);
      if (currentPurchase == null) {
        _setError('فاتورة المشتريات غير موجودة');
        return false;
      }

      // إذا كانت الفاتورة مكتملة، قم بإلغاء تأثيرها على المخزون
      if (currentPurchase.status == 'completed') {
        await _revertInventoryChanges(currentPurchase);
      }

      // حذف الفاتورة (حذف منطقي)
      await db.update(
        DatabaseService.tableInvoices,
        {
          'is_deleted': 1,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ? AND invoice_type = "purchase"',
        whereArgs: [id],
      );

      // إعادة تحميل الفواتير
      await loadPurchases();

      return true;
    } catch (e, stackTrace) {
      _setError('فشل حذف فاتورة المشتريات: ${e.toString()}');
      ErrorTracker.captureError(
        'Failed to delete purchase',
        error: e,
        stackTrace: stackTrace,
        context: {'presenter': 'PurchasePresenter', 'purchaseId': id},
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على إحصائيات المشتريات
  Future<Map<String, dynamic>> getPurchaseStatistics() async {
    try {
      final db = await _db.database;

      // الحصول على إجمالي المشتريات
      final totalResult = await db.rawQuery('''
        SELECT SUM(total) as total
        FROM invoices
        WHERE invoice_type = 'purchase' AND status = 'completed' AND is_deleted = 0
      ''');

      // الحصول على عدد المشتريات
      final countResult = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM invoices
        WHERE invoice_type = 'purchase' AND status = 'completed' AND is_deleted = 0
      ''');

      // الحصول على إجمالي المشتريات اليوم
      final today = DateTime.now().toIso8601String().substring(0, 10);
      final todayResult = await db.rawQuery('''
        SELECT SUM(total) as total
        FROM invoices
        WHERE invoice_type = 'purchase' AND status = 'completed' AND is_deleted = 0
        AND date LIKE '$today%'
      ''');

      // الحصول على إجمالي المشتريات هذا الشهر
      final thisMonth = DateTime.now().toIso8601String().substring(0, 7);
      final monthResult = await db.rawQuery('''
        SELECT SUM(total) as total
        FROM invoices
        WHERE invoice_type = 'purchase' AND status = 'completed' AND is_deleted = 0
        AND date LIKE '$thisMonth%'
      ''');

      return {
        'total': totalResult.first['total'] ?? 0.0,
        'count': countResult.first['count'] ?? 0,
        'today': todayResult.first['total'] ?? 0.0,
        'month': monthResult.first['total'] ?? 0.0,
      };
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to get purchase statistics',
        error: e,
        stackTrace: stackTrace,
        context: {'presenter': 'PurchasePresenter'},
      );
      return {
        'total': 0.0,
        'count': 0,
        'today': 0.0,
        'month': 0.0,
      };
    }
  }
}
