import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

import '../../../core/utils/index.dart';
import '../../../core/widgets/index.dart';
import '../presenters/reports_presenter.dart';
import '../../../core/theme/index.dart';

/// شاشة تقرير المبيعات
///
/// تعرض هذه الشاشة تقرير المبيعات مع رسوم بيانية لتوضيح المبيعات عبر الزمن
class SalesReportScreen extends StatefulWidget {
  const SalesReportScreen({Key? key}) : super(key: key);

  @override
  State<SalesReportScreen> createState() => _SalesReportScreenState();
}

class _SalesReportScreenState extends State<SalesReportScreen> {
  bool _isLoading = false;
  Map<String, dynamic> _salesStatistics = {};
  List<Map<String, dynamic>> _salesByPeriod = [];
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _period = 'daily'; // daily, weekly, monthly

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final presenter = AppProviders.getLazyPresenter<ReportsPresenter>(
          () => ReportsPresenter());

      // تحميل إحصائيات المبيعات
      _salesStatistics = await presenter.getSalesStatistics(
        startDate: _startDate,
        endDate: _endDate,
      );

      // تحميل المبيعات حسب الفترة
      _salesByPeriod = await presenter.getSalesByPeriod(
        startDate: _startDate,
        endDate: _endDate,
        period: _period,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'تقرير المبيعات',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildReportContent(),
    );
  }

  Widget _buildReportContent() {
    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDateRangeSelector(),
            const SizedBox(height: 20),
            _buildSummaryCards(),
            const SizedBox(height: 20),
            _buildSalesChart(),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نطاق التاريخ',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDateSelector(
                    label: 'من',
                    date: _startDate,
                    onDateSelected: (date) {
                      setState(() {
                        _startDate = date;
                        _loadData();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateSelector(
                    label: 'إلى',
                    date: _endDate,
                    onDateSelected: (date) {
                      setState(() {
                        _endDate = date;
                        _loadData();
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('تجميع حسب:'),
                const SizedBox(width: 16),
                DropdownButton<String>(
                  value: _period,
                  items: const [
                    DropdownMenuItem(
                      value: 'daily',
                      child: Text('يومي'),
                    ),
                    DropdownMenuItem(
                      value: 'weekly',
                      child: Text('أسبوعي'),
                    ),
                    DropdownMenuItem(
                      value: 'monthly',
                      child: Text('شهري'),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _period = value;
                        _loadData();
                      });
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector({
    required String label,
    required DateTime date,
    required Function(DateTime) onDateSelected,
  }) {
    return InkWell(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date,
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
        );

        if (selectedDate != null) {
          onDateSelected(selectedDate);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.lightTextSecondary),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const AppTypography(
                fontSize: 14,
                color: AppColors.lightTextSecondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              DateFormat('yyyy/MM/dd').format(date),
              style: const AppTypography(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    // استخراج البيانات من إحصائيات المبيعات
    final totalSales = _salesStatistics['total_sales'] as int? ?? 0;
    final totalAmount = _salesStatistics['total_amount'] as double? ?? 0.0;
    final averageSale = _salesStatistics['average_sale'] as double? ?? 0.0;
    final customerCount = _salesStatistics['customer_count'] as int? ?? 0;

    return GridView.count(
      crossAxisCount: Layout.isMobile() ? 2 : 4,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildSummaryCard(
          title: 'إجمالي المبيعات',
          value: totalSales.toString(),
          icon: Icons.receipt,
          color: AppColors.info,
        ),
        _buildSummaryCard(
          title: 'إجمالي المبلغ',
          value: '${totalAmount.toStringAsFixed(2)} ر.س',
          icon: Icons.attach_money,
          color: AppColors.success,
        ),
        _buildSummaryCard(
          title: 'متوسط المبيعات',
          value: '${averageSale.toStringAsFixed(2)} ر.س',
          icon: Icons.trending_up,
          color: AppColors.warning,
        ),
        _buildSummaryCard(
          title: 'عدد العملاء',
          value: customerCount.toString(),
          icon: Icons.people,
          color: AppColors.accent,
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const AppTypography(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: AppTypography(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesChart() {
    // تحضير بيانات الرسم البياني
    final List<FlSpot> salesSpots = [];
    final List<String> periodLabels = [];
    double maxY = 0;

    for (int i = 0; i < _salesByPeriod.length; i++) {
      final data = _salesByPeriod[i];
      final period = data['period'] as String? ?? '';
      final totalAmount = data['total_amount'] as double? ?? 0.0;

      salesSpots.add(FlSpot(i.toDouble(), totalAmount));
      periodLabels.add(_formatPeriodLabel(period));

      if (totalAmount > maxY) {
        maxY = totalAmount;
      }
    }

    // إضافة هامش للقيمة القصوى
    maxY = maxY * 1.2;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المبيعات عبر الزمن',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: salesSpots.isEmpty
                  ? const Center(child: Text('لا توجد بيانات للعرض'))
                  : LineChart(
                      LineChartData(
                        gridData: FlGridData(
                          show: true,
                          drawVerticalLine: true,
                          horizontalInterval: maxY / 5,
                          verticalInterval: 1,
                        ),
                        titlesData: FlTitlesData(
                          show: true,
                          rightTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          topTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 30,
                              interval: salesSpots.length > 10
                                  ? (salesSpots.length / 5).floor().toDouble()
                                  : 1,
                              getTitlesWidget: (value, meta) {
                                if (value.toInt() >= 0 &&
                                    value.toInt() < periodLabels.length) {
                                  return Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      periodLabels[value.toInt()],
                                      style: const AppTypography(
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  );
                                }
                                return const SizedBox();
                              },
                            ),
                          ),
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              interval: maxY / 5,
                              reservedSize: 42,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  value.toInt().toString(),
                                  style: const AppTypography(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        borderData: FlBorderData(
                          show: true,
                          border: Border.all(color: AppColors.lightBorder),
                        ),
                        minX: 0,
                        maxX: salesSpots.length.toDouble() - 1,
                        minY: 0,
                        maxY: maxY,
                        lineBarsData: [
                          LineChartBarData(
                            spots: salesSpots,
                            isCurved: true,
                            color: AppColors.info,
                            barWidth: 3,
                            isStrokeCapRound: true,
                            dotData: const FlDotData(show: true),
                            belowBarData: BarAreaData(
                              show: true,
                              color: AppColors.info
                                  .withValues(alpha: 0.2), // 0.2 * 255 = 51
                            ),
                          ),
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatPeriodLabel(String period) {
    if (_period == 'daily') {
      // تنسيق اليوم: 2023-01-01 -> 01/01
      final parts = period.split('-');
      if (parts.length == 3) {
        return '${parts[2]}/${parts[1]}';
      }
    } else if (_period == 'weekly') {
      // تنسيق الأسبوع: 2023-01 -> W01
      final parts = period.split('-');
      if (parts.length == 2) {
        return 'W${parts[1]}';
      }
    } else if (_period == 'monthly') {
      // تنسيق الشهر: 2023-01 -> 01/2023
      final parts = period.split('-');
      if (parts.length == 2) {
        return '${parts[1]}/${parts[0]}';
      }
    }

    return period;
  }
}
