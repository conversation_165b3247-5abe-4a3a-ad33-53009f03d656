import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'error_tracker.dart';

/// مساعد للتحقق من حالة الاتصال بالإنترنت ومراقبة التغييرات
///
/// يوفر هذا الصف طرقًا للتحقق من حالة الاتصال بالإنترنت ومراقبة التغييرات
/// ويمكن استخدامه للتحقق من توفر الاتصال قبل تنفيذ العمليات التي تتطلب اتصالاً
class ConnectivityHelper {
  // نمط Singleton
  static final ConnectivityHelper _instance = ConnectivityHelper._internal();
  factory ConnectivityHelper() => _instance;
  ConnectivityHelper._internal();

  // مثيل من Connectivity
  final Connectivity _connectivity = Connectivity();

  // متغير لتخزين حالة الاتصال الحالية
  bool _isConnected = false;

  // متغير لتخزين نوع الاتصال الحالي
  ConnectivityResult _connectionType = ConnectivityResult.none;

  // متغير لتخزين وقت آخر تحقق من الاتصال
  DateTime? _lastCheckTime;

  // متغير لتخزين وقت آخر تغيير في حالة الاتصال
  DateTime? _lastChangeTime;

  // متحكم تدفق لإرسال تحديثات حالة الاتصال
  final StreamController<bool> _connectionChangeController =
      StreamController<bool>.broadcast();

  // متحكم تدفق لإرسال تحديثات نوع الاتصال
  final StreamController<ConnectivityResult> _connectionTypeController =
      StreamController<ConnectivityResult>.broadcast();

  // مؤقت للتحقق الدوري من الاتصال
  Timer? _periodicCheckTimer;

  // إعدادات
  static const Duration _periodicCheckInterval = Duration(seconds: 30);
  static const Duration _minCheckInterval = Duration(seconds: 2);
  static const String _pingHost = 'google.com';
  static const int _pingPort = 443;
  static const Duration _pingTimeout = Duration(seconds: 5);

  // الحصول على تدفق تغييرات الاتصال
  Stream<bool> get onConnectivityChanged => _connectionChangeController.stream;

  // الحصول على تدفق تغييرات نوع الاتصال
  Stream<ConnectivityResult> get onConnectionTypeChanged =>
      _connectionTypeController.stream;

  // الحصول على حالة الاتصال الحالية
  Future<bool> get isConnected async {
    // إذا تم التحقق من الاتصال مؤخرًا، نعيد القيمة المخزنة
    if (_lastCheckTime != null &&
        DateTime.now().difference(_lastCheckTime!) < _minCheckInterval) {
      return _isConnected;
    }

    return await checkConnectivity();
  }

  // الحصول على نوع الاتصال الحالي
  ConnectivityResult get connectionType => _connectionType;

  // الحصول على وقت آخر تغيير في حالة الاتصال
  DateTime? get lastChangeTime => _lastChangeTime;

  // التحقق مما إذا كان الاتصال عبر WiFi
  bool get isWifi => _connectionType == ConnectivityResult.wifi;

  // التحقق مما إذا كان الاتصال عبر بيانات الجوال
  bool get isMobile => _connectionType == ConnectivityResult.mobile;

  /// التحقق من حالة الاتصال بالإنترنت
  Future<bool> checkConnectivity() async {
    try {
      _lastCheckTime = DateTime.now();

      // التحقق من نوع الاتصال
      final connectivityResult = await _connectivity.checkConnectivity();
      final previousConnectionType = _connectionType;
      _connectionType = connectivityResult;

      // إرسال تحديث إذا تغير نوع الاتصال
      if (previousConnectionType != _connectionType) {
        _connectionTypeController.add(_connectionType);
      }

      // التحقق من وجود اتصال بالإنترنت
      final hasConnection = connectivityResult != ConnectivityResult.none;

      // إذا كان هناك اتصال، نتحقق من الاتصال الفعلي بالإنترنت
      if (hasConnection) {
        final canReachInternet = await _canConnectToInternet();
        _updateConnectionStatus(canReachInternet);
        return canReachInternet;
      } else {
        _updateConnectionStatus(false);
        return false;
      }
    } catch (e, stackTrace) {
      // في حالة حدوث خطأ، نفترض أن الاتصال غير متوفر
      ErrorTracker.captureError(
        'Error checking connectivity',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'check_connectivity'},
      );

      _updateConnectionStatus(false);
      return false;
    }
  }

  /// التحقق من إمكانية الاتصال بالإنترنت عن طريق ping
  Future<bool> _canConnectToInternet() async {
    try {
      // محاولة الاتصال بخادم معروف
      final socket = await Socket.connect(
        _pingHost,
        _pingPort,
        timeout: _pingTimeout,
      );

      socket.destroy();
      return true;
    } catch (e) {
      // في حالة فشل الاتصال، نفترض أن الإنترنت غير متوفر
      return false;
    }
  }

  /// بدء الاستماع لتغييرات الاتصال
  void initialize() {
    // الاستماع لتغييرات الاتصال
    _connectivity.onConnectivityChanged.listen((result) {
      // عند تغير نوع الاتصال، نتحقق من الاتصال الفعلي
      _connectionType = result;
      _connectionTypeController.add(result);

      // التحقق من الاتصال الفعلي بالإنترنت
      checkConnectivity();
    });

    // التحقق من حالة الاتصال الأولية
    checkConnectivity();

    // بدء التحقق الدوري من الاتصال
    _startPeriodicCheck();
  }

  /// بدء التحقق الدوري من الاتصال
  void _startPeriodicCheck() {
    _periodicCheckTimer?.cancel();
    _periodicCheckTimer = Timer.periodic(_periodicCheckInterval, (_) {
      checkConnectivity();
    });
  }

  /// تحديث حالة الاتصال عند تغييرها
  void _updateConnectionStatus(bool isConnected) {
    if (_isConnected != isConnected) {
      _isConnected = isConnected;
      _lastChangeTime = DateTime.now();
      _connectionChangeController.add(isConnected);

      if (kDebugMode) {
        print(
            'Connectivity changed: ${isConnected ? 'Connected' : 'Disconnected'}');
      }
    }
  }

  /// تنفيذ عملية مع التحقق من الاتصال
  ///
  /// [operation] العملية المراد تنفيذها عند توفر الاتصال
  /// [offlineOperation] العملية البديلة في حالة عدم توفر الاتصال
  /// [showError] ما إذا كان سيتم إظهار خطأ في حالة عدم توفر الاتصال
  Future<T?> executeWithConnectivity<T>({
    required Future<T> Function() operation,
    Future<T> Function()? offlineOperation,
    bool showError = true,
  }) async {
    final connected = await isConnected;

    if (connected) {
      try {
        return await operation();
      } catch (e, stackTrace) {
        // التحقق مما إذا كان الخطأ متعلقًا بالاتصال
        if (_isConnectionError(e)) {
          // تحديث حالة الاتصال
          _updateConnectionStatus(false);

          // تنفيذ العملية البديلة إذا كانت متوفرة
          if (offlineOperation != null) {
            return await offlineOperation();
          }
        }

        // إعادة رمي الخطأ
        ErrorTracker.captureError(
          'Error executing operation with connectivity check',
          error: e,
          stackTrace: stackTrace,
          context: {'operation': 'execute_with_connectivity'},
        );
        rethrow;
      }
    } else {
      // تنفيذ العملية البديلة إذا كانت متوفرة
      if (offlineOperation != null) {
        return await offlineOperation();
      }

      // إظهار خطأ إذا تم طلب ذلك
      if (showError) {
        throw Exception('No internet connection');
      }

      return null;
    }
  }

  /// التحقق مما إذا كان الخطأ متعلقًا بالاتصال
  bool _isConnectionError(Object error) {
    final errorString = error.toString().toLowerCase();

    return errorString.contains('socket') ||
        errorString.contains('connection') ||
        errorString.contains('network') ||
        errorString.contains('internet') ||
        errorString.contains('timeout') ||
        error is SocketException ||
        error is TimeoutException;
  }

  /// إغلاق الموارد عند الانتهاء
  void dispose() {
    _periodicCheckTimer?.cancel();
    _connectionChangeController.close();
    _connectionTypeController.close();
  }
}
