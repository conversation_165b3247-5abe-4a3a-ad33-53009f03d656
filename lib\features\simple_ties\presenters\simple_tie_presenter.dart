import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../../../core/models/simple_tie.dart';
import '../../../core/utils/app_logger.dart';

/// حالة مقدم القيود البسيطة
class SimpleTieState {
  final List<SimpleTie> simpleTies;
  final bool isLoading;
  final String? errorMessage;

  SimpleTieState({
    required this.simpleTies,
    this.isLoading = false,
    this.errorMessage,
  });

  /// نسخ الحالة مع تعديل بعض الخصائص
  SimpleTieState copyWith({
    List<SimpleTie>? simpleTies,
    bool? isLoading,
    String? errorMessage,
  }) {
    return SimpleTieState(
      simpleTies: simpleTies ?? this.simpleTies,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }
}

/// مقدم القيود البسيطة
class SimpleTiePresenter extends ChangeNotifier {
  SimpleTieState _state = SimpleTieState(simpleTies: []);

  /// الحصول على الحالة الحالية
  SimpleTieState get state => _state;

  /// تحديث الحالة
  void _updateState(SimpleTieState newState) {
    _state = newState;
    notifyListeners();
  }

  /// تحميل القيود البسيطة
  Future<void> loadSimpleTies() async {
    _updateState(_state.copyWith(isLoading: true));

    try {
      // هنا يجب استدعاء خدمة قاعدة البيانات لتحميل القيود البسيطة
      // لكن سنستخدم بيانات تجريبية مؤقتًا
      await Future.delayed(const Duration(milliseconds: 500));

      final simpleTies = _getMockSimpleTies();
      _updateState(_state.copyWith(
        simpleTies: simpleTies,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      AppLogger.error('فشل في تحميل القيود البسيطة: $e');
      _updateState(_state.copyWith(
        isLoading: false,
        errorMessage: 'فشل في تحميل القيود البسيطة: $e',
      ));
    }
  }

  /// إنشاء قيد بسيط جديد
  Future<String?> createSimpleTie(SimpleTie simpleTie) async {
    _updateState(_state.copyWith(isLoading: true));

    try {
      // هنا يجب استدعاء خدمة قاعدة البيانات لإنشاء قيد بسيط جديد
      // لكن سنستخدم محاكاة مؤقتة
      await Future.delayed(const Duration(milliseconds: 500));

      // إنشاء رقم للقيد
      final number = 'JV-${DateTime.now().year}-${_state.simpleTies.length + 1}';
      final newSimpleTie = simpleTie.copyWith(
        id: const Uuid().v4(),
        number: number,
        createdAt: DateTime.now(),
      );

      final updatedSimpleTies = [..._state.simpleTies, newSimpleTie];
      _updateState(_state.copyWith(
        simpleTies: updatedSimpleTies,
        isLoading: false,
        errorMessage: null,
      ));

      return newSimpleTie.id;
    } catch (e) {
      AppLogger.error('فشل في إنشاء قيد بسيط: $e');
      _updateState(_state.copyWith(
        isLoading: false,
        errorMessage: 'فشل في إنشاء قيد بسيط: $e',
      ));
      return null;
    }
  }

  /// تحديث قيد بسيط
  Future<bool> updateSimpleTie(SimpleTie simpleTie) async {
    _updateState(_state.copyWith(isLoading: true));

    try {
      // هنا يجب استدعاء خدمة قاعدة البيانات لتحديث القيد البسيط
      // لكن سنستخدم محاكاة مؤقتة
      await Future.delayed(const Duration(milliseconds: 500));

      final updatedSimpleTie = simpleTie.copyWith(
        updatedAt: DateTime.now(),
      );

      final index = _state.simpleTies.indexWhere((tie) => tie.id == simpleTie.id);
      if (index == -1) {
        throw Exception('القيد البسيط غير موجود');
      }

      final updatedSimpleTies = [..._state.simpleTies];
      updatedSimpleTies[index] = updatedSimpleTie;

      _updateState(_state.copyWith(
        simpleTies: updatedSimpleTies,
        isLoading: false,
        errorMessage: null,
      ));

      return true;
    } catch (e) {
      AppLogger.error('فشل في تحديث القيد البسيط: $e');
      _updateState(_state.copyWith(
        isLoading: false,
        errorMessage: 'فشل في تحديث القيد البسيط: $e',
      ));
      return false;
    }
  }

  /// حذف قيد بسيط
  Future<bool> deleteSimpleTie(String id) async {
    _updateState(_state.copyWith(isLoading: true));

    try {
      // هنا يجب استدعاء خدمة قاعدة البيانات لحذف القيد البسيط
      // لكن سنستخدم محاكاة مؤقتة
      await Future.delayed(const Duration(milliseconds: 500));

      final index = _state.simpleTies.indexWhere((tie) => tie.id == id);
      if (index == -1) {
        throw Exception('القيد البسيط غير موجود');
      }

      final updatedSimpleTies = [..._state.simpleTies];
      updatedSimpleTies.removeAt(index);

      _updateState(_state.copyWith(
        simpleTies: updatedSimpleTies,
        isLoading: false,
        errorMessage: null,
      ));

      return true;
    } catch (e) {
      AppLogger.error('فشل في حذف القيد البسيط: $e');
      _updateState(_state.copyWith(
        isLoading: false,
        errorMessage: 'فشل في حذف القيد البسيط: $e',
      ));
      return false;
    }
  }

  /// تحديث القيود البسيطة
  Future<void> refreshSimpleTies() async {
    await loadSimpleTies();
  }

  /// الحصول على بيانات تجريبية للقيود البسيطة
  List<SimpleTie> _getMockSimpleTies() {
    return [
      SimpleTie(
        id: '1',
        number: 'JV-2023-001',
        date: DateTime(2023, 1, 15),
        amount: 1000.0,
        fromAccountId: '101',
        toAccountId: '201',
        currencyId: 'SAR',
        userId: 'admin',
        userName: 'المدير',
        notes: 'قيد افتتاحي',
        createdAt: DateTime(2023, 1, 15),
      ),
      SimpleTie(
        id: '2',
        number: 'JV-2023-002',
        date: DateTime(2023, 2, 20),
        amount: 2500.0,
        fromAccountId: '102',
        toAccountId: '202',
        currencyId: 'SAR',
        userId: 'admin',
        userName: 'المدير',
        notes: 'مصاريف تشغيلية',
        createdAt: DateTime(2023, 2, 20),
      ),
      SimpleTie(
        id: '3',
        number: 'JV-2023-003',
        date: DateTime(2023, 3, 10),
        amount: 3000.0,
        fromAccountId: '103',
        toAccountId: '203',
        currencyId: 'USD',
        exchangeRate: 3.75,
        userId: 'admin',
        userName: 'المدير',
        notes: 'إيرادات مبيعات',
        createdAt: DateTime(2023, 3, 10),
      ),
    ];
  }
}
