import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';
import '../../../core/utils/validators.dart';
import '../../../core/models/payment.dart';
import '../../../core/models/customer.dart';

/// مربع حوار الدفع
class PaymentDialog extends StatefulWidget {
  final double totalAmount;
  final String? customerId;
  final List<Customer> customers;

  const PaymentDialog({
    Key? key,
    required this.totalAmount,
    this.customerId,
    required this.customers,
  }) : super(key: key);

  @override
  State<PaymentDialog> createState() => _PaymentDialogState();
}

class _PaymentDialogState extends State<PaymentDialog> {
  // طريقة الدفع المحددة
  String _selectedPaymentMethod = PaymentMethods.cash;

  // المبلغ المدفوع
  final TextEditingController _amountPaidController = TextEditingController();

  // رقم المرجع (للبطاقات والتحويلات)
  final TextEditingController _referenceNumberController =
      TextEditingController();

  // معرف العميل
  String? _selectedCustomerId;

  // مفتاح النموذج
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    // تعيين المبلغ المدفوع بشكل افتراضي
    _amountPaidController.text = widget.totalAmount.toStringAsFixed(2);

    // تعيين العميل المحدد
    _selectedCustomerId = widget.customerId;
  }

  @override
  void dispose() {
    _amountPaidController.dispose();
    _referenceNumberController.dispose();
    super.dispose();
  }

  /// حساب المبلغ المتبقي
  double get _remainingAmount {
    final amountPaid = double.tryParse(_amountPaidController.text) ?? 0.0;
    return widget.totalAmount - amountPaid;
  }

  /// حساب الباقي
  double get _change {
    final amountPaid = double.tryParse(_amountPaidController.text) ?? 0.0;
    return amountPaid - widget.totalAmount;
  }

  /// إكمال عملية الدفع
  void _completePayment() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من أن المبلغ المدفوع كافٍ إذا لم يكن الدفع آجلاً
    final amountPaid = double.tryParse(_amountPaidController.text) ?? 0.0;
    if (_selectedPaymentMethod != PaymentMethods.credit &&
        amountPaid < widget.totalAmount) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('المبلغ المدفوع أقل من المبلغ المطلوب'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // التحقق من وجود عميل إذا كان الدفع آجلاً
    if (_selectedPaymentMethod == PaymentMethods.credit &&
        _selectedCustomerId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب اختيار عميل للدفع الآجل'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // إنشاء كائن الدفع
    final payment = Payment(
      method: _selectedPaymentMethod,
      amount: amountPaid,
      customerId: _selectedCustomerId,
      referenceNumber: _referenceNumberController.text.isNotEmpty
          ? _referenceNumberController.text
          : null,
    );

    // إغلاق مربع الحوار وإرجاع كائن الدفع
    Navigator.pop(context, payment);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('الدفع'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // المبلغ الإجمالي
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.lightTextSecondary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'المبلغ الإجمالي:',
                      style: AppTypography(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      widget.totalAmount.toStringAsFixed(2),
                      style:
                          AppTypography.lightTextTheme.headlineMedium?.copyWith(
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ) ??
                              const AppTypography(
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppDimensions.spacing16),

              // طريقة الدفع
              const Text(
                'طريقة الدفع:',
                style: AppTypography(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: AppDimensions.spacing8),
              Wrap(
                spacing: 8,
                children: [
                  _buildPaymentMethodChip(PaymentMethods.cash),
                  _buildPaymentMethodChip(PaymentMethods.creditCard),
                  _buildPaymentMethodChip(PaymentMethods.debitCard),
                  _buildPaymentMethodChip(PaymentMethods.bankTransfer),
                  _buildPaymentMethodChip(PaymentMethods.credit),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing16),

              // المبلغ المدفوع
              TextFormField(
                controller: _amountPaidController,
                decoration: const InputDecoration(
                  labelText: 'المبلغ المدفوع',
                  border: OutlineInputBorder(),
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                validator: Validators.required('المبلغ المدفوع'),
                onChanged: (value) {
                  setState(() {});
                },
              ),
              const SizedBox(height: AppDimensions.spacing16),

              // رقم المرجع (للبطاقات والتحويلات)
              if (_selectedPaymentMethod == PaymentMethods.creditCard ||
                  _selectedPaymentMethod == PaymentMethods.debitCard ||
                  _selectedPaymentMethod == PaymentMethods.bankTransfer)
                Column(
                  children: [
                    TextFormField(
                      controller: _referenceNumberController,
                      decoration: const InputDecoration(
                        labelText: 'رقم المرجع',
                        border: OutlineInputBorder(),
                      ),
                      validator: Validators.required('رقم المرجع'),
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                  ],
                ),

              // اختيار العميل (للدفع الآجل)
              if (_selectedPaymentMethod == PaymentMethods.credit)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'العميل:',
                      style: AppTypography(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: AppDimensions.spacing8),
                    DropdownButtonFormField<String?>(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedCustomerId,
                      items: widget.customers.map((customer) {
                        return DropdownMenuItem<String?>(
                          value: customer.id,
                          child: Text(customer.name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCustomerId = value;
                        });
                      },
                      validator: _selectedPaymentMethod == PaymentMethods.credit
                          ? Validators.required('يجب اختيار عميل للدفع الآجل')
                          : null,
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                  ],
                ),

              // الباقي أو المتبقي
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.lightSurface,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _change >= 0 ? 'الباقي:' : 'المتبقي:',
                      style: const AppTypography(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      _change >= 0
                          ? _change.toStringAsFixed(2)
                          : _remainingAmount.toStringAsFixed(2),
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        color:
                            _change >= 0 ? AppColors.success : AppColors.error,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _completePayment,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.success,
          ),
          child: const Text('إتمام الدفع'),
        ),
      ],
    );
  }

  /// بناء شريحة طريقة الدفع
  Widget _buildPaymentMethodChip(String method) {
    return ChoiceChip(
      label: Text(PaymentMethods.getLocalizedName(method)),
      selected: _selectedPaymentMethod == method,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _selectedPaymentMethod = method;
          });
        }
      },
    );
  }
}
