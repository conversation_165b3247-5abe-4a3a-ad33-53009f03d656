import '../../../core/models/base_model.dart';

/// نموذج العملية
/// يستخدم لربط الحسابات والسندات والفواتير لإنشاء تقارير مفصلة
class Operation extends BaseModel {
  final String
      operationType; // نوع العملية (فاتورة مبيعات، فاتورة مشتريات، سند قبض، سند صرف، قيد محاسبي)
  final String? documentId; // معرف المستند المرتبط (فاتورة، سند، قيد)
  final String? documentNumber; // رقم المستند
  final String? accountId; // معرف الحساب المرتبط
  final String? relatedAccountId; // معرف الحساب المقابل
  final double amount; // المبلغ
  final String? currencyId; // معرف العملة
  final double exchangeRate; // سعر الصرف
  final DateTime operationDate; // تاريخ العملية
  final String? userId; // معرف المستخدم الذي قام بالعملية
  final String? branchId; // معرف الفرع
  final String? warehouseId; // معرف المخزن (للعمليات المخزنية)
  final String? customerId; // معرف العميل (للمبيعات)
  final String? supplierId; // معرف المورد (للمشتريات)
  final String? notes; // ملاحظات
  final Map<String, dynamic>? metadata; // بيانات إضافية
  final bool isPosted; // هل تم ترحيل العملية
  final bool isVoided; // هل تم إلغاء العملية

  Operation({
    String? id,
    required this.operationType,
    this.documentId,
    this.documentNumber,
    this.accountId,
    this.relatedAccountId,
    required this.amount,
    this.currencyId,
    this.exchangeRate = 1.0,
    required this.operationDate,
    this.userId,
    this.branchId,
    this.warehouseId,
    this.customerId,
    this.supplierId,
    this.notes,
    this.metadata,
    this.isPosted = false,
    this.isVoided = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isDeleted = false,
  }) : super(
          id: id ?? '',
          createdAt: createdAt ?? DateTime.now(),
          updatedAt: updatedAt,
          isDeleted: isDeleted,
        );

  factory Operation.fromJson(Map<String, dynamic> json) {
    return Operation(
      id: json['id'],
      operationType: json['operationType'],
      documentId: json['documentId'],
      documentNumber: json['documentNumber'],
      accountId: json['accountId'],
      relatedAccountId: json['relatedAccountId'],
      amount: json['amount']?.toDouble() ?? 0.0,
      currencyId: json['currencyId'],
      exchangeRate: json['exchangeRate']?.toDouble() ?? 1.0,
      operationDate: DateTime.parse(json['operationDate']),
      userId: json['userId'],
      branchId: json['branchId'],
      warehouseId: json['warehouseId'],
      customerId: json['customerId'],
      supplierId: json['supplierId'],
      notes: json['notes'],
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
      isPosted: json['isPosted'] ?? false,
      isVoided: json['isVoided'] ?? false,
      createdAt:
          json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      isDeleted: json['isDeleted'] ?? false,
    );
  }

  factory Operation.fromMap(Map<String, dynamic> map) {
    return Operation(
      id: map['id']?.toString(),
      operationType: map['operation_type'],
      documentId: map['document_id'],
      documentNumber: map['document_number'],
      accountId: map['account_id'],
      relatedAccountId: map['related_account_id'],
      amount: map['amount']?.toDouble() ?? 0.0,
      currencyId: map['currency_id'],
      exchangeRate: map['exchange_rate']?.toDouble() ?? 1.0,
      operationDate: DateTime.parse(map['operation_date']),
      userId: map['user_id'],
      branchId: map['branch_id'],
      warehouseId: map['warehouse_id'],
      customerId: map['customer_id'],
      supplierId: map['supplier_id'],
      notes: map['notes'],
      metadata: map['metadata'] != null
          ? Map<String, dynamic>.from(map['metadata'])
          : null,
      isPosted: map['is_posted'] == 1,
      isVoided: map['is_voided'] == 1,
      createdAt:
          map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      isDeleted: map['is_deleted'] == 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'operationType': operationType,
      'documentId': documentId,
      'documentNumber': documentNumber,
      'accountId': accountId,
      'relatedAccountId': relatedAccountId,
      'amount': amount,
      'currencyId': currencyId,
      'exchangeRate': exchangeRate,
      'operationDate': operationDate.toIso8601String(),
      'userId': userId,
      'branchId': branchId,
      'warehouseId': warehouseId,
      'customerId': customerId,
      'supplierId': supplierId,
      'notes': notes,
      'metadata': metadata,
      'isPosted': isPosted,
      'isVoided': isVoided,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isDeleted': isDeleted,
    };
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'operation_type': operationType,
      'document_id': documentId,
      'document_number': documentNumber,
      'account_id': accountId,
      'related_account_id': relatedAccountId,
      'amount': amount,
      'currency_id': currencyId,
      'exchange_rate': exchangeRate,
      'operation_date': operationDate.toIso8601String(),
      'user_id': userId,
      'branch_id': branchId,
      'warehouse_id': warehouseId,
      'customer_id': customerId,
      'supplier_id': supplierId,
      'notes': notes,
      'metadata': metadata,
      'is_posted': isPosted ? 1 : 0,
      'is_voided': isVoided ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  Operation copyWith({
    String? id,
    String? operationType,
    String? documentId,
    String? documentNumber,
    String? accountId,
    String? relatedAccountId,
    double? amount,
    String? currencyId,
    double? exchangeRate,
    DateTime? operationDate,
    String? userId,
    String? branchId,
    String? warehouseId,
    String? customerId,
    String? supplierId,
    String? notes,
    Map<String, dynamic>? metadata,
    bool? isPosted,
    bool? isVoided,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
  }) {
    return Operation(
      id: id ?? this.id,
      operationType: operationType ?? this.operationType,
      documentId: documentId ?? this.documentId,
      documentNumber: documentNumber ?? this.documentNumber,
      accountId: accountId ?? this.accountId,
      relatedAccountId: relatedAccountId ?? this.relatedAccountId,
      amount: amount ?? this.amount,
      currencyId: currencyId ?? this.currencyId,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      operationDate: operationDate ?? this.operationDate,
      userId: userId ?? this.userId,
      branchId: branchId ?? this.branchId,
      warehouseId: warehouseId ?? this.warehouseId,
      customerId: customerId ?? this.customerId,
      supplierId: supplierId ?? this.supplierId,
      notes: notes ?? this.notes,
      metadata: metadata ?? this.metadata,
      isPosted: isPosted ?? this.isPosted,
      isVoided: isVoided ?? this.isVoided,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }
}
