/// نظام التحقق من صحة البيانات
/// يوفر مجموعة شاملة من دوال التحقق للنماذج والمدخلات
///
/// الميزات:
/// - التحقق الأساسي (مطلوب، بريد إلكتروني، هاتف)
/// - التحقق من النصوص (طول، نوع الأحرف)
/// - التحقق من الأرقام (موجب، سالب، نطاق)
/// - التحقق من كلمات المرور (قوة، تطابق)
/// - التحقق المتقدم (رقم قومي سعودي، رمز بريدي)
/// - دعم التحقق المركب والشرطي
///
/// الاستخدام:
/// ```dart
/// // التحقق البسيط
/// validator: Validators.required('اسم المستخدم')
///
/// // التحقق المركب
/// validator: Validators.combine([
///   Validators.required('البريد الإلكتروني'),
///   Validators.email(),
/// ])
///
/// // التحقق الشرطي
/// validator: Validators.conditional(
///   isRequired,
///   Validators.required('الحقل'),
/// )
/// ```
class Validators {
  // ========== التحقق الأساسي ==========

  /// التحقق من أن الحقل مطلوب
  static String? Function(String?) required(String fieldName) {
    return (value) {
      if (value == null || value.trim().isEmpty) {
        return '$fieldName مطلوب';
      }
      return null;
    };
  }

  /// التحقق من صحة البريد الإلكتروني
  static String? Function(String?) email() {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }

      final emailRegExp =
          RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');

      if (!emailRegExp.hasMatch(value.trim())) {
        return 'البريد الإلكتروني غير صحيح';
      }
      return null;
    };
  }

  /// التحقق من صحة رقم الهاتف (محسن للأرقام السعودية)
  static String? Function(String?) phone() {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }

      // إزالة المسافات والرموز
      final cleanPhone = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');

      // التحقق من الأرقام السعودية أولاً
      final saudiPhoneRegExp = RegExp(r'^(\+966|966|05)[0-9]{8}$');
      if (saudiPhoneRegExp.hasMatch(cleanPhone)) {
        return null; // رقم سعودي صحيح
      }

      // التحقق من الأرقام الدولية العامة
      final internationalPhoneRegExp = RegExp(r'^\+?[0-9]{8,15}$');
      if (internationalPhoneRegExp.hasMatch(cleanPhone)) {
        return null; // رقم دولي صحيح
      }

      return 'رقم الهاتف غير صحيح';
    };
  }

  // ========== التحقق من النصوص ==========

  /// التحقق من الحد الأدنى لطول النص
  static String? Function(String?) minLength(int length, String fieldName) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }
      if (value.length < length) {
        return '$fieldName يجب أن يكون $length أحرف على الأقل';
      }
      return null;
    };
  }

  /// التحقق من الحد الأقصى لطول النص
  static String? Function(String?) maxLength(int length, String fieldName) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }
      if (value.length > length) {
        return '$fieldName يجب أن يكون $length أحرف كحد أقصى';
      }
      return null;
    };
  }

  /// التحقق من نطاق طول النص
  static String? Function(String?) lengthRange(
      int min, int max, String fieldName) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }
      if (value.length < min || value.length > max) {
        return '$fieldName يجب أن يكون بين $min و $max أحرف';
      }
      return null;
    };
  }

  /// التحقق من أن النص يحتوي على أحرف عربية فقط
  static String? Function(String?) arabicOnly(String fieldName) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }

      final arabicRegExp = RegExp(r'^[\u0600-\u06FF\s]+$');
      if (!arabicRegExp.hasMatch(value)) {
        return '$fieldName يجب أن يحتوي على أحرف عربية فقط';
      }
      return null;
    };
  }

  /// التحقق من أن النص يحتوي على أحرف إنجليزية فقط
  static String? Function(String?) englishOnly(String fieldName) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }

      final englishRegExp = RegExp(r'^[a-zA-Z\s]+$');
      if (!englishRegExp.hasMatch(value)) {
        return '$fieldName يجب أن يحتوي على أحرف إنجليزية فقط';
      }
      return null;
    };
  }

  // ========== التحقق من الأرقام ==========

  /// التحقق من أن القيمة رقم صحيح
  static String? Function(String?) number(String fieldName) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }
      if (double.tryParse(value) == null) {
        return '$fieldName يجب أن يكون رقماً صحيحاً';
      }
      return null;
    };
  }

  /// التحقق من أن القيمة رقم صحيح (integer)
  static String? Function(String?) integer(String fieldName) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }
      if (int.tryParse(value) == null) {
        return '$fieldName يجب أن يكون رقماً صحيحاً';
      }
      return null;
    };
  }

  /// التحقق من أن الرقم موجب
  static String? Function(String?) positiveNumber(String fieldName) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }
      final number = double.tryParse(value);
      if (number == null) {
        return '$fieldName يجب أن يكون رقماً صحيحاً';
      }
      if (number <= 0) {
        return '$fieldName يجب أن يكون رقماً موجباً';
      }
      return null;
    };
  }

  /// التحقق من أن الرقم موجب أو صفر
  static String? Function(String?) nonNegativeNumber(String fieldName) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }
      final number = double.tryParse(value);
      if (number == null) {
        return '$fieldName يجب أن يكون رقماً صحيحاً';
      }
      if (number < 0) {
        return '$fieldName يجب أن يكون رقماً موجباً أو صفراً';
      }
      return null;
    };
  }

  /// التحقق من أن الرقم ضمن نطاق معين
  static String? Function(String?) numberRange(
      double min, double max, String fieldName) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }
      final number = double.tryParse(value);
      if (number == null) {
        return '$fieldName يجب أن يكون رقماً صحيحاً';
      }
      if (number < min || number > max) {
        return '$fieldName يجب أن يكون بين $min و $max';
      }
      return null;
    };
  }

  // ========== التحقق من كلمات المرور ==========

  /// التحقق من تطابق كلمة المرور
  static String? Function(String?) passwordMatch(String password) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }
      if (value != password) {
        return 'كلمات المرور غير متطابقة';
      }
      return null;
    };
  }

  /// التحقق من قوة كلمة المرور
  static String? Function(String?) strongPassword() {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }

      if (value.length < 8) {
        return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
      }

      if (!RegExp(r'[A-Z]').hasMatch(value)) {
        return 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
      }

      if (!RegExp(r'[a-z]').hasMatch(value)) {
        return 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
      }

      if (!RegExp(r'[0-9]').hasMatch(value)) {
        return 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
      }

      return null;
    };
  }

  // ========== التحقق المتقدم ==========

  /// التحقق من صحة الرقم القومي السعودي
  static String? Function(String?) saudiNationalId() {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }

      // إزالة المسافات
      final cleanId = value.replaceAll(' ', '');

      // التحقق من الطول (10 أرقام)
      if (cleanId.length != 10) {
        return 'الرقم القومي يجب أن يكون 10 أرقام';
      }

      // التحقق من أن جميع الأحرف أرقام
      if (!RegExp(r'^[0-9]+$').hasMatch(cleanId)) {
        return 'الرقم القومي يجب أن يحتوي على أرقام فقط';
      }

      // التحقق من أن الرقم الأول 1 أو 2
      final firstDigit = int.parse(cleanId[0]);
      if (firstDigit != 1 && firstDigit != 2) {
        return 'الرقم القومي غير صحيح';
      }

      return null;
    };
  }

  /// التحقق من صحة الرمز البريدي السعودي
  static String? Function(String?) saudiPostalCode() {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }

      // إزالة المسافات
      final cleanCode = value.replaceAll(' ', '');

      // التحقق من الطول (5 أرقام)
      if (cleanCode.length != 5) {
        return 'الرمز البريدي يجب أن يكون 5 أرقام';
      }

      // التحقق من أن جميع الأحرف أرقام
      if (!RegExp(r'^[0-9]+$').hasMatch(cleanCode)) {
        return 'الرمز البريدي يجب أن يحتوي على أرقام فقط';
      }

      return null;
    };
  }

  // ========== دوال مساعدة ==========

  /// دمج عدة دوال تحقق
  static String? Function(String?) combine(
      List<String? Function(String?)> validators) {
    return (value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) {
          return result;
        }
      }
      return null;
    };
  }

  /// التحقق الشرطي
  static String? Function(String?) conditional(
    bool condition,
    String? Function(String?) validator,
  ) {
    return (value) {
      if (condition) {
        return validator(value);
      }
      return null;
    };
  }

  /// التحقق المخصص
  static String? Function(String?) custom(
    bool Function(String?) test,
    String errorMessage,
  ) {
    return (value) {
      if (value == null || value.isEmpty) {
        return null; // اختياري إذا لم يكن مطلوب
      }
      if (!test(value)) {
        return errorMessage;
      }
      return null;
    };
  }
}
