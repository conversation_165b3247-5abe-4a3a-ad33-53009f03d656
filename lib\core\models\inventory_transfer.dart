import 'package:uuid/uuid.dart';
import 'base_model.dart';
import 'inventory_transfer_item.dart';

/// نموذج تحويل المخزون
class InventoryTransfer extends BaseModel {
  final String? referenceNumber;
  final DateTime date;
  final String sourceWarehouseId;
  final String destinationWarehouseId;
  final String? notes;
  final List<InventoryTransferItem> items;
  final bool isCompleted;
  final String status;

  // Getters for compatibility with inventory_transfer_form_screen.dart
  String get fromWarehouseId => sourceWarehouseId;
  String get toWarehouseId => destinationWarehouseId;

  InventoryTransfer({
    String? id,
    this.referenceNumber,
    required this.date,
    required this.sourceWarehouseId,
    required this.destinationWarehouseId,
    this.notes,
    this.items = const [],
    this.isCompleted = false,
    this.status = 'pending',
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا التحويل مع استبدال الحقول المحددة بقيم جديدة
  InventoryTransfer copyWith({
    String? id,
    String? referenceNumber,
    DateTime? date,
    String? sourceWarehouseId,
    String? destinationWarehouseId,
    String? notes,
    List<InventoryTransferItem>? items,
    bool? isCompleted,
    String? status,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return InventoryTransfer(
      id: id ?? this.id,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      date: date ?? this.date,
      sourceWarehouseId: sourceWarehouseId ?? this.sourceWarehouseId,
      destinationWarehouseId:
          destinationWarehouseId ?? this.destinationWarehouseId,
      notes: notes ?? this.notes,
      items: items ?? this.items,
      isCompleted: isCompleted ?? this.isCompleted,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل التحويل إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'reference_number': referenceNumber,
      'date': date.toIso8601String(),
      'source_warehouse_id': sourceWarehouseId,
      'destination_warehouse_id': destinationWarehouseId,
      'notes': notes,
      'is_completed': isCompleted ? 1 : 0,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء تحويل من Map
  factory InventoryTransfer.fromMap(Map<String, dynamic> map) {
    return InventoryTransfer(
      id: map['id'],
      referenceNumber: map['reference_number'],
      date: map['date'] != null ? DateTime.parse(map['date']) : DateTime.now(),
      sourceWarehouseId: map['source_warehouse_id'],
      destinationWarehouseId: map['destination_warehouse_id'],
      notes: map['notes'],
      isCompleted: map['is_completed'] == 1,
      status: map['status'] ?? 'pending',
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  @override
  String toString() {
    return 'InventoryTransfer(id: $id, referenceNumber: $referenceNumber, date: $date, sourceWarehouseId: $sourceWarehouseId, destinationWarehouseId: $destinationWarehouseId, isCompleted: $isCompleted)';
  }
}
