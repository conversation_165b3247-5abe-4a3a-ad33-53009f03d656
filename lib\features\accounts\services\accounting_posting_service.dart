import '../../../core/database/database_service.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';

/// خدمة ترحيل القيود المحاسبية
class AccountingPostingService {
  // نمط Singleton
  static final AccountingPostingService _instance =
      AccountingPostingService._internal();
  factory AccountingPostingService() => _instance;
  AccountingPostingService._internal();

  final DatabaseService _dbService = DatabaseService.instance;

  /// ترحيل قيد محاسبي
  Future<bool> postJournalEntry(String entryId, {String? userId}) async {
    try {
      AppLogger.info('ترحيل القيد المحاسبي: $entryId');

      final db = await _dbService.database;
      final now = DateTime.now().toIso8601String();

      // التحقق من وجود القيد
      final List<Map<String, dynamic>> entries = await db.query(
        'journal_entries',
        where: 'id = ? AND is_deleted = 0',
        whereArgs: [entryId],
      );

      if (entries.isEmpty) {
        AppLogger.warning('القيد المحاسبي غير موجود: $entryId');
        return false;
      }

      // التحقق من أن القيد غير مرحل بالفعل
      if (entries.first['is_posted'] == 1) {
        AppLogger.warning('القيد المحاسبي مرحل بالفعل: $entryId');
        return true; // نعتبرها عملية ناجحة لأن القيد مرحل بالفعل
      }

      // تحديث حالة القيد إلى مرحل
      await db.update(
        'journal_entries',
        {
          'is_posted': 1,
          'posted_at': now,
          'posted_by': userId,
          'updated_at': now,
          'updated_by': userId,
        },
        where: 'id = ?',
        whereArgs: [entryId],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في ترحيل القيد المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'entryId': entryId},
      );
      return false;
    }
  }

  /// إلغاء ترحيل قيد محاسبي
  Future<bool> unpostJournalEntry(String entryId, {String? userId}) async {
    try {
      AppLogger.info('إلغاء ترحيل القيد المحاسبي: $entryId');

      final db = await _dbService.database;
      final now = DateTime.now().toIso8601String();

      // التحقق من وجود القيد
      final List<Map<String, dynamic>> entries = await db.query(
        'journal_entries',
        where: 'id = ? AND is_deleted = 0',
        whereArgs: [entryId],
      );

      if (entries.isEmpty) {
        AppLogger.warning('القيد المحاسبي غير موجود: $entryId');
        return false;
      }

      // التحقق من أن القيد مرحل بالفعل
      if (entries.first['is_posted'] == 0) {
        AppLogger.warning('القيد المحاسبي غير مرحل بالفعل: $entryId');
        return true; // نعتبرها عملية ناجحة لأن القيد غير مرحل بالفعل
      }

      // تحديث حالة القيد إلى غير مرحل
      await db.update(
        'journal_entries',
        {
          'is_posted': 0,
          'posted_at': null,
          'posted_by': null,
          'updated_at': now,
          'updated_by': userId,
        },
        where: 'id = ?',
        whereArgs: [entryId],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إلغاء ترحيل القيد المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'entryId': entryId},
      );
      return false;
    }
  }
}
