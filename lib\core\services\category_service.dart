import '../database/database_service.dart';
import '../models/category.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';

/// خدمة للعمليات المتعلقة بالفئات الموحدة
class CategoryService {
  // نمط Singleton
  static final CategoryService _instance = CategoryService._internal();
  factory CategoryService() => _instance;
  CategoryService._internal();

  final DatabaseService _db = DatabaseService.instance;

  /// الحصول على جميع الفئات
  Future<List<Category>> getAllCategories({
    bool includeInactive = false,
    String? searchQuery,
    String? parentId,
    String? type,
  }) async {
    try {
      AppLogger.info('الحصول على جميع الفئات');

      // بناء شرط WHERE
      String whereClause = 'c.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (!includeInactive) {
        whereClause += ' AND c.is_active = 1';
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause += ' AND (c.name LIKE ? OR c.description LIKE ?)';
        whereArgs.addAll(['%$searchQuery%', '%$searchQuery%']);
      }

      if (parentId != null) {
        whereClause += ' AND c.parent_id = ?';
        whereArgs.add(parentId);
      }

      if (type != null) {
        whereClause += ' AND c.type = ?';
        whereArgs.add(type);
      }

      // استعلام قاعدة البيانات مع JOIN للحصول على أسماء الفئات الأب
      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          c.*,
          p.name as parent_name
        FROM ${DatabaseService.tableCategories} c
        LEFT JOIN ${DatabaseService.tableCategories} p ON c.parent_id = p.id
        WHERE $whereClause
        ORDER BY c.name ASC
      ''', whereArgs);

      // تحويل إلى كائنات Category
      return maps.map((map) => Category.fromMap(map)).toList();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع الفئات',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على فئة بواسطة المعرف
  Future<Category?> getCategoryById(String id) async {
    try {
      AppLogger.info('الحصول على فئة بواسطة المعرف: $id');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          c.*,
          p.name as parent_name
        FROM ${DatabaseService.tableCategories} c
        LEFT JOIN ${DatabaseService.tableCategories} p ON c.parent_id = p.id
        WHERE c.id = ? AND c.is_deleted = 0
      ''', [id]);

      if (maps.isEmpty) {
        return null;
      }

      return Category.fromMap(maps.first);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على فئة بواسطة المعرف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return null;
    }
  }

  /// إضافة فئة جديدة
  Future<bool> addCategory(Category category, {String? userId}) async {
    try {
      AppLogger.info('إضافة فئة جديدة: ${category.name}');

      final categoryMap = category.toMap();

      // تعيين created_by إذا تم توفيره
      if (userId != null) {
        categoryMap['created_by'] = userId;
      }

      await _db.insert(DatabaseService.tableCategories, categoryMap);

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة فئة',
        error: e,
        stackTrace: stackTrace,
        context: {'category': category.toString()},
      );
      return false;
    }
  }

  /// تحديث فئة موجودة
  Future<bool> updateCategory(Category category, {String? userId}) async {
    try {
      AppLogger.info('تحديث فئة: ${category.name}');

      final categoryMap = category.toMap();

      // تعيين updated_at و updated_by
      categoryMap['updated_at'] = DateTime.now().toIso8601String();
      if (userId != null) {
        categoryMap['updated_by'] = userId;
      }

      await _db.update(
        DatabaseService.tableCategories,
        categoryMap,
        where: 'id = ?',
        whereArgs: [category.id],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث فئة',
        error: e,
        stackTrace: stackTrace,
        context: {'category': category.toString()},
      );
      return false;
    }
  }

  /// حذف فئة (حذف منطقي)
  Future<bool> deleteCategory(String id, {String? userId, String? type}) async {
    try {
      AppLogger.info('حذف فئة: $id');

      // التحقق من وجود عناصر مرتبطة بهذه الفئة
      final itemsCount = await _getItemsCountByCategory(id, type);
      if (itemsCount > 0) {
        AppLogger.warning('لا يمكن حذف الفئة لأنها تحتوي على عناصر مرتبطة');
        return false;
      }

      // التحقق من وجود فئات فرعية
      final childrenCount = await _getChildCategoriesCount(id);
      if (childrenCount > 0) {
        AppLogger.warning('لا يمكن حذف الفئة لأنها تحتوي على فئات فرعية');
        return false;
      }

      final now = DateTime.now().toIso8601String();

      await _db.update(
        DatabaseService.tableCategories,
        {
          'is_deleted': 1,
          'is_active': 0,
          'updated_at': now,
          'updated_by': userId,
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف فئة',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// الحصول على عدد العناصر في فئة معينة
  Future<int> _getItemsCountByCategory(String categoryId, String? type) async {
    try {
      if (type == 'product') {
        final result = await _db.rawQuery(
          'SELECT COUNT(*) as count FROM ${DatabaseService.tableProducts} WHERE category_id = ? AND is_deleted = 0',
          [categoryId],
        );
        return result.first['count'] as int;
      } else if (type == 'expense') {
        final result = await _db.rawQuery(
          'SELECT COUNT(*) as count FROM expenses WHERE category_id = ? AND is_deleted = 0',
          [categoryId],
        );
        return result.first['count'] as int;
      }
      return 0;
    } catch (e) {
      AppLogger.error('فشل في الحصول على عدد العناصر في الفئة: $e');
      return 0;
    }
  }

  /// الحصول على عدد الفئات الفرعية لفئة معينة
  Future<int> _getChildCategoriesCount(String parentId) async {
    try {
      final result = await _db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseService.tableCategories} WHERE parent_id = ? AND is_deleted = 0',
        [parentId],
      );

      return result.first['count'] as int;
    } catch (e) {
      AppLogger.error('فشل في الحصول على عدد الفئات الفرعية: $e');
      return 0;
    }
  }

  /// الحصول على عدد الفئات
  Future<int> getCategoriesCount({bool includeInactive = false, String? type}) async {
    try {
      AppLogger.info('الحصول على عدد الفئات');

      String whereClause = 'is_deleted = 0';
      List<dynamic> whereArgs = [];
      
      if (!includeInactive) {
        whereClause += ' AND is_active = 1';
      }
      
      if (type != null) {
        whereClause += ' AND type = ?';
        whereArgs.add(type);
      }

      final result = await _db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseService.tableCategories} WHERE $whereClause',
        whereArgs,
      );

      return result.first['count'] as int;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على عدد الفئات',
        error: e,
        stackTrace: stackTrace,
      );
      return 0;
    }
  }
}
