import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:uuid/uuid.dart';
import '../../../core/models/expense.dart';
import '../../../core/theme/index.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/widgets/index.dart';
import '../presenters/expense_presenter.dart';

/// شاشة إضافة/تعديل مصروف
class ExpenseFormScreen extends StatefulWidget {
  final Expense? expense;

  const ExpenseFormScreen({Key? key, this.expense}) : super(key: key);

  @override
  State<ExpenseFormScreen> createState() => _ExpenseFormScreenState();
}

class _ExpenseFormScreenState extends State<ExpenseFormScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _referenceNumberController =
      TextEditingController();

  String? _selectedCategoryId;
  String _selectedPaymentMethod = 'cash';
  DateTime _expenseDate = DateTime.now();
  bool _isLoading = false;

  // استخدام التحميل الكسول
  late final ExpensePresenter _expensePresenter;

  final List<Map<String, dynamic>> _paymentMethods = [
    {'id': 'cash', 'name': 'نقدي'},
    {'id': 'bank', 'name': 'تحويل بنكي'},
    {'id': 'check', 'name': 'شيك'},
    {'id': 'credit_card', 'name': 'بطاقة ائتمان'},
    {'id': 'other', 'name': 'أخرى'},
  ];

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _expensePresenter = AppProviders.getLazyPresenter<ExpensePresenter>(
        () => ExpensePresenter());
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCategories();
      _initializeForm();
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    _referenceNumberController.dispose();
    super.dispose();
  }

  /// تحميل فئات المصروفات
  Future<void> _loadCategories() async {
    try {
      await _expensePresenter.loadExpenseCategories();
    } catch (e) {
      AppLogger.error('فشل في تحميل فئات المصروفات: $e');
    }
  }

  /// تهيئة النموذج بالبيانات الحالية إذا كان هناك مصروف للتعديل
  void _initializeForm() {
    if (widget.expense != null) {
      _amountController.text = widget.expense!.amount.toString();
      _descriptionController.text = widget.expense!.description ?? '';
      _referenceNumberController.text = widget.expense!.referenceNumber ?? '';
      _selectedCategoryId = widget.expense!.categoryId;
      _selectedPaymentMethod = widget.expense!.paymentMethod;
      _expenseDate = widget.expense!.expenseDate;
    }
  }

  /// حفظ المصروف
  Future<void> _saveExpense() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final expense = Expense(
        id: widget.expense?.id ?? const Uuid().v4(),
        categoryId: _selectedCategoryId,
        amount: double.parse(_amountController.text),
        description: _descriptionController.text.trim(),
        referenceNumber: _referenceNumberController.text.trim(),
        paymentMethod: _selectedPaymentMethod,
        expenseDate: _expenseDate,
        createdAt: widget.expense?.createdAt,
        createdBy: widget.expense?.createdBy,
      );

      bool success;
      if (widget.expense == null) {
        // إضافة مصروف جديد
        success = await _expensePresenter.addExpense(expense);
      } else {
        // تحديث مصروف موجود
        success = await _expensePresenter.updateExpense(expense);
      }

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.expense == null
                  ? 'تمت إضافة المصروف بنجاح'
                  : 'تم تحديث المصروف بنجاح',
            ),
          ),
        );
        Navigator.pop(context, true);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في حفظ المصروف'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      AppLogger.error('فشل في حفظ المصروف: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حفظ المصروف: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            Text(widget.expense == null ? 'إضافة مصروف جديد' : 'تعديل المصروف'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: ListenableBuilder(
        listenable: _expensePresenter,
        builder: (context, child) {
          if (_expensePresenter.isLoading || _isLoading) {
            return const AkLoadingIndicator();
          }

          final categories = _expensePresenter.categories;

          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // المبلغ
                  TextFormField(
                    controller: _amountController,
                    decoration: const InputDecoration(
                      labelText: 'المبلغ',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.attach_money),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال المبلغ';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: AppDimensions.spacing16),

                  // فئة المصروف
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'الفئة',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.category),
                    ),
                    value: _selectedCategoryId,
                    items: categories
                        .map((category) => DropdownMenuItem<String>(
                              value: category.id,
                              child: Text(category.name),
                            ))
                        .toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategoryId = value;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى اختيار الفئة';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: AppDimensions.spacing16),

                  // الوصف
                  TextFormField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'الوصف',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.description),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: AppDimensions.spacing16),

                  // رقم المرجع
                  TextFormField(
                    controller: _referenceNumberController,
                    decoration: const InputDecoration(
                      labelText: 'رقم المرجع',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.numbers),
                    ),
                  ),
                  const SizedBox(height: AppDimensions.spacing16),

                  // طريقة الدفع
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'طريقة الدفع',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.payment),
                    ),
                    value: _selectedPaymentMethod,
                    items: _paymentMethods
                        .map((method) => DropdownMenuItem<String>(
                              value: method['id'],
                              child: Text(method['name']),
                            ))
                        .toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedPaymentMethod = value!;
                      });
                    },
                  ),
                  const SizedBox(height: AppDimensions.spacing16),

                  // تاريخ المصروف
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'تاريخ المصروف',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    readOnly: true,
                    controller: TextEditingController(
                      text:
                          '${_expenseDate.day}/${_expenseDate.month}/${_expenseDate.year}',
                    ),
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _expenseDate,
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now(),
                      );
                      if (date != null) {
                        setState(() {
                          _expenseDate = date;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: AppDimensions.spacing24),

                  // زر الحفظ
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _saveExpense,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text(
                        widget.expense == null
                            ? 'إضافة المصروف'
                            : 'تحديث المصروف',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
