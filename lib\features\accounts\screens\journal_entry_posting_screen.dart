import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/index.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';

import '../services/accounting_engine.dart';
import '../presenters/journal_entry_presenter.dart';
import '../../../core/providers/app_providers.dart';

/// شاشة ترحيل القيود المحاسبية
class JournalEntryPostingScreen extends StatefulWidget {
  const JournalEntryPostingScreen({Key? key}) : super(key: key);

  @override
  State<JournalEntryPostingScreen> createState() =>
      _JournalEntryPostingScreenState();
}

class _JournalEntryPostingScreenState extends State<JournalEntryPostingScreen> {
  final AccountingEngine _accountingEngine = AccountingEngine();
  bool _isLoading = false;
  bool _isProcessing = false;
  String? _errorMessage;
  DateTimeRange _dateRange = DateTimeRange(
    start: DateTime.now().subtract(const Duration(days: 30)),
    end: DateTime.now(),
  );

  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  final List<String> _selectedEntries = [];

  // استخدام التحميل الكسول
  late final JournalEntryPresenter _journalEntryPresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _journalEntryPresenter =
        AppProviders.getLazyPresenter<JournalEntryPresenter>(
            () => JournalEntryPresenter());
    _loadJournalEntries();

    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadJournalEntries() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _journalEntryPresenter.loadJournalEntries(
        startDate: _dateRange.start,
        endDate: _dateRange.end,
      );
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في تحميل القيود المحاسبية: $e');
      ErrorTracker.captureError(
        'خطأ في تحميل القيود المحاسبية',
        error: e,
        stackTrace: stackTrace,
      );
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل القيود المحاسبية: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _postSelectedEntries() async {
    if (_selectedEntries.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى تحديد قيد واحد على الأقل للترحيل'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    // عرض مربع حوار للتأكيد
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الترحيل'),
        content: Text(
          'هل أنت متأكد من ترحيل ${_selectedEntries.length} قيد محاسبي؟\n'
          'لا يمكن التراجع عن هذه العملية إلا من خلال إلغاء الترحيل.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: AppColors.onPrimary,
            ),
            child: const Text('ترحيل'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      int successCount = 0;
      int failCount = 0;
      List<String> failedEntries = [];

      for (final entryId in _selectedEntries) {
        final success = await _accountingEngine.postJournalEntry(entryId);
        if (success) {
          successCount++;
        } else {
          failCount++;
          failedEntries.add(entryId);
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم ترحيل $successCount قيد بنجاح${failCount > 0 ? '، فشل ترحيل $failCount قيد' : ''}',
            ),
            backgroundColor:
                failCount > 0 ? AppColors.warning : AppColors.success,
            duration: const Duration(seconds: 5),
            action: failCount > 0
                ? SnackBarAction(
                    label: 'عرض التفاصيل',
                    onPressed: () => _showFailedEntriesDialog(failedEntries),
                  )
                : null,
          ),
        );
      }

      // إعادة تحميل القيود بعد الترحيل
      if (mounted) {
        await _loadJournalEntries();

        // إعادة تعيين القيود المحددة إذا نجحت جميع العمليات
        if (failCount == 0) {
          setState(() {
            _selectedEntries.clear();
          });
        } else {
          // إزالة القيود التي تم ترحيلها بنجاح فقط
          setState(() {
            _selectedEntries.removeWhere((id) => !failedEntries.contains(id));
          });
        }
      }
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في ترحيل القيود المحاسبية: $e');
      ErrorTracker.captureError(
        'خطأ في ترحيل القيود المحاسبية',
        error: e,
        stackTrace: stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء ترحيل القيود: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 10),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// عرض مربع حوار يوضح القيود التي فشل ترحيلها
  void _showFailedEntriesDialog(List<String> failedEntries) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('القيود التي فشل ترحيلها'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: failedEntries.length,
            itemBuilder: (context, index) {
              final entryId = failedEntries[index];
              final entry = _journalEntryPresenter.journalEntries.firstWhere(
                (e) => e.id == entryId,
                orElse: () => _journalEntryPresenter.journalEntries.first,
              );

              return ListTile(
                title: Text('قيد رقم: ${entry.entryNumber}'),
                subtitle: Text(
                    'التاريخ: ${DateFormat('yyyy-MM-dd').format(entry.entryDate)}'),
                trailing: const Icon(Icons.error, color: AppColors.error),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Future<void> _unpostSelectedEntries() async {
    if (_selectedEntries.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى تحديد قيد واحد على الأقل لإلغاء الترحيل'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    // عرض مربع حوار للتأكيد
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد إلغاء الترحيل'),
        content: Text(
          'هل أنت متأكد من إلغاء ترحيل ${_selectedEntries.length} قيد محاسبي؟\n'
          'سيؤدي هذا إلى إلغاء جميع الحركات المحاسبية المرتبطة بهذه القيود.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.warning,
              foregroundColor: AppColors.onPrimary,
            ),
            child: const Text('إلغاء الترحيل'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      int successCount = 0;
      int failCount = 0;
      List<String> failedEntries = [];

      for (final entryId in _selectedEntries) {
        final success = await _accountingEngine.unpostJournalEntry(entryId);
        if (success) {
          successCount++;
        } else {
          failCount++;
          failedEntries.add(entryId);
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إلغاء ترحيل $successCount قيد بنجاح${failCount > 0 ? '، فشل إلغاء ترحيل $failCount قيد' : ''}',
            ),
            backgroundColor:
                failCount > 0 ? AppColors.warning : AppColors.success,
            duration: const Duration(seconds: 5),
            action: failCount > 0
                ? SnackBarAction(
                    label: 'عرض التفاصيل',
                    onPressed: () =>
                        _showFailedUnpostEntriesDialog(failedEntries),
                  )
                : null,
          ),
        );
      }

      // إعادة تحميل القيود بعد إلغاء الترحيل
      if (mounted) {
        await _loadJournalEntries();

        // إعادة تعيين القيود المحددة إذا نجحت جميع العمليات
        if (failCount == 0) {
          setState(() {
            _selectedEntries.clear();
          });
        } else {
          // إزالة القيود التي تم إلغاء ترحيلها بنجاح فقط
          setState(() {
            _selectedEntries.removeWhere((id) => !failedEntries.contains(id));
          });
        }
      }
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في إلغاء ترحيل القيود المحاسبية: $e');
      ErrorTracker.captureError(
        'خطأ في إلغاء ترحيل القيود المحاسبية',
        error: e,
        stackTrace: stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إلغاء ترحيل القيود: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 10),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// عرض مربع حوار يوضح القيود التي فشل إلغاء ترحيلها
  void _showFailedUnpostEntriesDialog(List<String> failedEntries) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('القيود التي فشل إلغاء ترحيلها'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: failedEntries.length,
            itemBuilder: (context, index) {
              final entryId = failedEntries[index];
              final entry = _journalEntryPresenter.journalEntries.firstWhere(
                (e) => e.id == entryId,
                orElse: () => _journalEntryPresenter.journalEntries.first,
              );

              return ListTile(
                title: Text('قيد رقم: ${entry.entryNumber}'),
                subtitle: Text(
                    'التاريخ: ${DateFormat('yyyy-MM-dd').format(entry.entryDate)}'),
                trailing: const Icon(Icons.error, color: AppColors.error),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ترحيل القيود المحاسبية'),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildDateRangePicker(),
          const SizedBox(height: AppDimensions.spacing16),
          _buildActionButtons(),
          const SizedBox(height: AppDimensions.spacing16),
          Expanded(
            child: _buildJournalEntriesTable(),
          ),
        ],
      ),
    );
  }

  Widget _buildDateRangePicker() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الفترة الزمنية',
              style: AppTypography(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.spacing8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    readOnly: true,
                    decoration: const InputDecoration(
                      labelText: 'تاريخ البداية',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    controller: TextEditingController(
                      text: DateFormat('yyyy-MM-dd').format(_dateRange.start),
                    ),
                    onTap: () => _selectDateRange(context),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    readOnly: true,
                    decoration: const InputDecoration(
                      labelText: 'تاريخ النهاية',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    controller: TextEditingController(
                      text: DateFormat('yyyy-MM-dd').format(_dateRange.end),
                    ),
                    onTap: () => _selectDateRange(context),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDateRange(BuildContext context) async {
    final initialDateRange = _dateRange;
    final firstDate = DateTime(2000);
    final lastDate = DateTime(2100);

    final pickedDateRange = await showDateRangePicker(
      context: context,
      initialDateRange: initialDateRange,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.onPrimary,
              surface: AppColors.lightSurface,
              onSurface: Theme.of(context).textTheme.bodyLarge!.color!,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDateRange != null) {
      setState(() {
        _dateRange = pickedDateRange;
      });
      _loadJournalEntries();
    }
  }

  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ElevatedButton.icon(
              onPressed: _isProcessing ? null : _postSelectedEntries,
              icon: const Icon(Icons.check_circle_outline),
              label: const Text('ترحيل القيود المحددة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: AppColors.onPrimary,
              ),
            ),
            const SizedBox(width: 16),
            ElevatedButton.icon(
              onPressed: _isProcessing ? null : _unpostSelectedEntries,
              icon: const Icon(Icons.cancel_outlined),
              label: const Text('إلغاء ترحيل القيود المحددة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.warning,
                foregroundColor: AppColors.onPrimary,
              ),
            ),
            const SizedBox(width: 16),
            if (_isProcessing)
              const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacing8),
        Row(
          children: [
            TextButton.icon(
              onPressed: _isProcessing ? null : _selectAllEntries,
              icon: const Icon(Icons.select_all),
              label: const Text('تحديد الكل'),
            ),
            const SizedBox(width: 16),
            TextButton.icon(
              onPressed: _isProcessing ? null : _deselectAllEntries,
              icon: const Icon(Icons.deselect),
              label: const Text('إلغاء تحديد الكل'),
            ),
            const SizedBox(width: 16),
            Text('تم تحديد ${_selectedEntries.length} قيد'),
          ],
        ),
        const SizedBox(height: AppDimensions.spacing16),
        // حقل البحث
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            labelText: 'بحث في القيود',
            hintText: 'ابحث برقم القيد أو الوصف أو المرجع',
            prefixIcon: const Icon(Icons.search),
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                    },
                  )
                : null,
            border: const OutlineInputBorder(),
          ),
        ),
      ],
    );
  }

  /// تحديد جميع القيود
  void _selectAllEntries() {
    final entries = _journalEntryPresenter.journalEntries;

    setState(() {
      _selectedEntries.clear();
      for (final entry in entries) {
        _selectedEntries.add(entry.id);
      }
    });
  }

  /// إلغاء تحديد جميع القيود
  void _deselectAllEntries() {
    setState(() {
      _selectedEntries.clear();
    });
  }

  Widget _buildJournalEntriesTable() {
    return ListenableBuilder(
      listenable: _journalEntryPresenter,
      builder: (context, child) {
        if (_isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (_errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: AppColors.error,
                  size: 60,
                ),
                const SizedBox(height: AppDimensions.spacing16),
                Text(
                  _errorMessage!,
                  style: const AppTypography(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppDimensions.spacing16),
                ElevatedButton(
                  onPressed: _loadJournalEntries,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        // Filtrar las entradas según el término de búsqueda
        final allEntries = _journalEntryPresenter.journalEntries;
        final entries = _searchQuery.isEmpty
            ? allEntries
            : allEntries.where((entry) {
                final searchLower = _searchQuery.toLowerCase();
                return entry.entryNumber.toLowerCase().contains(searchLower) ||
                    entry.description.toLowerCase().contains(searchLower) ||
                    (entry.referenceNumber
                            ?.toLowerCase()
                            .contains(searchLower) ??
                        false);
              }).toList();

        if (entries.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.book,
                  color: AppColors.lightTextSecondary,
                  size: 60,
                ),
                SizedBox(height: 16),
                Text(
                  'لا توجد قيود محاسبية في الفترة المحددة',
                  style: AppTypography(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: SingleChildScrollView(
            child: DataTable(
              columns: const [
                DataColumn(label: Text('تحديد')),
                DataColumn(label: Text('الرقم')),
                DataColumn(label: Text('التاريخ')),
                DataColumn(label: Text('المرجع')),
                DataColumn(label: Text('الوصف')),
                DataColumn(label: Text('النوع')),
                DataColumn(label: Text('الحالة')),
                DataColumn(label: Text('المدين')),
                DataColumn(label: Text('الدائن')),
              ],
              rows: entries.map((entry) {
                final isSelected = _selectedEntries.contains(entry.id);

                return DataRow(
                  selected: isSelected,
                  cells: [
                    DataCell(
                      Checkbox(
                        value: isSelected,
                        onChanged: (value) {
                          setState(() {
                            if (value == true) {
                              _selectedEntries.add(entry.id);
                            } else {
                              _selectedEntries.remove(entry.id);
                            }
                          });
                        },
                      ),
                    ),
                    DataCell(Text(entry.entryNumber)),
                    DataCell(
                        Text(DateFormat('yyyy-MM-dd').format(entry.entryDate))),
                    DataCell(Text(entry.referenceNumber ?? '-')),
                    DataCell(Text(entry.description)),
                    DataCell(Text(_getEntryTypeText(entry.referenceType))),
                    DataCell(
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColorFromString(
                              entry.status.toString()),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _getStatusTextFromString(entry.status.toString()),
                          style: const AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                    DataCell(Text(
                      NumberFormat('#,##0.00').format(entry.totalDebit),
                      style: const AppTypography(
                        fontWeight: FontWeight.bold,
                      ),
                    )),
                    DataCell(Text(
                      NumberFormat('#,##0.00').format(entry.totalCredit),
                      style: const AppTypography(
                        fontWeight: FontWeight.bold,
                      ),
                    )),
                  ],
                );
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  /// الحصول على لون الحالة من النص
  Color _getStatusColorFromString(String status) {
    if (status.contains('draft')) {
      return AppColors.draft;
    } else if (status.contains('posted')) {
      return AppColors.posted;
    } else if (status.contains('voided') || status.contains('cancelled')) {
      return AppColors.voided;
    } else {
      return AppColors.disabled;
    }
  }

  /// الحصول على نص الحالة من النص
  String _getStatusTextFromString(String status) {
    if (status.contains('draft')) {
      return 'مسودة';
    } else if (status.contains('posted')) {
      return 'مرحل';
    } else if (status.contains('voided') || status.contains('cancelled')) {
      return 'ملغي';
    } else {
      return 'غير معروف';
    }
  }

  String _getEntryTypeText(String? type) {
    switch (type) {
      case 'salesInvoice':
        return 'فاتورة مبيعات';
      case 'purchaseInvoice':
        return 'فاتورة مشتريات';
      case 'receipt':
        return 'سند قبض';
      case 'payment':
        return 'سند صرف';
      case 'inventory':
        return 'حركة مخزون';
      case 'standard':
        return 'قيد عادي';
      case 'adjustment':
        return 'قيد تسوية';
      case 'opening':
        return 'قيد افتتاحي';
      case 'closing':
        return 'قيد إقفال';
      default:
        return type ?? 'غير محدد';
    }
  }
}
