// استيراد المكتبات الأساسية
import 'package:flutter/material.dart';

// استيراد ملفات التكوين والأدوات المساعدة
import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';

import '../../../core/utils/error_tracker.dart';
import '../../../core/utils/app_logger.dart';

// استيراد خدمات الجلسات
import '../../../core/services/session_manager.dart';

// استيراد نماذج وخدمات الإعداد
import '../models/setup_state.dart';
import '../services/setup_manager.dart';
import '../widgets/database_initialization_progress.dart';

/// شاشة الإعداد الأولي للتطبيق
/// تستخدم لإعداد قاعدة البيانات وتهيئة البيانات الأساسية عند تشغيل التطبيق لأول مرة
class InitialSetupScreen extends StatefulWidget {
  const InitialSetupScreen({Key? key}) : super(key: key);

  @override
  State<InitialSetupScreen> createState() => _InitialSetupScreenState();
}

/// حالة شاشة الإعداد الأولي
class _InitialSetupScreenState extends State<InitialSetupScreen> {
  /// مفتاح النموذج للتحقق من صحة المدخلات
  final _formKey = GlobalKey<FormState>();

  /// حالة الإعداد - تتبع حالة عملية الإعداد وتقدمها
  late final SetupState _setupState;

  /// مدير الإعداد - المسؤول عن تنفيذ عمليات الإعداد
  late final SetupManager _setupManager;

  /// مؤشر تحميل لعرض حالة التحميل
  bool _isLoading = false;

  /// مؤشر لحالة وجود خطأ أثناء الإعداد
  bool _hasError = false;

  @override
  void initState() {
    super.initState();

    // تهيئة حالة الإعداد ومدير الإعداد
    _setupState = SetupState();
    _setupManager = SetupManager(_setupState);

    _checkIfSetupAlreadyDone();

    // الاستماع لتغييرات حالة الإعداد
    _setupState.addListener(_onSetupStateChanged);
  }

  @override
  void dispose() {
    // إلغاء الاستماع لتغييرات حالة الإعداد
    _setupState.removeListener(_onSetupStateChanged);
    super.dispose();
  }

  /// تحديث حالة الشاشة عند تغيير حالة الإعداد
  /// هذه الدالة تستجيب للتغييرات في حالة الإعداد وتحدث واجهة المستخدم
  void _onSetupStateChanged() {
    if (mounted) {
      setState(() {
        // تحديث حالة الإعداد بناءً على الخطوة الحالية
        if (_setupState.currentStep == SetupStep.completed) {
          _isLoading = true; // نبقي على حالة التحميل لعرض شاشة النجاح
          AppLogger.info('✅ اكتمل إعداد النظام بنجاح');
          // لا نقوم بالانتقال هنا، سيتم ذلك من خلال _buildSuccessView
        } else if (_setupState.currentStep == SetupStep.error) {
          _hasError = true;
          _isLoading = false;
          AppLogger.error('❌ حدث خطأ أثناء إعداد النظام: ${_setupState.error}');
        }

        // يمكن إضافة منطق إضافي هنا للتعامل مع الخطوات الأخرى
      });
    }
  }

  /// التحقق مما إذا كان الإعداد قد تم بالفعل
  /// هذه الدالة تتحقق من حالة الإعداد باستخدام SessionManager
  Future<void> _checkIfSetupAlreadyDone() async {
    try {
      // التحقق من حالة الإعداد باستخدام SessionManager
      final bool isSetupCompleted = await SessionManager.isSetupCompleted();

      // إذا كان الإعداد قد تم بالفعل، يمكن اتخاذ إجراء هنا
      if (isSetupCompleted) {
        AppLogger.info('تم العثور على إعداد سابق للنظام');
        // يمكن إضافة منطق إضافي هنا للتعامل مع حالة الإعداد المسبق
      }
    } catch (e) {
      AppLogger.error('خطأ في التحقق من حالة الإعداد: $e');
      // في حالة حدوث خطأ، نفترض أن الإعداد لم يتم بعد
    }
  }

  /// إعداد قاعدة البيانات باستخدام مدير الإعداد
  /// هذه الدالة تبدأ عملية إعداد قاعدة البيانات وتهيئة البيانات الأساسية
  Future<void> _setupDatabase() async {
    // التحقق من وجود حالة النموذج وصحته قبل البدء
    if (_formKey.currentState == null || !_formKey.currentState!.validate()) {
      // إذا كانت حالة النموذج غير موجودة، نسجل ذلك
      if (_formKey.currentState == null) {
        AppLogger.warning(
            '⚠️ حالة النموذج غير موجودة (_formKey.currentState is null)');
      }
      return;
    }

    // تحديث حالة الواجهة لإظهار التحميل
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // تسجيل بدء عملية الإعداد
      AppLogger.info('🔄 بدء عملية إعداد النظام باستخدام مدير الإعداد...');

      // بدء عملية الإعداد باستخدام مدير الإعداد
      final setupResult = await _setupManager.startSetup();

      // التحقق من نتيجة الإعداد
      if (setupResult) {
        // تسجيل نجاح عملية الإعداد
        AppLogger.info('✅ تم إكمال إعداد النظام بنجاح!');

        // عرض رسالة نجاح للمستخدم
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إعداد النظام بنجاح!'),
              backgroundColor: AppColors.success,
              duration: Duration(seconds: 3),
            ),
          );

          // ملاحظة: الانتقال إلى شاشة تسجيل الدخول سيتم تلقائياً من خلال دالة _onSetupStateChanged
        }
        return;
      } else {
        // تسجيل فشل عملية الإعداد
        AppLogger.error('❌ فشل في إكمال إعداد النظام');

        // عرض رسالة خطأ للمستخدم
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'فشل في إكمال إعداد النظام: ${_setupState.error ?? "خطأ غير معروف"}'),
              backgroundColor: AppColors.error,
              duration: const Duration(seconds: 5),
            ),
          );
        }
        return;
      }
    } catch (e, stackTrace) {
      // تسجيل الخطأ في السجلات
      AppLogger.error('❌ خطأ عام في إعداد النظام: $e');

      // تسجيل الخطأ في نظام تتبع الأخطاء
      ErrorTracker.captureError(
        'فشل في إعداد النظام',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'setup_database'},
      );

      // تحديث حالة الواجهة لإظهار الخطأ
      setState(() {
        _isLoading = false;
        _hasError = true;
      });

      // عرض رسالة خطأ للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إعداد النظام: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  /// بناء واجهة المستخدم الرئيسية
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع عنوان مركزي
      appBar: AppBar(
        title: const Text('إعداد النظام'),
        centerTitle: true,
      ),
      // منطقة آمنة لتجنب تداخل المحتوى مع حواف الشاشة
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          // عرض واجهة التحميل أو نموذج الإعداد حسب حالة التحميل
          child: _isLoading ? _buildLoadingView() : _buildSetupForm(),
        ),
      ),
      // إضافة زر إعادة المحاولة في حالة وجود خطأ
      floatingActionButton: _hasError
          ? AkFloatingButton(
              icon: Icons.refresh,
              onPressed: _setupDatabase,
              tooltip: 'إعادة المحاولة',
              type: AkButtonType.primary,
            )
          : null,
    );
  }

  /// بناء واجهة التحميل وعرض حالة الإعداد
  Widget _buildLoadingView() {
    // إذا كانت عملية الإعداد مكتملة، نعرض رسالة النجاح
    if (_setupState.currentStep == SetupStep.completed) {
      return _buildSuccessView();
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // عنوان الإعداد
          const Text(
            'إعداد النظام',
            style: AppTypography(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 30),

          // استخدام ويدجت تقدم تهيئة قاعدة البيانات المحسن
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: DatabaseInitializationProgress(
              setupState: _setupState,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء نموذج الإعداد الأولي
  /// يعرض شاشة الترحيب وزر بدء الإعداد
  Widget _buildSetupForm() {
    return Form(
      key: _formKey, // ربط النموذج بمفتاح النموذج
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // عنوان الترحيب
            const Text(
              'مرحباً بك في إعداد النظام',
              style: AppTypography(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 20),

            // وصف عملية الإعداد
            const Text(
              'سيتم إعداد قاعدة البيانات وتهيئة البيانات الأساسية للنظام',
              textAlign: TextAlign.center,
              style: AppTypography(
                fontSize: 16,
                color: AppColors.secondary,
              ),
            ),
            const SizedBox(height: 40),

            // زر بدء الإعداد - يبدأ عملية إعداد قاعدة البيانات
            ElevatedButton(
              onPressed: _setupDatabase, // استدعاء دالة إعداد قاعدة البيانات
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor:
                    AppColors.onPrimary, // إضافة لون النص الأبيض للتباين
                padding:
                    const EdgeInsets.symmetric(horizontal: 50, vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'بدء الإعداد',
                style: AppTypography(
                  fontSize: 18,
                  color: AppColors.onPrimary, // تحديد لون النص بشكل صريح
                  fontWeight: FontWeight.bold, // جعل النص أكثر وضوحاً
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء واجهة النجاح بعد اكتمال الإعداد
  Widget _buildSuccessView() {
    // بدء مؤقت للانتقال التلقائي إلى شاشة تسجيل الدخول
    _startRedirectTimer();

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // أيقونة النجاح
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check_circle_outline,
              color: AppColors.success,
              size: 80,
            ),
          ),
          const SizedBox(height: 30),

          // عنوان النجاح
          const Text(
            'تم إعداد النظام بنجاح!',
            style: AppTypography(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.success,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),

          // رسالة النجاح
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.lightSurface,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  spreadRadius: 2,
                  blurRadius: 5,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: const Column(
              children: [
                Text(
                  'تم إعداد قاعدة البيانات وتهيئة البيانات الأساسية بنجاح',
                  style: AppTypography(
                    fontSize: 16,
                    color: AppColors.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 15),
                Text(
                  'سيتم نقلك إلى شاشة تسجيل الدخول خلال لحظات...',
                  style: AppTypography(
                    fontSize: 16,
                    color: AppColors.secondary,
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 20),

                // مؤشر التحميل الدائري
                SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppColors.success),
                    strokeWidth: 3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بدء مؤقت للانتقال التلقائي إلى شاشة تسجيل الدخول
  void _startRedirectTimer() {
    // تأخير لمدة 5 ثوان ثم الانتقال إلى شاشة تسجيل الدخول
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/login');
      }
    });
  }
}
