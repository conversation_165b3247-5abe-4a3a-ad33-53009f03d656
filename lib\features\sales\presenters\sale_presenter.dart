import 'package:flutter/foundation.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/models/sale.dart';

import '../../../core/models/sale_status.dart';
import '../services/sale_service.dart';

/// مقدم المبيعات
class SalePresenter extends ChangeNotifier {
  final SaleService _saleService = SaleService();

  List<Sale> _sales = [];
  Sale? _currentSale;
  bool _isLoading = false;
  String? _errorMessage;
  String? _searchQuery;
  String? _selectedStatus;
  String? _selectedCustomerId;
  DateTime? _startDate;
  DateTime? _endDate;

  /// الحصول على قائمة المبيعات
  List<Sale> get sales => _sales;

  /// الحصول على المبيع الحالي
  Sale? get currentSale => _currentSale;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// رسالة الخطأ (للتوافق مع الكود القديم)
  String? get error => _errorMessage;

  /// استعلام البحث
  String? get searchQuery => _searchQuery;

  /// تعيين استعلام البحث
  set searchQuery(String? value) {
    _searchQuery = value;
    loadSales();
  }

  /// الحالة المحددة
  String? get selectedStatus => _selectedStatus;

  /// تعيين الحالة المحددة
  set selectedStatus(String? value) {
    _selectedStatus = value;
    loadSales();
  }

  /// العميل المحدد
  String? get selectedCustomerId => _selectedCustomerId;

  /// تعيين العميل المحدد
  set selectedCustomerId(String? value) {
    _selectedCustomerId = value;
    loadSales();
  }

  /// تعيين نطاق التاريخ
  set dateRange(({DateTime? start, DateTime? end}) range) {
    _startDate = range.start;
    _endDate = range.end;
    loadSales();
  }

  /// تهيئة المقدم
  Future<void> init() async {
    await loadSales();
  }

  /// تحميل المبيعات
  Future<void> loadSales({String? type}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // بناء معايير البحث
      final filters = {
        'type': type,
        'search': _searchQuery,
        'status': _selectedStatus,
        'customerId': _selectedCustomerId,
        'startDate': _startDate,
        'endDate': _endDate,
      };

      final sales = await _saleService.getAllSales(filters: filters);

      _sales = sales;
      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المبيعات: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل المبيعات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
      rethrow;
    }
  }

  /// الحصول على مبيعات عميل معين
  Future<List<Sale>> getSalesByCustomerId(String customerId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final sales = await _saleService.getSalesByCustomerId(customerId);

      _isLoading = false;
      notifyListeners();

      return sales;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل مبيعات العميل: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل مبيعات العميل',
        error: e,
        stackTrace: stackTrace,
        context: {
          'customer_id': customerId,
        },
      );
      notifyListeners();
      return [];
    }
  }

  /// الحصول على مبيعات في فترة زمنية معينة
  Future<List<Sale>> getSalesByDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final sales = await _saleService.getSalesByDateRange(startDate, endDate);

      _isLoading = false;
      notifyListeners();

      return sales;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المبيعات في الفترة الزمنية: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل المبيعات في الفترة الزمنية',
        error: e,
        stackTrace: stackTrace,
        context: {
          'start_date': startDate.toIso8601String(),
          'end_date': endDate.toIso8601String(),
        },
      );
      notifyListeners();
      return [];
    }
  }

  /// تحميل مبيع بواسطة المعرف
  Future<void> loadSale(String id) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      _currentSale = await _saleService.getSaleById(id);

      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المبيع: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل المبيع',
        error: e,
        stackTrace: stackTrace,
        context: {
          'sale_id': id,
        },
      );
      notifyListeners();
    }
  }

  /// الحصول على مبيع بواسطة المعرف
  Future<Sale?> getSaleById(String id) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final sale = await _saleService.getSaleById(id);

      _isLoading = false;
      notifyListeners();

      return sale;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المبيع: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل المبيع',
        error: e,
        stackTrace: stackTrace,
        context: {
          'sale_id': id,
        },
      );
      notifyListeners();
      return null;
    }
  }

  /// إنشاء مبيع جديد
  Future<bool> createSale(Sale sale) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // إنشاء المبيع في قاعدة البيانات
      final newSaleId = await _saleService.createSale(
        items: sale.items,
        customerId: sale.customerId,
        customerName: sale.customerName,
        paymentMethod: sale.paymentMethod,
        amountPaid: sale.amountPaid,
        subtotal: sale.subtotal,
        discount: sale.discount,
        isDiscountPercentage: sale.isDiscountPercentage,
        tax: sale.tax,
        total: sale.total,
        notes: sale.notes,
      );

      if (newSaleId != null) {
        // إضافة المبيع الجديد إلى القائمة
        final newSale = sale.copyWith(id: newSaleId);
        _sales.insert(0, newSale);
      }

      _isLoading = false;
      notifyListeners();

      return newSaleId != null;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء إنشاء المبيع: $e';
      ErrorTracker.captureError(
        'خطأ في إنشاء المبيع',
        error: e,
        stackTrace: stackTrace,
        context: {
          'sale': sale.toMap(),
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// إلغاء مبيع
  Future<bool> cancelSale(String id) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _saleService.cancelSale(id);

      if (success) {
        // تحديث حالة المبيع في القائمة
        final index = _sales.indexWhere((sale) => sale.id == id);
        if (index >= 0) {
          _sales[index] = _sales[index].copyWith(
            status: SaleStatus.cancelled,
            updatedAt: DateTime.now(),
          );
        }
      }

      _isLoading = false;
      notifyListeners();

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء إلغاء المبيع: $e';
      ErrorTracker.captureError(
        'خطأ في إلغاء المبيع',
        error: e,
        stackTrace: stackTrace,
        context: {
          'sale_id': id,
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// حذف مبيع
  Future<bool> deleteSale(String id) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _saleService.deleteSale(id);

      if (success) {
        // حذف المبيع من القائمة
        _sales.removeWhere((sale) => sale.id == id);
      }

      _isLoading = false;
      notifyListeners();

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء حذف المبيع: $e';
      ErrorTracker.captureError(
        'خطأ في حذف المبيع',
        error: e,
        stackTrace: stackTrace,
        context: {
          'sale_id': id,
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// تحديث مبيع
  Future<bool> updateSale(Sale sale) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // تحديث المبيع في قاعدة البيانات
      // هذه الدالة غير موجودة حاليًا في SaleService
      // يجب إضافتها في المستقبل
      const success = true; // مؤقتًا

      if (success) {
        // تحديث المبيع في القائمة
        final index = _sales.indexWhere((s) => s.id == sale.id);
        if (index >= 0) {
          _sales[index] = sale;
        }

        // تحديث المبيع الحالي إذا كان هو نفسه
        if (_currentSale?.id == sale.id) {
          _currentSale = sale;
        }
      }

      _isLoading = false;
      notifyListeners();

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحديث المبيع: $e';
      ErrorTracker.captureError(
        'خطأ في تحديث المبيع',
        error: e,
        stackTrace: stackTrace,
        context: {
          'sale_id': sale.id,
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// الحصول على إحصائيات المبيعات
  Future<Map<String, dynamic>> getSalesStatistics() async {
    try {
      // حساب الإحصائيات من البيانات المحملة
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final thisMonth = DateTime(now.year, now.month, 1);
      final thisYear = DateTime(now.year, 1, 1);

      // المبيعات المكتملة فقط
      final completedSales =
          _sales.where((sale) => sale.status == SaleStatus.completed).toList();

      // إجمالي المبيعات اليومية
      final dailySales = completedSales
          .where((sale) => sale.createdAt.isAfter(today))
          .fold(0.0, (sum, sale) => sum + sale.total);

      // إجمالي المبيعات الشهرية
      final monthlySales = completedSales
          .where((sale) => sale.createdAt.isAfter(thisMonth))
          .fold(0.0, (sum, sale) => sum + sale.total);

      // إجمالي المبيعات السنوية
      final yearlySales = completedSales
          .where((sale) => sale.createdAt.isAfter(thisYear))
          .fold(0.0, (sum, sale) => sum + sale.total);

      return {
        'daily': dailySales,
        'monthly': monthlySales,
        'yearly': yearlySales,
        'count': completedSales.length,
      };
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في حساب إحصائيات المبيعات',
        error: e,
        stackTrace: stackTrace,
      );
      return {
        'daily': 0.0,
        'monthly': 0.0,
        'yearly': 0.0,
        'count': 0,
      };
    }
  }

  /// الحصول على إجمالي المبيعات
  double getTotalSales() {
    return _sales.fold(0, (sum, sale) => sum + sale.total);
  }

  /// الحصول على إجمالي المبيعات المكتملة
  double getTotalCompletedSales() {
    return _sales
        .where((sale) => sale.status == SaleStatus.completed)
        .fold(0, (sum, sale) => sum + sale.total);
  }

  /// الحصول على إجمالي المبيعات الملغاة
  double getTotalCancelledSales() {
    return _sales
        .where((sale) => sale.status == SaleStatus.cancelled)
        .fold(0, (sum, sale) => sum + sale.total);
  }

  /// الحصول على عدد المبيعات
  int getSalesCount() {
    return _sales.length;
  }

  /// الحصول على عدد المبيعات المكتملة
  int getCompletedSalesCount() {
    return _sales.where((sale) => sale.status == SaleStatus.completed).length;
  }

  /// الحصول على عدد المبيعات الملغاة
  int getCancelledSalesCount() {
    return _sales.where((sale) => sale.status == SaleStatus.cancelled).length;
  }
}
