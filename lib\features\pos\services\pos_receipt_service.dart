import 'package:flutter/material.dart';
import '../../../core/models/sale.dart';
import '../../../core/models/customer.dart';
import '../../../core/models/company_profile.dart';
import '../../../data/repositories/settings_repository.dart';

/// خدمة طباعة إيصالات نقاط البيع
/// توفر وظائف لإنشاء وطباعة إيصالات المبيعات
class POSReceiptService {
  final SettingsRepository _settingsRepository;

  POSReceiptService({required SettingsRepository settingsRepository})
      : _settingsRepository = settingsRepository;

  /// طباعة إيصال لعملية بيع
  Future<bool> printReceipt(Sale sale, {Customer? customer}) async {
    try {
      // الحصول على إعدادات التطبيق
      final appSettings = await _settingsRepository.getAppSettings();
      final printerEnabled = appSettings['printer_enabled'] ?? false;

      if (!printerEnabled) {
        debugPrint('الطابعة غير مفعلة في الإعدادات');
        return false;
      }

      // الحصول على معلومات الشركة
      final companyProfile = await _settingsRepository.getCompanyProfile();

      // إنشاء محتوى الإيصال
      final receiptContent = _generateReceiptContent(
        sale: sale,
        customer: customer,
        companyProfile: companyProfile,
      );

      // طباعة الإيصال
      final printerType = appSettings['printer_type'] ?? 'regular';
      if (printerType == 'pos') {
        // طباعة على طابعة نقاط البيع
        return await _printToPOSPrinter(
          receiptContent,
          appSettings['printer_ip'] ?? '',
          appSettings['printer_commands'] as String?,
        );
      } else {
        // طباعة على طابعة عادية
        return await _printToRegularPrinter(receiptContent);
      }
    } catch (e) {
      debugPrint('خطأ في طباعة الإيصال: $e');
      return false;
    }
  }

  /// إنشاء محتوى الإيصال
  String _generateReceiptContent({
    required Sale sale,
    Customer? customer,
    CompanyProfile? companyProfile,
  }) {
    final buffer = StringBuffer();

    // معلومات الشركة
    if (companyProfile != null) {
      buffer.writeln(companyProfile.name);
      if (companyProfile.address != null) {
        buffer.writeln(companyProfile.address);
      }
      if (companyProfile.phone != null) {
        buffer.writeln('هاتف: ${companyProfile.phone}');
      }
      if (companyProfile.taxNumber != null) {
        buffer.writeln('الرقم الضريبي: ${companyProfile.taxNumber}');
      }
    }

    buffer.writeln('\n');
    buffer.writeln('فاتورة مبيعات');
    buffer.writeln('رقم الفاتورة: ${sale.id}');
    buffer.writeln('التاريخ: ${_formatDate(sale.date)}');
    buffer.writeln('الوقت: ${_formatTime(sale.date)}');

    // معلومات العميل
    if (customer != null) {
      buffer.writeln('\nالعميل: ${customer.name}');
      if (customer.phone != null) {
        buffer.writeln('هاتف العميل: ${customer.phone}');
      }
    } else if (sale.customerId != null) {
      buffer.writeln('\nالعميل: ${sale.customerId}');
    } else {
      buffer.writeln('\nالعميل: عميل عادي');
    }

    // خط فاصل
    buffer.writeln('\n${'-' * 40}');

    // عناوين الأعمدة
    buffer.writeln('المنتج                  الكمية    السعر    الإجمالي');
    buffer.writeln('-' * 40);

    // تفاصيل المنتجات
    for (var item in sale.items) {
      final productName =
          _truncateText(item.productName ?? 'منتج غير معروف', 20);
      final quantity = item.quantity.toString().padLeft(5);
      final price = item.price.toStringAsFixed(2).padLeft(8);
      final total = item.total.toStringAsFixed(2).padLeft(10);

      buffer.writeln('$productName $quantity $price $total');
    }

    // خط فاصل
    buffer.writeln('-' * 40);

    // المجاميع
    buffer.writeln(
        'المجموع الفرعي:${sale.subtotal.toStringAsFixed(2).padLeft(28)}');
    if (sale.discount > 0) {
      buffer.writeln('الخصم:${sale.discount.toStringAsFixed(2).padLeft(35)}');
    }
    if (sale.tax > 0) {
      buffer.writeln('الضريبة:${sale.tax.toStringAsFixed(2).padLeft(35)}');
    }
    buffer.writeln('الإجمالي:${sale.total.toStringAsFixed(2).padLeft(34)}');

    // طريقة الدفع
    buffer
        .writeln('\nطريقة الدفع: ${_getPaymentMethodName(sale.paymentMethod)}');

    // ملاحظات
    if (sale.notes != null && sale.notes!.isNotEmpty) {
      buffer.writeln('\nملاحظات: ${sale.notes}');
    }

    // تذييل
    buffer.writeln('\n${'-' * 40}');
    buffer.writeln('شكراً لتسوقكم معنا');
    if (companyProfile?.receiptFooter != null) {
      buffer.writeln(companyProfile!.receiptFooter);
    }

    return buffer.toString();
  }

  /// طباعة على طابعة نقاط البيع
  Future<bool> _printToPOSPrinter(
    String content,
    String printerAddress,
    String? printerCommands,
  ) async {
    // هنا يتم تنفيذ الطباعة على طابعة نقاط البيع
    // يمكن استخدام مكتبات مثل esc_pos_printer أو esc_pos_bluetooth
    // للتعامل مع طابعات نقاط البيع

    // هذه مجرد محاكاة للطباعة
    debugPrint('طباعة على طابعة نقاط البيع: $printerAddress');
    debugPrint('محتوى الإيصال:');
    debugPrint(content);

    // في التطبيق الفعلي، يجب تنفيذ الاتصال بالطابعة وإرسال البيانات
    return true;
  }

  /// طباعة على طابعة عادية
  Future<bool> _printToRegularPrinter(String content) async {
    // هنا يتم تنفيذ الطباعة على طابعة عادية
    // يمكن استخدام مكتبات مثل printing أو pdf للطباعة العادية

    // هذه مجرد محاكاة للطباعة
    debugPrint('طباعة على طابعة عادية');
    debugPrint('محتوى الإيصال:');
    debugPrint(content);

    // في التطبيق الفعلي، يجب تنفيذ الطباعة
    return true;
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
  }

  /// تنسيق الوقت
  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// الحصول على اسم طريقة الدفع
  String _getPaymentMethodName(String? method) {
    switch (method) {
      case 'cash':
        return 'نقداً';
      case 'card':
        return 'بطاقة';
      case 'bank_transfer':
        return 'تحويل بنكي';
      default:
        return method ?? 'غير محدد';
    }
  }

  /// اقتطاع النص إذا كان طويلاً
  String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text.padRight(maxLength);
    }
    return '${text.substring(0, maxLength - 3)}...'.padRight(maxLength);
  }
}
