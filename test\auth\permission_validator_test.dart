import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/auth/permission_validator.dart';
import 'package:tajer_plus/core/auth/roles_schema.dart';
import 'package:tajer_plus/core/utils/app_logger.dart';

void main() {
  group('PermissionValidator Tests', () {
    test('No duplicate permission names', () {
      // جمع جميع أسماء الصلاحيات
      final permissionNames = <String>[];
      final duplicates = <String>[];

      // البحث عن التكرارات
      RolesSchema.permissions.forEach((module, perms) {
        perms.forEach((code, name) {
          if (permissionNames.contains(name)) {
            duplicates.add(name);
          } else {
            permissionNames.add(name);
          }
        });
      });

      // تسجيل التكرارات إن وجدت
      if (duplicates.isNotEmpty) {
        AppLogger.warning('Duplicate permission names found:');
        for (final name in duplicates) {
          AppLogger.warning('  - $name');
        }
      }

      // التأكد من عدم وجود تكرارات
      expect(duplicates, isEmpty, reason: 'Duplicate permission names found');
    });

    test('All role permission codes are valid', () {
      // جمع جميع رموز الصلاحيات المتاحة
      final allPermissionCodes = <String>[];
      final invalidCodes = <String>[];

      // جمع جميع رموز الصلاحيات
      RolesSchema.permissions.forEach((module, perms) {
        perms.forEach((code, name) {
          allPermissionCodes.add(code);
        });
      });

      // التحقق من صحة رموز الصلاحيات في تعريفات الأدوار
      RolesSchema.defaultRolePermissions.forEach((role, permissions) {
        for (final permissionCode in permissions) {
          if (!allPermissionCodes.contains(permissionCode)) {
            invalidCodes.add(permissionCode);
          }
        }
      });

      // تسجيل الرموز غير الصالحة إن وجدت
      if (invalidCodes.isNotEmpty) {
        AppLogger.warning('Invalid permission codes found:');
        for (final code in invalidCodes) {
          AppLogger.warning('  - $code');
        }
      }

      // التأكد من عدم وجود رموز غير صالحة
      expect(invalidCodes, isEmpty, reason: 'Invalid permission codes found');
    });

    test('Permission validator returns true for valid schema', () {
      // التحقق من صحة تعريفات الصلاحيات والأدوار
      final isValid = PermissionValidator.validateAll();

      // التأكد من أن التحقق ناجح
      expect(isValid, isTrue, reason: 'Permission validation failed');
    });
  });
}
