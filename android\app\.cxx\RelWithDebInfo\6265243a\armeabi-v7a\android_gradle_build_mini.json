{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterproject2\\New folder\\tajer_plus\\android\\app\\.cxx\\RelWithDebInfo\\6265243a\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterproject2\\New folder\\tajer_plus\\android\\app\\.cxx\\RelWithDebInfo\\6265243a\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}