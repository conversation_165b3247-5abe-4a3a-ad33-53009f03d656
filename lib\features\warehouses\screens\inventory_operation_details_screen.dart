import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/models/inventory_adjustment.dart';
import '../../../core/models/inventory_adjustment_item.dart';
import '../../../core/models/inventory_transfer.dart';
import '../../../core/models/inventory_transfer_item.dart';

import '../../../core/widgets/safe_layout.dart';
import '../presenters/inventory_adjustment_presenter.dart';
import '../presenters/inventory_transfer_presenter.dart';
import '../../../core/theme/index.dart';

/// شاشة تفاصيل عملية المخزون
class InventoryOperationDetailsScreen extends StatefulWidget {
  final dynamic operation;
  final String operationType;

  const InventoryOperationDetailsScreen({
    Key? key,
    required this.operation,
    required this.operationType,
  }) : super(key: key);

  @override
  State<InventoryOperationDetailsScreen> createState() =>
      _InventoryOperationDetailsScreenState();
}

class _InventoryOperationDetailsScreenState
    extends State<InventoryOperationDetailsScreen> {
  late InventoryAdjustmentPresenter _adjustmentPresenter;
  late InventoryTransferPresenter _transferPresenter;

  @override
  void initState() {
    super.initState();
    _adjustmentPresenter =
        AppProviders.getLazyPresenter<InventoryAdjustmentPresenter>(
            () => InventoryAdjustmentPresenter());
    _transferPresenter =
        AppProviders.getLazyPresenter<InventoryTransferPresenter>(
            () => InventoryTransferPresenter());
  }

  @override
  Widget build(BuildContext context) {
    return SafeLayout(
      title: _getTitle(),
      body: _buildBody(),
      child: Container(),
    );
  }

  /// الحصول على عنوان الشاشة
  String _getTitle() {
    if (widget.operationType == 'adjustment') {
      final adjustment = widget.operation as InventoryAdjustment;
      return 'تفاصيل تعديل المخزون: ${adjustment.referenceNumber ?? ''}';
    } else if (widget.operationType == 'transfer') {
      final transfer = widget.operation as InventoryTransfer;
      return 'تفاصيل تحويل المخزون: ${transfer.referenceNumber ?? ''}';
    }
    return 'تفاصيل عملية المخزون';
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (widget.operationType == 'adjustment') {
      return _buildAdjustmentDetails();
    } else if (widget.operationType == 'transfer') {
      return _buildTransferDetails();
    }
    return const Center(
      child: Text('نوع العملية غير معروف'),
    );
  }

  /// بناء تفاصيل تعديل المخزون
  Widget _buildAdjustmentDetails() {
    final adjustment = widget.operation as InventoryAdjustment;
    final warehouseName =
        _adjustmentPresenter.getWarehouseName(adjustment.warehouseId);
    final adjustmentTypeName =
        _adjustmentPresenter.getAdjustmentTypeName(adjustment.adjustmentType);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            title: 'معلومات التعديل',
            content: [
              _buildInfoRow('الرقم المرجعي', adjustment.referenceNumber ?? '-'),
              _buildInfoRow('التاريخ', _formatDate(adjustment.date)),
              _buildInfoRow('المستودع', warehouseName),
              _buildInfoRow('نوع التعديل', adjustmentTypeName),
              if (adjustment.notes != null && adjustment.notes!.isNotEmpty)
                _buildInfoRow('ملاحظات', adjustment.notes!),
              _buildInfoRow(
                  'تاريخ الإنشاء', _formatDateTime(adjustment.createdAt)),
              if (adjustment.createdBy != null)
                _buildInfoRow('بواسطة', adjustment.createdBy!),
              if (adjustment.updatedAt != null)
                _buildInfoRow(
                    'تاريخ التحديث', _formatDateTime(adjustment.updatedAt!)),
              if (adjustment.updatedBy != null)
                _buildInfoRow('تم التحديث بواسطة', adjustment.updatedBy!),
            ],
          ),
          const SizedBox(height: 16),
          _buildItemsCard(
            title: 'العناصر',
            items: adjustment.items,
            isAdjustment: true,
          ),
        ],
      ),
    );
  }

  /// بناء تفاصيل تحويل المخزون
  Widget _buildTransferDetails() {
    final transfer = widget.operation as InventoryTransfer;
    final sourceWarehouseName =
        _transferPresenter.getWarehouseName(transfer.sourceWarehouseId);
    final destinationWarehouseName =
        _transferPresenter.getWarehouseName(transfer.destinationWarehouseId);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            title: 'معلومات التحويل',
            content: [
              _buildInfoRow('الرقم المرجعي', transfer.referenceNumber ?? '-'),
              _buildInfoRow('التاريخ', _formatDate(transfer.date)),
              _buildInfoRow('من المستودع', sourceWarehouseName),
              _buildInfoRow('إلى المستودع', destinationWarehouseName),
              _buildInfoRow(
                  'الحالة', transfer.isCompleted ? 'مكتمل' : 'قيد التنفيذ'),
              if (transfer.notes != null && transfer.notes!.isNotEmpty)
                _buildInfoRow('ملاحظات', transfer.notes!),
              _buildInfoRow(
                  'تاريخ الإنشاء', _formatDateTime(transfer.createdAt)),
              if (transfer.createdBy != null)
                _buildInfoRow('بواسطة', transfer.createdBy!),
              if (transfer.updatedAt != null)
                _buildInfoRow(
                    'تاريخ التحديث', _formatDateTime(transfer.updatedAt!)),
              if (transfer.updatedBy != null)
                _buildInfoRow('تم التحديث بواسطة', transfer.updatedBy!),
            ],
          ),
          const SizedBox(height: 16),
          _buildItemsCard(
            title: 'العناصر',
            items: transfer.items,
            isAdjustment: false,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة معلومات
  Widget _buildInfoCard(
      {required String title, required List<Widget> content}) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTypography.lightTextTheme.headlineMedium?.copyWith(
                    color: AppColors.lightTextSecondary,
                    fontWeight: FontWeight.bold,
                  ) ??
                  const AppTypography(
                    color: AppColors.lightTextSecondary,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const Divider(),
            const SizedBox(height: 8),
            ...content,
          ],
        ),
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              '$label:',
              style: AppTypography.lightTextTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ) ??
                  const AppTypography(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTypography.lightTextTheme.bodyLarge,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة العناصر
  Widget _buildItemsCard({
    required String title,
    required List<dynamic> items,
    required bool isAdjustment,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTypography.lightTextTheme.headlineMedium?.copyWith(
                    color: AppColors.lightTextSecondary,
                    fontWeight: FontWeight.bold,
                  ) ??
                  const AppTypography(
                    color: AppColors.lightTextSecondary,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const Divider(),
            const SizedBox(height: 8),
            if (items.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Text('لا توجد عناصر'),
                ),
              )
            else
              isAdjustment
                  ? _buildAdjustmentItemsTable(
                      items as List<InventoryAdjustmentItem>)
                  : _buildTransferItemsTable(
                      items as List<InventoryTransferItem>),
          ],
        ),
      ),
    );
  }

  /// بناء جدول عناصر التعديل
  Widget _buildAdjustmentItemsTable(List<InventoryAdjustmentItem> items) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('المنتج')),
          DataColumn(label: Text('الكمية')),
          DataColumn(label: Text('ملاحظات')),
        ],
        rows: items.map((item) {
          final productName = item.product?.name ??
              _adjustmentPresenter.getProductName(item.productId);
          return DataRow(
            cells: [
              DataCell(Text(productName)),
              DataCell(Text(item.quantity.toString())),
              DataCell(Text(item.notes ?? '-')),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// بناء جدول عناصر التحويل
  Widget _buildTransferItemsTable(List<InventoryTransferItem> items) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('المنتج')),
          DataColumn(label: Text('الكمية')),
          DataColumn(label: Text('ملاحظات')),
        ],
        rows: items.map((item) {
          final productName = item.product?.name ??
              _transferPresenter.getProductName(item.productId);
          return DataRow(
            cells: [
              DataCell(Text(productName)),
              DataCell(Text(item.quantity.toString())),
              DataCell(Text(item.notes ?? '-')),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day} ${dateTime.hour}:${dateTime.minute}';
  }
}
