import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/index.dart';
import '../models/user_group.dart';
import '../presenters/user_presenter.dart';
import '../../../core/theme/index.dart';

/// شاشة نموذج مجموعة المستخدمين (إضافة/تعديل)
class UserGroupFormScreen extends StatefulWidget {
  final UserGroup? group;

  const UserGroupFormScreen({Key? key, this.group}) : super(key: key);

  @override
  State<UserGroupFormScreen> createState() => _UserGroupFormScreenState();
}

class _UserGroupFormScreenState extends State<UserGroupFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  bool _isActive = true;
  bool _isLoading = false;
  bool _isNewGroup = true;

  // استخدام خريطة للصلاحيات في واجهة المستخدم لسهولة التعامل معها
  Map<String, dynamic> _permissionsUI = {};

  // قائمة رموز الصلاحيات التي سيتم تخزينها في قاعدة البيانات
  List<String> _permissionCodes = [];

  // استخدام التحميل الكسول
  late final UserPresenter _userPresenter;

  @override
  void initState() {
    super.initState();
    _isNewGroup = widget.group == null;
    _userPresenter =
        AppProviders.getLazyPresenter<UserPresenter>(() => UserPresenter());

    // تهيئة البيانات إذا كان في وضع التعديل
    if (widget.group != null) {
      _nameController.text = widget.group!.name;
      _descriptionController.text = widget.group!.description ?? '';
      _isActive = widget.group!.isActive;

      // تحويل رموز الصلاحيات إلى واجهة المستخدم
      _permissionCodes = List<String>.from(widget.group!.permissionCodes);
      _convertPermissionCodesToUI();
    } else {
      // تهيئة الصلاحيات الافتراضية
      _initDefaultPermissions();
    }
  }

  /// تهيئة الصلاحيات الافتراضية
  void _initDefaultPermissions() {
    _permissionsUI = {
      'full_access': false,
      'users': {
        'view': false,
        'add': false,
        'edit': false,
        'delete': false,
      },
      'accounts': {
        'view': false,
        'add': false,
        'edit': false,
        'delete': false,
      },
      'products': {
        'view': false,
        'add': false,
        'edit': false,
        'delete': false,
      },
      'sales': {
        'view': false,
        'add': false,
        'edit': false,
        'delete': false,
      },
      'purchases': {
        'view': false,
        'add': false,
        'edit': false,
        'delete': false,
      },
      'reports': {
        'view': false,
        'export': false,
      },
      'settings': {
        'view': false,
        'edit': false,
      },
      'currencies': {
        'view': false,
        'add': false,
        'edit': false,
        'delete': false,
      },
      'vouchers': {
        'view': false,
        'add': false,
        'edit': false,
        'delete': false,
      },
    };

    // تهيئة قائمة رموز الصلاحيات فارغة
    _permissionCodes = [];
  }

  /// تحويل رموز الصلاحيات إلى واجهة المستخدم
  void _convertPermissionCodesToUI() {
    // تهيئة واجهة المستخدم بالقيم الافتراضية
    _initDefaultPermissions();

    // تعيين قيم الصلاحيات بناءً على الرموز
    for (final code in _permissionCodes) {
      _setPermissionByCode(code, true);
    }
  }

  /// تعيين صلاحية بناءً على الرمز
  void _setPermissionByCode(String code, bool value) {
    // تقسيم الرمز إلى أجزاء (مثال: users.view)
    final parts = code.split('.');

    if (parts.length == 2) {
      final module = parts[0];
      final action = parts[1];

      if (_permissionsUI.containsKey(module) &&
          _permissionsUI[module] is Map<String, dynamic> &&
          (_permissionsUI[module] as Map<String, dynamic>)
              .containsKey(action)) {
        _permissionsUI[module][action] = value;
      }
    } else if (code == 'full_access') {
      _permissionsUI['full_access'] = value;
      if (value) {
        _setAllPermissions(true);
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: _isNewGroup ? 'إضافة مجموعة جديدة' : 'تعديل المجموعة',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AkCard(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'معلومات المجموعة',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      // اسم المجموعة
                      AkTextInput(
                        controller: _nameController,
                        label: 'اسم المجموعة',
                        hint: 'أدخل اسم المجموعة',
                        isRequired: true,
                        prefixIcon: Icons.group,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال اسم المجموعة';
                          }
                          if (value.length < 3) {
                            return 'اسم المجموعة يجب أن يكون 3 أحرف على الأقل';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // وصف المجموعة
                      AkTextInput(
                        controller: _descriptionController,
                        label: 'وصف المجموعة',
                        hint: 'أدخل وصف المجموعة',
                        prefixIcon: Icons.description,
                        maxLines: 3,
                      ),
                      const SizedBox(height: 16),
                      // حالة النشاط
                      SwitchListTile(
                        title: const Text('نشط'),
                        value: _isActive,
                        onChanged: (value) {
                          setState(() {
                            _isActive = value;
                          });
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // الصلاحيات
              AkCard(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الصلاحيات',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      // وصول كامل
                      SwitchListTile(
                        title: const Text('وصول كامل للنظام'),
                        subtitle: const Text(
                            'منح جميع الصلاحيات للمستخدمين في هذه المجموعة'),
                        value: _permissionsUI['full_access'] ?? false,
                        onChanged: (value) {
                          setState(() {
                            _permissionsUI['full_access'] = value;
                            if (value) {
                              _setAllPermissions(true);
                            } else {
                              _updatePermissionCodes();
                            }
                          });
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                      const Divider(),
                      // صلاحيات المستخدمين
                      _buildPermissionSection(
                        'المستخدمين',
                        'users',
                        Icons.people,
                      ),
                      const Divider(),
                      // صلاحيات الحسابات
                      _buildPermissionSection(
                        'الحسابات',
                        'accounts',
                        Icons.account_balance,
                      ),
                      const Divider(),
                      // صلاحيات المنتجات
                      _buildPermissionSection(
                        'المنتجات',
                        'products',
                        Icons.inventory,
                      ),
                      const Divider(),
                      // صلاحيات المبيعات
                      _buildPermissionSection(
                        'المبيعات',
                        'sales',
                        Icons.point_of_sale,
                      ),
                      const Divider(),
                      // صلاحيات المشتريات
                      _buildPermissionSection(
                        'المشتريات',
                        'purchases',
                        Icons.shopping_cart,
                      ),
                      const Divider(),
                      // صلاحيات التقارير
                      _buildPermissionSection(
                        'التقارير',
                        'reports',
                        Icons.bar_chart,
                        hasDelete: false,
                      ),
                      const Divider(),
                      // صلاحيات الإعدادات
                      _buildPermissionSection(
                        'الإعدادات',
                        'settings',
                        Icons.settings,
                        hasDelete: false,
                      ),
                      const Divider(),
                      // صلاحيات العملات
                      _buildPermissionSection(
                        'العملات',
                        'currencies',
                        Icons.currency_exchange,
                      ),
                      const Divider(),
                      // صلاحيات السندات
                      _buildPermissionSection(
                        'السندات',
                        'vouchers',
                        Icons.receipt_long,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // زر الحفظ
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveGroup,
                  child: _isLoading
                      ? const CircularProgressIndicator(
                          color: AppColors.onPrimary)
                      : Text(_isNewGroup ? 'إضافة المجموعة' : 'حفظ التغييرات'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قسم الصلاحيات
  Widget _buildPermissionSection(
    String title,
    String permissionKey,
    IconData icon, {
    bool hasDelete = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: Icon(icon),
          title: Text(title),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // زر تحديد الكل
              TextButton(
                onPressed: () {
                  setState(() {
                    _setModulePermissions(permissionKey, true);
                  });
                },
                child: const Text('تحديد الكل'),
              ),
              // زر إلغاء تحديد الكل
              TextButton(
                onPressed: () {
                  setState(() {
                    _setModulePermissions(permissionKey, false);
                  });
                },
                child: const Text('إلغاء الكل'),
              ),
            ],
          ),
        ),
        Wrap(
          spacing: 8,
          children: [
            // صلاحية العرض
            _buildPermissionChip(
              'عرض',
              _permissionsUI[permissionKey]?['view'] ?? false,
              (value) {
                setState(() {
                  _permissionsUI[permissionKey]?['view'] = value;
                  _updatePermissionCodes();
                });
              },
            ),
            // صلاحية الإضافة
            _buildPermissionChip(
              'إضافة',
              _permissionsUI[permissionKey]?['add'] ?? false,
              (value) {
                setState(() {
                  _permissionsUI[permissionKey]?['add'] = value;
                  _updatePermissionCodes();
                });
              },
            ),
            // صلاحية التعديل
            _buildPermissionChip(
              'تعديل',
              _permissionsUI[permissionKey]?['edit'] ?? false,
              (value) {
                setState(() {
                  _permissionsUI[permissionKey]?['edit'] = value;
                  _updatePermissionCodes();
                });
              },
            ),
            // صلاحية الحذف (إذا كانت متاحة)
            if (hasDelete)
              _buildPermissionChip(
                'حذف',
                _permissionsUI[permissionKey]?['delete'] ?? false,
                (value) {
                  setState(() {
                    _permissionsUI[permissionKey]?['delete'] = value;
                    _updatePermissionCodes();
                  });
                },
              ),
            // صلاحية التصدير (للتقارير فقط)
            if (permissionKey == 'reports')
              _buildPermissionChip(
                'تصدير',
                _permissionsUI[permissionKey]?['export'] ?? false,
                (value) {
                  setState(() {
                    _permissionsUI[permissionKey]?['export'] = value;
                    _updatePermissionCodes();
                  });
                },
              ),
          ],
        ),
      ],
    );
  }

  /// بناء رقاقة الصلاحية
  Widget _buildPermissionChip(
      String label, bool value, void Function(bool) onChanged) {
    return FilterChip(
      label: Text(label),
      selected: value,
      onSelected: onChanged,
      selectedColor: AppColors.primary.withValues(alpha: 0.2),
      checkmarkColor: AppColors.primary,
    );
  }

  /// تعيين جميع الصلاحيات
  void _setAllPermissions(bool value) {
    _permissionsUI.forEach((key, module) {
      if (key != 'full_access' && module is Map<String, dynamic>) {
        _setModulePermissions(key, value);
      }
    });

    // تحديث قائمة رموز الصلاحيات
    _updatePermissionCodes();
  }

  /// تعيين صلاحيات وحدة معينة
  void _setModulePermissions(String moduleKey, bool value) {
    if (_permissionsUI[moduleKey] is Map<String, dynamic>) {
      final module = _permissionsUI[moduleKey] as Map<String, dynamic>;
      module.forEach((permKey, _) {
        module[permKey] = value;
      });
      _permissionsUI[moduleKey] = module;

      // تحديث قائمة رموز الصلاحيات
      _updatePermissionCodes();
    }
  }

  /// تحديث قائمة رموز الصلاحيات من واجهة المستخدم
  void _updatePermissionCodes() {
    _permissionCodes = [];

    // إضافة الوصول الكامل إذا كان مفعلاً
    if (_permissionsUI['full_access'] == true) {
      _permissionCodes.add('full_access');
    }

    // إضافة باقي الصلاحيات
    _permissionsUI.forEach((moduleKey, moduleValue) {
      if (moduleKey != 'full_access' && moduleValue is Map<String, dynamic>) {
        moduleValue.forEach((actionKey, actionValue) {
          if (actionValue == true) {
            _permissionCodes.add('$moduleKey.$actionKey');
          }
        });
      }
    });
  }

  /// حفظ المجموعة
  Future<void> _saveGroup() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // تحديث قائمة رموز الصلاحيات قبل الحفظ
    _updatePermissionCodes();

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء كائن المجموعة
      final group = UserGroup(
        id: widget.group?.id,
        name: _nameController.text,
        description: _descriptionController.text,
        permissionCodes: _permissionCodes,
        isActive: _isActive,
      );

      bool success;
      if (_isNewGroup) {
        success = await _userPresenter.addGroup(group);
      } else {
        success = await _userPresenter.updateGroup(group);
      }

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(_isNewGroup
                  ? 'تم إضافة المجموعة بنجاح'
                  : 'تم تحديث المجموعة بنجاح')),
        );
        Navigator.pop(context);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(_isNewGroup
                  ? 'فشل في إضافة المجموعة'
                  : 'فشل في تحديث المجموعة')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
