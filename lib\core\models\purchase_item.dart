import 'dart:convert';
import 'package:uuid/uuid.dart';

/// نموذج عنصر المشتريات
class PurchaseItem {
  final String id;
  final String? purchaseId;
  final String productId;
  final String? productName;
  final String? productCode;
  final String? unitId;
  final String? unitName;
  final double quantity;
  final double price;
  final double? cost;
  final double discount;
  final bool isDiscountPercentage;
  final double taxRate;
  final double subtotal;
  final double discountAmount;
  final double taxAmount;
  final double total;
  final DateTime? createdAt;
  final String? createdBy;
  final DateTime? updatedAt;
  final String? updatedBy;
  final bool isDeleted;

  PurchaseItem({
    String? id,
    this.purchaseId,
    required this.productId,
    this.productName,
    this.productCode,
    this.unitId,
    this.unitName,
    required this.quantity,
    required this.price,
    this.cost,
    this.discount = 0.0,
    this.isDiscountPercentage = false,
    this.taxRate = 0.0,
    double? subtotal,
    double? discountAmount,
    double? taxAmount,
    double? total,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.isDeleted = false,
  })  : id = id ?? const Uuid().v4(),
        subtotal = subtotal ?? (price * quantity),
        discountAmount = discountAmount ??
            (isDiscountPercentage
                ? (price * quantity) * (discount / 100)
                : discount),
        taxAmount = taxAmount ??
            ((price * quantity) -
                    (isDiscountPercentage
                        ? (price * quantity) * (discount / 100)
                        : discount)) *
                (taxRate / 100),
        total = total ??
            ((price * quantity) -
                (isDiscountPercentage
                    ? (price * quantity) * (discount / 100)
                    : discount) +
                ((price * quantity) -
                        (isDiscountPercentage
                            ? (price * quantity) * (discount / 100)
                            : discount)) *
                    (taxRate / 100));

  /// إنشاء نسخة من هذا العنصر مع استبدال الحقول المحددة بقيم جديدة
  PurchaseItem copyWith({
    String? id,
    String? purchaseId,
    String? productId,
    String? productName,
    String? productCode,
    String? unitId,
    String? unitName,
    double? quantity,
    double? price,
    double? cost,
    double? discount,
    bool? isDiscountPercentage,
    double? taxRate,
    double? subtotal,
    double? discountAmount,
    double? taxAmount,
    double? total,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return PurchaseItem(
      id: id ?? this.id,
      purchaseId: purchaseId ?? this.purchaseId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      unitId: unitId ?? this.unitId,
      unitName: unitName ?? this.unitName,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      cost: cost ?? this.cost,
      discount: discount ?? this.discount,
      isDiscountPercentage: isDiscountPercentage ?? this.isDiscountPercentage,
      taxRate: taxRate ?? this.taxRate,
      subtotal: subtotal ?? this.subtotal,
      discountAmount: discountAmount ?? this.discountAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      total: total ?? this.total,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل عنصر المشتريات إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'purchase_id': purchaseId,
      'product_id': productId,
      'product_name': productName,
      'product_code': productCode,
      'unit_id': unitId,
      'unit_name': unitName,
      'quantity': quantity,
      'price': price,
      'cost': cost,
      'discount': discount,
      'is_discount_percentage': isDiscountPercentage ? 1 : 0,
      'tax_rate': taxRate,
      'subtotal': subtotal,
      'discount_amount': discountAmount,
      'tax_amount': taxAmount,
      'total': total,
      'created_at': createdAt?.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء عنصر مشتريات من Map
  factory PurchaseItem.fromMap(Map<String, dynamic> map) {
    return PurchaseItem(
      id: map['id'],
      purchaseId: map['purchase_id'],
      productId: map['product_id'] ?? '',
      productName: map['product_name'],
      productCode: map['product_code'],
      unitId: map['unit_id'],
      unitName: map['unit_name'],
      quantity: map['quantity'] is int
          ? (map['quantity'] as int).toDouble()
          : (map['quantity'] as double? ?? 0.0),
      price: map['price'] is int
          ? (map['price'] as int).toDouble()
          : (map['price'] as double? ?? 0.0),
      cost: map['cost'] is int
          ? (map['cost'] as int).toDouble()
          : (map['cost'] as double?),
      discount: map['discount'] is int
          ? (map['discount'] as int).toDouble()
          : (map['discount'] as double? ?? 0.0),
      isDiscountPercentage: map['is_discount_percentage'] == 1,
      taxRate: map['tax_rate'] is int
          ? (map['tax_rate'] as int).toDouble()
          : (map['tax_rate'] as double? ?? 0.0),
      subtotal: map['subtotal'] is int
          ? (map['subtotal'] as int).toDouble()
          : (map['subtotal'] as double? ?? 0.0),
      discountAmount: map['discount_amount'] is int
          ? (map['discount_amount'] as int).toDouble()
          : (map['discount_amount'] as double? ?? 0.0),
      taxAmount: map['tax_amount'] is int
          ? (map['tax_amount'] as int).toDouble()
          : (map['tax_amount'] as double? ?? 0.0),
      total: map['total'] is int
          ? (map['total'] as int).toDouble()
          : (map['total'] as double? ?? 0.0),
      createdAt:
          map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل عنصر المشتريات إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء عنصر مشتريات من JSON
  factory PurchaseItem.fromJson(String source) =>
      PurchaseItem.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'PurchaseItem(id: $id, productId: $productId, productName: $productName, quantity: $quantity, price: $price, total: $total)';
  }
}
