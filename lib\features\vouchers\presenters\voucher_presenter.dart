import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../features/accounts/models/voucher.dart';

/// مقدم السندات
class VoucherPresenter extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final List<Voucher> _vouchers = [];
  bool _isLoading = false;
  String _errorMessage = '';

  // Getters
  List<Voucher> get vouchers => _vouchers;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  // تحميل السندات
  Future<void> loadVouchers(
      {String? voucherType, DateTimeRange? dateRange}) async {
    _setLoading(true);
    try {
      final db = await _databaseHelper.database;

      String query = '''
        SELECT v.*, a.name as account_name
        FROM vouchers v
        LEFT JOIN accounts a ON v.account_id = a.id
        WHERE v.is_deleted = 0
      ''';

      List<dynamic> args = [];

      if (voucherType != null) {
        query += ' AND v.voucher_type = ?';
        args.add(voucherType);
      }

      if (dateRange != null) {
        query += ' AND v.date BETWEEN ? AND ?';
        args.add(dateRange.start.toIso8601String());
        args.add(dateRange.end.toIso8601String());
      }

      query += ' ORDER BY v.date DESC';

      final List<Map<String, dynamic>> maps = await db.rawQuery(query, args);

      _vouchers.clear();
      for (var map in maps) {
        _vouchers.add(Voucher.fromMap(map));
      }

      _setLoading(false);
    } catch (e, stackTrace) {
      _handleError('فشل في تحميل السندات', e, stackTrace);
    }
  }

  // إضافة سند جديد
  Future<bool> addVoucher(Voucher voucher) async {
    try {
      final db = await _databaseHelper.database;

      // إنشاء معرف جديد إذا لم يكن موجودًا
      final String id = voucher.id ?? const Uuid().v4();

      // إنشاء رقم مرجعي للسند إذا لم يكن موجودًا
      String? referenceNumber = voucher.referenceNumber;
      if (referenceNumber == null || referenceNumber.isEmpty) {
        referenceNumber = await _generateReferenceNumber(voucher.voucherType);
      }

      // تحديث السند بالمعرف والرقم المرجعي
      final updatedVoucher = voucher.copyWith(
        id: id,
        referenceNumber: referenceNumber,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ السند في قاعدة البيانات
      await db.insert('vouchers', updatedVoucher.toMap());

      // تحديث رصيد الحساب
      await _updateAccountBalance(updatedVoucher);

      // إضافة السند إلى القائمة
      _vouchers.add(updatedVoucher);
      notifyListeners();

      return true;
    } catch (e, stackTrace) {
      _handleError('فشل في إضافة السند', e, stackTrace);
      return false;
    }
  }

  // تعديل سند
  Future<bool> updateVoucher(Voucher voucher) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على السند القديم
      final oldVoucherIndex = _vouchers.indexWhere((v) => v.id == voucher.id);
      if (oldVoucherIndex == -1) {
        throw Exception('السند غير موجود');
      }

      final oldVoucher = _vouchers[oldVoucherIndex];

      // تحديث السند بتاريخ التحديث
      final updatedVoucher = voucher.copyWith(
        updatedAt: DateTime.now(),
      );

      // تحديث السند في قاعدة البيانات
      await db.update(
        'vouchers',
        updatedVoucher.toMap(),
        where: 'id = ?',
        whereArgs: [updatedVoucher.id],
      );

      // عكس تأثير السند القديم على رصيد الحساب
      await _reverseAccountBalance(oldVoucher);

      // تحديث رصيد الحساب بالسند الجديد
      await _updateAccountBalance(updatedVoucher);

      // تحديث السند في القائمة
      _vouchers[oldVoucherIndex] = updatedVoucher;
      notifyListeners();

      return true;
    } catch (e, stackTrace) {
      _handleError('فشل في تعديل السند', e, stackTrace);
      return false;
    }
  }

  // حذف سند
  Future<bool> deleteVoucher(String id) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على السند
      final voucherIndex = _vouchers.indexWhere((v) => v.id == id);
      if (voucherIndex == -1) {
        throw Exception('السند غير موجود');
      }

      final voucher = _vouchers[voucherIndex];

      // حذف السند من قاعدة البيانات (حذف منطقي)
      await db.update(
        'vouchers',
        {'is_deleted': 1, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [id],
      );

      // عكس تأثير السند على رصيد الحساب
      await _reverseAccountBalance(voucher);

      // حذف السند من القائمة
      _vouchers.removeAt(voucherIndex);
      notifyListeners();

      return true;
    } catch (e, stackTrace) {
      _handleError('فشل في حذف السند', e, stackTrace);
      return false;
    }
  }

  // البحث عن سندات
  Future<List<Voucher>> searchVouchers(String query,
      {String? voucherType}) async {
    try {
      if (query.isEmpty) {
        return _vouchers
            .where((v) => voucherType == null || v.voucherType == voucherType)
            .toList();
      }

      final db = await _databaseHelper.database;

      String sqlQuery = '''
        SELECT v.*, a.name as account_name
        FROM vouchers v
        LEFT JOIN accounts a ON v.account_id = a.id
        WHERE v.is_deleted = 0
        AND (
          v.reference_number LIKE ? OR
          a.name LIKE ? OR
          v.handler LIKE ? OR
          v.notes LIKE ?
        )
      ''';

      List<dynamic> args = [
        '%$query%',
        '%$query%',
        '%$query%',
        '%$query%',
      ];

      if (voucherType != null) {
        sqlQuery += ' AND v.voucher_type = ?';
        args.add(voucherType);
      }

      sqlQuery += ' ORDER BY v.date DESC';

      final List<Map<String, dynamic>> maps = await db.rawQuery(sqlQuery, args);

      return maps.map((map) => Voucher.fromMap(map)).toList();
    } catch (e, stackTrace) {
      _handleError('فشل في البحث عن السندات', e, stackTrace);
      return [];
    }
  }

  // توليد رقم مرجعي للسند
  Future<String> _generateReferenceNumber(String voucherType) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على آخر رقم للسند من نفس النوع
      final result = await db.rawQuery('''
        SELECT MAX(CAST(SUBSTR(reference_number, INSTR(reference_number, '-') + 1) AS INTEGER)) as max_number
        FROM vouchers
        WHERE voucher_type = ?
      ''', [voucherType]);

      final maxNumber = result.first['max_number'] as int? ?? 0;
      final nextNumber = maxNumber + 1;

      // تحديد بادئة السند حسب نوعه
      String prefix;
      switch (voucherType) {
        case 'receipt':
          prefix = 'RV';
          break;
        case 'payment':
          prefix = 'PV';
          break;
        case 'journal':
          prefix = 'JV';
          break;
        default:
          prefix = 'V';
      }

      // إنشاء الرقم المرجعي بتنسيق PREFIX-NUMBER
      return '$prefix-$nextNumber';
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في توليد رقم مرجعي للسند',
        error: e,
        stackTrace: stackTrace,
        context: {'voucherType': voucherType},
      );

      // في حالة الفشل، استخدم الطابع الزمني كرقم مرجعي
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return 'V-$timestamp';
    }
  }

  // تحديث رصيد الحساب
  Future<void> _updateAccountBalance(Voucher voucher) async {
    try {
      final db = await _databaseHelper.database;

      // تحديد قيمة التغيير في الرصيد حسب نوع السند
      double balanceChange;
      switch (voucher.voucherType) {
        case 'receipt':
          balanceChange = voucher.localAmount; // زيادة الرصيد في حالة سند القبض
          break;
        case 'payment':
          balanceChange = -voucher.localAmount; // نقص الرصيد في حالة سند الصرف
          break;
        case 'journal':
          // في حالة القيد البسيط، لا يتم تغيير الرصيد هنا
          // يجب معالجة القيود المحاسبية بشكل منفصل
          return;
        default:
          return;
      }

      // تحديث رصيد الحساب
      await db.rawUpdate('''
        UPDATE accounts
        SET balance = balance + ?,
            updated_at = ?
        WHERE id = ?
      ''',
          [balanceChange, DateTime.now().toIso8601String(), voucher.accountId]);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث رصيد الحساب',
        error: e,
        stackTrace: stackTrace,
        context: {'voucherId': voucher.id, 'accountId': voucher.accountId},
      );
    }
  }

  // عكس تأثير السند على رصيد الحساب
  Future<void> _reverseAccountBalance(Voucher voucher) async {
    try {
      final db = await _databaseHelper.database;

      // تحديد قيمة التغيير في الرصيد حسب نوع السند
      double balanceChange;
      switch (voucher.voucherType) {
        case 'receipt':
          balanceChange =
              -voucher.localAmount; // نقص الرصيد لعكس تأثير سند القبض
          break;
        case 'payment':
          balanceChange =
              voucher.localAmount; // زيادة الرصيد لعكس تأثير سند الصرف
          break;
        case 'journal':
          // في حالة القيد البسيط، لا يتم تغيير الرصيد هنا
          return;
        default:
          return;
      }

      // تحديث رصيد الحساب
      await db.rawUpdate('''
        UPDATE accounts
        SET balance = balance + ?,
            updated_at = ?
        WHERE id = ?
      ''',
          [balanceChange, DateTime.now().toIso8601String(), voucher.accountId]);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في عكس تأثير السند على رصيد الحساب',
        error: e,
        stackTrace: stackTrace,
        context: {'voucherId': voucher.id, 'accountId': voucher.accountId},
      );
    }
  }

  // تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // معالجة الأخطاء
  void _handleError(String message, dynamic error, StackTrace stackTrace) {
    _errorMessage = '$message: ${error.toString()}';
    _setLoading(false);

    AppLogger.error(message, error: error, stackTrace: stackTrace);
    ErrorTracker.captureError(
      message,
      error: error,
      stackTrace: stackTrace,
      context: {'presenter': 'VoucherPresenter'},
    );
  }
}
