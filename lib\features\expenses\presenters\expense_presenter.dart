import 'package:flutter/material.dart';
import '../../../core/models/expense.dart';
import '../../../core/models/category.dart';
import '../../../core/services/expense_service.dart';
import '../../../core/services/category_service.dart';
import '../../../core/utils/error_tracker.dart';

/// مقدم المصروفات
/// يتعامل مع منطق إدارة المصروفات وفئاتها
class ExpensePresenter extends ChangeNotifier {
  final ExpenseService _expenseService = ExpenseService();
  final CategoryService _categoryService = CategoryService();

  List<Expense> _expenses = [];
  List<Category> _categories = [];
  bool _isLoading = false;
  String? _errorMessage;

  /// الحصول على قائمة المصروفات
  List<Expense> get expenses => _expenses;

  /// الحصول على قائمة فئات المصروفات
  List<Category> get categories => _categories;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// تحميل المصروفات
  Future<void> loadExpenses({
    bool includeDeleted = false,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? categoryId,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final expenses = await _expenseService.getAllExpenses(
        includeDeleted: includeDeleted,
        searchQuery: searchQuery,
        startDate: startDate,
        endDate: endDate,
        categoryId: categoryId,
      );

      _expenses = expenses;
      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المصروفات: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل المصروفات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
    }
  }

  /// تحميل فئات المصروفات
  Future<void> loadExpenseCategories({
    bool includeInactive = false,
    String? searchQuery,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // استخدام خدمة الفئات الموحدة مباشرة
      final categories = await _categoryService.getAllCategories(
        includeInactive: includeInactive,
        searchQuery: searchQuery,
        type: 'expense',
      );

      _categories = categories;
      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل فئات المصروفات: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل فئات المصروفات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
    }
  }

  /// إضافة مصروف جديد
  Future<bool> addExpense(Expense expense, {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _expenseService.addExpense(expense, userId: userId);

      if (success) {
        await loadExpenses();
      } else {
        _errorMessage = 'فشل في إضافة المصروف';
        _isLoading = false;
        notifyListeners();
      }

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء إضافة المصروف: $e';
      ErrorTracker.captureError(
        'خطأ في إضافة المصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'expense': expense.toString()},
      );
      notifyListeners();
      return false;
    }
  }

  /// تحديث مصروف
  Future<bool> updateExpense(Expense expense, {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success =
          await _expenseService.updateExpense(expense, userId: userId);

      if (success) {
        await loadExpenses();
      } else {
        _errorMessage = 'فشل في تحديث المصروف';
        _isLoading = false;
        notifyListeners();
      }

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحديث المصروف: $e';
      ErrorTracker.captureError(
        'خطأ في تحديث المصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'expense': expense.toString()},
      );
      notifyListeners();
      return false;
    }
  }

  /// حذف مصروف
  Future<bool> deleteExpense(String id, {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _expenseService.deleteExpense(id, userId: userId);

      if (success) {
        _expenses.removeWhere((expense) => expense.id == id);
        _isLoading = false;
        notifyListeners();
      } else {
        _errorMessage = 'فشل في حذف المصروف';
        _isLoading = false;
        notifyListeners();
      }

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء حذف المصروف: $e';
      ErrorTracker.captureError(
        'خطأ في حذف المصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      notifyListeners();
      return false;
    }
  }

  /// إضافة فئة مصروف جديدة
  Future<bool> addExpenseCategory(Category category, {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // التأكد من أن نوع الفئة هو مصروفات
      final categoryToAdd = category.copyWith(type: 'expense');

      // استخدام خدمة الفئات الموحدة مباشرة
      final success = await _categoryService.addCategory(categoryToAdd, userId: userId);

      if (success) {
        await loadExpenseCategories();
      } else {
        _errorMessage = 'فشل في إضافة فئة المصروف';
        _isLoading = false;
        notifyListeners();
      }

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء إضافة فئة المصروف: $e';
      ErrorTracker.captureError(
        'خطأ في إضافة فئة المصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'category': category.toString()},
      );
      notifyListeners();
      return false;
    }
  }

  /// تحديث فئة مصروف
  Future<bool> updateExpenseCategory(Category category, {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // التأكد من أن نوع الفئة هو مصروفات
      final categoryToUpdate = category.copyWith(type: 'expense');

      // استخدام خدمة الفئات الموحدة مباشرة
      final success = await _categoryService.updateCategory(categoryToUpdate, userId: userId);

      if (success) {
        await loadExpenseCategories();
      } else {
        _errorMessage = 'فشل في تحديث فئة المصروف';
        _isLoading = false;
        notifyListeners();
      }

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحديث فئة المصروف: $e';
      ErrorTracker.captureError(
        'خطأ في تحديث فئة المصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'category': category.toString()},
      );
      notifyListeners();
      return false;
    }
  }

  /// حذف فئة مصروف
  Future<bool> deleteExpenseCategory(String id, {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // استخدام خدمة الفئات الموحدة مباشرة
      final success = await _categoryService.deleteCategory(id, userId: userId, type: 'expense');

      if (success) {
        _categories.removeWhere((category) => category.id == id);
        _isLoading = false;
        notifyListeners();
      } else {
        _errorMessage = 'فشل في حذف فئة المصروف';
        _isLoading = false;
        notifyListeners();
      }

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء حذف فئة المصروف: $e';
      ErrorTracker.captureError(
        'خطأ في حذف فئة المصروف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      notifyListeners();
      return false;
    }
  }
}
