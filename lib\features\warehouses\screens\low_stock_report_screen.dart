import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:fl_chart/fl_chart.dart';

import '../presenters/inventory_presenter.dart';

import '../../../core/theme/index.dart';

/// شاشة تقرير المنتجات منخفضة المخزون
///
/// تعرض هذه الشاشة تقرير المنتجات منخفضة المخزون مع رسوم بيانية لتوضيح حالة المخزون
class LowStockReportScreen extends StatefulWidget {
  const LowStockReportScreen({Key? key}) : super(key: key);

  @override
  State<LowStockReportScreen> createState() => _LowStockReportScreenState();
}

class _LowStockReportScreenState extends State<LowStockReportScreen> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _lowStockItems = [];
  int _touchedIndex = -1;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final presenter = AppProviders.getLazyPresenter<InventoryPresenter>(
          () => InventoryPresenter());

      // تحميل المنتجات منخفضة المخزون
      _lowStockItems = await presenter.loadLowStockProducts();
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير المنتجات منخفضة المخزون'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildReportContent(),
    );
  }

  Widget _buildReportContent() {
    if (_lowStockItems.isEmpty) {
      return const Center(
        child: Text('لا توجد منتجات منخفضة المخزون'),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCard(),
            const SizedBox(height: 20),
            _buildLowStockChart(),
            const SizedBox(height: 20),
            _buildLowStockTable(),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard() {
    // حساب إجمالي المنتجات منخفضة المخزون
    final totalLowStock = _lowStockItems.length;

    // حساب عدد المنتجات النافدة
    final outOfStock = _lowStockItems
        .where((item) => (item['quantity'] as double? ?? 0) <= 0)
        .length;

    // حساب عدد المنتجات التي تحتاج إلى طلب
    final needOrder = _lowStockItems.where((item) {
      final quantity = item['quantity'] as double? ?? 0;
      final minStock = item['min_stock'] as double? ?? 0;
      return quantity > 0 && quantity <= minStock;
    }).length;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص المنتجات منخفضة المخزون',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: 'إجمالي المنتجات',
                    value: totalLowStock.toString(),
                    icon: Icons.inventory,
                    color: AppColors.lightTextSecondary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: 'نافدة',
                    value: outOfStock.toString(),
                    icon: Icons.remove_shopping_cart,
                    color: AppColors.lightTextSecondary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: 'تحتاج طلب',
                    value: needOrder.toString(),
                    icon: Icons.shopping_cart,
                    color: AppColors.lightTextSecondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          size: 32,
          color: color,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const AppTypography(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTypography(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLowStockChart() {
    // تجميع البيانات للرسم البياني
    final Map<String, int> statusData = {
      'نافد': 0,
      'منخفض جداً': 0,
      'منخفض': 0,
    };

    for (var item in _lowStockItems) {
      final quantity = item['quantity'] as double? ?? 0;
      final minStock = item['min_stock'] as double? ?? 0;

      if (quantity <= 0) {
        statusData['نافد'] = statusData['نافد']! + 1;
      } else if (quantity <= minStock * 0.5) {
        statusData['منخفض جداً'] = statusData['منخفض جداً']! + 1;
      } else {
        statusData['منخفض'] = statusData['منخفض']! + 1;
      }
    }

    // تحويل البيانات إلى قطاعات الرسم البياني
    final List<PieChartSectionData> sections = [];
    final List<Color> colors = [
      AppColors.error,
      AppColors.warning,
      AppColors.amber,
    ];

    int colorIndex = 0;
    statusData.forEach((status, count) {
      if (count > 0) {
        final isTouched = sections.length == _touchedIndex;
        final fontSize = isTouched ? 20.0 : 16.0;
        final radius = isTouched ? 110.0 : 100.0;

        sections.add(
          PieChartSectionData(
            color: colors[colorIndex % colors.length],
            value: count.toDouble(),
            title: count.toString(),
            radius: radius,
            titleStyle: AppTypography(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: AppColors.lightTextSecondary,
            ),
          ),
        );
      }

      colorIndex++;
    });

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع المنتجات منخفضة المخزون',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: sections.isEmpty
                  ? const Center(child: Text('لا توجد بيانات للعرض'))
                  : Row(
                      children: [
                        Expanded(
                          child: PieChart(
                            PieChartData(
                              pieTouchData: PieTouchData(
                                touchCallback:
                                    (FlTouchEvent event, pieTouchResponse) {
                                  setState(() {
                                    if (!event.isInterestedForInteractions ||
                                        pieTouchResponse == null ||
                                        pieTouchResponse.touchedSection ==
                                            null) {
                                      _touchedIndex = -1;
                                      return;
                                    }
                                    _touchedIndex = pieTouchResponse
                                        .touchedSection!.touchedSectionIndex;
                                  });
                                },
                              ),
                              borderData: FlBorderData(show: false),
                              sectionsSpace: 2,
                              centerSpaceRadius: 0,
                              sections: sections,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildLegendItem(
                                  'نافد', colors[0], statusData['نافد'] ?? 0),
                              _buildLegendItem('منخفض جداً', colors[1],
                                  statusData['منخفض جداً'] ?? 0),
                              _buildLegendItem(
                                  'منخفض', colors[2], statusData['منخفض'] ?? 0),
                            ],
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color, int count) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            color: color,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '$label ($count)',
              style: const AppTypography(fontSize: 12),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLowStockTable() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'قائمة المنتجات منخفضة المخزون',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('المنتج')),
                  DataColumn(label: Text('الباركود')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('الحد الأدنى')),
                  DataColumn(label: Text('النسبة')),
                  DataColumn(label: Text('الحالة')),
                ],
                rows: _lowStockItems.map((item) {
                  final productName =
                      item['product_name'] as String? ?? 'منتج غير معروف';
                  final barcode = item['barcode'] as String? ?? '-';
                  final quantity = item['quantity'] as double? ?? 0.0;
                  final minStock = item['min_stock'] as double? ?? 0.0;
                  final percentage =
                      minStock > 0 ? (quantity / minStock * 100).toInt() : 0;

                  String status;
                  Color statusColor;

                  if (quantity <= 0) {
                    status = 'نافد';
                    statusColor = AppColors.error;
                  } else if (quantity <= minStock * 0.5) {
                    status = 'منخفض جداً';
                    statusColor = AppColors.warning;
                  } else {
                    status = 'منخفض';
                    statusColor = AppColors.amber;
                  }

                  return DataRow(
                    cells: [
                      DataCell(Text(productName)),
                      DataCell(Text(barcode)),
                      DataCell(Text(quantity.toString())),
                      DataCell(Text(minStock.toString())),
                      DataCell(Text('$percentage%')),
                      DataCell(
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: statusColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            status,
                            style:
                                const AppTypography(color: AppColors.onPrimary),
                          ),
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
