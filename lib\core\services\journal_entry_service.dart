import 'package:sqflite/sqflite.dart';
import '../database/database_service.dart';
import '../models/journal_entry.dart';
import '../models/journal_entry_detail.dart';
import '../models/account.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';

/// خدمة للعمليات المتعلقة بالقيود المحاسبية
class JournalEntryService {
  // نمط Singleton
  static final JournalEntryService _instance = JournalEntryService._internal();
  factory JournalEntryService() => _instance;
  JournalEntryService._internal();

  final DatabaseService _db = DatabaseService.instance;

  /// الحصول على جميع القيود المحاسبية
  Future<List<JournalEntry>> getAllJournalEntries({
    JournalEntryType? type,
    JournalEntryStatus? status,
    DateTime? fromDate,
    DateTime? toDate,
    String? referenceId,
    String? referenceType,
    bool includeDeleted = false,
    int? limit,
    int? offset,
  }) async {
    try {
      AppLogger.info('الحصول على جميع القيود المحاسبية');

      // بناء شرط WHERE
      String whereClause = includeDeleted ? '1=1' : 'j.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (type != null) {
        whereClause += ' AND j.type = ?';
        whereArgs.add(type.toString().split('.').last);
      }

      if (status != null) {
        whereClause += ' AND j.status = ?';
        whereArgs.add(status.toString().split('.').last);
      }

      if (fromDate != null) {
        whereClause += ' AND j.entry_date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND j.entry_date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      if (referenceId != null) {
        whereClause += ' AND j.reference_id = ?';
        whereArgs.add(referenceId);
      }

      if (referenceType != null) {
        whereClause += ' AND j.reference_type = ?';
        whereArgs.add(referenceType);
      }

      // استعلام قاعدة البيانات
      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT j.*
        FROM ${DatabaseService.tableJournalEntries} j
        WHERE $whereClause
        ORDER BY j.entry_date DESC, j.entry_number DESC
        ${limit != null ? 'LIMIT $limit' : ''}
        ${offset != null ? 'OFFSET $offset' : ''}
      ''', whereArgs);

      // تحويل إلى كائنات JournalEntry
      final List<JournalEntry> entries = [];
      for (final map in maps) {
        // الحصول على تفاصيل القيد
        final details = await getJournalEntryDetails(map['id']);

        // إنشاء كائن القيد مع التفاصيل
        final entry = JournalEntry.fromMap(map, details);
        entries.add(entry);
      }

      return entries;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع القيود المحاسبية',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على قيد محاسبي بواسطة المعرف
  Future<JournalEntry?> getJournalEntryById(String id) async {
    try {
      AppLogger.info('الحصول على قيد محاسبي بواسطة المعرف: $id');

      final List<Map<String, dynamic>> maps = await _db.query(
        DatabaseService.tableJournalEntries,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isEmpty) {
        return null;
      }

      // الحصول على تفاصيل القيد
      final details = await getJournalEntryDetails(id);

      // إنشاء كائن القيد مع التفاصيل
      return JournalEntry.fromMap(maps.first, details);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على قيد محاسبي بواسطة المعرف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return null;
    }
  }

  /// الحصول على قيد محاسبي بواسطة رقم القيد
  Future<JournalEntry?> getJournalEntryByNumber(String entryNumber) async {
    try {
      AppLogger.info('الحصول على قيد محاسبي بواسطة رقم القيد: $entryNumber');

      final List<Map<String, dynamic>> maps = await _db.query(
        DatabaseService.tableJournalEntries,
        where: 'entry_number = ? AND is_deleted = 0',
        whereArgs: [entryNumber],
      );

      if (maps.isEmpty) {
        return null;
      }

      // الحصول على تفاصيل القيد
      final details = await getJournalEntryDetails(maps.first['id']);

      // إنشاء كائن القيد مع التفاصيل
      return JournalEntry.fromMap(maps.first, details);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على قيد محاسبي بواسطة رقم القيد',
        error: e,
        stackTrace: stackTrace,
        context: {'entryNumber': entryNumber},
      );
      return null;
    }
  }

  /// الحصول على تفاصيل القيد المحاسبي
  Future<List<JournalEntryDetail>> getJournalEntryDetails(
      String journalEntryId) async {
    try {
      AppLogger.info('الحصول على تفاصيل القيد المحاسبي: $journalEntryId');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          d.*,
          a.name as account_name,
          a.code as account_code,
          a.type as account_type
        FROM ${DatabaseService.tableJournalEntryDetails} d
        JOIN ${DatabaseService.tableAccounts} a ON d.account_id = a.id
        WHERE d.journal_entry_id = ? AND d.is_deleted = 0
        ORDER BY d.id
      ''', [journalEntryId]);

      // تحويل إلى كائنات JournalEntryDetail
      return List.generate(maps.length, (i) {
        final map = maps[i];

        // إضافة معلومات الحساب إلى الخريطة
        if (map['account_name'] != null) {
          map['account'] = {
            'id': map['account_id'],
            'name': map['account_name'],
            'code': map['account_code'],
            'type': map['account_type'],
          };
        }

        return JournalEntryDetail.fromMap(map);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على تفاصيل القيد المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'journalEntryId': journalEntryId},
      );
      return [];
    }
  }

  /// إضافة قيد محاسبي جديد
  Future<bool> addJournalEntry(JournalEntry entry, {String? userId}) async {
    try {
      AppLogger.info('إضافة قيد محاسبي جديد: ${entry.entryNumber}');

      // التحقق من عدم وجود قيد بنفس الرقم
      final existingEntry = await getJournalEntryByNumber(entry.entryNumber);
      if (existingEntry != null) {
        AppLogger.warning('رقم القيد موجود بالفعل: ${entry.entryNumber}');
        return false;
      }

      // التحقق من توازن القيد
      if (!entry.isBalanced) {
        AppLogger.warning(
            'القيد غير متوازن: ${entry.totalDebit} != ${entry.totalCredit}');
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        // إضافة القيد
        final entryMap = entry.toMap();

        // تعيين created_by إذا تم توفيره
        if (userId != null) {
          entryMap['created_by'] = userId;
        }

        await txn.insert(DatabaseService.tableJournalEntries, entryMap);

        // إضافة تفاصيل القيد
        for (final detail in entry.details) {
          final detailMap = detail.toMap();
          await txn.insert(DatabaseService.tableJournalEntryDetails, detailMap);
        }

        // إذا كان القيد مرحلًا، نقوم بتحديث أرصدة الحسابات
        if (entry.status == JournalEntryStatus.posted) {
          await _updateAccountBalances(txn, entry);
        }

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة قيد محاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'entry': entry.toString()},
      );
      return false;
    }
  }

  /// تحديث قيد محاسبي موجود
  Future<bool> updateJournalEntry(JournalEntry entry, {String? userId}) async {
    try {
      AppLogger.info('تحديث قيد محاسبي: ${entry.entryNumber}');

      // التحقق من عدم وجود قيد آخر بنفس الرقم
      final existingEntry = await getJournalEntryByNumber(entry.entryNumber);
      if (existingEntry != null && existingEntry.id != entry.id) {
        AppLogger.warning('رقم القيد موجود بالفعل: ${entry.entryNumber}');
        return false;
      }

      // التحقق من توازن القيد
      if (!entry.isBalanced) {
        AppLogger.warning(
            'القيد غير متوازن: ${entry.totalDebit} != ${entry.totalCredit}');
        return false;
      }

      // الحصول على القيد الحالي
      final currentEntry = await getJournalEntryById(entry.id);
      if (currentEntry == null) {
        AppLogger.warning('القيد غير موجود: ${entry.id}');
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        // تحديث القيد
        final entryMap = entry.toMap();

        // تعيين updated_at و updated_by
        entryMap['updated_at'] = DateTime.now().toIso8601String();
        if (userId != null) {
          entryMap['updated_by'] = userId;
        }

        await txn.update(
          DatabaseService.tableJournalEntries,
          entryMap,
          where: 'id = ?',
          whereArgs: [entry.id],
        );

        // حذف تفاصيل القيد القديمة
        await txn.delete(
          DatabaseService.tableJournalEntryDetails,
          where: 'journal_entry_id = ?',
          whereArgs: [entry.id],
        );

        // إضافة تفاصيل القيد الجديدة
        for (final detail in entry.details) {
          final detailMap = detail.toMap();
          await txn.insert(DatabaseService.tableJournalEntryDetails, detailMap);
        }

        // إذا تغيرت حالة القيد أو تفاصيله، نقوم بتحديث أرصدة الحسابات
        if (currentEntry.status != entry.status ||
            _hasDetailsChanged(currentEntry.details, entry.details)) {
          // إذا كان القيد مرحلًا سابقًا، نقوم بعكس تأثيره
          if (currentEntry.status == JournalEntryStatus.posted) {
            await _reverseAccountBalances(txn, currentEntry);
          }

          // إذا أصبح القيد مرحلًا، نقوم بتطبيق تأثيره
          if (entry.status == JournalEntryStatus.posted) {
            await _updateAccountBalances(txn, entry);
          }
        }

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث قيد محاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'entry': entry.toString()},
      );
      return false;
    }
  }

  /// حذف قيد محاسبي (حذف منطقي)
  Future<bool> deleteJournalEntry(String id, {String? userId}) async {
    try {
      AppLogger.info('حذف قيد محاسبي: $id');

      // الحصول على القيد
      final entry = await getJournalEntryById(id);
      if (entry == null) {
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        final now = DateTime.now().toIso8601String();

        // حذف القيد (حذف منطقي)
        await txn.update(
          DatabaseService.tableJournalEntries,
          {
            'is_deleted': 1,
            'updated_at': now,
            'updated_by': userId,
          },
          where: 'id = ?',
          whereArgs: [id],
        );

        // حذف تفاصيل القيد (حذف منطقي)
        await txn.update(
          DatabaseService.tableJournalEntryDetails,
          {
            'is_deleted': 1,
            'updated_at': now,
          },
          where: 'journal_entry_id = ?',
          whereArgs: [id],
        );

        // إذا كان القيد مرحلًا، نقوم بعكس تأثيره على أرصدة الحسابات
        if (entry.status == JournalEntryStatus.posted) {
          await _reverseAccountBalances(txn, entry);
        }

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف قيد محاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// ترحيل قيد محاسبي
  Future<bool> postJournalEntry(String id, {String? userId}) async {
    try {
      AppLogger.info('ترحيل قيد محاسبي: $id');

      // الحصول على القيد
      final entry = await getJournalEntryById(id);
      if (entry == null) {
        return false;
      }

      // التحقق من أن القيد ليس مرحلًا بالفعل
      if (entry.status == JournalEntryStatus.posted) {
        AppLogger.warning('القيد مرحل بالفعل: $id');
        return true;
      }

      // التحقق من توازن القيد
      if (!entry.isBalanced) {
        AppLogger.warning(
            'القيد غير متوازن: ${entry.totalDebit} != ${entry.totalCredit}');
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        // تحديث حالة القيد
        await txn.update(
          DatabaseService.tableJournalEntries,
          {
            'status': JournalEntryStatus.posted.toString().split('.').last,
            'updated_at': DateTime.now().toIso8601String(),
            'updated_by': userId,
          },
          where: 'id = ?',
          whereArgs: [id],
        );

        // تحديث أرصدة الحسابات
        await _updateAccountBalances(txn, entry);

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في ترحيل قيد محاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// إلغاء ترحيل قيد محاسبي
  Future<bool> unpostJournalEntry(String id, {String? userId}) async {
    try {
      AppLogger.info('إلغاء ترحيل قيد محاسبي: $id');

      // الحصول على القيد
      final entry = await getJournalEntryById(id);
      if (entry == null) {
        return false;
      }

      // التحقق من أن القيد مرحل
      if (entry.status != JournalEntryStatus.posted) {
        AppLogger.warning('القيد غير مرحل: $id');
        return true;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        // تحديث حالة القيد
        await txn.update(
          DatabaseService.tableJournalEntries,
          {
            'status': JournalEntryStatus.draft.toString().split('.').last,
            'updated_at': DateTime.now().toIso8601String(),
            'updated_by': userId,
          },
          where: 'id = ?',
          whereArgs: [id],
        );

        // عكس تأثير القيد على أرصدة الحسابات
        await _reverseAccountBalances(txn, entry);

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إلغاء ترحيل قيد محاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// تحديث أرصدة الحسابات
  Future<void> _updateAccountBalances(
      Transaction txn, JournalEntry entry) async {
    try {
      for (final detail in entry.details) {
        // الحصول على الحساب
        final account = await _getAccount(txn, detail.accountId);
        if (account == null) {
          continue;
        }

        // حساب التغيير في الرصيد بناءً على نوع الحساب
        double balanceChange = 0.0;

        switch (account.type) {
          case AccountType.asset:
          case AccountType.expense:
            balanceChange = detail.debit - detail.credit;
            break;
          case AccountType.liability:
          case AccountType.equity:
          case AccountType.revenue:
          case AccountType.customer:
          case AccountType.supplier:
          case AccountType.cash:
          case AccountType.bank:
          case AccountType.other:
            balanceChange = detail.credit - detail.debit;
            break;
        }

        // تحديث رصيد الحساب
        await txn.update(
          DatabaseService.tableAccounts,
          {
            'balance': account.balance + balanceChange,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [account.id],
        );
      }
    } catch (e) {
      AppLogger.error('فشل في تحديث أرصدة الحسابات: $e');
      rethrow;
    }
  }

  /// عكس تأثير القيد على أرصدة الحسابات
  Future<void> _reverseAccountBalances(
      Transaction txn, JournalEntry entry) async {
    try {
      for (final detail in entry.details) {
        // الحصول على الحساب
        final account = await _getAccount(txn, detail.accountId);
        if (account == null) {
          continue;
        }

        // حساب التغيير في الرصيد بناءً على نوع الحساب (عكس التأثير)
        double balanceChange = 0.0;

        switch (account.type) {
          case AccountType.asset:
          case AccountType.expense:
            balanceChange = detail.credit - detail.debit;
            break;
          case AccountType.liability:
          case AccountType.equity:
          case AccountType.revenue:
          case AccountType.customer:
          case AccountType.supplier:
          case AccountType.cash:
          case AccountType.bank:
          case AccountType.other:
            balanceChange = detail.debit - detail.credit;
            break;
        }

        // تحديث رصيد الحساب
        await txn.update(
          DatabaseService.tableAccounts,
          {
            'balance': account.balance + balanceChange,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [account.id],
        );
      }
    } catch (e) {
      AppLogger.error('فشل في عكس تأثير القيد على أرصدة الحسابات: $e');
      rethrow;
    }
  }

  /// الحصول على حساب (داخل معاملة)
  Future<Account?> _getAccount(Transaction txn, String accountId) async {
    try {
      final List<Map<String, dynamic>> maps = await txn.query(
        DatabaseService.tableAccounts,
        where: 'id = ? AND is_deleted = 0',
        whereArgs: [accountId],
      );

      if (maps.isEmpty) {
        return null;
      }

      return Account.fromMap(maps.first);
    } catch (e) {
      AppLogger.error('فشل في الحصول على الحساب (داخل معاملة): $e');
      return null;
    }
  }

  /// التحقق مما إذا كانت تفاصيل القيد قد تغيرت
  bool _hasDetailsChanged(
    List<JournalEntryDetail> oldDetails,
    List<JournalEntryDetail> newDetails,
  ) {
    if (oldDetails.length != newDetails.length) {
      return true;
    }

    // إنشاء خريطة للتفاصيل القديمة بواسطة معرف الحساب
    final oldDetailsMap = <String, JournalEntryDetail>{};
    for (final detail in oldDetails) {
      oldDetailsMap[detail.accountId] = detail;
    }

    // مقارنة التفاصيل الجديدة مع القديمة
    for (final newDetail in newDetails) {
      final oldDetail = oldDetailsMap[newDetail.accountId];
      if (oldDetail == null ||
          oldDetail.debit != newDetail.debit ||
          oldDetail.credit != newDetail.credit) {
        return true;
      }
    }

    return false;
  }
}
