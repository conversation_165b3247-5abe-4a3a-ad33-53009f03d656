import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/theme/index.dart';
import '../../../core/utils/index.dart';
import '../../../core/models/customer.dart';

/// مربع حوار نموذج العميل
class CustomerFormDialog extends StatefulWidget {
  final Customer? customer;

  const CustomerFormDialog({
    Key? key,
    this.customer,
  }) : super(key: key);

  @override
  State<CustomerFormDialog> createState() => _CustomerFormDialogState();
}

class _CustomerFormDialogState extends State<CustomerFormDialog> {
  // مفتاح النموذج
  final _formKey = GlobalKey<FormState>();

  // وحدات تحكم الحقول
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _taxNumberController = TextEditingController();
  final TextEditingController _creditLimitController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // تعبئة النموذج إذا كان هناك عميل
    if (widget.customer != null) {
      _nameController.text = widget.customer!.name;
      _phoneController.text = widget.customer!.phone ?? '';
      _emailController.text = widget.customer!.email ?? '';
      _addressController.text = widget.customer!.address ?? '';
      _taxNumberController.text = widget.customer!.taxNumber ?? '';
      // No hay campo creditLimit en el modelo Customer
      _notesController.text = widget.customer!.notes ?? '';
    } else {
      // القيم الافتراضية
      _creditLimitController.text = '0';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _taxNumberController.dispose();
    _creditLimitController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// حفظ النموذج
  void _saveForm() {
    if (_formKey.currentState!.validate()) {
      final customer = Customer(
        id: widget.customer?.id,
        name: _nameController.text,
        phone: _phoneController.text.isEmpty ? null : _phoneController.text,
        email: _emailController.text.isEmpty ? null : _emailController.text,
        address:
            _addressController.text.isEmpty ? null : _addressController.text,
        taxNumber: _taxNumberController.text.isEmpty
            ? null
            : _taxNumberController.text,
        // No hay campo creditLimit en el modelo Customer
        balance: widget.customer?.balance ?? 0,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
      );

      Navigator.pop(context, customer);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.customer == null ? 'إضافة عميل جديد' : 'تعديل العميل'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // اسم العميل
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم العميل *',
                  hintText: 'أدخل اسم العميل',
                  prefixIcon: Icon(Icons.person),
                ),
                validator: Validators.required('اسم العميل'),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: AppDimensions.spacing16),

              // رقم الهاتف
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف',
                  hintText: 'أدخل رقم الهاتف',
                  prefixIcon: Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: AppDimensions.spacing16),

              // البريد الإلكتروني
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  hintText: 'أدخل البريد الإلكتروني',
                  prefixIcon: Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: Validators.email(),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: AppDimensions.spacing16),

              // العنوان
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(
                  labelText: 'العنوان',
                  hintText: 'أدخل العنوان',
                  prefixIcon: Icon(Icons.location_on),
                ),
                maxLines: 2,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: AppDimensions.spacing16),

              // الرقم الضريبي
              TextFormField(
                controller: _taxNumberController,
                decoration: const InputDecoration(
                  labelText: 'الرقم الضريبي',
                  hintText: 'أدخل الرقم الضريبي',
                  prefixIcon: Icon(Icons.receipt),
                ),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: AppDimensions.spacing16),

              // حد الائتمان
              TextFormField(
                controller: _creditLimitController,
                decoration: const InputDecoration(
                  labelText: 'حد الائتمان',
                  hintText: 'أدخل حد الائتمان',
                  prefixIcon: Icon(Icons.credit_card),
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: Validators.number('الرصيد الافتتاحي'),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: AppDimensions.spacing16),

              // ملاحظات
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  hintText: 'أدخل ملاحظات',
                  prefixIcon: Icon(Icons.note),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveForm,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
          ),
          child: Text(widget.customer == null ? 'إضافة' : 'حفظ'),
        ),
      ],
    );
  }
}
