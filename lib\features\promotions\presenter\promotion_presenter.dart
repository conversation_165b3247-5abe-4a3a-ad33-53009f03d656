import 'package:flutter/foundation.dart';
import '../models/promotion.dart';
import '../services/promotion_service.dart';

class PromotionPresenter extends ChangeNotifier {
  final PromotionService _promotionService = PromotionService();
  List<Promotion> _promotions = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Promotion> get promotions => _promotions;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // استرجاع جميع العروض النشطة
  Future<void> loadActivePromotions() async {
    _setLoading(true);
    try {
      _promotions = await _promotionService.getActivePromotions();
      _error = null;
    } catch (e) {
      _error = 'Failed to load promotions: ${e.toString()}';
    } finally {
      _setLoading(false);
    }
  }

  // إضافة عرض جديد
  Future<bool> addPromotion(Promotion promotion) async {
    _setLoading(true);
    try {
      final id = await _promotionService.insertPromotion(promotion);
      if (id.isNotEmpty) {
        await loadActivePromotions();
        return true;
      }
      _error = 'Failed to add promotion';
      return false;
    } catch (e) {
      _error = 'Failed to add promotion: ${e.toString()}';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث عرض
  Future<bool> updatePromotion(Promotion promotion) async {
    _setLoading(true);
    try {
      final success = await _promotionService.updatePromotion(promotion);
      if (success) {
        await loadActivePromotions();
        return true;
      }
      _error = 'Failed to update promotion';
      return false;
    } catch (e) {
      _error = 'Failed to update promotion: ${e.toString()}';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // حذف عرض
  Future<bool> deletePromotion(String id) async {
    _setLoading(true);
    try {
      final success = await _promotionService.deletePromotion(id);
      if (success) {
        await loadActivePromotions();
        return true;
      }
      _error = 'Failed to delete promotion';
      return false;
    } catch (e) {
      _error = 'Failed to delete promotion: ${e.toString()}';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    Future.microtask(() => notifyListeners());
  }

  // تهيئة البيانات الأولية للعرض (إذا لم تكن موجودة)
  Future<void> initDefaultPromotions() async {
    final promotions = await _promotionService.getActivePromotions();
    if (promotions.isEmpty) {
      // إضافة بعض العروض الافتراضية إذا لم تكن هناك عروض موجودة
      await _promotionService.insertPromotion(
        Promotion(
          title: 'عرض اشتر قطعة واحصل على الثانية مجاناً',
          description: 'عرض محدود لفترة محدودة على جميع الملابس',
          expiryDate: '22/04/2025',
          colorHex: '#D32F2F', // أحمر
          iconName: 'local_offer',
          actionText: 'استفد الآن',
          displayOrder: 1,
        ),
      );

      await _promotionService.insertPromotion(
        Promotion(
          title: 'خصم 30% على المنتجات الجديدة',
          description: 'وصول تشكيلة جديدة من الأجهزة الإلكترونية',
          expiryDate: '15/05/2025',
          colorHex: '#1976D2', // أزرق
          iconName: 'new_releases',
          actionText: 'تسوق الآن',
          displayOrder: 2,
        ),
      );

      await _promotionService.insertPromotion(
        Promotion(
          title: 'شحن مجاني للطلبات فوق 500 ريال',
          description: 'لفترة محدودة على جميع المنتجات',
          expiryDate: '30/03/2025',
          colorHex: '#388E3C', // أخضر
          iconName: 'local_shipping',
          actionText: 'اطلب الآن',
          displayOrder: 3,
        ),
      );

      await loadActivePromotions();
    }
  }
}
