import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../../features/users/services/activity_log_service.dart';
import '../../features/users/models/activity_log.dart';

/// خدمة إشعارات النشاط
/// تقوم هذه الخدمة بإدارة إشعارات النشاط وعرضها للمستخدم
class ActivityNotificationService {
  // Singleton pattern
  static final ActivityNotificationService _instance =
      ActivityNotificationService._internal();
  factory ActivityNotificationService() => _instance;
  ActivityNotificationService._internal();

  final ActivityLogService _logService = ActivityLogService();
  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  Timer? _pollingTimer;
  DateTime? _lastCheckTime;
  bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات
  Future<void> init() async {
    if (_isInitialized) return;

    // تهيئة إعدادات الإشعارات
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
    );

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // تعيين وقت آخر فحص
    _lastCheckTime = DateTime.now();

    // بدء مراقبة النشاطات الجديدة
    _startPolling();

    _isInitialized = true;
  }

  /// بدء مراقبة النشاطات الجديدة
  void _startPolling() {
    // فحص النشاطات الجديدة كل دقيقة
    _pollingTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      _checkForNewActivities();
    });
  }

  /// إيقاف مراقبة النشاطات الجديدة
  void dispose() {
    _pollingTimer?.cancel();
    _pollingTimer = null;
  }

  /// فحص النشاطات الجديدة
  Future<void> _checkForNewActivities() async {
    if (_lastCheckTime == null) return;

    try {
      // الحصول على النشاطات الجديدة منذ آخر فحص
      final now = DateTime.now();
      final logs = await _logService.getActivities(
        startDate: _lastCheckTime,
        endDate: now,
      );

      // تحديث وقت آخر فحص
      _lastCheckTime = now;

      // عرض إشعارات للنشاطات المهمة فقط
      for (final log in logs) {
        if (_isImportantActivity(log)) {
          _showNotification(log);
        }
      }
    } catch (e) {
      // تجاهل الأخطاء في فحص النشاطات
      debugPrint('خطأ في فحص النشاطات الجديدة: $e');
    }
  }

  /// التحقق مما إذا كان النشاط مهمًا ويستحق الإشعار
  bool _isImportantActivity(ActivityLog log) {
    // تحديد النشاطات المهمة التي تستحق الإشعار
    // مثال: إنشاء أو حذف كيانات مهمة، تسجيل الدخول/الخروج، إلخ

    // النشاطات المتعلقة بالمبيعات والمشتريات
    if (log.module == 'sale' || log.module == 'purchase') {
      return true;
    }

    // عمليات الحذف
    if (log.action == 'delete') {
      return true;
    }

    // تسجيل الدخول/الخروج
    if (log.action == 'login' || log.action == 'logout') {
      return true;
    }

    // عمليات المزامنة
    if (log.action == 'sync') {
      return true;
    }

    return false;
  }

  /// عرض إشعار للنشاط
  Future<void> _showNotification(ActivityLog log) async {
    // تحديد عنوان الإشعار
    String title = 'نشاط جديد';
    String body = '';

    // تخصيص محتوى الإشعار حسب نوع النشاط
    switch (log.action) {
      case 'create':
        title = 'تم إنشاء ${_getEntityName(log.module)}';
        body = 'تم إنشاء ${_getEntityName(log.module)} جديد';
        break;
      case 'update':
        title = 'تم تحديث ${_getEntityName(log.module)}';
        body = 'تم تحديث ${_getEntityName(log.module)}';
        break;
      case 'delete':
        title = 'تم حذف ${_getEntityName(log.module)}';
        body = 'تم حذف ${_getEntityName(log.module)}';
        break;
      case 'login':
        title = 'تسجيل دخول';
        body = 'تم تسجيل الدخول إلى النظام';
        break;
      case 'logout':
        title = 'تسجيل خروج';
        body = 'تم تسجيل الخروج من النظام';
        break;
      case 'sync':
        title = 'مزامنة البيانات';
        body = 'تمت مزامنة البيانات بنجاح';
        break;
      default:
        body = 'نشاط جديد في النظام';
    }

    // إضافة تفاصيل إضافية إذا كانت متوفرة
    if (log.details.isNotEmpty) {
      body = '${log.details} - $body';
    }

    // إعدادات الإشعار
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'activity_channel',
      'سجلات النشاط',
      channelDescription: 'إشعارات سجلات النشاط',
      importance: Importance.high,
      priority: Priority.high,
    );

    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    // عرض الإشعار
    await _notificationsPlugin.show(
      log.id.hashCode, // استخدام معرف النشاط كمعرف للإشعار
      title,
      body,
      platformChannelSpecifics,
      payload: log.id,
    );
  }

  /// معالجة النقر على الإشعار
  void _onNotificationTapped(NotificationResponse response) {
    // يمكن إضافة منطق لفتح شاشة تفاصيل النشاط عند النقر على الإشعار
    debugPrint('تم النقر على إشعار النشاط: ${response.payload}');
  }

  /// الحصول على اسم الكيان بالعربية
  String _getEntityName(String entityType) {
    switch (entityType) {
      case 'product':
        return 'منتج';
      case 'category':
        return 'تصنيف';
      case 'customer':
        return 'عميل';
      case 'supplier':
        return 'مورد';
      case 'sale':
        return 'مبيعات';
      case 'purchase':
        return 'مشتريات';
      case 'account':
        return 'حساب';
      case 'user':
        return 'مستخدم';
      case 'settings':
        return 'إعدادات';
      default:
        return 'كيان';
    }
  }
}
