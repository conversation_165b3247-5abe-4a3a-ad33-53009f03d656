import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/database/database_helper.dart';

import '../../../core/utils/index.dart';
import '../../../core/widgets/index.dart';
import 'journal_entry_form_screen.dart';
import '../../../core/theme/index.dart';

/// شاشة قيود اليومية
class JournalEntriesScreen extends StatefulWidget {
  const JournalEntriesScreen({Key? key}) : super(key: key);

  @override
  State<JournalEntriesScreen> createState() => _JournalEntriesScreenState();
}

class _JournalEntriesScreenState extends State<JournalEntriesScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Map<String, dynamic>> _journalEntries = [];
  List<Map<String, dynamic>> _filteredEntries = [];
  bool _isLoading = true;
  String _searchQuery = '';
  DateTimeRange? _dateRange;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _loadJournalEntries();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _applyFilters();
    });
  }

  Future<void> _loadJournalEntries() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final db = await _databaseHelper.database;

      // استعلام للحصول على قيود اليومية مع معلومات إضافية
      final List<Map<String, dynamic>> entries = await db.rawQuery('''
        SELECT je.*,
               u.username as user_name,
               b.name as branch_name,
               (SELECT COUNT(*) FROM journal_entry_details WHERE journal_entry_id = je.id) as details_count,
               (SELECT SUM(debit) FROM journal_entry_details WHERE journal_entry_id = je.id) as total_amount
        FROM journal_entries je
        LEFT JOIN users u ON je.user_id = u.id
        LEFT JOIN branches b ON je.branch_id = b.id
        WHERE je.is_deleted = 0
        ORDER BY je.entry_date DESC
      ''');

      setState(() {
        _journalEntries = entries;
        _applyFilters();
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحميل قيود اليومية',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'JournalEntriesScreen'},
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل في تحميل قيود اليومية')),
        );
      }
    }
  }

  void _applyFilters() {
    List<Map<String, dynamic>> filtered = List.from(_journalEntries);

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((entry) {
        final entryNumber =
            entry['entry_number']?.toString().toLowerCase() ?? '';
        final description =
            entry['description']?.toString().toLowerCase() ?? '';
        final reference = entry['reference']?.toString().toLowerCase() ?? '';
        final userName = entry['user_name']?.toString().toLowerCase() ?? '';

        return entryNumber.contains(_searchQuery.toLowerCase()) ||
            description.contains(_searchQuery.toLowerCase()) ||
            reference.contains(_searchQuery.toLowerCase()) ||
            userName.contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // تطبيق فلتر التاريخ
    if (_dateRange != null) {
      filtered = filtered.where((entry) {
        final entryDate = DateTime.parse(entry['entry_date']);
        return (entryDate.isAfter(_dateRange!.start) ||
                entryDate.isAtSameMomentAs(_dateRange!.start)) &&
            (entryDate.isBefore(_dateRange!.end) ||
                entryDate.isAtSameMomentAs(_dateRange!.end));
      }).toList();
    }

    setState(() {
      _filteredEntries = filtered;
    });
  }

  void _onDateRangeChanged(DateTimeRange? range) {
    setState(() {
      _dateRange = range;
      _applyFilters();
    });
  }

  Future<void> _showEntryDetails(int entryId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final db = await _databaseHelper.database;

      // الحصول على تفاصيل القيد
      final List<Map<String, dynamic>> details = await db.rawQuery('''
        SELECT jed.*, a.name as account_name, a.code as account_code
        FROM journal_entry_details jed
        JOIN accounts a ON jed.account_id = a.id
        WHERE jed.journal_entry_id = ? AND jed.is_deleted = 0
        ORDER BY jed.id
      ''', [entryId]);

      // الحصول على معلومات القيد
      final List<Map<String, dynamic>> entryInfo = await db.rawQuery('''
        SELECT je.*, u.username as user_name, b.name as branch_name
        FROM journal_entries je
        LEFT JOIN users u ON je.user_id = u.id
        LEFT JOIN branches b ON je.branch_id = b.id
        WHERE je.id = ?
      ''', [entryId]);

      setState(() {
        _isLoading = false;
      });

      if (mounted && entryInfo.isNotEmpty) {
        _showEntryDetailsDialog(context, entryInfo.first, details);
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحميل تفاصيل القيد',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'JournalEntriesScreen', 'entry_id': entryId},
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل في تحميل تفاصيل القيد')),
        );
      }
    }
  }

  void _showEntryDetailsDialog(
    BuildContext context,
    Map<String, dynamic> entry,
    List<Map<String, dynamic>> details,
  ) {
    final theme = Theme.of(context);
    final isTablet = Layout.isTablet();

    // إضافة خيارات للتعديل والحذف
    void editEntry() {
      Navigator.pop(context); // إغلاق الحوار
      Navigator.of(context)
          .push(
        MaterialPageRoute(
          builder: (context) => JournalEntryFormScreen(journalEntry: entry),
        ),
      )
          .then((result) {
        if (result == true) {
          _loadJournalEntries();
        }
      });
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: isTablet ? 600 : 400,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'تفاصيل القيد',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.primaryColor,
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.edit, color: AppColors.info),
                        onPressed: editEntry,
                        tooltip: 'تعديل القيد',
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ],
              ),
              const Divider(),
              // معلومات القيد
              _buildEntryInfoSection(context, entry),
              const SizedBox(height: AppDimensions.spacing16),
              // جدول تفاصيل القيد
              Flexible(
                child: SingleChildScrollView(
                  child: _buildEntryDetailsTable(context, details),
                ),
              ),
              const SizedBox(height: AppDimensions.spacing16),
              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.print),
                    label: const Text('طباعة'),
                    onPressed: () {
                      // سيتم تنفيذها لاحقاً
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('سيتم إضافة ميزة الطباعة قريباً')),
                      );
                    },
                  ),
                  const SizedBox(width: 16),
                  OutlinedButton.icon(
                    icon: const Icon(Icons.edit),
                    label: const Text('تعديل'),
                    onPressed: () {
                      // سيتم تنفيذها لاحقاً
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('سيتم إضافة ميزة التعديل قريباً')),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEntryInfoSection(
      BuildContext context, Map<String, dynamic> entry) {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final entryDate = DateTime.parse(entry['entry_date']);

    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    label: 'رقم القيد',
                    value: entry['entry_number'] ?? '',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    label: 'تاريخ القيد',
                    value: dateFormat.format(entryDate),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.spacing8),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    label: 'الوصف',
                    value: entry['description'] ?? '',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    label: 'المرجع',
                    value: entry['reference'] ?? '',
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.spacing8),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    label: 'المستخدم',
                    value: entry['user_name'] ?? '',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    label: 'الفرع',
                    value: entry['branch_name'] ?? '',
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.spacing8),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    label: 'حالة الترحيل',
                    value: entry['is_posted'] == 1 ? 'مرحل' : 'غير مرحل',
                    valueColor: entry['is_posted'] == 1
                        ? AppColors.success
                        : AppColors.warning,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    label: 'تاريخ الإنشاء',
                    value: entry['created_at'] != null
                        ? dateFormat.format(DateTime.parse(entry['created_at']))
                        : '',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context, {
    required String label,
    required String value,
    Color? valueColor,
  }) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: AppColors.lightTextSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.spacing4),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: valueColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEntryDetailsTable(
    BuildContext context,
    List<Map<String, dynamic>> details,
  ) {
    final theme = Theme.of(context);

    // حساب المجاميع
    double totalDebit = 0.0;
    double totalCredit = 0.0;

    for (var detail in details) {
      totalDebit += detail['debit'] ?? 0.0;
      totalCredit += detail['credit'] ?? 0.0;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // عنوان الجدول
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          decoration: BoxDecoration(
            color: theme.primaryColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Row(
            children: [
              Expanded(flex: 1, child: _buildTableHeader(context, 'الكود')),
              Expanded(flex: 3, child: _buildTableHeader(context, 'الحساب')),
              Expanded(flex: 2, child: _buildTableHeader(context, 'مدين')),
              Expanded(flex: 2, child: _buildTableHeader(context, 'دائن')),
              Expanded(flex: 3, child: _buildTableHeader(context, 'البيان')),
            ],
          ),
        ),

        // صفوف الجدول
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: details.length,
          itemBuilder: (context, index) {
            final detail = details[index];
            final isEven = index % 2 == 0;

            return Container(
              color:
                  isEven ? AppColors.lightSurfaceVariant : AppColors.onPrimary,
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        detail['account_code'] ?? '',
                        style: theme.textTheme.bodySmall,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        detail['account_name'] ?? '',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        (detail['debit'] ?? 0.0).toStringAsFixed(2),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: (detail['debit'] ?? 0.0) > 0
                              ? AppColors.infoDark
                              : null,
                          fontWeight: (detail['debit'] ?? 0.0) > 0
                              ? FontWeight.bold
                              : null,
                        ),
                        textAlign: TextAlign.start,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        (detail['credit'] ?? 0.0).toStringAsFixed(2),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: (detail['credit'] ?? 0.0) > 0
                              ? AppColors.errorDark
                              : null,
                          fontWeight: (detail['credit'] ?? 0.0) > 0
                              ? FontWeight.bold
                              : null,
                        ),
                        textAlign: TextAlign.start,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        detail['description'] ?? '',
                        style: theme.textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),

        // صف المجاميع
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
          decoration: BoxDecoration(
            color: theme.primaryColor.withValues(alpha: 0.1),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(8),
              bottomRight: Radius.circular(8),
            ),
            border:
                Border.all(color: theme.primaryColor.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              const Expanded(flex: 1, child: SizedBox()),
              Expanded(
                flex: 3,
                child: Text(
                  'الإجمالي',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  totalDebit.toStringAsFixed(2),
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.lightTextSecondary,
                  ),
                  textAlign: TextAlign.start,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  totalCredit.toStringAsFixed(2),
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.lightTextSecondary,
                  ),
                  textAlign: TextAlign.start,
                ),
              ),
              const Expanded(flex: 3, child: SizedBox()),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTableHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        title,
        style: AppTypography.createCustomStyle(
          color: AppColors.lightTextSecondary,
          fontWeight: AppTypography.weightBold,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isTablet = Layout.isTablet();

    return Scaffold(
      appBar: const AkAppBar(
        title: 'قيود اليومية',
      ),
      body: AkLoadingOverlay(
        isLoading: _isLoading,
        child: Column(
          children: [
            // شريط البحث والفلترة
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  // حقل البحث
                  Expanded(
                    flex: 2,
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'بحث في القيود...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          vertical: 12,
                          horizontal: 16,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // فلتر التاريخ
                  Expanded(
                    flex: isTablet ? 2 : 1,
                    child: AkDateRangeInput(
                      label: 'فترة التاريخ',
                      onDateRangeSelected: (start, end) {
                        if (start != null && end != null) {
                          _onDateRangeChanged(
                              DateTimeRange(start: start, end: end));
                        } else {
                          _onDateRangeChanged(null);
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),

            // زر إضافة قيد جديد
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'عدد القيود: ${_filteredEntries.length}',
                    style: theme.textTheme.bodyMedium,
                  ),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة قيد جديد'),
                    onPressed: () {
                      // سيتم تنفيذها لاحقاً
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content:
                                Text('سيتم إضافة ميزة إنشاء قيد جديد قريباً')),
                      );
                    },
                  ),
                ],
              ),
            ),

            const Divider(height: 32),

            // قائمة القيود
            Expanded(
              child: _filteredEntries.isEmpty
                  ? AkEmptyState(
                      title: 'لا توجد قيود محاسبية',
                      message: _searchQuery.isNotEmpty || _dateRange != null
                          ? 'لا توجد قيود تطابق معايير البحث'
                          : 'لم يتم إنشاء أي قيود محاسبية حتى الآن',
                      description: _searchQuery.isNotEmpty || _dateRange != null
                          ? 'جرب تغيير معايير البحث أو التصفية'
                          : 'ابدأ بإنشاء قيد محاسبي جديد',
                      icon: Icons.note_alt_outlined,
                      onRefresh: _loadJournalEntries,
                      onAction: _searchQuery.isEmpty && _dateRange == null
                          ? () => Navigator.of(context)
                                  .push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const JournalEntryFormScreen(),
                                ),
                              )
                                  .then((result) {
                                if (result == true) {
                                  _loadJournalEntries();
                                }
                              })
                          : null,
                      actionText: 'إنشاء قيد جديد',
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      itemCount: _filteredEntries.length,
                      itemBuilder: (context, index) {
                        final entry = _filteredEntries[index];
                        final entryDate = DateTime.parse(entry['entry_date']);
                        final dateFormat = DateFormat('yyyy-MM-dd');

                        return AkCard(
                          margin: const EdgeInsets.only(bottom: 12.0),
                          onTap: () => _showEntryDetails(entry['id']),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: theme.primaryColor,
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          child: Text(
                                            entry['entry_number'] ?? '',
                                            style:
                                                AppTypography.createCustomStyle(
                                              color:
                                                  AppColors.lightTextSecondary,
                                              fontWeight:
                                                  AppTypography.weightBold,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          dateFormat.format(entryDate),
                                          style: theme.textTheme.bodyMedium,
                                        ),
                                      ],
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: entry['is_posted'] == 1
                                            ? AppColors.successLight
                                            : AppColors.warningLight,
                                        borderRadius: BorderRadius.circular(4),
                                        border: Border.all(
                                          color: entry['is_posted'] == 1
                                              ? AppColors.success
                                              : AppColors.warning,
                                        ),
                                      ),
                                      child: Text(
                                        entry['is_posted'] == 1
                                            ? 'مرحل'
                                            : 'غير مرحل',
                                        style: AppTypography.createCustomStyle(
                                          color: entry['is_posted'] == 1
                                              ? AppColors.successDark
                                              : AppColors.warningDark,
                                          fontWeight: AppTypography.weightBold,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: AppDimensions.spacing12),
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.description_outlined,
                                      size: 16,
                                      color: AppColors.lightTextSecondary,
                                    ),
                                    const SizedBox(width: 4),
                                    Expanded(
                                      child: Text(
                                        entry['description'] ?? 'بدون وصف',
                                        style: theme.textTheme.bodyMedium,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: AppDimensions.spacing8),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(
                                          Icons.person_outline,
                                          size: 16,
                                          color: AppColors.lightTextSecondary,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          entry['user_name'] ?? 'غير معروف',
                                          style: theme.textTheme.bodySmall
                                              ?.copyWith(
                                            color:
                                                AppColors.lightSurfaceVariant,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Text(
                                      'المبلغ: ${(entry['total_amount'] ?? 0.0).toStringAsFixed(2)}',
                                      style:
                                          theme.textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: theme.primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: AppDimensions.spacing8),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton.icon(
                                      icon: const Icon(Icons.visibility),
                                      label: const Text('عرض التفاصيل'),
                                      onPressed: () =>
                                          _showEntryDetails(entry['id']),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: AkFloatingButton(
        onPressed: () {
          Navigator.of(context)
              .push(
            MaterialPageRoute(
              builder: (context) => const JournalEntryFormScreen(),
            ),
          )
              .then((result) {
            // إعادة تحميل قيود اليومية بعد العودة من شاشة الإضافة/التعديل
            if (result == true) {
              _loadJournalEntries();
            }
          });
        },
        tooltip: 'إضافة قيد جديد',
        icon: Icons.add,
      ),
    );
  }
}
