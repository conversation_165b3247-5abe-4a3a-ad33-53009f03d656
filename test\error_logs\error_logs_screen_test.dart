import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/utils/error_tracker.dart';
import 'package:tajer_plus/features/error_logs/screens/error_logs_screen.dart';

void main() {
  group('ErrorLogsScreen Tests', () {
    setUp(() {
      // تنظيف السجل قبل كل اختبار
      ErrorTracker.clearHistory();
    });

    testWidgets('تعرض رسالة عندما لا توجد أخطاء', (WidgetTester tester) async {
      // بناء الشاشة
      await tester.pumpWidget(
        const MaterialApp(
          home: ErrorLogsScreen(),
        ),
      );

      // التحقق من وجود رسالة "لا توجد أخطاء"
      expect(find.text('No errors recorded'), findsOneWidget);
    });

    testWidgets('تعرض قائمة الأخطاء عند وجودها', (WidgetTester tester) async {
      // تسجيل بعض الأخطاء
      ErrorTracker.captureError(
        "خطأ تجريبي 1",
        error: Exception("هذا خطأ للاختبار 1"),
        stackTrace: StackTrace.current,
      );
      
      ErrorTracker.captureError(
        "خطأ تجريبي 2",
        error: Exception("هذا خطأ للاختبار 2"),
        stackTrace: StackTrace.current,
      );

      // بناء الشاشة
      await tester.pumpWidget(
        const MaterialApp(
          home: ErrorLogsScreen(),
        ),
      );

      // التحقق من عرض الأخطاء
      expect(find.text('خطأ تجريبي 1'), findsOneWidget);
      expect(find.text('خطأ تجريبي 2'), findsOneWidget);
      
      // التحقق من وجود إحصائيات الأخطاء
      expect(find.text('Error Statistics'), findsOneWidget);
      expect(find.text('Total Errors'), findsOneWidget);
      expect(find.text('2'), findsOneWidget); // إجمالي عدد الأخطاء
    });

    testWidgets('يمكن مسح سجل الأخطاء من الشاشة', (WidgetTester tester) async {
      // تسجيل خطأ
      ErrorTracker.captureError(
        "خطأ تجريبي",
        error: Exception("هذا خطأ للاختبار"),
        stackTrace: StackTrace.current,
      );

      // بناء الشاشة
      await tester.pumpWidget(
        const MaterialApp(
          home: ErrorLogsScreen(),
        ),
      );

      // التحقق من وجود الخطأ
      expect(find.text('خطأ تجريبي'), findsOneWidget);
      
      // النقر على زر مسح السجل
      await tester.tap(find.byIcon(Icons.delete_sweep));
      await tester.pump();
      
      // التحقق من مسح السجل
      expect(find.text('No errors recorded'), findsOneWidget);
    });

    testWidgets('يمكن توسيع تفاصيل الخطأ', (WidgetTester tester) async {
      // تسجيل خطأ مع سياق
      ErrorTracker.captureError(
        "خطأ تجريبي",
        error: Exception("هذا خطأ للاختبار"),
        stackTrace: StackTrace.current,
        context: {'screen': 'TestScreen', 'action': 'TestAction'},
      );

      // بناء الشاشة
      await tester.pumpWidget(
        const MaterialApp(
          home: ErrorLogsScreen(),
        ),
      );

      // النقر على الخطأ لتوسيعه
      await tester.tap(find.text('خطأ تجريبي'));
      await tester.pump();
      
      // التحقق من ظهور التفاصيل
      expect(find.text('Context:'), findsOneWidget);
      expect(find.text('Stack Trace:'), findsOneWidget);
      expect(find.textContaining('{screen: TestScreen, action: TestAction}'), findsOneWidget);
    });
  });
}
