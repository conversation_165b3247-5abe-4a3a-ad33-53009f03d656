import 'package:flutter/material.dart';
import '../../../core/models/sale.dart';
import '../../../core/models/sale_status.dart';
import '../presenters/sale_presenter.dart';
import '../../../core/widgets/index.dart';
import 'sale_form_screen.dart';
import 'sale_details_screen.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد
import '../../../core/providers/app_providers.dart'; // للتحميل الكسول

class SalesScreen extends StatefulWidget {
  const SalesScreen({Key? key}) : super(key: key);

  @override
  State<SalesScreen> createState() => _SalesScreenState();
}

class _SalesScreenState extends State<SalesScreen> {
  late SalePresenter _salePresenter;
  final _searchController = TextEditingController();
  bool _showSearchField = false; // متغير لإظهار/إخفاء حقل البحث
  String? _selectedStatus;
  DateTimeRange? _dateRange;

  @override
  void initState() {
    super.initState();
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    _salePresenter = AppProviders.getLazyPresenter<SalePresenter>(
      () => SalePresenter(),
    );
    // استخدام Future.microtask لتجنب استدعاء setState أثناء البناء
    Future.microtask(_loadSales);

    // إضافة مستمع للبحث (نفس آلية UsersScreen)
    _searchController.addListener(() {
      _salePresenter.searchQuery =
          _searchController.text.isNotEmpty ? _searchController.text : null;
    });
  }

  // تحميل المبيعات مع مراعاة نوع الفاتورة
  Future<void> _loadSales() async {
    await _salePresenter.loadSales(type: 'sale');
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'المبيعات',
        actions: [
          // أيقونة البحث (نفس آلية UsersScreen)
          IconButton(
            icon: Icon(_showSearchField ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                }
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToSaleForm(),
          ),
        ],
      ),
      body: Column(
        children: [
          // إظهار حقل البحث فقط عند الحاجة (نفس آلية UsersScreen)
          if (_showSearchField) _buildSearchBar(),
          _buildStatsCards(),
          Expanded(
            child: _buildSalesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: AppDimensions.getResponsivePadding(
          horizontal: 4, vertical: 2), // استخدام الهوامش المتجاوبة
      child: AkSearchInput(
        controller: _searchController,
        hint:
            'بحث في المبيعات (رقم الفاتورة، العميل، التاريخ، المبلغ)...', // نص توضيحي شامل
        onChanged: (value) {
          // منطق البحث سيتم تطبيقه تلقائياً عبر المستمع في initState
        },
        onClear: () {
          _searchController.clear();
        },
      ),
    );
  }

  Widget _buildStatsCards() {
    return Container(
      height: AppDimensions.statsCardHeight, // استخدام ثابت الأبعاد الموحد
      padding: AppDimensions.getResponsivePadding(
          horizontal: 4), // استخدام الهوامش المتجاوبة
      child: ListenableBuilder(
        listenable: _salePresenter,
        builder: (context, child) {
          return FutureBuilder<Map<String, dynamic>>(
            future: _salePresenter.getSalesStatistics(),
            builder: (context, snapshot) {
              final stats =
                  snapshot.data ?? {'daily': 0, 'monthly': 0, 'yearly': 0};

              return Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'اليوم',
                      '${stats['daily']?.toStringAsFixed(2) ?? '0.00'}',
                      AppColors.info,
                      Icons.today,
                    ),
                  ),
                  SizedBox(
                      width: AppDimensions
                          .smallSpacing), // استخدام المسافات الموحدة
                  Expanded(
                    child: _buildStatCard(
                      'هذا الشهر',
                      '${stats['monthly']?.toStringAsFixed(2) ?? '0.00'}',
                      AppColors.success,
                      Icons.calendar_today,
                    ),
                  ),
                  SizedBox(
                      width: AppDimensions
                          .smallSpacing), // استخدام المسافات الموحدة
                  Expanded(
                    child: _buildStatCard(
                      'هذه السنة',
                      '${stats['yearly']?.toStringAsFixed(2) ?? '0.00'}',
                      AppColors.accent,
                      Icons.bar_chart,
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, Color color, IconData icon) {
    return SizedBox(
      height: AppDimensions.statsCardHeight, // استخدام ثابت الأبعاد الموحد
      child: AkCard(
        size: AkCardSize.small,
        padding: AppDimensions.getResponsivePadding(
            horizontal: 1.5, vertical: 1), // استخدام الهوامش المتجاوبة
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: AppDimensions
                      .smallIconSize, // استخدام حجم الأيقونة الموحد
                ),
                SizedBox(
                    width:
                        AppDimensions.tinySpacing), // استخدام المسافات الموحدة
                Flexible(
                  child: Text(
                    title,
                    style: AppTypography(
                      color: color,
                      fontSize: AppTypography
                          .fontSizeXSmall, // استخدام حجم الخط الموحد
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            SizedBox(
                height: AppDimensions.tinySpacing), // استخدام المسافات الموحدة
            Flexible(
              child: Text(
                value,
                style: AppTypography(
                  fontWeight:
                      AppTypography.weightBold, // استخدام وزن الخط الموحد
                  fontSize:
                      AppTypography.fontSizeSmall, // استخدام حجم الخط الموحد
                  color: DynamicColors.onSurface(
                      context), // استخدام الألوان الديناميكية
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesList() {
    return ListenableBuilder(
      listenable: _salePresenter,
      builder: (context, child) {
        if (_salePresenter.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_salePresenter.error != null) {
          return Center(
            child: Text(
              'Error: ${_salePresenter.error}',
              style: const AppTypography(color: AppColors.error),
            ),
          );
        }

        final sales = _salePresenter.sales;
        if (sales.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('لا توجد مبيعات'),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  icon: const Icon(Icons.add),
                  label: const Text('إنشاء مبيعات جديدة'),
                  onPressed: () => _navigateToSaleForm(),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: sales.length,
          itemBuilder: (context, index) {
            final sale = sales[index];
            return _buildSaleCard(sale);
          },
        );
      },
    );
  }

  Widget _buildSaleCard(Sale sale) {
    final statusColor = _getStatusColor(sale.status);
    final formattedDate =
        '${sale.createdAt.day}/${sale.createdAt.month}/${sale.createdAt.year}';

    return AkCard(
      margin: AppDimensions.getResponsivePadding(
          horizontal: 4, vertical: 2), // استخدام الهوامش المتجاوبة
      onTap: () => _navigateToSaleDetails(sale),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'فاتورة #${sale.id.substring(0, 8)}', // ترجمة للعربية
                style: AppTypography(
                  fontWeight:
                      AppTypography.weightBold, // استخدام وزن الخط الموحد
                  fontSize:
                      AppTypography.fontSizeMedium, // استخدام حجم الخط الموحد
                  color: DynamicColors.onSurface(
                      context), // استخدام الألوان الديناميكية
                ),
              ),
              Container(
                padding: AppDimensions.getResponsivePadding(
                    horizontal: 2, vertical: 1), // استخدام الهوامش المتجاوبة
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(
                      AppDimensions.mediumRadius), // استخدام نصف القطر الموحد
                ),
                child: Text(
                  _getSaleStatusText(sale.status),
                  style: AppTypography(
                    color: statusColor,
                    fontWeight:
                        AppTypography.weightBold, // استخدام وزن الخط الموحد
                    fontSize:
                        AppTypography.fontSizeSmall, // استخدام حجم الخط الموحد
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
              height: AppDimensions.smallSpacing), // استخدام المسافات الموحدة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'العناصر: ${sale.items.length}', // ترجمة للعربية
                style: AppTypography(
                  fontSize:
                      AppTypography.fontSizeSmall, // استخدام حجم الخط الموحد
                  color: DynamicColors.onSurfaceVariant(
                      context), // استخدام الألوان الديناميكية
                ),
              ),
              Text(
                formattedDate,
                style: AppTypography(
                  fontSize:
                      AppTypography.fontSizeSmall, // استخدام حجم الخط الموحد
                  color: DynamicColors.onSurfaceVariant(
                      context), // استخدام الألوان الديناميكية
                ),
              ),
            ],
          ),
          SizedBox(
              height: AppDimensions.smallSpacing), // استخدام المسافات الموحدة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الإجمالي:', // ترجمة للعربية
                style: AppTypography(
                  fontSize:
                      AppTypography.fontSizeSmall, // استخدام حجم الخط الموحد
                  color: DynamicColors.onSurfaceVariant(
                      context), // استخدام الألوان الديناميكية
                ),
              ),
              Text(
                '${sale.total.toStringAsFixed(2)} ر.ي', // إضافة العملة
                style: AppTypography(
                  fontWeight:
                      AppTypography.weightBold, // استخدام وزن الخط الموحد
                  fontSize:
                      AppTypography.fontSizeMedium, // استخدام حجم الخط الموحد
                  color: DynamicColors.primaryDynamic(
                      context), // استخدام الألوان الديناميكية
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(SaleStatus status) {
    switch (status) {
      case SaleStatus.completed:
        return AppColors.success;
      case SaleStatus.draft:
        return AppColors.warning;
      case SaleStatus.pending:
        return AppColors.info;
      case SaleStatus.cancelled:
        return AppColors.error;
      case SaleStatus.returned:
        return AppColors.accent;
    }
  }

  String _getSaleStatusText(SaleStatus status) {
    switch (status) {
      case SaleStatus.completed:
        return 'مكتملة';
      case SaleStatus.draft:
        return 'مسودة';
      case SaleStatus.pending:
        return 'قيد الانتظار';
      case SaleStatus.cancelled:
        return 'ملغاة';
      case SaleStatus.returned:
        return 'مرتجع';
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('تصفية المبيعات'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AkDropdownInput<String>(
                    label: 'الحالة',
                    value: _selectedStatus,
                    hint: 'جميع الحالات',
                    items: const [
                      DropdownMenuItem(
                          value: null, child: Text('جميع الحالات')),
                      DropdownMenuItem(value: 'draft', child: Text('مسودة')),
                      DropdownMenuItem(
                          value: 'pending', child: Text('قيد الانتظار')),
                      DropdownMenuItem(
                          value: 'completed', child: Text('مكتملة')),
                      DropdownMenuItem(
                          value: 'cancelled', child: Text('ملغاة')),
                      DropdownMenuItem(value: 'returned', child: Text('مرتجع')),
                    ],
                    onChanged: (value) {
                      setState(() => _selectedStatus = value);
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          icon: const Icon(Icons.date_range),
                          label: Text(_dateRange != null
                              ? '${_dateRange!.start.day}/${_dateRange!.start.month} - ${_dateRange!.end.day}/${_dateRange!.end.month}'
                              : 'اختيار نطاق التاريخ'),
                          onPressed: () async {
                            final picked = await showDateRangePicker(
                              context: context,
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now(),
                              initialDateRange: _dateRange,
                            );
                            if (picked != null) {
                              setState(() => _dateRange = picked);
                            }
                          },
                        ),
                      ),
                      if (_dateRange != null)
                        IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () => setState(() => _dateRange = null),
                        ),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedStatus = null;
                      _dateRange = null;
                    });
                  },
                  child: const Text('مسح'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    _salePresenter.selectedStatus = _selectedStatus;
                    if (_dateRange != null) {
                      _salePresenter.dateRange = (
                        start: _dateRange!.start,
                        end: _dateRange!.end,
                      );
                    } else {
                      _salePresenter.dateRange = (start: null, end: null);
                    }
                    Navigator.pop(context);
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _navigateToSaleForm() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SaleFormScreen(invoiceType: 'sale'),
      ),
    );

    if (result == true && mounted) {
      _loadSales();
    }
  }

  Future<void> _navigateToSaleDetails(Sale sale) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SaleDetailsScreen(saleId: sale.id),
      ),
    );

    if (result == true) {
      _salePresenter.init();
    }
  }
}
