import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/utils/error_handler.dart';

void main() {
  group('ErrorHandler Advanced Tests', () {
    testWidgets('يعرض حوار تأكيد', (WidgetTester tester) async {
      // بناء تطبيق بسيط مع Scaffold
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    await ErrorHandler.showConfirmationDialog(
                      context,
                      'عنوان التأكيد',
                      'هل أنت متأكد من إتمام هذه العملية؟',
                    );
                  },
                  child: const Text('عرض حوار تأكيد'),
                );
              },
            ),
          ),
        ),
      );

      // النقر على الزر لعرض حوار التأكيد
      await tester.tap(find.text('عرض حوار تأكيد'));
      await tester.pumpAndSettle();
      
      // التحقق من ظهور حوار التأكيد
      expect(find.text('عنوان التأكيد'), findsOneWidget);
      expect(find.text('هل أنت متأكد من إتمام هذه العملية؟'), findsOneWidget);
      expect(find.text('نعم'), findsOneWidget);
      expect(find.text('لا'), findsOneWidget);
    });

    testWidgets('يعرض حوار إدخال', (WidgetTester tester) async {
      // بناء تطبيق بسيط مع Scaffold
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    await ErrorHandler.showInputDialog(
                      context,
                      'عنوان الإدخال',
                      'الرجاء إدخال البيانات',
                      hintText: 'إدخال هنا',
                    );
                  },
                  child: const Text('عرض حوار إدخال'),
                );
              },
            ),
          ),
        ),
      );

      // النقر على الزر لعرض حوار الإدخال
      await tester.tap(find.text('عرض حوار إدخال'));
      await tester.pumpAndSettle();
      
      // التحقق من ظهور حوار الإدخال
      expect(find.text('عنوان الإدخال'), findsOneWidget);
      expect(find.text('الرجاء إدخال البيانات'), findsOneWidget);
      expect(find.text('إدخال هنا'), findsOneWidget);
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('موافق'), findsOneWidget);
      expect(find.text('إلغاء'), findsOneWidget);
    });

    testWidgets('يعرض رسالة تحذير', (WidgetTester tester) async {
      // بناء تطبيق بسيط مع Scaffold
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorHandler.showWarning(
                      context,
                      'رسالة تحذير تجريبية',
                    );
                  },
                  child: const Text('عرض تحذير'),
                );
              },
            ),
          ),
        ),
      );

      // النقر على الزر لعرض التحذير
      await tester.tap(find.text('عرض تحذير'));
      await tester.pump();
      
      // التحقق من ظهور رسالة التحذير في Snackbar
      expect(find.text('رسالة تحذير تجريبية'), findsOneWidget);
    });

    testWidgets('يعرض رسالة معلومات', (WidgetTester tester) async {
      // بناء تطبيق بسيط مع Scaffold
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorHandler.showInfo(
                      context,
                      'رسالة معلومات تجريبية',
                    );
                  },
                  child: const Text('عرض معلومات'),
                );
              },
            ),
          ),
        ),
      );

      // النقر على الزر لعرض المعلومات
      await tester.tap(find.text('عرض معلومات'));
      await tester.pump();
      
      // التحقق من ظهور رسالة المعلومات في Snackbar
      expect(find.text('رسالة معلومات تجريبية'), findsOneWidget);
    });
  });
}
