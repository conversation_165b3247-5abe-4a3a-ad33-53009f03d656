import 'package:flutter/foundation.dart';

/// نموذج جرد المخزون
class InventoryCount {
  /// معرف الجرد
  final String? id;

  /// رقم الجرد
  final String countNumber;

  /// تاريخ الجرد
  final DateTime countDate;

  /// معرف المستودع
  final String warehouseId;

  /// اسم المستودع
  final String warehouseName;

  /// حالة الجرد (مسودة، مكتمل، معتمد)
  final String status;

  /// ملاحظات
  final String? notes;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  /// معرف المستخدم الذي أنشأ الجرد
  final String? createdBy;

  /// تاريخ التعديل
  final DateTime? updatedAt;

  /// معرف المستخدم الذي عدل الجرد
  final String? updatedBy;

  /// عناصر الجرد
  final List<InventoryCountItem> items;

  /// إنشاء جرد مخزون جديد
  InventoryCount({
    this.id,
    required this.countNumber,
    required this.countDate,
    required this.warehouseId,
    required this.warehouseName,
    required this.status,
    this.notes,
    required this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    required this.items,
  });

  /// إنشاء نسخة جديدة من الجرد مع تحديث بعض الحقول
  InventoryCount copyWith({
    String? id,
    String? countNumber,
    DateTime? countDate,
    String? warehouseId,
    String? warehouseName,
    String? status,
    String? notes,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    List<InventoryCountItem>? items,
  }) {
    return InventoryCount(
      id: id ?? this.id,
      countNumber: countNumber ?? this.countNumber,
      countDate: countDate ?? this.countDate,
      warehouseId: warehouseId ?? this.warehouseId,
      warehouseName: warehouseName ?? this.warehouseName,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      items: items ?? this.items,
    );
  }

  /// تحويل الجرد إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'count_number': countNumber,
      'count_date': countDate.toIso8601String(),
      'warehouse_id': warehouseId,
      'warehouse_name': warehouseName,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'items': items.map((item) => item.toMap()).toList(),
    };
  }

  /// إنشاء جرد من خريطة
  factory InventoryCount.fromMap(Map<String, dynamic> map) {
    return InventoryCount(
      id: map['id'],
      countNumber: map['count_number'],
      countDate: DateTime.parse(map['count_date']),
      warehouseId: map['warehouse_id'],
      warehouseName: map['warehouse_name'],
      status: map['status'],
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      items: List<InventoryCountItem>.from(
        (map['items'] as List).map(
          (item) => InventoryCountItem.fromMap(item),
        ),
      ),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is InventoryCount &&
        other.id == id &&
        other.countNumber == countNumber &&
        other.countDate == countDate &&
        other.warehouseId == warehouseId &&
        other.warehouseName == warehouseName &&
        other.status == status &&
        other.notes == notes &&
        other.createdAt == createdAt &&
        other.createdBy == createdBy &&
        other.updatedAt == updatedAt &&
        other.updatedBy == updatedBy &&
        listEquals(other.items, items);
  }

  @override
  int get hashCode {
    return id.hashCode ^
        countNumber.hashCode ^
        countDate.hashCode ^
        warehouseId.hashCode ^
        warehouseName.hashCode ^
        status.hashCode ^
        notes.hashCode ^
        createdAt.hashCode ^
        createdBy.hashCode ^
        updatedAt.hashCode ^
        updatedBy.hashCode ^
        items.hashCode;
  }
}

/// نموذج عنصر جرد المخزون
class InventoryCountItem {
  /// معرف العنصر
  final String? id;

  /// معرف الجرد
  final String? countId;

  /// معرف المنتج
  final String productId;

  /// اسم المنتج
  final String productName;

  /// رمز المنتج
  final String? productCode;

  /// باركود المنتج
  final String? productBarcode;

  /// الكمية في النظام
  final double systemQuantity;

  /// الكمية المجرودة
  final double countedQuantity;

  /// الفرق
  final double difference;

  /// اسم الوحدة
  final String? unitName;

  /// ملاحظات
  final String? notes;

  /// هل تم جرد العنصر
  final bool isCounted;

  /// إنشاء عنصر جرد مخزون جديد
  InventoryCountItem({
    this.id,
    this.countId,
    required this.productId,
    required this.productName,
    this.productCode,
    this.productBarcode,
    required this.systemQuantity,
    required this.countedQuantity,
    required this.difference,
    this.unitName,
    this.notes,
    required this.isCounted,
  });

  /// إنشاء نسخة جديدة من العنصر مع تحديث بعض الحقول
  InventoryCountItem copyWith({
    String? id,
    String? countId,
    String? productId,
    String? productName,
    String? productCode,
    String? productBarcode,
    double? systemQuantity,
    double? countedQuantity,
    double? difference,
    String? unitName,
    String? notes,
    bool? isCounted,
  }) {
    return InventoryCountItem(
      id: id ?? this.id,
      countId: countId ?? this.countId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      productBarcode: productBarcode ?? this.productBarcode,
      systemQuantity: systemQuantity ?? this.systemQuantity,
      countedQuantity: countedQuantity ?? this.countedQuantity,
      difference: difference ?? this.difference,
      unitName: unitName ?? this.unitName,
      notes: notes ?? this.notes,
      isCounted: isCounted ?? this.isCounted,
    );
  }

  /// تحويل العنصر إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'count_id': countId,
      'product_id': productId,
      'product_name': productName,
      'product_code': productCode,
      'product_barcode': productBarcode,
      'system_quantity': systemQuantity,
      'counted_quantity': countedQuantity,
      'difference': difference,
      'unit_name': unitName,
      'notes': notes,
      'is_counted': isCounted,
    };
  }

  /// إنشاء عنصر من خريطة
  factory InventoryCountItem.fromMap(Map<String, dynamic> map) {
    return InventoryCountItem(
      id: map['id'],
      countId: map['count_id'],
      productId: map['product_id'],
      productName: map['product_name'],
      productCode: map['product_code'],
      productBarcode: map['product_barcode'],
      systemQuantity: map['system_quantity']?.toDouble() ?? 0.0,
      countedQuantity: map['counted_quantity']?.toDouble() ?? 0.0,
      difference: map['difference']?.toDouble() ?? 0.0,
      unitName: map['unit_name'],
      notes: map['notes'],
      isCounted: map['is_counted'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is InventoryCountItem &&
        other.id == id &&
        other.countId == countId &&
        other.productId == productId &&
        other.productName == productName &&
        other.productCode == productCode &&
        other.productBarcode == productBarcode &&
        other.systemQuantity == systemQuantity &&
        other.countedQuantity == countedQuantity &&
        other.difference == difference &&
        other.unitName == unitName &&
        other.notes == notes &&
        other.isCounted == isCounted;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        countId.hashCode ^
        productId.hashCode ^
        productName.hashCode ^
        productCode.hashCode ^
        productBarcode.hashCode ^
        systemQuantity.hashCode ^
        countedQuantity.hashCode ^
        difference.hashCode ^
        unitName.hashCode ^
        notes.hashCode ^
        isCounted.hashCode;
  }
}
