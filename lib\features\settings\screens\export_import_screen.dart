import 'package:flutter/material.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/widgets/index.dart';

import '../../../core/theme/index.dart';

/// شاشة تصدير واستيراد البيانات
class ExportImportScreen extends StatefulWidget {
  const ExportImportScreen({Key? key}) : super(key: key);

  @override
  State<ExportImportScreen> createState() => _ExportImportScreenState();
}

class _ExportImportScreenState extends State<ExportImportScreen> {
  bool _isLoading = false;
  String? _lastBackupDate;
  String? _exportPath;
  String? _importPath;
  String? _errorMessage;
  String? _successMessage;

  @override
  void initState() {
    super.initState();
    _checkLastBackupDate();
    _requestPermissions();
  }

  /// طلب الأذونات اللازمة
  Future<void> _requestPermissions() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      if (status.isDenied) {
        setState(() {
          _errorMessage = 'يجب منح إذن الوصول إلى التخزين لاستخدام هذه الميزة';
        });
      }
    }
  }

  /// التحقق من تاريخ آخر نسخة احتياطية
  Future<void> _checkLastBackupDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastBackup = prefs.getString('last_backup_date');
      if (lastBackup != null) {
        setState(() {
          _lastBackupDate = lastBackup;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage =
            'فشل في التحقق من تاريخ آخر نسخة احتياطية: ${e.toString()}';
      });
    }
  }

  /// تصدير قاعدة البيانات
  Future<void> _exportDatabase() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // إنشاء مجلد للنسخ الاحتياطية إذا لم يكن موجودًا
      final appDir = await getExternalStorageDirectory();
      final backupDir = Directory('${appDir!.path}/backups');
      if (!backupDir.existsSync()) {
        backupDir.createSync(recursive: true);
      }

      // إنشاء اسم ملف النسخة الاحتياطية باستخدام التاريخ والوقت الحاليين
      final now = DateTime.now();
      final formatter = DateFormat('yyyy-MM-dd_HH-mm-ss');
      final backupFileName = 'tajer_plus_backup_${formatter.format(now)}.db';
      final backupPath = '${backupDir.path}/$backupFileName';

      // تحديث تاريخ آخر نسخة احتياطية
      final prefs = await SharedPreferences.getInstance();
      final dateFormatter = DateFormat('yyyy-MM-dd HH:mm:ss');
      await prefs.setString('last_backup_date', dateFormatter.format(now));

      setState(() {
        _isLoading = false;
        _exportPath = backupPath;
        _lastBackupDate = dateFormatter.format(now);
        _successMessage = 'تم تصدير قاعدة البيانات بنجاح إلى:\n$backupPath';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'فشل في تصدير قاعدة البيانات: ${e.toString()}';
      });
    }
  }

  /// استيراد قاعدة البيانات
  Future<void> _importDatabase() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // اختيار ملف النسخة الاحتياطية
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['db'],
      );

      if (result == null || result.files.isEmpty) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final file = result.files.first;
      final importPath = file.path;

      if (importPath == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'مسار الملف غير صالح';
        });
        return;
      }

      // التحقق من أن الملف موجود
      final importFile = File(importPath);
      if (!importFile.existsSync()) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'ملف النسخة الاحتياطية غير موجود';
        });
        return;
      }

      setState(() {
        _isLoading = false;
        _importPath = importPath;
        _successMessage = 'تم استيراد قاعدة البيانات بنجاح';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'فشل في استيراد قاعدة البيانات: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AkAppBar(
        title: 'تصدير/استيراد البيانات',
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (_errorMessage != null)
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.lightTextSecondary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        _errorMessage!,
                        style: const AppTypography(color: AppColors.errorDark),
                      ),
                    ),
                  if (_successMessage != null)
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.lightTextSecondary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        _successMessage!,
                        style:
                            const AppTypography(color: AppColors.successDark),
                      ),
                    ),
                  const SizedBox(height: 16),
                  _buildExportSection(),
                  const SizedBox(height: 24),
                  _buildImportSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildExportSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تصدير قاعدة البيانات',
              style: AppTypography(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'قم بتصدير قاعدة البيانات الحالية كنسخة احتياطية. يمكنك استخدام هذه النسخة لاستعادة البيانات لاحقًا.',
            ),
            const SizedBox(height: 8),
            if (_lastBackupDate != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Text(
                  'آخر نسخة احتياطية: $_lastBackupDate',
                  style: const AppTypography(fontStyle: FontStyle.italic),
                ),
              ),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: _exportDatabase,
              icon: const Icon(Icons.upload_file),
              label: const Text('تصدير قاعدة البيانات'),
            ),
            if (_exportPath != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'تم التصدير إلى: $_exportPath',
                  style: const AppTypography(fontSize: 12),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildImportSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'استيراد قاعدة البيانات',
              style: AppTypography(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'قم باستيراد نسخة احتياطية من قاعدة البيانات. سيؤدي ذلك إلى استبدال جميع البيانات الحالية.',
            ),
            const SizedBox(height: 16),
            const Text(
              'تحذير: سيتم فقدان جميع البيانات الحالية عند استيراد نسخة احتياطية.',
              style: AppTypography(
                  color: AppColors.lightTextSecondary,
                  fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('تأكيد الاستيراد'),
                    content: const Text(
                      'هل أنت متأكد من استيراد نسخة احتياطية؟ سيتم فقدان جميع البيانات الحالية.',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('إلغاء'),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _importDatabase();
                        },
                        child: const Text('استيراد'),
                      ),
                    ],
                  ),
                );
              },
              icon: const Icon(Icons.download),
              label: const Text('استيراد قاعدة البيانات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.warning,
              ),
            ),
            if (_importPath != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'تم الاستيراد من: $_importPath',
                  style: const AppTypography(fontSize: 12),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
