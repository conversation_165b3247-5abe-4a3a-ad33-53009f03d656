import 'package:flutter/foundation.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/auth/models/user_role.dart';
import '../models/permission_model.dart';
import '../models/user_group.dart';

/// مقدم الصلاحيات
/// يستخدم لإدارة الصلاحيات والأدوار ومجموعات المستخدمين
class PermissionPresenter extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // قوائم البيانات
  List<PermissionModel> _permissions = [];
  List<PermissionModel> _filteredPermissions = [];
  List<UserRole> _roles = []; // تم تغييره من Role إلى UserRole
  List<UserGroup> _userGroups = [];

  // حالة التحميل والخطأ
  bool _isLoading = false;
  String? _errorMessage;

  // الحصول على البيانات
  List<PermissionModel> get permissions => _filteredPermissions;
  List<UserRole> get roles => _roles;

  List<UserGroup> get userGroups => _userGroups;

  // حالة التحميل والخطأ
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  /// تحميل الصلاحيات
  Future<void> loadPermissions() async {
    _setLoading(true);

    try {
      final db = await _databaseHelper.database;

      // الحصول على جميع الصلاحيات غير المحذوفة
      final permissionsData = await db.query(
        'permissions',
        where: 'is_deleted = 0',
        orderBy: 'module ASC, name ASC',
      );

      // تحويل البيانات إلى نماذج
      _permissions =
          permissionsData.map((data) => PermissionModel.fromMap(data)).toList();
      _filteredPermissions = List.from(_permissions);

      AppLogger.info('تم تحميل ${_permissions.length} صلاحية');

      notifyListeners();
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في تحميل الصلاحيات: $e');
      ErrorTracker.captureError(
        'فشل في تحميل الصلاحيات',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل الأدوار
  Future<void> loadRoles() async {
    // تجنب تحميل الأدوار إذا كانت قيد التحميل بالفعل
    if (_isLoading) {
      AppLogger.info('تم تجاهل طلب تحميل الأدوار لأنها قيد التحميل بالفعل');
      return;
    }

    _setLoading(true);

    try {
      final db = await _databaseHelper.database;

      // الحصول على جميع الأدوار غير المحذوفة
      final rolesData = await db.query(
        'roles',
        where: 'is_deleted = 0',
        orderBy: 'name ASC',
      );

      // تحويل البيانات إلى نماذج UserRole
      final newRoles = rolesData.map((data) => UserRole.fromMap(data)).toList();

      // تحديث القائمة فقط إذا كانت هناك تغييرات
      if (_roles.length != newRoles.length ||
          !_areRolesEqual(_roles, newRoles)) {
        _roles = newRoles;
        AppLogger.info('تم تحميل ${_roles.length} دور');
        notifyListeners();
      } else {
        AppLogger.info('لم يتم تحديث الأدوار لأنها لم تتغير');
      }
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في تحميل الأدوار: $e');
      ErrorTracker.captureError(
        'فشل في تحميل الأدوار',
        error: e,
        stackTrace: stackTrace,
      );

      // إذا كانت القائمة فارغة، قم بإضافة أدوار افتراضية
      if (_roles.isEmpty) {
        _roles = [
          UserRole(
            id: 'admin',
            name: 'admin',
            displayName: 'مدير النظام',
            permissions: [],
          ),
          UserRole(
            id: 'manager',
            name: 'manager',
            displayName: 'مدير',
            permissions: [],
          ),
          UserRole(
            id: 'accountant',
            name: 'accountant',
            displayName: 'محاسب',
            permissions: [],
          ),
          UserRole(
            id: 'cashier',
            name: 'cashier',
            displayName: 'أمين صندوق',
            permissions: [],
          ),
          UserRole(
            id: 'user',
            name: 'user',
            displayName: 'مستخدم',
            permissions: [],
          ),
          UserRole(
            id: 'guest',
            name: 'guest',
            displayName: 'زائر',
            permissions: [],
          ),
        ];
        AppLogger.info('تم إضافة أدوار افتراضية بسبب فشل التحميل');
        notifyListeners();
      }
    } finally {
      _setLoading(false);
    }
  }

  /// مقارنة قائمتين من الأدوار للتحقق من تطابقهما
  bool _areRolesEqual(List<UserRole> list1, List<UserRole> list2) {
    if (list1.length != list2.length) return false;

    for (int i = 0; i < list1.length; i++) {
      if (list1[i].id != list2[i].id || list1[i].name != list2[i].name) {
        return false;
      }
    }

    return true;
  }

  /// تحميل مجموعات المستخدمين
  Future<void> loadUserGroups() async {
    _setLoading(true);

    try {
      final db = await _databaseHelper.database;

      // الحصول على جميع مجموعات المستخدمين غير المحذوفة
      final userGroupsData = await db.query(
        'user_groups',
        where: 'is_deleted = 0',
        orderBy: 'name ASC',
      );

      // تحويل البيانات إلى نماذج
      _userGroups =
          userGroupsData.map((data) => UserGroup.fromMap(data)).toList();

      AppLogger.info('تم تحميل ${_userGroups.length} مجموعة مستخدمين');

      notifyListeners();
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في تحميل مجموعات المستخدمين: $e');
      ErrorTracker.captureError(
        'فشل في تحميل مجموعات المستخدمين',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على الوحدات المتاحة
  List<String> getModules() {
    final modules = _filteredPermissions.map((p) => p.module).toSet().toList();
    modules.sort();
    return modules;
  }

  /// الحصول على الصلاحيات حسب الوحدة
  List<PermissionModel> getPermissionsByModule(String module) {
    return _filteredPermissions.where((p) => p.module == module).toList();
  }

  /// الحصول على صلاحيات الدور
  Future<List<PermissionModel>> getRolePermissions(String roleId) async {
    try {
      AppLogger.info('🔍 بدء البحث عن صلاحيات الدور: $roleId');

      // التأكد من تحميل الصلاحيات أولاً
      if (_permissions.isEmpty) {
        AppLogger.info('📥 تحميل الصلاحيات أولاً...');
        await loadPermissions();
      }

      AppLogger.info('📊 عدد الصلاحيات المحملة: ${_permissions.length}');

      final db = await _databaseHelper.database;

      // البحث مباشرة في جدول role_permissions أولاً
      final rolePermissionsData = await db.query(
        'role_permissions',
        columns: ['permission_id'],
        where: 'role_id = ? AND is_deleted = 0',
        whereArgs: [roleId],
      );

      final permissionIds = rolePermissionsData
          .map((data) => data['permission_id'] as String)
          .toList();

      AppLogger.info(
          '📊 تم العثور على ${permissionIds.length} صلاحية للدور $roleId من جدول role_permissions');

      if (permissionIds.isNotEmpty) {
        AppLogger.info(
            '🔗 معرفات الصلاحيات: ${permissionIds.take(5).join(', ')}${permissionIds.length > 5 ? '...' : ''}');
      }

      if (permissionIds.isNotEmpty) {
        // الحصول على تفاصيل الصلاحيات
        final permissionModels = <PermissionModel>[];

        for (final permissionId in permissionIds) {
          // البحث عن الصلاحية في قائمة الصلاحيات المحملة باستخدام ID
          final permission = _permissions.firstWhere(
            (p) => p.id == permissionId,
            orElse: () {
              AppLogger.warning(
                  '⚠️ لم يتم العثور على صلاحية بمعرف: $permissionId في قائمة الصلاحيات المحملة');
              AppLogger.info(
                  '🔍 أول 3 صلاحيات محملة: ${_permissions.take(3).map((p) => '${p.id}:${p.name}').join(', ')}');
              // محاولة البحث في قاعدة البيانات مباشرة
              return PermissionModel(
                id: permissionId,
                name: 'صلاحية غير معروفة',
                module: 'unknown',
                code: 'unknown_$permissionId',
              );
            },
          );

          if (permission.name != 'صلاحية غير معروفة') {
            AppLogger.info(
                '✅ تم العثور على صلاحية: ${permission.name} (${permission.id})');
          }

          permissionModels.add(permission);
        }

        AppLogger.info(
            '✅ تم تحويل ${permissionModels.length} صلاحية إلى نماذج');
        return permissionModels;
      }

      // إذا لم نجد صلاحيات في جدول role_permissions، نتحقق من الدور المحلي
      final role = _roles.firstWhere(
        (r) => r.id == roleId,
        orElse: () => UserRole(
          id: roleId,
          name: 'unknown',
          displayName: 'غير معروف',
          permissions: [],
        ),
      );

      // إذا كان الدور يحتوي على صلاحيات مباشرة، نستخدمها
      if (role.permissions.isNotEmpty) {
        AppLogger.info(
            '📋 تم العثور على ${role.permissions.length} صلاحية مباشرة للدور $roleId');

        // تحويل رموز الصلاحيات إلى نماذج PermissionModel
        final permissionModels = <PermissionModel>[];
        for (final permCode in role.permissions) {
          // البحث عن الصلاحية في قائمة الصلاحيات
          final permission = _permissions.firstWhere(
            (p) => p.code == permCode,
            orElse: () => PermissionModel(
              id: permCode,
              name: permCode,
              module: 'unknown',
              code: permCode,
            ),
          );
          permissionModels.add(permission);
        }
        return permissionModels;
      }

      AppLogger.warning('⚠️ لم يتم العثور على أي صلاحيات للدور $roleId');
      return [];
    } catch (e, stackTrace) {
      AppLogger.error('فشل في الحصول على صلاحيات الدور: $e');
      ErrorTracker.captureError(
        'فشل في الحصول على صلاحيات الدور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'roleId': roleId,
        },
      );
      return [];
    }
  }

  /// تحديث صلاحيات الدور
  Future<bool> updateRolePermissions(
      String roleId, List<String> permissionIds) async {
    _setLoading(true);

    try {
      AppLogger.info('🔄 بدء تحديث صلاحيات الدور في جدول role_permissions');
      AppLogger.info('📊 معرف الدور: $roleId');
      AppLogger.info('📊 عدد الصلاحيات الجديدة: ${permissionIds.length}');

      // استخدام جدول role_permissions دائماً لضمان الاتساق
      final db = await _databaseHelper.database;

      // بدء معاملة لضمان تنفيذ جميع العمليات بنجاح
      await db.transaction((txn) async {
        AppLogger.info('🗑️ حذف جميع الصلاحيات الحالية للدور...');

        // حذف جميع الصلاحيات الحالية للدور (حذف فعلي)
        final deleteResult = await txn.delete(
          'role_permissions',
          where: 'role_id = ?',
          whereArgs: [roleId],
        );

        AppLogger.info('🗑️ تم حذف $deleteResult صلاحية سابقة');

        AppLogger.info('➕ إضافة ${permissionIds.length} صلاحية جديدة...');

        // إضافة الصلاحيات الجديدة
        for (int i = 0; i < permissionIds.length; i++) {
          final permissionId = permissionIds[i];
          final id = '${DateTime.now().millisecondsSinceEpoch}_$i';

          await txn.insert(
            'role_permissions',
            {
              'id': id,
              'role_id': roleId,
              'permission_id': permissionId,
              'created_at': DateTime.now().toIso8601String(),
              'updated_at': DateTime.now().toIso8601String(),
            },
          );

          AppLogger.info('✅ تم إضافة صلاحية: $permissionId');
        }

        AppLogger.info('✅ تم إضافة جميع الصلاحيات الجديدة بنجاح');
      });

      AppLogger.info('✅ تم تحديث صلاحيات الدور بنجاح في جدول role_permissions');

      // تحديث الدور في القائمة المحلية إذا كان موجوداً
      final roleIndex = _roles.indexWhere((r) => r.id == roleId);
      if (roleIndex >= 0) {
        final role = _roles[roleIndex];
        final updatedRole = role.copyWith(permissions: permissionIds);
        _roles[roleIndex] = updatedRole;
        AppLogger.info('✅ تم تحديث الدور في القائمة المحلية');
      }

      // إشعار المستمعين بالتحديث
      notifyListeners();

      return true;
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في تحديث صلاحيات الدور: $e');
      ErrorTracker.captureError(
        'فشل في تحديث صلاحيات الدور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'roleId': roleId,
          'permissionIds': permissionIds,
        },
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تصفية الصلاحيات حسب البحث
  void filterPermissions(String query) {
    if (query.isEmpty) {
      _filteredPermissions = List.from(_permissions);
    } else {
      _filteredPermissions = _permissions.where((permission) {
        final name = permission.name.toLowerCase();
        final description = (permission.description ?? '').toLowerCase();
        final module = permission.module.toLowerCase();
        final code = (permission.code ?? '').toLowerCase();
        final searchQuery = query.toLowerCase();

        return name.contains(searchQuery) ||
            description.contains(searchQuery) ||
            module.contains(searchQuery) ||
            code.contains(searchQuery);
      }).toList();
    }

    notifyListeners();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setErrorMessage(String message) {
    _errorMessage = message;
    AppLogger.error(message);
    notifyListeners();
  }
}
