import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:tajer_plus/core/database/database_schema.dart';

void main() {
  // تهيئة قاعدة بيانات الاختبار
  setUpAll(() {
    // تهيئة sqflite_common_ffi
    sqfliteFfiInit();
    // تعيين مصنع قاعدة البيانات
    databaseFactory = databaseFactoryFfi;
  });

  group('DatabaseSchema Integration Tests', () {
    late Database db;

    // إنشاء قاعدة بيانات مؤقتة قبل كل اختبار
    setUp(() async {
      // فتح قاعدة بيانات في الذاكرة
      db = await openDatabase(
        inMemoryDatabasePath,
        version: 1,
        onCreate: (db, version) async {
          // لا نحتاج لإنشاء الجداول هنا لأننا سنختبر ذلك
        },
      );
    });

    // إغلاق قاعدة البيانات بعد كل اختبار
    tearDown(() async {
      await db.close();
    });

    test('createAllTables should create all tables without errors', () async {
      // تنفيذ إنشاء جميع الجداول
      await DatabaseSchema.createAllTables(db);

      // التحقق من وجود الجداول الرئيسية
      final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE 'android_%'");

      // التحقق من عدد الجداول
      expect(tables.length, greaterThan(20),
          reason: 'يجب أن يكون هناك أكثر من 20 جدول');

      // التحقق من وجود بعض الجداول الرئيسية
      final tableNames = tables.map((t) => t['name'] as String).toList();
      expect(tableNames, contains('users'));
      expect(tableNames, contains('roles'));
      expect(tableNames, contains('permissions'));
      expect(tableNames, contains('role_permissions'));
      expect(tableNames, contains('user_groups'));
      expect(tableNames, contains('products'));
      expect(tableNames, contains('categories'));
      expect(tableNames, contains('units'));
      expect(tableNames, contains('settings'));
    });

    test('createUsersTable should create users table with correct schema',
        () async {
      // إنشاء جدول المستخدمين
      await DatabaseSchema.createUsersTable(db);

      // التحقق من وجود الجدول
      final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='users'");
      expect(tables.length, 1);

      // التحقق من هيكل الجدول
      final columns = await db.rawQuery('PRAGMA table_info(users)');

      // التحقق من وجود الأعمدة الرئيسية
      final columnNames = columns.map((c) => c['name'] as String).toList();
      expect(columnNames, contains('id'));
      expect(columnNames, contains('username'));
      expect(columnNames, contains('email'));
      expect(columnNames, contains('password'));
      expect(columnNames, contains('role_id'));
      expect(columnNames, contains('user_group_id'));
      expect(columnNames, contains('is_active'));
      expect(columnNames, contains('created_at'));
      expect(columnNames, contains('updated_at'));
      expect(columnNames, contains('is_deleted'));
    });

    test('createSettingsTable should create settings table with correct schema',
        () async {
      // إنشاء جدول الإعدادات
      await DatabaseSchema.createSettingsTable(db);

      // التحقق من وجود الجدول
      final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='settings'");
      expect(tables.length, 1);

      // التحقق من هيكل الجدول
      final columns = await db.rawQuery('PRAGMA table_info(settings)');

      // التحقق من وجود الأعمدة الرئيسية
      final columnNames = columns.map((c) => c['name'] as String).toList();
      expect(columnNames, contains('id'));
      expect(columnNames, contains('key'));
      expect(columnNames, contains('value'));
      expect(columnNames, contains('description'));
      expect(columnNames, contains('created_at'));
      expect(columnNames, contains('updated_at'));
      expect(columnNames, contains('is_deleted'));
    });
  });
}
