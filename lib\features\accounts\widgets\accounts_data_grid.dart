import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart' as intl;

import '../../../core/theme/index.dart';

class AccountsDataGrid extends StatefulWidget {
  final List<Map<String, dynamic>> accounts;
  final Function(Map<String, dynamic>) onEdit;
  final Function(Map<String, dynamic>) onDelete;
  final Function(Map<String, dynamic>) onPrint;

  const AccountsDataGrid({
    Key? key,
    required this.accounts,
    required this.onEdit,
    required this.onDelete,
    required this.onPrint,
  }) : super(key: key);

  @override
  State<AccountsDataGrid> createState() => _AccountsDataGridState();
}

class _AccountsDataGridState extends State<AccountsDataGrid> {
  late AccountsDataSource _accountsDataSource;
  late DataGridController _dataGridController;

  @override
  void initState() {
    super.initState();
    _accountsDataSource = AccountsDataSource(
      accounts: widget.accounts,
      onEdit: widget.onEdit,
      onDelete: widget.onDelete,
      onPrint: widget.onPrint,
    );
    _dataGridController = DataGridController();
  }

  @override
  void didUpdateWidget(AccountsDataGrid oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.accounts != widget.accounts) {
      _accountsDataSource = AccountsDataSource(
        accounts: widget.accounts,
        onEdit: widget.onEdit,
        onDelete: widget.onDelete,
        onPrint: widget.onPrint,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.lightBorder, width: 1),
            borderRadius: BorderRadius.circular(8),
            boxShadow: const [
              BoxShadow(
                color: AppColors.lightShadow,
                blurRadius: 5,
                spreadRadius: 1,
                offset: Offset(0, 2),
              ),
            ],
          ),
          margin: const EdgeInsets.all(8),
          child: SfDataGrid(
            source: _accountsDataSource,
            controller: _dataGridController,
            allowSorting: true,
            allowFiltering: true,
            allowColumnsResizing: true,
            allowPullToRefresh: true,
            frozenRowsCount: 1, // تثبيت صف العناوين
            frozenColumnsCount: 1, // تثبيت عمود الإجراءات
            columnWidthMode:
                ColumnWidthMode.fill, // تعديل عرض العمود ليملأ المساحة المتاحة
            gridLinesVisibility:
                GridLinesVisibility.both, // إظهار جميع الخطوط الفاصلة
            headerGridLinesVisibility:
                GridLinesVisibility.both, // إظهار جميع الخطوط الفاصلة في الرأس
            selectionMode: SelectionMode.single,
            navigationMode: GridNavigationMode.row,
            columns: _buildColumns(),
            rowHeight: 65,
            headerRowHeight: 55,
            isScrollbarAlwaysShown: true, // إظهار شريط التمرير دائمًا
            columnResizeMode: ColumnResizeMode
                .onResizeEnd, // تغيير حجم العمود عند الانتهاء من السحب
            tableSummaryRows: [
              GridTableSummaryRow(
                showSummaryInRow: false,
                title: 'إجمالي الحسابات: {count}',
                columns: [
                  const GridSummaryColumn(
                    name: 'count',
                    columnName: 'id',
                    summaryType: GridSummaryType.count,
                  ),
                ],
                position: GridTableSummaryRowPosition.bottom,
              ),
            ],
            // تخصيص ألوان الجدول
          ),
        ),
        // زر عرض كامل الشاشة
        Positioned(
          top: 12,
          right: 12,
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.onPrimary.withValues(
                  alpha: 0.8), // استخدام withValues بدلاً من withOpacity
              borderRadius: BorderRadius.circular(4),
              boxShadow: [
                BoxShadow(
                  color: AppColors.lightTextSecondary.withValues(
                      alpha: 0.3), // استخدام withValues بدلاً من withOpacity
                  blurRadius: 2,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: IconButton(
              icon: Icon(
                Icons.fullscreen,
                color: theme.primaryColor,
                size: 20,
              ),
              tooltip: 'عرض كامل الشاشة',
              onPressed: () {
                _toggleFullScreen(context);
              },
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // دالة للتبديل بين وضع ملء الشاشة والوضع العادي
  void _toggleFullScreen(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        insetPadding: EdgeInsets.zero, // إزالة الهوامش
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          color: AppColors.onPrimary,
          child: Column(
            children: [
              // شريط العنوان
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                color: AppColors.errorLight,
                child: Row(
                  children: [
                    const Text(
                      'عرض جدول الحسابات',
                      style: AppTypography(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.of(context).pop(),
                      tooltip: 'إغلاق',
                    ),
                  ],
                ),
              ),
              // الجدول
              Expanded(
                child: SfDataGrid(
                  source: _accountsDataSource,
                  controller: _dataGridController,
                  allowSorting: true,
                  allowFiltering: true,
                  allowColumnsResizing: true,
                  allowPullToRefresh: true,
                  frozenRowsCount: 1, // تثبيت صف العناوين
                  frozenColumnsCount: 1, // تثبيت عمود الإجراءات
                  columnWidthMode: ColumnWidthMode.fill,
                  gridLinesVisibility: GridLinesVisibility.both,
                  headerGridLinesVisibility: GridLinesVisibility.both,
                  selectionMode: SelectionMode.single,
                  navigationMode: GridNavigationMode.row,
                  columns: _buildColumns(),
                  rowHeight: 65,
                  headerRowHeight: 55,
                  isScrollbarAlwaysShown: true,
                  columnResizeMode: ColumnResizeMode.onResizeEnd,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<GridColumn> _buildColumns() {
    const Color headerColor = AppColors.error;
    const Color headerBgColor = AppColors.errorLight;

    return [
      // 1. الإجراءات (أول عمود وثابت)
      GridColumn(
        columnName: 'actions',
        width: 70, // عرض ثابت
        allowSorting: false,
        allowFiltering: false,
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          decoration: const BoxDecoration(
            color: headerBgColor,
            border: Border(
              bottom: BorderSide(color: headerColor, width: 2),
              right: BorderSide(color: headerColor, width: 2),
            ),
          ),
          child: const Text(
            '',
            style: AppTypography(
              fontWeight: FontWeight.bold,
              color: Colors.transparent,
            ),
          ),
        ),
      ),

      // 2. الرقم التسلسلي
      GridColumn(
        columnName: 'id',
        width: 60, // عرض ثابت
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          decoration: const BoxDecoration(
            color: headerBgColor,
            border: Border(
              bottom: BorderSide(color: headerColor, width: 2),
            ),
          ),
          child: const Text(
            '#',
            style: AppTypography(
              fontWeight: FontWeight.bold,
              color: headerColor,
              fontSize: 16,
            ),
          ),
        ),
      ),

      // 3. اسم الحساب
      GridColumn(
        columnName: 'name',
        width: 180,
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.centerRight,
          decoration: const BoxDecoration(
            color: headerBgColor,
            border: Border(
              bottom: BorderSide(color: headerColor, width: 2),
            ),
          ),
          child: const Text(
            'اسم الحساب',
            style: AppTypography(
              fontWeight: FontWeight.bold,
              color: headerColor,
              fontSize: 14,
            ),
          ),
        ),
      ),

      // 4. الحساب الأب
      GridColumn(
        columnName: 'parent',
        width: 180,
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.centerRight,
          decoration: const BoxDecoration(
            color: headerBgColor,
            border: Border(
              bottom: BorderSide(color: headerColor, width: 2),
            ),
          ),
          child: const Text(
            'الحساب الأب',
            style: AppTypography(
              fontWeight: FontWeight.bold,
              color: headerColor,
              fontSize: 14,
            ),
          ),
        ),
      ),

      // 5. الكود
      GridColumn(
        columnName: 'code',
        width: 120,
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.centerRight,
          decoration: const BoxDecoration(
            color: headerBgColor,
            border: Border(
              bottom: BorderSide(color: headerColor, width: 2),
            ),
          ),
          child: const Text(
            'الكود',
            style: AppTypography(
              fontWeight: FontWeight.bold,
              color: headerColor,
              fontSize: 14,
            ),
          ),
        ),
      ),

      // 6. النوع
      GridColumn(
        columnName: 'type',
        width: 140,
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.centerRight,
          decoration: const BoxDecoration(
            color: headerBgColor,
            border: Border(
              bottom: BorderSide(color: headerColor, width: 2),
            ),
          ),
          child: const Text(
            'النوع',
            style: AppTypography(
              fontWeight: FontWeight.bold,
              color: headerColor,
              fontSize: 14,
            ),
          ),
        ),
      ),

      // 7. الرصيد
      GridColumn(
        columnName: 'balance',
        width: 140,
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.centerRight,
          decoration: const BoxDecoration(
            color: headerBgColor,
            border: Border(
              bottom: BorderSide(color: headerColor, width: 2),
            ),
          ),
          child: const Text(
            'الرصيد',
            style: AppTypography(
              fontWeight: FontWeight.bold,
              color: headerColor,
              fontSize: 14,
            ),
          ),
        ),
      ),

      // 8. الهاتف
      GridColumn(
        columnName: 'phone',
        width: 140,
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.centerRight,
          decoration: const BoxDecoration(
            color: headerBgColor,
            border: Border(
              bottom: BorderSide(color: headerColor, width: 2),
            ),
          ),
          child: const Text(
            'الهاتف',
            style: AppTypography(
              fontWeight: FontWeight.bold,
              color: headerColor,
              fontSize: 14,
            ),
          ),
        ),
      ),

      // 9. الملاحظات
      GridColumn(
        columnName: 'notes',
        width: 180,
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.centerRight,
          decoration: const BoxDecoration(
            color: headerBgColor,
            border: Border(
              bottom: BorderSide(color: headerColor, width: 2),
            ),
          ),
          child: const Text(
            'الملاحظات',
            style: AppTypography(
              fontWeight: FontWeight.bold,
              color: headerColor,
              fontSize: 14,
            ),
          ),
        ),
      ),

      // 10. الحالة
      GridColumn(
        columnName: 'status',
        width: 100,
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          decoration: const BoxDecoration(
            color: headerBgColor,
            border: Border(
              bottom: BorderSide(color: headerColor, width: 2),
              left: BorderSide(color: headerColor, width: 2),
            ),
          ),
          child: const Text(
            'الحالة',
            style: AppTypography(
              fontWeight: FontWeight.bold,
              color: headerColor,
              fontSize: 14,
            ),
          ),
        ),
      ),
    ];
  }
}

class AccountsDataSource extends DataGridSource {
  final List<Map<String, dynamic>> accounts;
  final Function(Map<String, dynamic>) onEdit;
  final Function(Map<String, dynamic>) onDelete;
  final Function(Map<String, dynamic>) onPrint;

  List<DataGridRow> _accountsData = [];

  AccountsDataSource({
    required this.accounts,
    required this.onEdit,
    required this.onDelete,
    required this.onPrint,
  }) {
    _accountsData = accounts.asMap().entries.map<DataGridRow>((entry) {
      final int index = entry.key;
      final Map<String, dynamic> account = entry.value;
      return DataGridRow(cells: [
        // 1. الإجراءات (أول عمود)
        DataGridCell<Map<String, dynamic>>(
            columnName: 'actions', value: account),

        // 2. الرقم التسلسلي
        DataGridCell<int>(columnName: 'id', value: index + 1),

        // 3. اسم الحساب
        DataGridCell<String>(
            columnName: 'name', value: account['name'] as String? ?? ''),

        // 4. الحساب الأب
        DataGridCell<Map<String, dynamic>>(columnName: 'parent', value: {
          'parent_name': account['parent_name'] as String? ?? '',
          'parent_code': account['parent_code'] as String? ?? '',
        }),

        // 5. الكود
        DataGridCell<String>(
            columnName: 'code', value: account['code'] as String? ?? ''),

        // 6. النوع
        DataGridCell<String>(
            columnName: 'type', value: account['type'] as String? ?? ''),

        // 7. الرصيد
        DataGridCell<double>(
            columnName: 'balance',
            value: account['balance'] is int
                ? (account['balance'] as int).toDouble()
                : account['balance'] as double? ?? 0.0),

        // 8. الهاتف
        DataGridCell<String>(
            columnName: 'phone', value: account['phone'] as String? ?? ''),

        // 9. الملاحظات
        DataGridCell<String>(
            columnName: 'notes', value: account['notes'] as String? ?? ''),

        // 10. الحالة
        DataGridCell<bool>(
            columnName: 'status', value: account['is_active'] == 1),
      ]);
    }).toList();
  }

  @override
  List<DataGridRow> get rows => _accountsData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    // Obtener el índice de la fila para alternar colores
    final int rowIndex = _accountsData.indexOf(row);
    final bool isEvenRow = rowIndex % 2 == 0;
    final Color rowColor = isEvenRow
        ? AppColors.onPrimary
        : AppColors.indigo.withValues(alpha: 0.02);

    return DataGridRowAdapter(
      color: rowColor,
      cells: row.getCells().map<Widget>((dataGridCell) {
        switch (dataGridCell.columnName) {
          case 'actions':
            // الإجراءات (قائمة منسدلة)
            final Map<String, dynamic> account = dataGridCell.value;
            final String phone = account['phone'] as String? ?? '';

            return Container(
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.center,
              child: PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert,
                    color: AppColors.lightTextSecondary),
                tooltip: 'الإجراءات',
                onSelected: (String value) {
                  switch (value) {
                    case 'edit':
                      onEdit(account);
                      break;
                    case 'delete':
                      onDelete(account);
                      break;
                    case 'print':
                      onPrint(account);
                      break;
                    case 'call':
                      if (phone.isNotEmpty) {
                        _makePhoneCall(phone);
                      }
                      break;
                  }
                },
                itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                  const PopupMenuItem<String>(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, color: AppColors.info, size: 20),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem<String>(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: AppColors.error, size: 20),
                        SizedBox(width: 8),
                        Text('حذف'),
                      ],
                    ),
                  ),
                  const PopupMenuItem<String>(
                    value: 'print',
                    child: Row(
                      children: [
                        Icon(Icons.print, color: AppColors.accent, size: 20),
                        SizedBox(width: 8),
                        Text('طباعة'),
                      ],
                    ),
                  ),
                  if (phone.isNotEmpty)
                    const PopupMenuItem<String>(
                      value: 'call',
                      child: Row(
                        children: [
                          Icon(Icons.phone, color: AppColors.success, size: 20),
                          SizedBox(width: 8),
                          Text('اتصال بالرقم'),
                        ],
                      ),
                    ),
                ],
              ),
            );

          case 'id':
            // الرقم التسلسلي
            return Container(
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.center,
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AppColors.lightSurfaceVariant,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  dataGridCell.value.toString(),
                  style: const AppTypography(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            );

          case 'name':
            // اسم الحساب
            return Container(
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.centerRight,
              child: Text(
                dataGridCell.value.toString(),
                style: const AppTypography(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            );

          case 'parent':
            // الحساب الأب
            final Map<String, dynamic> parentData = dataGridCell.value;
            final String parentName = parentData['parent_name'];
            final String parentCode = parentData['parent_code'];

            return Container(
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.centerRight,
              child: parentName.isNotEmpty
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.subdirectory_arrow_right,
                            size: 14, color: AppColors.lightTextSecondary),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            parentName,
                            overflow: TextOverflow.ellipsis,
                            style: const AppTypography(
                              color: AppColors.lightTextSecondary,
                            ),
                          ),
                        ),
                        if (parentCode.isNotEmpty) ...[
                          const SizedBox(width: 4),
                          Text(
                            '#$parentCode',
                            style: const AppTypography(
                              fontSize: 10,
                              color: AppColors.lightTextSecondary,
                            ),
                          ),
                        ],
                      ],
                    )
                  : const Text('-',
                      style:
                          AppTypography(color: AppColors.lightTextSecondary)),
            );

          case 'code':
            // الكود
            return Container(
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.centerRight,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                  border:
                      Border.all(color: AppColors.info.withValues(alpha: 0.3)),
                ),
                child: Text(
                  dataGridCell.value.toString(),
                  style: const AppTypography(color: AppColors.info),
                ),
              ),
            );

          case 'type':
            // النوع - نعرض فقط رئيسي أو فرعي
            final String type = dataGridCell.value;
            final bool isMainAccount = type == 'main';
            final Color typeColor =
                isMainAccount ? AppColors.accent : AppColors.lightTextSecondary;
            final String displayText = isMainAccount ? 'رئيسي' : 'فرعي';
            final IconData typeIcon = isMainAccount
                ? Icons.account_tree
                : Icons.subdirectory_arrow_right;

            return Container(
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.centerRight,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: typeColor.withValues(alpha: 0.12),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: typeColor.withValues(alpha: 0.4)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      typeIcon,
                      size: 16,
                      color: typeColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      displayText,
                      style: AppTypography(
                        color: typeColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );

          case 'balance':
            // الرصيد
            final double balance = dataGridCell.value;
            final bool isPositive = balance >= 0;
            final Color balanceColor =
                isPositive ? AppColors.success : AppColors.error;

            return Container(
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.centerRight,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: balanceColor.withValues(alpha: 0.08),
                  borderRadius: BorderRadius.circular(4),
                  border:
                      Border.all(color: balanceColor.withValues(alpha: 0.2)),
                ),
                child: Text(
                  _formatBalance(balance),
                  style: AppTypography(
                    fontWeight: FontWeight.bold,
                    color: isPositive
                        ? AppColors.successDark
                        : AppColors.errorDark,
                  ),
                ),
              ),
            );

          case 'phone':
            // الهاتف
            final String phone = dataGridCell.value;
            return Container(
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.centerRight,
              child: phone.isNotEmpty
                  ? InkWell(
                      onTap: () => _makePhoneCall(phone),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.phone,
                              size: 16, color: AppColors.info),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              phone,
                              style: const AppTypography(
                                color: AppColors.info,
                                decoration: TextDecoration.underline,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    )
                  : const Text('-',
                      style:
                          AppTypography(color: AppColors.lightTextSecondary)),
            );

          case 'notes':
            // الملاحظات
            final String notes = dataGridCell.value;
            return Container(
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.centerRight,
              child: notes.isNotEmpty
                  ? Tooltip(
                      message: notes,
                      child: Text(
                        notes,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        style: const AppTypography(
                            color: AppColors.lightTextSecondary),
                      ),
                    )
                  : const Text('-',
                      style:
                          AppTypography(color: AppColors.lightTextSecondary)),
            );

          case 'status':
            // الحالة
            final bool isActive = dataGridCell.value;
            return Container(
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.center,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isActive
                      ? AppColors.success.withValues(alpha: 0.12)
                      : AppColors.error.withValues(alpha: 0.12),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isActive ? Icons.check_circle : Icons.cancel,
                      size: 16,
                      color: isActive ? AppColors.success : AppColors.error,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      isActive ? 'نشط' : 'غير نشط',
                      style: AppTypography(
                        color: isActive
                            ? AppColors.successDark
                            : AppColors.errorDark,
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            );

          default:
            return Container(
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.center,
              child: Text(
                dataGridCell.value.toString(),
              ),
            );
        }
      }).toList(),
    );
  }

  // دالة لفتح مكالمة هاتفية
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      throw 'لا يمكن الاتصال بالرقم $phoneNumber';
    }
  }

  String _formatBalance(double balance) {
    // استخدام تنسيق العملة
    final formatter = intl.NumberFormat.currency(
      symbol: '', // بدون رمز العملة
      decimalDigits: 2,
    );

    return formatter.format(balance);
  }
}
