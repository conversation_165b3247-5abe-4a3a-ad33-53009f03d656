import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../theme/index.dart';

// ═══════════════════════════════════════════════════════════════════════════════
// 📊 نظام الرسوم البيانية الموحد والشامل (AK Charts System)
// ═══════════════════════════════════════════════════════════════════════════════

/// نظام شامل وموحد لجميع الرسوم البيانية في تطبيق تاجر بلس
///
/// **المميزات:**
/// - تصميم موحد ومتناسق لجميع الرسوم البيانية
/// - دعم كامل للوضع المظلم/الفاتح باستخدام `Theme.of(context).brightness`
/// - عدم وجود قيم صريحة - استخدام `AppColors.*`, `AppDimensions.*`, `AppTypography.*` فقط
/// - تحميل كسول للرسوم البيانية المعقدة
/// - تعليقات شاملة باللغة العربية
/// - أحجام متعددة قابلة للتخصيص
/// - دعم التفاعل والرسوم المتحركة
/// - رسوم بيانية متخصصة للمشروع التجاري اليمني

// ───────────────────────────────────────────────────────────────────────────────
// ● الأنواع والثوابت
// ───────────────────────────────────────────────────────────────────────────────

/// أنواع الرسوم البيانية المتاحة
enum AkChartType {
  /// رسم بياني خطي
  line,

  /// رسم بياني بالأعمدة
  bar,

  /// رسم بياني دائري
  pie,

  /// رسم بياني مساحي
  area,

  /// رسم بياني نقطي
  scatter,

  /// رسم بياني مختلط
  mixed,
}

/// أحجام الرسوم البيانية المتاحة
enum AkChartSize {
  /// صغير - للبطاقات والمعاينات
  small,

  /// متوسط - للاستخدام العادي
  medium,

  /// كبير - للتقارير المفصلة
  large,

  /// كبير جداً - للعرض الكامل
  extraLarge,
}

/// أنماط الرسوم البيانية
enum AkChartStyle {
  /// نمط بسيط
  simple,

  /// نمط متقدم مع تفاصيل
  detailed,

  /// نمط تفاعلي
  interactive,

  /// نمط احترافي للتقارير
  professional,
}

// ───────────────────────────────────────────────────────────────────────────────
// ● نموذج البيانات
// ───────────────────────────────────────────────────────────────────────────────

/// نموذج نقطة البيانات للرسوم البيانية
class AkChartDataPoint {
  /// القيمة على المحور السيني
  final double x;

  /// القيمة على المحور الصادي
  final double y;

  /// تسمية النقطة
  final String? label;

  /// لون النقطة
  final Color? color;

  /// معلومات إضافية
  final Map<String, dynamic>? metadata;

  const AkChartDataPoint({
    required this.x,
    required this.y,
    this.label,
    this.color,
    this.metadata,
  });
}

/// نموذج سلسلة البيانات للرسوم البيانية
class AkChartSeries {
  /// اسم السلسلة
  final String name;

  /// نقاط البيانات
  final List<AkChartDataPoint> data;

  /// لون السلسلة
  final Color? color;

  /// هل السلسلة مرئية
  final bool isVisible;

  /// نوع الخط (للرسوم الخطية)
  final bool isCurved;

  const AkChartSeries({
    required this.name,
    required this.data,
    this.color,
    this.isVisible = true,
    this.isCurved = false,
  });
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📈 القسم الأول: الرسم البياني الخطي (AkLineChart)
// ═══════════════════════════════════════════════════════════════════════════════

/// رسم بياني خطي موحد مع تصميم متناسق
///
/// **المميزات:**
/// - خطوط متعددة مع ألوان مختلفة
/// - دعم الرسوم المتحركة
/// - تفاعل مع النقاط
/// - تخصيص الألوان والأحجام
/// - دعم الوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkLineChart(
///   series: [
///     AkChartSeries(
///       name: 'المبيعات',
///       data: salesData,
///       color: AppColors.success,
///     ),
///   ],
///   title: 'مبيعات الشهر',
///   size: AkChartSize.medium,
/// )
/// ```
class AkLineChart extends StatefulWidget {
  /// سلاسل البيانات
  final List<AkChartSeries> series;

  /// عنوان الرسم البياني
  final String? title;

  /// حجم الرسم البياني
  final AkChartSize size;

  /// نمط الرسم البياني
  final AkChartStyle style;

  /// هل يتم عرض الشبكة
  final bool showGrid;

  /// هل يتم عرض التسميات
  final bool showLabels;

  /// هل يتم عرض الأسطورة
  final bool showLegend;

  /// دالة تنفذ عند النقر على نقطة
  final Function(AkChartDataPoint)? onPointTap;

  /// ألوان مخصصة
  final List<Color>? customColors;

  const AkLineChart({
    super.key,
    required this.series,
    this.title,
    this.size = AkChartSize.medium,
    this.style = AkChartStyle.simple,
    this.showGrid = true,
    this.showLabels = true,
    this.showLegend = true,
    this.onPointTap,
    this.customColors,
  });

  @override
  State<AkLineChart> createState() => _AkLineChartState();
}

class _AkLineChartState extends State<AkLineChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// الحصول على ارتفاع الرسم البياني حسب الحجم
  double _getChartHeight() {
    switch (widget.size) {
      case AkChartSize.small:
        return 150;
      case AkChartSize.medium:
        return 250;
      case AkChartSize.large:
        return 350;
      case AkChartSize.extraLarge:
        return 450;
    }
  }

  /// الحصول على ألوان افتراضية للسلاسل
  List<Color> _getDefaultColors() {
    return widget.customColors ??
        [
          AppColors.primary,
          AppColors.success,
          AppColors.warning,
          AppColors.error,
          AppColors.info,
        ];
  }

  /// بناء خطوط البيانات مع التحميل الكسول
  List<LineChartBarData> _buildLineChartBarData() {
    final colors = _getDefaultColors();

    return widget.series
        .asMap()
        .entries
        .map((entry) {
          final index = entry.key;
          final series = entry.value;

          if (!series.isVisible) return null;

          return LineChartBarData(
            spots:
                series.data.map((point) => FlSpot(point.x, point.y)).toList(),
            color: series.color ?? colors[index % colors.length],
            barWidth: widget.style == AkChartStyle.simple ? 2 : 3,
            isStrokeCapRound: true,
            isCurved: series.isCurved,
            dotData: FlDotData(
              show: widget.style != AkChartStyle.simple,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: barData.color ?? AppColors.primary,
                  strokeWidth: 2,
                  strokeColor: AppColors.lightBackground,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: widget.style == AkChartStyle.detailed,
              color: (series.color ?? colors[index % colors.length])
                  .withValues(alpha: 0.1),
            ),
          );
        })
        .where((data) => data != null)
        .cast<LineChartBarData>()
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          height: _getChartHeight(),
          padding: EdgeInsets.all(AppDimensions.defaultMargin),
          decoration: BoxDecoration(
            color: isDark ? AppColors.darkSurface : AppColors.lightSurface,
            borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
            boxShadow: [
              BoxShadow(
                color: AppColors.lightTextSecondary.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان
              if (widget.title != null) ...[
                Text(
                  widget.title!,
                  style: AppTypography.createCustomStyle(
                    fontSize: AppTypography.fontSizeLarge,
                    fontWeight: AppTypography.weightBold,
                    color: isDark
                        ? AppColors.darkTextPrimary
                        : AppColors.lightTextPrimary,
                  ),
                ),
                SizedBox(height: AppDimensions.smallMargin),
              ],

              // الرسم البياني
              Expanded(
                child: LineChart(
                  LineChartData(
                    lineBarsData: _buildLineChartBarData(),
                    titlesData: FlTitlesData(
                      show: widget.showLabels,
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 40,
                          getTitlesWidget: (value, meta) {
                            return Text(
                              value.toInt().toString(),
                              style: AppTypography.createCustomStyle(
                                fontSize: AppTypography.fontSizeSmall,
                                color: AppColors.lightTextSecondary,
                              ),
                            );
                          },
                        ),
                      ),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 30,
                          getTitlesWidget: (value, meta) {
                            return Text(
                              value.toInt().toString(),
                              style: AppTypography.createCustomStyle(
                                fontSize: AppTypography.fontSizeSmall,
                                color: AppColors.lightTextSecondary,
                              ),
                            );
                          },
                        ),
                      ),
                      topTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false)),
                      rightTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false)),
                    ),
                    gridData: FlGridData(
                      show: widget.showGrid,
                      drawVerticalLine: true,
                      drawHorizontalLine: true,
                      getDrawingHorizontalLine: (value) {
                        return FlLine(
                          color: AppColors.lightTextSecondary
                              .withValues(alpha: 0.2),
                          strokeWidth: 1,
                        );
                      },
                      getDrawingVerticalLine: (value) {
                        return FlLine(
                          color: AppColors.lightTextSecondary
                              .withValues(alpha: 0.2),
                          strokeWidth: 1,
                        );
                      },
                    ),
                    borderData: FlBorderData(
                      show: true,
                      border: Border.all(
                        color:
                            AppColors.lightTextSecondary.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    lineTouchData: LineTouchData(
                      enabled: widget.onPointTap != null,
                      touchCallback: (event, response) {
                        if (widget.onPointTap != null &&
                            response?.lineBarSpots != null) {
                          final spot = response!.lineBarSpots!.first;
                          final point = AkChartDataPoint(
                            x: spot.x,
                            y: spot.y,
                          );
                          widget.onPointTap!(point);
                        }
                      },
                    ),
                  ),
                ),
              ),

              // الأسطورة
              if (widget.showLegend && widget.series.length > 1) ...[
                SizedBox(height: AppDimensions.smallMargin),
                Wrap(
                  spacing: AppDimensions.defaultMargin,
                  children: widget.series.asMap().entries.map((entry) {
                    final index = entry.key;
                    final series = entry.value;
                    final colors = _getDefaultColors();

                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color:
                                series.color ?? colors[index % colors.length],
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: AppDimensions.tinySpacing),
                        Text(
                          series.name,
                          style: AppTypography.createCustomStyle(
                            fontSize: AppTypography.fontSizeSmall,
                            color: AppColors.lightTextSecondary,
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📊 القسم الثاني: الرسم البياني بالأعمدة (AkBarChart)
// ═══════════════════════════════════════════════════════════════════════════════

/// رسم بياني بالأعمدة موحد مع تصميم متناسق
class AkBarChart extends StatefulWidget {
  /// بيانات الأعمدة
  final List<AkChartDataPoint> data;

  /// عنوان الرسم البياني
  final String? title;

  /// حجم الرسم البياني
  final AkChartSize size;

  /// ألوان الأعمدة
  final List<Color>? colors;

  /// هل يتم عرض القيم على الأعمدة
  final bool showValues;

  const AkBarChart({
    super.key,
    required this.data,
    this.title,
    this.size = AkChartSize.medium,
    this.colors,
    this.showValues = true,
  });

  @override
  State<AkBarChart> createState() => _AkBarChartState();
}

class _AkBarChartState extends State<AkBarChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  double _getChartHeight() {
    switch (widget.size) {
      case AkChartSize.small:
        return 150;
      case AkChartSize.medium:
        return 250;
      case AkChartSize.large:
        return 350;
      case AkChartSize.extraLarge:
        return 450;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final defaultColors = [
      AppColors.primary,
      AppColors.success,
      AppColors.warning,
      AppColors.error,
      AppColors.info,
    ];

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          height: _getChartHeight(),
          padding: EdgeInsets.all(AppDimensions.defaultMargin),
          decoration: BoxDecoration(
            color: isDark ? AppColors.darkSurface : AppColors.lightSurface,
            borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
            boxShadow: [
              BoxShadow(
                color: AppColors.lightTextSecondary.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.title != null) ...[
                Text(
                  widget.title!,
                  style: AppTypography.createCustomStyle(
                    fontSize: AppTypography.fontSizeLarge,
                    fontWeight: AppTypography.weightBold,
                    color: isDark
                        ? AppColors.darkTextPrimary
                        : AppColors.lightTextPrimary,
                  ),
                ),
                SizedBox(height: AppDimensions.smallMargin),
              ],
              Expanded(
                child: BarChart(
                  BarChartData(
                    barGroups: widget.data.asMap().entries.map((entry) {
                      final index = entry.key;
                      final point = entry.value;
                      final colors = widget.colors ?? defaultColors;

                      return BarChartGroupData(
                        x: index,
                        barRods: [
                          BarChartRodData(
                            toY: point.y * _animation.value,
                            color: point.color ?? colors[index % colors.length],
                            width: 20,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ],
                      );
                    }).toList(),
                    titlesData: FlTitlesData(
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 40,
                          getTitlesWidget: (value, meta) {
                            return Text(
                              value.toInt().toString(),
                              style: AppTypography.createCustomStyle(
                                fontSize: AppTypography.fontSizeSmall,
                                color: AppColors.lightTextSecondary,
                              ),
                            );
                          },
                        ),
                      ),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 30,
                          getTitlesWidget: (value, meta) {
                            final index = value.toInt();
                            if (index >= 0 && index < widget.data.length) {
                              return Text(
                                widget.data[index].label ?? index.toString(),
                                style: AppTypography.createCustomStyle(
                                  fontSize: AppTypography.fontSizeSmall,
                                  color: AppColors.lightTextSecondary,
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                      ),
                      topTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false)),
                      rightTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false)),
                    ),
                    gridData: FlGridData(
                      show: true,
                      drawVerticalLine: false,
                      getDrawingHorizontalLine: (value) {
                        return FlLine(
                          color: AppColors.lightTextSecondary
                              .withValues(alpha: 0.2),
                          strokeWidth: 1,
                        );
                      },
                    ),
                    borderData: FlBorderData(
                      show: true,
                      border: Border.all(
                        color:
                            AppColors.lightTextSecondary.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🥧 القسم الثالث: الرسم البياني الدائري (AkPieChart)
// ═══════════════════════════════════════════════════════════════════════════════

/// رسم بياني دائري موحد مع تصميم متناسق
class AkPieChart extends StatefulWidget {
  /// بيانات القطاعات
  final List<AkChartDataPoint> data;

  /// عنوان الرسم البياني
  final String? title;

  /// حجم الرسم البياني
  final AkChartSize size;

  /// ألوان القطاعات
  final List<Color>? colors;

  /// هل يتم عرض النسب المئوية
  final bool showPercentages;

  /// هل يتم عرض الأسطورة
  final bool showLegend;

  const AkPieChart({
    super.key,
    required this.data,
    this.title,
    this.size = AkChartSize.medium,
    this.colors,
    this.showPercentages = true,
    this.showLegend = true,
  });

  @override
  State<AkPieChart> createState() => _AkPieChartState();
}

class _AkPieChartState extends State<AkPieChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  int touchedIndex = -1;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  double _getChartHeight() {
    switch (widget.size) {
      case AkChartSize.small:
        return 200;
      case AkChartSize.medium:
        return 300;
      case AkChartSize.large:
        return 400;
      case AkChartSize.extraLarge:
        return 500;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final defaultColors = [
      AppColors.primary,
      AppColors.success,
      AppColors.warning,
      AppColors.error,
      AppColors.info,
    ];

    final total = widget.data.fold<double>(0, (sum, point) => sum + point.y);

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          height: _getChartHeight(),
          padding: EdgeInsets.all(AppDimensions.defaultMargin),
          decoration: BoxDecoration(
            color: isDark ? AppColors.darkSurface : AppColors.lightSurface,
            borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
            boxShadow: [
              BoxShadow(
                color: AppColors.lightTextSecondary.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.title != null) ...[
                Text(
                  widget.title!,
                  style: AppTypography.createCustomStyle(
                    fontSize: AppTypography.fontSizeLarge,
                    fontWeight: AppTypography.weightBold,
                    color: isDark
                        ? AppColors.darkTextPrimary
                        : AppColors.lightTextPrimary,
                  ),
                ),
                SizedBox(height: AppDimensions.smallMargin),
              ],
              Expanded(
                child: Row(
                  children: [
                    // الرسم البياني الدائري
                    Expanded(
                      flex: 2,
                      child: PieChart(
                        PieChartData(
                          sections: widget.data.asMap().entries.map((entry) {
                            final index = entry.key;
                            final point = entry.value;
                            final colors = widget.colors ?? defaultColors;
                            final isTouched = index == touchedIndex;
                            final radius = isTouched ? 60.0 : 50.0;
                            final percentage = (point.y / total * 100);

                            return PieChartSectionData(
                              value: point.y * _animation.value,
                              color:
                                  point.color ?? colors[index % colors.length],
                              radius: radius,
                              title: widget.showPercentages
                                  ? '${percentage.toStringAsFixed(1)}%'
                                  : '',
                              titleStyle: AppTypography.createCustomStyle(
                                fontSize: AppTypography.fontSizeSmall,
                                fontWeight: AppTypography.weightBold,
                                color: AppColors.lightBackground,
                              ),
                            );
                          }).toList(),
                          pieTouchData: PieTouchData(
                            touchCallback: (event, response) {
                              setState(() {
                                if (response?.touchedSection != null) {
                                  touchedIndex = response!
                                      .touchedSection!.touchedSectionIndex;
                                } else {
                                  touchedIndex = -1;
                                }
                              });
                            },
                          ),
                          sectionsSpace: 2,
                          centerSpaceRadius: 0,
                        ),
                      ),
                    ),

                    // الأسطورة
                    if (widget.showLegend) ...[
                      SizedBox(width: AppDimensions.defaultMargin),
                      Expanded(
                        flex: 1,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: widget.data.asMap().entries.map((entry) {
                            final index = entry.key;
                            final point = entry.value;
                            final colors = widget.colors ?? defaultColors;
                            final percentage = (point.y / total * 100);

                            return Padding(
                              padding: EdgeInsets.only(
                                  bottom: AppDimensions.tinySpacing),
                              child: Row(
                                children: [
                                  Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      color: point.color ??
                                          colors[index % colors.length],
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  SizedBox(width: AppDimensions.tinySpacing),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          point.label ?? 'قطاع ${index + 1}',
                                          style:
                                              AppTypography.createCustomStyle(
                                            fontSize:
                                                AppTypography.fontSizeSmall,
                                            color: AppColors.lightTextSecondary,
                                          ),
                                        ),
                                        Text(
                                          '${percentage.toStringAsFixed(1)}%',
                                          style:
                                              AppTypography.createCustomStyle(
                                            fontSize:
                                                AppTypography.fontSizeSmall,
                                            color: AppColors.lightTextSecondary,
                                            fontWeight:
                                                AppTypography.weightMedium,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📊 القسم الرابع: الدوال المساعدة السريعة (AkCharts)
// ═══════════════════════════════════════════════════════════════════════════════

/// فئة الدوال المساعدة السريعة للرسوم البيانية
/// توفر طرق سريعة لإنشاء الرسوم البيانية الشائعة للمشروع التجاري
class AkCharts {
  AkCharts._();

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال الرسوم البيانية السريعة للمشروع التجاري
  // ───────────────────────────────────────────────────────────────────────────────

  /// رسم بياني خطي للمبيعات اليومية
  static Widget dailySales({
    required List<AkChartDataPoint> salesData,
    String title = 'المبيعات اليومية',
    AkChartSize size = AkChartSize.medium,
    Function(AkChartDataPoint)? onPointTap,
  }) {
    return AkLineChart(
      series: [
        AkChartSeries(
          name: 'المبيعات',
          data: salesData,
          color: AppColors.success,
          isCurved: true,
        ),
      ],
      title: title,
      size: size,
      style: AkChartStyle.detailed,
      onPointTap: onPointTap,
    );
  }

  /// رسم بياني خطي مقارن للمبيعات والمشتريات
  static Widget salesVsPurchases({
    required List<AkChartDataPoint> salesData,
    required List<AkChartDataPoint> purchasesData,
    String title = 'المبيعات مقابل المشتريات',
    AkChartSize size = AkChartSize.medium,
  }) {
    return AkLineChart(
      series: [
        AkChartSeries(
          name: 'المبيعات',
          data: salesData,
          color: AppColors.success,
          isCurved: true,
        ),
        AkChartSeries(
          name: 'المشتريات',
          data: purchasesData,
          color: AppColors.warning,
          isCurved: true,
        ),
      ],
      title: title,
      size: size,
      style: AkChartStyle.interactive,
    );
  }

  /// رسم بياني بالأعمدة للمنتجات الأكثر مبيعاً
  static Widget topSellingProducts({
    required List<AkChartDataPoint> productsData,
    String title = 'المنتجات الأكثر مبيعاً',
    AkChartSize size = AkChartSize.medium,
  }) {
    return AkBarChart(
      data: productsData,
      title: title,
      size: size,
      colors: const [
        AppColors.primary,
        AppColors.success,
        AppColors.info,
        AppColors.warning,
        AppColors.secondary,
      ],
    );
  }

  /// رسم بياني دائري لتوزيع المبيعات حسب الفئات
  static Widget salesByCategory({
    required List<AkChartDataPoint> categoriesData,
    String title = 'توزيع المبيعات حسب الفئات',
    AkChartSize size = AkChartSize.medium,
  }) {
    return AkPieChart(
      data: categoriesData,
      title: title,
      size: size,
      showPercentages: true,
      showLegend: true,
    );
  }

  /// رسم بياني دائري لطرق الدفع
  static Widget paymentMethods({
    required List<AkChartDataPoint> paymentData,
    String title = 'طرق الدفع',
    AkChartSize size = AkChartSize.small,
  }) {
    return AkPieChart(
      data: paymentData,
      title: title,
      size: size,
      colors: const [
        AppColors.success, // نقدي
        AppColors.primary, // بطاقة
        AppColors.warning, // آجل
        AppColors.info, // تحويل
      ],
      showPercentages: true,
      showLegend: true,
    );
  }

  /// رسم بياني خطي للأرباح الشهرية
  static Widget monthlyProfits({
    required List<AkChartDataPoint> profitsData,
    String title = 'الأرباح الشهرية',
    AkChartSize size = AkChartSize.large,
  }) {
    return AkLineChart(
      series: [
        AkChartSeries(
          name: 'الأرباح',
          data: profitsData,
          color: AppColors.success,
          isCurved: true,
        ),
      ],
      title: title,
      size: size,
      style: AkChartStyle.professional,
      showGrid: true,
      showLabels: true,
      showLegend: false,
    );
  }

  /// رسم بياني بالأعمدة للمبيعات الأسبوعية
  static Widget weeklySales({
    required List<AkChartDataPoint> weeklyData,
    String title = 'المبيعات الأسبوعية',
    AkChartSize size = AkChartSize.medium,
  }) {
    return AkBarChart(
      data: weeklyData,
      title: title,
      size: size,
      colors: const [AppColors.primary],
      showValues: true,
    );
  }

  /// رسم بياني دائري لحالة المخزون
  static Widget stockStatus({
    required List<AkChartDataPoint> stockData,
    String title = 'حالة المخزون',
    AkChartSize size = AkChartSize.medium,
  }) {
    return AkPieChart(
      data: stockData,
      title: title,
      size: size,
      colors: const [
        AppColors.success, // متوفر
        AppColors.warning, // منخفض
        AppColors.error, // نفد
      ],
      showPercentages: true,
      showLegend: true,
    );
  }

  /// رسم بياني خطي لنمو العملاء
  static Widget customerGrowth({
    required List<AkChartDataPoint> customersData,
    String title = 'نمو قاعدة العملاء',
    AkChartSize size = AkChartSize.medium,
  }) {
    return AkLineChart(
      series: [
        AkChartSeries(
          name: 'العملاء الجدد',
          data: customersData,
          color: AppColors.info,
          isCurved: true,
        ),
      ],
      title: title,
      size: size,
      style: AkChartStyle.detailed,
    );
  }

  /// رسم بياني مقارن للمبيعات السنوية
  static Widget yearlyComparison({
    required List<AkChartDataPoint> currentYearData,
    required List<AkChartDataPoint> previousYearData,
    String title = 'مقارنة المبيعات السنوية',
    AkChartSize size = AkChartSize.large,
  }) {
    return AkLineChart(
      series: [
        AkChartSeries(
          name: 'السنة الحالية',
          data: currentYearData,
          color: AppColors.primary,
          isCurved: true,
        ),
        AkChartSeries(
          name: 'السنة السابقة',
          data: previousYearData,
          color: AppColors.secondary,
          isCurved: true,
        ),
      ],
      title: title,
      size: size,
      style: AkChartStyle.professional,
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال مساعدة لإنشاء البيانات
  // ───────────────────────────────────────────────────────────────────────────────

  /// إنشاء بيانات وهمية للاختبار - مبيعات يومية
  static List<AkChartDataPoint> generateDailySalesData() {
    return List.generate(7, (index) {
      return AkChartDataPoint(
        x: index.toDouble(),
        y: (1000 + (index * 200) + (index % 2 * 300)).toDouble(),
        label: _getDayName(index),
      );
    });
  }

  /// إنشاء بيانات وهمية للاختبار - فئات المنتجات
  static List<AkChartDataPoint> generateCategorySalesData() {
    final categories = ['إلكترونيات', 'ملابس', 'طعام', 'كتب', 'أخرى'];
    final values = [35.0, 25.0, 20.0, 15.0, 5.0];

    return List.generate(categories.length, (index) {
      return AkChartDataPoint(
        x: index.toDouble(),
        y: values[index],
        label: categories[index],
      );
    });
  }

  /// إنشاء بيانات وهمية للاختبار - طرق الدفع
  static List<AkChartDataPoint> generatePaymentMethodsData() {
    final methods = ['نقدي', 'بطاقة', 'آجل', 'تحويل'];
    final values = [45.0, 30.0, 20.0, 5.0];

    return List.generate(methods.length, (index) {
      return AkChartDataPoint(
        x: index.toDouble(),
        y: values[index],
        label: methods[index],
      );
    });
  }

  /// الحصول على اسم اليوم
  static String _getDayName(int dayIndex) {
    const days = [
      'السبت',
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة'
    ];
    return days[dayIndex % days.length];
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال مساعدة للتحليل
  // ───────────────────────────────────────────────────────────────────────────────

  /// حساب إجمالي القيم
  static double calculateTotal(List<AkChartDataPoint> data) {
    return data.fold(0.0, (sum, point) => sum + point.y);
  }

  /// حساب المتوسط
  static double calculateAverage(List<AkChartDataPoint> data) {
    if (data.isEmpty) return 0.0;
    return calculateTotal(data) / data.length;
  }

  /// العثور على أعلى قيمة
  static AkChartDataPoint? findMaxValue(List<AkChartDataPoint> data) {
    if (data.isEmpty) return null;
    return data.reduce((a, b) => a.y > b.y ? a : b);
  }

  /// العثور على أقل قيمة
  static AkChartDataPoint? findMinValue(List<AkChartDataPoint> data) {
    if (data.isEmpty) return null;
    return data.reduce((a, b) => a.y < b.y ? a : b);
  }

  /// حساب معدل النمو
  static double calculateGrowthRate(List<AkChartDataPoint> data) {
    if (data.length < 2) return 0.0;
    final first = data.first.y;
    final last = data.last.y;
    if (first == 0) return 0.0;
    return ((last - first) / first) * 100;
  }
}
