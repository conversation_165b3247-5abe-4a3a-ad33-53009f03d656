import 'package:flutter/material.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';
import 'simple_account_form_screen.dart';
import '../widgets/account_tree_widget.dart';
import '../widgets/accounts_synchronized_table.dart';

class AccountsScreen extends StatefulWidget {
  const AccountsScreen({Key? key}) : super(key: key);

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen>
    with TickerProviderStateMixin {
  // TODO: Implementar con DatabaseService cuando esté disponible
  List<Map<String, dynamic>> _accounts = [];
  List<Map<String, dynamic>> _filteredAccounts = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedAccountType = 'all';
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;

  // للإحصائيات
  int _totalAccounts = 0;
  Map<String, int> _accountTypeCount = {};
  double _totalAssets = 0;
  double _totalLiabilities = 0;

  // للتصدير والطباعة
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadAccounts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAccounts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implementar con DatabaseService cuando esté disponible
      // Datos de ejemplo para mostrar la interfaz
      final List<Map<String, dynamic>> accounts = [
        {
          'id': '1',
          'name': 'الأصول',
          'code': '1000',
          'parent_id': null,
          'parent_name': null,
          'balance': 10000.0,
          'account_type': 'asset',
        },
        {
          'id': '2',
          'name': 'الخصوم',
          'code': '2000',
          'parent_id': null,
          'parent_name': null,
          'balance': 5000.0,
          'account_type': 'liability',
        },
      ];

      // Calcular estadísticas
      _totalAccounts = accounts.length;
      _accountTypeCount = {};
      _totalAssets = 0;
      _totalLiabilities = 0;

      for (var account in accounts) {
        final type = account['type'] as String;
        _accountTypeCount[type] = (_accountTypeCount[type] ?? 0) + 1;

        // Convertir el balance a double (puede venir como int desde SQLite)
        final balance = double.tryParse(account['balance'].toString()) ?? 0.0;

        if (type == 'asset') {
          _totalAssets += balance;
        } else if (type == 'liability') {
          _totalLiabilities += balance;
        }
      }

      setState(() {
        _accounts = accounts;
        _applyFilters();
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to load accounts',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'AccountsScreen'},
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في تحميل الحسابات')),
        );
      }
    }
  }

  void _applyFilters() {
    List<Map<String, dynamic>> filtered = List.from(_accounts);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((account) {
        final name = account['name'].toString().toLowerCase();
        final type = account['type'].toString().toLowerCase();
        final query = _searchQuery.toLowerCase();
        return name.contains(query) || type.contains(query);
      }).toList();
    }

    // Apply account type filter
    if (_selectedAccountType != 'all') {
      filtered = filtered.where((account) {
        return account['type'] == _selectedAccountType;
      }).toList();
    }

    setState(() {
      _filteredAccounts = filtered;
    });
  }

  String _getAccountTypeDisplayName(String type) {
    switch (type) {
      case 'asset':
        return 'أصول';
      case 'liability':
        return 'خصوم';
      case 'equity':
        return 'حقوق ملكية';
      case 'revenue':
        return 'إيرادات';
      case 'expense':
        return 'مصروفات';
      case 'customer':
        return 'عميل';
      case 'supplier':
        return 'مورد';
      case 'cash':
        return 'نقدية';
      default:
        return type;
    }
  }

  Color _getAccountTypeColor(String type) {
    switch (type) {
      case 'asset':
        return AppColors.info;
      case 'liability':
        return AppColors.error;
      case 'equity':
        return AppColors.accent;
      case 'revenue':
        return AppColors.success;
      case 'expense':
        return AppColors.warning;
      case 'customer':
        return AppColors.secondary;
      case 'supplier':
        return AppColors.warning;
      case 'cash':
        return DynamicColors.primary;
      default:
        return AppColors.secondary;
    }
  }

  void _navigateToAccountForm(BuildContext context,
      {Map<String, dynamic>? account}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SimpleAccountFormScreen(account: account),
      ),
    ).then((_) => _loadAccounts());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AkAppBar(
        title: 'إدارة الحسابات',
        actions: [
          // Selector de vista (lista/rejilla) implementado como TabBar
          SizedBox(
            width: 100,
            child: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(icon: Icon(Icons.view_list)),
                Tab(icon: Icon(Icons.grid_view)),
              ],
              indicatorColor: AppColors.primary,
              labelColor: AppColors.primary,
              unselectedLabelColor: AppColors.lightTextSecondary,
            ),
          ),
          // Botón para exportar
          IconButton(
            icon: const Icon(Icons.ios_share),
            tooltip: 'تصدير',
            onPressed: _exportData,
          ),
          // Botón para imprimir
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: 'طباعة',
            onPressed: _printData,
          ),
          // Botón para añadir nueva cuenta
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'إضافة حساب جديد',
            onPressed: () => _navigateToAccountForm(context),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: AkLoadingIndicator())
          : NestedScrollView(
              headerSliverBuilder: (context, innerBoxIsScrolled) {
                return [
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        // Panel de estadísticas
                        _buildStatistics(),

                        // Filtros y búsqueda
                        _buildFilters(),
                      ],
                    ),
                  ),
                ];
              },
              body: _filteredAccounts.isEmpty
                  ? _buildEmptyState()
                  : TabBarView(
                      controller: _tabController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _buildAccountsList(),
                        _buildAccountsGrid(),
                      ],
                    ),
            ),
      floatingActionButton: AkFloatingButton(
        onPressed: () => _navigateToAccountForm(context),
        tooltip: 'إضافة حساب جديد',
        icon: Icons.add,
      ),
    );
  }

  Widget _buildEmptyState() {
    final bool hasFilters =
        _searchQuery.isNotEmpty || _selectedAccountType != 'all';

    return AkEmptyState(
      title: hasFilters ? 'لا توجد نتائج' : 'لا توجد حسابات',
      message: hasFilters
          ? 'لا توجد حسابات تطابق معايير البحث'
          : 'لم يتم إنشاء أي حسابات حتى الآن',
      description: hasFilters
          ? 'جرب تغيير معايير البحث أو التصفية'
          : 'ابدأ بإضافة حساب جديد لإدارة أموالك',
      icon: Icons.account_balance_wallet,
      onRefresh: hasFilters ? null : _loadAccounts,
      onAction: hasFilters ? null : () => _navigateToAccountForm(context),
      actionText: hasFilters ? null : 'إضافة حساب جديد',
    );
  }

  Widget _buildStatistics() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات الحسابات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: AppDimensions.spacing8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildStatCard(
                  title: 'إجمالي الحسابات',
                  value: _totalAccounts.toString(),
                  icon: Icons.account_balance_wallet,
                  color: AppColors.lightTextSecondary,
                ),
                _buildStatCard(
                  title: 'إجمالي الأصول',
                  value: _totalAssets.toStringAsFixed(2),
                  icon: Icons.account_balance,
                  color: AppColors.lightTextSecondary,
                ),
                _buildStatCard(
                  title: 'إجمالي الخصوم',
                  value: _totalLiabilities.toStringAsFixed(2),
                  icon: Icons.money_off,
                  color: AppColors.lightTextSecondary,
                ),
                ...(_accountTypeCount.entries.map((entry) => _buildStatCard(
                      title: _getAccountTypeDisplayName(entry.key),
                      value: entry.value.toString(),
                      icon: _getAccountTypeIcon(entry.key),
                      color: _getAccountTypeColor(entry.key),
                    ))),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(right: 8.0),
      child: Container(
        padding: const EdgeInsets.all(12.0),
        width: 120,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: AppDimensions.spacing8),
            Text(
              value,
              style: AppTypography.createCustomStyle(
                fontSize: 18,
                fontWeight: AppTypography.weightBold,
              ),
            ),
            SizedBox(height: AppDimensions.smallSpacing),
            Text(
              title,
              style: AppTypography.createCustomStyle(fontSize: 12),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getAccountTypeIcon(String type) {
    switch (type) {
      case 'asset':
        return Icons.account_balance;
      case 'liability':
        return Icons.money_off;
      case 'equity':
        return Icons.pie_chart;
      case 'revenue':
        return Icons.trending_up;
      case 'expense':
        return Icons.trending_down;
      case 'customer':
        return Icons.person;
      case 'supplier':
        return Icons.business;
      case 'cash':
        return Icons.payments;
      default:
        return Icons.account_balance_wallet;
    }
  }

  // بناء عرض الجدول (DataGrid)
  Widget _buildAccountsList() {
    return AccountsSynchronizedTable(
      accounts: _filteredAccounts,
      onEdit: _handleEditAccount,
      onDelete: _handleDeleteAccount,
      onPrint: _handlePrintAccount,
    );
  }

  void _handleDeleteAccount(Map<String, dynamic> account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الحساب "${account['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // هنا يمكن إضافة منطق الحذف الفعلي
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text('تم حذف الحساب "${account['name']}" بنجاح')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _handlePrintAccount(Map<String, dynamic> account) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('جاري طباعة بيانات الحساب "${account['name']}"')),
    );
    // هنا يمكن إضافة منطق الطباعة الفعلي
  }

  // بناء عرض الشجرة (TreeView)
  Widget _buildAccountsGrid() {
    return AccountTreeWidget(
      accounts: _filteredAccounts,
      onSelect: _handleSelectAccount,
    );
  }

  void _handleEditAccount(Map<String, dynamic> account) {
    _navigateToAccountForm(context, account: account);
  }

  void _handleSelectAccount(dynamic account) {
    // لا نقوم بعرض أي رسالة عند اختيار الحساب
    // يمكن استخدام هذه الدالة لعرض تفاصيل الحساب في المستقبل
  }

  // دالة لتصدير البيانات
  void _exportData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البيانات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر صيغة التصدير:'),
            const SizedBox(height: AppDimensions.spacing16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildExportButton(
                  context,
                  icon: Icons.table_chart,
                  label: 'Excel',
                  color: AppColors.lightTextSecondary,
                  onTap: () => _exportToExcel(),
                ),
                _buildExportButton(
                  context,
                  icon: Icons.picture_as_pdf,
                  label: 'PDF',
                  color: AppColors.lightTextSecondary,
                  onTap: () => _exportToPdf(),
                ),
                _buildExportButton(
                  context,
                  icon: Icons.text_snippet,
                  label: 'CSV',
                  color: AppColors.lightTextSecondary,
                  onTap: () => _exportToCsv(),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildExportButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: AppColors.lightTextSecondary,
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: AppDimensions.spacing8),
          Text(
            label,
            style: AppTypography.createCustomStyle(
              color: color,
              fontWeight: AppTypography.weightBold,
            ),
          ),
        ],
      ),
    );
  }

  // تصدير إلى Excel
  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير البيانات إلى Excel...'),
        duration: Duration(seconds: 1),
      ),
    );

    // هنا يمكن إضافة كود التصدير الفعلي
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير البيانات بنجاح إلى Excel'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    });
  }

  // تصدير إلى PDF
  void _exportToPdf() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير البيانات إلى PDF...'),
        duration: Duration(seconds: 1),
      ),
    );

    // هنا يمكن إضافة كود التصدير الفعلي
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير البيانات بنجاح إلى PDF'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    });
  }

  // تصدير إلى CSV
  void _exportToCsv() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير البيانات إلى CSV...'),
        duration: Duration(seconds: 1),
      ),
    );

    // هنا يمكن إضافة كود التصدير الفعلي
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير البيانات بنجاح إلى CSV'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    });
  }

  // دالة للطباعة
  void _printData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طباعة البيانات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر نوع التقرير للطباعة:'),
            const SizedBox(height: AppDimensions.spacing16),
            ListTile(
              leading: const Icon(Icons.list_alt, color: AppColors.info),
              title: const Text('قائمة الحسابات'),
              subtitle: const Text('طباعة جميع الحسابات في قائمة'),
              onTap: () {
                Navigator.pop(context);
                _printAccountsList();
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.account_tree, color: AppColors.success),
              title: const Text('شجرة الحسابات'),
              subtitle: const Text('طباعة الحسابات في شكل شجرة هرمية'),
              onTap: () {
                Navigator.pop(context);
                _printAccountsTree();
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.summarize, color: AppColors.accent),
              title: const Text('ملخص الحسابات'),
              subtitle: const Text('طباعة ملخص إحصائي للحسابات'),
              onTap: () {
                Navigator.pop(context);
                _printAccountsSummary();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  // طباعة قائمة الحسابات
  void _printAccountsList() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة قائمة الحسابات...'),
        duration: Duration(seconds: 1),
      ),
    );

    // هنا يمكن إضافة كود الطباعة الفعلي
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال قائمة الحسابات للطباعة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    });
  }

  // طباعة شجرة الحسابات
  void _printAccountsTree() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة شجرة الحسابات...'),
        duration: Duration(seconds: 1),
      ),
    );

    // هنا يمكن إضافة كود الطباعة الفعلي
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال شجرة الحسابات للطباعة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    });
  }

  // طباعة ملخص الحسابات
  void _printAccountsSummary() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة ملخص الحسابات...'),
        duration: Duration(seconds: 1),
      ),
    );

    // هنا يمكن إضافة كود الطباعة الفعلي
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال ملخص الحسابات للطباعة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    });
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          TextField(
            decoration: const InputDecoration(
              hintText: 'بحث...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _applyFilters();
            },
          ),
          const SizedBox(height: AppDimensions.spacing16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('all', 'الكل'),
                _buildFilterChip('asset', 'أصول'),
                _buildFilterChip('liability', 'خصوم'),
                _buildFilterChip('equity', 'حقوق ملكية'),
                _buildFilterChip('revenue', 'إيرادات'),
                _buildFilterChip('expense', 'مصروفات'),
                _buildFilterChip('customer', 'عملاء'),
                _buildFilterChip('supplier', 'موردين'),
                _buildFilterChip('cash', 'نقدية'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String type, String label) {
    final isSelected = _selectedAccountType == type;

    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: FilterChip(
        selected: isSelected,
        label: Text(label),
        onSelected: (selected) {
          setState(() {
            _selectedAccountType = selected ? type : 'all';
          });
          _applyFilters();
        },
        backgroundColor: AppColors.lightSurfaceVariant,
        selectedColor: AppColors.primary.withValues(alpha: 0.2),
        checkmarkColor: AppColors.primary,
      ),
    );
  }
}
