import 'dart:convert';

/// سكيما الأدوار والصلاحيات في النظام
class RolesSchema {
  /// الأدوار المتاحة في النظام
  /// تم تحديث الأدوار لتكون أكثر وضوحاً ودقة
  static const Map<String, String> roles = {
    'owner': 'مالك',
    'admin': 'مدير',
    'manager': 'مشرف',
    'accountant': 'محاسب',
    'inventory_manager': 'مدير مخزون',
    'cashier': 'كاشير',
    'salesperson': 'مندوب مبيعات',
    'viewer': 'مشاهد',
    'guest': 'زائر',
  };

  /// الصلاحيات المتاحة في النظام مقسمة حسب الوحدات
  static const Map<String, Map<String, String>> permissions = {
    'dashboard': {
      'view_dashboard': 'عرض لوحة التحكم',
      'view_reports': 'عرض التقارير',
      'view_statistics': 'عرض الإحصائيات',
    },
    'sales': {
      'view_sales': 'عرض المبيعات',
      'create_sale': 'إنشاء عملية بيع',
      'edit_sale': 'تعديل عملية بيع',
      'delete_sale': 'حذف عملية بيع',
      'view_sales_reports':
          'عرض تقارير المبيعات', // توحيد مع الصلاحية في وحدة التقارير
      'manage_discounts': 'إدارة الخصومات',
      'manage_returns': 'إدارة المرتجعات',
      'view_pos': 'استخدام نقطة البيع',
    },
    'purchases': {
      'view_purchases': 'عرض المشتريات',
      'create_purchase': 'إنشاء عملية شراء',
      'edit_purchase': 'تعديل عملية شراء',
      'delete_purchase': 'حذف عملية شراء',
      'view_purchase_reports':
          'عرض تقارير المشتريات', // توحيد مع الصلاحية في وحدة التقارير
      'manage_purchase_returns': 'إدارة مرتجعات المشتريات',
    },
    'inventory': {
      'view_inventory': 'عرض المخزون',
      'add_product': 'إضافة منتج',
      'edit_product': 'تعديل منتج',
      'delete_product': 'حذف منتج',
      'manage_categories': 'إدارة التصنيفات',
      'manage_units': 'إدارة وحدات القياس',
      'manage_inventory_adjustments': 'إدارة تسويات المخزون',
      'manage_inventory_transfers': 'إدارة تحويلات المخزون',
      'view_inventory_reports':
          'عرض تقارير المخزون', // توحيد مع الصلاحية في وحدة التقارير
    },
    'customers': {
      'view_customers': 'عرض العملاء',
      'add_customer': 'إضافة عميل',
      'edit_customer': 'تعديل عميل',
      'delete_customer': 'حذف عميل',
      'view_customer_reports':
          'عرض تقارير العملاء', // توحيد مع الصلاحية في وحدة التقارير
    },
    'suppliers': {
      'view_suppliers': 'عرض الموردين',
      'add_supplier': 'إضافة مورد',
      'edit_supplier': 'تعديل مورد',
      'delete_supplier': 'حذف مورد',
      'view_supplier_reports':
          'عرض تقارير الموردين', // توحيد مع الصلاحية في وحدة التقارير
    },
    'expenses': {
      'view_expenses': 'عرض المصروفات',
      'add_expense': 'إضافة مصروف',
      'edit_expense': 'تعديل مصروف',
      'delete_expense': 'حذف مصروف',
      'manage_expense_categories': 'إدارة تصنيفات المصروفات',
      'view_expense_reports':
          'عرض تقارير المصروفات', // توحيد مع الصلاحية في وحدة التقارير
    },
    'users': {
      'view_users': 'عرض المستخدمين',
      'add_user': 'إضافة مستخدم',
      'edit_user': 'تعديل مستخدم',
      'delete_user': 'حذف مستخدم',
      'manage_roles': 'إدارة الأدوار',
      'manage_permissions': 'إدارة الصلاحيات',
    },
    'settings': {
      'view_settings': 'عرض الإعدادات',
      'edit_settings': 'تعديل الإعدادات',
      'manage_company_info': 'إدارة معلومات الشركة',
      'manage_tax_settings': 'إدارة إعدادات الضرائب',
      'manage_backup': 'إدارة النسخ الاحتياطي',
    },
    'branches': {
      'access_all_branches': 'الوصول لجميع الفروع',
      'access_single_branch': 'الوصول لفرع واحد فقط',
      'access_main_branch_only': 'الوصول للفرع الرئيسي فقط',
      'access_specific_branches': 'الوصول لفروع محددة',
      'manage_branches': 'إدارة الفروع',
      'view_branches': 'عرض الفروع',
      'switch_branches': 'التبديل بين الفروع',
    },
    'reports': {
      'view_sales_reports': 'عرض تقارير المبيعات العامة',
      'view_purchase_reports': 'عرض تقارير المشتريات العامة',
      'view_inventory_reports': 'عرض تقارير المخزون العامة',
      'view_customer_reports': 'عرض تقارير العملاء العامة',
      'view_supplier_reports': 'عرض تقارير الموردين العامة',
      'view_expense_reports': 'عرض تقارير المصروفات العامة',
      'view_profit_loss_reports': 'عرض تقارير الأرباح والخسائر',
      'view_financial_reports': 'عرض التقارير المالية',
      'view_daily_reports': 'عرض التقارير اليومية',
      'export_reports': 'تصدير التقارير',
    },
    'accounting': {
      'view_accounts': 'عرض الحسابات',
      'create_account': 'إنشاء حساب',
      'edit_account': 'تعديل حساب',
      'delete_account': 'حذف حساب',
      'view_transactions': 'عرض المعاملات',
      'create_transaction': 'إنشاء معاملة',
      'edit_transaction': 'تعديل معاملة',
      'delete_transaction': 'حذف معاملة',
    },
    'payments': {
      'process_payments': 'معالجة المدفوعات',
      'view_payment_methods': 'عرض طرق الدفع',
      'manage_payment_methods': 'إدارة طرق الدفع',
    },
    'inventory_advanced': {
      'manage_stock_alerts': 'إدارة تنبيهات المخزون',
    },
  };

  /// الصلاحيات الافتراضية لكل دور
  static final Map<String, List<String>> defaultRolePermissions = {
    'owner': _getAllPermissions(),
    'admin': _getAllPermissions(),
    'manager': [
      // صلاحيات لوحة التحكم
      'view_dashboard',
      'view_reports',
      'view_statistics',

      // صلاحيات المبيعات
      'view_sales',
      'create_sale',
      'edit_sale',
      'view_sales_reports', // Actualizado para usar el código unificado
      'manage_discounts',
      'manage_returns',
      'view_pos',

      // صلاحيات المشتريات
      'view_purchases',
      'create_purchase',
      'edit_purchase',
      'view_purchase_reports',

      // صلاحيات المخزون
      'view_inventory',
      'add_product',
      'edit_product',
      'manage_categories',
      'manage_units',
      'manage_inventory_adjustments',
      'manage_inventory_transfers',
      'view_inventory_reports',

      // صلاحيات العملاء والموردين
      'view_customers',
      'add_customer',
      'edit_customer',
      'view_customer_reports',
      'view_suppliers',
      'add_supplier',
      'edit_supplier',
      'view_supplier_reports',

      // صلاحيات المصروفات
      'view_expenses',
      'add_expense',
      'edit_expense',
      'manage_expense_categories',
      'view_expense_reports',

      // صلاحيات التقارير
      'view_sales_reports',
      'view_purchase_reports',
      'view_inventory_reports',
      'view_customer_reports',
      'view_supplier_reports',
      'view_expense_reports',
      'view_profit_loss_reports',
      'export_reports',

      // صلاحيات الفروع
      'access_all_branches',
      'manage_branches',
      'view_branches',
      'switch_branches',
    ],
    'cashier': [
      // صلاحيات لوحة التحكم
      'view_dashboard',

      // صلاحيات المبيعات (صلاحيات كاملة للكاشير)
      'view_sales',
      'create_sale',
      'edit_sale',
      'view_pos',
      'manage_returns',
      'manage_discounts',
      'view_sales_reports',

      // صلاحيات المخزون (للاستعلام)
      'view_inventory',

      // صلاحيات العملاء (للتعامل اليومي)
      'view_customers',
      'add_customer',
      'edit_customer',

      // صلاحيات المدفوعات
      'process_payments',
      'view_payment_methods',

      // صلاحيات التقارير (محدودة)
      'view_sales_reports',
      'view_daily_reports',

      // صلاحيات الفروع
      'access_single_branch',
      'view_branches',
    ],
    'salesperson': [
      // صلاحيات لوحة التحكم
      'view_dashboard',

      // صلاحيات المبيعات (محدودة)
      'view_sales',
      'create_sale',
      'view_pos',
      'manage_returns',

      // صلاحيات المخزون (للاستعلام)
      'view_inventory',

      // صلاحيات العملاء (صلاحيات كاملة)
      'view_customers',
      'add_customer',
      'edit_customer',
      'view_customer_reports',

      // صلاحيات التقارير (محدودة)
      'view_sales_reports',
      'view_customer_reports',

      // صلاحيات الفروع
      'access_single_branch',
      'view_branches',
    ],
    'accountant': [
      // صلاحيات لوحة التحكم
      'view_dashboard',
      'view_reports',
      'view_statistics',

      // صلاحيات المبيعات (للمراجعة المالية)
      'view_sales',
      'view_sales_reports',

      // صلاحيات المشتريات (للمراجعة المالية)
      'view_purchases',
      'create_purchase',
      'edit_purchase',
      'view_purchase_reports',

      // صلاحيات المخزون (للتقييم المالي)
      'view_inventory',
      'view_inventory_reports',

      // صلاحيات العملاء والموردين (للمتابعة المالية)
      'view_customers',
      'edit_customer',
      'view_customer_reports',
      'view_suppliers',
      'add_supplier',
      'edit_supplier',
      'view_supplier_reports',

      // صلاحيات المصروفات (صلاحيات كاملة)
      'view_expenses',
      'add_expense',
      'edit_expense',
      'delete_expense',
      'view_expense_reports',

      // صلاحيات المحاسبة والمالية
      'view_accounts',
      'create_account',
      'edit_account',
      'view_transactions',
      'create_transaction',
      'edit_transaction',

      // صلاحيات التقارير المالية (شاملة)
      'view_sales_reports',
      'view_purchase_reports',
      'view_inventory_reports',
      'view_customer_reports',
      'view_supplier_reports',
      'view_expense_reports',
      'view_profit_loss_reports',
      'view_financial_reports',
      'export_reports',

      // صلاحيات الفروع
      'access_all_branches',
      'view_branches',
      'switch_branches',
    ],
    'inventory_manager': [
      // صلاحيات لوحة التحكم
      'view_dashboard',
      'view_reports',
      'view_statistics',

      // صلاحيات المخزون (صلاحيات كاملة)
      'view_inventory',
      'add_product',
      'edit_product',
      'delete_product',
      'manage_categories',
      'manage_units',
      'manage_inventory_adjustments',
      'manage_inventory_transfers',
      'view_inventory_reports',
      'manage_stock_alerts',

      // صلاحيات المشتريات (للتزويد)
      'view_purchases',
      'create_purchase',
      'edit_purchase',
      'delete_purchase',
      'view_purchase_reports',

      // صلاحيات الموردين (للتعامل معهم)
      'view_suppliers',
      'add_supplier',
      'edit_supplier',
      'delete_supplier',
      'view_supplier_reports',

      // صلاحيات المبيعات (للمتابعة)
      'view_sales',
      'view_sales_reports',

      // صلاحيات التقارير
      'view_inventory_reports',
      'view_purchase_reports',
      'view_supplier_reports',
      'export_reports',

      // صلاحيات الفروع
      'access_all_branches',
      'view_branches',
      'switch_branches',
    ],
    'viewer': [
      // صلاحيات لوحة التحكم
      'view_dashboard',
      'view_reports',
      'view_statistics',

      // صلاحيات المبيعات
      'view_sales',
      'view_sales_reports', // Actualizado para usar el código unificado

      // صلاحيات المشتريات
      'view_purchases',
      'view_purchase_reports',

      // صلاحيات المخزون
      'view_inventory',
      'view_inventory_reports',

      // صلاحيات العملاء والموردين
      'view_customers',
      'view_customer_reports',
      'view_suppliers',
      'view_supplier_reports',

      // صلاحيات المصروفات
      'view_expenses',
      'view_expense_reports',

      // صلاحيات التقارير
      'view_sales_reports',
      'view_purchase_reports',
      'view_inventory_reports',
      'view_customer_reports',
      'view_supplier_reports',
      'view_expense_reports',
      'view_profit_loss_reports',

      // صلاحيات الفروع
      'access_all_branches',
      'view_branches',
    ],
    'guest': [
      // صلاحيات محدودة جداً للزوار
      'view_dashboard',

      // صلاحيات المخزون (للاستعلام فقط)
      'view_inventory',

      // صلاحيات العملاء (للاستعلام فقط)
      'view_customers',

      // صلاحيات الفروع (محدودة)
      'access_single_branch',
      'view_branches',
    ],
  };

  /// الحصول على جميع الصلاحيات المتاحة في النظام
  static List<String> _getAllPermissions() {
    final List<String> allPermissions = [];

    permissions.forEach((module, modulePermissions) {
      modulePermissions.forEach((permission, _) {
        allPermissions.add(permission);
      });
    });

    return allPermissions;
  }

  /// تحويل سكيما الأدوار إلى JSON
  static String toJson() {
    final Map<String, dynamic> schema = {
      'roles': roles,
      'permissions': permissions,
      'defaultRolePermissions': defaultRolePermissions,
    };

    return jsonEncode(schema);
  }

  /// إنشاء سكيما الأدوار من JSON
  static Map<String, dynamic> fromJson(String jsonString) {
    return jsonDecode(jsonString) as Map<String, dynamic>;
  }
}
