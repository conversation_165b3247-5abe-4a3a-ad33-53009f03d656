import '../../../core/models/product.dart';

/// نموذج عنصر السلة
class CartItem {
  final Product product;
  final double quantity;
  final double price;
  final double discount;
  final bool isDiscountPercentage;
  final double taxRate;

  CartItem({
    required this.product,
    this.quantity = 1.0,
    double? price,
    this.discount = 0.0,
    this.isDiscountPercentage = false,
    double? taxRate,
  })  : price = price ?? product.salePrice,
        taxRate = taxRate ?? product.taxRate;

  /// إنشاء نسخة من هذا العنصر مع استبدال الحقول المحددة بقيم جديدة
  CartItem copyWith({
    Product? product,
    double? quantity,
    double? price,
    double? discount,
    bool? isDiscountPercentage,
    double? taxRate,
  }) {
    return CartItem(
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      discount: discount ?? this.discount,
      isDiscountPercentage: isDiscountPercentage ?? this.isDiscountPercentage,
      taxRate: taxRate ?? this.taxRate,
    );
  }

  /// حساب المجموع الفرعي (السعر × الكمية)
  double get subtotal => price * quantity;

  /// حساب قيمة الخصم
  double get discountAmount {
    if (isDiscountPercentage) {
      return subtotal * (discount / 100);
    } else {
      return discount;
    }
  }

  /// حساب المجموع بعد الخصم
  double get totalAfterDiscount => subtotal - discountAmount;

  /// حساب قيمة الضريبة
  double get taxAmount => totalAfterDiscount * (taxRate / 100);

  /// حساب المجموع النهائي (بعد الخصم والضريبة)
  double get total => totalAfterDiscount + taxAmount;

  /// تحويل العنصر إلى Map
  Map<String, dynamic> toMap() {
    return {
      'product_id': product.id,
      'product_name': product.name,
      'quantity': quantity,
      'price': price,
      'discount': discount,
      'is_discount_percentage': isDiscountPercentage ? 1 : 0,
      'tax_rate': taxRate,
      'subtotal': subtotal,
      'discount_amount': discountAmount,
      'tax_amount': taxAmount,
      'total': total,
    };
  }

  @override
  String toString() {
    return 'CartItem(product: ${product.name}, quantity: $quantity, price: $price, total: $total)';
  }
}
