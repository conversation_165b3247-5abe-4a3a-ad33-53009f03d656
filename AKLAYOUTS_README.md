# 📐 نظام التخطيطات الموحد (AK Layouts System)

نظام شامل وموحد لجميع تخطيطات الواجهة في تطبيق تاجر بلس، مصمم خصيصاً للمشاريع التجارية اليمنية.

## 🎯 **المميزات الرئيسية**

- ✅ **تصميم موحد ومتناسق** لجميع التخطيطات
- ✅ **دعم كامل للوضع المظلم/الفاتح** باستخدام `Theme.of(context).brightness`
- ✅ **عدم وجود قيم صريحة** - استخدام `AppColors.*`, `AppDimensions.*`, `AppTypography.*` فقط
- ✅ **تخطيطات متجاوبة ومرنة** تتكيف مع جميع الأحجام
- ✅ **دوال مساعدة سريعة** للاستخدام المباشر
- ✅ **تعليقات شاملة باللغة العربية**
- ✅ **مساحات موحدة** بين العناصر
- ✅ **دوال متخصصة للمشروع التجاري**

## 📋 **التخطيطات المتوفرة**

### ↔️ **1. الصف المتجاوب (AkRow)**
صف متجاوب موحد مع محاذاة قابلة للتخصيص ومساحات موحدة.

### ↕️ **2. العمود المتجاوب (AkColumn)**
عمود متجاوب موحد مع محاذاة قابلة للتخصيص ومساحات موحدة.

### ⬜ **3. المساحة الفارغة (AkSpacer)**
مساحة فارغة موحدة مع أحجام محددة مسبقاً.

## 🚀 **أمثلة الاستخدام**

### **الصف الأساسي:**
```dart
// صف بسيط مع مساحات متوسطة
AkRow(
  children: [
    Text('العنصر الأول'),
    Text('العنصر الثاني'),
    Text('العنصر الثالث'),
  ],
  spacing: AkSpacingSize.medium,
)
```

### **صف مع توزيع متساوي:**
```dart
// صف مع توزيع العناصر بالتساوي
AkRow(
  children: [
    Text('البداية'),
    Text('الوسط'),
    Text('النهاية'),
  ],
  alignment: AkAlignment.spaceBetween,
  spacing: AkSpacingSize.small,
)
```

### **عمود مع محاذاة في الوسط:**
```dart
// عمود مع محاذاة في الوسط
AkColumn(
  children: [
    Icon(Icons.check_circle, size: 64),
    Text('تم الحفظ بنجاح'),
    Text('تم حفظ البيانات في قاعدة البيانات'),
  ],
  alignment: AkAlignment.center,
  spacing: AkSpacingSize.large,
)
```

### **المساحات الفارغة:**
```dart
// مساحات بأحجام مختلفة
Column(
  children: [
    Text('النص الأول'),
    AkSpacer.small(), // مساحة صغيرة
    Text('النص الثاني'),
    AkSpacer.large(), // مساحة كبيرة
    Text('النص الثالث'),
  ],
)
```

## 🎨 **أنواع المحاذاة**

| النوع | الوصف | الاستخدام |
|-------|--------|-----------|
| `start` | بداية | محاذاة في البداية |
| `center` | وسط | محاذاة في الوسط |
| `end` | نهاية | محاذاة في النهاية |
| `spaceBetween` | توزيع متساوي | مساحات متساوية بين العناصر |
| `spaceAround` | توزيع حول | مساحات حول العناصر |
| `spaceEvenly` | توزيع متساوي مع مساحات | توزيع متساوي مع مساحات |

## 📏 **أحجام المساحات**

| الحجم | الوصف | القيمة |
|-------|--------|---------|
| `tiny` | صغيرة جداً | 4px |
| `small` | صغيرة | 8px |
| `medium` | متوسطة | 16px |
| `large` | كبيرة | 24px |
| `extraLarge` | كبيرة جداً | 32px |

## 🛠️ **الدوال المساعدة المتوفرة**

### **دوال الصفوف السريعة:**
- `AkLayouts.spaceBetween()` - صف مع توزيع متساوي
- `AkLayouts.center()` - صف مع محاذاة في الوسط
- `AkLayouts.end()` - صف مع محاذاة في النهاية

### **دوال الأعمدة السريعة:**
- `AkLayouts.columnSpaceBetween()` - عمود مع توزيع متساوي
- `AkLayouts.columnCenter()` - عمود مع محاذاة في الوسط

### **دوال متخصصة للمشروع التجاري:**
- `AkLayouts.productCard()` - تخطيط بطاقة منتج
- `AkLayouts.statsRow()` - تخطيط صف إحصائيات
- `AkLayouts.pageHeader()` - تخطيط رأس الصفحة
- `AkLayouts.listItem()` - تخطيط عنصر قائمة
- `AkLayouts.form()` - تخطيط نموذج

## 🎯 **أمثلة متقدمة**

### **بطاقة منتج:**
```dart
AkLayouts.productCard(
  image: Image.network('https://example.com/product.jpg'),
  title: Text('هاتف ذكي'),
  price: Text('1,250 ر.ي'),
  description: Text('هاتف ذكي بمواصفات عالية'),
  actions: Row(
    children: [
      AkButton(text: 'إضافة للسلة', onPressed: () {}),
      AkButton(text: 'عرض التفاصيل', onPressed: () {}),
    ],
  ),
)
```

### **صف إحصائيات:**
```dart
AkLayouts.statsRow(
  stats: [
    AkStatsCard(title: 'المبيعات', value: '25,480 ر.ي'),
    AkStatsCard(title: 'الطلبات', value: '156'),
    AkStatsCard(title: 'العملاء', value: '89'),
  ],
)
```

### **رأس الصفحة:**
```dart
AkLayouts.pageHeader(
  title: Text('إدارة المنتجات'),
  subtitle: Text('عرض وإدارة جميع المنتجات'),
  actions: Row(
    children: [
      AkButton(text: 'إضافة منتج', onPressed: () {}),
      AkButton(text: 'تصدير', onPressed: () {}),
    ],
  ),
)
```

### **عنصر قائمة:**
```dart
AkLayouts.listItem(
  leading: CircleAvatar(child: Icon(Icons.person)),
  title: Text('أحمد محمد'),
  subtitle: Text('عميل منذ 2023'),
  trailing: Icon(Icons.arrow_forward_ios),
  onTap: () => openCustomerDetails(),
)
```

### **نموذج:**
```dart
AkLayouts.form(
  fields: [
    AkCurrencyInput(label: 'السعر', controller: priceController),
    AkPercentageInput(label: 'الخصم', controller: discountController),
    AkLongTextInput(label: 'الوصف', controller: descriptionController),
  ],
  actions: Row(
    children: [
      AkButton(text: 'حفظ', onPressed: () {}),
      AkButton(text: 'إلغاء', onPressed: () {}),
    ],
  ),
  spacing: AkSpacingSize.large,
)
```

## 🎨 **التخصيص المتقدم**

### **صف مع خلفية وحدود:**
```dart
AkRow(
  children: [Text('محتوى'), Icon(Icons.star)],
  padding: EdgeInsets.all(16),
  margin: EdgeInsets.all(8),
  backgroundColor: AppColors.lightSurfaceVariant,
  borderRadius: 12,
  spacing: AkSpacingSize.medium,
)
```

### **عمود موسع:**
```dart
AkColumn(
  children: [
    Text('رأس'),
    Expanded(child: Text('محتوى قابل للتوسع')),
    Text('تذييل'),
  ],
  expanded: true,
  spacing: AkSpacingSize.large,
)
```

### **مساحات مخصصة:**
```dart
// مساحة أفقية
Row(
  children: [
    Text('يسار'),
    AkSpacer.horizontal(AkSpacingSize.large),
    Text('يمين'),
  ],
)

// مساحة عمودية
Column(
  children: [
    Text('أعلى'),
    AkSpacer.vertical(AkSpacingSize.extraLarge),
    Text('أسفل'),
  ],
)
```

## 📱 **التوافق والاستجابة**

- ✅ **متوافق مع جميع أحجام الشاشات**
- ✅ **يتكيف مع اتجاه الشاشة**
- ✅ **يدعم الخطوط العربية**
- ✅ **متوافق مع إعدادات إمكانية الوصول**
- ✅ **تخطيطات مرنة ومتجاوبة**

## 🎨 **الدعم للوضع المظلم/الفاتح**

النظام يتكيف تلقائياً مع وضع التطبيق:

```dart
// الألوان تتغير تلقائياً حسب الوضع
backgroundColor: isDark ? AppColors.darkSurface : AppColors.lightSurface,
textColor: isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
```

## 🔄 **الترقية من الأنظمة القديمة**

### **من enhanced_ui_components.dart:**
```dart
// ❌ القديم
Row(children: [...], mainAxisAlignment: MainAxisAlignment.spaceBetween)

// ✅ الجديد
AkLayouts.spaceBetween(children: [...])
```

### **من التخطيطات اليدوية:**
```dart
// ❌ القديم
Column(
  children: [
    widget1,
    SizedBox(height: 16),
    widget2,
    SizedBox(height: 16),
    widget3,
  ],
)

// ✅ الجديد
AkColumn(
  children: [widget1, widget2, widget3],
  spacing: AkSpacingSize.medium,
)
```

## 📊 **الإحصائيات**

- **عدد التخطيطات**: 3 أنواع أساسية
- **عدد الدوال المساعدة**: 10 دوال
- **عدد أنواع المحاذاة**: 6 أنواع
- **عدد أحجام المساحات**: 5 أحجام
- **الدعم للغات**: العربية (أساسي)
- **حجم الملف**: ~550 سطر
- **التبعيات**: لا توجد تبعيات خارجية

---

**تم تطوير هذا النظام خصيصاً لتطبيق تاجر بلس - نظام إدارة المبيعات اليمني** 🇾🇪
