import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../core/theme/index.dart';

/// عداد متحرك يعرض قيمة عددية مع تأثير متحرك
class AnimatedCounter extends StatefulWidget {
  /// القيمة النهائية للعداد
  final double value;

  /// وحدة القياس (مثل ر.س، $، إلخ)
  final String? unit;

  /// عدد الأرقام العشرية
  final int decimalPlaces;

  /// مدة الحركة
  final Duration duration;

  /// نمط النص
  final AppTypography? style;

  /// حجم النص
  final double? fontSize;

  /// لون النص
  final Color? color;

  /// وزن الخط
  final FontWeight? fontWeight;

  /// محاذاة النص
  final TextAlign textAlign;

  /// دالة تنسيق مخصصة
  final String Function(double)? formatter;

  const AnimatedCounter({
    Key? key,
    required this.value,
    this.unit,
    this.decimalPlaces = 0,
    this.duration = const Duration(milliseconds: 1500),
    this.style,
    this.fontSize,
    this.color,
    this.fontWeight,
    this.textAlign = TextAlign.center,
    this.formatter,
  }) : super(key: key);

  @override
  State<AnimatedCounter> createState() => _AnimatedCounterState();
}

class _AnimatedCounterState extends State<AnimatedCounter>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late double _oldValue;

  @override
  void initState() {
    super.initState();
    _oldValue = 0;
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    _animation = Tween<double>(begin: _oldValue, end: widget.value).animate(
        CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic))
      ..addListener(() {
        setState(() {});
      });
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedCounter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _oldValue = oldWidget.value;
      _animation = Tween<double>(begin: _oldValue, end: widget.value).animate(
          CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic));
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _formatValue(double value) {
    if (widget.formatter != null) {
      return widget.formatter!(value);
    }

    if (widget.decimalPlaces == 0) {
      return value.toInt().toString();
    }

    return value.toStringAsFixed(widget.decimalPlaces);
  }

  @override
  Widget build(BuildContext context) {
    final formattedValue = _formatValue(_animation.value);
    final unitText = widget.unit != null ? ' ${widget.unit}' : '';

    final defaultStyle = AppTypography(
      fontSize: widget.fontSize ?? 24,
      fontWeight: widget.fontWeight ?? FontWeight.bold,
      color: widget.color,
    );

    return Text(
      '$formattedValue$unitText',
      style: widget.style ?? defaultStyle,
      textAlign: widget.textAlign,
    );
  }
}

/// عداد متحرك مع أيقونة وعنوان
class IconAnimatedCounter extends StatelessWidget {
  /// العنوان
  final String title;

  /// القيمة
  final double value;

  /// الأيقونة
  final IconData icon;

  /// لون الأيقونة والقيمة
  final Color color;

  /// وحدة القياس
  final String? unit;

  /// عدد الأرقام العشرية
  final int decimalPlaces;

  /// دالة عند النقر
  final VoidCallback? onTap;

  /// دالة تنسيق مخصصة
  final String Function(double)? formatter;

  const IconAnimatedCounter({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.unit,
    this.decimalPlaces = 0,
    this.onTap,
    this.formatter,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shadowColor: color.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: color.withValues(alpha: 0.1), width: 1),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing16),
              AnimatedCounter(
                value: value,
                unit: unit,
                decimalPlaces: decimalPlaces,
                color: color,
                fontSize: 28,
                fontWeight: FontWeight.bold,
                formatter: formatter,
              ),
              const SizedBox(height: AppDimensions.spacing8),
              LinearProgressIndicator(
                value: math.min(1.0, value / 100), // Ejemplo simple
                backgroundColor: color.withValues(alpha: 0.1),
                valueColor: AlwaysStoppedAnimation<Color>(color),
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
