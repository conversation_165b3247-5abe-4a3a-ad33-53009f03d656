import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:uuid/uuid.dart';
import '../../../core/models/category.dart';

import '../../../core/utils/app_logger.dart';
import '../../../core/widgets/app_drawer.dart';
import '../../../core/widgets/index.dart';
import '../presenters/expense_presenter.dart';
import '../../../core/theme/index.dart';

/// شاشة إدارة فئات المصروفات
class ExpenseCategoryScreen extends StatefulWidget {
  const ExpenseCategoryScreen({Key? key}) : super(key: key);

  @override
  State<ExpenseCategoryScreen> createState() => _ExpenseCategoryScreenState();
}

class _ExpenseCategoryScreenState extends State<ExpenseCategoryScreen> {
  final TextEditingController _searchController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  bool _isActive = true;
  bool _isEditing = false;
  String? _selectedCategoryId;

  // استخدام التحميل الكسول
  late final ExpensePresenter _expensePresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _expensePresenter = AppProviders.getLazyPresenter<ExpensePresenter>(
        () => ExpensePresenter());
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCategories();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// تحميل فئات المصروفات
  Future<void> _loadCategories() async {
    try {
      await _expensePresenter.loadExpenseCategories(
        searchQuery:
            _searchController.text.isNotEmpty ? _searchController.text : null,
      );
    } catch (e) {
      AppLogger.error('فشل في تحميل فئات المصروفات: $e');
    }
  }

  /// إضافة فئة مصروف جديدة
  Future<void> _addCategory() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final category = Category(
        id: const Uuid().v4(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        isActive: _isActive,
        type: 'expense',
      );

      final success = await _expensePresenter.addExpenseCategory(category);

      if (success && mounted) {
        _resetForm();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تمت إضافة الفئة بنجاح')),
        );
      }
    } catch (e) {
      AppLogger.error('فشل في إضافة فئة المصروف: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في إضافة الفئة: $e')),
        );
      }
    }
  }

  /// تحديث فئة مصروف
  Future<void> _updateCategory() async {
    if (!_formKey.currentState!.validate() || _selectedCategoryId == null) {
      return;
    }

    try {
      final category = Category(
        id: _selectedCategoryId!,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        isActive: _isActive,
        type: 'expense',
      );

      final success = await _expensePresenter.updateExpenseCategory(category);

      if (success && mounted) {
        _resetForm();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تحديث الفئة بنجاح')),
        );
      }
    } catch (e) {
      AppLogger.error('فشل في تحديث فئة المصروف: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحديث الفئة: $e')),
        );
      }
    }
  }

  /// حذف فئة مصروف
  Future<void> _deleteCategory(String id) async {
    try {
      // عرض مربع حوار للتأكيد
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: const Text('هل أنت متأكد من حذف هذه الفئة؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('حذف'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        final success = await _expensePresenter.deleteExpenseCategory(id);

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف الفئة بنجاح')),
          );
        }
      }
    } catch (e) {
      AppLogger.error('فشل في حذف فئة المصروف: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حذف الفئة: $e')),
        );
      }
    }
  }

  /// إعادة تعيين النموذج
  void _resetForm() {
    setState(() {
      _nameController.clear();
      _descriptionController.clear();
      _isActive = true;
      _isEditing = false;
      _selectedCategoryId = null;
    });
  }

  /// تحرير فئة
  void _editCategory(Category category) {
    setState(() {
      _selectedCategoryId = category.id;
      _nameController.text = category.name;
      _descriptionController.text = category.description ?? '';
      _isActive = category.isActive;
      _isEditing = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('فئات المصروفات'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      drawer: const AppDrawer(),
      body: ListenableBuilder(
        listenable: _expensePresenter,
        builder: (context, child) {
          if (_expensePresenter.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          final categories = _expensePresenter.categories;

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // نموذج إضافة/تحرير فئة
                Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _isEditing ? 'تحرير فئة' : 'إضافة فئة جديدة',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: AppDimensions.spacing16),
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الفئة',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال اسم الفئة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: AppDimensions.spacing8),
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'الوصف',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                      ),
                      const SizedBox(height: AppDimensions.spacing8),
                      Row(
                        children: [
                          Checkbox(
                            value: _isActive,
                            onChanged: (value) {
                              setState(() {
                                _isActive = value ?? true;
                              });
                            },
                          ),
                          const Text('نشط'),
                        ],
                      ),
                      const SizedBox(height: AppDimensions.spacing16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed:
                                  _isEditing ? _updateCategory : _addCategory,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primary,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: Text(_isEditing ? 'تحديث' : 'إضافة'),
                            ),
                          ),
                          if (_isEditing) ...[
                            const SizedBox(width: 8),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: _resetForm,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.lightTextSecondary,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 12),
                                ),
                                child: const Text('إلغاء'),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppDimensions.spacing24),
                // بحث
                TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    labelText: 'بحث',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    _loadCategories();
                  },
                ),
                const SizedBox(height: AppDimensions.spacing16),
                // قائمة الفئات
                Text(
                  'الفئات (${categories.length})',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: AppDimensions.spacing8),
                Expanded(
                  child: categories.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.category,
                                size: 64,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 16),
                              Text(
                                'لا توجد فئات مصروفات',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          itemCount: categories.length,
                          itemBuilder: (context, index) {
                            final category = categories[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                title: Text(category.name),
                                subtitle: Text(category.description ?? ''),
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    if (!category.isActive)
                                      const Padding(
                                        padding: EdgeInsets.only(left: 8.0),
                                        child: Chip(
                                          label: Text('غير نشط'),
                                          backgroundColor: AppColors.warning,
                                          labelStyle: AppTypography(
                                              color: AppColors.onPrimary),
                                        ),
                                      ),
                                    IconButton(
                                      icon: const Icon(Icons.edit,
                                          color: AppColors.primary),
                                      onPressed: () => _editCategory(category),
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.delete,
                                          color: AppColors.error),
                                      onPressed: () =>
                                          _deleteCategory(category.id),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
