import 'package:uuid/uuid.dart';
import 'dart:convert';
import 'base_model.dart';
import 'journal_entry_detail.dart';

/// أنواع القيود المحاسبية
enum JournalEntryType {
  manual,
  system,
  closing,
  opening,
  adjustment,
}

/// حالات القيد المحاسبي
enum JournalEntryStatus {
  draft,
  posted,
  reversed,
}

/// نموذج القيد المحاسبي
class JournalEntry extends BaseModel {
  final String entryNumber;
  final DateTime entryDate;
  final String? description;
  final String? reference;
  final String? referenceId; // معرف المستند المرتبط (فاتورة، دفعة، إلخ)
  final String? referenceType; // نوع المستند المرتبط
  final JournalEntryType type;
  final JournalEntryStatus status;
  final String? fiscalPeriodId;
  final String? branchId;
  final bool isRecurring;
  final Map<String, dynamic>? recurrencePattern;
  final List<JournalEntryDetail> details;

  JournalEntry({
    String? id,
    required this.entryNumber,
    required this.entryDate,
    this.description,
    this.reference,
    this.referenceId,
    this.referenceType,
    this.type = JournalEntryType.manual,
    this.status = JournalEntryStatus.draft,
    this.fiscalPeriodId,
    this.branchId,
    this.isRecurring = false,
    this.recurrencePattern,
    this.details = const [],
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا القيد مع استبدال الحقول المحددة بقيم جديدة
  JournalEntry copyWith({
    String? id,
    String? entryNumber,
    DateTime? entryDate,
    String? description,
    String? reference,
    String? referenceId,
    String? referenceType,
    JournalEntryType? type,
    JournalEntryStatus? status,
    String? fiscalPeriodId,
    String? branchId,
    bool? isRecurring,
    Map<String, dynamic>? recurrencePattern,
    List<JournalEntryDetail>? details,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return JournalEntry(
      id: id ?? this.id,
      entryNumber: entryNumber ?? this.entryNumber,
      entryDate: entryDate ?? this.entryDate,
      description: description ?? this.description,
      reference: reference ?? this.reference,
      referenceId: referenceId ?? this.referenceId,
      referenceType: referenceType ?? this.referenceType,
      type: type ?? this.type,
      status: status ?? this.status,
      fiscalPeriodId: fiscalPeriodId ?? this.fiscalPeriodId,
      branchId: branchId ?? this.branchId,
      isRecurring: isRecurring ?? this.isRecurring,
      recurrencePattern: recurrencePattern ?? this.recurrencePattern,
      details: details ?? this.details,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل القيد المحاسبي إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'entry_number': entryNumber,
      'entry_date': entryDate.toIso8601String(),
      'description': description,
      'reference': reference,
      'reference_id': referenceId,
      'reference_type': referenceType,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'fiscal_period_id': fiscalPeriodId,
      'branch_id': branchId,
      'is_recurring': isRecurring ? 1 : 0,
      'recurrence_pattern':
          recurrencePattern != null ? jsonEncode(recurrencePattern) : null,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء قيد محاسبي من Map
  factory JournalEntry.fromMap(Map<String, dynamic> map,
      [List<JournalEntryDetail>? details]) {
    return JournalEntry(
      id: map['id'],
      entryNumber: map['entry_number'],
      entryDate: map['entry_date'] != null
          ? DateTime.parse(map['entry_date'])
          : DateTime.now(),
      description: map['description'],
      reference: map['reference'],
      referenceId: map['reference_id'],
      referenceType: map['reference_type'],
      type: _parseJournalEntryType(map['type']),
      status: _parseJournalEntryStatus(map['status']),
      fiscalPeriodId: map['fiscal_period_id'],
      branchId: map['branch_id'],
      isRecurring: map['is_recurring'] == 1,
      recurrencePattern: map['recurrence_pattern'] != null
          ? jsonDecode(map['recurrence_pattern'])
          : null,
      details: details ?? [],
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحليل نوع القيد المحاسبي من النص
  static JournalEntryType _parseJournalEntryType(String? typeString) {
    switch (typeString) {
      case 'manual':
        return JournalEntryType.manual;
      case 'system':
        return JournalEntryType.system;
      case 'closing':
        return JournalEntryType.closing;
      case 'opening':
        return JournalEntryType.opening;
      case 'adjustment':
        return JournalEntryType.adjustment;
      default:
        return JournalEntryType.manual;
    }
  }

  /// تحليل حالة القيد المحاسبي من النص
  static JournalEntryStatus _parseJournalEntryStatus(String? statusString) {
    switch (statusString) {
      case 'draft':
        return JournalEntryStatus.draft;
      case 'posted':
        return JournalEntryStatus.posted;
      case 'reversed':
        return JournalEntryStatus.reversed;
      default:
        return JournalEntryStatus.draft;
    }
  }

  /// حساب إجمالي المدين
  double get totalDebit {
    return details.fold(0.0, (sum, detail) => sum + detail.debit);
  }

  /// حساب إجمالي الدائن
  double get totalCredit {
    return details.fold(0.0, (sum, detail) => sum + detail.credit);
  }

  /// التحقق من توازن القيد
  bool get isBalanced {
    return totalDebit == totalCredit;
  }

  @override
  String toString() {
    return 'JournalEntry(id: $id, entryNumber: $entryNumber, entryDate: $entryDate, type: $type, status: $status)';
  }
}
