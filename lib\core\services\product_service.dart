import '../database/database_service.dart';
import '../models/product.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';

/// خدمة للعمليات المتعلقة بالمنتجات
class ProductService {
  // نمط Singleton
  static final ProductService _instance = ProductService._internal();
  factory ProductService() => _instance;
  ProductService._internal();

  final DatabaseService _db = DatabaseService.instance;

  /// الحصول على جميع المنتجات
  Future<List<Product>> getAllProducts({
    bool includeInactive = false,
    String? searchQuery,
    String? categoryId,
    bool onlyServices = false,
    bool excludeServices = false,
    int? limit,
    int? offset,
  }) async {
    try {
      AppLogger.info('الحصول على جميع المنتجات');

      // بناء شرط WHERE
      String whereClause = 'p.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (!includeInactive) {
        whereClause += ' AND p.is_active = 1';
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause +=
            ' AND (p.name LIKE ? OR p.code LIKE ? OR p.barcode LIKE ?)';
        whereArgs
            .addAll(['%$searchQuery%', '%$searchQuery%', '%$searchQuery%']);
      }

      if (categoryId != null) {
        whereClause += ' AND p.category_id = ?';
        whereArgs.add(categoryId);
      }

      if (onlyServices) {
        whereClause += ' AND p.is_service = 1';
      } else if (excludeServices) {
        whereClause += ' AND p.is_service = 0';
      }

      // استعلام قاعدة البيانات مع JOIN للحصول على أسماء الفئات والوحدات
      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          p.*,
          c.name as category_name,
          u.name as unit_name
        FROM ${DatabaseService.tableProducts} p
        LEFT JOIN ${DatabaseService.tableCategories} c ON p.category_id = c.id
        LEFT JOIN ${DatabaseService.tableUnits} u ON p.unit_id = u.id
        WHERE $whereClause
        ORDER BY p.name ASC
        ${limit != null ? 'LIMIT $limit' : ''}
        ${offset != null ? 'OFFSET $offset' : ''}
      ''', whereArgs);

      // تحويل إلى كائنات Product
      return List.generate(maps.length, (i) {
        return Product.fromMap(maps[i]);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع المنتجات',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على منتج بواسطة المعرف
  Future<Product?> getProductById(String id) async {
    try {
      AppLogger.info('الحصول على منتج بواسطة المعرف: $id');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          p.*,
          c.name as category_name,
          u.name as unit_name
        FROM ${DatabaseService.tableProducts} p
        LEFT JOIN ${DatabaseService.tableCategories} c ON p.category_id = c.id
        LEFT JOIN ${DatabaseService.tableUnits} u ON p.unit_id = u.id
        WHERE p.id = ? AND p.is_deleted = 0
      ''', [id]);

      if (maps.isEmpty) {
        return null;
      }

      return Product.fromMap(maps.first);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على منتج بواسطة المعرف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return null;
    }
  }

  /// الحصول على منتج بواسطة الباركود
  Future<Product?> getProductByBarcode(String barcode) async {
    try {
      AppLogger.info('الحصول على منتج بواسطة الباركود: $barcode');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          p.*,
          c.name as category_name,
          u.name as unit_name
        FROM ${DatabaseService.tableProducts} p
        LEFT JOIN ${DatabaseService.tableCategories} c ON p.category_id = c.id
        LEFT JOIN ${DatabaseService.tableUnits} u ON p.unit_id = u.id
        WHERE p.barcode = ? AND p.is_deleted = 0 AND p.is_active = 1
      ''', [barcode]);

      if (maps.isEmpty) {
        return null;
      }

      return Product.fromMap(maps.first);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على منتج بواسطة الباركود',
        error: e,
        stackTrace: stackTrace,
        context: {'barcode': barcode},
      );
      return null;
    }
  }

  /// إضافة منتج جديد
  Future<bool> addProduct(Product product, {String? userId}) async {
    try {
      AppLogger.info('إضافة منتج جديد: ${product.name}');

      final productMap = product.toMap();

      // تعيين created_by إذا تم توفيره
      if (userId != null) {
        productMap['created_by'] = userId;
      }

      await _db.insert(DatabaseService.tableProducts, productMap);

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة منتج',
        error: e,
        stackTrace: stackTrace,
        context: {'product': product.toString()},
      );
      return false;
    }
  }

  /// تحديث منتج موجود
  Future<bool> updateProduct(Product product, {String? userId}) async {
    try {
      AppLogger.info('تحديث منتج: ${product.name}');

      final productMap = product.toMap();

      // تعيين updated_at و updated_by
      productMap['updated_at'] = DateTime.now().toIso8601String();
      if (userId != null) {
        productMap['updated_by'] = userId;
      }

      await _db.update(
        DatabaseService.tableProducts,
        productMap,
        where: 'id = ?',
        whereArgs: [product.id],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث منتج',
        error: e,
        stackTrace: stackTrace,
        context: {'product': product.toString()},
      );
      return false;
    }
  }

  /// حذف منتج (حذف منطقي)
  Future<bool> deleteProduct(String id, {String? userId}) async {
    try {
      AppLogger.info('حذف منتج: $id');

      final now = DateTime.now().toIso8601String();

      await _db.update(
        DatabaseService.tableProducts,
        {
          'is_deleted': 1,
          'updated_at': now,
          'updated_by': userId,
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف منتج',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// الحصول على عدد المنتجات
  Future<int> getProductCount(
      {bool includeInactive = false,
      bool onlyServices = false,
      bool excludeServices = false}) async {
    try {
      AppLogger.info('الحصول على عدد المنتجات');

      String whereClause = 'is_deleted = 0';
      if (!includeInactive) {
        whereClause += ' AND is_active = 1';
      }

      if (onlyServices) {
        whereClause += ' AND is_service = 1';
      } else if (excludeServices) {
        whereClause += ' AND is_service = 0';
      }

      final result = await _db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseService.tableProducts} WHERE $whereClause',
      );

      return result.first['count'] as int;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على عدد المنتجات',
        error: e,
        stackTrace: stackTrace,
      );
      return 0;
    }
  }
}
