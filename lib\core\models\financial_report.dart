import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// أنواع التقارير المالية
enum FinancialReportType {
  balanceSheet,
  incomeStatement,
  cashFlow,
  equityChanges,
  custom,
}

/// نموذج التقرير المالي الموحد
class FinancialReport extends BaseModel {
  // معلومات أساسية
  final String title;
  final String subtitle;
  final FinancialReportType type;
  
  // معلومات الفترة
  final DateTime reportDate;
  final DateTime startDate;
  final DateTime endDate;
  
  // محتوى التقرير
  final Map<String, dynamic> details;
  final Map<String, dynamic> summary;
  
  // معلومات إضافية
  final String? notes;
  final String? currency;
  final bool isPublished;
  final String? approvedBy;
  final DateTime? approvedAt;

  FinancialReport({
    String? id,
    required this.title,
    required this.subtitle,
    required this.type,
    required this.reportDate,
    required this.startDate,
    required this.endDate,
    required this.details,
    required this.summary,
    this.notes,
    this.currency = 'SAR',
    this.isPublished = false,
    this.approvedBy,
    this.approvedAt,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا التقرير مع استبدال الحقول المحددة بقيم جديدة
  FinancialReport copyWith({
    String? id,
    String? title,
    String? subtitle,
    FinancialReportType? type,
    DateTime? reportDate,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? details,
    Map<String, dynamic>? summary,
    String? notes,
    String? currency,
    bool? isPublished,
    String? approvedBy,
    DateTime? approvedAt,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return FinancialReport(
      id: id ?? this.id,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      type: type ?? this.type,
      reportDate: reportDate ?? this.reportDate,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      details: details ?? this.details,
      summary: summary ?? this.summary,
      notes: notes ?? this.notes,
      currency: currency ?? this.currency,
      isPublished: isPublished ?? this.isPublished,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل التقرير إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'type': type.toString().split('.').last,
      'report_date': reportDate.toIso8601String(),
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'details': jsonEncode(details),
      'summary': jsonEncode(summary),
      'notes': notes,
      'currency': currency,
      'is_published': isPublished ? 1 : 0,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء تقرير من Map
  factory FinancialReport.fromMap(Map<String, dynamic> map) {
    return FinancialReport(
      id: map['id'],
      title: map['title'] ?? '',
      subtitle: map['subtitle'] ?? '',
      type: _parseReportType(map['type']),
      reportDate: map['report_date'] != null
          ? DateTime.parse(map['report_date'])
          : DateTime.now(),
      startDate: map['start_date'] != null
          ? DateTime.parse(map['start_date'])
          : DateTime.now().subtract(const Duration(days: 30)),
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'])
          : DateTime.now(),
      details: map['details'] != null
          ? (map['details'] is String
              ? jsonDecode(map['details'])
              : map['details'] as Map<String, dynamic>)
          : {},
      summary: map['summary'] != null
          ? (map['summary'] is String
              ? jsonDecode(map['summary'])
              : map['summary'] as Map<String, dynamic>)
          : {},
      notes: map['notes'],
      currency: map['currency'] ?? 'SAR',
      isPublished: map['is_published'] == 1,
      approvedBy: map['approved_by'],
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'])
          : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحليل نوع التقرير من النص
  static FinancialReportType _parseReportType(String? typeString) {
    switch (typeString) {
      case 'balanceSheet':
        return FinancialReportType.balanceSheet;
      case 'incomeStatement':
        return FinancialReportType.incomeStatement;
      case 'cashFlow':
        return FinancialReportType.cashFlow;
      case 'equityChanges':
        return FinancialReportType.equityChanges;
      default:
        return FinancialReportType.custom;
    }
  }

  /// تحويل التقرير إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء تقرير من JSON
  factory FinancialReport.fromJson(String source) =>
      FinancialReport.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'FinancialReport(id: $id, title: $title, type: $type)';
  }
  
  /// إنشاء تقرير فارغ
  factory FinancialReport.empty() {
    return FinancialReport(
      title: '',
      subtitle: '',
      type: FinancialReportType.custom,
      reportDate: DateTime.now(),
      startDate: DateTime.now().subtract(const Duration(days: 30)),
      endDate: DateTime.now(),
      details: {},
      summary: {},
    );
  }
  
  /// إنشاء تقرير قائمة المركز المالي
  factory FinancialReport.balanceSheet({
    required DateTime reportDate,
    required Map<String, dynamic> details,
    Map<String, dynamic>? summary,
    String? createdBy,
  }) {
    return FinancialReport(
      title: 'قائمة المركز المالي',
      subtitle: 'كما في ${reportDate.toString().split(' ')[0]}',
      type: FinancialReportType.balanceSheet,
      reportDate: reportDate,
      startDate: DateTime(reportDate.year, 1, 1),
      endDate: reportDate,
      details: details,
      summary: summary ?? {},
      createdBy: createdBy,
    );
  }
  
  /// إنشاء تقرير قائمة الدخل
  factory FinancialReport.incomeStatement({
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, dynamic> details,
    Map<String, dynamic>? summary,
    String? createdBy,
  }) {
    return FinancialReport(
      title: 'قائمة الدخل',
      subtitle: 'للفترة من ${startDate.toString().split(' ')[0]} إلى ${endDate.toString().split(' ')[0]}',
      type: FinancialReportType.incomeStatement,
      reportDate: DateTime.now(),
      startDate: startDate,
      endDate: endDate,
      details: details,
      summary: summary ?? {},
      createdBy: createdBy,
    );
  }
  
  /// إنشاء تقرير قائمة التدفقات النقدية
  factory FinancialReport.cashFlow({
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, dynamic> details,
    Map<String, dynamic>? summary,
    String? createdBy,
  }) {
    return FinancialReport(
      title: 'قائمة التدفقات النقدية',
      subtitle: 'للفترة من ${startDate.toString().split(' ')[0]} إلى ${endDate.toString().split(' ')[0]}',
      type: FinancialReportType.cashFlow,
      reportDate: DateTime.now(),
      startDate: startDate,
      endDate: endDate,
      details: details,
      summary: summary ?? {},
      createdBy: createdBy,
    );
  }
}
