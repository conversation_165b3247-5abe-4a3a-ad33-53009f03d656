import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

import '../../../core/models/warehouse.dart';
import '../../../core/database/database_service.dart';
import '../../../core/utils/app_logger.dart';

/// مقدم المخازن الموحد
///
/// يوفر هذا المقدم واجهة للتعامل مع المخازن
/// تم تحديث المقدم ليركز فقط على إدارة المستودعات
class WarehousePresenter with ChangeNotifier {
  final _db = DatabaseService.instance;

  List<Warehouse> _warehouses = [];

  Warehouse? _selectedWarehouse;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Warehouse> get warehouses => _warehouses;
  Warehouse? get selectedWarehouse => _selectedWarehouse;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get errorMessage => _error;

  /// تهيئة المقدم
  Future<void> init() async {
    await loadWarehouses();
  }

  /// تحميل جميع المخازن
  Future<void> loadWarehouses({bool includeInactive = false}) async {
    _setLoading(true);
    try {
      // بناء استعلام SQL
      String query = 'SELECT * FROM warehouses WHERE 1=1';

      if (!includeInactive) {
        query += ' AND is_active = 1';
      }

      query += ' ORDER BY is_default DESC, name ASC';

      // تنفيذ الاستعلام
      final results = await _db.rawQuery(query);

      // تحويل النتائج إلى كائنات Warehouse
      _warehouses = results.map((item) => Warehouse.fromMap(item)).toList();

      // إذا لم يكن هناك مخزن محدد، نختار المخزن الافتراضي
      if (_selectedWarehouse == null && _warehouses.isNotEmpty) {
        final defaultWarehouse = _warehouses.firstWhere(
          (w) => w.isDefault,
          orElse: () => _warehouses.first,
        );
        _selectedWarehouse = defaultWarehouse;
      }

      _setError(null);
    } catch (e) {
      _setError('فشل تحميل المخازن: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// تحديد مخزن
  void selectWarehouse(Warehouse warehouse) {
    _selectedWarehouse = warehouse;
    notifyListeners();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setError(String? value) {
    _error = value;
    notifyListeners();
  }

  /// إضافة مخزن جديد
  Future<bool> addWarehouse(Warehouse warehouse) async {
    _setLoading(true);
    try {
      // إنشاء معرف جديد
      final id = const Uuid().v4();

      // إعداد بيانات المخزن
      final now = DateTime.now().toIso8601String();
      final warehouseData = {
        ...warehouse.toMap(),
        'id': id,
        'created_at': now,
        'updated_at': now,
      };

      // تنفيذ استعلام SQL لإضافة المخزن (محسن - إزالة branch_name)
      await _db.rawQuery('''
        INSERT INTO warehouses (
          id, name, description, address, is_default, is_active,
          branch_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      ''', [
        warehouseData['id'],
        warehouseData['name'],
        warehouseData['description'],
        warehouseData['address'],
        warehouseData['is_default'] ? 1 : 0,
        warehouseData['is_active'] ? 1 : 0,
        warehouseData['branch_id'],
        warehouseData['created_at'],
        warehouseData['updated_at'],
      ]);

      // إضافة المخزن إلى القائمة
      final newWarehouse = warehouse.copyWith(id: id);
      _warehouses.add(newWarehouse);

      // إذا كان المخزن افتراضياً، نقوم بتحديث المخازن الأخرى
      if (warehouse.isDefault) {
        // تحديث المخازن الأخرى في قاعدة البيانات
        await _db.rawQuery('''
          UPDATE warehouses
          SET is_default = 0, updated_at = ?
          WHERE id != ?
        ''', [now, id]);

        // تحديث المخازن في الذاكرة
        _warehouses = _warehouses.map((w) {
          if (w.id != id) {
            return w.copyWith(isDefault: false);
          }
          return w;
        }).toList();
      }

      notifyListeners();
      _setError(null);
      return true;
    } catch (e) {
      _setError('فشل إضافة المخزن: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث مخزن
  Future<bool> updateWarehouse(Warehouse warehouse) async {
    _setLoading(true);
    try {
      // إعداد بيانات المخزن
      final now = DateTime.now().toIso8601String();
      final warehouseData = {
        ...warehouse.toMap(),
        'updated_at': now,
      };

      // تنفيذ استعلام SQL لتحديث المخزن (محسن - إزالة branch_name)
      await _db.rawQuery('''
        UPDATE warehouses SET
          name = ?, description = ?, address = ?, is_default = ?, is_active = ?,
          branch_id = ?, updated_at = ?
        WHERE id = ?
      ''', [
        warehouseData['name'],
        warehouseData['description'],
        warehouseData['address'],
        warehouseData['is_default'] ? 1 : 0,
        warehouseData['is_active'] ? 1 : 0,
        warehouseData['branch_id'],
        warehouseData['updated_at'],
        warehouseData['id'],
      ]);

      // تحديث المخزن في القائمة
      final index = _warehouses.indexWhere((w) => w.id == warehouse.id);
      if (index >= 0) {
        _warehouses[index] = warehouse;
      }

      // إذا كان المخزن افتراضياً، نقوم بتحديث المخازن الأخرى
      if (warehouse.isDefault) {
        // تحديث المخازن الأخرى في قاعدة البيانات
        await _db.rawQuery('''
          UPDATE warehouses
          SET is_default = 0, updated_at = ?
          WHERE id != ?
        ''', [now, warehouse.id]);

        // تحديث المخازن في الذاكرة
        _warehouses = _warehouses.map((w) {
          if (w.id != warehouse.id) {
            return w.copyWith(isDefault: false);
          }
          return w;
        }).toList();
      }

      // إذا كان المخزن المحدد هو المخزن الذي تم تحديثه
      if (_selectedWarehouse?.id == warehouse.id) {
        _selectedWarehouse = warehouse;
      }

      notifyListeners();
      _setError(null);
      return true;
    } catch (e) {
      _setError('فشل تحديث المخزن: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف مخزن
  Future<bool> deleteWarehouse(String id) async {
    _setLoading(true);
    try {
      // تنفيذ استعلام SQL لحذف المخزن (حذف ناعم)
      final now = DateTime.now().toIso8601String();
      await _db.rawQuery('''
        UPDATE warehouses
        SET is_active = 0, updated_at = ?
        WHERE id = ?
      ''', [now, id]);

      // حذف المخزن من القائمة
      _warehouses.removeWhere((w) => w.id == id);

      // إذا كان المخزن المحدد هو المخزن الذي تم حذفه
      if (_selectedWarehouse?.id == id) {
        _selectedWarehouse = _warehouses.isNotEmpty ? _warehouses.first : null;
      }

      notifyListeners();
      _setError(null);
      return true;
    } catch (e) {
      _setError('فشل حذف المخزن: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على المخزن الافتراضي
  Warehouse? getDefaultWarehouse() {
    if (_warehouses.isEmpty) return null;

    try {
      return _warehouses.firstWhere(
        (w) => w.isDefault,
        orElse: () => _warehouses.first,
      );
    } catch (e) {
      AppLogger.error('خطأ في الحصول على المخزن الافتراضي', error: e);
      return null;
    }
  }

  /// البحث عن مخازن
  Future<List<Warehouse>> searchWarehouses(String query) async {
    try {
      if (query.isEmpty) {
        return _warehouses;
      }

      // تصفية المخازن محليًا
      return _warehouses.where((warehouse) {
        return warehouse.name.toLowerCase().contains(query.toLowerCase()) ||
            (warehouse.code?.toLowerCase().contains(query.toLowerCase()) ??
                false) ||
            (warehouse.address?.toLowerCase().contains(query.toLowerCase()) ??
                false);
      }).toList();
    } catch (e) {
      AppLogger.error('خطأ في البحث عن مخازن', error: e);
      return [];
    }
  }
}
