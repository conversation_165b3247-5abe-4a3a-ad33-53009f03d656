import 'package:flutter/material.dart';
import '../../../core/models/purchase.dart';
import '../presenters/purchase_presenter.dart';

import 'purchase_form_screen.dart';
import 'purchase_details_screen.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد
import '../../../core/providers/app_providers.dart'; // للتحميل الكسول

/// شاشة عرض فواتير المشتريات
class PurchasesScreen extends StatefulWidget {
  const PurchasesScreen({Key? key}) : super(key: key);

  @override
  State<PurchasesScreen> createState() => _PurchasesScreenState();
}

class _PurchasesScreenState extends State<PurchasesScreen> {
  late PurchasePresenter _purchasePresenter;
  final _searchController = TextEditingController();
  String? _selectedStatus;
  DateTimeRange? _dateRange;
  String _selectedDateFilter = 'all';

  @override
  void initState() {
    super.initState();
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    _purchasePresenter = AppProviders.getLazyPresenter<PurchasePresenter>(
      () => PurchasePresenter(),
    );
    _purchasePresenter.init();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// عرض رسالة في شريط Snackbar
  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'فواتير المشتريات',
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToPurchaseForm(),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildDateFilter(),
          _buildStatsCards(),
          Expanded(
            child: _buildPurchasesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث عن فواتير المشتريات...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _purchasePresenter.searchQuery = null;
                  },
                )
              : null,
        ),
        onChanged: (value) {
          _purchasePresenter.searchQuery = value.isNotEmpty ? value : null;
        },
      ),
    );
  }

  Widget _buildDateFilter() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('all', 'الكل'),
                  const SizedBox(width: 8),
                  _buildFilterChip('today', 'اليوم'),
                  const SizedBox(width: 8),
                  _buildFilterChip('week', 'هذا الأسبوع'),
                  const SizedBox(width: 8),
                  _buildFilterChip('month', 'هذا الشهر'),
                  const SizedBox(width: 8),
                  _buildFilterChip('custom', 'مخصص'),
                ],
              ),
            ),
          ),
          if (_selectedDateFilter == 'custom')
            IconButton(
              icon: const Icon(Icons.date_range),
              onPressed: _selectDateRange,
            ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    return FilterChip(
      selected: _selectedDateFilter == value,
      label: Text(label),
      onSelected: (selected) {
        setState(() {
          _selectedDateFilter = value;
          _updateDateRange(value);
        });
      },
    );
  }

  void _updateDateRange(String filter) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (filter) {
      case 'today':
        _dateRange = DateTimeRange(
          start: today,
          end: today.add(const Duration(days: 1)),
        );
        break;
      case 'week':
        // يبدأ الأسبوع من يوم الأحد
        final startOfWeek = today.subtract(Duration(days: today.weekday % 7));
        _dateRange = DateTimeRange(
          start: startOfWeek,
          end: startOfWeek.add(const Duration(days: 7)),
        );
        break;
      case 'month':
        _dateRange = DateTimeRange(
          start: DateTime(now.year, now.month, 1),
          end: DateTime(now.year, now.month + 1, 0),
        );
        break;
      case 'custom':
        // لا تغير نطاق التاريخ، سيتم تحديده من خلال مربع حوار اختيار التاريخ
        if (_dateRange == null) {
          _selectDateRange();
        }
        break;
      default:
        _dateRange = null;
        break;
    }

    _purchasePresenter.dateRange = _dateRange;
  }

  Future<void> _selectDateRange() async {
    final initialDateRange = _dateRange ??
        DateTimeRange(
          start: DateTime.now().subtract(const Duration(days: 7)),
          end: DateTime.now(),
        );

    final newDateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: initialDateRange,
    );

    if (newDateRange != null) {
      setState(() {
        _dateRange = newDateRange;
        _selectedDateFilter = 'custom';
        _purchasePresenter.dateRange = _dateRange;
      });
    }
  }

  Widget _buildStatsCards() {
    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListenableBuilder(
        listenable: _purchasePresenter,
        builder: (context, child) {
          return FutureBuilder<Map<String, dynamic>>(
            future: _purchasePresenter.getPurchaseStatistics(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              final stats = snapshot.data ??
                  {
                    'daily_total': 0.0,
                    'monthly_total': 0.0,
                    'unpaid_count': 0,
                    'unpaid_total': 0.0,
                  };

              return ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  _buildStatCard(
                    'اليوم',
                    '${stats['daily_total']} ر.س',
                    Icons.today,
                    AppColors.info,
                  ),
                  _buildStatCard(
                    'الشهر',
                    '${stats['monthly_total']} ر.س',
                    Icons.calendar_month,
                    AppColors.success,
                  ),
                  _buildStatCard(
                    'غير مدفوع',
                    '${stats['unpaid_count']} فاتورة',
                    Icons.payment,
                    AppColors.warning,
                  ),
                  _buildStatCard(
                    'المستحقات',
                    '${stats['unpaid_total']} ر.س',
                    Icons.account_balance_wallet,
                    AppColors.error,
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
      child: Container(
        width: 150,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Text(title, style: AppTypography(color: color)),
              ],
            ),
            const Spacer(),
            Text(
              value,
              style: const AppTypography(
                  fontWeight: FontWeight.bold, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPurchasesList() {
    return ListenableBuilder(
      listenable: _purchasePresenter,
      builder: (context, child) {
        if (_purchasePresenter.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_purchasePresenter.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  'حدث خطأ أثناء تحميل فواتير المشتريات',
                  style: AppTypography(color: AppColors.error),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _purchasePresenter.loadPurchases,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (_purchasePresenter.purchases.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('لا توجد فواتير مشتريات'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _navigateToPurchaseForm,
                  child: const Text('إضافة فاتورة مشتريات'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _purchasePresenter.loadPurchases,
          child: ListView.builder(
            itemCount: _purchasePresenter.purchases.length,
            padding: const EdgeInsets.all(16),
            itemBuilder: (context, index) {
              final purchase = _purchasePresenter.purchases[index];
              return _buildPurchaseItem(purchase);
            },
          ),
        );
      },
    );
  }

  Widget _buildPurchaseItem(Purchase purchase) {
    // سيتم استخدام هذا المتغير في المستقبل عند تنفيذ وضع الألوان الداكنة
    // final isDark = Theme.of(context).brightness == Brightness.dark;
    final isCredit = purchase.isCredit;
    final isPaid = purchase.isPaid;
    final isPartiallyPaid = purchase.isPartiallyPaid;

    Color statusColor;
    if (purchase.status == 'cancelled') {
      statusColor = AppColors.statusCancelled;
    } else if (isCredit && !isPaid) {
      statusColor = AppColors.error;
    } else if (isPartiallyPaid) {
      statusColor = AppColors.warning;
    } else {
      statusColor = AppColors.success;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _viewPurchaseDetails(purchase),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          'فاتورة مشتريات رقم [${purchase.referenceNumber}]',
                          style:
                              const AppTypography(fontWeight: FontWeight.bold),
                        ),
                        if (isCredit)
                          Container(
                            margin: const EdgeInsets.only(right: 8),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.error.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.circle,
                                  size: 8,
                                  color: AppColors.lightTextSecondary,
                                ),
                                SizedBox(width: 4),
                                Text(
                                  'آجل',
                                  style: AppTypography(
                                    fontSize: 12,
                                    color: AppColors.lightTextSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert),
                    onSelected: (value) =>
                        _handleMenuItemSelected(value, purchase),
                    itemBuilder: (context) => [
                      const PopupMenuItem<String>(
                        value: 'view',
                        child: Row(
                          children: [
                            Icon(Icons.visibility),
                            SizedBox(width: 8),
                            Text('عرض'),
                          ],
                        ),
                      ),
                      if (purchase.status != 'cancelled')
                        const PopupMenuItem<String>(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                      if (purchase.status != 'cancelled')
                        const PopupMenuItem<String>(
                          value: 'cancel',
                          child: Row(
                            children: [
                              Icon(Icons.cancel),
                              SizedBox(width: 8),
                              Text('إلغاء'),
                            ],
                          ),
                        ),
                      if (purchase.status == 'cancelled')
                        const PopupMenuItem<String>(
                          value: 'activate',
                          child: Row(
                            children: [
                              Icon(Icons.restore),
                              SizedBox(width: 8),
                              Text('تنشيط'),
                            ],
                          ),
                        ),
                      const PopupMenuItem<String>(
                        value: 'print',
                        child: Row(
                          children: [
                            Icon(Icons.print),
                            SizedBox(width: 8),
                            Text('طباعة'),
                          ],
                        ),
                      ),
                      const PopupMenuItem<String>(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: AppColors.error),
                            SizedBox(width: 8),
                            Text('حذف',
                                style: AppTypography(color: AppColors.error)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const Divider(),
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    '${purchase.date.day}/${purchase.date.month}/${purchase.date.year}',
                  ),
                  const SizedBox(width: 16),
                  const Icon(Icons.access_time, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    '${purchase.date.hour}:${purchase.date.minute.toString().padLeft(2, '0')}',
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.person, size: 16),
                  const SizedBox(width: 8),
                  Text(purchase.supplierName ?? 'غير محدد'),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(
                          alpha:
                              0.1), // استخدام withValues بدلاً من withOpacity
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.circle, size: 8, color: statusColor),
                        const SizedBox(width: 4),
                        Text(
                          purchase.status == 'cancelled'
                              ? 'ملغاة'
                              : purchase.paymentStatusText,
                          style: AppTypography(
                            fontSize: 12,
                            color: statusColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('الإجمالي:'),
                  Text(
                    '${purchase.total} ر.س',
                    style: const AppTypography(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              if (isPartiallyPaid) ...[
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('المدفوع:'),
                    Text(
                      '${purchase.paid} ر.س',
                      style: const AppTypography(color: AppColors.successDark),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('المتبقي:'),
                    Text(
                      '${purchase.remaining} ر.س',
                      style: const AppTypography(color: AppColors.errorDark),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية فواتير المشتريات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String?>(
              decoration: const InputDecoration(labelText: 'حالة الفاتورة'),
              value: _selectedStatus,
              items: const [
                DropdownMenuItem<String?>(
                  value: null,
                  child: Text('الكل'),
                ),
                DropdownMenuItem<String>(
                  value: 'pending',
                  child: Text('قيد الانتظار'),
                ),
                DropdownMenuItem<String>(
                  value: 'completed',
                  child: Text('مكتملة'),
                ),
                DropdownMenuItem<String>(
                  value: 'cancelled',
                  child: Text('ملغاة'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              _purchasePresenter.selectedStatus = _selectedStatus;
              Navigator.pop(context);
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _navigateToPurchaseForm() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PurchaseFormScreen(),
      ),
    ).then((_) => _purchasePresenter.loadPurchases());
  }

  void _viewPurchaseDetails(Purchase purchase) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PurchaseDetailsScreen(purchase: purchase),
      ),
    ).then((_) => _purchasePresenter.loadPurchases());
  }

  void _handleMenuItemSelected(String value, Purchase purchase) {
    switch (value) {
      case 'view':
        _viewPurchaseDetails(purchase);
        break;
      case 'edit':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PurchaseFormScreen(purchase: purchase),
          ),
        ).then((_) => _purchasePresenter.loadPurchases());
        break;
      case 'cancel':
        _showConfirmationDialog(
          'هل أنت متأكد من إلغاء هذه الفاتورة؟',
          () =>
              _purchasePresenter.changePurchaseStatus(purchase.id, 'cancelled'),
        );
        break;
      case 'activate':
        _showConfirmationDialog(
          'هل أنت متأكد من تنشيط هذه الفاتورة؟',
          () =>
              _purchasePresenter.changePurchaseStatus(purchase.id, 'completed'),
        );
        break;
      case 'print':
        // تنفيذ عملية الطباعة
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('جاري تنفيذ الطباعة...')),
        );
        break;
      case 'delete':
        _showConfirmationDialog(
          'هل أنت متأكد من حذف هذه الفاتورة؟',
          () => _purchasePresenter.deletePurchase(purchase.id),
        );
        break;
    }
  }

  void _showConfirmationDialog(
      String message, Future<bool> Function() onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await onConfirm();

              // التحقق من أن الشاشة لا تزال مثبتة قبل استخدام context
              if (!mounted) return;

              // استخدام _showSnackBar بدلاً من استخدام context مباشرة
              if (success) {
                _showSnackBar('تمت العملية بنجاح');
              } else {
                _showSnackBar(
                  _purchasePresenter.error ?? 'حدث خطأ أثناء تنفيذ العملية',
                  isError: true,
                );
              }
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }
}
