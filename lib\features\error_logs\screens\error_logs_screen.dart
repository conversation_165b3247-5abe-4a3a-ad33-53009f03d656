import 'package:flutter/material.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';

class ErrorLogsScreen extends StatefulWidget {
  const ErrorLogsScreen({super.key});

  @override
  State<ErrorLogsScreen> createState() => _ErrorLogsScreenState();
}

class _ErrorLogsScreenState extends State<ErrorLogsScreen> {
  @override
  Widget build(BuildContext context) {
    final errors = ErrorTracker.getRecentErrors();
    final stats = ErrorTracker.getErrorStats();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Error Logs'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          _buildErrorStats(stats),
          Expanded(
            child: _buildErrorList(errors),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          ErrorTracker.clearHistory();
          setState(() {});
        },
        backgroundColor: AppColors.error,
        child: const Icon(Icons.delete_sweep, color: AppColors.onPrimary),
      ),
    );
  }

  Widget _buildErrorStats(Map<String, dynamic> stats) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Error Statistics',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Errors',
                  stats['totalErrors'].toString(),
                  Icons.error_outline,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Error Rate',
                  '${stats['recentErrorRate'].toStringAsFixed(2)}/min',
                  Icons.speed,
                ),
              ),
            ],
          ),
          if (stats['mostCommonError'] != null) ...[
            const SizedBox(height: AppDimensions.spacing16),
            AkCard(
              child: ListTile(
                title: const Text('Most Common Error'),
                subtitle: Text(
                  '${stats['mostCommonError']} (${stats['mostCommonErrorCount']} times)',
                ),
                leading: const Icon(Icons.warning_amber_rounded),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 24),
            const SizedBox(height: AppDimensions.spacing8),
            Text(
              title,
              style: const AppTypography(fontSize: 12),
            ),
            const SizedBox(height: AppDimensions.spacing4),
            Text(
              value,
              style: const AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorList(List<ErrorRecord> errors) {
    if (errors.isEmpty) {
      return const Center(
        child: Text('No errors recorded'),
      );
    }

    return ListView.builder(
      itemCount: errors.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        final error = errors[index];
        return AkCard(
          padding: const EdgeInsets.all(0),
          child: ExpansionTile(
            title: Text(error.message),
            subtitle: Text(
              error.timestamp.toString(),
              style: const AppTypography(fontSize: 12),
            ),
            leading: const Icon(Icons.error_outline, color: AppColors.error),
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (error.context != null) ...[
                      const Text(
                        'Context:',
                        style: AppTypography(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: AppDimensions.spacing8),
                      Text(error.context.toString()),
                      const SizedBox(height: AppDimensions.spacing16),
                    ],
                    const Text(
                      'Stack Trace:',
                      style: AppTypography(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: AppDimensions.spacing8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        error.stackTrace.toString(),
                        style: const AppTypography(
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
