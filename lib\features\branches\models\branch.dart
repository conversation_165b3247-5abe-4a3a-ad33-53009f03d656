import 'dart:convert';
import 'package:uuid/uuid.dart';
import '../../../core/models/base_model.dart';

/// نموذج الفرع الموحد
/// تم توحيده من جميع نماذج الفروع في المشروع
class Branch extends BaseModel {
  // معلومات أساسية
  final String name;
  final String? code;
  final String? description;

  // معلومات الاتصال
  final String? address;
  final String? phone;
  final String? email;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;

  // معلومات الحالة
  final bool isActive;
  final bool isMain;
  final bool isDefault;

  // معلومات إضافية
  final String? managerId; // فقط المعرف - بدون اسم مكرر
  final Map<String, dynamic>? metadata;

  Branch({
    String? id,
    required this.name,
    this.code,
    this.description,
    this.address,
    this.phone,
    this.email,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.isActive = true,
    this.isMain = false,
    this.isDefault = false,
    this.managerId,
    this.metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا الفرع مع استبدال الحقول المحددة بقيم جديدة
  Branch copyWith({
    String? id,
    String? name,
    String? code,
    String? description,
    String? address,
    String? phone,
    String? email,
    String? city,
    String? state,
    String? country,
    String? postalCode,
    bool? isActive,
    bool? isMain,
    bool? isDefault,
    String? managerId,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Branch(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      description: description ?? this.description,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      isActive: isActive ?? this.isActive,
      isMain: isMain ?? this.isMain,
      isDefault: isDefault ?? this.isDefault,
      managerId: managerId ?? this.managerId,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل الفرع إلى Map
  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'name': name,
      'code': code,
      'description': description,
      'address': address,
      'phone': phone,
      'email': email,
      'city': city,
      'state': state,
      'country': country,
      'postal_code': postalCode,
      'is_active': isActive ? 1 : 0,
      'is_main': isMain ? 1 : 0,
      'is_default': isDefault ? 1 : 0,
      'manager_id': managerId,
      'metadata': metadata != null ? jsonEncode(metadata) : null,
    });
    return map;
  }

  /// إنشاء فرع من Map
  factory Branch.fromMap(Map<String, dynamic> map) {
    return Branch(
      id: map['id'],
      name: map['name'] ?? '',
      code: map['code'],
      description: map['description'],
      address: map['address'],
      phone: map['phone'],
      email: map['email'],
      city: map['city'],
      state: map['state'],
      country: map['country'],
      postalCode: map['postal_code'],
      isActive: map['is_active'] == 1 || map['is_active'] == true,
      isMain: map['is_main'] == 1 || map['is_main'] == true,
      isDefault: map['is_default'] == 1 || map['is_default'] == true,
      managerId: map['manager_id'],
      metadata: map['metadata'] != null
          ? (map['metadata'] is String
              ? jsonDecode(map['metadata'])
              : map['metadata'] as Map<String, dynamic>)
          : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1 || map['is_deleted'] == true,
    );
  }

  /// تحويل الفرع إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء فرع من JSON
  factory Branch.fromJson(String source) => Branch.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'Branch(id: $id, name: $name, isMain: $isMain, isActive: $isActive)';
  }
}
