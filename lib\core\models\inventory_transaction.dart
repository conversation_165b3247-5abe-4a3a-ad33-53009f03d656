import 'package:uuid/uuid.dart';
import 'base_model.dart';
import 'product.dart';
import 'warehouse.dart';
import 'unit.dart';

/// أنواع حركات المخزون
enum InventoryTransactionType {
  purchase,
  sale,
  transfer,
  adjustment,
  returnIn,
  returnOut,
  count,
}

/// نموذج حركة المخزون
class InventoryTransaction extends BaseModel {
  final InventoryTransactionType transactionType;
  final String? referenceId; // معرف المستند المرتبط (فاتورة، تحويل، إلخ)
  final String? referenceType; // نوع المستند المرتبط
  final String productId;
  final Product? product; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final String warehouseId;
  final Warehouse? warehouse; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final double quantity;
  final String? unitId;
  final Unit? unit; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final double? unitCost;
  final DateTime? expiryDate;
  final String? batchNumber;
  final String? notes;
  final DateTime transactionDate;

  InventoryTransaction({
    String? id,
    required this.transactionType,
    this.referenceId,
    this.referenceType,
    required this.productId,
    this.product,
    required this.warehouseId,
    this.warehouse,
    required this.quantity,
    this.unitId,
    this.unit,
    this.unitCost,
    this.expiryDate,
    this.batchNumber,
    this.notes,
    DateTime? transactionDate,
    DateTime? createdAt,
    String? createdBy,
    bool isDeleted = false,
  })  : transactionDate = transactionDate ?? DateTime.now(),
        super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: null,
          updatedBy: null,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذه الحركة مع استبدال الحقول المحددة بقيم جديدة
  InventoryTransaction copyWith({
    String? id,
    InventoryTransactionType? transactionType,
    String? referenceId,
    String? referenceType,
    String? productId,
    Product? product,
    String? warehouseId,
    Warehouse? warehouse,
    double? quantity,
    String? unitId,
    Unit? unit,
    double? unitCost,
    DateTime? expiryDate,
    String? batchNumber,
    String? notes,
    DateTime? transactionDate,
    DateTime? createdAt,
    String? createdBy,
    bool? isDeleted,
  }) {
    return InventoryTransaction(
      id: id ?? this.id,
      transactionType: transactionType ?? this.transactionType,
      referenceId: referenceId ?? this.referenceId,
      referenceType: referenceType ?? this.referenceType,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      warehouseId: warehouseId ?? this.warehouseId,
      warehouse: warehouse ?? this.warehouse,
      quantity: quantity ?? this.quantity,
      unitId: unitId ?? this.unitId,
      unit: unit ?? this.unit,
      unitCost: unitCost ?? this.unitCost,
      expiryDate: expiryDate ?? this.expiryDate,
      batchNumber: batchNumber ?? this.batchNumber,
      notes: notes ?? this.notes,
      transactionDate: transactionDate ?? this.transactionDate,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل حركة المخزون إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'transaction_type': transactionType.toString().split('.').last,
      'reference_id': referenceId,
      'reference_type': referenceType,
      'product_id': productId,
      'warehouse_id': warehouseId,
      'quantity': quantity,
      'unit_id': unitId,
      'unit_cost': unitCost,
      'expiry_date': expiryDate?.toIso8601String(),
      'batch_number': batchNumber,
      'notes': notes,
      'transaction_date': transactionDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء حركة مخزون من Map
  factory InventoryTransaction.fromMap(Map<String, dynamic> map) {
    return InventoryTransaction(
      id: map['id'],
      transactionType: _parseTransactionType(map['transaction_type']),
      referenceId: map['reference_id'],
      referenceType: map['reference_type'],
      productId: map['product_id'],
      product: map['product'] != null ? Product.fromMap(map['product']) : null,
      warehouseId: map['warehouse_id'],
      warehouse:
          map['warehouse'] != null ? Warehouse.fromMap(map['warehouse']) : null,
      quantity: map['quantity'] is int
          ? (map['quantity'] as int).toDouble()
          : (map['quantity'] as double? ?? 0.0),
      unitId: map['unit_id'],
      unit: map['unit'] != null ? Unit.fromMap(map['unit']) : null,
      unitCost: map['unit_cost'] != null
          ? (map['unit_cost'] is int
              ? (map['unit_cost'] as int).toDouble()
              : map['unit_cost'] as double)
          : null,
      expiryDate: map['expiry_date'] != null
          ? DateTime.parse(map['expiry_date'])
          : null,
      batchNumber: map['batch_number'],
      notes: map['notes'],
      transactionDate: map['transaction_date'] != null
          ? DateTime.parse(map['transaction_date'])
          : DateTime.now(),
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحليل نوع الحركة من النص
  static InventoryTransactionType _parseTransactionType(String? typeString) {
    switch (typeString) {
      case 'purchase':
        return InventoryTransactionType.purchase;
      case 'sale':
        return InventoryTransactionType.sale;
      case 'transfer':
        return InventoryTransactionType.transfer;
      case 'adjustment':
        return InventoryTransactionType.adjustment;
      case 'return_in':
        return InventoryTransactionType.returnIn;
      case 'return_out':
        return InventoryTransactionType.returnOut;
      case 'count':
        return InventoryTransactionType.count;
      default:
        return InventoryTransactionType.adjustment;
    }
  }

  @override
  String toString() {
    return 'InventoryTransaction(id: $id, type: $transactionType, productId: $productId, quantity: $quantity)';
  }
}
