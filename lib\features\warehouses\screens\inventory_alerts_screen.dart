import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/index.dart';
import '../presenters/warehouse_presenter.dart';
import '../../products/presenters/product_presenter.dart';
import '../../../core/models/product.dart';
import 'inventory_adjustment_form_screen.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

/// شاشة تنبيهات المخزون
class InventoryAlertsScreen extends StatefulWidget {
  const InventoryAlertsScreen({Key? key}) : super(key: key);

  @override
  State<InventoryAlertsScreen> createState() => _InventoryAlertsScreenState();
}

class _InventoryAlertsScreenState extends State<InventoryAlertsScreen> {
  late WarehousePresenter _warehousePresenter;
  late ProductPresenter _productPresenter;

  String? _selectedWarehouseId;

  @override
  void initState() {
    super.initState();
    _warehousePresenter = AppProviders.getLazyPresenter<WarehousePresenter>(
        () => WarehousePresenter());
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
        () => ProductPresenter());
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await _warehousePresenter.loadWarehouses();
    await _productPresenter.loadProducts();

    // تعيين المخزن الافتراضي
    if (_selectedWarehouseId == null) {
      final defaultWarehouse = _warehousePresenter.getDefaultWarehouse();
      if (defaultWarehouse != null) {
        setState(() {
          _selectedWarehouseId = defaultWarehouse.id;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تنبيهات المخزون'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: SafeLayout(
        child: Column(
          children: [
            // شريط الفلترة
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'خيارات التنبيهات',
                        style: AppTypography.createCustomStyle(
                          fontSize: 18,
                          fontWeight: AppTypography.weightBold,
                        ),
                      ),
                      const SizedBox(height: AppDimensions.spacing16),

                      // المخزن
                      ListenableBuilder(
                        listenable: _warehousePresenter,
                        builder: (context, child) {
                          return DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              labelText: 'المخزن',
                              border: OutlineInputBorder(),
                            ),
                            value: _selectedWarehouseId,
                            items: _warehousePresenter.warehouses
                                .map((warehouse) => DropdownMenuItem(
                                      value: warehouse.id,
                                      child: Text(warehouse.name),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedWarehouseId = value;
                                });
                              }
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // جدول التنبيهات
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: ListenableBuilder(
                  listenable: _productPresenter,
                  builder: (context, child) {
                    if (_productPresenter.isLoading) {
                      return const Center(
                        child: AkLoadingIndicator(),
                      );
                    }

                    // تصفية المنتجات
                    final List<Product> convertedProducts = _productPresenter
                        .products
                        .map((p) => Product(
                            id: p.id,
                            name: p.name,
                            barcode: p.barcode,
                            minStock: p.minStock,
                            salePrice: p.salePrice))
                        .toList();

                    final alertProducts = _getAlertProducts(convertedProducts);

                    if (alertProducts.isEmpty) {
                      return const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: AppColors.lightTextSecondary,
                              size: 48,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد تنبيهات للمخزون',
                              style: AppTypography(
                                  color: AppColors.lightTextSecondary),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      );
                    }

                    return _buildAlertsTable(alertProducts);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: AkFloatingButton(
        onPressed: _addAdjustment,
        tooltip: 'إضافة تعديل',
        icon: Icons.add,
      ),
    );
  }

  /// الحصول على المنتجات التي تحتاج إلى تنبيه
  List<Product> _getAlertProducts(List<Product> products) {
    // في هذه الحالة، نفترض أن جميع المنتجات لديها مستوى منخفض من المخزون
    // لأننا لا نملك وصولاً إلى الكمية الفعلية في نموذج المنتج
    return products.where((product) => product.minStock > 0).toList();
  }

  /// بناء جدول التنبيهات
  Widget _buildAlertsTable(List<Product> products) {
    final columns = [
      const FinancialTableColumn(
        title: 'اسم المنتج',
        field: 'name',
        alignment: ColumnAlignment.start,
      ),
      const FinancialTableColumn(
        title: 'الباركود',
        field: 'barcode',
        alignment: ColumnAlignment.center,
      ),
      const FinancialTableColumn(
        title: 'الكمية الحالية',
        field: 'quantity',
        alignment: ColumnAlignment.center,
        isNumeric: true,
      ),
      const FinancialTableColumn(
        title: 'الحد الأدنى',
        field: 'min_quantity',
        alignment: ColumnAlignment.center,
        isNumeric: true,
      ),
      const FinancialTableColumn(
        title: 'النقص',
        field: 'shortage',
        alignment: ColumnAlignment.center,
        isNumeric: true,
      ),
      const FinancialTableColumn(
        title: 'الحالة',
        field: 'status',
        alignment: ColumnAlignment.center,
      ),
      const FinancialTableColumn(
        title: 'الإجراءات',
        field: 'actions',
        alignment: ColumnAlignment.center,
      ),
    ];

    final data = products.map((product) {
      final shortage =
          product.minStock - 0; // هنا يجب الحصول على الكمية الفعلية من المخزون

      return {
        'id': product.id,
        'name': product.name,
        'barcode': product.barcode ?? '',
        'quantity': 0, // هنا يجب الحصول على الكمية الفعلية من المخزون
        'min_quantity': product.minStock,
        'shortage': shortage > 0 ? shortage : 0,
        'status': _getProductStatus(product),
        'actions': product,
      };
    }).toList();

    return FinancialDataTable(
      columns: columns,
      data: data,
      customCellBuilder: (context, rowIndex, columnIndex, value, field) {
        if (field == 'status') {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(value),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              value,
              style: const AppTypography(color: AppColors.onPrimary),
            ),
          );
        } else if (field == 'actions') {
          final product = value as Product;
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.add_circle, color: AppColors.success),
                onPressed: () => _addProductAdjustment(product, 'increase'),
                tooltip: 'زيادة المخزون',
              ),
              IconButton(
                icon: const Icon(Icons.remove_circle, color: AppColors.error),
                onPressed: () => _addProductAdjustment(product, 'decrease'),
                tooltip: 'نقصان المخزون',
              ),
              IconButton(
                icon: const Icon(Icons.edit, color: AppColors.info),
                onPressed: () => _addProductAdjustment(product, 'inventory'),
                tooltip: 'تعديل المخزون',
              ),
            ],
          );
        }
        return null;
      },
    );
  }

  /// الحصول على حالة المنتج
  String _getProductStatus(Product product) {
    // هنا يجب الحصول على الكمية الفعلية من المخزون
    double quantity = 0;

    if (quantity <= 0) {
      return 'نافد';
    } else if (quantity <= product.minStock) {
      return 'منخفض';
    } else {
      return 'متوفر';
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(String status) {
    switch (status) {
      case 'نافد':
        return AppColors.error;
      case 'منخفض':
        return AppColors.warning;
      case 'متوفر':
        return AppColors.success;
      default:
        return AppColors.secondary;
    }
  }

  /// إضافة تعديل
  void _addAdjustment() async {
    if (_selectedWarehouseId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار المخزن أولاً'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InventoryAdjustmentFormScreen(
          initialWarehouseId: _selectedWarehouseId,
          initialAdjustmentType: 'addition',
        ),
      ),
    );

    if (result == true) {
      _loadData();
    }
  }

  /// إضافة تعديل لمنتج محدد
  void _addProductAdjustment(Product product, String adjustmentType) async {
    if (_selectedWarehouseId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار المخزن أولاً'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // إنشاء تعديل جديد مع المنتج المحدد
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InventoryAdjustmentFormScreen(
          initialProduct: product,
          initialWarehouseId: _selectedWarehouseId,
          initialAdjustmentType: adjustmentType,
        ),
      ),
    );

    if (result == true) {
      _loadData();
    }
  }
}
