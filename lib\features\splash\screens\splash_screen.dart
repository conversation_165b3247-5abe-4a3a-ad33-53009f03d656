import 'dart:async';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/constants/app_constants.dart';

import '../../../core/utils/index.dart';
import '../../../core/services/session_manager.dart';
import '../../../core/auth/services/login_settings_service.dart';
import '../../../core/theme/index.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  bool _isFirstLaunch = false;
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animations - faster and smoother
    _controller = AnimationController(
      duration: const Duration(
          milliseconds: 800), // تقليل المدة من 3 ثوان إلى 800 مللي ثانية
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.6,
          curve: Curves.easeIn), // تعديل منحنى الأنيميشن
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.9, // تغيير من 0.8 إلى 0.9 لتقليل مقدار التكبير
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.2, 0.8,
          curve: Curves.easeOutCubic), // تعديل منحنى الأنيميشن
    ));

    // Start animation and check app state
    _controller.forward();

    // تأخير بدء التحقق من الصلاحيات وحالة التطبيق
    // لإعطاء وقت للشاشة للظهور بشكل كامل قبل بدء العمليات الثقيلة
    Future.delayed(const Duration(milliseconds: 500), () {
      _requestPermissions();
    });
  }

  /// طلب الصلاحيات اللازمة للتطبيق
  Future<void> _requestPermissions() async {
    try {
      AppLogger.info('طلب صلاحيات التخزين...');

      // طلب صلاحيات التخزين
      final storageStatus = await Permission.storage.status;

      if (!storageStatus.isGranted) {
        AppLogger.info('صلاحيات التخزين غير ممنوحة، جاري طلبها...');
        final result = await Permission.storage.request();

        if (result.isGranted) {
          AppLogger.info('تم منح صلاحيات التخزين بنجاح');
        } else {
          AppLogger.warning('تم رفض صلاحيات التخزين: $result');
          // عرض رسالة للمستخدم
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content:
                    Text('يحتاج التطبيق إلى صلاحيات التخزين للعمل بشكل صحيح'),
                backgroundColor: AppColors.warning,
                duration: Duration(seconds: 5),
              ),
            );
          }
        }
      } else {
        AppLogger.info('صلاحيات التخزين ممنوحة بالفعل');
      }

      // طلب صلاحيات إضافية إذا لزم الأمر
      // ...

      // بعد طلب الصلاحيات، نتابع التحقق من حالة التطبيق
      _checkFirstLaunch();
    } catch (e) {
      AppLogger.error('خطأ في طلب الصلاحيات: $e');
      // في حالة الخطأ، نتابع التحقق من حالة التطبيق
      _checkFirstLaunch();
    }
  }

  /// التحقق من حالة التطبيق وتوجيه المستخدم إلى الشاشة المناسبة
  /// تم تبسيط هذه الدالة لتكون أكثر كفاءة وأقل تعقيداً
  Future<void> _checkFirstLaunch() async {
    try {
      AppLogger.info('🔍 بدء تنفيذ _checkFirstLaunch()');

      // تبسيط عملية التحقق من حالة الإعداد
      AppLogger.info('🔍 جاري التحقق من حالة الإعداد...');
      bool isSetupCompleted = await SessionManager.isSetupCompleted();
      bool isFirstLaunch = await SessionManager.isFirstLaunch();
      bool isLoggedIn = await SessionManager.isLoggedIn();

      AppLogger.info('=== حالة الإعداد في _checkFirstLaunch ===');
      AppLogger.info('- حالة الإعداد: $isSetupCompleted');
      AppLogger.info('- التشغيل الأول: $isFirstLaunch');
      AppLogger.info('- تسجيل الدخول: $isLoggedIn');
      AppLogger.info('====================================');

      // التحقق من وجود قاعدة البيانات بطريقة مبسطة وسريعة
      AppLogger.info('🔍 جاري التحقق من قاعدة البيانات...');
      bool needsSetup = false;

      // استخدام الدالة الجديدة للتحقق من وجود ملف قاعدة البيانات دون فتحها
      bool databaseExists = await _databaseHelper.databaseFileExists();
      AppLogger.info('🔍 التحقق من وجود ملف قاعدة البيانات: $databaseExists');

      // إذا كانت قاعدة البيانات موجودة، نقوم بفتحها فقط دون تهيئة
      if (databaseExists) {
        AppLogger.info('🔍 قاعدة البيانات موجودة، جاري فتحها...');
        final db = await _databaseHelper.openExistingDatabase();
        if (db == null) {
          AppLogger.warning('⚠️ فشل في فتح قاعدة البيانات الموجودة');
          needsSetup = true;
        } else {
          AppLogger.info('✅ تم فتح قاعدة البيانات الموجودة بنجاح');
        }
      }

      if (!databaseExists) {
        AppLogger.info(
            '⚠️ ملف قاعدة البيانات غير موجود، يحتاج التطبيق إلى إعداد');
        needsSetup = true;

        // إعادة تعيين حالة الإعداد والتشغيل الأول
        await SessionManager.setSetupCompleted(false);
        await SessionManager.setFirstLaunch(true);
        isSetupCompleted = false;
        isFirstLaunch = true;
      } else if (!isSetupCompleted || isFirstLaunch) {
        // إذا كانت قاعدة البيانات موجودة ولكن لم يكتمل الإعداد أو كان التشغيل الأول
        AppLogger.info(
            '⚠️ قاعدة البيانات موجودة ولكن لم يكتمل الإعداد أو كان التشغيل الأول');
        needsSetup = true;
      }

      // تحديث حالة الشاشة
      setState(() {
        _isFirstLaunch = isFirstLaunch || !isSetupCompleted || needsSetup;
      });
      AppLogger.info(
          '✅ تم تحديث حالة الشاشة: _isFirstLaunch = $_isFirstLaunch');

      // تحديد الشاشة التي سيتم الانتقال إليها
      String targetRoute;

      if (_isFirstLaunch || !isSetupCompleted || needsSetup) {
        // توجيه المستخدم إلى شاشة الإعداد الأولي
        AppLogger.info('🚀 سيتم الانتقال إلى شاشة الإعداد الأولي');
        targetRoute = AppRoutes.initialSetup;
      } else {
        // التحقق من إعدادات تسجيل الدخول
        final shouldShowLogin =
            await LoginSettingsService.shouldShowLoginScreen();

        if (isLoggedIn && !shouldShowLogin) {
          // إذا كان المستخدم مسجل الدخول ولا يحتاج لإعادة تسجيل الدخول
          AppLogger.info(
              '🚀 المستخدم مسجل الدخول وإعداد "تذكر تسجيل الدخول" مفعل، سيتم الانتقال إلى شاشة الداش بورد');
          targetRoute = AppRoutes.dashboard;

          // تحديث وقت آخر نشاط
          await LoginSettingsService.updateLastActivity();
        } else {
          // توجيه المستخدم إلى شاشة تسجيل الدخول
          if (shouldShowLogin) {
            AppLogger.info(
                '🚀 سيتم عرض شاشة تسجيل الدخول (حسب إعدادات المستخدم أو انتهاء الجلسة)');
          } else {
            AppLogger.info('🚀 سيتم الانتقال إلى شاشة تسجيل الدخول');
          }
          targetRoute = AppRoutes.login;

          // مسح بيانات الجلسة إذا انتهت المهلة
          if (shouldShowLogin) {
            await LoginSettingsService.clearSession();
          }
        }
      }

      // التحقق من حالة الـ widget قبل الانتقال
      if (mounted) {
        AppLogger.info('🚀 جاري الانتقال إلى: $targetRoute');

        // إضافة تأخير قصير للتأكد من اكتمال الرسوم المتحركة
        await Future.delayed(const Duration(milliseconds: 500));

        if (mounted) {
          Navigator.pushReplacementNamed(context, targetRoute);
          AppLogger.info('✅ تم تنفيذ أمر الانتقال إلى: $targetRoute');
        }
      }
    } catch (e, stackTrace) {
      // تسجيل الخطأ للتتبع
      AppLogger.error('❌ خطأ في دالة _checkFirstLaunch: $e');
      ErrorTracker.captureError(
        'فشل في التحقق من حالة التطبيق',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'check_first_launch'},
      );

      // في حالة الخطأ، نوجه المستخدم إلى شاشة الإعداد الأولي
      if (mounted) {
        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'حدث خطأ أثناء التحقق من حالة التطبيق. سيتم توجيهك إلى شاشة الإعداد.'),
            backgroundColor: AppColors.warning,
            duration: Duration(seconds: 3),
          ),
        );

        // الانتقال إلى شاشة الإعداد الأولي
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            Navigator.pushReplacementNamed(context, AppRoutes.initialSetup);
          }
        });
      }
    }
  }

  // إعادة تعيين الجلسة
  Future<void> _resetSession() async {
    await SessionManager.resetSession();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إعادة تعيين الجلسة بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
      _checkFirstLaunch();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DynamicColors.primary,
      body: SafeArea(
        child: LayoutBuilder(builder: (context, constraints) {
          return Stack(
            children: [
              // المحتوى الرئيسي
              Center(
                child: AnimatedBuilder(
                  animation: _controller,
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: ScaleTransition(
                        scale: _scaleAnimation,
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize
                                .min, // تقليل حجم العمود للحد الأدنى
                            children: [
                              _buildLogo(constraints),
                              SizedBox(height: Layout.h(2)), // تقليل المسافة
                              Text(
                                AppConstants.appName,
                                style: AppTypography(
                                  fontSize: Layout.getResponsiveFontSize(
                                      28), // تقليل حجم الخط
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.onPrimary,
                                ),
                              ),
                              SizedBox(height: Layout.h(1)), // تقليل المسافة
                              Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: Layout.w(10)),
                                child: Text(
                                  'Your Complete Business Management Solution',
                                  style: AppTypography(
                                    fontSize: Layout.getResponsiveFontSize(
                                        14), // تقليل حجم الخط
                                    color: AppColors.onPrimary
                                        .withValues(alpha: 0.7),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              SizedBox(height: Layout.h(4)), // تقليل المسافة
                              SizedBox(
                                width: Layout.w(6), // تقليل الحجم
                                height: Layout.w(6), // تقليل الحجم
                                child: const CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.onPrimary),
                                  strokeWidth: 2, // تقليل سمك الخط
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

              // إصدار التطبيق في الأسفل إلى اليسار
              Positioned(
                left: 16,
                bottom: 16,
                child: Text(
                  'v1.0.0', // استخدام قيمة ثابتة بدلاً من AppConfig.appVersion
                  style: AppTypography(
                    color: AppColors.onPrimary.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
              ),

              // زر الإعدادات في الأسفل إلى اليمين
              Positioned(
                right: 16,
                bottom: 16,
                child: TextButton(
                  onPressed: () async {
                    // عرض قائمة خيارات
                    showModalBottomSheet(
                      context: context,
                      backgroundColor: Colors.transparent,
                      builder: (context) => Container(
                        decoration: const BoxDecoration(
                          color: AppColors.lightSurface,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Color.fromRGBO(0, 0, 0, 0.1),
                              blurRadius: 10,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              margin: const EdgeInsets.only(top: 10),
                              width: 50,
                              height: 5,
                              decoration: BoxDecoration(
                                color: AppColors.lightBorder,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            const SizedBox(height: 20),
                            const Text(
                              'خيارات التطبيق',
                              style: AppTypography(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 20),
                            ListTile(
                              leading: const Icon(Icons.settings_applications),
                              title: const Text('إعداد النظام'),
                              subtitle:
                                  const Text('إعادة تهيئة النظام من البداية'),
                              onTap: () {
                                Navigator.pop(context);
                                Navigator.pushReplacementNamed(
                                    context, AppRoutes.initialSetup);
                              },
                            ),
                            ListTile(
                              leading: const Icon(Icons.refresh),
                              title: const Text('إعادة تعيين الجلسة'),
                              subtitle: const Text(
                                  'مسح بيانات تسجيل الدخول والإعدادات'),
                              onTap: () {
                                Navigator.pop(context);
                                _resetSession();
                              },
                            ),
                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    );
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.onPrimary.withValues(alpha: 0.7),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.settings, size: 16),
                      SizedBox(width: 4),
                      Text('الإعدادات'),
                    ],
                  ),
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildLogo(BoxConstraints constraints) {
    // حساب الحجم المناسب بناءً على عرض الشاشة
    final logoSize = constraints.maxWidth * 0.3; // تقليل الحجم من 35% إلى 30%
    final iconSize = logoSize * 0.22; // تقليل الحجم من 25% إلى 22%

    return Container(
      width: logoSize,
      height: logoSize,
      decoration: BoxDecoration(
        color: AppColors.onPrimary,
        borderRadius: BorderRadius.circular(logoSize * 0.13), // نسبة متجاوبة
        boxShadow: AppColors.createSoftShadow(
          color: AppColors.lightShadow,
          blurRadius: 10,
          spreadRadius: 1,
          opacity: 0.1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.shopping_cart,
                size: iconSize,
                color: DynamicColors.primary,
              ),
              SizedBox(width: iconSize * 0.25),
              Icon(
                Icons.store,
                size: iconSize,
                color: DynamicColors.primary,
              ),
            ],
          ),
          SizedBox(height: iconSize * 0.35),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.inventory,
                size: iconSize,
                color: DynamicColors.primary,
              ),
              SizedBox(width: iconSize * 0.25),
              Icon(
                Icons.people,
                size: iconSize,
                color: DynamicColors.primary,
              ),
            ],
          ),
          SizedBox(height: iconSize * 0.25),
          Text(
            'تاجر بلس',
            style: AppTypography(
              fontSize: logoSize * 0.13, // 13% من حجم الشعار
              fontWeight: FontWeight.bold,
              color: DynamicColors.primary,
            ),
          ),
        ],
      ),
    );
  }
}
