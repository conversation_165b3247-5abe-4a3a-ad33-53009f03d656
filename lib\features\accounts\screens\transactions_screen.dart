import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/widgets/index.dart';
import 'transaction_form_screen.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/theme/index.dart';

class TransactionsScreen extends StatefulWidget {
  final int? accountId; // Optional: to filter transactions by account

  const TransactionsScreen({Key? key, this.accountId}) : super(key: key);

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Map<String, dynamic>> _transactions = [];
  List<Map<String, dynamic>> _filteredTransactions = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedTransactionType = 'all';
  DateTimeRange? _dateRange;

  @override
  void initState() {
    super.initState();
    _loadTransactions();
  }

  Future<void> _loadTransactions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final db = await _databaseHelper.database;

      // Build the query based on whether we're filtering by account
      String query = '''
        SELECT t.*,
               a1.name as account_name,
               a2.name as related_account_name,
               u.username as user_name,
               b.name as branch_name
        FROM transactions t
        LEFT JOIN accounts a1 ON t.account_id = a1.id
        LEFT JOIN accounts a2 ON t.related_account_id = a2.id
        LEFT JOIN users u ON t.user_id = u.id
        LEFT JOIN branches b ON t.branch_id = b.id
      ''';

      List<dynamic> whereArgs = [];

      if (widget.accountId != null) {
        query += ' WHERE t.account_id = ? OR t.related_account_id = ?';
        whereArgs = [widget.accountId, widget.accountId];
      }

      query += ' ORDER BY t.transaction_date DESC';

      final List<Map<String, dynamic>> transactions =
          await db.rawQuery(query, whereArgs);

      setState(() {
        _transactions = transactions;
        _applyFilters();
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to load transactions',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'TransactionsScreen'},
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load transactions')),
        );
      }
    }
  }

  void _applyFilters() {
    List<Map<String, dynamic>> filtered = List.from(_transactions);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((transaction) {
        final accountName =
            transaction['account_name']?.toString().toLowerCase() ?? '';
        final relatedAccountName =
            transaction['related_account_name']?.toString().toLowerCase() ?? '';
        final notes = transaction['notes']?.toString().toLowerCase() ?? '';
        final query = _searchQuery.toLowerCase();

        return accountName.contains(query) ||
            relatedAccountName.contains(query) ||
            notes.contains(query);
      }).toList();
    }

    // Apply transaction type filter
    if (_selectedTransactionType != 'all') {
      filtered = filtered.where((transaction) {
        return transaction['transaction_type'] == _selectedTransactionType;
      }).toList();
    }

    // Apply date range filter
    if (_dateRange != null) {
      filtered = filtered.where((transaction) {
        final transactionDate = DateTime.parse(transaction['transaction_date']);
        return transactionDate
                .isAfter(_dateRange!.start.subtract(const Duration(days: 1))) &&
            transactionDate
                .isBefore(_dateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    setState(() {
      _filteredTransactions = filtered;
    });
  }

  // تم تعطيل هذه الدالة مؤقتاً حتى يتم استخدامها في المستقبل
  // String _getTransactionTypeDisplayName(String type) {
  //   switch (type) {
  //     case 'income':
  //       return 'إيراد';
  //     case 'expense':
  //       return 'مصروف';
  //     case 'transfer':
  //       return 'تحويل';
  //     case 'sale':
  //       return 'مبيعات';
  //     case 'purchase':
  //       return 'مشتريات';
  //     default:
  //       return type;
  //   }
  // }

  Color _getTransactionTypeColor(String type) {
    return AppColors.getTransactionTypeColor(type);
  }

  void _navigateToTransactionForm(BuildContext context,
      {Map<String, dynamic>? transaction}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TransactionFormScreen(
          transaction: transaction,
          initialAccountId: widget.accountId,
        ),
      ),
    ).then((_) => _loadTransactions());
  }

  Future<void> _selectDateRange() async {
    final initialDateRange = _dateRange ??
        DateTimeRange(
          start: DateTime.now().subtract(const Duration(days: 30)),
          end: DateTime.now(),
        );

    final newDateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      initialDateRange: initialDateRange,
    );

    if (newDateRange != null) {
      setState(() {
        _dateRange = newDateRange;
      });
      _applyFilters();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: widget.accountId != null ? 'حركات الحساب' : 'المعاملات المالية',
        showBackButton: true,
      ),
      floatingActionButton: AkFloatingButton(
        onPressed: () => _navigateToTransactionForm(context),
        icon: Icons.add,
        tooltip: 'إضافة معاملة جديدة',
      ),
      body: Column(
        children: [
          _buildFilters(),
          Expanded(
            child: _isLoading
                ? const Center(child: AkLoadingIndicator())
                : _filteredTransactions.isEmpty
                    ? AkEmptyState(
                        title: 'لا توجد معاملات',
                        message: _searchQuery.isNotEmpty ||
                                _selectedTransactionType != 'all' ||
                                _dateRange != null
                            ? 'لا توجد معاملات تطابق معايير البحث'
                            : 'لم يتم تسجيل أي معاملات مالية حتى الآن',
                        description: _searchQuery.isNotEmpty ||
                                _selectedTransactionType != 'all' ||
                                _dateRange != null
                            ? 'جرب تغيير معايير البحث أو التصفية'
                            : 'ابدأ بإضافة معاملة مالية جديدة',
                        icon: Icons.receipt_long,
                        onRefresh: _loadTransactions,
                        onAction: _searchQuery.isEmpty &&
                                _selectedTransactionType == 'all' &&
                                _dateRange == null
                            ? () => _navigateToTransactionForm(context)
                            : null,
                        actionText: 'إضافة معاملة',
                      )
                    : ListView.builder(
                        itemCount: _filteredTransactions.length,
                        padding: const EdgeInsets.all(16),
                        itemBuilder: (context, index) {
                          final transaction = _filteredTransactions[index];
                          return _buildTransactionCard(transaction);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          TextField(
            decoration: const InputDecoration(
              hintText: 'بحث...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _applyFilters();
            },
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip('all', 'الكل'),
                      _buildFilterChip('income', 'إيراد'),
                      _buildFilterChip('expense', 'مصروف'),
                      _buildFilterChip('transfer', 'تحويل'),
                      _buildFilterChip('sale', 'مبيعات'),
                      _buildFilterChip('purchase', 'مشتريات'),
                    ],
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.date_range),
                onPressed: _selectDateRange,
                tooltip: 'تصفية حسب التاريخ',
              ),
            ],
          ),
          if (_dateRange != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Row(
                children: [
                  Expanded(
                    child: Chip(
                      label: Text(
                        'من ${DateFormat('yyyy/MM/dd').format(_dateRange!.start)} إلى ${DateFormat('yyyy/MM/dd').format(_dateRange!.end)}',
                        style: AppTypography.createCustomStyle(fontSize: 12),
                      ),
                      deleteIcon: const Icon(Icons.close, size: 16),
                      onDeleted: () {
                        setState(() {
                          _dateRange = null;
                        });
                        _applyFilters();
                      },
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String type, String label) {
    final isSelected = _selectedTransactionType == type;

    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: FilterChip(
        selected: isSelected,
        label: Text(label),
        onSelected: (selected) {
          setState(() {
            _selectedTransactionType = selected ? type : 'all';
          });
          _applyFilters();
        },
        backgroundColor: AppColors.lightSurfaceVariant,
        selectedColor: AppColors.primary.withValues(alpha: 0.2),
        checkmarkColor: AppColors.primary,
      ),
    );
  }

  Widget _buildTransactionCard(Map<String, dynamic> transaction) {
    final transactionType = transaction['transaction_type'] as String;
    final amount = transaction['amount'] as double;
    // استخدام المتغيرات في التعليقات لتجنب تحذير "المتغير غير مستخدم"
    // accountName: ${transaction['account_name']}
    // relatedAccountName: ${transaction['related_account_name']}
    final transactionDate = DateTime.parse(transaction['transaction_date']);
    final notes = transaction['notes'] as String?;

    return AkCard(
      onTap: () =>
          _navigateToTransactionForm(context, transaction: transaction),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getTransactionTypeColor(transactionType),
          child: Icon(
            _getTransactionTypeIcon(transactionType),
            color: AppColors.lightTextSecondary,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                _getTransactionDescription(transaction),
                style: AppTypography.createCustomStyle(
                  fontWeight: AppTypography.weightBold,
                ),
              ),
            ),
            Text(
              '${amount.toStringAsFixed(2)} ر.س',
              style: AppTypography.createCustomStyle(
                color: _getAmountColor(transactionType),
                fontWeight: AppTypography.weightBold,
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(DateFormat('yyyy/MM/dd').format(transactionDate)),
            if (notes != null && notes.isNotEmpty)
              Text(
                notes,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.arrow_forward_ios, size: 16),
          onPressed: () =>
              _navigateToTransactionForm(context, transaction: transaction),
        ),
      ),
    );
  }

  IconData _getTransactionTypeIcon(String type) {
    switch (type) {
      case 'income':
        return Icons.arrow_downward;
      case 'expense':
        return Icons.arrow_upward;
      case 'transfer':
        return Icons.swap_horiz;
      case 'sale':
        return Icons.shopping_cart;
      case 'purchase':
        return Icons.store;
      default:
        return Icons.attach_money;
    }
  }

  Color _getAmountColor(String type) {
    switch (type) {
      case 'income':
      case 'sale':
        return AppColors.success;
      case 'expense':
      case 'purchase':
        return AppColors.error;
      default:
        return AppColors.info;
    }
  }

  String _getTransactionDescription(Map<String, dynamic> transaction) {
    final transactionType = transaction['transaction_type'] as String;
    final accountName = transaction['account_name'] as String? ?? 'غير معروف';
    final relatedAccountName =
        transaction['related_account_name'] as String? ?? 'غير معروف';

    switch (transactionType) {
      case 'income':
        return 'إيراد إلى $accountName';
      case 'expense':
        return 'مصروف من $accountName';
      case 'transfer':
        return 'تحويل من $accountName إلى $relatedAccountName';
      case 'sale':
        return 'مبيعات: $relatedAccountName';
      case 'purchase':
        return 'مشتريات: $relatedAccountName';
      default:
        return transactionType;
    }
  }
}
