import 'package:sqflite/sqflite.dart';
import '../database/database_service.dart';
import '../models/invoice.dart';
import '../models/invoice_item.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';

/// خدمة للعمليات المتعلقة بالفواتير
class InvoiceService {
  // نمط Singleton
  static final InvoiceService _instance = InvoiceService._internal();
  factory InvoiceService() => _instance;
  InvoiceService._internal();

  final DatabaseService _db = DatabaseService.instance;

  /// الحصول على جميع الفواتير
  Future<List<Invoice>> getAllInvoices({
    InvoiceType? type,
    InvoiceStatus? status,
    String? customerId,
    String? supplierId,
    DateTime? fromDate,
    DateTime? toDate,
    bool includeDeleted = false,
    int? limit,
    int? offset,
  }) async {
    try {
      AppLogger.info('الحصول على جميع الفواتير');

      // بناء شرط WHERE
      String whereClause = includeDeleted ? '1=1' : 'i.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (type != null) {
        whereClause += ' AND i.invoice_type = ?';
        whereArgs.add(type.toString().split('.').last);
      }

      if (status != null) {
        whereClause += ' AND i.status = ?';
        whereArgs.add(status.toString().split('.').last);
      }

      if (customerId != null) {
        whereClause += ' AND i.customer_id = ?';
        whereArgs.add(customerId);
      }

      if (supplierId != null) {
        whereClause += ' AND i.supplier_id = ?';
        whereArgs.add(supplierId);
      }

      if (fromDate != null) {
        whereClause += ' AND i.date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND i.date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      // استعلام قاعدة البيانات مع JOIN للحصول على معلومات العميل والمورد والمستودع
      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT 
          i.*,
          c.name as customer_name,
          s.name as supplier_name,
          w.name as warehouse_name
        FROM ${DatabaseService.tableInvoices} i
        LEFT JOIN ${DatabaseService.tableCustomers} c ON i.customer_id = c.id
        LEFT JOIN ${DatabaseService.tableSuppliers} s ON i.supplier_id = s.id
        LEFT JOIN ${DatabaseService.tableWarehouses} w ON i.warehouse_id = w.id
        WHERE $whereClause
        ORDER BY i.date DESC, i.invoice_number DESC
        ${limit != null ? 'LIMIT $limit' : ''}
        ${offset != null ? 'OFFSET $offset' : ''}
      ''', whereArgs);

      // تحويل إلى كائنات Invoice
      final List<Invoice> invoices = [];
      for (final map in maps) {
        // إضافة معلومات العميل والمورد إلى الخريطة
        if (map['customer_id'] != null && map['customer_name'] != null) {
          map['customer'] = {
            'id': map['customer_id'],
            'name': map['customer_name'],
          };
        }

        if (map['supplier_id'] != null && map['supplier_name'] != null) {
          map['supplier'] = {
            'id': map['supplier_id'],
            'name': map['supplier_name'],
          };
        }

        // الحصول على عناصر الفاتورة
        final items = await getInvoiceItems(map['id']);

        // إنشاء كائن الفاتورة مع العناصر
        final invoice = Invoice.fromMap(map, items);
        invoices.add(invoice);
      }

      return invoices;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع الفواتير',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على فاتورة بواسطة المعرف
  Future<Invoice?> getInvoiceById(String id) async {
    try {
      AppLogger.info('الحصول على فاتورة بواسطة المعرف: $id');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT 
          i.*,
          c.name as customer_name,
          s.name as supplier_name,
          w.name as warehouse_name
        FROM ${DatabaseService.tableInvoices} i
        LEFT JOIN ${DatabaseService.tableCustomers} c ON i.customer_id = c.id
        LEFT JOIN ${DatabaseService.tableSuppliers} s ON i.supplier_id = s.id
        LEFT JOIN ${DatabaseService.tableWarehouses} w ON i.warehouse_id = w.id
        WHERE i.id = ?
      ''', [id]);

      if (maps.isEmpty) {
        return null;
      }

      final map = maps.first;

      // إضافة معلومات العميل والمورد إلى الخريطة
      if (map['customer_id'] != null && map['customer_name'] != null) {
        map['customer'] = {
          'id': map['customer_id'],
          'name': map['customer_name'],
        };
      }

      if (map['supplier_id'] != null && map['supplier_name'] != null) {
        map['supplier'] = {
          'id': map['supplier_id'],
          'name': map['supplier_name'],
        };
      }

      // الحصول على عناصر الفاتورة
      final items = await getInvoiceItems(id);

      // إنشاء كائن الفاتورة مع العناصر
      return Invoice.fromMap(map, items);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على فاتورة بواسطة المعرف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return null;
    }
  }

  /// الحصول على عناصر الفاتورة
  Future<List<InvoiceItem>> getInvoiceItems(String invoiceId) async {
    try {
      AppLogger.info('الحصول على عناصر الفاتورة: $invoiceId');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT 
          i.*,
          p.name as product_name,
          p.code as product_code,
          u.name as unit_name
        FROM ${DatabaseService.tableInvoiceItems} i
        LEFT JOIN ${DatabaseService.tableProducts} p ON i.product_id = p.id
        LEFT JOIN ${DatabaseService.tableUnits} u ON i.unit_id = u.id
        WHERE i.invoice_id = ? AND i.is_deleted = 0
        ORDER BY i.id
      ''', [invoiceId]);

      // تحويل إلى كائنات InvoiceItem
      return List.generate(maps.length, (i) {
        final map = maps[i];

        // إضافة معلومات المنتج إلى الخريطة
        if (map['product_id'] != null && map['product_name'] != null) {
          map['product'] = {
            'id': map['product_id'],
            'name': map['product_name'],
            'code': map['product_code'],
          };
        }

        return InvoiceItem.fromMap(map);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على عناصر الفاتورة',
        error: e,
        stackTrace: stackTrace,
        context: {'invoiceId': invoiceId},
      );
      return [];
    }
  }

  /// إضافة فاتورة جديدة مع عناصرها
  Future<bool> addInvoice(Invoice invoice, {String? userId}) async {
    try {
      AppLogger.info('إضافة فاتورة جديدة: ${invoice.invoiceNumber}');

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        // إضافة الفاتورة
        final invoiceMap = invoice.toMap();

        // تعيين created_by إذا تم توفيره
        if (userId != null) {
          invoiceMap['created_by'] = userId;
        }

        await txn.insert(DatabaseService.tableInvoices, invoiceMap);

        // إضافة عناصر الفاتورة
        for (final item in invoice.items) {
          final itemMap = item.toMap();
          await txn.insert(DatabaseService.tableInvoiceItems, itemMap);
        }

        // تحديث رصيد العميل أو المورد
        if (invoice.customerId != null) {
          await _updateCustomerBalance(
              txn, invoice.customerId!, invoice.total, invoice.invoiceType);
        } else if (invoice.supplierId != null) {
          await _updateSupplierBalance(
              txn, invoice.supplierId!, invoice.total, invoice.invoiceType);
        }

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة فاتورة',
        error: e,
        stackTrace: stackTrace,
        context: {'invoice': invoice.toString()},
      );
      return false;
    }
  }

  /// تحديث فاتورة موجودة مع عناصرها
  Future<bool> updateInvoice(Invoice invoice, {String? userId}) async {
    try {
      AppLogger.info('تحديث فاتورة: ${invoice.invoiceNumber}');

      // الحصول على الفاتورة القديمة لحساب الفرق في الرصيد
      final oldInvoice = await getInvoiceById(invoice.id);
      if (oldInvoice == null) {
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        // تحديث الفاتورة
        final invoiceMap = invoice.toMap();

        // تعيين updated_at و updated_by
        invoiceMap['updated_at'] = DateTime.now().toIso8601String();
        if (userId != null) {
          invoiceMap['updated_by'] = userId;
        }

        await txn.update(
          DatabaseService.tableInvoices,
          invoiceMap,
          where: 'id = ?',
          whereArgs: [invoice.id],
        );

        // حذف عناصر الفاتورة القديمة
        await txn.delete(
          DatabaseService.tableInvoiceItems,
          where: 'invoice_id = ?',
          whereArgs: [invoice.id],
        );

        // إضافة عناصر الفاتورة الجديدة
        for (final item in invoice.items) {
          final itemMap = item.toMap();
          await txn.insert(DatabaseService.tableInvoiceItems, itemMap);
        }

        // تحديث رصيد العميل أو المورد
        if (invoice.customerId != null) {
          // حساب الفرق في الرصيد
          final balanceDifference = invoice.total - oldInvoice.total;
          await _updateCustomerBalance(
              txn, invoice.customerId!, balanceDifference, invoice.invoiceType);
        } else if (invoice.supplierId != null) {
          // حساب الفرق في الرصيد
          final balanceDifference = invoice.total - oldInvoice.total;
          await _updateSupplierBalance(
              txn, invoice.supplierId!, balanceDifference, invoice.invoiceType);
        }

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث فاتورة',
        error: e,
        stackTrace: stackTrace,
        context: {'invoice': invoice.toString()},
      );
      return false;
    }
  }

  /// حذف فاتورة (حذف منطقي)
  Future<bool> deleteInvoice(String id, {String? userId}) async {
    try {
      AppLogger.info('حذف فاتورة: $id');

      // الحصول على الفاتورة لمعرفة العميل أو المورد والمبلغ
      final invoice = await getInvoiceById(id);
      if (invoice == null) {
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        final now = DateTime.now().toIso8601String();

        // تحديث الفاتورة
        await txn.update(
          DatabaseService.tableInvoices,
          {
            'is_deleted': 1,
            'updated_at': now,
            'updated_by': userId,
          },
          where: 'id = ?',
          whereArgs: [id],
        );

        // تحديث عناصر الفاتورة
        await txn.update(
          DatabaseService.tableInvoiceItems,
          {
            'is_deleted': 1,
            'updated_at': now,
          },
          where: 'invoice_id = ?',
          whereArgs: [id],
        );

        // عكس تأثير الفاتورة على رصيد العميل أو المورد
        if (invoice.customerId != null) {
          // عكس المبلغ (سالب)
          await _updateCustomerBalance(
              txn, invoice.customerId!, -invoice.total, invoice.invoiceType);
        } else if (invoice.supplierId != null) {
          // عكس المبلغ (سالب)
          await _updateSupplierBalance(
              txn, invoice.supplierId!, -invoice.total, invoice.invoiceType);
        }

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف فاتورة',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// تحديث رصيد العميل
  Future<void> _updateCustomerBalance(
    Transaction txn,
    String customerId,
    double amount,
    InvoiceType invoiceType,
  ) async {
    try {
      // تحديد المبلغ بناءً على نوع الفاتورة
      double balanceChange = 0.0;

      switch (invoiceType) {
        case InvoiceType.sale:
          // زيادة رصيد العميل (مدين)
          balanceChange = amount;
          break;
        case InvoiceType.saleReturn:
          // تخفيض رصيد العميل (دائن)
          balanceChange = -amount;
          break;
        default:
          // أنواع أخرى لا تؤثر على رصيد العميل
          return;
      }

      // الحصول على الرصيد الحالي
      final List<Map<String, dynamic>> result = await txn.query(
        DatabaseService.tableCustomers,
        columns: ['balance'],
        where: 'id = ?',
        whereArgs: [customerId],
      );

      if (result.isEmpty) {
        return;
      }

      final currentBalance = result.first['balance'] is int
          ? (result.first['balance'] as int).toDouble()
          : (result.first['balance'] as double? ?? 0.0);

      // حساب الرصيد الجديد
      final newBalance = currentBalance + balanceChange;

      // تحديث الرصيد
      await txn.update(
        DatabaseService.tableCustomers,
        {'balance': newBalance, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [customerId],
      );
    } catch (e) {
      AppLogger.error('فشل في تحديث رصيد العميل: $e');
      rethrow;
    }
  }

  /// تحديث رصيد المورد
  Future<void> _updateSupplierBalance(
    Transaction txn,
    String supplierId,
    double amount,
    InvoiceType invoiceType,
  ) async {
    try {
      // تحديد المبلغ بناءً على نوع الفاتورة
      double balanceChange = 0.0;

      switch (invoiceType) {
        case InvoiceType.purchase:
          // زيادة رصيد المورد (دائن)
          balanceChange = amount;
          break;
        case InvoiceType.purchaseReturn:
          // تخفيض رصيد المورد (مدين)
          balanceChange = -amount;
          break;
        default:
          // أنواع أخرى لا تؤثر على رصيد المورد
          return;
      }

      // الحصول على الرصيد الحالي
      final List<Map<String, dynamic>> result = await txn.query(
        DatabaseService.tableSuppliers,
        columns: ['balance'],
        where: 'id = ?',
        whereArgs: [supplierId],
      );

      if (result.isEmpty) {
        return;
      }

      final currentBalance = result.first['balance'] is int
          ? (result.first['balance'] as int).toDouble()
          : (result.first['balance'] as double? ?? 0.0);

      // حساب الرصيد الجديد
      final newBalance = currentBalance + balanceChange;

      // تحديث الرصيد
      await txn.update(
        DatabaseService.tableSuppliers,
        {'balance': newBalance, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [supplierId],
      );
    } catch (e) {
      AppLogger.error('فشل في تحديث رصيد المورد: $e');
      rethrow;
    }
  }
}
