import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/index.dart';
import '../presenters/currency_presenter.dart';
import '../../../core/models/currency.dart';
import '../../../core/widgets/data_table_widget.dart';

import '../../../core/theme/index.dart';

class CurrencyManagementScreen extends StatefulWidget {
  const CurrencyManagementScreen({Key? key}) : super(key: key);

  @override
  State<CurrencyManagementScreen> createState() =>
      _CurrencyManagementScreenState();
}

class _CurrencyManagementScreenState extends State<CurrencyManagementScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _symbolController = TextEditingController();
  final TextEditingController _exchangeRateController = TextEditingController();

  bool _isLoading = false;
  bool _isSaving = false;
  bool _isEditing = false;
  bool _isActive = true;
  bool _isDefault = false;
  String? _editingCurrencyId;

  // استخدام التحميل الكسول
  late final CurrencyPresenter _currencyPresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _currencyPresenter = AppProviders.getLazyPresenter<CurrencyPresenter>(
        () => CurrencyPresenter());
    _loadCurrencies();
  }

  Future<void> _loadCurrencies() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _currencyPresenter.loadCurrencies();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل العملات: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _editCurrency(Currency currency) {
    setState(() {
      _isEditing = true;
      _editingCurrencyId = currency.id;
      _nameController.text = currency.name;
      _codeController.text = currency.code;
      _symbolController.text = currency.symbol ?? '';
      _exchangeRateController.text = currency.exchangeRate.toString();
      _isActive = currency.isActive;
      _isDefault = currency.isDefault;
    });
  }

  Future<void> _saveCurrency() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final currency = Currency(
        id: _isEditing ? _editingCurrencyId : null,
        name: _nameController.text,
        code: _codeController.text,
        symbol: _symbolController.text,
        exchangeRate: double.parse(_exchangeRateController.text),
        isActive: _isActive,
        isDefault: _isDefault,
      );

      bool success;
      if (_isEditing) {
        success = await _currencyPresenter.updateCurrency(currency);
      } else {
        success = await _currencyPresenter.addCurrency(currency);
      }

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(_isEditing
                    ? 'تم تحديث العملة بنجاح'
                    : 'تم إضافة العملة بنجاح')),
          );
        }
        _resetForm();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(_isEditing
                    ? 'فشل في تحديث العملة'
                    : 'فشل في إضافة العملة')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حفظ العملة: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _deleteCurrency(String id) async {
    try {
      final success = await _currencyPresenter.deleteCurrency(id);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف العملة بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حذف العملة: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _setDefaultCurrency(String id) async {
    try {
      final success = await _currencyPresenter.setDefaultCurrency(id);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تعيين العملة الافتراضية بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('فشل في تعيين العملة الافتراضية: ${e.toString()}')),
        );
      }
    }
  }

  void _resetForm() {
    _nameController.clear();
    _codeController.clear();
    _symbolController.clear();
    _exchangeRateController.clear();
    setState(() {
      _isEditing = false;
      _editingCurrencyId = null;
      _isActive = true;
      _isDefault = false;
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _symbolController.dispose();
    _exchangeRateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _currencyPresenter,
      builder: (context, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('إدارة العملات'),
            backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          ),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    // نموذج إضافة/تعديل العملة
                    Expanded(
                      flex: 1,
                      child: SingleChildScrollView(
                        padding: EdgeInsets.zero,
                        child: Card(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 4.0, vertical: 4.0),
                            child: Form(
                              key: _formKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 4.0, vertical: 2.0),
                                    child: Text(
                                      _isEditing
                                          ? 'تعديل العملة'
                                          : 'إضافة عملة جديدة',
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium,
                                    ),
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // اسم العملة ورمزها
                                  Row(
                                    children: [
                                      // اسم العملة
                                      Expanded(
                                        flex: 2,
                                        child: TextFormField(
                                          controller: _nameController,
                                          decoration: const InputDecoration(
                                            labelText: 'اسم العملة',
                                            hintText: 'مثال: ريال يمني',
                                            border: OutlineInputBorder(),
                                          ),
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'يرجى إدخال اسم العملة';
                                            }
                                            return null;
                                          },
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      // رمز العملة
                                      Expanded(
                                        flex: 1,
                                        child: TextFormField(
                                          controller: _symbolController,
                                          decoration: const InputDecoration(
                                            labelText: 'رمز العملة',
                                            hintText: 'مثال: ر.ي',
                                            border: OutlineInputBorder(),
                                          ),
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'يرجى إدخال رمز العملة';
                                            }
                                            return null;
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // كود العملة وسعر الصرف
                                  Row(
                                    children: [
                                      // كود العملة
                                      Expanded(
                                        flex: 1,
                                        child: TextFormField(
                                          controller: _codeController,
                                          decoration: const InputDecoration(
                                            labelText: 'كود العملة',
                                            hintText: 'مثال: YER',
                                            border: OutlineInputBorder(),
                                          ),
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'يرجى إدخال كود العملة';
                                            }
                                            return null;
                                          },
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      // سعر الصرف
                                      Expanded(
                                        flex: 1,
                                        child: TextFormField(
                                          controller: _exchangeRateController,
                                          decoration: const InputDecoration(
                                            labelText: 'سعر الصرف',
                                            hintText: 'مثال: 1.0',
                                            border: OutlineInputBorder(),
                                          ),
                                          keyboardType: const TextInputType
                                              .numberWithOptions(decimal: true),
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'يرجى إدخال سعر الصرف';
                                            }
                                            if (double.tryParse(value) ==
                                                null) {
                                              return 'يرجى إدخال رقم صحيح';
                                            }
                                            if (double.parse(value) <= 0) {
                                              return 'يجب أن يكون سعر الصرف أكبر من صفر';
                                            }
                                            return null;
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // خيارات العملة
                                  Row(
                                    children: [
                                      // نشطة
                                      Expanded(
                                        child: SwitchListTile(
                                          title: const Text('نشطة'),
                                          value: _isActive,
                                          onChanged: (value) {
                                            setState(() {
                                              _isActive = value;
                                            });
                                          },
                                        ),
                                      ),
                                      // افتراضية
                                      Expanded(
                                        child: SwitchListTile(
                                          title: const Text('افتراضية'),
                                          value: _isDefault,
                                          onChanged: (value) {
                                            setState(() {
                                              _isDefault = value;
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // أزرار الحفظ والإلغاء
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      // زر الحفظ
                                      Expanded(
                                        child: ElevatedButton.icon(
                                          onPressed:
                                              _isSaving ? null : _saveCurrency,
                                          icon: _isSaving
                                              ? const SizedBox(
                                                  width: 18,
                                                  height: 18,
                                                  child:
                                                      CircularProgressIndicator(
                                                          color: AppColors
                                                              .lightTextSecondary,
                                                          strokeWidth: 2))
                                              : const Icon(Icons.save,
                                                  size: 20),
                                          label: Text(
                                            _isEditing
                                                ? 'تحديث العملة'
                                                : 'إضافة العملة',
                                            style: const AppTypography(
                                                fontSize: 15),
                                          ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: AppColors.primary,
                                            foregroundColor:
                                                AppColors.onPrimary,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      // زر الإلغاء
                                      if (_isEditing)
                                        Expanded(
                                          child: OutlinedButton.icon(
                                            onPressed: _resetForm,
                                            icon: const Icon(Icons.cancel,
                                                size: 20),
                                            label: const Text(
                                              'إلغاء التعديل',
                                              style:
                                                  AppTypography(fontSize: 15),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.spacing8),

                    // جدول العملات
                    Expanded(
                      flex: 1,
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4.0, vertical: 4.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 4.0, vertical: 2.0),
                                child: Text(
                                  'قائمة العملات',
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                ),
                              ),
                              Expanded(
                                child: AdvancedDataTable(
                                  columns: const [
                                    DataColumn(label: Text('إجراءات')),
                                    DataColumn(label: Text('اسم العملة')),
                                    DataColumn(label: Text('الرمز')),
                                    DataColumn(label: Text('الكود')),
                                    DataColumn(label: Text('سعر الصرف')),
                                    DataColumn(label: Text('الحالة')),
                                  ],
                                  rows: _currencyPresenter.currencies
                                      .map((currency) {
                                    final isHighlighted = _isEditing &&
                                        _editingCurrencyId == currency.id;

                                    return DataRow(
                                      color: isHighlighted
                                          ? WidgetStateProperty.all(
                                              Theme.of(context)
                                                  .primaryColor
                                                  .withValues(alpha: 0.1))
                                          : null,
                                      cells: [
                                        DataCell(
                                          PopupMenuButton<String>(
                                            icon: const Icon(Icons.more_vert),
                                            onSelected: (value) async {
                                              if (value == 'edit') {
                                                _editCurrency(currency);
                                              } else if (value == 'delete') {
                                                // حذف العملة بعد التأكيد
                                                final confirm =
                                                    await showDialog<bool>(
                                                  context: context,
                                                  builder: (context) =>
                                                      AlertDialog(
                                                    title: const Text(
                                                        'تأكيد الحذف'),
                                                    content: const Text(
                                                        'هل أنت متأكد من حذف هذه العملة؟'),
                                                    actions: [
                                                      TextButton(
                                                        onPressed: () =>
                                                            Navigator.of(
                                                                    context)
                                                                .pop(false),
                                                        child:
                                                            const Text('إلغاء'),
                                                      ),
                                                      ElevatedButton(
                                                        onPressed: () =>
                                                            Navigator.of(
                                                                    context)
                                                                .pop(true),
                                                        style: ElevatedButton
                                                            .styleFrom(
                                                          backgroundColor:
                                                              AppColors.error,
                                                        ),
                                                        child:
                                                            const Text('حذف'),
                                                      ),
                                                    ],
                                                  ),
                                                );

                                                if (confirm == true) {
                                                  await _deleteCurrency(
                                                      currency.id);
                                                }
                                              } else if (value ==
                                                  'setDefault') {
                                                await _setDefaultCurrency(
                                                    currency.id);
                                              }
                                            },
                                            itemBuilder: (context) => [
                                              const PopupMenuItem<String>(
                                                value: 'edit',
                                                child: Row(
                                                  children: [
                                                    Icon(Icons.edit),
                                                    SizedBox(width: 8),
                                                    Text('تعديل'),
                                                  ],
                                                ),
                                              ),
                                              if (!currency.isDefault)
                                                const PopupMenuItem<String>(
                                                  value: 'setDefault',
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.star,
                                                          color:
                                                              AppColors.amber),
                                                      SizedBox(width: 8),
                                                      Text('تعيين كافتراضية'),
                                                    ],
                                                  ),
                                                ),
                                              if (!currency.isDefault)
                                                const PopupMenuItem<String>(
                                                  value: 'delete',
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.delete,
                                                          color:
                                                              AppColors.error),
                                                      SizedBox(width: 8),
                                                      Text('حذف'),
                                                    ],
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                        DataCell(Text(currency.name)),
                                        DataCell(Text(currency.symbol ?? '')),
                                        DataCell(Text(currency.code)),
                                        DataCell(Text(currency.exchangeRate
                                            .toStringAsFixed(6))),
                                        DataCell(
                                          Row(
                                            children: [
                                              Icon(
                                                currency.isDefault
                                                    ? Icons.star
                                                    : Icons.circle,
                                                color: currency.isDefault
                                                    ? AppColors.amber
                                                    : currency.isActive
                                                        ? AppColors.success
                                                        : AppColors.error,
                                                size: 16,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                currency.isDefault
                                                    ? 'افتراضية'
                                                    : currency.isActive
                                                        ? 'نشطة'
                                                        : 'غير نشطة',
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    );
                                  }).toList(),
                                  isLoading: _currencyPresenter.isLoading,
                                  showCellBorder: true,
                                  zebraPattern: true,
                                  headerBackgroundColor: AppColors.primary,
                                  headerTextColor: AppColors.onPrimary,
                                  showRowNumbers: false,
                                  emptyMessage: 'لا توجد عملات',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
        );
      },
    );
  }
}
