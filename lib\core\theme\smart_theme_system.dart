import 'package:flutter/material.dart';

/// نظام ثيمات ذكي مبسط
/// يوفر دوال مساعدة لحساب الألوان المناسبة والتباين
class SmartThemeSystem {
  /// الحصول على لون النص المناسب للخلفية
  static Color getSmartTextColor(
    BuildContext context, {
    Color? backgroundColor,
  }) {
    final theme = Theme.of(context);
    final bgColor = backgroundColor ?? theme.scaffoldBackgroundColor;

    // حساب السطوع
    final luminance = bgColor.computeLuminance();

    // إذا كانت الخلفية فاتحة، استخدم نص داكن
    // إذا كانت الخلفية داكنة، استخدم نص فاتح
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// الحصول على لون النص المناسب للأزرار
  static Color getSmartButtonTextColor(
      BuildContext context, Color buttonColor) {
    final luminance = buttonColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// فحص وإصلاح مشاكل التباين
  static Map<String, Color> fixContrastIssues(
    BuildContext context,
    Color backgroundColor,
    Color textColor,
  ) {
    final smartTextColor =
        getSmartTextColor(context, backgroundColor: backgroundColor);

    return {
      'backgroundColor': backgroundColor,
      'textColor': smartTextColor,
    };
  }

  /// الحصول على معلومات الثيم الحالي
  static Map<String, dynamic> getCurrentThemeInfo(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return {
      'isDark': isDark,
      'backgroundColor': theme.scaffoldBackgroundColor,
      'textColor': theme.textTheme.bodyLarge?.color ??
          (isDark ? Colors.white : Colors.black),
      'primaryColor': theme.colorScheme.primary,
    };
  }

  /// حساب التباين بين لونين
  static double calculateContrast(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();

    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// التحقق من أن التباين مقبول (WCAG AA)
  static bool hasGoodContrast(Color foreground, Color background) {
    return calculateContrast(foreground, background) >= 4.5;
  }

  /// الحصول على لون متباين مناسب
  static Color getContrastingColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5
        ? Colors.black87
        : Colors.white.withValues(alpha: 0.87);
  }

  /// تفتيح لون بنسبة معينة
  static Color lighten(Color color, double amount) {
    assert(amount >= 0 && amount <= 1, 'Amount must be between 0 and 1');

    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);

    return hsl.withLightness(lightness).toColor();
  }

  /// تغميق لون بنسبة معينة
  static Color darken(Color color, double amount) {
    assert(amount >= 0 && amount <= 1, 'Amount must be between 0 and 1');

    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);

    return hsl.withLightness(lightness).toColor();
  }

  /// تطبيق شفافية على لون
  static Color withOpacity(Color color, double opacity) {
    assert(opacity >= 0 && opacity <= 1, 'Opacity must be between 0 and 1');
    return color.withValues(alpha: opacity);
  }

  /// إنشاء تدرج جميل من لون أساسي
  static LinearGradient createBeautifulGradient(Color primaryColor) {
    final lighterColor = lighten(primaryColor, 0.2);

    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [primaryColor, lighterColor],
    );
  }

  /// الحصول على ظلال جميلة للبطاقات
  static List<BoxShadow> getBeautifulCardShadow({Color? shadowColor}) {
    final color = shadowColor ?? Colors.black.withValues(alpha: 0.1);

    return [
      BoxShadow(
        color: color,
        blurRadius: 8,
        spreadRadius: 1,
        offset: const Offset(0, 2),
      ),
      BoxShadow(
        color: color.withValues(alpha: 0.05),
        blurRadius: 16,
        spreadRadius: 2,
        offset: const Offset(0, 4),
      ),
    ];
  }
}
