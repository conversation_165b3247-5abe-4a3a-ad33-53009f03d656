import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';
import '../models/cart_item.dart';

/// واجهة عنصر السلة
class CartItemWidget extends StatelessWidget {
  final CartItem item;
  final Function(double) onQuantityChanged;
  final VoidCallback onRemove;

  const CartItemWidget({
    Key? key,
    required this.item,
    required this.onQuantityChanged,
    required this.onRemove,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            // صف المعلومات الرئيسية
            Row(
              children: [
                // صورة المنتج
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppColors.lightTextSecondary,
                    borderRadius: BorderRadius.circular(4),
                    image: item.product.imageUrl != null
                        ? DecorationImage(
                            image: NetworkImage(item.product.imageUrl!),
                            fit: BoxFit.cover,
                            onError: (exception, stackTrace) {},
                          )
                        : null,
                  ),
                  child: item.product.imageUrl == null
                      ? Icon(
                          item.product.isService
                              ? Icons.miscellaneous_services
                              : Icons.inventory_2,
                          color: AppColors.lightTextSecondary,
                        )
                      : null,
                ),
                const SizedBox(width: 8),

                // معلومات المنتج
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.product.name,
                        style:
                            AppTypography.lightTextTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ) ??
                                const AppTypography(fontWeight: FontWeight.bold),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        '${item.price.toStringAsFixed(2)} × ${item.quantity.toStringAsFixed(item.product.isService ? 2 : 0)}',
                        style: AppTypography.lightTextTheme.bodySmall,
                      ),
                    ],
                  ),
                ),

                // المجموع
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      item.total.toStringAsFixed(2),
                      style: AppTypography.lightTextTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ) ??
                          const AppTypography(fontWeight: FontWeight.bold),
                    ),
                    if (item.taxRate > 0)
                      Text(
                        'شامل ضريبة ${item.taxRate}%',
                        style: AppTypography.lightTextTheme.bodySmall,
                      ),
                  ],
                ),
              ],
            ),

            const Divider(height: 16),

            // صف التحكم بالكمية
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // أزرار تغيير الكمية
                Row(
                  children: [
                    // زر النقص
                    _buildQuantityButton(
                      icon: Icons.remove,
                      onPressed: () {
                        final newQuantity = item.quantity - 1;
                        if (newQuantity > 0) {
                          onQuantityChanged(newQuantity);
                        } else {
                          onRemove();
                        }
                      },
                    ),

                    // حقل الكمية
                    Container(
                      width: 50,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      child: TextField(
                        controller: TextEditingController(
                          text: item.quantity.toStringAsFixed(
                            item.product.isService ? 2 : 0,
                          ),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                        ),
                        textAlign: TextAlign.center,
                        decoration: const InputDecoration(
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 8,
                          ),
                          border: OutlineInputBorder(),
                        ),
                        onSubmitted: (value) {
                          final newQuantity = double.tryParse(value);
                          if (newQuantity != null && newQuantity > 0) {
                            onQuantityChanged(newQuantity);
                          }
                        },
                      ),
                    ),

                    // زر الزيادة
                    _buildQuantityButton(
                      icon: Icons.add,
                      onPressed: () {
                        onQuantityChanged(item.quantity + 1);
                      },
                    ),
                  ],
                ),

                // زر الحذف
                IconButton(
                  icon: const Icon(Icons.delete),
                  color: AppColors.lightTextSecondary,
                  onPressed: onRemove,
                  tooltip: 'حذف',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر تغيير الكمية
  Widget _buildQuantityButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: AppColors.lightTextSecondary,
        borderRadius: BorderRadius.circular(4),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: AppColors.lightTextSecondary,
          size: 16,
        ),
        padding: EdgeInsets.zero,
        onPressed: onPressed,
      ),
    );
  }
}
