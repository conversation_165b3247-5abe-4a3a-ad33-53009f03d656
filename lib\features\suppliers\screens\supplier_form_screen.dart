import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/models/supplier.dart';
import '../presenters/supplier_presenter.dart';
import '../../accounts/presenters/account_presenter.dart'; // إضافة مقدم الحسابات
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';

class SupplierFormScreen extends StatefulWidget {
  final Supplier? supplier;

  const SupplierFormScreen({Key? key, this.supplier}) : super(key: key);

  @override
  State<SupplierFormScreen> createState() => _SupplierFormScreenState();
}

class _SupplierFormScreenState extends State<SupplierFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late SupplierPresenter _presenter;
  late AccountPresenter _accountPresenter;

  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _taxNumberController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isActive = true;

  String? _selectedAccountId; // معرف الحساب المحدد
  List<Map<String, dynamic>> _accounts = []; // قائمة الحسابات
  bool _isLoadingAccounts = false; // حالة تحميل الحسابات

  @override
  void initState() {
    super.initState();
    _presenter = AppProviders.getLazyPresenter<SupplierPresenter>(
        () => SupplierPresenter());
    _accountPresenter = AppProviders.getLazyPresenter<AccountPresenter>(
        () => AccountPresenter());
    _initializeFields();
    _loadAccounts();
  }

  void _initializeFields() {
    if (widget.supplier != null) {
      _nameController.text = widget.supplier!.name;
      _phoneController.text = widget.supplier!.phone ?? '';
      _emailController.text = widget.supplier!.email ?? '';
      _addressController.text = widget.supplier!.address ?? '';
      _taxNumberController.text = widget.supplier!.taxNumber ?? '';
      _notesController.text = widget.supplier!.notes ?? '';
      _isActive = widget.supplier!.isActive;
      _selectedAccountId = widget.supplier!.accountId; // تعيين معرف الحساب
    }
  }

  // دالة لتحميل الحسابات من شجرة الحسابات
  Future<void> _loadAccounts() async {
    setState(() {
      _isLoadingAccounts = true;
    });

    try {
      // استخدام الاستماع للتغييرات بدلاً من الاستدعاء المباشر
      // تم تعديل هذا الجزء لتجنب مشكلة setState أثناء البناء
      _accountPresenter.addListener(_updateAccountsList);

      // تحقق مما إذا كانت الحسابات محملة بالفعل
      if (_accountPresenter.accounts.isEmpty) {
        // استدعاء تحميل الحسابات بشكل منفصل
        Future.microtask(() => _accountPresenter.loadAccounts());
      } else {
        // إذا كانت الحسابات محملة بالفعل، قم بتحديث القائمة
        _updateAccountsList();
      }
    } catch (e) {
      setState(() {
        _isLoadingAccounts = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load accounts: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // دالة لتحديث قائمة الحسابات
  void _updateAccountsList() {
    if (mounted) {
      setState(() {
        // الحصول على الحسابات من نوع "payable" (ذمم دائنة) أو "supplier" (موردين)
        _accounts = _accountPresenter.accounts.where((account) {
          final type = account['type']?.toString().toLowerCase() ?? '';
          return type == 'liability' || type == 'payable' || type == 'supplier';
        }).toList();

        _isLoadingAccounts = false;
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _taxNumberController.dispose();
    _notesController.dispose();
    // إزالة المستمع عند التخلص من الشاشة
    _accountPresenter.removeListener(_updateAccountsList);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: widget.supplier == null ? 'Add Supplier' : 'Edit Supplier',
        showBackButton: true,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  _buildBasicInfoSection(),
                  const SizedBox(height: 16),
                  _buildContactInfoSection(),
                  const SizedBox(height: 16),
                  _buildAdditionalInfoSection(),
                ],
              ),
            ),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Supplier Name',
                hintText: 'Enter supplier name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter supplier name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // حقل اختيار الحساب من شجرة الحسابات
            _buildAccountSelector(),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Active'),
              subtitle: const Text('Enable this supplier for selection'),
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  // دالة لبناء حقل اختيار الحساب
  Widget _buildAccountSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Account*',
              style: AppTypography(fontWeight: FontWeight.bold),
            ),
            const SizedBox(width: 8),
            if (_isLoadingAccounts)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String?>(
          value: _selectedAccountId,
          decoration: const InputDecoration(
            hintText: 'Select an account',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          ),
          items: _accounts.map((account) {
            final accountName = account['name'] as String;
            final accountCode = account['code'] as String?;
            final accountType = account['type'] as String?;

            return DropdownMenuItem<String?>(
              value: account['id'].toString(),
              child: Row(
                children: [
                  Icon(
                    _getAccountIcon(accountType),
                    size: 16,
                    color: _getAccountColor(accountType),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      accountName,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (accountCode != null) ...[
                    const SizedBox(width: 8),
                    Text(
                      '#$accountCode',
                      style: const AppTypography(
                        fontSize: 12,
                        color: AppColors.lightTextSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedAccountId = value;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select an account';
            }
            return null;
          },
          isExpanded: true,
        ),
        const SizedBox(height: 4),
        TextButton.icon(
          onPressed: () {
            // هنا يمكن إضافة الانتقال إلى شاشة إضافة حساب جديد
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Navigate to add new account screen'),
              ),
            );
          },
          icon: const Icon(Icons.add, size: 16),
          label: const Text('Add New Account'),
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: const Size(0, 32),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            alignment: Alignment.centerLeft,
          ),
        ),
      ],
    );
  }

  // دالة للحصول على أيقونة الحساب حسب نوعه
  IconData _getAccountIcon(String? accountType) {
    switch (accountType?.toLowerCase()) {
      case 'liability':
        return Icons.account_balance;
      case 'payable':
        return Icons.payments;
      case 'supplier':
        return Icons.business;
      default:
        return Icons.account_circle;
    }
  }

  // دالة للحصول على لون الحساب حسب نوعه
  Color _getAccountColor(String? accountType) {
    switch (accountType?.toLowerCase()) {
      case 'liability':
        return AppColors.error;
      case 'payable':
        return AppColors.warning;
      case 'supplier':
        return AppColors.accent;
      default:
        return AppColors.secondary;
    }
  }

  Widget _buildContactInfoSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contact Information',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                hintText: 'Enter phone number',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                hintText: 'Enter email address',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'Address',
                hintText: 'Enter supplier address',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.location_on),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Additional Information',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _taxNumberController,
              decoration: const InputDecoration(
                labelText: 'Tax Number',
                hintText: 'Enter tax registration number',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes',
                hintText: 'Enter any additional notes',
                border: OutlineInputBorder(),
              ),
              maxLines: 5,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightSurface,
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _submitForm,
        style: ElevatedButton.styleFrom(
          minimumSize: const Size.fromHeight(50),
        ),
        child: Text(
          widget.supplier == null ? 'Create Supplier' : 'Update Supplier',
        ),
      ),
    );
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      // التحقق من اختيار حساب
      if (_selectedAccountId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select an account for the supplier'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      final supplier = Supplier(
        id: widget.supplier?.id,
        name: _nameController.text,
        phone: _phoneController.text.isEmpty ? null : _phoneController.text,
        email: _emailController.text.isEmpty ? null : _emailController.text,
        address:
            _addressController.text.isEmpty ? null : _addressController.text,
        taxNumber: _taxNumberController.text.isEmpty
            ? null
            : _taxNumberController.text,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        isActive: _isActive,
        accountId: _selectedAccountId, // إضافة معرف الحساب
      );

      bool success;
      if (widget.supplier == null) {
        success = await _presenter.addSupplier(supplier);
      } else {
        success = await _presenter.updateSupplier(supplier);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? '${widget.supplier == null ? 'Created' : 'Updated'} supplier successfully'
                : 'Failed to ${widget.supplier == null ? 'create' : 'update'} supplier'),
            backgroundColor: success ? AppColors.success : AppColors.error,
          ),
        );

        if (success) {
          Navigator.pop(context, true);
        }
      }
    }
  }
}
