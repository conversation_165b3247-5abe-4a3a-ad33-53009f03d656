import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج الإشعار الموحد
class Notification extends BaseModel {
  // معلومات أساسية
  final String title;
  final String body;
  final String type; // info, warning, error, success
  
  // معلومات الارتباط
  final String? module; // المودول المرتبط بالإشعار (sales, purchases, inventory, etc.)
  final String? action; // الإجراء المرتبط بالإشعار (create, update, delete, etc.)
  final String? entityId; // معرف الكيان المرتبط بالإشعار
  final String? entityType; // نوع الكيان المرتبط بالإشعار
  
  // معلومات الحالة
  final bool isRead;
  final DateTime date;
  
  // معلومات إضافية
  final String? icon;
  final String? color;
  final Map<String, dynamic>? data;
  final String? userId; // معرف المستخدم المرسل إليه الإشعار

  Notification({
    String? id,
    required this.title,
    required this.body,
    this.type = 'info',
    this.module,
    this.action,
    this.entityId,
    this.entityType,
    this.isRead = false,
    DateTime? date,
    this.icon,
    this.color,
    this.data,
    this.userId,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : 
    date = date ?? DateTime.now(),
    super(
      id: id ?? const Uuid().v4(),
      createdAt: createdAt ?? DateTime.now(),
      createdBy: createdBy,
      updatedAt: updatedAt,
      updatedBy: updatedBy,
      isDeleted: isDeleted,
    );

  /// إنشاء نسخة من هذا الإشعار مع استبدال الحقول المحددة بقيم جديدة
  Notification copyWith({
    String? id,
    String? title,
    String? body,
    String? type,
    String? module,
    String? action,
    String? entityId,
    String? entityType,
    bool? isRead,
    DateTime? date,
    String? icon,
    String? color,
    Map<String, dynamic>? data,
    String? userId,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Notification(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      module: module ?? this.module,
      action: action ?? this.action,
      entityId: entityId ?? this.entityId,
      entityType: entityType ?? this.entityType,
      isRead: isRead ?? this.isRead,
      date: date ?? this.date,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      data: data ?? this.data,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل الإشعار إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type,
      'module': module,
      'action': action,
      'entity_id': entityId,
      'entity_type': entityType,
      'is_read': isRead ? 1 : 0,
      'date': date.toIso8601String(),
      'icon': icon,
      'color': color,
      'data': data != null ? jsonEncode(data) : null,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء إشعار من Map
  factory Notification.fromMap(Map<String, dynamic> map) {
    return Notification(
      id: map['id'],
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      type: map['type'] ?? 'info',
      module: map['module'],
      action: map['action'],
      entityId: map['entity_id'],
      entityType: map['entity_type'],
      isRead: map['is_read'] == 1,
      date: map['date'] != null ? DateTime.parse(map['date']) : DateTime.now(),
      icon: map['icon'],
      color: map['color'],
      data: map['data'] != null
          ? (map['data'] is String
              ? jsonDecode(map['data'])
              : map['data'] as Map<String, dynamic>)
          : null,
      userId: map['user_id'],
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل الإشعار إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء إشعار من JSON
  factory Notification.fromJson(String source) =>
      Notification.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'Notification(id: $id, title: $title, isRead: $isRead)';
  }
  
  /// إنشاء إشعار معلومات
  factory Notification.info({
    required String title,
    required String body,
    String? module,
    String? action,
    String? entityId,
    String? entityType,
    String? userId,
    Map<String, dynamic>? data,
  }) {
    return Notification(
      title: title,
      body: body,
      type: 'info',
      module: module,
      action: action,
      entityId: entityId,
      entityType: entityType,
      userId: userId,
      data: data,
      icon: 'info',
      color: '#2196F3',
    );
  }
  
  /// إنشاء إشعار نجاح
  factory Notification.success({
    required String title,
    required String body,
    String? module,
    String? action,
    String? entityId,
    String? entityType,
    String? userId,
    Map<String, dynamic>? data,
  }) {
    return Notification(
      title: title,
      body: body,
      type: 'success',
      module: module,
      action: action,
      entityId: entityId,
      entityType: entityType,
      userId: userId,
      data: data,
      icon: 'check_circle',
      color: '#4CAF50',
    );
  }
  
  /// إنشاء إشعار تحذير
  factory Notification.warning({
    required String title,
    required String body,
    String? module,
    String? action,
    String? entityId,
    String? entityType,
    String? userId,
    Map<String, dynamic>? data,
  }) {
    return Notification(
      title: title,
      body: body,
      type: 'warning',
      module: module,
      action: action,
      entityId: entityId,
      entityType: entityType,
      userId: userId,
      data: data,
      icon: 'warning',
      color: '#FF9800',
    );
  }
  
  /// إنشاء إشعار خطأ
  factory Notification.error({
    required String title,
    required String body,
    String? module,
    String? action,
    String? entityId,
    String? entityType,
    String? userId,
    Map<String, dynamic>? data,
  }) {
    return Notification(
      title: title,
      body: body,
      type: 'error',
      module: module,
      action: action,
      entityId: entityId,
      entityType: entityType,
      userId: userId,
      data: data,
      icon: 'error',
      color: '#F44336',
    );
  }
}
