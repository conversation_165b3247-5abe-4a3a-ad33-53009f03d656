import 'package:flutter/material.dart';
import '../theme/index.dart';

/// نظام التخطيطات الموحد لتطبيق تاجر بلس
/// يحتوي على جميع تخطيطات الواجهة المستخدمة في التطبيق
///
/// **المميزات:**
/// - تصميم موحد ومتناسق لجميع التخطيطات
/// - دعم كامل للوضع المظلم/الفاتح
/// - تخطيطات متجاوبة ومرنة
/// - تحميل كسول للعناصر الثقيلة
/// - دوال مساعدة سريعة
/// - تعليقات شاملة باللغة العربية

// ═══════════════════════════════════════════════════════════════════════════════
// ● الأنواع والثوابت
// ═══════════════════════════════════════════════════════════════════════════════

/// أنواع التخطيطات المختلفة
enum AkLayoutType {
  /// تخطيط عمودي
  column,

  /// تخطيط أفقي
  row,

  /// تخطيط شبكي
  grid,

  /// تخطيط مرن
  flex,

  /// تخطيط ملفوف
  wrap,
}

/// أنواع المحاذاة
enum AkAlignment {
  /// بداية
  start,

  /// وسط
  center,

  /// نهاية
  end,

  /// توزيع متساوي
  spaceBetween,

  /// توزيع حول
  spaceAround,

  /// توزيع متساوي مع مساحات
  spaceEvenly,
}

/// أحجام المساحات
enum AkSpacingSize {
  /// مساحة صغيرة جداً
  tiny,

  /// مساحة صغيرة
  small,

  /// مساحة افتراضية
  medium,

  /// مساحة كبيرة
  large,

  /// مساحة كبيرة جداً
  extraLarge,
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● 1. الصف المتجاوب الموحد (AkRow)
// ═══════════════════════════════════════════════════════════════════════════════

/// صف متجاوب موحد مع تصميم متناسق
///
/// **المميزات:**
/// - تصميم موحد لجميع الصفوف
/// - محاذاة قابلة للتخصيص
/// - مساحات موحدة بين العناصر
/// - دعم كامل للوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkRow(
///   children: [Text('عنصر 1'), Text('عنصر 2')],
///   alignment: AkAlignment.spaceBetween,
///   spacing: AkSpacingSize.medium,
/// )
/// ```
class AkRow extends StatelessWidget {
  /// قائمة العناصر الفرعية
  final List<Widget> children;

  /// نوع المحاذاة
  final AkAlignment alignment;

  /// المحاذاة العمودية
  final CrossAxisAlignment crossAxisAlignment;

  /// حجم المساحة بين العناصر
  final AkSpacingSize spacing;

  /// هل يتم توسيع الصف ليملأ العرض المتاح
  final bool expanded;

  /// الحشو الداخلي
  final EdgeInsetsGeometry? padding;

  /// الهامش الخارجي
  final EdgeInsetsGeometry? margin;

  /// لون الخلفية
  final Color? backgroundColor;

  /// نصف قطر الزوايا
  final double? borderRadius;

  const AkRow({
    super.key,
    required this.children,
    this.alignment = AkAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.spacing = AkSpacingSize.medium,
    this.expanded = false,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد المحاذاة الأفقية
    final mainAxisAlignment = _getMainAxisAlignment();

    // تحديد المساحة بين العناصر
    final spacingValue = _getSpacingValue();

    // بناء قائمة العناصر مع المساحات
    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1 && spacingValue > 0) {
        spacedChildren.add(SizedBox(width: spacingValue));
      }
    }

    Widget rowWidget = Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: expanded ? MainAxisSize.max : MainAxisSize.min,
      children: spacedChildren,
    );

    // إضافة الحاوية إذا كانت هناك خصائص إضافية
    if (padding != null ||
        margin != null ||
        backgroundColor != null ||
        borderRadius != null) {
      rowWidget = Container(
        padding: padding,
        margin: margin,
        decoration: backgroundColor != null || borderRadius != null
            ? BoxDecoration(
                color: backgroundColor,
                borderRadius: borderRadius != null
                    ? BorderRadius.circular(borderRadius!)
                    : null,
              )
            : null,
        child: rowWidget,
      );
    }

    return rowWidget;
  }

  /// الحصول على محاذاة المحور الرئيسي
  MainAxisAlignment _getMainAxisAlignment() {
    switch (alignment) {
      case AkAlignment.start:
        return MainAxisAlignment.start;
      case AkAlignment.center:
        return MainAxisAlignment.center;
      case AkAlignment.end:
        return MainAxisAlignment.end;
      case AkAlignment.spaceBetween:
        return MainAxisAlignment.spaceBetween;
      case AkAlignment.spaceAround:
        return MainAxisAlignment.spaceAround;
      case AkAlignment.spaceEvenly:
        return MainAxisAlignment.spaceEvenly;
    }
  }

  /// الحصول على قيمة المساحة
  double _getSpacingValue() {
    switch (spacing) {
      case AkSpacingSize.tiny:
        return AppDimensions.tinySpacing;
      case AkSpacingSize.small:
        return AppDimensions.smallSpacing;
      case AkSpacingSize.medium:
        return AppDimensions.defaultSpacing;
      case AkSpacingSize.large:
        return AppDimensions.largeSpacing;
      case AkSpacingSize.extraLarge:
        return AppDimensions.extraLargeSpacing;
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● 2. العمود المتجاوب الموحد (AkColumn)
// ═══════════════════════════════════════════════════════════════════════════════

/// عمود متجاوب موحد مع تصميم متناسق
///
/// **المميزات:**
/// - تصميم موحد لجميع الأعمدة
/// - محاذاة قابلة للتخصيص
/// - مساحات موحدة بين العناصر
/// - دعم كامل للوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkColumn(
///   children: [Text('عنصر 1'), Text('عنصر 2')],
///   alignment: AkAlignment.center,
///   spacing: AkSpacingSize.large,
/// )
/// ```
class AkColumn extends StatelessWidget {
  /// قائمة العناصر الفرعية
  final List<Widget> children;

  /// نوع المحاذاة
  final AkAlignment alignment;

  /// المحاذاة الأفقية
  final CrossAxisAlignment crossAxisAlignment;

  /// حجم المساحة بين العناصر
  final AkSpacingSize spacing;

  /// هل يتم توسيع العمود ليملأ الارتفاع المتاح
  final bool expanded;

  /// الحشو الداخلي
  final EdgeInsetsGeometry? padding;

  /// الهامش الخارجي
  final EdgeInsetsGeometry? margin;

  /// لون الخلفية
  final Color? backgroundColor;

  /// نصف قطر الزوايا
  final double? borderRadius;

  const AkColumn({
    super.key,
    required this.children,
    this.alignment = AkAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.spacing = AkSpacingSize.medium,
    this.expanded = false,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد المحاذاة العمودية
    final mainAxisAlignment = _getMainAxisAlignment();

    // تحديد المساحة بين العناصر
    final spacingValue = _getSpacingValue();

    // بناء قائمة العناصر مع المساحات
    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1 && spacingValue > 0) {
        spacedChildren.add(SizedBox(height: spacingValue));
      }
    }

    Widget columnWidget = Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: expanded ? MainAxisSize.max : MainAxisSize.min,
      children: spacedChildren,
    );

    // إضافة الحاوية إذا كانت هناك خصائص إضافية
    if (padding != null ||
        margin != null ||
        backgroundColor != null ||
        borderRadius != null) {
      columnWidget = Container(
        padding: padding,
        margin: margin,
        decoration: backgroundColor != null || borderRadius != null
            ? BoxDecoration(
                color: backgroundColor,
                borderRadius: borderRadius != null
                    ? BorderRadius.circular(borderRadius!)
                    : null,
              )
            : null,
        child: columnWidget,
      );
    }

    return columnWidget;
  }

  /// الحصول على محاذاة المحور الرئيسي
  MainAxisAlignment _getMainAxisAlignment() {
    switch (alignment) {
      case AkAlignment.start:
        return MainAxisAlignment.start;
      case AkAlignment.center:
        return MainAxisAlignment.center;
      case AkAlignment.end:
        return MainAxisAlignment.end;
      case AkAlignment.spaceBetween:
        return MainAxisAlignment.spaceBetween;
      case AkAlignment.spaceAround:
        return MainAxisAlignment.spaceAround;
      case AkAlignment.spaceEvenly:
        return MainAxisAlignment.spaceEvenly;
    }
  }

  /// الحصول على قيمة المساحة
  double _getSpacingValue() {
    switch (spacing) {
      case AkSpacingSize.tiny:
        return AppDimensions.tinySpacing;
      case AkSpacingSize.small:
        return AppDimensions.smallSpacing;
      case AkSpacingSize.medium:
        return AppDimensions.defaultSpacing;
      case AkSpacingSize.large:
        return AppDimensions.largeSpacing;
      case AkSpacingSize.extraLarge:
        return AppDimensions.extraLargeSpacing;
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● الدوال المساعدة السريعة (AkLayouts)
// ═══════════════════════════════════════════════════════════════════════════════

/// فئة الدوال المساعدة السريعة للتخطيطات
/// توفر طرق سريعة لإنشاء التخطيطات الشائعة
class AkLayouts {
  AkLayouts._();

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال الصفوف السريعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// صف مع توزيع متساوي
  static Widget spaceBetween({
    required List<Widget> children,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
  }) {
    return AkRow(
      alignment: AkAlignment.spaceBetween,
      crossAxisAlignment: crossAxisAlignment,
      spacing: AkSpacingSize.tiny,
      children: children,
    );
  }

  /// صف مع محاذاة في الوسط
  static Widget center({
    required List<Widget> children,
    AkSpacingSize spacing = AkSpacingSize.medium,
  }) {
    return AkRow(
      alignment: AkAlignment.center,
      spacing: spacing,
      children: children,
    );
  }

  /// صف مع محاذاة في النهاية
  static Widget end({
    required List<Widget> children,
    AkSpacingSize spacing = AkSpacingSize.medium,
  }) {
    return AkRow(
      alignment: AkAlignment.end,
      spacing: spacing,
      children: children,
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال الأعمدة السريعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// عمود مع توزيع متساوي
  static Widget columnSpaceBetween({
    required List<Widget> children,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
  }) {
    return AkColumn(
      alignment: AkAlignment.spaceBetween,
      crossAxisAlignment: crossAxisAlignment,
      spacing: AkSpacingSize.tiny,
      children: children,
    );
  }

  /// عمود مع محاذاة في الوسط
  static Widget columnCenter({
    required List<Widget> children,
    AkSpacingSize spacing = AkSpacingSize.medium,
  }) {
    return AkColumn(
      alignment: AkAlignment.center,
      spacing: spacing,
      children: children,
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال متخصصة للمشروع التجاري
  // ───────────────────────────────────────────────────────────────────────────────

  /// تخطيط بطاقة منتج
  static Widget productCard({
    required Widget image,
    required Widget title,
    required Widget price,
    Widget? description,
    Widget? actions,
  }) {
    return AkColumn(
      spacing: AkSpacingSize.small,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        image,
        title,
        if (description != null) description,
        price,
        if (actions != null) actions,
      ],
    );
  }

  /// تخطيط صف إحصائيات
  static Widget statsRow({
    required List<Widget> stats,
  }) {
    return AkRow(
      alignment: AkAlignment.spaceEvenly,
      spacing: AkSpacingSize.medium,
      children: stats,
    );
  }

  /// تخطيط رأس الصفحة
  static Widget pageHeader({
    required Widget title,
    Widget? subtitle,
    Widget? actions,
  }) {
    return AkColumn(
      spacing: AkSpacingSize.small,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (actions != null)
          AkRow(
            alignment: AkAlignment.spaceBetween,
            children: [title, actions],
          )
        else
          title,
        if (subtitle != null) subtitle,
      ],
    );
  }

  /// تخطيط عنصر قائمة
  static Widget listItem({
    Widget? leading,
    required Widget title,
    Widget? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.defaultMargin),
        child: AkRow(
          spacing: AkSpacingSize.medium,
          children: [
            if (leading != null) leading,
            Expanded(
              child: AkColumn(
                spacing: AkSpacingSize.tiny,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  title,
                  if (subtitle != null) subtitle,
                ],
              ),
            ),
            if (trailing != null) trailing,
          ],
        ),
      ),
    );
  }

  /// تخطيط نموذج
  static Widget form({
    required List<Widget> fields,
    Widget? actions,
    AkSpacingSize spacing = AkSpacingSize.medium,
  }) {
    return AkColumn(
      spacing: spacing,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ...fields,
        if (actions != null) actions,
      ],
    );
  }
}
