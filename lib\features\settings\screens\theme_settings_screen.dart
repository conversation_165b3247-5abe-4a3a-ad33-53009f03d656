import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/theme/index.dart';

/// شاشة إعدادات الثيم والألوان
/// تسمح للمستخدم بتخصيص مظهر التطبيق
class ThemeSettingsScreen extends StatefulWidget {
  const ThemeSettingsScreen({Key? key}) : super(key: key);

  @override
  State<ThemeSettingsScreen> createState() => _ThemeSettingsScreenState();
}

class _ThemeSettingsScreenState extends State<ThemeSettingsScreen> {
  late final ThemeManager _themeManager;

  @override
  void initState() {
    super.initState();
    _themeManager =
        AppProviders.getLazyPresenter<ThemeManager>(() => ThemeManager());
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _themeManager,
      builder: (context, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('إعدادات المظهر'),
            leading: Icon<PERSON>utton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: SingleChildScrollView(
            padding: AppDimensions.screenPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // قسم وضع الثيم (فاتح/داكن)
                _buildThemeModeSection(_themeManager),

                SizedBox(height: AppDimensions.largeSpacing),

                // قسم الألوان
                _buildColorThemeSection(_themeManager),

                SizedBox(height: AppDimensions.largeSpacing),

                // معلومات الثيم الحالي
                _buildCurrentThemeInfo(_themeManager),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء قسم وضع الثيم
  Widget _buildThemeModeSection(ThemeManager themeManager) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: AppDimensions.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.brightness_6,
                  color: DynamicColors.primary,
                  size: AppDimensions.defaultIconSize,
                ),
                SizedBox(width: AppDimensions.smallSpacing),
                Text(
                  'وضع الإضاءة',
                  style: AppTypography.lightTextTheme.titleLarge?.copyWith(
                    fontWeight: AppTypography.weightSemiBold,
                  ),
                ),
              ],
            ),

            SizedBox(height: AppDimensions.mediumSpacing),

            // خيارات وضع الثيم
            _buildThemeModeOption(
              themeManager,
              ThemeMode.light,
              'الوضع الفاتح',
              'استخدام الوضع الفاتح دائماً',
              Icons.light_mode,
            ),

            _buildThemeModeOption(
              themeManager,
              ThemeMode.dark,
              'الوضع الداكن',
              'استخدام الوضع الداكن دائماً',
              Icons.dark_mode,
            ),

            _buildThemeModeOption(
              themeManager,
              ThemeMode.system,
              'حسب النظام',
              'يتبع إعدادات النظام تلقائياً',
              Icons.brightness_auto,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء خيار وضع الثيم
  Widget _buildThemeModeOption(
    ThemeManager themeManager,
    ThemeMode mode,
    String title,
    String subtitle,
    IconData icon,
  ) {
    final isSelected = themeManager.themeMode == mode;

    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.smallSpacing),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => themeManager.setThemeMode(mode),
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          child: Container(
            padding: AppDimensions.cardPadding,
            decoration: BoxDecoration(
              color: isSelected
                  ? DynamicColors.primary.withValues(alpha: 0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              border: Border.all(
                color:
                    isSelected ? DynamicColors.primary : AppColors.lightBorder,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(AppDimensions.smallSpacing),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? DynamicColors.primary
                        : AppColors.lightSurfaceVariant,
                    borderRadius:
                        BorderRadius.circular(AppDimensions.smallRadius),
                  ),
                  child: Icon(
                    icon,
                    color: isSelected
                        ? AppColors.onPrimary
                        : AppColors.lightTextSecondary,
                    size: AppDimensions.smallIconSize,
                  ),
                ),
                SizedBox(width: AppDimensions.defaultSpacing),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTypography.lightTextTheme.bodyLarge?.copyWith(
                          fontWeight: isSelected
                              ? AppTypography.weightMedium
                              : AppTypography.weightRegular,
                          color: isSelected
                              ? DynamicColors.primary
                              : AppColors.lightTextPrimary,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: AppTypography.lightTextTheme.bodySmall?.copyWith(
                          color: AppColors.secondary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: DynamicColors.primary,
                    size: AppDimensions.smallIconSize,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء قسم الألوان
  Widget _buildColorThemeSection(ThemeManager themeManager) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: AppDimensions.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.palette,
                  color: DynamicColors.primary,
                  size: AppDimensions.defaultIconSize,
                ),
                SizedBox(width: AppDimensions.smallSpacing),
                Text(
                  'لون التطبيق',
                  style: AppTypography.lightTextTheme.titleLarge?.copyWith(
                    fontWeight: AppTypography.weightSemiBold,
                  ),
                ),
              ],
            ),

            SizedBox(height: AppDimensions.mediumSpacing),

            // قائمة الألوان المتاحة
            _buildColorThemeGrid(themeManager),
          ],
        ),
      ),
    );
  }

  /// بناء شبكة ألوان الثيمات
  Widget _buildColorThemeGrid(ThemeManager themeManager) {
    const availableThemes = AppColors.availableThemes;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: availableThemes.length,
      itemBuilder: (context, index) {
        final themeKey = availableThemes.keys.elementAt(index);
        final themeData = availableThemes[themeKey]!;
        final isSelected = themeManager.colorTheme == themeKey;

        return GestureDetector(
          onTap: () {
            themeManager.setColorTheme(themeKey);
            _showThemeAppliedMessage(context, themeKey);
          },
          child: Container(
            decoration: BoxDecoration(
              color: themeData['primary'],
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              border: Border.all(
                color: isSelected ? Colors.white : Colors.transparent,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: themeData['primary'].withValues(alpha: 0.3),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: isSelected
                ? const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 24,
                  )
                : null,
          ),
        );
      },
    );
  }

  /// عرض رسالة تأكيد تطبيق الثيم
  void _showThemeAppliedMessage(BuildContext context, String themeKey) {
    final themeData = AppColors.availableThemes[themeKey];
    final themeName = themeData?['name'] ?? 'الثيم الجديد';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'تم تطبيق $themeName بنجاح',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: themeData?['primary'] ?? AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// بناء معلومات الثيم الحالي
  Widget _buildCurrentThemeInfo(ThemeManager themeManager) {
    return Card(
      elevation: 4,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              DynamicColors.primary.withValues(alpha: 0.1),
              DynamicColors.primary.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        ),
        child: Padding(
          padding: AppDimensions.cardPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: DynamicColors.primary,
                    size: AppDimensions.defaultIconSize,
                  ),
                  SizedBox(width: AppDimensions.smallSpacing),
                  Text(
                    'الإعدادات الحالية',
                    style: AppTypography.lightTextTheme.titleLarge?.copyWith(
                      fontWeight: AppTypography.weightSemiBold,
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppDimensions.mediumSpacing),
              _buildInfoRow(
                  'وضع الإضاءة:', themeManager.getThemeModeDisplayName()),
              _buildInfoRow('لون التطبيق:', themeManager.colorThemeName),
              _buildInfoRow(
                  'الحالة:', themeManager.isDarkMode ? 'داكن' : 'فاتح'),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء صف المعلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimensions.smallSpacing),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
              color: AppColors.lightTextSecondary,
            ),
          ),
          Text(
            value,
            style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
              fontWeight: AppTypography.weightMedium,
              color: DynamicColors.primary,
            ),
          ),
        ],
      ),
    );
  }
}
