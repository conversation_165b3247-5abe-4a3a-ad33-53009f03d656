import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../utils/app_logger.dart';

/// خدمة لحفظ حالة الإعداد في ملف محلي
/// تستخدم كطريقة بديلة لـ SharedPreferences للتأكد من حفظ الحالة
class SetupFlagService {
  static const String _setupFlagFileName = 'setup_completed.flag';
  static const String _firstLaunchFileName = 'first_launch.flag';

  /// حفظ حالة اكتمال الإعداد في ملف محلي
  static Future<bool> setSetupCompleted(bool isCompleted) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_setupFlagFileName');

      // كتابة القيمة في الملف بغض النظر عن القيمة (true أو false)
      await file.writeAsString(
          'SETUP_COMPLETED=$isCompleted|${DateTime.now().toIso8601String()}');
      AppLogger.info('✅ تم حفظ حالة الإعداد في ملف محلي: $isCompleted');

      return true;
    } catch (e) {
      AppLogger.error('❌ خطأ في حفظ حالة الإعداد في ملف محلي: $e');
      return false;
    }
  }

  /// التحقق مما إذا كان الإعداد الأولي قد اكتمل
  static Future<bool> isSetupCompleted() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_setupFlagFileName');

      final bool exists = await file.exists();
      AppLogger.info('🔍 قراءة حالة الإعداد من ملف محلي: $exists');

      // إذا كان الملف غير موجود، فهذا يعني أن الإعداد غير مكتمل
      if (!exists) {
        return false;
      }

      // قراءة محتوى الملف للتأكد من صحته
      final content = await file.readAsString();
      AppLogger.info('🔍 محتوى ملف الإعداد: $content');

      // تحليل محتوى الملف للحصول على القيمة
      if (content.startsWith('SETUP_COMPLETED=')) {
        final parts = content.split('|');
        if (parts.isNotEmpty) {
          final valueStr = parts[0].replaceFirst('SETUP_COMPLETED=', '');
          final isSetupCompleted = valueStr.toLowerCase() == 'true';
          AppLogger.info(
              '🔍 قراءة حالة الإعداد من ملف محلي: $isSetupCompleted');
          return isSetupCompleted;
        }
      }

      // إذا لم نتمكن من تحليل المحتوى، نفترض أن الإعداد مكتمل (لأن الملف موجود)
      return true;
    } catch (e) {
      AppLogger.error('❌ خطأ في قراءة حالة الإعداد من ملف محلي: $e');
      return false;
    }
  }

  /// حفظ حالة التشغيل الأول في ملف محلي
  static Future<bool> setFirstLaunch(bool isFirstLaunch) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_firstLaunchFileName');

      // كتابة القيمة في الملف بغض النظر عن القيمة (true أو false)
      await file.writeAsString(
          'FIRST_LAUNCH=$isFirstLaunch|${DateTime.now().toIso8601String()}');
      AppLogger.info('✅ تم حفظ حالة التشغيل الأول في ملف محلي: $isFirstLaunch');

      return true;
    } catch (e) {
      AppLogger.error('❌ خطأ في حفظ حالة التشغيل الأول في ملف محلي: $e');
      return false;
    }
  }

  /// التحقق مما إذا كان هذا هو التشغيل الأول للتطبيق
  static Future<bool> isFirstLaunch() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_firstLaunchFileName');

      // التحقق من وجود ملف التشغيل الأول
      final bool exists = await file.exists();

      // إذا كان الملف غير موجود، فهذا يعني أنه التشغيل الأول
      if (!exists) {
        AppLogger.info(
            '🔍 قراءة حالة التشغيل الأول من ملف محلي: true (الملف غير موجود)');
        return true;
      }

      // إذا كان الملف موجودًا، نقرأ محتواه للتحقق من القيمة
      final content = await file.readAsString();
      AppLogger.info('🔍 محتوى ملف التشغيل الأول: $content');

      // تحليل محتوى الملف للحصول على القيمة
      if (content.startsWith('FIRST_LAUNCH=')) {
        final parts = content.split('|');
        if (parts.isNotEmpty) {
          final valueStr = parts[0].replaceFirst('FIRST_LAUNCH=', '');
          final isFirstLaunch = valueStr.toLowerCase() == 'true';
          AppLogger.info(
              '🔍 قراءة حالة التشغيل الأول من ملف محلي: $isFirstLaunch');
          return isFirstLaunch;
        }
      }

      // إذا لم نتمكن من تحليل المحتوى، نفترض أنه ليس التشغيل الأول
      AppLogger.info(
          '🔍 قراءة حالة التشغيل الأول من ملف محلي: false (تم العثور على الملف)');
      return false;
    } catch (e) {
      AppLogger.error('❌ خطأ في قراءة حالة التشغيل الأول من ملف محلي: $e');
      return true; // نفترض أنه التشغيل الأول في حالة حدوث خطأ
    }
  }

  /// إعادة تعيين جميع الإعدادات
  static Future<bool> resetAll() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final setupFile = File('${directory.path}/$_setupFlagFileName');
      final firstLaunchFile = File('${directory.path}/$_firstLaunchFileName');

      bool success = true;

      if (await setupFile.exists()) {
        await setupFile.delete();
      }

      if (await firstLaunchFile.exists()) {
        await firstLaunchFile.delete();
      }

      AppLogger.info('✅ تم إعادة تعيين جميع الإعدادات المحلية');

      return success;
    } catch (e) {
      AppLogger.error('❌ خطأ في إعادة تعيين الإعدادات المحلية: $e');
      return false;
    }
  }
}
