# 🎯 نظام حقول الإدخال الموحد والشامل (AK Inputs System)

## 🎯 نظرة عامة

نظام حقول إدخال موحد وشامل يوفر جميع أنواع الحقول المطلوبة في التطبيق مع تصميم متناسق ودعم كامل للوضع المظلم/الفاتح.

## 🔢 الحقول الرقمية

### 1. AkCurrencyInput - حقل المبالغ المالية
```dart
AkCurrencyInput(
  label: 'سعر المنتج',
  controller: priceController,
  currencySymbol: 'ر.ي',
  minAmount: 0.01,
  maxAmount: 999999.99,
  isRequired: true,
)
```

**المميزات:**
- تنسيق تلقائي للمبالغ مع فاصل الآلاف
- رمز العملة القابل للتخصيص (ر.ي، $، €، إلخ)
- تحقق من صحة المبلغ مع رسائل خطأ واضحة
- دعم كامل للوضع المظلم والفاتح

### 2. AkPercentageInput - حقل النسب المئوية
```dart
AkPercentageInput(
  label: 'نسبة الخصم',
  controller: discountController,
  minPercentage: 0.0,
  maxPercentage: 50.0,
  isRequired: true,
)
```

**المميزات:**
- تحقق تلقائي من النطاق (0-100% افتراضياً)
- تنسيق تلقائي مع رمز النسبة المئوية
- دعم كامل للوضع المظلم والفاتح

## 📞 حقول الاتصال

### 3. AkPhoneInput - حقل رقم الهاتف
```dart
AkPhoneInput(
  label: 'رقم الهاتف',
  controller: phoneController,
  defaultCountryCode: '+967',
  isRequired: true,
)
```

**المميزات:**
- تنسيق تلقائي لأرقام الهاتف اليمنية
- رمز الدولة القابل للتخصيص (+967 افتراضي لليمن)
- تحقق من صحة رقم الهاتف للشبكات اليمنية
- دعم كامل للوضع المظلم والفاتح

### 4. AkEmailInput - حقل البريد الإلكتروني
```dart
AkEmailInput(
  label: 'البريد الإلكتروني',
  controller: emailController,
  showSuggestions: true,
  isRequired: true,
)
```

**المميزات:**
- تحقق متقدم من صحة البريد الإلكتروني
- اقتراحات تلقائية للنطاقات الشائعة
- دعم كامل للوضع المظلم والفاتح

## 🔐 حقول الأمان

### 5. AkPasswordInput - حقل كلمة المرور
```dart
AkPasswordInput(
  label: 'كلمة المرور',
  controller: passwordController,
  showStrengthIndicator: true,
  isRequired: true,
)
```

**المميزات:**
- مؤشر قوة كلمة المرور
- إظهار/إخفاء كلمة المرور
- تحقق من معايير الأمان
- دعم كامل للوضع المظلم والفاتح

## 📝 حقول النصوص المتخصصة

### 6. AkLongTextInput - حقل النصوص الطويلة
```dart
AkLongTextInput(
  label: 'ملاحظات',
  controller: notesController,
  maxLength: 500,
  minLines: 3,
  maxLines: 6,
)
```

**المميزات:**
- دعم النصوص متعددة الأسطر
- عداد الأحرف التفاعلي
- تحديد الحد الأدنى والأقصى للأسطر
- دعم كامل للوضع المظلم والفاتح

### 7. AkSearchInput - حقل البحث
```dart
AkSearchInput(
  label: 'البحث',
  controller: searchController,
  showSuggestions: true,
  onSearch: (query) => performSearch(query),
)
```

**المميزات:**
- اقتراحات تلقائية أثناء الكتابة
- تحميل كسول للنتائج
- تنظيف تلقائي للاستعلام
- دعم كامل للوضع المظلم والفاتح

## 🔤 حقول الرموز والملفات

### 8. AkCodeInput - حقل الرموز والباركود
```dart
AkCodeInput(
  label: 'باركود المنتج',
  controller: barcodeController,
  codeType: AkCodeType.barcode,
  enableScanning: true,
)
```

**المميزات:**
- دعم أنواع مختلفة من الرموز (باركود، QR، مخصص)
- مسح ضوئي للرموز (اختياري)
- تحقق من صحة الرمز
- دعم كامل للوضع المظلم والفاتح

### 9. AkFileInput - حقل رفع الملفات
```dart
AkFileInput(
  label: 'رفع المستندات',
  allowedExtensions: ['pdf', 'doc', 'jpg'],
  maxFileSize: 5 * 1024 * 1024, // 5 MB
  onFileSelected: (file) => handleFile(file),
)
```

**المميزات:**
- تحديد أنواع الملفات المسموحة
- تحديد الحد الأقصى لحجم الملف
- معاينة الملفات المختارة
- دعم كامل للوضع المظلم والفاتح

## 📋 حقول الاختيار المتقدمة

### 10. AkDropdownInput - القائمة المنسدلة
```dart
AkDropdownInput<String>(
  label: 'اختر المدينة',
  items: ['صنعاء', 'عدن', 'تعز', 'الحديدة'],
  enableSearch: true,
  isRequired: true,
)
```

**المميزات:**
- بحث داخل القائمة
- دعم الأنواع المختلفة (Generic)
- تحميل كسول للعناصر
- دعم كامل للوضع المظلم والفاتح

### 11. AkDateInput - حقل التاريخ
```dart
AkDateInput(
  label: 'تاريخ الميلاد',
  controller: birthDateController,
  firstDate: DateTime(1950),
  lastDate: DateTime.now(),
  showHijriDate: true,
)
```

**المميزات:**
- منتقي تاريخ محلي باللغة العربية
- تنسيق التاريخ حسب النمط المحلي
- دعم التواريخ الهجرية والميلادية
- دعم كامل للوضع المظلم والفاتح

## 🎨 الدعم الكامل للوضع المظلم/الفاتح

جميع الحقول تدعم تلقائياً:

### ✅ الألوان التكيفية
- **النص الأساسي**: `isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary`
- **النص الثانوي**: `isDark ? AppColors.darkTextSecondary : AppColors.lightTextSecondary`
- **الخلفية**: `isDark ? AppColors.darkSurfaceVariant : AppColors.lightSurfaceVariant`
- **الحدود**: `isDark ? AppColors.darkBorder : AppColors.lightBorder`

### ✅ التحقق التلقائي من الوضع
```dart
final theme = Theme.of(context);
final isDark = theme.brightness == Brightness.dark;
```

### ✅ عدم استخدام القيم الصريحة
- ❌ `Colors.red` → ✅ `AppColors.error`
- ❌ `EdgeInsets.all(16)` → ✅ `AppDimensions.defaultMargin`
- ❌ `fontSize: 14` → ✅ `AppTypography.fontSizeMedium`
- ❌ `BorderRadius.circular(8)` → ✅ `AppDimensions.mediumRadius`

## 🚀 المميزات الرئيسية

### ✅ التحميل الكسول
- **منسقات الأرقام**: يتم تحميلها عند الحاجة فقط
- **الاقتراحات**: يتم بناؤها عند التفاعل
- **التحقق من الصحة**: يتم تطبيقه حسب الحاجة

### ✅ التكامل مع نظام الثيمات
- **الألوان**: `AppColors.*`
- **الأبعاد**: `AppDimensions.*`
- **الخطوط**: `AppTypography.*`

### ✅ التخصيص اليمني
- **العملة الافتراضية**: ريال يمني (ر.ي)
- **رمز الدولة**: +967 لليمن
- **أرقام الهاتف**: تحقق من الشبكات اليمنية
- **التاريخ**: دعم التقويم الهجري

### ✅ التحقق المتقدم
- **استخدام Validators**: دوال مساعدة موحدة
- **رسائل خطأ واضحة**: باللغة العربية
- **تحقق تفاعلي**: أثناء الكتابة

## 📱 الاستجابة والتكيف

جميع الحقول تتكيف تلقائياً مع:
- **حجم الشاشة**: أحجام متجاوبة
- **الوضع المظلم/الفاتح**: ألوان تكيفية
- **اتجاه النص**: دعم RTL/LTR

## 🎯 الاستخدام الموصى به

1. **استخدم الحقول المتخصصة** للبيانات المحددة
2. **طبق التحميل الكسول** للعناصر الثقيلة
3. **اتبع نظام الثيمات** لضمان التناسق
4. **استخدم التحقق المدمج** لضمان جودة البيانات
5. **اختبر في كلا الوضعين** (مظلم/فاتح)

---

**`akinputs.dart` هو المصدر الوحيد والشامل لجميع حقول الإدخال في التطبيق!** 🚀
