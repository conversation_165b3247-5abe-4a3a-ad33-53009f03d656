import 'package:flutter/foundation.dart';
import '../../../core/services/supplier_service.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/models/supplier.dart';

/// مقدم الموردين
class SupplierPresenter extends ChangeNotifier {
  final SupplierService _supplierService = SupplierService();

  List<Supplier> _suppliers = [];
  bool _isLoading = false;
  String? _errorMessage;

  /// الحصول على قائمة الموردين
  List<Supplier> get suppliers => _suppliers;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// تحميل الموردين
  Future<void> loadSuppliers() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final suppliers = await _supplierService.getSuppliers();

      _suppliers = suppliers;
      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل الموردين: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل الموردين',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
      rethrow;
    }
  }

  /// الحصول على مورد بواسطة المعرف
  Supplier? getSupplierById(String id) {
    try {
      return _suppliers.firstWhere((supplier) => supplier.id == id);
    } catch (e) {
      return null;
    }
  }

  /// إضافة مورد جديد
  Future<bool> addSupplier(Supplier supplier) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final newSupplier = await _supplierService.addSupplier(supplier);

      _suppliers.add(newSupplier);
      _isLoading = false;
      notifyListeners();

      return true;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء إضافة المورد: $e';
      ErrorTracker.captureError(
        'خطأ في إضافة مورد',
        error: e,
        stackTrace: stackTrace,
        context: {
          'supplier_name': supplier.name,
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// تحديث مورد
  Future<bool> updateSupplier(Supplier supplier) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final updatedSupplier = await _supplierService.updateSupplier(supplier);

      final index = _suppliers.indexWhere((s) => s.id == supplier.id);
      if (index >= 0) {
        _suppliers[index] = updatedSupplier;
      }

      _isLoading = false;
      notifyListeners();

      return true;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحديث المورد: $e';
      ErrorTracker.captureError(
        'خطأ في تحديث مورد',
        error: e,
        stackTrace: stackTrace,
        context: {
          'supplier_id': supplier.id,
          'supplier_name': supplier.name,
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// حذف مورد
  Future<bool> deleteSupplier(String id) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _supplierService.deleteSupplier(id);

      if (success) {
        _suppliers.removeWhere((supplier) => supplier.id == id);
      }

      _isLoading = false;
      notifyListeners();

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء حذف المورد: $e';
      ErrorTracker.captureError(
        'خطأ في حذف مورد',
        error: e,
        stackTrace: stackTrace,
        context: {
          'supplier_id': id,
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// تحديث رصيد المورد
  Future<bool> updateSupplierBalance(
      String id, double amount, String reason) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final supplier = getSupplierById(id);
      if (supplier == null) {
        throw Exception('المورد غير موجود');
      }

      final updatedSupplier = supplier.copyWith(
        balance: supplier.balance + amount,
        updatedAt: DateTime.now(),
      );

      final success = await updateSupplier(updatedSupplier);

      _isLoading = false;
      notifyListeners();

      return success;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحديث رصيد المورد: $e';
      ErrorTracker.captureError(
        'خطأ في تحديث رصيد مورد',
        error: e,
        stackTrace: stackTrace,
        context: {
          'supplier_id': id,
          'amount': amount,
          'reason': reason,
        },
      );
      notifyListeners();
      return false;
    }
  }

  /// البحث عن الموردين
  List<Supplier> searchSuppliers(String query) {
    if (query.isEmpty) {
      return _suppliers;
    }

    final lowercaseQuery = query.toLowerCase();

    return _suppliers.where((supplier) {
      final name = supplier.name.toLowerCase();
      final phone = supplier.phone?.toLowerCase() ?? '';
      final email = supplier.email?.toLowerCase() ?? '';
      final address = supplier.address?.toLowerCase() ?? '';

      return name.contains(lowercaseQuery) ||
          phone.contains(lowercaseQuery) ||
          email.contains(lowercaseQuery) ||
          address.contains(lowercaseQuery);
    }).toList();
  }

  /// الحصول على الموردين النشطين
  List<Supplier> getActiveSuppliers() {
    return _suppliers.where((supplier) => supplier.isActive).toList();
  }

  /// الحصول على الموردين ذوي الرصيد المدين
  List<Supplier> getSuppliersWithDebitBalance() {
    return _suppliers.where((supplier) => supplier.balance < 0).toList();
  }

  /// الحصول على الموردين ذوي الرصيد الدائن
  List<Supplier> getSuppliersWithCreditBalance() {
    return _suppliers.where((supplier) => supplier.balance > 0).toList();
  }

  /// الحصول على إجمالي رصيد الموردين
  double getTotalSuppliersBalance() {
    return _suppliers.fold(0, (sum, supplier) => sum + supplier.balance);
  }
}
