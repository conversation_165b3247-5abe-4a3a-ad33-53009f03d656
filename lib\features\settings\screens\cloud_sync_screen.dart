import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:intl/intl.dart' as intl;

import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';
import '../presenters/cloud_sync_presenter.dart';
import '../../../data/services/cloud_sync_service.dart';

/// شاشة المزامنة السحابية
/// تتيح للمستخدم تسجيل الدخول إلى خدمة المزامنة السحابية ومزامنة البيانات
class CloudSyncScreen extends StatefulWidget {
  const CloudSyncScreen({Key? key}) : super(key: key);

  @override
  State<CloudSyncScreen> createState() => _CloudSyncScreenState();
}

class _CloudSyncScreenState extends State<CloudSyncScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  // استخدام التحميل الكسول
  late final CloudSyncPresenter _cloudSyncPresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _cloudSyncPresenter = AppProviders.getLazyPresenter<CloudSyncPresenter>(
        () => CloudSyncPresenter());
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// الحصول على نص حالة المزامنة
  String _getSyncStatusText(SyncStatus status) {
    switch (status) {
      case SyncStatus.synced:
        return 'متزامن';
      case SyncStatus.pending:
        return 'جاري المزامنة...';
      case SyncStatus.failed:
        return 'فشل المزامنة';
      case SyncStatus.offline:
        return 'غير متصل';
    }
  }

  /// الحصول على لون حالة المزامنة
  Color _getSyncStatusColor(SyncStatus status) {
    switch (status) {
      case SyncStatus.synced:
        return AppColors.syncSynced;
      case SyncStatus.pending:
        return AppColors.syncPending;
      case SyncStatus.failed:
        return AppColors.syncFailed;
      case SyncStatus.offline:
        return AppColors.syncOffline;
    }
  }

  /// الحصول على أيقونة حالة المزامنة
  IconData _getSyncStatusIcon(SyncStatus status) {
    switch (status) {
      case SyncStatus.synced:
        return Icons.cloud_done;
      case SyncStatus.pending:
        return Icons.cloud_sync;
      case SyncStatus.failed:
        return Icons.cloud_off;
      case SyncStatus.offline:
        return Icons.cloud_off;
    }
  }

  /// عرض مربع حوار تسجيل الدخول
  Future<void> _showLoginDialog(
      BuildContext context, CloudSyncPresenter presenter) async {
    _emailController.clear();
    _passwordController.clear();

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('تسجيل الدخول للمزامنة السحابية'),
              content: SingleChildScrollView(
                child: ListBody(
                  children: <Widget>[
                    TextField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: 'البريد الإلكتروني',
                        prefixIcon: Icon(Icons.email),
                      ),
                      keyboardType: TextInputType.emailAddress,
                      textDirection: TextDirection.ltr,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _passwordController,
                      obscureText: _obscurePassword,
                      decoration: InputDecoration(
                        labelText: 'كلمة المرور',
                        prefixIcon: const Icon(Icons.lock),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscurePassword
                                ? Icons.visibility
                                : Icons.visibility_off,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                        ),
                      ),
                      textDirection: TextDirection.ltr,
                    ),
                  ],
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: const Text('إلغاء'),
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                ),
                TextButton(
                  child: const Text('تسجيل الدخول'),
                  onPressed: () async {
                    if (_emailController.text.isEmpty ||
                        _passwordController.text.isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                              'الرجاء إدخال البريد الإلكتروني وكلمة المرور'),
                          backgroundColor: AppColors.error,
                        ),
                      );
                      return;
                    }

                    Navigator.of(dialogContext).pop();

                    final success = await presenter.signIn(
                      _emailController.text.trim(),
                      _passwordController.text,
                    );

                    if (!success && mounted) {
                      _showSnackBar(
                        presenter.error ?? 'فشل تسجيل الدخول',
                        isError: true,
                      );
                    }
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// عرض رسالة في شريط Snackbar
  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AkAppBar(
        title: 'المزامنة السحابية',
      ),
      body: ListenableBuilder(
        listenable: _cloudSyncPresenter,
        builder: (context, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Status card
                Card(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              _getSyncStatusIcon(
                                  _cloudSyncPresenter.syncStatus),
                              color: _getSyncStatusColor(
                                  _cloudSyncPresenter.syncStatus),
                              size: 32,
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'حالة المزامنة',
                                  style: AppTypography(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  _getSyncStatusText(
                                      _cloudSyncPresenter.syncStatus),
                                  style: AppTypography(
                                    color: _getSyncStatusColor(
                                        _cloudSyncPresenter.syncStatus),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const Divider(height: 24),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('آخر مزامنة:'),
                            Text(
                              _cloudSyncPresenter.lastSyncTime != null
                                  ? intl.DateFormat('yyyy-MM-dd HH:mm')
                                      .format(_cloudSyncPresenter.lastSyncTime!)
                                  : 'لم تتم المزامنة بعد',
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('الحالة:'),
                            Text(
                              _cloudSyncPresenter.isOnline
                                  ? 'متصل'
                                  : 'غير متصل',
                              style: AppTypography(
                                color: _cloudSyncPresenter.isOnline
                                    ? AppColors.success
                                    : AppColors.lightTextSecondary,
                              ),
                            ),
                          ],
                        ),
                        if (_cloudSyncPresenter.isLoggedIn) ...[
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text('البريد الإلكتروني:'),
                              Text(_cloudSyncPresenter.userEmail ?? ''),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Action buttons
                if (_cloudSyncPresenter.isLoggedIn) ...[
                  ElevatedButton.icon(
                    icon: const Icon(Icons.sync),
                    label: const Text('مزامنة الآن'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: _cloudSyncPresenter.isSyncing
                        ? null
                        : () async {
                            final success =
                                await _cloudSyncPresenter.syncData();
                            if (!success && mounted) {
                              _showSnackBar(
                                _cloudSyncPresenter.error ?? 'فشل المزامنة',
                                isError: true,
                              );
                            }
                          },
                  ),
                  const SizedBox(height: 12),
                  OutlinedButton.icon(
                    icon: const Icon(Icons.logout),
                    label: const Text('تسجيل الخروج'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: () async {
                      await _cloudSyncPresenter.signOut();
                      if (mounted) {
                        _showSnackBar(
                          'تم تسجيل الخروج بنجاح',
                          isError: false,
                        );
                      }
                    },
                  ),
                ] else ...[
                  ElevatedButton.icon(
                    icon: const Icon(Icons.login),
                    label: const Text('تسجيل الدخول'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: () =>
                        _showLoginDialog(context, _cloudSyncPresenter),
                  ),
                ],
                const SizedBox(height: 24),

                // Info section
                const Card(
                  elevation: 4,
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'معلومات المزامنة السحابية',
                          style: AppTypography(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'تتيح المزامنة السحابية حفظ بياناتك على السحابة ومزامنتها بين أجهزتك المختلفة. '
                          'يمكنك العمل بدون اتصال بالإنترنت وستتم المزامنة تلقائياً عند توفر الاتصال.',
                        ),
                        SizedBox(height: 8),
                        Text(
                          'ملاحظة: يجب أن يكون لديك حساب على خدمة المزامنة السحابية للاستفادة من هذه الميزة.',
                          style: AppTypography(fontStyle: FontStyle.italic),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
