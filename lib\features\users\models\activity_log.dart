/// أنواع الإجراءات
enum ActionType {
  create,
  update,
  delete,
  read,
  login,
  logout,
  error,
  sync,
  export,
  import,
  print,
  approve,
  reject,
  cancel,
  restore,
  archive,
  unarchive,
  other,
}

/// أنواع الكيانات
enum EntityType {
  user,
  product,
  category,
  customer,
  supplier,
  invoice,
  order,
  payment,
  receipt,
  expense,
  report,
  setting,
  branch,
  warehouse,
  inventory,
  journalEntry,
  account,
  transaction,
  other,
}

/// نموذج سجل النشاطات
class ActivityLog {
  final String id;
  final String userId;
  final String userName;
  final String action;
  final String module;
  final String details;
  final String ipAddress;
  final DateTime timestamp;

  ActivityLog({
    required this.id,
    required this.userId,
    required this.userName,
    required this.action,
    required this.module,
    required this.details,
    required this.ipAddress,
    required this.timestamp,
  });

  /// إنشاء من Map
  factory ActivityLog.fromMap(Map<String, dynamic> map) {
    return ActivityLog(
      id: map['id'],
      userId: map['user_id'],
      userName: map['user_name'],
      action: map['action'],
      module: map['module'],
      details: map['details'],
      ipAddress: map['ip_address'],
      // استخدام timestamp أولاً، وإذا لم يكن موجود استخدم created_at
      timestamp: map['timestamp'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['timestamp'])
          : DateTime.parse(map['created_at']),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'user_name': userName,
      'action': action,
      'module': module,
      'details': details,
      'ip_address': ipAddress,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'created_at': timestamp.toIso8601String(), // إضافة created_at
    };
  }

  /// نسخة مع تعديلات
  ActivityLog copyWith({
    String? id,
    String? userId,
    String? userName,
    String? action,
    String? module,
    String? details,
    String? ipAddress,
    DateTime? timestamp,
  }) {
    return ActivityLog(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      action: action ?? this.action,
      module: module ?? this.module,
      details: details ?? this.details,
      ipAddress: ipAddress ?? this.ipAddress,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  String toString() {
    return 'ActivityLog(id: $id, userId: $userId, userName: $userName, action: $action, module: $module, details: $details, ipAddress: $ipAddress, timestamp: $timestamp)';
  }
}
