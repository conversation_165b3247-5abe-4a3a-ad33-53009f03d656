import 'package:flutter/material.dart';
import 'simplified_access_management_screen.dart';

import '../../../core/theme/index.dart';

/// شاشة نموذج مستوى الوصول
/// تستخدم لإنشاء أو تعديل مستوى وصول
class AccessLevelFormScreen extends StatefulWidget {
  final AccessLevel? accessLevel;

  const AccessLevelFormScreen({
    Key? key,
    this.accessLevel,
  }) : super(key: key);

  @override
  State<AccessLevelFormScreen> createState() => _AccessLevelFormScreenState();
}

class _AccessLevelFormScreenState extends State<AccessLevelFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();

  bool _isLoading = false;
  String? _error;

  // الوظائف المتاحة في النظام
  final List<JobFunction> _jobFunctions = [
    JobFunction(
      id: 'sales',
      name: 'المبيعات',
      icon: Icons.point_of_sale,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المبيعات والفواتير والعملاء',
    ),
    JobFunction(
      id: 'inventory',
      name: 'المخزون',
      icon: Icons.inventory_2,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المنتجات والمخزون والمستودعات',
    ),
    JobFunction(
      id: 'purchases',
      name: 'المشتريات',
      icon: Icons.shopping_cart,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المشتريات والموردين',
    ),
    JobFunction(
      id: 'finance',
      name: 'المالية',
      icon: Icons.account_balance,
      color: AppColors.lightTextSecondary,
      description: 'إدارة الحسابات والمصروفات والإيرادات',
    ),
    JobFunction(
      id: 'reports',
      name: 'التقارير',
      icon: Icons.bar_chart,
      color: AppColors.lightTextSecondary,
      description: 'عرض وطباعة التقارير المختلفة',
    ),
    JobFunction(
      id: 'settings',
      name: 'الإعدادات',
      icon: Icons.settings,
      color: AppColors.lightTextSecondary,
      description: 'إدارة إعدادات النظام والمستخدمين',
    ),
  ];

  // مستويات الوصول لكل وظيفة
  final Map<String, AccessLevelType> _functionLevels = {};

  @override
  void initState() {
    super.initState();

    // تهيئة البيانات إذا كان هناك مستوى وصول للتعديل
    if (widget.accessLevel != null) {
      _nameController.text = widget.accessLevel!.name;
      _functionLevels.addAll(widget.accessLevel!.functionLevels);
    } else {
      // تهيئة مستويات الوصول الافتراضية
      for (final jobFunction in _jobFunctions) {
        _functionLevels[jobFunction.id] = AccessLevelType.none;
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.accessLevel == null
            ? 'إضافة مستوى وصول جديد'
            : 'تعديل مستوى الوصول'),
        actions: [
          TextButton.icon(
            onPressed: _saveAccessLevel,
            icon: const Icon(Icons.save),
            label: const Text('حفظ'),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.onPrimary,
            ),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // اسم مستوى الوصول
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'اسم مستوى الوصول',
              hintText: 'مثال: مدير مبيعات، محاسب، موظف استقبال',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.badge),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال اسم مستوى الوصول';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),

          // عنوان مستويات الوصول
          const Text(
            'تحديد مستويات الوصول',
            style: AppTypography(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'حدد مستوى الوصول المناسب لكل وظيفة:',
            style: AppTypography(
              color: AppColors.lightTextSecondary,
            ),
          ),
          const SizedBox(height: 16),

          // قائمة الوظائف ومستويات الوصول
          ...List.generate(_jobFunctions.length, (index) {
            final jobFunction = _jobFunctions[index];
            final accessLevelType =
                _functionLevels[jobFunction.id] ?? AccessLevelType.none;

            return _buildFunctionAccessSelector(jobFunction, accessLevelType);
          }),

          // رسالة الخطأ
          if (_error != null)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.12),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: AppColors.error,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _error!,
                      style: const AppTypography(
                        color: AppColors.lightTextSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // زر الحفظ
          Container(
            margin: const EdgeInsets.only(top: 24),
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _saveAccessLevel,
              icon: const Icon(Icons.save),
              label: const Text('حفظ مستوى الوصول'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محدد مستوى الوصول للوظيفة
  Widget _buildFunctionAccessSelector(
      JobFunction jobFunction, AccessLevelType accessLevelType) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الوظيفة
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: jobFunction.color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    jobFunction.icon,
                    color: jobFunction.color,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        jobFunction.name,
                        style: const AppTypography(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        jobFunction.description,
                        style: const AppTypography(
                          fontSize: 12,
                          color: AppColors.lightTextSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // محدد مستوى الوصول
            Row(
              children: [
                _buildAccessLevelOption(
                  jobFunction.id,
                  AccessLevelType.none,
                  'لا وصول',
                  AppColors.lightTextSecondary,
                  accessLevelType == AccessLevelType.none,
                ),
                _buildAccessLevelOption(
                  jobFunction.id,
                  AccessLevelType.view,
                  'عرض فقط',
                  AppColors.info,
                  accessLevelType == AccessLevelType.view,
                ),
                _buildAccessLevelOption(
                  jobFunction.id,
                  AccessLevelType.edit,
                  'تعديل',
                  AppColors.warning,
                  accessLevelType == AccessLevelType.edit,
                ),
                _buildAccessLevelOption(
                  jobFunction.id,
                  AccessLevelType.full,
                  'وصول كامل',
                  AppColors.success,
                  accessLevelType == AccessLevelType.full,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء خيار مستوى الوصول
  Widget _buildAccessLevelOption(
    String functionId,
    AccessLevelType type,
    String label,
    Color color,
    bool isSelected,
  ) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _functionLevels[functionId] = type;
          });
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? color.withValues(alpha: 0.2)
                : AppColors.lightTextSecondary.withValues(alpha: 0.08),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? color : Colors.transparent,
              width: 2,
            ),
          ),
          child: Column(
            children: [
              Icon(
                _getAccessLevelIcon(type),
                color: isSelected ? color : AppColors.lightTextSecondary,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: AppTypography(
                  fontSize: 12,
                  color: isSelected ? color : AppColors.lightTextSecondary,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على أيقونة مستوى الوصول
  IconData _getAccessLevelIcon(AccessLevelType type) {
    switch (type) {
      case AccessLevelType.none:
        return Icons.block;
      case AccessLevelType.view:
        return Icons.visibility;
      case AccessLevelType.edit:
        return Icons.edit;
      case AccessLevelType.full:
        return Icons.admin_panel_settings;
    }
  }

  /// حفظ مستوى الوصول
  void _saveAccessLevel() {
    // التحقق من صحة النموذج
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    // سيتم تنفيذها لاحقاً
    // هنا يتم تحويل مستويات الوصول إلى صلاحيات وحفظها

    // محاكاة عملية الحفظ
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _isLoading = false;
      });

      // العودة إلى الشاشة السابقة
      if (mounted) {
        Navigator.of(context).pop();
      }
    });
  }
}
