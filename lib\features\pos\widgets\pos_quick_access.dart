import 'package:flutter/material.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/theme/index.dart';

/// ويدجت للوصول السريع إلى نظام نقاط البيع
/// يمكن استخدامه في الشاشة الرئيسية أو لوحة التحكم
class POSQuickAccess extends StatelessWidget {
  const POSQuickAccess({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => AppRoutes.navigateTo(context, AppRoutes.pos),
        borderRadius: BorderRadius.circular(12),
        child: const Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.point_of_sale,
                size: 48,
                color: AppColors.lightTextSecondary,
              ),
              SizedBox(height: 12),
              Text(
                'نقاط البيع',
                style: AppTypography(fontWeight: FontWeight.bold, fontSize: 16),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                'إدارة المبيعات وإصدار الفواتير',
                style: AppTypography(
                    fontSize: 12, color: AppColors.lightTextSecondary),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
