import 'dart:convert';
import 'package:uuid/uuid.dart';

/// نموذج السند المالي
class Voucher {
  final String? id;
  final String voucherNumber;
  final String voucherType;
  final DateTime voucherDate;
  final String? referenceNumber;
  final String? description;
  final double amount;
  final String? accountId;
  final String? accountName;
  final String? contactId;
  final String? contactName;
  final String? contactType;
  final String? paymentMethod;
  final String status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? createdBy;
  final String? updatedBy;
  final bool isDeleted;
  final String? handler;
  final String? notes;
  final String? currency;
  final double exchangeRate;

  /// المبلغ بالعملة المحلية (المبلغ × سعر الصرف)
  double get localAmount => amount * exchangeRate;

  /// إنشاء سند مالي جديد
  Voucher({
    this.id,
    required this.voucherNumber,
    required this.voucherType,
    required this.voucherDate,
    this.referenceNumber,
    this.description,
    required this.amount,
    this.accountId,
    this.accountName,
    this.contactId,
    this.contactName,
    this.contactType,
    this.paymentMethod,
    this.status = 'draft',
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.isDeleted = false,
    this.handler,
    this.notes,
    this.currency,
    this.exchangeRate = 1.0,
  });

  /// إنشاء نسخة من هذا السند مع استبدال الحقول المحددة بقيم جديدة
  Voucher copyWith({
    String? id,
    String? voucherNumber,
    String? voucherType,
    DateTime? voucherDate,
    String? referenceNumber,
    String? description,
    double? amount,
    String? accountId,
    String? accountName,
    String? contactId,
    String? contactName,
    String? contactType,
    String? paymentMethod,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    bool? isDeleted,
    String? handler,
    String? notes,
    String? currency,
    double? exchangeRate,
  }) {
    return Voucher(
      id: id ?? this.id,
      voucherNumber: voucherNumber ?? this.voucherNumber,
      voucherType: voucherType ?? this.voucherType,
      voucherDate: voucherDate ?? this.voucherDate,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      accountId: accountId ?? this.accountId,
      accountName: accountName ?? this.accountName,
      contactId: contactId ?? this.contactId,
      contactName: contactName ?? this.contactName,
      contactType: contactType ?? this.contactType,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
      handler: handler ?? this.handler,
      notes: notes ?? this.notes,
      currency: currency ?? this.currency,
      exchangeRate: exchangeRate ?? this.exchangeRate,
    );
  }

  /// إنشاء سند مالي من خريطة
  factory Voucher.fromMap(Map<String, dynamic> map) {
    return Voucher(
      id: map['id'],
      voucherNumber: map['voucher_number'] ?? '',
      voucherType: map['voucher_type'] ?? 'receipt',
      voucherDate: map['voucher_date'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['voucher_date'])
          : DateTime.now(),
      referenceNumber: map['reference_number'],
      description: map['description'],
      amount: map['amount']?.toDouble() ?? 0.0,
      accountId: map['account_id'],
      accountName: map['account_name'],
      contactId: map['contact_id'],
      contactName: map['contact_name'],
      contactType: map['contact_type'],
      paymentMethod: map['payment_method'],
      status: map['status'] ?? 'draft',
      createdAt: map['created_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'])
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updated_at'])
          : null,
      createdBy: map['created_by'],
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
      handler: map['handler'],
      notes: map['notes'],
      currency: map['currency'],
      exchangeRate: map['exchange_rate']?.toDouble() ?? 1.0,
    );
  }

  /// تحويل السند المالي إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id ?? const Uuid().v4(),
      'voucher_number': voucherNumber,
      'voucher_type': voucherType,
      'voucher_date': voucherDate.millisecondsSinceEpoch,
      'reference_number': referenceNumber,
      'description': description,
      'amount': amount,
      'account_id': accountId,
      'account_name': accountName,
      'contact_id': contactId,
      'contact_name': contactName,
      'contact_type': contactType,
      'payment_method': paymentMethod,
      'status': status,
      'created_at': createdAt?.millisecondsSinceEpoch ??
          DateTime.now().millisecondsSinceEpoch,
      'updated_at': updatedAt?.millisecondsSinceEpoch,
      'created_by': createdBy,
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
      'handler': handler,
      'notes': notes,
      'currency': currency,
      'exchange_rate': exchangeRate,
    };
  }

  /// تحويل السند المالي إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء سند مالي من JSON
  factory Voucher.fromJson(String source) =>
      Voucher.fromMap(json.decode(source));

  /// إنشاء سند مالي فارغ
  factory Voucher.empty() {
    return Voucher(
      voucherNumber: '',
      voucherType: 'receipt',
      voucherDate: DateTime.now(),
      amount: 0.0,
      exchangeRate: 1.0,
    );
  }

  @override
  String toString() {
    return 'Voucher(id: $id, voucherNumber: $voucherNumber, voucherType: $voucherType, voucherDate: $voucherDate, referenceNumber: $referenceNumber, description: $description, amount: $amount, accountId: $accountId, accountName: $accountName, contactId: $contactId, contactName: $contactName, contactType: $contactType, paymentMethod: $paymentMethod, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, updatedBy: $updatedBy, isDeleted: $isDeleted, handler: $handler, notes: $notes, currency: $currency, exchangeRate: $exchangeRate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Voucher &&
        other.id == id &&
        other.voucherNumber == voucherNumber &&
        other.voucherType == voucherType &&
        other.voucherDate == voucherDate &&
        other.referenceNumber == referenceNumber &&
        other.description == description &&
        other.amount == amount &&
        other.accountId == accountId &&
        other.accountName == accountName &&
        other.contactId == contactId &&
        other.contactName == contactName &&
        other.contactType == contactType &&
        other.paymentMethod == paymentMethod &&
        other.status == status &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.createdBy == createdBy &&
        other.updatedBy == updatedBy &&
        other.isDeleted == isDeleted &&
        other.handler == handler &&
        other.notes == notes &&
        other.currency == currency &&
        other.exchangeRate == exchangeRate;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        voucherNumber.hashCode ^
        voucherType.hashCode ^
        voucherDate.hashCode ^
        referenceNumber.hashCode ^
        description.hashCode ^
        amount.hashCode ^
        accountId.hashCode ^
        accountName.hashCode ^
        contactId.hashCode ^
        contactName.hashCode ^
        contactType.hashCode ^
        paymentMethod.hashCode ^
        status.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        createdBy.hashCode ^
        updatedBy.hashCode ^
        isDeleted.hashCode ^
        handler.hashCode ^
        notes.hashCode ^
        currency.hashCode ^
        exchangeRate.hashCode;
  }
}
