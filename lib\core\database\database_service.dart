import 'package:sqflite/sqflite.dart';
import 'package:sqflite/sqlite_api.dart'; // Importar para ConflictAlgorithm
import '../utils/error_tracker.dart';
import 'database_helper.dart';

/// Service class for database operations
/// This is a singleton that provides a consistent interface
/// for database operations across the application.
class DatabaseService {
  // Singleton pattern
  static final DatabaseService _instance = DatabaseService._internal();
  static DatabaseService get instance => _instance;
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Table names constants
  static const String tableUsers = 'users';
  static const String tableRoles = 'roles';
  static const String tablePermissions = 'permissions';
  static const String tableRolePermissions = 'role_permissions';
  static const String tableCustomers = 'customers';
  static const String tableSuppliers = 'suppliers';
  static const String tableProducts = 'products';
  static const String tableCategories = 'categories'; // جدول الفئات الموحد
  static const String tableUnits = 'units';
  static const String tableInventory = 'inventory';
  static const String tableWarehouses = 'warehouses';
  static const String tableInventoryTransactions = 'inventory_transactions';
  static const String tableInvoices = 'invoices';
  static const String tableInvoiceItems = 'invoice_items';
  static const String tablePayments = 'payments';
  static const String tablePaymentMethods = 'payment_methods';
  static const String tableAccounts = 'accounts';
  static const String tableJournalEntries = 'journal_entries';
  static const String tableJournalEntryDetails = 'journal_entry_details';
  static const String tableFiscalPeriods = 'fiscal_periods';
  static const String tableAuditLogs = 'audit_logs';
  static const String tableInventoryAdjustments = 'inventory_adjustments';
  static const String tableInventoryAdjustmentItems =
      'inventory_adjustment_items';
  static const String tablePurchases = 'purchases';
  static const String tablePurchaseItems = 'purchase_items';
  static const String tableInventoryTransfers = 'inventory_transfers';
  static const String tableInventoryTransferItems = 'inventory_transfer_items';
  static const String tableStockTransfers = 'stock_transfers';
  static const String tableStockTransferItems = 'stock_transfer_items';
  static const String tablePurchaseInvoices = 'purchase_invoices';
  static const String tablePurchaseInvoiceItems = 'purchase_invoice_items';
  static const String tableSales = 'sales';
  static const String tableSaleItems = 'sale_items';
  static const String tableSalesInvoices = 'sales_invoices';
  static const String tableSalesInvoiceItems = 'sales_invoice_items';

  /// Get the database instance
  Future<Database> get database async => await _databaseHelper.database;

  /// Insert a record into the database
  Future<int> insert(String table, Map<String, dynamic> data) async {
    try {
      final db = await database;
      final id = await db.insert(
        table,
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return id;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to insert data into $table',
        error: e,
        stackTrace: stackTrace,
        context: {'table': table, 'data': data},
      );
      rethrow;
    }
  }

  /// Update records in the database
  Future<int> update(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    try {
      final db = await database;
      final rowsAffected = await db.update(
        table,
        data,
        where: where,
        whereArgs: whereArgs,
      );
      return rowsAffected;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to update data in $table',
        error: e,
        stackTrace: stackTrace,
        context: {
          'table': table,
          'data': data,
          'where': where,
          'whereArgs': whereArgs
        },
      );
      rethrow;
    }
  }

  /// Delete records from the database
  Future<int> delete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    try {
      final db = await database;
      final rowsAffected = await db.delete(
        table,
        where: where,
        whereArgs: whereArgs,
      );
      return rowsAffected;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to delete data from $table',
        error: e,
        stackTrace: stackTrace,
        context: {'table': table, 'where': where, 'whereArgs': whereArgs},
      );
      rethrow;
    }
  }

  /// Query the database
  Future<List<Map<String, dynamic>>> query(
    String table, {
    bool distinct = false,
    List<String>? columns,
    String? where,
    List<dynamic>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await database;
      final result = await db.query(
        table,
        distinct: distinct,
        columns: columns,
        where: where,
        whereArgs: whereArgs,
        groupBy: groupBy,
        having: having,
        orderBy: orderBy,
        limit: limit,
        offset: offset,
      );
      return result;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to query data from $table',
        error: e,
        stackTrace: stackTrace,
        context: {
          'table': table,
          'where': where,
          'whereArgs': whereArgs,
        },
      );
      return [];
    }
  }

  /// Execute a raw SQL query
  Future<List<Map<String, dynamic>>> rawQuery(
    String sql, [
    List<dynamic>? arguments,
  ]) async {
    try {
      final db = await database;
      final result = await db.rawQuery(sql, arguments);
      return result;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to execute raw query',
        error: e,
        stackTrace: stackTrace,
        context: {'sql': sql, 'arguments': arguments},
      );
      return [];
    }
  }

  /// Execute a batch of operations
  Future<List<dynamic>> batch(
    Future<void> Function(Batch batch) operations,
  ) async {
    try {
      final db = await database;
      final batch = db.batch();

      await operations(batch);

      return await batch.commit();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to execute batch operations',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Execute a transaction
  Future<T> transaction<T>(
    Future<T> Function(Transaction txn) action,
  ) async {
    try {
      final db = await database;
      return await db.transaction(action);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to execute transaction',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get a record by ID
  Future<Map<String, dynamic>?> getById(String table, dynamic id) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        table,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return maps.first;
      }
      return null;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to get record by ID from $table',
        error: e,
        stackTrace: stackTrace,
        context: {'table': table, 'id': id},
      );
      return null;
    }
  }
}
