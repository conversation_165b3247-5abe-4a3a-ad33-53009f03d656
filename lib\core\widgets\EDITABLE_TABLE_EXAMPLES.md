# 📝 أمثلة الجدول القابل للتحرير (Editable UnifiedDataTable)

## 🎯 نظرة عامة

الجدول الموحد `UnifiedDataTable` يدعم الآن **التحرير المباشر للخلايا** مع أنواع مختلفة من حقول الإدخال:

- ✅ **نص عادي** - تحرير النصوص
- ✅ **أرقام** - مع التحقق من النطاق
- ✅ **مبالغ مالية** - مع التنسيق التلقائي
- ✅ **نسب مئوية** - مع التحقق من 0-100%
- ✅ **قوائم منسدلة** - للخيارات المحددة
- ✅ **تواريخ** - مع منتقي التاريخ
- ✅ **تاريخ ووقت** - مع منتقي التاريخ والوقت
- ✅ **أرقام هاتف** - مع التنسيق
- ✅ **بريد إلكتروني** - مع التحقق
- ✅ **نص متعدد الأسطر** - للملاحظات

## 🔧 مثال شامل - جدول منتجات قابل للتحرير:

```dart
import '../core/widgets/index.dart';

class EditableProductsTable extends StatefulWidget {
  const EditableProductsTable({super.key});

  @override
  State<EditableProductsTable> createState() => _EditableProductsTableState();
}

class _EditableProductsTableState extends State<EditableProductsTable> {
  List<Product> products = [
    Product(
      id: 1,
      name: 'لابتوب ديل',
      category: 'إلكترونيات',
      price: 2500.00,
      stock: 15,
      discount: 10.0,
      supplier: 'مورد أ',
      expiryDate: DateTime(2025, 12, 31),
      description: 'لابتوب عالي الأداء',
    ),
    // المزيد من المنتجات...
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedAppBar(
        title: 'جدول المنتجات القابل للتحرير',
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveChanges,
            tooltip: 'حفظ التغييرات',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: UnifiedDataTable<Product>(
          data: products,
          showSearchBar: true,
          showRowNumbers: true,
          style: TableStyle.modern,
          columns: [
            // عمود اسم المنتج - قابل للتحرير
            UnifiedTableColumn<Product>(
              title: 'اسم المنتج',
              getValue: (product) => product.name,
              editable: true,
              editType: EditableFieldType.text,
              onValueChanged: (product, newValue) {
                setState(() {
                  product.name = newValue.toString();
                });
              },
              validator: (value) {
                if (value == null || value.toString().trim().isEmpty) {
                  return 'اسم المنتج مطلوب';
                }
                return null;
              },
            ),

            // عمود الفئة - قائمة منسدلة
            UnifiedTableColumn<Product>(
              title: 'الفئة',
              getValue: (product) => product.category,
              editable: true,
              editType: EditableFieldType.dropdown,
              dropdownOptions: const [
                DropdownOption(label: 'إلكترونيات', value: 'إلكترونيات'),
                DropdownOption(label: 'ملابس', value: 'ملابس'),
                DropdownOption(label: 'طعام', value: 'طعام'),
                DropdownOption(label: 'كتب', value: 'كتب'),
                DropdownOption(label: 'أدوات منزلية', value: 'أدوات منزلية'),
              ],
              onValueChanged: (product, newValue) {
                setState(() {
                  product.category = newValue.toString();
                });
              },
            ),

            // عمود السعر - مبلغ مالي
            UnifiedTableColumn<Product>(
              title: 'السعر',
              getValue: (product) => product.price,
              numeric: true,
              editable: true,
              editType: EditableFieldType.currency,
              showTotal: true,
              formatTotal: (total) => '${NumberFormat('#,##0.00').format(total)} ر.ي',
              onValueChanged: (product, newValue) {
                setState(() {
                  product.price = double.tryParse(newValue.toString()) ?? 0.0;
                });
              },
              minValue: 0.01,
              maxValue: 100000.0,
            ),

            // عمود المخزون - رقم
            UnifiedTableColumn<Product>(
              title: 'المخزون',
              getValue: (product) => product.stock,
              numeric: true,
              editable: true,
              editType: EditableFieldType.number,
              onValueChanged: (product, newValue) {
                setState(() {
                  product.stock = int.tryParse(newValue.toString()) ?? 0;
                });
              },
              minValue: 0,
              maxValue: 10000,
              buildCell: (product, value) => DataCell(
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStockColor(value as int),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    value.toString(),
                    style: const AppTypography(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),

            // عمود الخصم - نسبة مئوية
            UnifiedTableColumn<Product>(
              title: 'الخصم',
              getValue: (product) => product.discount,
              numeric: true,
              editable: true,
              editType: EditableFieldType.percentage,
              onValueChanged: (product, newValue) {
                setState(() {
                  product.discount = double.tryParse(newValue.toString()) ?? 0.0;
                });
              },
            ),

            // عمود المورد - نص
            UnifiedTableColumn<Product>(
              title: 'المورد',
              getValue: (product) => product.supplier,
              editable: true,
              editType: EditableFieldType.text,
              onValueChanged: (product, newValue) {
                setState(() {
                  product.supplier = newValue.toString();
                });
              },
            ),

            // عمود تاريخ الانتهاء - تاريخ
            UnifiedTableColumn<Product>(
              title: 'تاريخ الانتهاء',
              getValue: (product) => product.expiryDate,
              editable: true,
              editType: EditableFieldType.date,
              onValueChanged: (product, newValue) {
                setState(() {
                  product.expiryDate = newValue as DateTime;
                });
              },
              buildCell: (product, value) => DataCell(
                Text(
                  value != null ? DateFormat('yyyy/MM/dd').format(value) : '',
                  style: AppTypography(
                    color: _isExpiringSoon(value) ? AppColors.error : null,
                    fontWeight: _isExpiringSoon(value) ? FontWeight.bold : null,
                  ),
                ),
              ),
            ),

            // عمود الوصف - نص متعدد الأسطر
            UnifiedTableColumn<Product>(
              title: 'الوصف',
              getValue: (product) => product.description,
              editable: true,
              editType: EditableFieldType.multiline,
              onValueChanged: (product, newValue) {
                setState(() {
                  product.description = newValue.toString();
                });
              },
            ),

            // عمود الإجراءات - غير قابل للتحرير
            UnifiedTableColumn<Product>(
              title: 'الإجراءات',
              sortable: false,
              buildCell: (product, value) => DataCell(
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.visibility, size: 18),
                      onPressed: () => _viewProduct(product),
                      tooltip: 'عرض',
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, size: 18, color: AppColors.error),
                      onPressed: () => _deleteProduct(product),
                      tooltip: 'حذف',
                    ),
                  ],
                ),
              ),
            ),
          ],
          onRowTap: (product, index) {
            _showProductDetails(product);
          },
          customFilter: (product, query) {
            return product.name.toLowerCase().contains(query.toLowerCase()) ||
                   product.category.toLowerCase().contains(query.toLowerCase()) ||
                   product.supplier.toLowerCase().contains(query.toLowerCase());
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewProduct,
        child: const Icon(Icons.add),
        tooltip: 'إضافة منتج جديد',
      ),
    );
  }

  Color _getStockColor(int stock) {
    if (stock > 50) return AppColors.success;
    if (stock > 10) return AppColors.warning;
    return AppColors.error;
  }

  bool _isExpiringSoon(DateTime? date) {
    if (date == null) return false;
    final now = DateTime.now();
    final difference = date.difference(now).inDays;
    return difference <= 30; // ينتهي خلال 30 يوم
  }

  void _saveChanges() {
    // حفظ التغييرات في قاعدة البيانات
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حفظ التغييرات بنجاح')),
    );
  }

  void _viewProduct(Product product) {
    // عرض تفاصيل المنتج
  }

  void _deleteProduct(Product product) {
    // حذف المنتج
    setState(() {
      products.remove(product);
    });
  }

  void _addNewProduct() {
    // إضافة منتج جديد
  }

  void _showProductDetails(Product product) {
    // عرض تفاصيل المنتج في حوار
  }
}

// نموذج المنتج
class Product {
  int id;
  String name;
  String category;
  double price;
  int stock;
  double discount;
  String supplier;
  DateTime? expiryDate;
  String description;

  Product({
    required this.id,
    required this.name,
    required this.category,
    required this.price,
    required this.stock,
    required this.discount,
    required this.supplier,
    this.expiryDate,
    required this.description,
  });
}
```

## 💰 مثال جدول فواتير قابل للتحرير:

```dart
class EditableInvoiceTable extends StatefulWidget {
  const EditableInvoiceTable({super.key});

  @override
  State<EditableInvoiceTable> createState() => _EditableInvoiceTableState();
}

class _EditableInvoiceTableState extends State<EditableInvoiceTable> {
  List<InvoiceItem> items = [];

  @override
  Widget build(BuildContext context) {
    return UnifiedDataTable<InvoiceItem>(
      data: items,
      showTotals: true,
      style: TableStyle.modern,
      columns: [
        UnifiedTableColumn<InvoiceItem>(
          title: 'الصنف',
          getValue: (item) => item.productName,
          editable: true,
          editType: EditableFieldType.text,
          onValueChanged: (item, newValue) {
            setState(() {
              item.productName = newValue.toString();
              _calculateTotal(item);
            });
          },
        ),
        UnifiedTableColumn<InvoiceItem>(
          title: 'الكمية',
          getValue: (item) => item.quantity,
          numeric: true,
          editable: true,
          editType: EditableFieldType.number,
          onValueChanged: (item, newValue) {
            setState(() {
              item.quantity = double.tryParse(newValue.toString()) ?? 0;
              _calculateTotal(item);
            });
          },
          minValue: 0.01,
        ),
        UnifiedTableColumn<InvoiceItem>(
          title: 'سعر الوحدة',
          getValue: (item) => item.unitPrice,
          numeric: true,
          editable: true,
          editType: EditableFieldType.currency,
          onValueChanged: (item, newValue) {
            setState(() {
              item.unitPrice = double.tryParse(newValue.toString()) ?? 0;
              _calculateTotal(item);
            });
          },
        ),
        UnifiedTableColumn<InvoiceItem>(
          title: 'الخصم %',
          getValue: (item) => item.discountPercent,
          numeric: true,
          editable: true,
          editType: EditableFieldType.percentage,
          onValueChanged: (item, newValue) {
            setState(() {
              item.discountPercent = double.tryParse(newValue.toString()) ?? 0;
              _calculateTotal(item);
            });
          },
        ),
        UnifiedTableColumn<InvoiceItem>(
          title: 'الإجمالي',
          getValue: (item) => item.total,
          numeric: true,
          showTotal: true,
          formatTotal: (total) => '${NumberFormat('#,##0.00').format(total)} ر.ي',
          buildCell: (item, value) => DataCell(
            Text(
              '${NumberFormat('#,##0.00').format(value)} ر.ي',
              style: const AppTypography(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _calculateTotal(InvoiceItem item) {
    final subtotal = item.quantity * item.unitPrice;
    final discount = subtotal * (item.discountPercent / 100);
    item.total = subtotal - discount;
  }
}
```

## 🎨 المزايا الجديدة:

### ✅ **تحرير مباشر:**
- تحرير الخلايا مباشرة دون نوافذ منبثقة
- حفظ تلقائي عند تغيير القيمة
- تحقق فوري من صحة البيانات

### ✅ **أنواع حقول متنوعة:**
- حقول مخصصة لكل نوع بيانات
- تنسيق تلقائي للمبالغ والتواريخ
- قوائم منسدلة للخيارات المحددة

### ✅ **تحقق متقدم:**
- تحقق من النطاق للأرقام
- تحقق من صحة البريد الإلكتروني
- رسائل خطأ واضحة بالعربية

### ✅ **تجربة مستخدم محسنة:**
- تنسيق بصري جذاب
- ألوان تفاعلية للحالات
- أيقونات واضحة للإجراءات

---

**الجدول القابل للتحرير يوفر تجربة تحرير سلسة ومتقدمة مع الحفاظ على التصميم الموحد والأداء العالي.**
