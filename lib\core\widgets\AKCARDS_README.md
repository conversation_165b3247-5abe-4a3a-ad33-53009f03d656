# 🎯 نظام البطاقات الموحد والشامل (AK Cards System)

## 🎯 نظرة عامة

نظام بطاقات موحد وشامل يوفر جميع أنواع البطاقات المطلوبة في التطبيق مع تصميم متناسق ودعم كامل للوضع المظلم/الفاتح.

## 🔘 البطاقات الأساسية

### 1. AkCard - البطاقة الأساسية الموحدة
```dart
AkCard(
  type: AkCardType.elevated,
  size: AkCardSize.medium,
  shape: AkCardShape.rounded,
  onTap: () => handleTap(),
  child: Text('محتوى البطاقة'),
)
```

**المميزات:**
- دعم جميع أنواع البطاقات (مسطحة، مرفوعة، محددة، متدرجة)
- أحجام مختلفة (صغير، متوسط، كبير، كبير جداً)
- أشكال مختلفة (مستطيل، مربع، دائري، حاد)
- تأثيرات تفاعلية مع التحميل الكسول

### 2. AkInfoCard - بطاقة المعلومات
```dart
AkInfoCard(
  title: 'معلومات المنتج',
  subtitle: 'تفاصيل إضافية',
  description: 'وصف مفصل للمنتج',
  icon: Icons.info,
  onTap: () => showDetails(),
)
```

**المميزات:**
- تصميم موحد لعرض المعلومات
- دعم الأيقونات والعناوين الفرعية
- سهم تنقل اختياري
- تأثيرات تفاعلية

### 3. AkActionCard - بطاقة الإجراءات
```dart
AkActionCard(
  title: 'إعدادات الحساب',
  subtitle: 'إدارة معلومات الحساب',
  icon: Icons.settings,
  actions: [
    AkButtons.edit(onPressed: () => editAccount()),
    AkButtons.delete(onPressed: () => deleteAccount()),
  ],
)
```

**المميزات:**
- تصميم موحد للبطاقات مع أزرار
- دعم أزرار متعددة مع تخطيط مرن
- تكامل مع نظام الأزرار الموحد (akbuttons.dart)
- اتجاه ترتيب الأزرار قابل للتخصيص

## 📊 البطاقات المتخصصة

### 4. AkStatsCard - بطاقة الإحصائيات
```dart
AkStatsCard(
  title: 'المبيعات',
  value: '25,480 ر.ي',
  subtitle: 'هذا الشهر',
  icon: Icons.shopping_cart,
  showTrend: true,
  trendValue: 12.5,
  isPositiveTrend: true,
  onTap: () => showSalesDetails(),
)
```

**المميزات:**
- تصميم موحد لعرض الإحصائيات
- دعم مؤشرات الاتجاه (صعود/هبوط)
- ألوان متنوعة حسب السياق
- تأثيرات تفاعلية متقدمة مع رسوم متحركة

## 🎨 الدعم الكامل للوضع المظلم/الفاتح

جميع البطاقات تدعم تلقائياً:

### ✅ الألوان التكيفية
- **الخلفية**: `isDark ? AppColors.darkSurface : AppColors.lightSurface`
- **النص الأساسي**: `isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary`
- **النص الثانوي**: `isDark ? AppColors.darkTextSecondary : AppColors.lightTextSecondary`
- **الحدود**: `isDark ? AppColors.darkBorder : AppColors.lightBorder`
- **الظلال**: `isDark ? AppColors.darkShadow : AppColors.lightShadow`

### ✅ التحقق التلقائي من الوضع
```dart
final theme = Theme.of(context);
final isDark = theme.brightness == Brightness.dark;
```

### ✅ عدم استخدام القيم الصريحة
- ❌ `Colors.blue` → ✅ `AppColors.primary`
- ❌ `EdgeInsets.all(16)` → ✅ `AppDimensions.defaultPadding`
- ❌ `fontSize: 14` → ✅ `AppTypography.fontSizeMedium`
- ❌ `BorderRadius.circular(8)` → ✅ `AppDimensions.mediumRadius`

## 🛠️ الدوال المساعدة السريعة

### ✅ دوال إنشاء عامة
```dart
// بطاقة أساسية
AkCards.basic(
  child: Text('محتوى'),
  onTap: () {},
)

// بطاقة معلومات
AkCards.info(
  title: 'العنوان',
  subtitle: 'العنوان الفرعي',
  icon: Icons.info,
)

// بطاقة إحصائيات
AkCards.stats(
  title: 'المبيعات',
  value: '25,480 ر.ي',
  icon: Icons.shopping_cart,
)

// بطاقة إجراءات
AkCards.action(
  title: 'الإعدادات',
  actions: [AkButtons.edit(onPressed: () {})],
)
```

### ✅ دوال متخصصة للمشروع
```dart
// بطاقة مبيعات
AkCards.sales(
  value: '25,480 ر.ي',
  showTrend: true,
  trendValue: 12.5,
)

// بطاقة مشتريات
AkCards.purchases(
  value: '18,320 ر.ي',
  showTrend: true,
  trendValue: -5.2,
  isPositiveTrend: false,
)

// بطاقة أرباح
AkCards.profit(
  value: '7,160 ر.ي',
  showTrend: true,
  trendValue: 18.3,
)

// بطاقة عملاء
AkCards.customers(
  value: '1,245',
  subtitle: 'إجمالي العملاء',
)

// بطاقة منتجات
AkCards.products(
  value: '856',
  subtitle: 'إجمالي المنتجات',
)
```

## 🎯 الأنواع والثوابت

### ✅ أنواع البطاقات (AkCardType)
- **flat**: بطاقة مسطحة (بدون ظل)
- **elevated**: بطاقة مرفوعة (مع ظل)
- **outlined**: بطاقة محددة (مع حدود)
- **gradient**: بطاقة متدرجة (مع خلفية متدرجة)

### ✅ أحجام البطاقات (AkCardSize)
- **small**: صغير
- **medium**: متوسط
- **large**: كبير
- **extraLarge**: كبير جداً

### ✅ أشكال البطاقات (AkCardShape)
- **rounded**: مستطيل مع زوايا مدورة
- **square**: مربع
- **circular**: دائري الزوايا
- **sharp**: مستطيل بزوايا حادة

## 🚀 المميزات الرئيسية

### ✅ التحميل الكسول
- **الرسوم المتحركة**: يتم تحميلها عند الحاجة فقط
- **مؤشرات الاتجاه**: يتم بناؤها عند التفاعل
- **التأثيرات التفاعلية**: يتم تطبيقها حسب الحاجة

### ✅ التكامل مع الأنظمة الموحدة
- **الأزرار**: تكامل مع `akbuttons.dart`
- **الحقول**: تكامل مع `akinputs.dart` (عند الحاجة)
- **الثيمات**: `AppColors.*`, `AppDimensions.*`, `AppTypography.*`

### ✅ التخصيص اليمني
- **العملة الافتراضية**: ريال يمني (ر.ي)
- **التنسيق**: فاصل الآلاف العربي
- **الألوان**: متناسقة مع الهوية اليمنية

### ✅ التأثيرات التفاعلية
- **الضغط**: تأثير تصغير وتكبير
- **التحويم**: تغيير الظل والارتفاع
- **الرسوم المتحركة**: انتقالات سلسة

## 📱 الاستجابة والتكيف

جميع البطاقات تتكيف تلقائياً مع:
- **حجم الشاشة**: أحجام متجاوبة
- **الوضع المظلم/الفاتح**: ألوان تكيفية
- **اتجاه النص**: دعم RTL/LTR

## 🎯 الاستخدام الموصى به

1. **استخدم البطاقات المتخصصة** للبيانات المحددة
2. **طبق التحميل الكسول** للعناصر الثقيلة
3. **اتبع نظام الثيمات** لضمان التناسق
4. **استخدم الدوال المساعدة** لسرعة التطوير
5. **اختبر في كلا الوضعين** (مظلم/فاتح)

## 🔄 الملفات المحذوفة والمستبدلة

- ✅ **adaptive_card.dart** → استخدم `AkCard`
- ✅ **stat_cards.dart** → استخدم `AkStatsCard`
- ✅ **custom_card.dart** → استخدم `AkCard`

---

**`akcards.dart` هو المصدر الوحيد والشامل لجميع البطاقات في التطبيق!** 🚀
