import 'package:flutter/material.dart';
import '../../../core/utils/index.dart';
import '../../../core/theme/index.dart';

/// شبكة الوصول السريع
/// تستخدم لعرض أزرار الوصول السريع في الشاشة الرئيسية
class QuickAccessGrid extends StatelessWidget {
  final List<Map<String, dynamic>> items;
  final Function(int) onTap;

  const QuickAccessGrid({
    Key? key,
    required this.items,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // حساب عدد الأعمدة بناءً على عرض الشاشة
        const double spacing = Layout.defaultSpacing;
        final int columns = MediaQuery.of(context).size.width > 600 ? 4 : 2;

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columns,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            childAspectRatio: 1.2,
          ),
          itemCount: items.length,
          itemBuilder: (context, index) {
            final item = items[index];
            return _buildGridItem(context, item, index);
          },
        );
      },
    );
  }

  Widget _buildGridItem(
      BuildContext context, Map<String, dynamic> item, int index) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => onTap(index),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: 24,
              backgroundColor: item['color'].withValues(alpha: 0.2),
              child: Icon(
                item['icon'],
                color: item['color'],
                size: 24,
              ),
            ),
            const SizedBox(height: AppDimensions.spacing8),
            Text(
              item['title'],
              textAlign: TextAlign.center,
              style: const AppTypography(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
