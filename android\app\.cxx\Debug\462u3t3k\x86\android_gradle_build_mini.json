{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterproject2\\tajer_plus\\android\\app\\.cxx\\Debug\\462u3t3k\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterproject2\\tajer_plus\\android\\app\\.cxx\\Debug\\462u3t3k\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}