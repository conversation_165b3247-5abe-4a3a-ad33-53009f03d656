import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:intl/intl.dart';
import '../../../core/widgets/index.dart';
import '../../../core/models/sale.dart';
import '../../../core/models/sale_status.dart';
import '../presenters/sale_presenter.dart';
import '../../customers/presenters/customer_presenter.dart';
import '../widgets/sale_details_dialog.dart';
import '../widgets/sale_filter_dialog.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

/// شاشة إدارة المبيعات
class SalesManagementScreen extends StatefulWidget {
  const SalesManagementScreen({Key? key}) : super(key: key);

  @override
  State<SalesManagementScreen> createState() => _SalesManagementScreenState();
}

class _SalesManagementScreenState extends State<SalesManagementScreen> {
  // مقدمو البيانات
  late SalePresenter _salePresenter;
  late CustomerPresenter _customerPresenter;

  // حالة البحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // حالة الفرز
  String _sortField = 'date';
  bool _sortAscending = false;

  // حالة الفلترة
  String? _selectedCustomerId;
  String? _selectedPaymentMethod;
  SaleStatus? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;

  // حالة التحميل
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // تهيئة المقدمين
    _salePresenter =
        AppProviders.getLazyPresenter<SalePresenter>(() => SalePresenter());
    _customerPresenter = AppProviders.getLazyPresenter<CustomerPresenter>(
        () => CustomerPresenter());

    // تحميل البيانات باستخدام Future.microtask لتجنب استدعاء setState أثناء البناء
    Future.microtask(_loadData);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل المبيعات
      await _salePresenter.loadSales();

      // تحميل العملاء
      await _customerPresenter.loadCustomers();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تحميل البيانات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// عرض تفاصيل البيع
  void _showSaleDetails(Sale sale) {
    showDialog(
      context: context,
      builder: (context) => SaleDetailsDialog(sale: sale),
    );
  }

  /// إلغاء عملية بيع
  Future<void> _cancelSale(Sale sale) async {
    if (sale.status == SaleStatus.cancelled) {
      _showErrorSnackBar('تم إلغاء عملية البيع بالفعل');
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء عملية البيع'),
        content: Text(
            'هل أنت متأكد من إلغاء عملية البيع رقم ${sale.id.substring(0, 8)}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('تراجع'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('إلغاء البيع'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        // تحديث حالة البيع إلى ملغي
        final updatedSale = sale.copyWith(status: SaleStatus.cancelled);
        final success = await _salePresenter.updateSale(updatedSale);

        if (success) {
          _showSuccessSnackBar('تم إلغاء عملية البيع بنجاح');
          _loadData();
        } else {
          _showErrorSnackBar('فشل إلغاء عملية البيع');
        }
      } catch (e) {
        _showErrorSnackBar('حدث خطأ أثناء إلغاء عملية البيع: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// حذف عملية بيع
  Future<void> _deleteSale(Sale sale) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف عملية البيع'),
        content: Text(
            'هل أنت متأكد من حذف عملية البيع رقم ${sale.id.substring(0, 8)}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final success = await _salePresenter.deleteSale(sale.id);

        if (success) {
          _showSuccessSnackBar('تم حذف عملية البيع بنجاح');
          _loadData();
        } else {
          _showErrorSnackBar('فشل حذف عملية البيع');
        }
      } catch (e) {
        _showErrorSnackBar('حدث خطأ أثناء حذف عملية البيع: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// طباعة إيصال البيع
  void _printReceipt(Sale sale) {
    // TODO: تنفيذ طباعة الإيصال
    _showSuccessSnackBar('جاري طباعة الإيصال...');
  }

  /// فرز المبيعات
  void _sortSales(String field) {
    setState(() {
      if (_sortField == field) {
        _sortAscending = !_sortAscending;
      } else {
        _sortField = field;
        _sortAscending = true;
      }
    });
  }

  /// عرض مربع حوار الفلترة
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => SaleFilterDialog(
        selectedCustomerId: _selectedCustomerId,
        selectedPaymentMethod: _selectedPaymentMethod,
        selectedStatus: _selectedStatus != null
            ? saleStatusToString(_selectedStatus!)
            : null,
        startDate: _startDate,
        endDate: _endDate,
        customers: _customerPresenter.customers,
        onApplyFilter: (customerId, paymentMethod, status, startDate, endDate) {
          setState(() {
            _selectedCustomerId = customerId;
            _selectedPaymentMethod = paymentMethod;
            _selectedStatus =
                status != null ? stringToSaleStatus(status) : null;
            _startDate = startDate;
            _endDate = endDate;
          });
        },
        onClearFilter: () {
          setState(() {
            _selectedCustomerId = null;
            _selectedPaymentMethod = null;
            _selectedStatus = null;
            _startDate = null;
            _endDate = null;
          });
        },
      ),
    );
  }

  /// الحصول على المبيعات المفلترة
  List<Sale> _getFilteredSales() {
    // فلترة المبيعات حسب البحث والفلاتر
    return _salePresenter.sales.where((sale) {
      // فلترة حسب البحث
      final saleId = sale.id.toLowerCase();
      final customerName = sale.customerName?.toLowerCase() ?? '';
      final query = _searchQuery.toLowerCase();

      final matchesSearch =
          saleId.contains(query) || customerName.contains(query);

      // فلترة حسب العميل
      final matchesCustomer =
          _selectedCustomerId == null || sale.customerId == _selectedCustomerId;

      // فلترة حسب طريقة الدفع
      final matchesPaymentMethod = _selectedPaymentMethod == null ||
          sale.paymentMethod == _selectedPaymentMethod;

      // فلترة حسب الحالة
      final matchesStatus =
          _selectedStatus == null || sale.status == _selectedStatus;

      // فلترة حسب التاريخ
      bool matchesDate = true;
      if (_startDate != null) {
        matchesDate = sale.createdAt.isAfter(_startDate!) ||
            sale.createdAt.isAtSameMomentAs(_startDate!);
      }
      if (_endDate != null && matchesDate) {
        final endOfDay = DateTime(
            _endDate!.year, _endDate!.month, _endDate!.day, 23, 59, 59);
        matchesDate = sale.createdAt.isBefore(endOfDay) ||
            sale.createdAt.isAtSameMomentAs(endOfDay);
      }

      return matchesSearch &&
          matchesCustomer &&
          matchesPaymentMethod &&
          matchesStatus &&
          matchesDate;
    }).toList()
      // فرز المبيعات
      ..sort((a, b) {
        int compare;

        switch (_sortField) {
          case 'date':
            compare = a.createdAt.compareTo(b.createdAt);
            break;
          case 'customer':
            final aName = a.customerName ?? '';
            final bName = b.customerName ?? '';
            compare = aName.compareTo(bName);
            break;
          case 'total':
            compare = a.total.compareTo(b.total);
            break;
          case 'status':
            compare = a.status.index.compareTo(b.status.index);
            break;
          default:
            compare = a.createdAt.compareTo(b.createdAt);
        }

        return _sortAscending ? compare : -compare;
      });
  }

  @override
  Widget build(BuildContext context) {
    return SafeLayout(
      title: 'إدارة المبيعات',
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFilterDialog,
          tooltip: 'فلترة',
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadData,
          tooltip: 'تحديث',
        ),
      ],
      body: _buildContent(),
      child: Container(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // شريط البحث وفلترة التاريخ
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // حقل البحث
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'بحث عن فاتورة...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),

              // فلترة التاريخ
              OutlinedButton.icon(
                icon: const Icon(Icons.date_range),
                label: Text(_startDate != null && _endDate != null
                    ? '${_startDate!.day}/${_startDate!.month} - ${_endDate!.day}/${_endDate!.month}'
                    : 'اختيار نطاق التاريخ'),
                onPressed: () async {
                  final picked = await showDateRangePicker(
                    context: context,
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now(),
                    initialDateRange: _startDate != null && _endDate != null
                        ? DateTimeRange(start: _startDate!, end: _endDate!)
                        : null,
                  );
                  if (picked != null) {
                    setState(() {
                      _startDate = picked.start;
                      _endDate = picked.end;
                    });
                  }
                },
              ),
            ],
          ),
        ),

        // عدد المبيعات وحالة الفلترة
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'عدد المبيعات: ${_getFilteredSales().length}',
                style: AppTypography.lightTextTheme.titleMedium,
              ),
              const Spacer(),
              if (_searchQuery.isNotEmpty ||
                  _selectedCustomerId != null ||
                  _selectedPaymentMethod != null ||
                  _selectedStatus != null ||
                  _startDate != null ||
                  _endDate != null)
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                      _searchController.clear();
                      _selectedCustomerId = null;
                      _selectedPaymentMethod = null;
                      _selectedStatus = null;
                      _startDate = null;
                      _endDate = null;
                    });
                  },
                  icon: const Icon(Icons.clear),
                  label: const Text('مسح الفلاتر'),
                ),
            ],
          ),
        ),

        // قائمة المبيعات
        Expanded(
          child: _buildSalesList(),
        ),
      ],
    );
  }

  /// بناء قائمة المبيعات
  Widget _buildSalesList() {
    final sales = _getFilteredSales();

    if (sales.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.receipt_long,
              size: 64,
              color: AppColors.lightTextSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty &&
                      _selectedCustomerId == null &&
                      _selectedPaymentMethod == null &&
                      _selectedStatus == null &&
                      _startDate == null &&
                      _endDate == null
                  ? 'لا يوجد مبيعات'
                  : 'لا يوجد مبيعات مطابقة للفلاتر',
              style: AppTypography.lightTextTheme.titleMedium,
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: [
            DataColumn(
              label: const Text('التاريخ'),
              onSort: (_, __) => _sortSales('date'),
            ),
            const DataColumn(
              label: Text('رقم الفاتورة'),
            ),
            DataColumn(
              label: const Text('العميل'),
              onSort: (_, __) => _sortSales('customer'),
            ),
            const DataColumn(
              label: Text('طريقة الدفع'),
            ),
            DataColumn(
              label: const Text('المبلغ'),
              onSort: (_, __) => _sortSales('total'),
              numeric: true,
            ),
            DataColumn(
              label: const Text('الحالة'),
              onSort: (_, __) => _sortSales('status'),
            ),
            const DataColumn(
              label: Text('الإجراءات'),
            ),
          ],
          rows: sales.map((sale) {
            return DataRow(
              cells: [
                DataCell(Text(
                    DateFormat('yyyy-MM-dd HH:mm').format(sale.createdAt))),
                DataCell(Text(sale.id.substring(0, 8))),
                DataCell(Text(sale.customerName ?? 'عميل نقدي')),
                DataCell(Text(_getPaymentMethodName(sale.paymentMethod))),
                DataCell(Text(sale.total.toStringAsFixed(2))),
                DataCell(_buildStatusChip(sale.status)),
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.visibility),
                        onPressed: () => _showSaleDetails(sale),
                        tooltip: 'عرض التفاصيل',
                      ),
                      IconButton(
                        icon: const Icon(Icons.print),
                        onPressed: () => _printReceipt(sale),
                        tooltip: 'طباعة',
                      ),
                      if (sale.status != SaleStatus.cancelled)
                        IconButton(
                          icon: const Icon(Icons.cancel),
                          onPressed: () => _cancelSale(sale),
                          tooltip: 'إلغاء',
                          color: AppColors.lightTextSecondary,
                        ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _deleteSale(sale),
                        tooltip: 'حذف',
                        color: AppColors.lightTextSecondary,
                      ),
                    ],
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  /// بناء شريحة الحالة
  Widget _buildStatusChip(SaleStatus status) {
    Color color;
    String label;

    switch (status) {
      case SaleStatus.completed:
        color = AppColors.success;
        label = 'مكتمل';
        break;
      case SaleStatus.cancelled:
        color = AppColors.error;
        label = 'ملغي';
        break;
      case SaleStatus.draft:
        color = AppColors.warning;
        label = 'مسودة';
        break;
      case SaleStatus.pending:
        color = AppColors.info;
        label = 'قيد الانتظار';
        break;
      case SaleStatus.returned:
        color = AppColors.accent;
        label = 'مرتجع';
        break;
    }

    return Chip(
      label: Text(
        label,
        style: const AppTypography(
          color: AppColors.lightTextSecondary,
          fontSize: 12,
        ),
      ),
      backgroundColor: color,
      padding: EdgeInsets.zero,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  /// الحصول على اسم طريقة الدفع
  String _getPaymentMethodName(String method) {
    switch (method) {
      case 'cash':
        return 'نقداً';
      case 'credit_card':
        return 'بطاقة ائتمان';
      case 'debit_card':
        return 'بطاقة خصم';
      case 'bank_transfer':
        return 'تحويل بنكي';
      case 'check':
        return 'شيك';
      case 'credit':
        return 'آجل';
      case 'other':
        return 'أخرى';
      default:
        return method;
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
        ),
      );
    });
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.success,
        ),
      );
    });
  }
}
