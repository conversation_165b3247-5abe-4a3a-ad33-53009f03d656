import 'package:uuid/uuid.dart';
import 'base_model.dart';
import 'product.dart';
import 'warehouse.dart';

/// نموذج المخزون
class Inventory extends BaseModel {
  final String productId;
  final Product? product; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final String warehouseId;
  final Warehouse? warehouse; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final double quantity;
  final double minQuantity;
  final double? maxQuantity;
  final DateTime? lastCountDate;
  final DateTime? lastPurchaseDate;
  final DateTime? lastSaleDate;

  Inventory({
    String? id,
    required this.productId,
    this.product,
    required this.warehouseId,
    this.warehouse,
    this.quantity = 0.0,
    this.minQuantity = 0.0,
    this.maxQuantity,
    this.lastCountDate,
    this.lastPurchaseDate,
    this.lastSaleDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: null,
          updatedAt: updatedAt,
          updatedBy: null,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا المخزون مع استبدال الحقول المحددة بقيم جديدة
  Inventory copyWith({
    String? id,
    String? productId,
    Product? product,
    String? warehouseId,
    Warehouse? warehouse,
    double? quantity,
    double? minQuantity,
    double? maxQuantity,
    DateTime? lastCountDate,
    DateTime? lastPurchaseDate,
    DateTime? lastSaleDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
  }) {
    return Inventory(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      warehouseId: warehouseId ?? this.warehouseId,
      warehouse: warehouse ?? this.warehouse,
      quantity: quantity ?? this.quantity,
      minQuantity: minQuantity ?? this.minQuantity,
      maxQuantity: maxQuantity ?? this.maxQuantity,
      lastCountDate: lastCountDate ?? this.lastCountDate,
      lastPurchaseDate: lastPurchaseDate ?? this.lastPurchaseDate,
      lastSaleDate: lastSaleDate ?? this.lastSaleDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل المخزون إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'warehouse_id': warehouseId,
      'quantity': quantity,
      'min_quantity': minQuantity,
      'max_quantity': maxQuantity,
      'last_count_date': lastCountDate?.toIso8601String(),
      'last_purchase_date': lastPurchaseDate?.toIso8601String(),
      'last_sale_date': lastSaleDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء مخزون من Map
  factory Inventory.fromMap(Map<String, dynamic> map) {
    return Inventory(
      id: map['id'],
      productId: map['product_id'],
      product: map['product'] != null ? Product.fromMap(map['product']) : null,
      warehouseId: map['warehouse_id'],
      warehouse:
          map['warehouse'] != null ? Warehouse.fromMap(map['warehouse']) : null,
      quantity: map['quantity'] is int
          ? (map['quantity'] as int).toDouble()
          : (map['quantity'] as double? ?? 0.0),
      minQuantity: map['min_quantity'] is int
          ? (map['min_quantity'] as int).toDouble()
          : (map['min_quantity'] as double? ?? 0.0),
      maxQuantity: map['max_quantity'] != null
          ? (map['max_quantity'] is int
              ? (map['max_quantity'] as int).toDouble()
              : map['max_quantity'] as double)
          : null,
      lastCountDate: map['last_count_date'] != null
          ? DateTime.parse(map['last_count_date'])
          : null,
      lastPurchaseDate: map['last_purchase_date'] != null
          ? DateTime.parse(map['last_purchase_date'])
          : null,
      lastSaleDate: map['last_sale_date'] != null
          ? DateTime.parse(map['last_sale_date'])
          : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      isDeleted: map['is_deleted'] == 1,
    );
  }

  @override
  String toString() {
    return 'Inventory(id: $id, productId: $productId, warehouseId: $warehouseId, quantity: $quantity)';
  }
}
