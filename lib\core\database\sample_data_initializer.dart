import 'package:uuid/uuid.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';
import 'database_service.dart';

/// مهيئ البيانات التجريبية
/// يستخدم لإنشاء بيانات تجريبية للتطبيق
class SampleDataInitializer {
  final DatabaseService _db = DatabaseService.instance;

  /// تهيئة البيانات التجريبية
  Future<bool> initializeSampleData() async {
    try {
      AppLogger.info('بدء تهيئة البيانات التجريبية...');

      // إنشاء بيانات تجريبية
      await createSampleCategories();
      await createSampleUnits();
      await createSampleProducts();
      await createSampleCustomers();
      await createSampleSuppliers();
      await createSampleWarehouses();
      // تم تعطيل هذه الدوال مؤقتًا لأنها غير مكتملة
      // await createSampleSales();
      // await createSamplePurchases();

      // إنشاء شجرة الحسابات المحاسبية
      await initializeAccountingChartOfAccounts();

      AppLogger.info('تم تهيئة البيانات التجريبية بنجاح');
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تهيئة البيانات التجريبية',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// تهيئة شجرة الحسابات المحاسبية المتعارف عليها عالمياً
  Future<bool> initializeAccountingChartOfAccounts() async {
    try {
      AppLogger.info('بدء تهيئة شجرة الحسابات المحاسبية...');

      final db = await _db.database;

      // التحقق من وجود حسابات في جدول accounts
      final accountsCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM accounts',
      );
      final count = accountsCount.first['count'] as int;

      if (count > 0) {
        AppLogger.info('الحسابات موجودة بالفعل، تخطي إنشاء شجرة الحسابات');
        return true;
      }

      // إنشاء الحسابات الرئيسية (المستوى الأول)
      final rootAccounts = [
        // الأصول (Assets)
        _createAccount(
          name: 'الأصول',
          code: '1',
          accountType: 'asset',
          description: 'جميع الأصول والممتلكات',
        ),

        // الخصوم (Liabilities)
        _createAccount(
          name: 'الخصوم',
          code: '2',
          accountType: 'liability',
          description: 'جميع الالتزامات والديون',
        ),

        // حقوق الملكية (Equity)
        _createAccount(
          name: 'حقوق الملكية',
          code: '3',
          accountType: 'equity',
          description: 'حقوق المساهمين ورأس المال',
        ),

        // الإيرادات (Revenue)
        _createAccount(
          name: 'الإيرادات',
          code: '4',
          accountType: 'revenue',
          description: 'جميع مصادر الدخل والإيرادات',
        ),

        // المصروفات (Expenses)
        _createAccount(
          name: 'المصروفات',
          code: '5',
          accountType: 'expense',
          description: 'جميع النفقات والمصروفات',
        ),
      ];

      // إدخال الحسابات الرئيسية
      final rootAccountIds = <String, String>{};
      for (final account in rootAccounts) {
        await db.insert('accounts', account);
        rootAccountIds[account['code']] = account['id'];
      }

      AppLogger.info('تم إنشاء ${rootAccounts.length} حسابات رئيسية');

      // إنشاء الحسابات الفرعية للأصول (المستوى الثاني)
      final assetAccounts = [
        _createAccount(
          name: 'الأصول المتداولة',
          code: '1.1',
          accountType: 'asset',
          description: 'الأصول التي يمكن تحويلها إلى نقد خلال سنة',
          parentId: rootAccountIds['1'],
        ),
        _createAccount(
          name: 'الأصول الثابتة',
          code: '1.2',
          accountType: 'asset',
          description: 'الأصول طويلة الأجل مثل المباني والمعدات',
          parentId: rootAccountIds['1'],
        ),
        _createAccount(
          name: 'الأصول غير الملموسة',
          code: '1.3',
          accountType: 'asset',
          description:
              'الأصول غير المادية مثل براءات الاختراع والعلامات التجارية',
          parentId: rootAccountIds['1'],
        ),
        _createAccount(
          name: 'الاستثمارات طويلة الأجل',
          code: '1.4',
          accountType: 'asset',
          description: 'استثمارات طويلة الأجل في أسهم وسندات',
          parentId: rootAccountIds['1'],
        ),
      ];

      // إدخال حسابات الأصول
      final assetAccountIds = <String, String>{};
      for (final account in assetAccounts) {
        await db.insert('accounts', account);
        assetAccountIds[account['code']] = account['id'];
      }

      // إنشاء الحسابات الفرعية للخصوم (المستوى الثاني)
      final liabilityAccounts = [
        _createAccount(
          name: 'الخصوم المتداولة',
          code: '2.1',
          accountType: 'liability',
          description: 'الالتزامات قصيرة الأجل المستحقة خلال سنة',
          parentId: rootAccountIds['2'],
        ),
        _createAccount(
          name: 'الخصوم طويلة الأجل',
          code: '2.2',
          accountType: 'liability',
          description: 'الالتزامات طويلة الأجل المستحقة بعد سنة أو أكثر',
          parentId: rootAccountIds['2'],
        ),
      ];

      // إدخال حسابات الخصوم
      final liabilityAccountIds = <String, String>{};
      for (final account in liabilityAccounts) {
        await db.insert('accounts', account);
        liabilityAccountIds[account['code']] = account['id'];
      }

      // إنشاء الحسابات الفرعية لحقوق الملكية (المستوى الثاني)
      final equityAccounts = [
        _createAccount(
          name: 'رأس المال',
          code: '3.1',
          accountType: 'equity',
          description: 'رأس المال المدفوع',
          parentId: rootAccountIds['3'],
        ),
        _createAccount(
          name: 'الأرباح المحتجزة',
          code: '3.2',
          accountType: 'equity',
          description: 'الأرباح المحتفظ بها في الشركة',
          parentId: rootAccountIds['3'],
        ),
        _createAccount(
          name: 'توزيعات الأرباح',
          code: '3.3',
          accountType: 'equity',
          description: 'توزيعات الأرباح للمساهمين',
          parentId: rootAccountIds['3'],
        ),
      ];

      // إدخال حسابات حقوق الملكية
      for (final account in equityAccounts) {
        await db.insert('accounts', account);
      }

      // إنشاء الحسابات الفرعية للإيرادات (المستوى الثاني)
      final revenueAccounts = [
        _createAccount(
          name: 'إيرادات المبيعات',
          code: '4.1',
          accountType: 'revenue',
          description: 'إيرادات من بيع البضائع',
          parentId: rootAccountIds['4'],
        ),
        _createAccount(
          name: 'إيرادات الخدمات',
          code: '4.2',
          accountType: 'revenue',
          description: 'إيرادات من تقديم الخدمات',
          parentId: rootAccountIds['4'],
        ),
        _createAccount(
          name: 'إيرادات أخرى',
          code: '4.3',
          accountType: 'revenue',
          description: 'إيرادات متنوعة أخرى',
          parentId: rootAccountIds['4'],
        ),
      ];

      // إدخال حسابات الإيرادات
      for (final account in revenueAccounts) {
        await db.insert('accounts', account);
      }

      // إنشاء الحسابات الفرعية للمصروفات (المستوى الثاني)
      final expenseAccounts = [
        _createAccount(
          name: 'تكلفة البضاعة المباعة',
          code: '5.1',
          accountType: 'expense',
          description: 'تكلفة البضائع المباعة',
          parentId: rootAccountIds['5'],
        ),
        _createAccount(
          name: 'مصروفات التشغيل',
          code: '5.2',
          accountType: 'expense',
          description: 'مصروفات تشغيلية عامة',
          parentId: rootAccountIds['5'],
        ),
        _createAccount(
          name: 'مصروفات البيع والتسويق',
          code: '5.3',
          accountType: 'expense',
          description: 'مصروفات البيع والتسويق والإعلان',
          parentId: rootAccountIds['5'],
        ),
        _createAccount(
          name: 'مصروفات إدارية وعمومية',
          code: '5.4',
          accountType: 'expense',
          description: 'مصروفات إدارية وعمومية',
          parentId: rootAccountIds['5'],
        ),
        _createAccount(
          name: 'مصروفات الضرائب',
          code: '5.5',
          accountType: 'expense',
          description: 'ضرائب الدخل وغيرها',
          parentId: rootAccountIds['5'],
        ),
      ];

      // إدخال حسابات المصروفات
      for (final account in expenseAccounts) {
        await db.insert('accounts', account);
      }

      // إنشاء الحسابات الفرعية للأصول المتداولة (المستوى الثالث)
      final currentAssetAccounts = [
        _createAccount(
          name: 'النقد وما في حكمه',
          code: '1.1.1',
          accountType: 'asset',
          description: 'النقد في الصندوق والبنوك',
          parentId: assetAccountIds['1.1'],
        ),
        _createAccount(
          name: 'الذمم المدينة',
          code: '1.1.2',
          accountType: 'asset',
          description: 'المبالغ المستحقة من العملاء',
          parentId: assetAccountIds['1.1'],
        ),
        _createAccount(
          name: 'المخزون',
          code: '1.1.3',
          accountType: 'asset',
          description: 'البضائع المتاحة للبيع',
          parentId: assetAccountIds['1.1'],
        ),
        _createAccount(
          name: 'مصروفات مدفوعة مقدماً',
          code: '1.1.4',
          accountType: 'asset',
          description: 'مصروفات مدفوعة مقدماً مثل الإيجار والتأمين',
          parentId: assetAccountIds['1.1'],
        ),
      ];

      // إدخال حسابات الأصول المتداولة
      for (final account in currentAssetAccounts) {
        await db.insert('accounts', account);
      }

      // إنشاء الحسابات الفرعية للأصول الثابتة (المستوى الثالث)
      final fixedAssetAccounts = [
        _createAccount(
          name: 'الأراضي',
          code: '1.2.1',
          accountType: 'asset',
          description: 'الأراضي المملوكة',
          parentId: assetAccountIds['1.2'],
        ),
        _createAccount(
          name: 'المباني',
          code: '1.2.2',
          accountType: 'asset',
          description: 'المباني والإنشاءات',
          parentId: assetAccountIds['1.2'],
        ),
        _createAccount(
          name: 'الآلات والمعدات',
          code: '1.2.3',
          accountType: 'asset',
          description: 'الآلات والمعدات المستخدمة في الإنتاج',
          parentId: assetAccountIds['1.2'],
        ),
        _createAccount(
          name: 'الأثاث والتجهيزات',
          code: '1.2.4',
          accountType: 'asset',
          description: 'الأثاث والتجهيزات المكتبية',
          parentId: assetAccountIds['1.2'],
        ),
        _createAccount(
          name: 'السيارات ووسائل النقل',
          code: '1.2.5',
          accountType: 'asset',
          description: 'السيارات ووسائل النقل المملوكة',
          parentId: assetAccountIds['1.2'],
        ),
        _createAccount(
          name: 'مجمع الإهلاك',
          code: '1.2.6',
          accountType: 'asset',
          description: 'مجمع إهلاك الأصول الثابتة (حساب متمم)',
          parentId: assetAccountIds['1.2'],
        ),
      ];

      // إدخال حسابات الأصول الثابتة
      for (final account in fixedAssetAccounts) {
        await db.insert('accounts', account);
      }

      // إنشاء الحسابات الفرعية للخصوم المتداولة (المستوى الثالث)
      final currentLiabilityAccounts = [
        _createAccount(
          name: 'الذمم الدائنة',
          code: '2.1.1',
          accountType: 'liability',
          description: 'المبالغ المستحقة للموردين',
          parentId: liabilityAccountIds['2.1'],
        ),
        _createAccount(
          name: 'القروض قصيرة الأجل',
          code: '2.1.2',
          accountType: 'liability',
          description: 'القروض المستحقة خلال سنة',
          parentId: liabilityAccountIds['2.1'],
        ),
        _createAccount(
          name: 'مصروفات مستحقة',
          code: '2.1.3',
          accountType: 'liability',
          description: 'المصروفات المستحقة غير المدفوعة',
          parentId: liabilityAccountIds['2.1'],
        ),
        _createAccount(
          name: 'إيرادات مقبوضة مقدماً',
          code: '2.1.4',
          accountType: 'liability',
          description: 'إيرادات محصلة مقدماً قبل تقديم الخدمة',
          parentId: liabilityAccountIds['2.1'],
        ),
        _createAccount(
          name: 'ضرائب مستحقة',
          code: '2.1.5',
          accountType: 'liability',
          description: 'ضرائب مستحقة الدفع',
          parentId: liabilityAccountIds['2.1'],
        ),
      ];

      // إدخال حسابات الخصوم المتداولة
      for (final account in currentLiabilityAccounts) {
        await db.insert('accounts', account);
      }

      // إنشاء الحسابات الفرعية للخصوم طويلة الأجل (المستوى الثالث)
      final longTermLiabilityAccounts = [
        _createAccount(
          name: 'القروض طويلة الأجل',
          code: '2.2.1',
          accountType: 'liability',
          description: 'القروض المستحقة بعد سنة أو أكثر',
          parentId: liabilityAccountIds['2.2'],
        ),
        _createAccount(
          name: 'السندات المصدرة',
          code: '2.2.2',
          accountType: 'liability',
          description: 'السندات المصدرة طويلة الأجل',
          parentId: liabilityAccountIds['2.2'],
        ),
        _createAccount(
          name: 'مخصص مكافأة نهاية الخدمة',
          code: '2.2.3',
          accountType: 'liability',
          description: 'مخصص مكافأة نهاية الخدمة للموظفين',
          parentId: liabilityAccountIds['2.2'],
        ),
      ];

      // إدخال حسابات الخصوم طويلة الأجل
      for (final account in longTermLiabilityAccounts) {
        await db.insert('accounts', account);
      }

      AppLogger.info('تم إنشاء شجرة الحسابات المحاسبية بنجاح');
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تهيئة شجرة الحسابات المحاسبية',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// إنشاء حساب محاسبي
  Map<String, dynamic> _createAccount({
    required String name,
    required String code,
    required String accountType,
    String? description,
    String? parentId,
    double openingBalance = 0.0,
  }) {
    final now = DateTime.now().toIso8601String();
    return {
      'id': const Uuid().v4(),
      'name': name,
      'code': code, // رمز الحساب
      'description': description,
      'parent_id': parentId,
      'account_type': accountType,
      'type': accountType, // الاحتفاظ بحقل type للتوافق مع الكود القديم
      'opening_balance': openingBalance,
      'current_balance': openingBalance,
      'is_active': 1,
      'created_at': now,
      'updated_at': now,
      'created_by': 'system',
      'updated_by': null,
      'is_deleted': 0,
    };
  }

  /// إنشاء فئات تجريبية
  Future<void> createSampleCategories() async {
    try {
      final db = await _db.database;

      // التحقق من وجود فئات منتجات في جدول categories
      final productCategoriesCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM categories WHERE type = ?',
        ['product'],
      );
      final count = productCategoriesCount.first['count'] as int;

      if (count > 0) {
        AppLogger.info(
            'فئات المنتجات موجودة بالفعل، تخطي إنشاء فئات منتجات تجريبية');
      } else {
        // إنشاء فئات منتجات تجريبية
        final productCategories = [
          {
            'id': const Uuid().v4(),
            'name': 'إلكترونيات',
            'description': 'منتجات إلكترونية',
            'type': 'product',
            'is_active': 1,
            'is_deleted': 0,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          },
          {
            'id': const Uuid().v4(),
            'name': 'ملابس',
            'description': 'ملابس رجالية ونسائية',
            'type': 'product',
            'is_active': 1,
            'is_deleted': 0,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          },
          {
            'id': const Uuid().v4(),
            'name': 'أغذية',
            'description': 'منتجات غذائية',
            'type': 'product',
            'is_active': 1,
            'is_deleted': 0,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          },
        ];

        // إدخال فئات المنتجات في جدول categories
        for (final category in productCategories) {
          await db.insert('categories', category);
        }

        AppLogger.info(
            'تم إنشاء ${productCategories.length} فئات منتجات تجريبية');
      }

      // إنشاء فئات مصروفات تجريبية
      final expenseCategoriesCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM categories WHERE type = ?',
        ['expense'],
      );
      final expenseCount = expenseCategoriesCount.first['count'] as int;

      if (expenseCount > 0) {
        AppLogger.info(
            'فئات المصروفات موجودة بالفعل، تخطي إنشاء فئات مصروفات تجريبية');
      } else {
        // إنشاء فئات مصروفات تجريبية
        final expenseCategories = [
          {
            'id': const Uuid().v4(),
            'name': 'إيجار',
            'description': 'مصاريف الإيجار',
            'type': 'expense',
            'is_active': 1,
            'is_deleted': 0,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          },
          {
            'id': const Uuid().v4(),
            'name': 'رواتب',
            'description': 'رواتب الموظفين',
            'type': 'expense',
            'is_active': 1,
            'is_deleted': 0,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          },
          {
            'id': const Uuid().v4(),
            'name': 'مرافق',
            'description': 'مصاريف المرافق (كهرباء، ماء، إلخ)',
            'type': 'expense',
            'is_active': 1,
            'is_deleted': 0,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          },
        ];

        // إدخال فئات المصروفات في جدول categories
        for (final category in expenseCategories) {
          await db.insert('categories', category);
        }

        AppLogger.info(
            'تم إنشاء ${expenseCategories.length} فئات مصروفات تجريبية');
      }
    } catch (e) {
      AppLogger.error('خطأ في إنشاء فئات تجريبية: $e');
      rethrow;
    }
  }

  /// إنشاء وحدات تجريبية
  Future<void> createSampleUnits() async {
    try {
      final db = await _db.database;

      // التحقق من وجود وحدات
      final unitsCount = await _getTableCount('units');
      if (unitsCount > 0) {
        AppLogger.info('الوحدات موجودة بالفعل، تخطي إنشاء وحدات تجريبية');
        return;
      }

      // إنشاء وحدات تجريبية
      final units = [
        {
          'id': const Uuid().v4(),
          'name': 'قطعة',
          'code': 'PCS',
          'is_active': 1,
          'is_deleted': 0,
          'created_at': DateTime.now().toIso8601String(),
        },
        {
          'id': const Uuid().v4(),
          'name': 'كيلوجرام',
          'code': 'KG',
          'is_active': 1,
          'is_deleted': 0,
          'created_at': DateTime.now().toIso8601String(),
        },
        {
          'id': const Uuid().v4(),
          'name': 'لتر',
          'code': 'L',
          'is_active': 1,
          'is_deleted': 0,
          'created_at': DateTime.now().toIso8601String(),
        },
      ];

      // إدخال الوحدات
      for (final unit in units) {
        await db.insert('units', unit);
      }

      AppLogger.info('تم إنشاء ${units.length} وحدات تجريبية');
    } catch (e) {
      AppLogger.error('خطأ في إنشاء وحدات تجريبية: $e');
      rethrow;
    }
  }

  /// إنشاء منتجات تجريبية
  Future<void> createSampleProducts() async {
    try {
      final db = await _db.database;

      // التحقق من وجود منتجات
      final productsCount = await _getTableCount('products');
      if (productsCount > 0) {
        AppLogger.info('المنتجات موجودة بالفعل، تخطي إنشاء منتجات تجريبية');
        return;
      }

      // الحصول على فئات المنتجات
      final categories = await db.query(
        'categories',
        where: 'type = ? AND is_deleted = ?',
        whereArgs: ['product', 0],
      );
      if (categories.isEmpty) {
        AppLogger.warning('لا توجد فئات منتجات، تخطي إنشاء منتجات تجريبية');
        return;
      }

      // الحصول على الوحدات
      final units = await db.query('units');
      if (units.isEmpty) {
        AppLogger.warning('لا توجد وحدات، تخطي إنشاء منتجات تجريبية');
        return;
      }

      // التحقق من عدد الفئات المتاحة وتعديل المنتجات وفقًا لذلك
      final categoryCount = categories.length;
      final unitCount = units.length;

      // إنشاء منتجات تجريبية مع مراعاة عدد الفئات والوحدات المتاحة
      final products = [];

      // إضافة منتج للفئة الأولى
      if (categoryCount > 0 && unitCount > 0) {
        products.add({
          'id': const Uuid().v4(),
          'name': 'هاتف ذكي',
          'description': 'هاتف ذكي حديث',
          'barcode': '1234567890123',
          'category_id': categories[0]['id'],
          'unit_id': units[0]['id'],
          'cost_price': 1000.0,
          'selling_price': 1200.0,
          'quantity': 50.0,
          'min_stock': 10.0,
          'is_active': 1,
          'is_deleted': 0,
          'created_at': DateTime.now().toIso8601String(),
        });
      }

      // إضافة منتج للفئة الثانية إذا كانت متوفرة
      if (categoryCount > 1 && unitCount > 0) {
        products.add({
          'id': const Uuid().v4(),
          'name': 'قميص رجالي',
          'description': 'قميص رجالي قطن',
          'barcode': '2345678901234',
          'category_id': categories[1]['id'],
          'unit_id': units[0]['id'],
          'cost_price': 50.0,
          'selling_price': 80.0,
          'quantity': 100.0,
          'min_stock': 20.0,
          'is_active': 1,
          'is_deleted': 0,
          'created_at': DateTime.now().toIso8601String(),
        });
      }

      // إضافة منتج للفئة الثالثة إذا كانت متوفرة ووحدة ثانية إذا كانت متوفرة
      if (categoryCount > 2 && unitCount > 1) {
        products.add({
          'id': const Uuid().v4(),
          'name': 'أرز بسمتي',
          'description': 'أرز بسمتي فاخر',
          'barcode': '3456789012345',
          'category_id': categories[2]['id'],
          'unit_id': units[1]['id'],
          'cost_price': 10.0,
          'selling_price': 15.0,
          'quantity': 200.0,
          'min_stock': 50.0,
          'is_active': 1,
          'is_deleted': 0,
          'created_at': DateTime.now().toIso8601String(),
        });
      } else if (categoryCount > 2 && unitCount > 0) {
        // استخدام الوحدة الأولى إذا لم تكن هناك وحدة ثانية
        products.add({
          'id': const Uuid().v4(),
          'name': 'أرز بسمتي',
          'description': 'أرز بسمتي فاخر',
          'barcode': '3456789012345',
          'category_id': categories[2]['id'],
          'unit_id': units[0]['id'],
          'cost_price': 10.0,
          'selling_price': 15.0,
          'quantity': 200.0,
          'min_stock': 50.0,
          'is_active': 1,
          'is_deleted': 0,
          'created_at': DateTime.now().toIso8601String(),
        });
      }

      // إدخال المنتجات
      if (products.isNotEmpty) {
        for (final product in products) {
          await db.insert('products', product);
        }
        AppLogger.info('تم إنشاء ${products.length} منتجات تجريبية');
      } else {
        AppLogger.warning(
            'لم يتم إنشاء أي منتجات تجريبية بسبب عدم توفر الفئات أو الوحدات الكافية');
      }
    } catch (e) {
      AppLogger.error('خطأ في إنشاء منتجات تجريبية: $e');
      rethrow;
    }
  }

  /// إنشاء عملاء تجريبيين
  Future<void> createSampleCustomers() async {
    try {
      final db = await _db.database;

      // التحقق من وجود عملاء
      final customersCount = await _getTableCount('customers');
      if (customersCount > 0) {
        AppLogger.info('العملاء موجودون بالفعل، تخطي إنشاء عملاء تجريبيين');
        return;
      }

      // إنشاء عملاء تجريبيين
      final customers = [
        {
          'id': const Uuid().v4(),
          'name': 'أحمد محمد',
          'phone': '0123456789',
          'email': '<EMAIL>',
          'address': 'القاهرة، مصر',
          'opening_balance': 0.0,
          'balance': 0.0,
          'credit_limit': 1000.0,
          'is_active': 1,
          'is_deleted': 0,
          'created_at': DateTime.now().toIso8601String(),
        },
        {
          'id': const Uuid().v4(),
          'name': 'محمد علي',
          'phone': '0123456788',
          'email': '<EMAIL>',
          'address': 'الإسكندرية، مصر',
          'opening_balance': 0.0,
          'balance': 0.0,
          'credit_limit': 500.0,
          'is_active': 1,
          'is_deleted': 0,
          'created_at': DateTime.now().toIso8601String(),
        },
      ];

      // إدخال العملاء
      for (final customer in customers) {
        await db.insert('customers', customer);
      }

      AppLogger.info('تم إنشاء ${customers.length} عملاء تجريبيين');
    } catch (e) {
      AppLogger.error('خطأ في إنشاء عملاء تجريبيين: $e');
      rethrow;
    }
  }

  /// إنشاء موردين تجريبيين
  Future<void> createSampleSuppliers() async {
    try {
      final db = await _db.database;

      // التحقق من وجود موردين
      final suppliersCount = await _getTableCount('suppliers');
      if (suppliersCount > 0) {
        AppLogger.info('الموردون موجودون بالفعل، تخطي إنشاء موردين تجريبيين');
        return;
      }

      // إنشاء موردين تجريبيين
      final suppliers = [
        {
          'id': const Uuid().v4(),
          'name': 'شركة الإلكترونيات',
          'phone': '**********',
          'email': '<EMAIL>',
          'address': 'القاهرة، مصر',
          'balance': 0.0,
          'supplier_type': 'شركة',
          'account_id': null,
          'is_active': 1,
          'is_deleted': 0,
          'created_at': DateTime.now().toIso8601String(),
        },
        {
          'id': const Uuid().v4(),
          'name': 'شركة الملابس',
          'phone': '**********',
          'email': '<EMAIL>',
          'address': 'الإسكندرية، مصر',
          'balance': 0.0,
          'supplier_type': 'مصنع',
          'account_id': null,
          'is_active': 1,
          'is_deleted': 0,
          'created_at': DateTime.now().toIso8601String(),
        },
      ];

      // إدخال الموردين
      for (final supplier in suppliers) {
        await db.insert('suppliers', supplier);
      }

      AppLogger.info('تم إنشاء ${suppliers.length} موردين تجريبيين');
    } catch (e) {
      AppLogger.error('خطأ في إنشاء موردين تجريبيين: $e');
      rethrow;
    }
  }

  /// إنشاء مستودعات تجريبية
  Future<void> createSampleWarehouses() async {
    try {
      final db = await _db.database;

      // التحقق من وجود مستودعات
      final warehousesCount = await _getTableCount('warehouses');
      if (warehousesCount > 0) {
        AppLogger.info('المستودعات موجودة بالفعل، تخطي إنشاء مستودعات تجريبية');
        return;
      }

      // إنشاء مستودعات تجريبية
      final warehouses = [
        {
          'id': const Uuid().v4(),
          'name': 'المستودع الرئيسي',
          'code': 'WH001',
          'address': 'القاهرة، مصر',
          'is_default': 1,
          'is_active': 1,
          'created_at': DateTime.now().toIso8601String(),
        },
        {
          'id': const Uuid().v4(),
          'name': 'مستودع الفرع',
          'code': 'WH002',
          'address': 'الإسكندرية، مصر',
          'is_default': 0,
          'is_active': 1,
          'created_at': DateTime.now().toIso8601String(),
        },
      ];

      // إدخال المستودعات
      for (final warehouse in warehouses) {
        await db.insert('warehouses', warehouse);
      }

      AppLogger.info('تم إنشاء ${warehouses.length} مستودعات تجريبية');
    } catch (e) {
      AppLogger.error('خطأ في إنشاء مستودعات تجريبية: $e');
      rethrow;
    }
  }

  /// إنشاء مبيعات تجريبية
  Future<void> createSampleSales() async {
    // سيتم تنفيذها لاحقاً
    AppLogger.info('تم تخطي إنشاء مبيعات تجريبية (غير مكتمل)');
  }

  /// إنشاء مشتريات تجريبية
  Future<void> createSamplePurchases() async {
    // سيتم تنفيذها لاحقاً
    AppLogger.info('تم تخطي إنشاء مشتريات تجريبية (غير مكتمل)');
  }

  /// الحصول على عدد السجلات في جدول
  Future<int> _getTableCount(String tableName) async {
    try {
      final db = await _db.database;
      final result =
          await db.rawQuery('SELECT COUNT(*) as count FROM $tableName');
      return result.first['count'] as int;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على عدد السجلات في جدول $tableName: $e');
      return 0;
    }
  }
}
