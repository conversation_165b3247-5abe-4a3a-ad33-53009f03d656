/// أنواع الوصول للفروع في النظام
enum BranchAccessType {
  /// الوصول لفرع واحد فقط (الفرع المحدد في ملف المستخدم)
  singleBranch('single_branch', 'فرع واحد فقط'),

  /// الوصول لجميع الفروع
  allBranches('all_branches', 'جميع الفروع'),

  /// الوصول للفرع الرئيسي فقط
  mainBranchOnly('main_branch_only', 'الفرع الرئيسي فقط'),

  /// الوصول لفروع محددة
  specificBranches('specific_branches', 'فروع محددة');

  const BranchAccessType(this.code, this.displayName);

  /// رمز نوع الوصول (يُحفظ في قاعدة البيانات)
  final String code;

  /// الاسم المعروض للمستخدم
  final String displayName;

  /// الحصول على نوع الوصول من الرمز
  static BranchAccessType fromCode(String code) {
    return BranchAccessType.values.firstWhere(
      (type) => type.code == code,
      orElse: () => BranchAccessType.singleBranch, // القيمة الافتراضية
    );
  }

  /// الحصول على جميع أنواع الوصول كقائمة للعرض في القوائم المنسدلة
  static List<Map<String, String>> getDropdownItems() {
    return BranchAccessType.values
        .map((type) => {
              'value': type.code,
              'label': type.displayName,
            })
        .toList();
  }

  /// التحقق من إمكانية الوصول لفرع معين
  bool canAccessBranch(
      String? userBranchId, String targetBranchId, bool isMainBranch) {
    switch (this) {
      case BranchAccessType.singleBranch:
        return userBranchId == targetBranchId;

      case BranchAccessType.allBranches:
        return true;

      case BranchAccessType.mainBranchOnly:
        return isMainBranch;

      case BranchAccessType.specificBranches:
        // سيتم التحقق من الفروع المحددة في منطق منفصل
        return false;
    }
  }

  /// الحصول على وصف مفصل لنوع الوصول
  String get description {
    switch (this) {
      case BranchAccessType.singleBranch:
        return 'يمكن للمستخدم الوصول فقط للفرع المحدد في ملفه الشخصي';

      case BranchAccessType.allBranches:
        return 'يمكن للمستخدم الوصول لجميع الفروع في النظام';

      case BranchAccessType.specificBranches:
        return 'يمكن للمستخدم الوصول لفروع محددة يتم تحديدها بشكل منفصل';

      case BranchAccessType.mainBranchOnly:
        return 'يمكن للمستخدم الوصول للفرع الرئيسي فقط';
    }
  }

  /// التحقق من إمكانية عرض قائمة الفروع في شاشة تسجيل الدخول
  bool get shouldShowBranchSelector {
    switch (this) {
      case BranchAccessType.singleBranch:
      case BranchAccessType.mainBranchOnly:
        return false; // لا نعرض قائمة الفروع

      case BranchAccessType.allBranches:
      case BranchAccessType.specificBranches:
        return true; // نعرض قائمة الفروع
    }
  }

  /// الحصول على الأيقونة المناسبة لنوع الوصول
  String get iconCode {
    switch (this) {
      case BranchAccessType.singleBranch:
        return 'business'; // أيقونة مبنى واحد

      case BranchAccessType.allBranches:
        return 'domain'; // أيقونة مجال/شبكة

      case BranchAccessType.specificBranches:
        return 'account_tree'; // أيقونة شجرة/تفرع

      case BranchAccessType.mainBranchOnly:
        return 'home_work'; // أيقونة المكتب الرئيسي
    }
  }
}
