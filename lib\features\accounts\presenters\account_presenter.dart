import 'package:uuid/uuid.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/providers/base_presenter.dart';

class AccountPresenter extends BaseListPresenter<Map<String, dynamic>> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  List<Map<String, dynamic>> _transactions = [];
  Map<String, dynamic> _accountsTree = {};

  // Getters
  List<Map<String, dynamic>> get accounts => items;
  List<Map<String, dynamic>> get transactions => _transactions;
  Map<String, dynamic> get accountsTree => _accountsTree;

  // تنفيذ الدوال المطلوبة من BaseListPresenter
  @override
  Future<List<Map<String, dynamic>>> loadItemsFromSource() async {
    final db = await _databaseHelper.database;

    // التحقق من عدد الحسابات وإنشاء حسابات افتراضية إذا لزم الأمر
    final countResult =
        await db.rawQuery('SELECT COUNT(*) as count FROM accounts');
    final accountsCount = countResult.first['count'] as int;

    if (accountsCount == 0) {
      AppLogger.info('No accounts found, creating default accounts');
      await createDefaultAccountsChart();
    }

    // الحصول على جميع الحسابات مع معلومات الحساب الأب
    final List<Map<String, dynamic>> accounts = await db.rawQuery('''
      SELECT
        a.*,
        p.name as parent_name,
        p.code as parent_code,
        p.account_type as parent_type,
        p.description as parent_description
      FROM accounts a
      LEFT JOIN accounts p ON a.parent_id = p.id
      WHERE a.is_deleted = 0
      ORDER BY a.account_type, a.code
    ''');

    // تحويل البيانات لتكون أكثر سهولة في الاستخدام
    return accounts.map((account) {
      return {
        'id': account['id'],
        'name': account['name'],
        'code': account['code'],
        'description': account['description'],
        'type': account['account_type'],
        'parent_id': account['parent_id'],
        'parent_name': account['parent_name'],
        'parent_code': account['parent_code'],
        'opening_balance': account['opening_balance'],
        'current_balance': account['current_balance'],
        'is_active': account['is_active'] == 1,
        'created_at': account['created_at'],
        'updated_at': account['updated_at'],
        'created_by': account['created_by'],
        'updated_by': account['updated_by'],
        'is_deleted': account['is_deleted'] == 1,
        'level': (account['code'] as String).split('.').length - 1,
      };
    }).toList();
  }

  @override
  bool matchesSearch(Map<String, dynamic> item, String query) {
    final lowerQuery = query.toLowerCase();
    return (item['name'] as String).toLowerCase().contains(lowerQuery) ||
        (item['code'] as String).toLowerCase().contains(lowerQuery) ||
        ((item['description'] as String?) ?? '')
            .toLowerCase()
            .contains(lowerQuery);
  }

  // Initialize the presenter
  @override
  Future<void> init() async {
    await loadItems();
    _buildAccountsTree();
  }

  // تحميل جميع الحسابات (للتوافق مع الكود القديم)
  Future<void> loadAccounts() async {
    await loadItems();
    _buildAccountsTree();
  }

  // بناء شجرة الحسابات
  void _buildAccountsTree() {
    final Map<String, dynamic> tree = <String, dynamic>{};

    // إضافة الحسابات الرئيسية أولاً
    for (final account in items) {
      if (account['parent_id'] == null) {
        tree[account['id'].toString()] = {
          'account': account,
          'children': <String, dynamic>{},
          'level': 0,
        };
      }
    }

    // إضافة الحسابات الفرعية
    bool hasChanges = true;
    int maxIterations = 10;
    int iteration = 0;

    List<Map<String, dynamic>> remainingAccounts =
        items.where((account) => account['parent_id'] != null).toList();

    while (hasChanges &&
        iteration < maxIterations &&
        remainingAccounts.isNotEmpty) {
      hasChanges = false;
      iteration++;

      List<Map<String, dynamic>> stillRemainingAccounts = [];

      for (final account in remainingAccounts) {
        final parentId = account['parent_id'].toString();
        bool added = _addChildToParent(tree, parentId, account);

        if (added) {
          hasChanges = true;
        } else {
          stillRemainingAccounts.add(account);
        }
      }

      remainingAccounts = stillRemainingAccounts;
    }

    _accountsTree = tree;
  }

  // إضافة حساب فرعي إلى الحساب الأب
  bool _addChildToParent(Map<String, dynamic> tree, String parentId,
      Map<String, dynamic> childAccount) {
    if (tree.containsKey(parentId)) {
      tree[parentId]['children'][childAccount['id'].toString()] = {
        'account': childAccount,
        'children': <String, dynamic>{},
        'level': (tree[parentId]['level'] as int) + 1,
      };
      return true;
    }

    for (final node in tree.values) {
      if (node['children'] is Map) {
        bool added = _addChildToParent(
            node['children'] as Map<String, dynamic>, parentId, childAccount);
        if (added) return true;
      }
    }

    return false;
  }

  // Load transactions for a specific account
  Future<void> loadTransactionsForAccount(int accountId) async {
    setLoading(true);

    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> transactions = await db.rawQuery('''
        SELECT t.*,
               a1.name as account_name,
               a2.name as related_account_name,
               u.username as user_name,
               b.name as branch_name
        FROM transactions t
        LEFT JOIN accounts a1 ON t.account_id = a1.id
        LEFT JOIN accounts a2 ON t.related_account_id = a2.id
        LEFT JOIN users u ON t.user_id = u.id
        LEFT JOIN branches b ON t.branch_id = b.id
        WHERE t.account_id = ? OR t.related_account_id = ?
        ORDER BY t.transaction_date DESC
      ''', [accountId, accountId]);

      _transactions = transactions;
      setLoading(false);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to load transactions',
        error: e,
        stackTrace: stackTrace,
        context: {'presenter': 'AccountPresenter', 'accountId': accountId},
      );

      setErrorMessage('Failed to load transactions: ${e.toString()}');
      setLoading(false);
    }
  }

  // إضافة حساب جديد
  Future<bool> addAccount(Map<String, dynamic> accountData) async {
    setLoading(true);

    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now().toIso8601String();

      final Map<String, dynamic> dataToInsert = {
        'id': accountData['id'] ?? const Uuid().v4(),
        'name': accountData['name'],
        'code': accountData['code'],
        'description': accountData['description'],
        'account_type': accountData['type'],
        'type': accountData['type'],
        'parent_id': accountData['parent_id'],
        'opening_balance': accountData['opening_balance'] ?? 0.0,
        'current_balance': accountData['current_balance'] ??
            accountData['opening_balance'] ??
            0.0,
        'is_active': accountData['is_active'] == true ? 1 : 0,
        'created_at': now,
        'created_by': accountData['created_by'] ?? 'current_user',
        'is_deleted': 0,
      };

      await db.insert('accounts', dataToInsert);
      await loadAccounts();

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة حساب',
        error: e,
        stackTrace: stackTrace,
        context: {'presenter': 'AccountPresenter', 'accountData': accountData},
      );

      setErrorMessage('فشل في إضافة حساب: ${e.toString()}');
      setLoading(false);
      notifyListeners();
      return false;
    }
  }

  // إنشاء مخطط حسابات افتراضي
  Future<bool> createDefaultAccountsChart() async {
    try {
      // سيتم تنفيذ هذه الدالة لاحقًا
      return true;
    } catch (e) {
      setErrorMessage('فشل في إنشاء مخطط الحسابات الافتراضي: ${e.toString()}');
      return false;
    }
  }

  // تحميل شجرة الحسابات
  Future<void> loadAccountsChart() async {
    await loadAccounts();
  }

  // حذف حساب (حذف منطقي فقط)
  Future<bool> deleteAccount(String accountId) async {
    setLoading(true);

    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now().toIso8601String();

      // التحقق من وجود حسابات فرعية
      final List<Map<String, dynamic>> childAccounts = await db.query(
        'accounts',
        where: 'parent_id = ? AND is_deleted = 0',
        whereArgs: [accountId],
      );

      if (childAccounts.isNotEmpty) {
        setErrorMessage(
            'لا يمكن حذف الحساب لأنه يحتوي على حسابات فرعية. يجب حذف الحسابات الفرعية أولاً.');
        setLoading(false);
        notifyListeners();
        return false;
      }

      // التحقق من وجود معاملات مرتبطة بالحساب
      final List<Map<String, dynamic>> transactions = await db.query(
        'transactions',
        where: 'account_id = ? OR related_account_id = ?',
        whereArgs: [accountId, accountId],
        limit: 1,
      );

      if (transactions.isNotEmpty) {
        setErrorMessage(
            'لا يمكن حذف الحساب لأنه يحتوي على معاملات. يجب حذف المعاملات أولاً أو استخدام الحذف المنطقي.');
        setLoading(false);
        notifyListeners();
        return false;
      }

      // تنفيذ الحذف المنطقي (تحديث حقل is_deleted)
      await db.update(
        'accounts',
        {
          'is_deleted': 1,
          'updated_at': now,
          'updated_by': 'current_user',
        },
        where: 'id = ?',
        whereArgs: [accountId],
      );

      // إعادة تحميل الحسابات لتحديث القائمة
      await loadAccounts();

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف الحساب',
        error: e,
        stackTrace: stackTrace,
        context: {
          'presenter': 'AccountPresenter',
          'accountId': accountId,
        },
      );

      setErrorMessage('فشل في حذف الحساب: ${e.toString()}');
      setLoading(false);
      notifyListeners();
      return false;
    }
  }

  // تحديث حالة الحساب (نشط/غير نشط)
  Future<bool> updateAccountStatus(String accountId, bool isActive) async {
    setLoading(true);

    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now().toIso8601String();

      await db.update(
        'accounts',
        {
          'is_active': isActive ? 1 : 0,
          'updated_at': now,
          'updated_by': 'current_user',
        },
        where: 'id = ?',
        whereArgs: [accountId],
      );

      // إعادة تحميل الحسابات لتحديث القائمة
      await loadAccounts();

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث حالة الحساب',
        error: e,
        stackTrace: stackTrace,
        context: {
          'presenter': 'AccountPresenter',
          'accountId': accountId,
          'isActive': isActive,
        },
      );

      setErrorMessage('فشل في تحديث حالة الحساب: ${e.toString()}');
      setLoading(false);
      notifyListeners();
      return false;
    }
  }

  // الحصول على ملخص مالي
  Future<Map<String, dynamic>> getFinancialSummary() async {
    setLoading(true);

    try {
      final db = await _databaseHelper.database;

      // حساب إجمالي الأصول
      final assetsResult = await db.rawQuery('''
        SELECT SUM(current_balance) as total
        FROM accounts
        WHERE account_type = 'asset' AND is_deleted = 0
      ''');
      final double totalAssets =
          (assetsResult.first['total'] as num?)?.toDouble() ?? 0.0;

      // حساب إجمالي الخصوم
      final liabilitiesResult = await db.rawQuery('''
        SELECT SUM(current_balance) as total
        FROM accounts
        WHERE account_type = 'liability' AND is_deleted = 0
      ''');
      final double totalLiabilities =
          (liabilitiesResult.first['total'] as num?)?.toDouble() ?? 0.0;

      // حساب إجمالي حقوق الملكية
      final equityResult = await db.rawQuery('''
        SELECT SUM(current_balance) as total
        FROM accounts
        WHERE account_type = 'equity' AND is_deleted = 0
      ''');
      final double totalEquity =
          (equityResult.first['total'] as num?)?.toDouble() ?? 0.0;

      // حساب إجمالي الإيرادات
      final revenueResult = await db.rawQuery('''
        SELECT SUM(current_balance) as total
        FROM accounts
        WHERE account_type = 'revenue' AND is_deleted = 0
      ''');
      final double totalRevenue =
          (revenueResult.first['total'] as num?)?.toDouble() ?? 0.0;

      // حساب إجمالي المصروفات
      final expenseResult = await db.rawQuery('''
        SELECT SUM(current_balance) as total
        FROM accounts
        WHERE account_type = 'expense' AND is_deleted = 0
      ''');
      final double totalExpense =
          (expenseResult.first['total'] as num?)?.toDouble() ?? 0.0;

      // حساب صافي الربح
      final double netIncome = totalRevenue - totalExpense;

      setLoading(false);

      return {
        'totalAssets': totalAssets,
        'totalLiabilities': totalLiabilities,
        'totalEquity': totalEquity,
        'totalRevenue': totalRevenue,
        'totalExpense': totalExpense,
        'netIncome': netIncome,
      };
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على الملخص المالي',
        error: e,
        stackTrace: stackTrace,
        context: {'presenter': 'AccountPresenter'},
      );

      setErrorMessage('فشل في الحصول على الملخص المالي: ${e.toString()}');
      setLoading(false);
      notifyListeners();

      return {
        'totalAssets': 0.0,
        'totalLiabilities': 0.0,
        'totalEquity': 0.0,
        'totalRevenue': 0.0,
        'totalExpense': 0.0,
        'netIncome': 0.0,
      };
    }
  }
}
