import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../../../core/theme/app_dimensions.dart';
import '../services/auth_service.dart';

/// أدوات مساعدة للتحقق من الصلاحيات
class PermissionUtils {
  static final AuthService _authService = AuthService();

  /// التحقق من صلاحية واحدة مع عرض رسالة خطأ اختيارية
  static Future<bool> checkPermission({
    required BuildContext context,
    required String permission,
    String? noPermissionMessage,
    bool showDialog = true,
  }) async {
    final hasPermission = await _authService.hasPermission(permission);

    if (!hasPermission && showDialog && context.mounted) {
      _showNoPermissionDialog(
        context,
        noPermissionMessage ?? 'ليس لديك صلاحية لتنفيذ هذه العملية',
        permission,
      );
    }

    return hasPermission;
  }

  /// التحقق من صلاحيات متعددة (يجب توفر جميعها)
  static Future<bool> checkAllPermissions({
    required BuildContext context,
    required List<String> permissions,
    String? noPermissionMessage,
    bool showDialog = true,
  }) async {
    final hasPermissions = await _authService.hasAllPermissions(permissions);

    if (!hasPermissions && showDialog && context.mounted) {
      _showNoPermissionDialog(
        context,
        noPermissionMessage ?? 'ليس لديك الصلاحيات المطلوبة لتنفيذ هذه العملية',
        permissions.join(', '),
      );
    }

    return hasPermissions;
  }

  /// التحقق من صلاحيات متعددة (يكفي توفر واحدة منها)
  static Future<bool> checkAnyPermission({
    required BuildContext context,
    required List<String> permissions,
    String? noPermissionMessage,
    bool showDialog = true,
  }) async {
    final hasPermission = await _authService.hasAnyPermission(permissions);

    if (!hasPermission && showDialog && context.mounted) {
      _showNoPermissionDialog(
        context,
        noPermissionMessage ??
            'ليس لديك أي من الصلاحيات المطلوبة لتنفيذ هذه العملية',
        permissions.join(', '),
      );
    }

    return hasPermission;
  }

  /// التحقق من صلاحية CRUD
  static Future<bool> checkCRUDPermission({
    required BuildContext context,
    required String module,
    required String operation,
    String? noPermissionMessage,
    bool showDialog = true,
  }) async {
    final hasPermission = await _authService.canPerformCRUD(module, operation);

    if (!hasPermission && showDialog && context.mounted) {
      final operationName = _getOperationName(operation);
      _showNoPermissionDialog(
        context,
        noPermissionMessage ?? 'ليس لديك صلاحية $operationName في $module',
        '${operation}_$module',
      );
    }

    return hasPermission;
  }

  /// التحقق من صلاحية الوصول لوحدة
  static Future<bool> checkModuleAccess({
    required BuildContext context,
    required String module,
    String? noPermissionMessage,
    bool showDialog = true,
  }) async {
    final hasAccess = await _authService.canAccessModule(module);

    if (!hasAccess && showDialog && context.mounted) {
      _showNoPermissionDialog(
        context,
        noPermissionMessage ?? 'ليس لديك صلاحية للوصول إلى وحدة $module',
        'view_$module',
      );
    }

    return hasAccess;
  }

  /// تنفيذ عملية مع التحقق من الصلاحية
  static Future<void> executeWithPermission({
    required BuildContext context,
    required String permission,
    required VoidCallback onExecute,
    String? noPermissionMessage,
    bool showDialog = true,
  }) async {
    final hasPermission = await checkPermission(
      context: context,
      permission: permission,
      noPermissionMessage: noPermissionMessage,
      showDialog: showDialog,
    );

    if (hasPermission) {
      onExecute();
    }
  }

  /// تنفيذ عملية CRUD مع التحقق من الصلاحية
  static Future<void> executeCRUDWithPermission({
    required BuildContext context,
    required String module,
    required String operation,
    required VoidCallback onExecute,
    String? noPermissionMessage,
    bool showDialog = true,
  }) async {
    final hasPermission = await checkCRUDPermission(
      context: context,
      module: module,
      operation: operation,
      noPermissionMessage: noPermissionMessage,
      showDialog: showDialog,
    );

    if (hasPermission) {
      onExecute();
    }
  }

  /// عرض رسالة Snackbar لعدم وجود صلاحية
  static void showNoPermissionSnackbar({
    required BuildContext context,
    String? message,
    String? permission,
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.lock_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message ?? 'ليس لديك صلاحية لتنفيذ هذه العملية',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض مربع حوار عدم وجود صلاحية
  static void _showNoPermissionDialog(
    BuildContext context,
    String message,
    String permission,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(
              Icons.lock_outline,
              color: AppColors.warning,
              size: 24,
            ),
            SizedBox(width: AppDimensions.spacing8),
            Text('غير مصرح'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            const SizedBox(height: AppDimensions.spacing16),
            Container(
              padding: const EdgeInsets.all(AppDimensions.spacing12),
              decoration: BoxDecoration(
                color: AppColors.lightSurface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.lightBorder,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    size: 16,
                    color: AppColors.lightTextSecondary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'الصلاحية المطلوبة: $permission',
                      style: const AppTypography(
                        color: AppColors.lightTextSecondary,
                        fontSize: AppTypography.fontSizeSmall,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم العملية بالعربية
  static String _getOperationName(String operation) {
    switch (operation.toLowerCase()) {
      case 'create':
      case 'add':
        return 'الإضافة';
      case 'read':
      case 'view':
        return 'العرض';
      case 'update':
      case 'edit':
        return 'التعديل';
      case 'delete':
        return 'الحذف';
      default:
        return operation;
    }
  }

  /// التحقق من صلاحية بدون عرض رسائل (للاستخدام في الواجهات)
  static Future<bool> hasPermissionSilent(String permission) async {
    return await _authService.hasPermission(permission);
  }

  /// التحقق من صلاحيات متعددة بدون عرض رسائل
  static Future<bool> hasAllPermissionsSilent(List<String> permissions) async {
    return await _authService.hasAllPermissions(permissions);
  }

  /// التحقق من صلاحيات بديلة بدون عرض رسائل
  static Future<bool> hasAnyPermissionSilent(List<String> permissions) async {
    return await _authService.hasAnyPermission(permissions);
  }

  /// الحصول على جميع صلاحيات المستخدم الحالي
  static Future<List<String>> getCurrentUserPermissions() async {
    return await _authService.getCurrentUserPermissions();
  }
}
