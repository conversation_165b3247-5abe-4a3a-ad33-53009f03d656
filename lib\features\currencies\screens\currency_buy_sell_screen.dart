import 'package:flutter/material.dart';
import '../../../core/utils/index.dart';

import '../../../core/widgets/index.dart';
import '../presenters/currency_presenter.dart';
import '../../../core/widgets/data_table_widget.dart';
import '../../../core/theme/index.dart';
import '../../../core/providers/app_providers.dart';

class CurrencyBuySellScreen extends StatefulWidget {
  final bool isBuy; // true للشراء، false للبيع

  const CurrencyBuySellScreen({Key? key, required this.isBuy})
      : super(key: key);

  @override
  State<CurrencyBuySellScreen> createState() => _CurrencyBuySellScreenState();
}

class _CurrencyBuySellScreenState extends State<CurrencyBuySellScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _exchangeRateController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  String? _selectedFromCurrencyId;
  String? _selectedToCurrencyId;
  double _calculatedAmount = 0.0;
  bool _isLoading = false;
  bool _isSaving = false;
  List<Map<String, dynamic>> _transactions = [];
  bool _showSearchField = false;

  // استخدام التحميل الكسول
  late final CurrencyPresenter _currencyPresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _currencyPresenter = AppProviders.getLazyPresenter<CurrencyPresenter>(
        () => CurrencyPresenter());
    // Usar addPostFrameCallback para evitar llamar a setState durante la construcción
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
    _amountController.addListener(_calculateAmount);
    _exchangeRateController.addListener(_calculateAmount);
  }

  void _calculateAmount() {
    if (_amountController.text.isNotEmpty &&
        _exchangeRateController.text.isNotEmpty) {
      try {
        // Eliminar cualquier formato (comas, espacios) antes de parsear
        final String cleanAmount =
            _amountController.text.replaceAll(RegExp(r'[^\d.]'), '');
        final String cleanRate =
            _exchangeRateController.text.replaceAll(RegExp(r'[^\d.]'), '');

        final amount = double.parse(cleanAmount);
        final rate = double.parse(cleanRate);
        setState(() {
          _calculatedAmount = amount * rate;
        });
      } catch (e) {
        setState(() {
          _calculatedAmount = 0.0;
        });
      }
    } else {
      setState(() {
        _calculatedAmount = 0.0;
      });
    }
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل العملات
      await _currencyPresenter.loadCurrencies();

      if (!mounted) return;

      // تعيين العملة الافتراضية
      if (_currencyPresenter.defaultCurrency != null) {
        setState(() {
          if (widget.isBuy) {
            // في حالة الشراء، العملة الافتراضية هي العملة المحلية التي سندفع بها
            _selectedToCurrencyId = _currencyPresenter.defaultCurrency!.id;
          } else {
            // في حالة البيع، العملة الافتراضية هي العملة المحلية التي سنستلم بها
            _selectedFromCurrencyId = _currencyPresenter.defaultCurrency!.id;
          }
        });
      }

      // تحميل المعاملات السابقة
      _loadTransactions();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل البيانات: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _loadTransactions() {
    // هذه دالة وهمية لتحميل المعاملات السابقة
    // في التطبيق الحقيقي، يجب استرداد هذه البيانات من قاعدة البيانات
    setState(() {
      _transactions = [
        {
          'id': '1',
          'date': DateTime.now().subtract(const Duration(days: 1)),
          'fromCurrency': 'دولار أمريكي',
          'toCurrency': 'ريال يمني',
          'amount': 100.0,
          'rate': 500.0,
          'total': 50000.0,
          'type': widget.isBuy ? 'شراء' : 'بيع',
        },
        {
          'id': '2',
          'date': DateTime.now().subtract(const Duration(days: 2)),
          'fromCurrency': 'يورو',
          'toCurrency': 'ريال يمني',
          'amount': 50.0,
          'rate': 550.0,
          'total': 27500.0,
          'type': widget.isBuy ? 'شراء' : 'بيع',
        },
      ];
    });
  }

  Future<void> _saveCurrencyTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من اختيار العملات
    if (_selectedFromCurrencyId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار العملة المصدر')),
      );
      return;
    }

    if (_selectedToCurrencyId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار العملة الهدف')),
      );
      return;
    }

    // استخدام التحميل الكسول

    // عرض مربع حوار التأكيد
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد ${widget.isBuy ? 'الشراء' : 'البيع'}'),
        content: Text('هل تريد ${widget.isBuy ? 'شراء' : 'بيع'} العملة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    if (confirm != true || !mounted) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // الحصول على العملات المحددة
      final fromCurrency = _currencyPresenter.currencies
          .firstWhere((c) => c.id == _selectedFromCurrencyId);
      final toCurrency = _currencyPresenter.currencies
          .firstWhere((c) => c.id == _selectedToCurrencyId);

      // إضافة المعاملة إلى القائمة (هذا مجرد مثال، يجب حفظها في قاعدة البيانات)
      final newTransaction = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'date': DateTime.now(),
        'fromCurrency': fromCurrency.name,
        'toCurrency': toCurrency.name,
        'amount': double.parse(_amountController.text),
        'rate': double.parse(_exchangeRateController.text),
        'total': _calculatedAmount,
        'type': widget.isBuy ? 'شراء' : 'بيع',
      };

      setState(() {
        _transactions.insert(0, newTransaction);
      });

      // إعادة تعيين النموذج
      _resetForm();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content:
                  Text('تم ${widget.isBuy ? 'شراء' : 'بيع'} العملة بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  'فشل في ${widget.isBuy ? 'شراء' : 'بيع'} العملة: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _resetForm() {
    _amountController.clear();
    _exchangeRateController.clear();
    _notesController.clear();
    setState(() {
      _calculatedAmount = 0.0;
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _exchangeRateController.dispose();
    _notesController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _currencyPresenter,
      builder: (context, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(widget.isBuy ? 'شراء العملات' : 'بيع العملات'),
            backgroundColor: Theme.of(context).colorScheme.inversePrimary,
            actions: [
              // أيقونة البحث
              IconButton(
                icon: Icon(_showSearchField ? Icons.close : Icons.search),
                onPressed: () {
                  setState(() {
                    _showSearchField = !_showSearchField;
                    if (!_showSearchField) {
                      _searchController.clear();
                    }
                  });
                },
              ),
            ],
          ),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث)
                    if (_showSearchField)
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: TextField(
                          controller: _searchController,
                          decoration: const InputDecoration(
                            hintText: 'بحث عن معاملة...',
                            prefixIcon: Icon(Icons.search),
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            // تنفيذ البحث
                          },
                        ),
                      ),
                    // نموذج إضافة معاملة عملة
                    Expanded(
                      flex: 1,
                      child: SingleChildScrollView(
                        padding: EdgeInsets.zero,
                        child: Card(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 4.0, vertical: 4.0),
                            child: Form(
                              key: _formKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // العملة المصدر
                                  DropdownButtonFormField<String>(
                                    value: _selectedFromCurrencyId,
                                    decoration: InputDecoration(
                                      labelText: widget.isBuy
                                          ? 'العملة المشتراة *'
                                          : 'العملة المباعة *',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(
                                            Layout.defaultRadius),
                                      ),
                                      filled: true,
                                      prefixIcon:
                                          const Icon(Icons.currency_exchange),
                                    ),
                                    items: _currencyPresenter.currencies
                                        .map((currency) {
                                      return DropdownMenuItem<String>(
                                        value: currency.id,
                                        child: Text(
                                            '${currency.name} (${currency.symbol})'),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedFromCurrencyId = value;
                                      });
                                    },
                                    validator: (value) {
                                      if (value == null) {
                                        return 'يرجى اختيار العملة';
                                      }
                                      return null;
                                    },
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // العملة الهدف
                                  DropdownButtonFormField<String>(
                                    value: _selectedToCurrencyId,
                                    decoration: InputDecoration(
                                      labelText: widget.isBuy
                                          ? 'العملة المدفوعة *'
                                          : 'العملة المستلمة *',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(
                                            Layout.defaultRadius),
                                      ),
                                      filled: true,
                                      prefixIcon:
                                          const Icon(Icons.currency_exchange),
                                    ),
                                    items: _currencyPresenter.currencies
                                        .map((currency) {
                                      return DropdownMenuItem<String>(
                                        value: currency.id,
                                        child: Text(
                                            '${currency.name} (${currency.symbol})'),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedToCurrencyId = value;
                                      });
                                    },
                                    validator: (value) {
                                      if (value == null) {
                                        return 'يرجى اختيار العملة';
                                      }
                                      return null;
                                    },
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // المبلغ وسعر الصرف
                                  Row(
                                    children: [
                                      // المبلغ
                                      Expanded(
                                        flex: 1,
                                        child: FinancialTextField(
                                          controller: _amountController,
                                          label: 'المبلغ',
                                          hint: 'أدخل المبلغ',
                                          isRequired: true,
                                          prefixIcon: Icons.attach_money,
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'يرجى إدخال المبلغ';
                                            }
                                            if (double.tryParse(
                                                    value.replaceAll(
                                                        RegExp(r'[^\d.]'),
                                                        '')) ==
                                                null) {
                                              return 'يرجى إدخال رقم صحيح';
                                            }
                                            if (double.parse(value.replaceAll(
                                                    RegExp(r'[^\d.]'), '')) <=
                                                0) {
                                              return 'يجب أن يكون المبلغ أكبر من صفر';
                                            }
                                            return null;
                                          },
                                          onChanged: (_) => _calculateAmount(),
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      // سعر الصرف
                                      Expanded(
                                        flex: 1,
                                        child: FinancialTextField(
                                          controller: _exchangeRateController,
                                          label: 'سعر الصرف',
                                          hint: 'أدخل سعر الصرف',
                                          isRequired: true,
                                          decimalPlaces: 6,
                                          prefixIcon: Icons.currency_exchange,
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'يرجى إدخال سعر الصرف';
                                            }
                                            if (double.tryParse(
                                                    value.replaceAll(
                                                        RegExp(r'[^\d.]'),
                                                        '')) ==
                                                null) {
                                              return 'يرجى إدخال رقم صحيح';
                                            }
                                            if (double.parse(value.replaceAll(
                                                    RegExp(r'[^\d.]'), '')) <=
                                                0) {
                                              return 'يجب أن يكون سعر الصرف أكبر من صفر';
                                            }
                                            return null;
                                          },
                                          onChanged: (_) => _calculateAmount(),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // المبلغ المحسوب
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context)
                                          .primaryColor
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(
                                          Layout.defaultRadius),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        const Text('المبلغ الإجمالي:'),
                                        Text(
                                          NumberFormatter.formatNumber(
                                              _calculatedAmount,
                                              decimalPlaces: 2),
                                          style: const AppTypography(
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.lightTextSecondary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // الملاحظات
                                  TextFormField(
                                    controller: _notesController,
                                    decoration: const InputDecoration(
                                      labelText: 'ملاحظات',
                                      hintText: 'أدخل ملاحظات إضافية (اختياري)',
                                      border: OutlineInputBorder(),
                                      prefixIcon: Icon(Icons.note),
                                    ),
                                    maxLines: 2,
                                  ),
                                  const SizedBox(
                                      height: AppDimensions.spacing4),

                                  // زر الحفظ
                                  Center(
                                    child: SizedBox(
                                      width: Layout.isTablet()
                                          ? 200
                                          : double.infinity,
                                      height: 45,
                                      child: ElevatedButton.icon(
                                        onPressed: _isSaving
                                            ? null
                                            : _saveCurrencyTransaction,
                                        icon: _isSaving
                                            ? const SizedBox(
                                                width: 18,
                                                height: 18,
                                                child:
                                                    CircularProgressIndicator(
                                                        color: AppColors
                                                            .lightTextSecondary,
                                                        strokeWidth: 2))
                                            : const Icon(Icons.save, size: 20),
                                        label: Text(
                                          widget.isBuy ? 'شراء' : 'بيع',
                                          style:
                                              const AppTypography(fontSize: 15),
                                        ),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: AppColors.primary,
                                          foregroundColor: AppColors.onPrimary,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    // جدول المعاملات
                    Expanded(
                      flex: 1,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4.0, vertical: 4.0),
                        child: Card(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 4.0, vertical: 4.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 4.0, vertical: 2.0),
                                  child: Text(
                                    'سجل معاملات ${widget.isBuy ? 'شراء' : 'بيع'} العملات',
                                    style:
                                        Theme.of(context).textTheme.titleMedium,
                                  ),
                                ),
                                Expanded(
                                  child: AdvancedDataTable(
                                    columns: const [
                                      DataColumn(label: Text('م')),
                                      DataColumn(label: Text('التاريخ')),
                                      DataColumn(label: Text('من')),
                                      DataColumn(label: Text('إلى')),
                                      DataColumn(label: Text('المبلغ')),
                                      DataColumn(label: Text('السعر')),
                                      DataColumn(label: Text('الإجمالي')),
                                    ],
                                    rows: _transactions
                                        .asMap()
                                        .entries
                                        .map((entry) {
                                      final transaction = entry.value;
                                      return DataRow(
                                        cells: [
                                          DataCell(Text('${entry.key + 1}')),
                                          DataCell(Text(
                                              '${(transaction['date'] as DateTime).year}-${(transaction['date'] as DateTime).month.toString().padLeft(2, '0')}-${(transaction['date'] as DateTime).day.toString().padLeft(2, '0')}')),
                                          DataCell(Text(
                                              transaction['fromCurrency']
                                                  as String)),
                                          DataCell(Text(
                                              transaction['toCurrency']
                                                  as String)),
                                          DataCell(Text(
                                              (transaction['amount'] as double)
                                                  .toStringAsFixed(2))),
                                          DataCell(Text(
                                              (transaction['rate'] as double)
                                                  .toStringAsFixed(2))),
                                          DataCell(Text(
                                              (transaction['total'] as double)
                                                  .toStringAsFixed(2))),
                                        ],
                                      );
                                    }).toList(),
                                    isLoading: false,
                                    showCellBorder: true,
                                    zebraPattern: true,
                                    headerBackgroundColor: AppColors.primary,
                                    headerTextColor: AppColors.onPrimary,
                                    showRowNumbers: false,
                                    emptyMessage: 'لا توجد معاملات',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
        );
      },
    );
  }
}
