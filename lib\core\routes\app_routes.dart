import 'package:flutter/material.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/settings/screens/legal_screen.dart';
import '../../features/settings/screens/theme_settings_screen.dart';
import '../../features/splash/screens/splash_screen.dart';
import '../../features/products/screens/products_screen.dart';
import '../../features/products/screens/product_form_screen.dart';
import '../../features/products/screens/product_opening_balance_screen.dart';
import '../../features/customers/screens/customers_screen.dart';
import '../../features/customers/screens/customer_form_screen.dart';
import '../../features/suppliers/screens/suppliers_screen.dart';
import '../../features/suppliers/screens/supplier_form_screen.dart';
import '../../features/dashboard/screens/dashboard_screen.dart';
import '../../features/sales/screens/sales_screen.dart';
import '../../features/sales/screens/sale_form_screen.dart';
import '../../features/sales/screens/sale_details_screen.dart';
import '../../features/settings/screens/settings_screen.dart';
import '../../features/settings/screens/printer_settings_screen.dart';
import '../../features/settings/screens/company_profile_screen.dart';
import '../../features/users/screens/users_screen.dart';
import '../../features/users/screens/user_form_screen.dart';
import '../../features/users/screens/user_groups_screen.dart';
import '../../features/users/screens/user_group_form_screen.dart';
import '../../features/users/screens/roles_management_screen.dart';
import '../../features/users/screens/activity_log_screen.dart';
import '../../features/users/screens/user_role_assignment_screen.dart';

import '../../features/users/models/user_group.dart';
import '../../features/accounts/screens/accounts_screen.dart';
import '../../features/accounts/screens/financial_reports_screen.dart';
import '../../features/accounts/screens/account_opening_balance_screen.dart';
import '../../features/accounts/screens/chart_of_accounts_screen.dart';
import '../../features/accounts/screens/journal_entries_screen.dart';
import '../../features/accounts/screens/journal_entry_form_screen.dart';
import '../../features/accounts/screens/journal_entry_posting_screen.dart';
import '../../features/accounts/screens/transactions_screen.dart';
import '../../features/accounts/screens/transaction_form_screen.dart';
import '../../features/setup/screens/initial_setup_screen.dart';
import '../../features/warehouses/screens/warehouse_home_screen.dart';
import '../../features/warehouses/screens/warehouse_management_screen.dart';

// Importaciones de modelos
import '../../core/models/product.dart';
import '../../core/models/customer.dart';
import '../../core/models/supplier.dart';
import '../../core/models/sale.dart';
import '../../features/accounts/models/voucher.dart';
import '../../features/users/models/user.dart';
import '../../features/warehouses/screens/inventory_management_screen.dart';
import '../../features/warehouses/screens/inventory_report_screen.dart';
import '../../features/warehouses/screens/inventory_alerts_screen.dart';
import '../../features/warehouses/screens/inventory_dashboard_screen.dart';
import '../../features/warehouses/screens/inventory_count_screen.dart';
import '../../features/suppliers/screens/supplier_management_screen.dart';
// تم حذف customer_management_screen.dart - مكررة
import '../../features/products/screens/product_management_screen.dart';
import '../../features/pos/screens/pos_screen.dart';
import '../../features/sales/screens/sales_management_screen.dart';
import '../../features/vouchers/screens/vouchers_screen.dart';
import '../../features/vouchers/screens/receipt_voucher_screen.dart';
import '../../features/vouchers/screens/payment_voucher_screen.dart';
import '../../features/vouchers/screens/journal_voucher_screen.dart';
import '../../features/currencies/screens/currency_exchange_screen.dart';
import '../../features/currencies/screens/currency_management_screen.dart';
import '../../features/branches/screens/branches_screen.dart';
import '../../features/settings/screens/export_import_screen.dart';
import '../../features/simple_ties/screens/simple_tie_screen.dart';
import '../../features/expenses/screens/expense_screen.dart';
import '../../features/expenses/screens/expense_category_screen.dart';
import '../../features/expenses/screens/expense_form_screen.dart';
import '../../features/settings/screens/sample_data_screen.dart';
import '../../features/reports/screens/reports_screen.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String home = '/home';
  static const String initialSetup = '/initial-setup';
  static const String products = '/products';
  static const String productForm = '/product-form';
  static const String customers = '/customers';
  static const String customerForm = '/customer-form';
  static const String suppliers = '/suppliers';
  static const String supplierForm = '/supplier-form';
  static const String sales = '/sales';
  static const String saleForm = '/sale-form';
  static const String saleDetails = '/sale-details';
  static const String settings = '/settings';
  static const String accounts = '/accounts';
  static const String accountForm = '/account-form';
  static const String transactions = '/transactions';
  static const String transactionForm = '/transaction-form';
  static const String financialReports = '/financial-reports';
  static const String integratedFinancialReports =
      '/integrated-financial-reports';
  static const String productOpeningBalance = '/product-opening-balance';
  static const String accountOpeningBalance = '/account-opening-balance';
  static const String enhancedChartOfAccounts = '/enhanced-chart-of-accounts';
  static const String chartOfAccounts = '/chart-of-accounts';
  static const String journalEntries = '/journal-entries';
  static const String journalEntryForm = '/journal-entry-form';
  static const String enhancedJournalEntryForm = '/enhanced-journal-entry-form';
  static const String enhancedFinancialReports = '/enhanced-financial-reports';
  static const String journalEntryPosting = '/journal-entry-posting';
  static const String homeScreen = '/home-screen';
  static const String warehouseHome = '/warehouse-home';
  static const String warehouseManagement = '/warehouse-management';
  static const String inventoryManagement = '/inventory-management';
  static const String inventoryReport = '/inventory-report';
  static const String inventoryAlerts = '/inventory-alerts';
  static const String inventoryDashboard = '/inventory-dashboard';
  static const String inventoryCount = '/inventory-count';

  // مسارات الموردين
  static const String supplierManagement = '/supplier-management';

  // مسارات العملاء
  static const String customerManagement = '/customer-management';

  // مسارات المنتجات
  static const String productManagement = '/product-management';

  // مسارات المبيعات
  static const String pos = '/pos';
  static const String salesManagement = '/sales-management';

  static const String printerSettings = '/printer-settings';
  static const String companyProfile = '/company-profile';
  static const String users = '/users';
  static const String userForm = '/user-form';
  static const String userGroups = '/user-groups';
  static const String userGroupForm = '/user-group-form';
  static const String vouchers = '/vouchers';
  static const String receiptVoucher = '/receipt-voucher';
  static const String paymentVoucher = '/payment-voucher';
  static const String journalVoucher = '/journal-voucher';
  static const String currencyExchange = '/currency-exchange';
  static const String currencyManagement = '/currency-management';
  static const String branches = '/branches';
  static const String dashboard = '/dashboard';
  static const String exportImport = '/export-import';
  static const String simpleTie = '/simple-tie';

  // مسارات المصروفات
  static const String expenses = '/expenses';
  static const String expenseCategories = '/expense-categories';
  static const String expenseForm = '/expense-form';

  // مسارات المستخدمين والصلاحيات
  static const String usersManagement = '/users-management';
  static const String rolesManagement = '/roles-management';
  static const String activityLog = '/activity-log';
  static const String userRoleAssignment = '/user-role-assignment';

  // مسارات الإعدادات الإضافية
  static const String sampleData = '/sample-data';

  // مسارات الشروط والخصوصية
  static const String legal = '/legal';

  // مسارات التقارير
  static const String reports = '/reports';

  // مسارات إعدادات الثيم
  static const String themeSettings = '/theme_settings';

  // Route map
  static Map<String, WidgetBuilder> get routes {
    return {
      initialSetup: (context) => const InitialSetupScreen(),
      splash: (context) => const SplashScreen(),
      login: (context) => const LoginScreen(),
      register: (context) => const RegisterScreen(),
      homeScreen: (context) => const DashboardScreen(),
      products: (context) => const ProductsScreen(),
      customers: (context) => const CustomersScreen(),
      suppliers: (context) => const SuppliersScreen(),
      sales: (context) => const SalesScreen(),
      settings: (context) => const SettingsScreen(),
      accounts: (context) => const AccountsScreen(),
      financialReports: (context) => const FinancialReportsScreen(),
      // تم حذف الشاشات المكررة - استخدم financialReports بدلاً منها
      chartOfAccounts: (context) => const ChartOfAccountsScreen(),
      journalEntries: (context) => const JournalEntriesScreen(),
      transactions: (context) => const TransactionsScreen(),
      productOpeningBalance: (context) => const ProductOpeningBalanceScreen(),
      accountOpeningBalance: (context) => const AccountOpeningBalanceScreen(),
      // تم حذف EnhancedChartOfAccountsScreen - استخدم chartOfAccounts بدلاً منها
      journalEntryPosting: (context) => const JournalEntryPostingScreen(),
      warehouseHome: (context) => const WarehouseHomeScreen(),
      warehouseManagement: (context) => const WarehouseManagementScreen(),
      inventoryManagement: (context) => const InventoryManagementScreen(),
      inventoryReport: (context) => const InventoryReportScreen(),
      inventoryAlerts: (context) => const InventoryAlertsScreen(),
      inventoryDashboard: (context) => const InventoryDashboardScreen(),
      inventoryCount: (context) => const InventoryCountScreen(),
      supplierManagement: (context) => const SupplierManagementScreen(),
      // تم حذف CustomerManagementScreen - استخدم customers بدلاً منها
      productManagement: (context) => const ProductManagementScreen(),
      pos: (context) => const POSScreen(),
      salesManagement: (context) => const SalesManagementScreen(),
      printerSettings: (context) => const PrinterSettingsScreen(),
      companyProfile: (context) => const CompanyProfileScreen(),
      sampleData: (context) => const SampleDataScreen(),
      users: (context) => const UsersScreen(),
      userGroups: (context) => const UserGroupsScreen(),
      vouchers: (context) => const VouchersScreen(),
      receiptVoucher: (context) => const ReceiptVoucherScreen(),
      paymentVoucher: (context) => const PaymentVoucherScreen(),
      journalVoucher: (context) => const JournalVoucherScreen(),
      currencyExchange: (context) => const CurrencyExchangeScreen(),
      currencyManagement: (context) => const CurrencyManagementScreen(),
      branches: (context) => const BranchesScreen(),
      home: (context) => const DashboardScreen(),
      dashboard: (context) => const DashboardScreen(),
      exportImport: (context) => const ExportImportScreen(),
      simpleTie: (context) => const SimpleTieScreen(),

      // Rutas de gestión de usuarios y roles
      usersManagement: (context) => const UsersScreen(),
      rolesManagement: (context) => const RolesManagementScreen(),
      activityLog: (context) => const ActivityLogScreen(),
      userRoleAssignment: (context) => const UserRoleAssignmentScreen(),

      // مسارات المصروفات
      expenses: (context) => const ExpenseScreen(),
      expenseCategories: (context) => const ExpenseCategoryScreen(),
      expenseForm: (context) => const ExpenseFormScreen(),

      // مسارات الشروط والخصوصية
      legal: (context) => const LegalScreen(),

      // مسارات التقارير
      reports: (context) => const ReportsScreen(),

      // مسارات إعدادات الثيم
      themeSettings: (context) => const ThemeSettingsScreen(),
    };
  }

  // Route generator for routes with parameters
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case productForm:
        final Product? product = settings.arguments as Product?;
        return MaterialPageRoute(
          builder: (context) => ProductFormScreen(product: product),
        );

      case customerForm:
        final customer = settings.arguments;
        return MaterialPageRoute(
          builder: (context) =>
              CustomerFormScreen(customer: customer as Customer?),
        );

      case supplierForm:
        final Supplier? supplier = settings.arguments as Supplier?;
        return MaterialPageRoute(
          builder: (context) => SupplierFormScreen(supplier: supplier),
        );

      case saleForm:
        final Sale? sale = settings.arguments as Sale?;
        return MaterialPageRoute(
          builder: (context) => SaleFormScreen(sale: sale),
        );

      case saleDetails:
        final String saleId = settings.arguments as String;
        return MaterialPageRoute(
          builder: (context) => SaleDetailsScreen(saleId: saleId),
        );

      case receiptVoucher:
        final Voucher? voucher = settings.arguments as Voucher?;
        return MaterialPageRoute(
          builder: (context) => ReceiptVoucherScreen(voucher: voucher),
        );

      case paymentVoucher:
        final Voucher? voucher = settings.arguments as Voucher?;
        return MaterialPageRoute(
          builder: (context) => PaymentVoucherScreen(voucher: voucher),
        );

      case journalVoucher:
        final Voucher? voucher = settings.arguments as Voucher?;
        return MaterialPageRoute(
          builder: (context) => JournalVoucherScreen(voucher: voucher),
        );

      case userForm:
        final User? user = settings.arguments as User?;
        return MaterialPageRoute(
          builder: (context) => UserFormScreen(user: user),
        );

      case userGroupForm:
        final UserGroup? group = settings.arguments as UserGroup?;
        return MaterialPageRoute(
          builder: (context) => UserGroupFormScreen(group: group),
        );

      case journalEntryForm:
        final Map<String, dynamic>? journalEntry =
            settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (context) =>
              JournalEntryFormScreen(journalEntry: journalEntry),
        );

      case enhancedJournalEntryForm:
        final Map<String, dynamic>? journalEntry =
            settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (context) =>
              JournalEntryFormScreen(journalEntry: journalEntry),
        );

      case transactionForm:
        final Map<String, dynamic>? transaction =
            settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (context) => TransactionFormScreen(transaction: transaction),
        );

      default:
        return MaterialPageRoute(
          builder: (context) => const Scaffold(
            body: Center(
              child: Text('Route not found!'),
            ),
          ),
        );
    }
  }

  // Navigation methods
  static void navigateTo(BuildContext context, String routeName,
      {Object? arguments}) {
    Navigator.pushNamed(context, routeName, arguments: arguments);
  }

  static void navigateToAndReplace(BuildContext context, String routeName,
      {Object? arguments}) {
    Navigator.pushReplacementNamed(context, routeName, arguments: arguments);
  }

  static void navigateToAndClearStack(BuildContext context, String routeName,
      {Object? arguments}) {
    Navigator.pushNamedAndRemoveUntil(context, routeName, (route) => false,
        arguments: arguments);
  }

  static void goBack(BuildContext context, {dynamic result}) {
    Navigator.pop(context, result);
  }
}
