/// حالات المشتريات
enum PurchaseStatus {
  /// مسودة
  draft,

  /// معلق
  pending,

  /// مكتمل
  completed,

  /// ملغي
  cancelled,
}

/// الحصول على اسم حالة المشتريات
String getPurchaseStatusName(PurchaseStatus status) {
  switch (status) {
    case PurchaseStatus.draft:
      return 'مسودة';
    case PurchaseStatus.pending:
      return 'معلق';
    case PurchaseStatus.completed:
      return 'مكتمل';
    case PurchaseStatus.cancelled:
      return 'ملغي';
  }
}

/// الحصول على حالة المشتريات من الاسم
PurchaseStatus getPurchaseStatusFromName(String name) {
  switch (name) {
    case 'مسودة':
      return PurchaseStatus.draft;
    case 'معلق':
      return PurchaseStatus.pending;
    case 'مكتمل':
      return PurchaseStatus.completed;
    case 'ملغي':
      return PurchaseStatus.cancelled;
    default:
      return PurchaseStatus.draft;
  }
}

/// الحصول على حالة المشتريات من القيمة النصية
PurchaseStatus getPurchaseStatusFromString(String value) {
  switch (value) {
    case 'draft':
      return PurchaseStatus.draft;
    case 'pending':
      return PurchaseStatus.pending;
    case 'completed':
      return PurchaseStatus.completed;
    case 'cancelled':
      return PurchaseStatus.cancelled;
    default:
      return PurchaseStatus.draft;
  }
}
