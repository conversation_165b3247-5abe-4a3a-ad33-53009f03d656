import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نوع الفئة
enum CategoryType {
  product,
  expense,
  income,
  customer,
  supplier,
  account,
  other,
}

/// نموذج الفئة الموحد
/// تم توحيده من جميع نماذج الفئات في المشروع
class Category extends BaseModel {
  // معلومات أساسية
  final String name;
  final String? description;
  final String? code;
  final bool isActive;

  // معلومات التسلسل الهرمي
  final String? parentId;
  final String? parentName; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات

  // معلومات التصنيف
  final String
      type; // نوع الفئة (product, expense, income, customer, supplier, account, other)

  // معلومات إضافية
  final String? imageUrl;
  final Map<String, dynamic>? metadata;

  Category({
    String? id,
    required this.name,
    this.description,
    this.code,
    this.isActive = true,
    this.parentId,
    this.parentName,
    required this.type,
    this.imageUrl,
    this.metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذه الفئة مع استبدال الحقول المحددة بقيم جديدة
  Category copyWith({
    String? id,
    String? name,
    String? description,
    String? code,
    bool? isActive,
    String? parentId,
    String? parentName,
    String? type,
    String? imageUrl,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      code: code ?? this.code,
      isActive: isActive ?? this.isActive,
      parentId: parentId ?? this.parentId,
      parentName: parentName ?? this.parentName,
      type: type ?? this.type,
      imageUrl: imageUrl ?? this.imageUrl,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل الفئة إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'code': code,
      'is_active': isActive ? 1 : 0,
      'parent_id': parentId,
      'type': type,
      'image_url': imageUrl,
      'metadata': metadata != null ? _encodeMetadata(metadata!) : null,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء فئة من Map
  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['id'],
      name: map['name'] ?? '',
      description: map['description'],
      code: map['code'],
      isActive: map['is_active'] == 1 || map['is_active'] == true,
      parentId: map['parent_id'],
      parentName: map['parent_name'],
      type: map['type'] ?? 'product',
      imageUrl: map['image_url'],
      metadata:
          map['metadata'] != null ? _decodeMetadata(map['metadata']) : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1 || map['is_deleted'] == true,
    );
  }

  /// تشفير البيانات الوصفية
  static String _encodeMetadata(Map<String, dynamic> metadata) {
    try {
      return jsonEncode(metadata);
    } catch (e) {
      return '{}';
    }
  }

  /// فك تشفير البيانات الوصفية
  static Map<String, dynamic> _decodeMetadata(String metadataString) {
    try {
      return jsonDecode(metadataString) as Map<String, dynamic>;
    } catch (e) {
      return {};
    }
  }

  /// تحويل الفئة إلى JSON
  String toJson() {
    return jsonEncode(toMap());
  }

  /// إنشاء فئة من JSON
  factory Category.fromJson(String source) {
    return Category.fromMap(jsonDecode(source));
  }

  /// التحقق مما إذا كانت الفئة هي فئة منتج
  bool get isProductCategory => type == 'product';

  /// التحقق مما إذا كانت الفئة هي فئة مصروفات
  bool get isExpenseCategory => type == 'expense';

  /// التحقق مما إذا كانت الفئة هي فئة إيرادات
  bool get isIncomeCategory => type == 'income';

  /// التحقق مما إذا كانت الفئة هي فئة عملاء
  bool get isCustomerCategory => type == 'customer';

  /// التحقق مما إذا كانت الفئة هي فئة موردين
  bool get isSupplierCategory => type == 'supplier';

  /// التحقق مما إذا كانت الفئة هي فئة حسابات
  bool get isAccountCategory => type == 'account';

  @override
  String toString() {
    return 'Category(id: $id, name: $name, type: $type)';
  }

  /// إضافة عامل الفهرس للوصول إلى خصائص الفئة كما لو كانت Map
  dynamic operator [](String key) {
    switch (key) {
      case 'id':
        return id;
      case 'name':
        return name;
      case 'description':
        return description;
      case 'code':
        return code;
      case 'is_active':
      case 'isActive':
        return isActive;
      case 'parent_id':
      case 'parentId':
        return parentId;
      case 'parent_name':
      case 'parentName':
        return parentName;
      case 'type':
        return type;
      case 'image_url':
      case 'imageUrl':
        return imageUrl;
      case 'metadata':
        return metadata;
      case 'created_at':
      case 'createdAt':
        return createdAt;
      case 'created_by':
      case 'createdBy':
        return createdBy;
      case 'updated_at':
      case 'updatedAt':
        return updatedAt;
      case 'updated_by':
      case 'updatedBy':
        return updatedBy;
      case 'is_deleted':
      case 'isDeleted':
        return isDeleted;
      default:
        return null;
    }
  }
}
