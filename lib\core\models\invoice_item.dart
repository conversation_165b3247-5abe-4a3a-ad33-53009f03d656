import 'package:uuid/uuid.dart';
import 'base_model.dart';
import 'product.dart';

/// نموذج عنصر الفاتورة
class InvoiceItem extends BaseModel {
  final String invoiceId;
  final String productId;
  final Product? product; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final String? description;
  final double quantity;
  final String? unitId;
  final String? unitName; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final double unitPrice;
  final String? discountType; // percentage, amount
  final double discountValue;
  final double taxRate;
  final double taxAmount;
  final double subtotal;
  final double total;

  InvoiceItem({
    String? id,
    required this.invoiceId,
    required this.productId,
    this.product,
    this.description,
    required this.quantity,
    this.unitId,
    this.unitName,
    required this.unitPrice,
    this.discountType,
    this.discountValue = 0.0,
    this.taxRate = 0.0,
    this.taxAmount = 0.0,
    required this.subtotal,
    required this.total,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: null,
          updatedAt: updatedAt,
          updatedBy: null,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا العنصر مع استبدال الحقول المحددة بقيم جديدة
  InvoiceItem copyWith({
    String? id,
    String? invoiceId,
    String? productId,
    Product? product,
    String? description,
    double? quantity,
    String? unitId,
    String? unitName,
    double? unitPrice,
    String? discountType,
    double? discountValue,
    double? taxRate,
    double? taxAmount,
    double? subtotal,
    double? total,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unitId: unitId ?? this.unitId,
      unitName: unitName ?? this.unitName,
      unitPrice: unitPrice ?? this.unitPrice,
      discountType: discountType ?? this.discountType,
      discountValue: discountValue ?? this.discountValue,
      taxRate: taxRate ?? this.taxRate,
      taxAmount: taxAmount ?? this.taxAmount,
      subtotal: subtotal ?? this.subtotal,
      total: total ?? this.total,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل عنصر الفاتورة إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_id': invoiceId,
      'product_id': productId,
      'description': description,
      'quantity': quantity,
      'unit_id': unitId,
      'unit_price': unitPrice,
      'discount_type': discountType,
      'discount_value': discountValue,
      'tax_rate': taxRate,
      'tax_amount': taxAmount,
      'subtotal': subtotal,
      'total': total,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء عنصر فاتورة من Map
  factory InvoiceItem.fromMap(Map<String, dynamic> map) {
    return InvoiceItem(
      id: map['id'],
      invoiceId: map['invoice_id'],
      productId: map['product_id'],
      product: map['product'] != null ? Product.fromMap(map['product']) : null,
      description: map['description'],
      quantity: map['quantity'] is int
          ? (map['quantity'] as int).toDouble()
          : (map['quantity'] as double? ?? 0.0),
      unitId: map['unit_id'],
      unitName: map['unit_name'],
      unitPrice: map['unit_price'] is int
          ? (map['unit_price'] as int).toDouble()
          : (map['unit_price'] as double? ?? 0.0),
      discountType: map['discount_type'],
      discountValue: map['discount_value'] is int
          ? (map['discount_value'] as int).toDouble()
          : (map['discount_value'] as double? ?? 0.0),
      taxRate: map['tax_rate'] is int
          ? (map['tax_rate'] as int).toDouble()
          : (map['tax_rate'] as double? ?? 0.0),
      taxAmount: map['tax_amount'] is int
          ? (map['tax_amount'] as int).toDouble()
          : (map['tax_amount'] as double? ?? 0.0),
      subtotal: map['subtotal'] is int
          ? (map['subtotal'] as int).toDouble()
          : (map['subtotal'] as double? ?? 0.0),
      total: map['total'] is int
          ? (map['total'] as int).toDouble()
          : (map['total'] as double? ?? 0.0),
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      isDeleted: map['is_deleted'] == 1,
    );
  }

  @override
  String toString() {
    return 'InvoiceItem(id: $id, productId: $productId, quantity: $quantity, unitPrice: $unitPrice, total: $total)';
  }
}
