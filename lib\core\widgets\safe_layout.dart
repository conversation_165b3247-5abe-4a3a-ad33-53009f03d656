import 'package:flutter/material.dart';

/// ودجت التخطيط الآمن
/// يستخدم لتغليف المحتوى وضمان عدم حدوث مشاكل في التخطيط
class SafeLayout extends StatelessWidget {
  final Widget child;
  final String? title;
  final List<Widget>? actions;
  final Widget? body;
  final Widget? floatingActionButton;
  final bool preventOverflow;
  final bool enableScrolling;
  final EdgeInsetsGeometry? padding;
  final bool useScaffold;

  const SafeLayout({
    Key? key,
    required this.child,
    this.title,
    this.actions,
    this.body,
    this.floatingActionButton,
    this.preventOverflow = true,
    this.enableScrolling = true,
    this.padding,
    this.useScaffold = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // إذا كان هناك عنوان أو إجراءات أو جسم، فاستخدم Scaffold
    if (title != null ||
        actions != null ||
        body != null ||
        floatingActionButton != null ||
        useScaffold) {
      return Scaffold(
        appBar: title != null
            ? AppBar(
                title: Text(title!),
                actions: actions,
              )
            : null,
        body: body ?? _buildContent(child),
        floatingActionButton: floatingActionButton,
      );
    }

    // وإلا استخدم المحتوى العادي
    return _buildContent(child);
  }

  /// بناء المحتوى مع التمرير والحشوة
  Widget _buildContent(Widget content) {
    // إضافة حشوة إذا تم تحديدها
    if (padding != null) {
      content = Padding(
        padding: padding!,
        child: content,
      );
    }

    // تمكين التمرير إذا كان مطلوبًا
    if (enableScrolling) {
      content = SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: content,
      );
    }

    return content;
  }
}
