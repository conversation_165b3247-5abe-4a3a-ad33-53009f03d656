import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:intl/intl.dart';

import '../../../core/models/customer.dart';
import '../../../core/models/sale.dart';
import '../../../core/models/sale_status.dart';
import '../../sales/presenters/sale_presenter.dart';
import '../../../core/theme/index.dart';

/// مربع حوار تفاصيل العميل
class CustomerDetailsDialog extends StatefulWidget {
  final Customer customer;

  const CustomerDetailsDialog({
    Key? key,
    required this.customer,
  }) : super(key: key);

  @override
  State<CustomerDetailsDialog> createState() => _CustomerDetailsDialogState();
}

class _CustomerDetailsDialogState extends State<CustomerDetailsDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  List<Sale> _customerSales = [];

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 2, vsync: this);

    // تحميل مبيعات العميل
    _loadCustomerSales();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل مبيعات العميل
  Future<void> _loadCustomerSales() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Obtener las ventas del cliente
      final salePresenter =
          AppProviders.getLazyPresenter<SalePresenter>(() => SalePresenter());
      // استخدام معرف العميل
      _customerSales =
          await salePresenter.getSalesByCustomerId(widget.customer.id);
    } catch (e) {
      // تجاهل الخطأ
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('تفاصيل العميل: ${widget.customer.name}'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // علامات التبويب
            TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'المعلومات الأساسية'),
                Tab(text: 'المبيعات'),
              ],
              labelColor: AppColors.primary,
              unselectedLabelColor: AppColors.lightTextSecondary,
              indicatorColor: AppColors.primary,
            ),

            // محتوى التبويب
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildBasicInfoTab(),
                  _buildSalesTab(),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  /// بناء تبويب المعلومات الأساسية
  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات الاتصال
          _buildInfoCard(
            title: 'معلومات الاتصال',
            children: [
              _buildInfoRow('الاسم:', widget.customer.name),
              if (widget.customer.phone != null)
                _buildInfoRow('رقم الهاتف:', widget.customer.phone!),
              if (widget.customer.email != null)
                _buildInfoRow('البريد الإلكتروني:', widget.customer.email!),
              if (widget.customer.address != null)
                _buildInfoRow('العنوان:', widget.customer.address!),
              if (widget.customer.taxNumber != null)
                _buildInfoRow('الرقم الضريبي:', widget.customer.taxNumber!),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),

          // المعلومات المالية
          _buildInfoCard(
            title: 'المعلومات المالية',
            children: [
              _buildInfoRow(
                'الرصيد:',
                widget.customer.balance.toStringAsFixed(2),
                textColor: widget.customer.balance > 0 ? AppColors.error : null,
              ),
              // No hay campo creditLimit en el modelo Customer
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),

          // ملاحظات
          if (widget.customer.notes != null &&
              widget.customer.notes!.isNotEmpty)
            _buildInfoCard(
              title: 'ملاحظات',
              children: [
                Text(widget.customer.notes!),
              ],
            ),
        ],
      ),
    );
  }

  /// بناء تبويب المبيعات
  Widget _buildSalesTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_customerSales.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 64,
              color: AppColors.lightTextSecondary,
            ),
            SizedBox(height: 16),
            Text('لا يوجد مبيعات لهذا العميل'),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص المبيعات
          _buildInfoCard(
            title: 'ملخص المبيعات',
            children: [
              _buildInfoRow('عدد المبيعات:', _customerSales.length.toString()),
              _buildInfoRow(
                'إجمالي المبيعات:',
                _customerSales
                    .fold<double>(0, (sum, sale) => sum + sale.total)
                    .toStringAsFixed(2),
              ),
              _buildInfoRow(
                'إجمالي المدفوعات:',
                _customerSales
                    .fold<double>(0, (sum, sale) => sum + sale.amountPaid)
                    .toStringAsFixed(2),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),

          // قائمة المبيعات
          Text(
            'آخر المبيعات',
            style: AppTypography.lightTextTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ) ??
                const AppTypography(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppDimensions.spacing8),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _customerSales.length,
            itemBuilder: (context, index) {
              final sale = _customerSales[index];
              return _buildSaleItem(sale);
            },
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة معلومات
  Widget _buildInfoCard({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTypography.lightTextTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ) ??
                  const AppTypography(fontWeight: FontWeight.bold),
            ),
            const Divider(),
            ...children,
          ],
        ),
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value, {Color? textColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const AppTypography(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTypography(color: textColor),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر بيع
  Widget _buildSaleItem(Sale sale) {
    // تحديد لون الحالة
    Color statusColor;
    String statusText;

    switch (sale.status) {
      case SaleStatus.completed:
        statusColor = AppColors.success;
        statusText = 'مكتمل';
        break;
      case SaleStatus.cancelled:
        statusColor = AppColors.error;
        statusText = 'ملغي';
        break;
      case SaleStatus.draft:
        statusColor = AppColors.warning;
        statusText = 'مسودة';
        break;
      case SaleStatus.pending:
        statusColor = AppColors.info;
        statusText = 'معلق';
        break;
      case SaleStatus.returned:
        statusColor = AppColors.accent;
        statusText = 'مرتجع';
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text('فاتورة #${sale.id.substring(0, 8)}'),
        subtitle: Text(DateFormat('yyyy-MM-dd HH:mm').format(sale.createdAt)),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${sale.total.toStringAsFixed(2)} ريال',
              style: const AppTypography(
                fontWeight: FontWeight.bold,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                statusText,
                style: AppTypography(
                  color: statusColor,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
