import 'package:flutter/material.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';

class AccountTreeScreen extends StatefulWidget {
  const AccountTreeScreen({Key? key}) : super(key: key);

  @override
  State<AccountTreeScreen> createState() => _AccountTreeScreenState();
}

class _AccountTreeScreenState extends State<AccountTreeScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final TextEditingController _searchController = TextEditingController();

  List<Map<String, dynamic>> _allAccounts = [];
  List<Map<String, dynamic>> _filteredAccounts = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAccounts();
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterAccounts();
    });
  }

  Future<void> _loadAccounts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final db = await _databaseHelper.database;

      // Get all accounts with parent information
      final List<Map<String, dynamic>> accounts = await db.rawQuery('''
        SELECT a.*,
               p.name as parent_name,
               a.created_by as created_by_name
        FROM accounts a
        LEFT JOIN accounts p ON a.parent_id = p.id
        ORDER BY a.name
      ''');

      setState(() {
        _allAccounts = accounts;
        _filteredAccounts = accounts;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to load accounts for tree view',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'AccountTreeScreen'},
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل في تحميل الحسابات')),
        );
      }
    }
  }

  void _filterAccounts() {
    if (_searchQuery.isEmpty) {
      setState(() {
        _filteredAccounts = _allAccounts;
      });
      return;
    }

    final query = _searchQuery.toLowerCase();
    setState(() {
      _filteredAccounts = _allAccounts.where((account) {
        final name = account['name'].toString().toLowerCase();
        final parentName =
            account['parent_name']?.toString().toLowerCase() ?? '';
        return name.contains(query) || parentName.contains(query);
      }).toList();
    });
  }

  List<Map<String, dynamic>> _getChildAccounts(int parentId) {
    return _filteredAccounts
        .where((account) =>
            account['parent_id'] != null && account['parent_id'] == parentId)
        .toList();
  }

  Widget _buildAccountTree() {
    // Get root accounts (accounts with no parent)
    final rootAccounts = _filteredAccounts
        .where((account) => account['parent_id'] == null)
        .toList();

    return ListView.builder(
      itemCount: rootAccounts.length,
      itemBuilder: (context, index) {
        final account = rootAccounts[index];
        return _buildAccountItem(account, 0);
      },
    );
  }

  Widget _buildAccountItem(Map<String, dynamic> account, int level) {
    final children = _getChildAccounts(account['id']);
    final hasChildren = children.isNotEmpty;

    return ExpansionTile(
      leading: Icon(
        hasChildren ? Icons.account_balance : Icons.account_circle,
        color: AppColors.lightTextSecondary,
      ),
      title: Text(
        account['name'] ?? '',
        style: AppTypography.createCustomStyle(
          fontWeight: level == 0 ? AppTypography.weightBold : FontWeight.normal,
        ),
      ),
      subtitle: Text(
        _getAccountTypeDisplayName(account['type'] as String),
        style: AppTypography.createCustomStyle(
          fontSize: 12,
          color: AppColors.lightTextSecondary,
        ),
      ),
      children: [
        if (hasChildren)
          Padding(
            padding: const EdgeInsets.only(left: 16.0),
            child: ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: children.length,
              itemBuilder: (context, index) {
                return _buildAccountItem(children[index], level + 1);
              },
            ),
          ),
      ],
    );
  }

  String _getAccountTypeDisplayName(String type) {
    switch (type) {
      case 'asset':
        return 'أصول';
      case 'liability':
        return 'خصوم';
      case 'equity':
        return 'حقوق ملكية';
      case 'revenue':
        return 'إيرادات';
      case 'expense':
        return 'مصروفات';
      case 'customer':
        return 'عميل';
      case 'supplier':
        return 'مورد';
      case 'cash':
        return 'نقدية';
      default:
        return type;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AkAppBar(
        title: 'شجرة الحسابات',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Search field
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'بحث عن حساب...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _filterAccounts();
                });
              },
            ),
          ),

          // Account tree
          Expanded(
            child: _isLoading
                ? const Center(child: AkLoadingIndicator())
                : _filteredAccounts.isEmpty
                    ? AkEmptyState(
                        title: 'لا توجد حسابات',
                        message: _searchQuery.isNotEmpty
                            ? 'لا توجد حسابات تطابق معايير البحث'
                            : 'لم يتم إنشاء أي حسابات حتى الآن',
                        description: _searchQuery.isNotEmpty
                            ? 'جرب تغيير كلمة البحث'
                            : 'ابدأ بإنشاء حساب جديد',
                        icon: Icons.account_tree,
                        onRefresh: _loadAccounts,
                      )
                    : AkCard(
                        child: _buildAccountTree(),
                      ),
          ),
        ],
      ),
    );
  }
}
