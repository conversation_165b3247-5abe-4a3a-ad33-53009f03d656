import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
// import 'package:excel/excel.dart'; // Comentado temporalmente por conflictos de dependencias
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../../../core/database/database_service.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/theme/index.dart';

/// خدمة تصدير واستيراد البيانات
/// تستخدم لتصدير واستيراد البيانات من وإلى التطبيق
class ExportImportService {
  final DatabaseService _db = DatabaseService.instance;

  // Singleton pattern
  static final ExportImportService _instance = ExportImportService._internal();
  factory ExportImportService() => _instance;
  ExportImportService._internal();

  /// تصدير البيانات إلى ملف CSV
  Future<String?> exportToCsv(String tableName, {String? query}) async {
    try {
      // التحقق من الصلاحيات
      if (!await _checkPermissions()) {
        return null;
      }

      // الحصول على البيانات
      final data = await _getDataForExport(tableName, query: query);
      if (data.isEmpty) {
        return 'لا توجد بيانات للتصدير';
      }

      // تحويل البيانات إلى CSV
      final csvData = const ListToCsvConverter().convert(data);

      // حفظ الملف
      final directory = await getExternalStorageDirectory();
      if (directory == null) {
        return 'لا يمكن الوصول إلى مجلد التخزين';
      }

      final fileName =
          '${tableName}_${DateTime.now().millisecondsSinceEpoch}.csv';
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(csvData);

      // مشاركة الملف - تم تعطيل المشاركة مؤقتًا بسبب مشاكل التوافق
      // await Share.shareFiles([file.path], text: 'تصدير بيانات $tableName');

      return 'تم تصدير البيانات بنجاح إلى $fileName';
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في تصدير البيانات إلى CSV: $e');
      ErrorTracker.captureError(
        'خطأ في تصدير البيانات إلى CSV',
        error: e,
        stackTrace: stackTrace,
        context: {'tableName': tableName, 'query': query},
      );
      return 'حدث خطأ أثناء تصدير البيانات: $e';
    }
  }

  /// تصدير البيانات إلى ملف Excel
  Future<String?> exportToExcel(String tableName, {String? query}) async {
    try {
      // التحقق من الصلاحيات
      if (!await _checkPermissions()) {
        return null;
      }

      // الحصول على البيانات
      final data = await _getDataForExport(tableName, query: query);
      if (data.isEmpty) {
        return 'لا توجد بيانات للتصدير';
      }

      // تحويل البيانات إلى CSV كبديل مؤقت عن Excel
      final csvData = const ListToCsvConverter().convert(data);

      // حفظ الملف
      final directory = await getExternalStorageDirectory();
      if (directory == null) {
        return 'لا يمكن الوصول إلى مجلد التخزين';
      }

      final fileName =
          '${tableName}_${DateTime.now().millisecondsSinceEpoch}.csv';
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(csvData);

      // مشاركة الملف - تم تعطيل المشاركة مؤقتًا بسبب مشاكل التوافق
      // await Share.shareXFiles([XFile(file.path)], text: 'تصدير بيانات $tableName');

      return 'تم تصدير البيانات بنجاح إلى $fileName (CSV بدلاً من Excel بسبب مشكلة توافق)';
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في تصدير البيانات إلى Excel: $e');
      ErrorTracker.captureError(
        'خطأ في تصدير البيانات إلى Excel',
        error: e,
        stackTrace: stackTrace,
        context: {'tableName': tableName, 'query': query},
      );
      return 'حدث خطأ أثناء تصدير البيانات: $e';
    }
  }

  /// تصدير البيانات إلى ملف PDF
  Future<String?> exportToPdf(String tableName,
      {String? query, String? title}) async {
    try {
      // التحقق من الصلاحيات
      if (!await _checkPermissions()) {
        return null;
      }

      // الحصول على البيانات
      final data = await _getDataForExport(tableName, query: query);
      if (data.isEmpty) {
        return 'لا توجد بيانات للتصدير';
      }

      // إنشاء ملف PDF
      final pdf = pw.Document();

      // إضافة صفحة
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return [
              // العنوان
              pw.Header(
                level: 0,
                child: pw.Text(title ?? 'تصدير بيانات $tableName'),
              ),
              // التاريخ
              pw.Paragraph(
                text:
                    'تاريخ التصدير: ${DateTime.now().toString().split('.').first}',
              ),
              // الجدول
              pw.TableHelper.fromTextArray(
                headers: data[0],
                data: data.sublist(1),
                border: pw.TableBorder.all(),
                headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                headerDecoration: pw.BoxDecoration(
                    color: PdfColor.fromInt(
                        AppColors.lightSurfaceVariant.toARGB32())),
                cellHeight: 30,
                cellAlignments: {
                  for (int i = 0; i < data[0].length; i++)
                    i: pw.Alignment.center,
                },
              ),
            ];
          },
        ),
      );

      // حفظ الملف
      final directory = await getExternalStorageDirectory();
      if (directory == null) {
        return 'لا يمكن الوصول إلى مجلد التخزين';
      }

      final fileName =
          '${tableName}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(await pdf.save());

      // مشاركة الملف - تم تعطيل المشاركة مؤقتًا بسبب مشاكل التوافق
      // await Share.shareXFiles([XFile(file.path)], text: 'تصدير بيانات $tableName');

      return 'تم تصدير البيانات بنجاح إلى $fileName';
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في تصدير البيانات إلى PDF: $e');
      ErrorTracker.captureError(
        'خطأ في تصدير البيانات إلى PDF',
        error: e,
        stackTrace: stackTrace,
        context: {'tableName': tableName, 'query': query},
      );
      return 'حدث خطأ أثناء تصدير البيانات: $e';
    }
  }

  /// تصدير البيانات إلى ملف JSON
  Future<String?> exportToJson(String tableName, {String? query}) async {
    try {
      // التحقق من الصلاحيات
      if (!await _checkPermissions()) {
        return null;
      }

      // الحصول على البيانات
      final rawData = await _getDataForExport(tableName, query: query);
      if (rawData.isEmpty) {
        return 'لا توجد بيانات للتصدير';
      }

      // تحويل البيانات إلى JSON
      final headers = rawData[0];
      final jsonData = [];
      for (int i = 1; i < rawData.length; i++) {
        final row = rawData[i];
        final Map<String, dynamic> rowData = {};
        for (int j = 0; j < headers.length; j++) {
          rowData[headers[j].toString()] = row[j];
        }
        jsonData.add(rowData);
      }

      // حفظ الملف
      final directory = await getExternalStorageDirectory();
      if (directory == null) {
        return 'لا يمكن الوصول إلى مجلد التخزين';
      }

      final fileName =
          '${tableName}_${DateTime.now().millisecondsSinceEpoch}.json';
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(jsonEncode(jsonData));

      // مشاركة الملف - تم تعطيل المشاركة مؤقتًا بسبب مشاكل التوافق
      // await Share.shareXFiles([XFile(file.path)], text: 'تصدير بيانات $tableName');

      return 'تم تصدير البيانات بنجاح إلى $fileName';
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في تصدير البيانات إلى JSON: $e');
      ErrorTracker.captureError(
        'خطأ في تصدير البيانات إلى JSON',
        error: e,
        stackTrace: stackTrace,
        context: {'tableName': tableName, 'query': query},
      );
      return 'حدث خطأ أثناء تصدير البيانات: $e';
    }
  }

  /// استيراد البيانات من ملف CSV
  Future<String?> importFromCsv(String tableName) async {
    try {
      // التحقق من الصلاحيات
      if (!await _checkPermissions()) {
        return null;
      }

      // اختيار الملف
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result == null || result.files.isEmpty) {
        return 'لم يتم اختيار ملف';
      }

      final file = File(result.files.first.path!);
      final csvData = await file.readAsString();
      final rows = const CsvToListConverter().convert(csvData);

      if (rows.isEmpty) {
        return 'الملف فارغ';
      }

      // استيراد البيانات
      return await _importData(tableName, rows);
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في استيراد البيانات من CSV: $e');
      ErrorTracker.captureError(
        'خطأ في استيراد البيانات من CSV',
        error: e,
        stackTrace: stackTrace,
        context: {'tableName': tableName},
      );
      return 'حدث خطأ أثناء استيراد البيانات: $e';
    }
  }

  /// استيراد البيانات من ملف Excel
  Future<String?> importFromExcel(String tableName) async {
    try {
      // التحقق من الصلاحيات
      if (!await _checkPermissions()) {
        return null;
      }

      // اختيار الملف
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'], // استخدام CSV بدلاً من Excel مؤقتاً
      );

      if (result == null || result.files.isEmpty) {
        return 'لم يتم اختيار ملف';
      }

      final file = File(result.files.first.path!);
      final csvData = await file.readAsString();
      final rows = const CsvToListConverter().convert(csvData);

      if (rows.isEmpty) {
        return 'الملف فارغ';
      }

      // استيراد البيانات
      return await _importData(tableName, rows);
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في استيراد البيانات من Excel: $e');
      ErrorTracker.captureError(
        'خطأ في استيراد البيانات من Excel',
        error: e,
        stackTrace: stackTrace,
        context: {'tableName': tableName},
      );
      return 'حدث خطأ أثناء استيراد البيانات: $e';
    }
  }

  /// استيراد البيانات من ملف JSON
  Future<String?> importFromJson(String tableName) async {
    try {
      // التحقق من الصلاحيات
      if (!await _checkPermissions()) {
        return null;
      }

      // اختيار الملف
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result == null || result.files.isEmpty) {
        return 'لم يتم اختيار ملف';
      }

      final file = File(result.files.first.path!);
      final jsonData = await file.readAsString();
      final List<dynamic> data = jsonDecode(jsonData);

      if (data.isEmpty) {
        return 'الملف فارغ';
      }

      // تحويل البيانات إلى قائمة
      final rows = <List<dynamic>>[];
      final headers = data[0].keys.toList();
      rows.add(headers);

      for (final item in data) {
        final row = <dynamic>[];
        for (final header in headers) {
          row.add(item[header]);
        }
        rows.add(row);
      }

      // استيراد البيانات
      return await _importData(tableName, rows);
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في استيراد البيانات من JSON: $e');
      ErrorTracker.captureError(
        'خطأ في استيراد البيانات من JSON',
        error: e,
        stackTrace: stackTrace,
        context: {'tableName': tableName},
      );
      return 'حدث خطأ أثناء استيراد البيانات: $e';
    }
  }

  /// الحصول على البيانات للتصدير
  Future<List<List<dynamic>>> _getDataForExport(String tableName,
      {String? query}) async {
    final db = await _db.database;
    final List<Map<String, dynamic>> results;

    if (query != null && query.isNotEmpty) {
      results = await db.rawQuery(query);
    } else {
      results = await db.query(tableName);
    }

    if (results.isEmpty) {
      return [];
    }

    // استخراج أسماء الأعمدة
    final headers = results.first.keys.toList();
    final data = <List<dynamic>>[];
    data.add(headers);

    // إضافة البيانات
    for (final row in results) {
      final rowData = <dynamic>[];
      for (final header in headers) {
        rowData.add(row[header]);
      }
      data.add(rowData);
    }

    return data;
  }

  /// استيراد البيانات
  Future<String> _importData(String tableName, List<List<dynamic>> rows) async {
    try {
      final db = await _db.database;
      final headers = rows[0].map((e) => e.toString()).toList();

      // التحقق من وجود الأعمدة المطلوبة
      final tableInfo = await db.rawQuery('PRAGMA table_info($tableName)');
      final tableColumns =
          tableInfo.map((col) => col['name'].toString()).toList();

      // التحقق من توافق الأعمدة
      for (final header in headers) {
        if (!tableColumns.contains(header)) {
          return 'العمود "$header" غير موجود في الجدول';
        }
      }

      // بدء المعاملة
      await db.transaction((txn) async {
        for (int i = 1; i < rows.length; i++) {
          final row = rows[i];
          final Map<String, dynamic> rowData = {};

          for (int j = 0; j < headers.length; j++) {
            if (j < row.length) {
              rowData[headers[j]] = row[j];
            }
          }

          // التحقق من وجود السجل
          final id = rowData['id'];
          if (id != null) {
            final existing = await txn.query(
              tableName,
              where: 'id = ?',
              whereArgs: [id],
              limit: 1,
            );

            if (existing.isNotEmpty) {
              // تحديث السجل الموجود
              await txn.update(
                tableName,
                rowData,
                where: 'id = ?',
                whereArgs: [id],
              );
            } else {
              // إضافة سجل جديد
              await txn.insert(tableName, rowData);
            }
          } else {
            // إضافة سجل جديد بدون معرف
            await txn.insert(tableName, rowData);
          }
        }
      });

      return 'تم استيراد ${rows.length - 1} سجل بنجاح';
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في استيراد البيانات: $e');
      ErrorTracker.captureError(
        'خطأ في استيراد البيانات',
        error: e,
        stackTrace: stackTrace,
        context: {'tableName': tableName, 'rowCount': rows.length},
      );
      return 'حدث خطأ أثناء استيراد البيانات: $e';
    }
  }

  /// التحقق من الصلاحيات
  Future<bool> _checkPermissions() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.status;
      if (!status.isGranted) {
        final result = await Permission.storage.request();
        return result.isGranted;
      }
      return true;
    }
    return true;
  }
}
