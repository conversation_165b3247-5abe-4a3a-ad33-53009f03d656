import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/providers/app_providers.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../core/utils/index.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/services/session_manager.dart';
import '../../../core/services/setup_flag_service.dart';

import '../../../core/theme/index.dart'; // استخدام الثيم الموحد
import '../../../core/widgets/index.dart';
import '../../../features/branches/models/branch.dart';
import '../../branches/services/branch_service.dart';
import '../../../core/auth/services/auth_service.dart';
import '../../../features/users/models/user.dart';
import '../../../core/auth/models/user_role.dart';
import '../../../features/users/presenters/permission_presenter.dart';

/// شاشة تسجيل الدخول الرئيسية لتطبيق تاجر بلس
/// تدعم تسجيل الدخول العادي والدخول كزائر مع دعم كامل للعربية وRTL

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with SingleTickerProviderStateMixin {
  /// مفتاح النموذج للتحقق من صحة البيانات المدخلة
  final _formKey = GlobalKey<FormState>();

  /// متحكم حقل اسم المستخدم أو البريد الإلكتروني
  final _usernameOrEmailController = TextEditingController();

  /// متحكم حقل كلمة المرور
  final _passwordController = TextEditingController();

  /// حالة تذكر بيانات تسجيل الدخول
  bool _rememberMe = false;

  /// متغير للتحكم في إظهار زر التسجيل (يخفى بعد أول تسجيل دخول ناجح)
  bool _showRegisterButton = true;

  /// معرف الفرع المحدد للدخول
  String? _selectedBranchId;

  /// قائمة الفروع المتاحة
  List<Branch> _branches = [];

  /// حالة تحميل الفروع
  bool _isLoadingBranches = false;

  /// متحكم الرسوم المتحركة للواجهة
  late AnimationController _animationController;

  /// رسم متحرك للظهور التدريجي
  late Animation<double> _fadeAnimation;

  /// قائمة أدوار المستخدمين المتاحة
  List<UserRole> _roles = [];

  /// حالة تحميل الأدوار
  bool _isLoadingRoles = false;

  /// مقدم خدمة الصلاحيات (تحميل كسول)
  late final PermissionPresenter _permissionPresenter;

  @override
  void initState() {
    super.initState();

    /// تهيئة مقدم خدمة الصلاحيات باستخدام التحميل الكسول
    _permissionPresenter = AppProviders.getLazyPresenter<PermissionPresenter>(
        () => PermissionPresenter());

    /// إعداد متحكم الرسوم المتحركة للواجهة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    /// إنشاء رسم متحرك للظهور التدريجي مع منحنى سلس
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );

    /// بدء الرسم المتحرك
    _animationController.forward();

    /// تحميل بيانات تسجيل الدخول المحفوظة مسبقاً
    _loadSavedCredentials();

    /// تحميل قائمة الفروع المتاحة
    _loadBranches();

    /// تحميل الأدوار ومعالجة الوسيطات بعد بناء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadRoles();
        _processArguments();
      }
    });
  }

  /// معالجة الوسيطات المرسلة من شاشة إنشاء الحساب
  void _processArguments() {
    // استخدام Future.delayed لضمان تنفيذ الكود بعد بناء الشاشة بالكامل
    Future.delayed(Duration.zero, () {
      if (!mounted) return;

      final args = ModalRoute.of(context)?.settings.arguments;
      AppLogger.info('معالجة الوسيطات: $args');

      if (args != null && args is Map<String, dynamic>) {
        AppLogger.info('تم استلام وسيطات صالحة من نوع Map');

        setState(() {
          if (args.containsKey('username')) {
            _usernameOrEmailController.text = args['username'] as String;
            AppLogger.info('تم تعيين اسم المستخدم: ${args['username']}');
          }
          if (args.containsKey('password')) {
            _passwordController.text = args['password'] as String;
            AppLogger.info('تم تعيين كلمة المرور');
          }

          // إخفاء زر التسجيل عند استلام بيانات من شاشة تعديل الحساب
          _showRegisterButton = false;
          AppLogger.info('تم إخفاء زر التسجيل');
        });

        AppLogger.info('تم استلام بيانات المستخدم من شاشة تعديل الحساب');

        // حفظ حالة إخفاء زر التسجيل في التخزين المحلي
        _saveHasLoggedInBefore();
      } else {
        AppLogger.info(
            'لم يتم استلام وسيطات أو الوسيطات ليست من النوع المتوقع');
      }
    });
  }

  /// حفظ حالة تسجيل الدخول السابق
  Future<void> _saveHasLoggedInBefore() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('hasLoggedInBefore', true);
      AppLogger.info('✅ تم حفظ حالة إخفاء زر التسجيل بنجاح');
    } catch (e) {
      AppLogger.error('❌ خطأ في حفظ حالة إخفاء زر التسجيل: $e');
    }
  }

  Future<void> _loadSavedCredentials() async {
    final prefs = await SharedPreferences.getInstance();

    // التحقق من وجود بيانات مؤقتة من شاشة التسجيل
    final tempUsername = prefs.getString('temp_username');
    final tempPassword = prefs.getString('temp_password');

    if (tempUsername != null && tempPassword != null) {
      AppLogger.info(
          'تم العثور على بيانات مؤقتة محفوظة: username=$tempUsername');

      setState(() {
        _usernameOrEmailController.text = tempUsername;
        _passwordController.text = tempPassword;
        _showRegisterButton = false;
      });

      // حذف البيانات المؤقتة بعد استخدامها
      await prefs.remove('temp_username');
      await prefs.remove('temp_password');

      // حفظ حالة إخفاء زر التسجيل
      await _saveHasLoggedInBefore();

      AppLogger.info('تم تحميل البيانات المؤقتة وحذفها بعد الاستخدام');
    } else {
      // تحميل بيانات تسجيل الدخول المحفوظة العادية
      setState(() {
        _usernameOrEmailController.text =
            prefs.getString('usernameOrEmail') ?? '';
        _passwordController.text = prefs.getString('password') ?? '';
        _rememberMe = prefs.getBool('rememberMe') ?? false;

        // Verificar si ya se ha iniciado sesión exitosamente antes
        _showRegisterButton = !(prefs.getBool('hasLoggedInBefore') ?? false);

        // تحميل معرف الفرع المحفوظ
        final savedBranchId = prefs.getString('branchId');
        if (savedBranchId != null && savedBranchId.isNotEmpty) {
          _selectedBranchId = savedBranchId;
          AppLogger.info('تم تحميل معرف الفرع المحفوظ: $_selectedBranchId');
        }
      });
    }
  }

  // هذه الدالة لم تعد مستخدمة حيث تم نقل وظيفتها إلى _performLoginInBackground
  // Future<void> _saveCredentials() async {
  //   if (_rememberMe) {
  //     final prefs = await SharedPreferences.getInstance();
  //     await prefs.setString('email', _emailController.text);
  //     await prefs.setString('password', _passwordController.text);
  //     await prefs.setBool('rememberMe', true);
  //   } else {
  //     final prefs = await SharedPreferences.getInstance();
  //     await prefs.remove('email');
  //     await prefs.remove('password');
  //     await prefs.setBool('rememberMe', false);
  //   }
  // }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // استدعاء معالجة الوسيطات عند تغيير التبعيات
    // هذا يضمن استدعاء الدالة بعد بناء الشاشة وعند تغيير الوسيطات
    _processArguments();
  }

  @override
  void dispose() {
    _usernameOrEmailController.dispose();
    _passwordController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل قائمة الفروع
  Future<void> _loadBranches() async {
    setState(() {
      _isLoadingBranches = true;
    });

    try {
      AppLogger.info('بدء تحميل الفروع...');

      final branchService = BranchService();
      final branches = await branchService.getBranches();

      AppLogger.info('تم استرجاع ${branches.length} فرع من الخدمة');

      // طباعة تفاصيل الفروع للتشخيص
      for (var branch in branches) {
        AppLogger.info(
            'فرع: ${branch.name}, معرف: ${branch.id}, رئيسي: ${branch.isMain}');
      }

      setState(() {
        _branches = branches;
        _isLoadingBranches = false;

        // تحديد الفرع الرئيسي كافتراضي
        if (branches.isNotEmpty) {
          final mainBranch = branches.firstWhere(
            (branch) => branch.isMain,
            orElse: () => branches.first,
          );

          _selectedBranchId = mainBranch.id;
          AppLogger.info(
              'تم تحديد الفرع: ${mainBranch.name} (${mainBranch.id})');
        } else {
          AppLogger.warning('لا توجد فروع متاحة! إنشاء فرع افتراضي للعرض فقط');
          _branches = [Branch(id: 'default', name: 'الفرع الرئيسي')];
          _selectedBranchId = 'default';
        }
      });
    } catch (e) {
      AppLogger.error('خطأ في تحميل الفروع: $e');
      setState(() {
        _isLoadingBranches = false;
        // إضافة فرع افتراضي في حالة الخطأ
        _branches = [Branch(id: 'default', name: 'الفرع الرئيسي')];
        _selectedBranchId = 'default';
      });
    }
  }

  /// تحميل قائمة الأدوار
  Future<void> _loadRoles() async {
    if (!mounted) return;

    setState(() {
      _isLoadingRoles = true;
    });

    try {
      AppLogger.info('بدء تحميل الأدوار...');

      // التحقق من وجود السياق قبل استخدامه
      if (!mounted) return;

      await _permissionPresenter.loadRoles();

      // التحقق مرة أخرى بعد العملية غير المتزامنة
      if (!mounted) return;

      final roles = _permissionPresenter.roles;

      AppLogger.info('تم استرجاع ${roles.length} دور من الخدمة');

      setState(() {
        _roles = roles;
        _isLoadingRoles = false;
      });
    } catch (e) {
      AppLogger.error('خطأ في تحميل الأدوار: $e');

      // التحقق من وجود السياق قبل استخدامه
      if (!mounted) return;

      setState(() {
        _isLoadingRoles = false;
        // إضافة أدوار افتراضية في حالة الخطأ
        _roles = [
          UserRole(
            id: 'admin',
            name: 'admin',
            displayName: 'مدير النظام',
            permissions: [],
          ),
          UserRole(
            id: 'manager',
            name: 'manager',
            displayName: 'مدير',
            permissions: [],
          ),
          UserRole(
            id: 'accountant',
            name: 'accountant',
            displayName: 'محاسب',
            permissions: [],
          ),
          UserRole(
            id: 'cashier',
            name: 'cashier',
            displayName: 'أمين صندوق',
            permissions: [],
          ),
          UserRole(
            id: 'user',
            name: 'user',
            displayName: 'مستخدم',
            permissions: [],
          ),
          UserRole(
            id: 'guest',
            name: 'guest',
            displayName: 'زائر',
            permissions: [],
          ),
        ];
      });
    }
  }

  /// عرض معلومات الأدوار
  void _showRolesInfo() {
    // تحميل الأدوار إذا لم تكن محملة بالفعل
    if (_roles.isEmpty && !_isLoadingRoles) {
      _loadRoles();
    }

    // استخدام نسخة محلية من الأدوار لتجنب مشاكل البناء
    final currentRoles = List<UserRole>.from(_roles);

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.shield,
                color: Theme.of(dialogContext).colorScheme.primary),
            const SizedBox(width: 8),
            const Text('معلومات الأدوار'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: _isLoadingRoles
              ? const Center(child: AkLoadingIndicator())
              : ListView(
                  shrinkWrap: true,
                  children: [
                    Text(
                      'يوفر النظام عدة أدوار مختلفة للمستخدمين، كل دور له صلاحيات محددة:',
                      style: Theme.of(dialogContext)
                          .textTheme
                          .titleSmall
                          ?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                    ..._buildRoleInfoItemsWithRoles(
                        currentRoles, dialogContext),
                  ],
                ),
        ),
        actions: [
          AkTextButton(
            text: 'إغلاق',
            onPressed: () => Navigator.pop(dialogContext),
            type: AkButtonType.secondary,
          ),
        ],
      ),
    );
  }

  /// بناء قائمة معلومات الأدوار باستخدام قائمة أدوار محددة
  List<Widget> _buildRoleInfoItemsWithRoles(
      List<UserRole> roles, BuildContext dialogContext) {
    final List<Widget> items = [];
    final theme = Theme.of(dialogContext);

    // إذا كانت قائمة الأدوار فارغة، استخدم الأدوار الافتراضية
    final rolesList = roles.isEmpty
        ? [
            UserRole(
              id: 'admin',
              name: 'admin',
              displayName: 'مدير النظام',
              permissions: [],
            ),
            UserRole(
              id: 'manager',
              name: 'manager',
              displayName: 'مدير',
              permissions: [],
            ),
            UserRole(
              id: 'accountant',
              name: 'accountant',
              displayName: 'محاسب',
              permissions: [],
            ),
            UserRole(
              id: 'cashier',
              name: 'cashier',
              displayName: 'أمين صندوق',
              permissions: [],
            ),
            UserRole(
              id: 'user',
              name: 'user',
              displayName: 'مستخدم',
              permissions: [],
            ),
            UserRole(
              id: 'guest',
              name: 'guest',
              displayName: 'زائر',
              permissions: [],
            ),
          ]
        : roles;

    // وصف كل دور
    final Map<String, String> roleDescriptions = {
      'admin':
          'يمتلك جميع الصلاحيات في النظام ويمكنه إدارة المستخدمين والإعدادات.',
      'manager': 'يمكنه إدارة العمليات اليومية والتقارير والموظفين.',
      'accountant': 'يمكنه إدارة الحسابات والفواتير والتقارير المالية.',
      'cashier': 'يمكنه إدارة المبيعات والمشتريات وعمليات الدفع.',
      'user': 'يمكنه استخدام النظام بصلاحيات محدودة حسب المجموعة.',
      'guest': 'يمكنه استعراض بعض البيانات فقط دون إجراء أي تعديلات.',
    };

    // إنشاء عنصر لكل دور
    for (final role in rolesList) {
      final roleId = role.id.toLowerCase();
      final description =
          roleDescriptions[roleId] ?? 'دور مخصص بصلاحيات محددة.';
      final roleColor = _getRoleColor(roleId, theme);

      items.add(
        AkCard(
          margin: const EdgeInsets.only(bottom: AppDimensions.spacing8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: roleColor.withValues(alpha: 0.2),
              child: Icon(Icons.shield, color: roleColor),
            ),
            title: Text(
              role.displayName,
              style: AppTypography(
                fontWeight: FontWeight.bold,
                color: DynamicColors.textPrimary(context),
                fontSize: AppTypography.fontSizeMedium,
              ),
            ),
            subtitle: Text(
              description,
              style: AppTypography(
                color: DynamicColors.textSecondary(context),
                fontSize: AppTypography.fontSizeSmall,
              ),
            ),
          ),
        ),
      );
    }

    return items;
  }

  /// الحصول على لون الدور
  Color _getRoleColor(String role, ThemeData theme) {
    switch (role.toLowerCase()) {
      case 'admin':
        return theme.colorScheme.primary;
      case 'manager':
        return theme.colorScheme.secondary; // Changed from primaryLight
      case 'accountant':
        return theme.colorScheme.tertiary; // Changed from AppColors.accent
      case 'cashier':
        return theme.colorScheme
            .primaryContainer; // Changed from AppColors.success, using a theme color
      case 'user':
        return theme.colorScheme.onSurface
            .withValues(alpha: 0.6); // Changed from AppColors.secondary
      case 'guest':
        return theme.colorScheme.onSurface
            //.withValues(alpha:0.4); // Changed from AppColors.secondary
            .withValues(alpha: 0.4); // Changed from AppColors.secondary
      default:
        return theme.colorScheme.primary; // Changed from primaryDark
    }
  }

  void _handleLogin() async {
    if (_formKey.currentState?.validate() ?? false) {
      // التحقق من اختيار الفرع
      if (_selectedBranchId == null || _selectedBranchId!.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('الرجاء اختيار الفرع'),
              backgroundColor: AppColors.error,
            ),
          );
        }
        return;
      }

      try {
        // عرض مؤشر التحميل
        _showLoadingDialog();

        // حفظ بيانات الدخول إذا تم اختيار تذكرني
        if (_rememberMe) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(
              'usernameOrEmail', _usernameOrEmailController.text);
          await prefs.setString('password', _passwordController.text);
          await prefs.setString('branchId', _selectedBranchId!);
          await prefs.setBool('rememberMe', true);
        } else {
          final prefs = await SharedPreferences.getInstance();
          await prefs.remove('usernameOrEmail');
          await prefs.remove('password');
          await prefs.remove('branchId');
          await prefs.setBool('rememberMe', false);
        }

        // Guardar que el usuario ha iniciado sesión exitosamente
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('hasLoggedInBefore', true);

        // استخدام خدمة المصادقة الموحدة
        final authService = AuthService();
        final user = await authService.login(
          _usernameOrEmailController.text, // اسم المستخدم أو البريد الإلكتروني
          _passwordController.text, // كلمة المرور
          branchId: _selectedBranchId, // معرف الفرع
        );

        if (user == null) {
          throw Exception('فشل في تسجيل الدخول. تأكد من صحة بيانات الاعتماد.');
        }

        // تسجيل الدخول في مدير الجلسة
        final bool loginSuccess = await SessionManager.login(
          user.id, // معرف المستخدم
          user.username, // اسم المستخدم
          user.roleId ?? 'user', // دور المستخدم (استخدام 'user' كقيمة افتراضية)
          branchId: _selectedBranchId,
        );

        if (!loginSuccess) {
          throw Exception('فشل في حفظ بيانات تسجيل الدخول');
        }

        // تحديث حالة الإعداد في الملف المحلي أيضًا
        final bool fileSetupSaved =
            await SetupFlagService.setSetupCompleted(true);
        final bool fileFirstLaunchSaved =
            await SetupFlagService.setFirstLaunch(false);

        // التحقق من نجاح حفظ الإعدادات في الملف المحلي
        if (!fileSetupSaved || !fileFirstLaunchSaved) {
          AppLogger.error(
              '❌ فشل في حفظ حالة الإعداد في الملف المحلي عند تسجيل الدخول: fileSetupSaved=$fileSetupSaved, fileFirstLaunchSaved=$fileFirstLaunchSaved');
          throw Exception('فشل في حفظ حالة الإعداد في الملف المحلي');
        } else {
          AppLogger.info(
              '✅ تم حفظ حالة الإعداد في الملف المحلي بنجاح عند تسجيل الدخول');
        }

        AppLogger.info(
            'تم تسجيل الدخول بنجاح: ${user.username} (${user.fullName}) في الفرع: $_selectedBranchId');

        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();
        }

        // انتقال إلى لوحة المعلومات (الداش بورد)
        if (mounted) {
          Navigator.of(context).pushReplacementNamed(AppRoutes.dashboard);
        }
      } catch (e) {
        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();

          // عرض رسالة خطأ
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل تسجيل الدخول: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }

        AppLogger.error('خطأ في تسجيل الدخول: $e');
        ErrorTracker.captureError(
          'فشل في تسجيل الدخول',
          error: e,
          stackTrace: StackTrace.current,
          context: {
            'username': _usernameOrEmailController.text,
            'branchId': _selectedBranchId,
          },
        );
      }
    }
  }

  // عرض مؤشر التحميل
  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: AkLoadingIndicator(),
      ),
    );
  }

  void _handleGuestLogin() async {
    // التحقق من اختيار الفرع
    if (_selectedBranchId == null || _selectedBranchId!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('الرجاء اختيار الفرع'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    try {
      // عرض مؤشر التحميل
      _showLoadingDialog();

      // إنشاء مستخدم زائر مؤقت
      final guestUser = User(
        id: 'guest',
        username: 'guest',
        password: '',
        fullName: 'زائر',
        email: '',
        phone: '',
        roleId: 'guest',
        userGroupId: 'guest',
        isActive: true,
      );

      // تسجيل الدخول كزائر
      final bool loginSuccess = await SessionManager.login(
        guestUser.id, // معرف المستخدم
        guestUser.username, // اسم المستخدم
        guestUser.roleId ?? 'guest', // دور المستخدم
        branchId: _selectedBranchId, // إضافة معرف الفرع
      );

      if (!loginSuccess) {
        throw Exception('فشل في حفظ بيانات تسجيل الدخول كزائر');
      }

      // تحديث حالة الإعداد في الملف المحلي أيضًا
      final bool fileSetupSaved =
          await SetupFlagService.setSetupCompleted(true);
      final bool fileFirstLaunchSaved =
          await SetupFlagService.setFirstLaunch(false);

      // التحقق من نجاح حفظ الإعدادات في الملف المحلي
      if (!fileSetupSaved || !fileFirstLaunchSaved) {
        AppLogger.error(
            '❌ فشل في حفظ حالة الإعداد في الملف المحلي عند تسجيل الدخول كزائر: fileSetupSaved=$fileSetupSaved, fileFirstLaunchSaved=$fileFirstLaunchSaved');
        throw Exception('فشل في حفظ حالة الإعداد في الملف المحلي');
      } else {
        AppLogger.info(
            '✅ تم حفظ حالة الإعداد في الملف المحلي بنجاح عند تسجيل الدخول كزائر');
      }

      // حفظ معرف الفرع في خدمة المصادقة
      final authService = AuthService();
      await authService.setCurrentBranch(_selectedBranchId!);

      AppLogger.info(
          'تم تسجيل الدخول كزائر بنجاح في الفرع: $_selectedBranchId');

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      // انتقال إلى لوحة المعلومات (الداش بورد)
      if (mounted) {
        Navigator.of(context).pushReplacementNamed(AppRoutes.dashboard);
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();

        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل تسجيل الدخول كزائر: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }

      AppLogger.error('خطأ في تسجيل الدخول كزائر: $e');
      ErrorTracker.captureError(
        'فشل في تسجيل الدخول كزائر',
        error: e,
        stackTrace: StackTrace.current,
        context: {
          'branchId': _selectedBranchId,
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = DynamicColors.isDarkMode(context);

    return Scaffold(
      backgroundColor: DynamicColors.background(context),
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: isDark
                    ? AppColors.darkGradient
                    : [
                        DynamicColors.surface(context),
                        DynamicColors.surfaceVariant(context),
                      ],
              ),
            ),
            child: SafeArea(
              child: LayoutBuilder(builder: (context, constraints) {
                return SingleChildScrollView(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.defaultMargin,
                      vertical: AppDimensions.mediumSpacing,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        SizedBox(height: AppDimensions.largeSpacing),
                        _buildAppLogo(constraints),
                        SizedBox(height: AppDimensions.largeSpacing),
                        Text(
                          'مرحباً بك في تاجر بلس',
                          style: AppTypography(
                            fontWeight: FontWeight.bold,
                            color: DynamicColors.primary,
                            fontSize: AppTypography.fontSizeLarge,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: AppDimensions.smallSpacing),
                        Text(
                          'برنامجك الشامل لإدارة الأعمال التجارية',
                          style: AppTypography(
                            color: DynamicColors.textSecondary(context),
                            fontSize: AppTypography.fontSizeMedium,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: AppDimensions.extraLargeSpacing),
                        AkCard(
                          child: Padding(
                            padding:
                                EdgeInsets.all(AppDimensions.defaultMargin),
                            child: Form(
                              key: _formKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: _buildUsernameField(),
                                  ),
                                  SizedBox(height: AppDimensions.mediumSpacing),
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: _buildPasswordField(),
                                  ),
                                  SizedBox(height: AppDimensions.mediumSpacing),
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: _buildBranchDropdown(),
                                  ),
                                  SizedBox(height: AppDimensions.largeSpacing),
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: _buildRememberMeRow(),
                                  ),
                                  SizedBox(height: AppDimensions.largeSpacing),
                                  _buildLoginButton(),
                                ],
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: AppDimensions.largeSpacing),
                        _buildSignUpRow(),
                        SizedBox(height: AppDimensions.mediumSpacing),
                        _buildGuestLoginButton(),
                        SizedBox(height: AppDimensions.mediumSpacing),
                        _buildRolesInfoButton(),
                        SizedBox(height: AppDimensions.largeSpacing),
                        _buildSocialButtonsRow(),
                      ],
                    ));
              }),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حقل اسم المستخدم أو البريد الإلكتروني باستخدام مكونات AK الموحدة
  Widget _buildUsernameField() {
    return AkTextInput(
      controller: _usernameOrEmailController,
      label: 'اسم المستخدم أو البريد الإلكتروني',
      hint: 'أدخل اسم المستخدم أو البريد الإلكتروني',
      prefixIcon: Icons.person,
      keyboardType: TextInputType.text,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'الرجاء إدخال اسم المستخدم أو البريد الإلكتروني';
        }
        return null;
      },
    );
  }

  /// بناء حقل كلمة المرور باستخدام مكونات AK الموحدة
  Widget _buildPasswordField() {
    return AkPasswordInput(
      controller: _passwordController,
      label: 'كلمة المرور',
      hint: 'أدخل كلمة المرور',
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'الرجاء إدخال كلمة المرور';
        }
        if (value.length < 6) {
          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        return null;
      },
    );
  }

  /// بناء صف تذكرني ونسيت كلمة المرور باستخدام مكونات AK الموحدة
  Widget _buildRememberMeRow() {
    return Row(
      children: [
        /// مربع اختيار تذكرني مع تحجيم متجاوب
        Transform.scale(
          scale: 0.9,
          child: Checkbox(
            value: _rememberMe,
            activeColor: DynamicColors.primary,
            onChanged: (value) {
              setState(() {
                _rememberMe = value ?? false;
              });
            },
          ),
        ),

        /// نص تذكرني
        Text(
          'تذكرني',
          style: AppTypography(
            color: DynamicColors.textSecondary(context),
            fontSize: AppTypography.fontSizeMedium,
          ),
        ),
        const Spacer(),

        /// زر نسيت كلمة المرور
        AkTextButton(
          text: 'نسيت كلمة المرور؟',
          onPressed: () {
            /// عرض حوار نسيت كلمة المرور
            _showForgotPasswordDialog();
          },
          type: AkButtonType.primary,
        ),
      ],
    );
  }

  /// عرض حوار نسيت كلمة المرور
  void _showForgotPasswordDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.help_outline, color: DynamicColors.primary),
            SizedBox(width: AppDimensions.smallSpacing),
            Text(
              'نسيت كلمة المرور؟',
              style: AppTypography(
                color: DynamicColors.textPrimary(context),
                fontSize: AppTypography.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Text(
          'يرجى التواصل مع مدير النظام لإعادة تعيين كلمة المرور الخاصة بك.',
          style: AppTypography(
            color: DynamicColors.textSecondary(context),
            fontSize: AppTypography.fontSizeMedium,
          ),
        ),
        actions: [
          AkTextButton(
            text: 'إغلاق',
            onPressed: () => Navigator.of(context).pop(),
            type: AkButtonType.secondary,
          ),
        ],
      ),
    );
  }

  /// بناء حقل الفرع باستخدام AkDropdownInput الموحد
  Widget _buildBranchDropdown() {
    /// تحديد الفرع الرئيسي كافتراضي إذا لم يكن محدد
    if (_branches.isNotEmpty && _selectedBranchId == null) {
      final mainBranch = _branches.firstWhere(
        (branch) => branch.isMain,
        orElse: () => _branches.first,
      );
      _selectedBranchId = mainBranch.id;
    }

    /// إذا لم تكن هناك فروع، استخدم فرع افتراضي
    if (_branches.isEmpty) {
      _branches = [Branch(id: 'default', name: 'الفرع الرئيسي')];
      _selectedBranchId = 'default';
    }

    /// إذا كان هناك فرع واحد فقط، اعرضه كحقل للقراءة فقط
    if (_branches.length == 1) {
      return AkTextInput(
        label: 'الفرع',
        controller: TextEditingController(text: _branches.first.name),
        prefixIcon: Icons.store,
        readOnly: true,
        hint: 'الفرع المحدد',
      );
    }

    /// إذا كان هناك أكثر من فرع، اعرض قائمة منسدلة تفاعلية
    return _isLoadingBranches
        ? Container(
            height: 56, // نفس ارتفاع AkTextInput
            decoration: BoxDecoration(
              border: Border.all(color: DynamicColors.border(context)),
              borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
            ),
            child: const Center(
              child: AkLoadingIndicator(),
            ),
          )
        : AkDropdownInput<String>(
            label: 'الفرع',
            value: _selectedBranchId,
            hint: 'اختر الفرع',
            prefixIcon: Icons.store,
            isRequired: true,
            items: _branches.map((branch) {
              return DropdownMenuItem<String>(
                value: branch.id,
                child: Text(
                  branch.name,
                  style: AppTypography(
                    color: DynamicColors.textPrimary(context),
                    fontSize: AppTypography.fontSizeMedium,
                  ),
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedBranchId = value;
              });
              AppLogger.info('تم تغيير الفرع إلى: $value');
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'الرجاء اختيار الفرع';
              }
              return null;
            },
          );
  }

  /// بناء زر تسجيل الدخول باستخدام مكونات AK الموحدة
  Widget _buildLoginButton() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: AkButton(
        text: 'تسجيل الدخول',
        onPressed: _handleLogin,
        type: AkButtonType.primary,
        size: AkButtonSize.medium, // تغيير من large إلى medium لحجم خط مناسب
        icon: Icons.login,
        isFullWidth: true, // جعل الزر يأخذ العرض الكامل للتناسق
      ),
    );
  }

  /// بناء صف إنشاء حساب جديد باستخدام مكونات AK الموحدة
  Widget _buildSignUpRow() {
    /// إذا كان زر التسجيل مخفي، لا تعرض شيئاً
    if (!_showRegisterButton) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'ليس لديك حساب؟',
          style: AppTypography(
            color: DynamicColors.textSecondary(context),
            fontSize: AppTypography.fontSizeMedium,
          ),
        ),
        AkTextButton(
          text: 'إنشاء حساب جديد',
          onPressed: () {
            Navigator.of(context).pushNamed('/register');
          },
          type: AkButtonType.primary,
        ),
      ],
    );
  }

  /// بناء زر الدخول كزائر باستخدام مكونات AK الموحدة
  Widget _buildGuestLoginButton() {
    return AkTextButton(
      text: 'الدخول كزائر',
      onPressed: _handleGuestLogin,
      type: AkButtonType.secondary,
      icon: Icons.person_outline,
    );
  }

  /// بناء زر معلومات الأدوار والصلاحيات باستخدام مكونات AK الموحدة
  Widget _buildRolesInfoButton() {
    return AkTextButton(
      text: 'معلومات الأدوار والصلاحيات',
      onPressed: _showRolesInfo,
      type: AkButtonType.info,
      icon: Icons.info_outline,
    );
  }

  /// بناء صف أزرار الشبكات الاجتماعية مع الألوان الثابتة للعلامات التجارية
  Widget _buildSocialButtonsRow() {
    const buttonSize = 30.0;

    return Column(
      children: [
        Text(
          'أو تواصل معنا عبر:',
          style: AppTypography(
            fontSize: AppTypography.fontSizeMedium,
            color: DynamicColors.textSecondary(context),
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppDimensions.mediumSpacing),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            /// زر فيسبوك - لون ثابت للعلامة التجارية
            _buildSocialButton(
              icon: FontAwesomeIcons.facebook,
              color: const Color(0xFF1877F2), // لون فيسبوك الرسمي
              size: buttonSize,
              onTap: () {
                _launchUrl('https://www.facebook.com');
              },
            ),
            SizedBox(width: AppDimensions.defaultMargin),

            /// زر واتساب - لون ثابت للعلامة التجارية
            _buildSocialButton(
              icon: FontAwesomeIcons.whatsapp,
              color: const Color(0xFF25D366), // لون واتساب الرسمي
              size: buttonSize,
              onTap: () {
                _openWhatsApp();
              },
            ),
            SizedBox(width: AppDimensions.defaultMargin),

            /// زر جوجل - لون ثابت للعلامة التجارية
            _buildSocialButton(
              icon: FontAwesomeIcons.google,
              color: const Color(0xFF4285F4), // لون جوجل الرسمي
              size: buttonSize,
              onTap: () {
                _launchUrl('https://www.google.com');
              },
            ),
          ],
        ),
      ],
    );
  }

  /// فتح رابط في المتصفح
  Future<void> _launchUrl(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        AppLogger.error('لا يمكن فتح الرابط: $url');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('لا يمكن فتح الرابط: $url'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في فتح الرابط: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الرابط: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// فتح واتساب مع رسالة
  Future<void> _openWhatsApp() async {
    try {
      const String phoneNumber = '967770119544';
      const String message =
          'مرحباً، أنا مستخدم لتطبيق تاجر بلس وأحتاج إلى مساعدة.';

      // تشفير الرسالة
      final String encodedMessage = Uri.encodeComponent(message);

      // إنشاء رابط واتساب
      final String whatsappUrl =
          'https://wa.me/$phoneNumber?text=$encodedMessage';

      // فتح الرابط
      await _launchUrl(whatsappUrl);
    } catch (e) {
      AppLogger.error('خطأ في فتح واتساب: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح واتساب: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// بناء شعار التطبيق مع تصميم متجاوب وألوان ديناميكية
  Widget _buildAppLogo(BoxConstraints constraints) {
    /// حساب الحجم المناسب بناءً على عرض الشاشة
    final logoSize = constraints.maxWidth * 0.28; // 28% من عرض الشاشة
    final iconSize = logoSize * 0.25; // 25% من حجم الشعار

    return Center(
      child: Container(
        width: logoSize,
        height: logoSize,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              DynamicColors.primary, // لون ديناميكي يتكيف مع الثيم
              AppColors.secondary, // لون ثابت للتدرج
            ],
          ),
          borderRadius: BorderRadius.circular(AppDimensions.largeRadius),
          boxShadow: [
            BoxShadow(
              color: DynamicColors.primary.withValues(alpha: 0.15),
              blurRadius: 8,
              spreadRadius: 1,
              offset: const Offset(0, 4),
            )
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            /// الصف الأول من الأيقونات
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_cart,
                  size: iconSize,
                  color: Colors.white, // أبيض ثابت للوضوح على التدرج
                ),
                SizedBox(width: iconSize * 0.3),
                Icon(
                  Icons.store,
                  size: iconSize,
                  color: Colors.white, // أبيض ثابت للوضوح على التدرج
                ),
              ],
            ),
            SizedBox(height: iconSize * 0.5),

            /// الصف الثاني من الأيقونات
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inventory,
                  size: iconSize,
                  color: Colors.white, // أبيض ثابت للوضوح على التدرج
                ),
                SizedBox(width: iconSize * 0.3),
                Icon(
                  Icons.people,
                  size: iconSize,
                  color: Colors.white, // أبيض ثابت للوضوح على التدرج
                ),
              ],
            ),
            SizedBox(height: iconSize * 0.3),

            /// نص اسم التطبيق
            Text(
              'تاجر بلس',
              style: AppTypography(
                fontSize: iconSize * 0.6,
                fontWeight: FontWeight.bold,
                color: Colors.white, // أبيض ثابت للوضوح على التدرج
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر شبكة اجتماعية مع تصميم موحد وألوان ثابتة للعلامات التجارية
  Widget _buildSocialButton({
    required IconData icon,
    required Color color, // لون ثابت للعلامة التجارية
    required double size,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
      child: Container(
        padding: EdgeInsets.all(size * 0.4),
        decoration: BoxDecoration(
          color:
              color.withValues(alpha: 0.1), // خلفية شفافة بلون العلامة التجارية
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          border: Border.all(
            color: color.withValues(
                alpha: 0.3), // حدود شفافة بلون العلامة التجارية
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: color, // لون العلامة التجارية الثابت
          size: size,
        ),
      ),
    );
  }
}
