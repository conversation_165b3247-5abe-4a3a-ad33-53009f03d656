import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/models/purchase.dart';
import '../presenters/purchase_presenter.dart';
import '../../../core/widgets/index.dart';
import 'purchase_form_screen.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

/// شاشة عرض تفاصيل فاتورة المشتريات
class PurchaseDetailsScreen extends StatefulWidget {
  final Purchase purchase;

  const PurchaseDetailsScreen({Key? key, required this.purchase})
      : super(key: key);

  @override
  State<PurchaseDetailsScreen> createState() => _PurchaseDetailsScreenState();
}

class _PurchaseDetailsScreenState extends State<PurchaseDetailsScreen> {
  late Purchase _purchase;
  late PurchasePresenter _purchasePresenter;

  @override
  void initState() {
    super.initState();
    _purchase = widget.purchase;
    _purchasePresenter = AppProviders.getLazyPresenter<PurchasePresenter>(
        () => PurchasePresenter());
  }

  @override
  Widget build(BuildContext context) {
    // سيتم استخدام هذا المتغير في المستقبل عند تنفيذ وضع الألوان الداكنة
    // final isDark = Theme.of(context).brightness == Brightness.dark;
    final isCredit = _purchase.isCredit;
    final isPaid = _purchase.isPaid;
    final isPartiallyPaid = _purchase.isPartiallyPaid;

    Color statusColor;
    if (_purchase.status == 'cancelled') {
      statusColor = AppColors.statusCancelled;
    } else if (isCredit && !isPaid) {
      statusColor = AppColors.error;
    } else if (isPartiallyPaid) {
      statusColor = AppColors.warning;
    } else {
      statusColor = AppColors.success;
    }

    return Scaffold(
      appBar: AkAppBar(
        title: 'تفاصيل فاتورة المشتريات',
        showBackButton: true,
        actions: [
          if (_purchase.status != 'cancelled')
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _editPurchase,
            ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: _handleMenuItemSelected,
            itemBuilder: (context) => [
              const PopupMenuItem<String>(
                value: 'print',
                child: Row(
                  children: [
                    Icon(Icons.print),
                    SizedBox(width: 8),
                    Text('طباعة'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('مشاركة'),
                  ],
                ),
              ),
              if (_purchase.status != 'cancelled')
                const PopupMenuItem<String>(
                  value: 'cancel',
                  child: Row(
                    children: [
                      Icon(Icons.cancel),
                      SizedBox(width: 8),
                      Text('إلغاء الفاتورة'),
                    ],
                  ),
                ),
              if (_purchase.status == 'cancelled')
                const PopupMenuItem<String>(
                  value: 'activate',
                  child: Row(
                    children: [
                      Icon(Icons.restore),
                      SizedBox(width: 8),
                      Text('تنشيط الفاتورة'),
                    ],
                  ),
                ),
              const PopupMenuItem<String>(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: AppColors.error),
                    SizedBox(width: 8),
                    Text('حذف', style: AppTypography(color: AppColors.error)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPurchaseHeader(statusColor),
            const SizedBox(height: 16),
            _buildSupplierInfo(),
            const SizedBox(height: 16),
            _buildItemsTable(),
            const SizedBox(height: 16),
            _buildTotalsSection(),
            if (_purchase.notes != null && _purchase.notes!.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildNotesSection(),
            ],
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _printPurchase,
                icon: const Icon(Icons.print),
                label: const Text('طباعة'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _sharePurchase,
                icon: const Icon(Icons.share),
                label: const Text('مشاركة'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPurchaseHeader(Color statusColor) {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'فاتورة مشتريات رقم [${_purchase.referenceNumber}]',
                    style: const AppTypography(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(
                        alpha: 0.1), // استخدام withValues بدلاً من withOpacity
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.circle, size: 10, color: statusColor),
                      const SizedBox(width: 6),
                      Text(
                        _purchase.status == 'cancelled'
                            ? 'ملغاة'
                            : _purchase.paymentStatusText,
                        style: AppTypography(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: statusColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildInfoItem(
                  Icons.calendar_today,
                  'التاريخ',
                  '${_purchase.date.day}/${_purchase.date.month}/${_purchase.date.year}',
                ),
                const SizedBox(width: 24),
                _buildInfoItem(
                  Icons.access_time,
                  'الوقت',
                  '${_purchase.date.hour}:${_purchase.date.minute.toString().padLeft(2, '0')}',
                ),
                const SizedBox(width: 24),
                _buildInfoItem(
                  Icons.payment,
                  'طريقة الدفع',
                  _purchase.paymentType == 'cash' ? 'نقدي' : 'آجل',
                ),
              ],
            ),
            if (_purchase.isCredit && _purchase.dueDate != null) ...[
              const SizedBox(height: 12),
              _buildInfoItem(
                Icons.event,
                'تاريخ الاستحقاق',
                '${_purchase.dueDate!.day}/${_purchase.dueDate!.month}/${_purchase.dueDate!.year}',
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: AppColors.lightSurfaceVariant),
        const SizedBox(width: 4),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const AppTypography(
                fontSize: 12,
                color: AppColors.lightTextSecondary,
              ),
            ),
            Text(
              value,
              style: const AppTypography(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSupplierInfo() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات المورد',
              style: AppTypography(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.person,
                    size: 16, color: AppColors.lightSurfaceVariant),
                const SizedBox(width: 8),
                Text(
                  _purchase.supplierName ?? 'غير محدد',
                  style: const AppTypography(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            // Información de contacto del proveedor se obtiene de metadata
            if (_purchase.metadata != null &&
                _purchase.metadata!['supplier_phone'] != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.phone,
                      size: 16, color: AppColors.lightSurfaceVariant),
                  const SizedBox(width: 8),
                  Text(_purchase.metadata!['supplier_phone']),
                ],
              ),
            ],
            if (_purchase.metadata != null &&
                _purchase.metadata!['supplier_address'] != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.location_on,
                      size: 16, color: AppColors.lightSurfaceVariant),
                  const SizedBox(width: 8),
                  Expanded(
                      child: Text(_purchase.metadata!['supplier_address'])),
                ],
              ),
            ],
            if (_purchase.warehouseName != null) ...[
              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 12),
              Row(
                children: [
                  const Icon(Icons.warehouse,
                      size: 16, color: AppColors.lightSurfaceVariant),
                  const SizedBox(width: 8),
                  Text(
                    'المستودع: ${_purchase.warehouseName}',
                    style: const AppTypography(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildItemsTable() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المنتجات',
              style: AppTypography(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 12),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columnSpacing: 20,
                headingTextStyle: const AppTypography(
                  fontWeight: FontWeight.bold,
                  color: AppColors.lightTextSecondary,
                ),
                dataRowColor: WidgetStateProperty.resolveWith<Color>(
                  (Set<WidgetState> states) {
                    if (states.contains(WidgetState.selected)) {
                      return AppColors.primary.withValues(
                          alpha:
                              0.08); // استخدام withValues بدلاً من withOpacity
                    }
                    return Colors.transparent;
                  },
                ),
                columns: const [
                  DataColumn(label: Text('#')),
                  DataColumn(label: Text('المنتج')),
                  DataColumn(label: Text('الوحدة')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('السعر')),
                  DataColumn(label: Text('الإجمالي')),
                ],
                rows: List<DataRow>.generate(
                  _purchase.items.length,
                  (index) {
                    final item = _purchase.items[index];
                    return DataRow(
                      cells: [
                        DataCell(Text('${index + 1}')),
                        DataCell(Text(item.productName ?? 'غير محدد')),
                        DataCell(Text(item.unitName ?? 'غير محدد')),
                        DataCell(Text('${item.quantity}')),
                        DataCell(Text('${item.price} ر.س')),
                        DataCell(Text('${item.total} ر.س')),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalsSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص الفاتورة',
              style: AppTypography(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 12),
            _buildTotalRow('المجموع الفرعي', '${_purchase.subtotal} ر.س'),
            if (_purchase.discount > 0) ...[
              const SizedBox(height: 8),
              _buildTotalRow('الخصم', '${_purchase.discount} ر.س'),
            ],
            if (_purchase.tax > 0) ...[
              const SizedBox(height: 8),
              _buildTotalRow('الضريبة', '${_purchase.tax} ر.س'),
            ],
            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 8),
            _buildTotalRow(
              'الإجمالي',
              '${_purchase.total} ر.س',
              isBold: true,
            ),
            if (_purchase.isPartiallyPaid || _purchase.isCredit) ...[
              const SizedBox(height: 8),
              _buildTotalRow(
                'المدفوع',
                '${_purchase.paid} ر.س',
                valueColor: AppColors.successDark,
              ),
              const SizedBox(height: 8),
              _buildTotalRow(
                'المتبقي',
                '${_purchase.remaining} ر.س',
                valueColor: AppColors.errorDark,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(
    String label,
    String value, {
    bool isBold = false,
    Color? valueColor,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTypography(
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: AppTypography(
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            color: valueColor,
          ),
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات',
              style: AppTypography(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 12),
            Text(_purchase.notes ?? ''),
          ],
        ),
      ),
    );
  }

  void _editPurchase() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PurchaseFormScreen(purchase: _purchase),
      ),
    ).then((result) {
      if (result == true) {
        _refreshPurchaseDetails();
      }
    });
  }

  void _handleMenuItemSelected(String value) {
    switch (value) {
      case 'print':
        _printPurchase();
        break;
      case 'share':
        _sharePurchase();
        break;
      case 'cancel':
        _showConfirmationDialog(
          'هل أنت متأكد من إلغاء هذه الفاتورة؟',
          () => _purchasePresenter.changePurchaseStatus(
              _purchase.id, 'cancelled'),
        );
        break;
      case 'activate':
        _showConfirmationDialog(
          'هل أنت متأكد من تنشيط هذه الفاتورة؟',
          () => _purchasePresenter.changePurchaseStatus(
              _purchase.id, 'completed'),
        );
        break;
      case 'delete':
        _showConfirmationDialog(
          'هل أنت متأكد من حذف هذه الفاتورة؟',
          () => _purchasePresenter.deletePurchase(_purchase.id),
        );
        break;
    }
  }

  void _printPurchase() {
    // تنفيذ عملية الطباعة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تنفيذ الطباعة...')),
    );
  }

  void _sharePurchase() {
    // تنفيذ عملية المشاركة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تنفيذ المشاركة...')),
    );
  }

  void _showConfirmationDialog(
      String message, Future<bool> Function() onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _executeConfirmedAction(onConfirm);
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  Future<void> _executeConfirmedAction(
      Future<bool> Function() onConfirm) async {
    final success = await onConfirm();

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تمت العملية بنجاح')),
      );
      if (_purchase.status == 'cancelled') {
        Navigator.pop(context);
      } else {
        _refreshPurchaseDetails();
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text(_purchasePresenter.error ?? 'حدث خطأ أثناء تنفيذ العملية'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _refreshPurchaseDetails() async {
    try {
      final updatedPurchase =
          await _purchasePresenter.getPurchaseById(_purchase.id);
      if (updatedPurchase != null) {
        setState(() {
          _purchase = updatedPurchase;
        });
      }
    } catch (e) {
      // يمكن تجاهل الخطأ هنا
    }
  }
}
