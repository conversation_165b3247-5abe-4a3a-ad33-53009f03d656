import 'dart:convert';
import 'package:uuid/uuid.dart';
import '../../../core/models/base_model.dart';

/// نموذج مجموعة المستخدمين
class UserGroup extends BaseModel {
  /// اسم المجموعة
  final String name;

  /// وصف المجموعة
  final String? description;

  /// رموز الصلاحيات
  final List<String> permissionCodes;

  /// حالة النشاط
  final bool isActive;

  /// إنشاء مجموعة مستخدمين جديدة
  UserGroup({
    String? id,
    required this.name,
    this.description,
    List<String>? permissionCodes,
    this.isActive = true,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  })  : permissionCodes = permissionCodes ?? [],
        super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// نسخ الكائن مع تعديل بعض الخصائص
  UserGroup copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? permissionCodes,
    bool? isActive,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return UserGroup(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      permissionCodes: permissionCodes ?? List.from(this.permissionCodes),
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل الكائن إلى Map
  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'name': name,
      'description': description,
      'permission_codes': jsonEncode(permissionCodes),
      'is_active': isActive ? 1 : 0,
    });
    return map;
  }

  /// إنشاء كائن من Map
  factory UserGroup.fromMap(Map<String, dynamic> map) {
    List<String> codes = [];

    // استخراج رموز الصلاحيات من الخريطة
    if (map['permission_codes'] != null) {
      try {
        final dynamic decodedCodes = map['permission_codes'] is String
            ? jsonDecode(map['permission_codes'])
            : map['permission_codes'];

        if (decodedCodes is List) {
          codes = List<String>.from(decodedCodes);
        }
      } catch (e) {
        // في حالة فشل فك الترميز، نستخدم قائمة فارغة
      }
    } else if (map['permissions'] != null) {
      // للتوافق مع البيانات القديمة
      try {
        final dynamic decodedPermissions = map['permissions'] is String
            ? jsonDecode(map['permissions'])
            : map['permissions'];

        if (decodedPermissions is Map) {
          // استخراج مفاتيح الصلاحيات المفعلة
          codes = decodedPermissions.entries
              .where((entry) => entry.value == true)
              .map((entry) => entry.key.toString())
              .toList();
        }
      } catch (e) {
        // في حالة فشل فك الترميز، نستخدم قائمة فارغة
      }
    }

    return UserGroup(
      id: map['id'],
      name: map['name'] ?? '',
      description: map['description'],
      permissionCodes: codes,
      isActive: map['is_active'] == 1,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل الكائن إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء كائن من JSON
  factory UserGroup.fromJson(String source) =>
      UserGroup.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'UserGroup(id: $id, name: $name, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserGroup && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
