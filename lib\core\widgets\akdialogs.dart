import 'package:flutter/material.dart';
import 'dart:async';
import '../theme/index.dart';

/// نظام الحوارات الموحد لتطبيق تاجر بلس
/// يحتوي على جميع أنواع الحوارات المستخدمة في التطبيق
///
/// **المميزات:**
/// - تصميم موحد ومتناسق لجميع الحوارات
/// - دعم كامل للوضع المظلم/الفاتح
/// - تأثيرات تفاعلية متقدمة
/// - تحميل كسول للعناصر الثقيلة
/// - دوال مساعدة سريعة
/// - تعليقات شاملة باللغة العربية

// ═══════════════════════════════════════════════════════════════════════════════
// ● الأنواع والثوابت
// ═══════════════════════════════════════════════════════════════════════════════

/// أنواع الحوارات المختلفة
enum AkDialogType {
  /// حوار عادي
  normal,

  /// حوار تحذير
  warning,

  /// حوار خطر
  danger,

  /// حوار نجاح
  success,

  /// حوار معلومات
  info,

  /// حوار تحميل
  loading,
}

/// أحجام الحوارات المختلفة
enum AkDialogSize {
  /// حوار صغير
  small,

  /// حوار متوسط
  medium,

  /// حوار كبير
  large,

  /// حوار كامل الشاشة
  fullScreen,
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● 1. حوار التأكيد الموحد (AkConfirmDialog)
// ═══════════════════════════════════════════════════════════════════════════════

/// حوار تأكيد موحد مع تصميم متناسق
///
/// **المميزات:**
/// - تصميم موحد لجميع حوارات التأكيد
/// - دعم أنواع مختلفة (عادي، تحذير، خطر)
/// - تأثيرات تفاعلية متقدمة
/// - دعم كامل للوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkConfirmDialog.show(
///   context: context,
///   title: 'تأكيد الحذف',
///   content: 'هل أنت متأكد من حذف هذا العنصر؟',
///   type: AkDialogType.danger,
///   onConfirm: () => deleteItem(),
/// )
/// ```
class AkConfirmDialog extends StatelessWidget {
  /// عنوان الحوار
  final String title;

  /// محتوى الحوار
  final String content;

  /// نص زر التأكيد
  final String confirmText;

  /// نص زر الإلغاء
  final String cancelText;

  /// دالة التأكيد
  final VoidCallback? onConfirm;

  /// دالة الإلغاء
  final VoidCallback? onCancel;

  /// نوع الحوار
  final AkDialogType type;

  /// أيقونة الحوار
  final IconData? icon;

  /// لون مخصص للحوار
  final Color? customColor;

  /// هل يمكن إغلاق الحوار بالضغط خارجه
  final bool barrierDismissible;

  const AkConfirmDialog({
    super.key,
    required this.title,
    required this.content,
    this.confirmText = 'تأكيد',
    this.cancelText = 'إلغاء',
    this.onConfirm,
    this.onCancel,
    this.type = AkDialogType.normal,
    this.icon,
    this.customColor,
    this.barrierDismissible = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // تحديد الألوان حسب النوع والوضع
    final iconColor = _getIconColor(isDark);
    final confirmButtonColor = _getConfirmButtonColor(isDark);

    return AlertDialog(
      backgroundColor: isDark ? AppColors.darkSurface : AppColors.lightSurface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
      ),
      elevation: AppDimensions.elevationHigh,
      title: Row(
        children: [
          if (icon != null) ...[
            Container(
              padding: EdgeInsets.all(AppDimensions.smallMargin),
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.smallRadius),
              ),
              child: Icon(
                icon,
                size: AppDimensions.mediumIconSize,
                color: iconColor,
              ),
            ),
            SizedBox(width: AppDimensions.defaultSpacing),
          ],
          Expanded(
            child: Text(
              title,
              style: AppTypography.createCustomStyle(
                fontSize: AppDimensions.largeFontSize,
                fontWeight: AppTypography.weightMedium,
                color: isDark
                    ? AppColors.darkTextPrimary
                    : AppColors.lightTextPrimary,
              ),
            ),
          ),
        ],
      ),
      content: Text(
        content,
        style: AppTypography.createCustomStyle(
          fontSize: AppDimensions.defaultFontSize,
          fontWeight: AppTypography.weightRegular,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            if (onCancel != null) {
              onCancel!();
            }
            Navigator.of(context).pop(false);
          },
          child: Text(
            cancelText,
            style: AppTypography.createCustomStyle(
              fontSize: AppDimensions.defaultFontSize,
              fontWeight: AppTypography.weightMedium,
              color: isDark
                  ? AppColors.darkTextSecondary
                  : AppColors.lightTextSecondary,
            ),
          ),
        ),
        SizedBox(width: AppDimensions.smallSpacing),
        ElevatedButton(
          onPressed: () {
            if (onConfirm != null) {
              onConfirm!();
            }
            Navigator.of(context).pop(true);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: confirmButtonColor,
            foregroundColor: AppColors.onPrimary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.smallRadius),
            ),
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.defaultMargin,
              vertical: AppDimensions.smallMargin,
            ),
          ),
          child: Text(
            confirmText,
            style: AppTypography.createCustomStyle(
              fontSize: AppDimensions.defaultFontSize,
              fontWeight: AppTypography.weightMedium,
              color: AppColors.onPrimary,
            ),
          ),
        ),
      ],
    );
  }

  /// الحصول على لون الأيقونة حسب النوع
  Color _getIconColor(bool isDark) {
    if (customColor != null) return customColor!;

    switch (type) {
      case AkDialogType.normal:
        return AppColors.primary;
      case AkDialogType.warning:
        return AppColors.warning;
      case AkDialogType.danger:
        return AppColors.error;
      case AkDialogType.success:
        return AppColors.success;
      case AkDialogType.info:
        return AppColors.info;
      case AkDialogType.loading:
        return AppColors.primary;
    }
  }

  /// الحصول على لون زر التأكيد حسب النوع
  Color _getConfirmButtonColor(bool isDark) {
    if (customColor != null) return customColor!;

    switch (type) {
      case AkDialogType.normal:
        return AppColors.primary;
      case AkDialogType.warning:
        return AppColors.warning;
      case AkDialogType.danger:
        return AppColors.error;
      case AkDialogType.success:
        return AppColors.success;
      case AkDialogType.info:
        return AppColors.info;
      case AkDialogType.loading:
        return AppColors.primary;
    }
  }

  /// عرض حوار التأكيد
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String content,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    AkDialogType type = AkDialogType.normal,
    IconData? icon,
    Color? customColor,
    bool barrierDismissible = true,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AkConfirmDialog(
        title: title,
        content: content,
        confirmText: confirmText,
        cancelText: cancelText,
        onConfirm: onConfirm,
        onCancel: onCancel,
        type: type,
        icon: icon ?? _getDefaultIcon(type),
        customColor: customColor,
        barrierDismissible: barrierDismissible,
      ),
    );
  }

  /// الحصول على الأيقونة الافتراضية حسب النوع
  static IconData _getDefaultIcon(AkDialogType type) {
    switch (type) {
      case AkDialogType.normal:
        return Icons.help_outline;
      case AkDialogType.warning:
        return Icons.warning_amber;
      case AkDialogType.danger:
        return Icons.error_outline;
      case AkDialogType.success:
        return Icons.check_circle_outline;
      case AkDialogType.info:
        return Icons.info_outline;
      case AkDialogType.loading:
        return Icons.hourglass_empty;
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● 2. حوار التنبيه الموحد (AkAlertDialog)
// ═══════════════════════════════════════════════════════════════════════════════

/// حوار تنبيه موحد مع تأثيرات متقدمة
///
/// **المميزات:**
/// - تصميم عصري مع تأثيرات بصرية
/// - دعم أنواع مختلفة من التنبيهات
/// - إغلاق تلقائي بعد مدة محددة
/// - تأثيرات انتقالية سلسة
/// - دعم كامل للوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkAlertDialog.success(
///   context: context,
///   title: 'تم الحفظ',
///   message: 'تم حفظ البيانات بنجاح',
///   duration: Duration(seconds: 3),
/// )
/// ```
class AkAlertDialog extends StatelessWidget {
  /// عنوان التنبيه
  final String title;

  /// رسالة التنبيه
  final String message;

  /// نوع التنبيه
  final AkDialogType type;

  /// أيقونة التنبيه
  final IconData? icon;

  /// لون مخصص
  final Color? customColor;

  /// هل يمكن إغلاق التنبيه
  final bool dismissible;

  /// أزرار إضافية
  final List<Widget>? actions;

  /// مدة الإغلاق التلقائي
  final Duration? autoDismissDuration;

  const AkAlertDialog({
    super.key,
    required this.title,
    required this.message,
    this.type = AkDialogType.info,
    this.icon,
    this.customColor,
    this.dismissible = true,
    this.actions,
    this.autoDismissDuration,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // تحديد الألوان حسب النوع والوضع
    final alertColor = _getAlertColor(isDark);
    final iconColor = _getIconColor(isDark);

    // إعداد الإغلاق التلقائي
    if (autoDismissDuration != null && dismissible) {
      Timer(autoDismissDuration!, () {
        if (Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
      });
    }

    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: AppDimensions.getResponsiveWidgetSize(80),
        ),
        decoration: BoxDecoration(
          color: isDark ? AppColors.darkSurface : AppColors.lightSurface,
          borderRadius: BorderRadius.circular(AppDimensions.largeRadius),
          boxShadow: [
            BoxShadow(
              color: isDark ? AppColors.darkShadow : AppColors.lightShadow,
              blurRadius: AppDimensions.elevationHigh * 2,
              spreadRadius: 0,
              offset: const Offset(0, 1) * AppDimensions.elevationHigh,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس التنبيه
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(AppDimensions.defaultMargin),
              decoration: BoxDecoration(
                color: alertColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.largeRadius),
                  topRight: Radius.circular(AppDimensions.largeRadius),
                ),
              ),
              child: Column(
                children: [
                  // أيقونة التنبيه
                  Container(
                    padding: EdgeInsets.all(AppDimensions.defaultMargin),
                    decoration: BoxDecoration(
                      color: alertColor.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon ?? AkConfirmDialog._getDefaultIcon(type),
                      size: AppDimensions.largeIconSize,
                      color: iconColor,
                    ),
                  ),
                  SizedBox(height: AppDimensions.defaultSpacing),
                  // عنوان التنبيه
                  Text(
                    title,
                    style: AppTypography.createCustomStyle(
                      fontSize: AppDimensions.largeFontSize,
                      fontWeight: AppTypography.weightMedium,
                      color: isDark
                          ? AppColors.darkTextPrimary
                          : AppColors.lightTextPrimary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            // محتوى التنبيه
            Padding(
              padding: EdgeInsets.all(AppDimensions.defaultMargin),
              child: Column(
                children: [
                  // رسالة التنبيه
                  Text(
                    message,
                    style: AppTypography.createCustomStyle(
                      fontSize: AppDimensions.defaultFontSize,
                      fontWeight: AppTypography.weightRegular,
                      color: isDark
                          ? AppColors.darkTextSecondary
                          : AppColors.lightTextSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  // الأزرار
                  if (actions != null && actions!.isNotEmpty) ...[
                    SizedBox(height: AppDimensions.defaultSpacing),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: actions!
                          .map((action) => Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppDimensions.smallSpacing,
                                ),
                                child: action,
                              ))
                          .toList(),
                    ),
                  ] else if (dismissible) ...[
                    SizedBox(height: AppDimensions.defaultSpacing),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: alertColor,
                        foregroundColor: AppColors.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppDimensions.smallRadius),
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.largeMargin,
                          vertical: AppDimensions.smallMargin,
                        ),
                      ),
                      child: Text(
                        'موافق',
                        style: AppTypography.createCustomStyle(
                          fontSize: AppDimensions.defaultFontSize,
                          fontWeight: AppTypography.weightMedium,
                          color: AppColors.onPrimary,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون التنبيه حسب النوع
  Color _getAlertColor(bool isDark) {
    if (customColor != null) return customColor!;

    switch (type) {
      case AkDialogType.normal:
        return AppColors.primary;
      case AkDialogType.warning:
        return AppColors.warning;
      case AkDialogType.danger:
        return AppColors.error;
      case AkDialogType.success:
        return AppColors.success;
      case AkDialogType.info:
        return AppColors.info;
      case AkDialogType.loading:
        return AppColors.primary;
    }
  }

  /// الحصول على لون الأيقونة حسب النوع
  Color _getIconColor(bool isDark) {
    if (customColor != null) return customColor!;

    switch (type) {
      case AkDialogType.normal:
        return AppColors.primary;
      case AkDialogType.warning:
        return AppColors.warning;
      case AkDialogType.danger:
        return AppColors.error;
      case AkDialogType.success:
        return AppColors.success;
      case AkDialogType.info:
        return AppColors.info;
      case AkDialogType.loading:
        return AppColors.primary;
    }
  }

  /// عرض تنبيه نجاح
  static Future<void> success({
    required BuildContext context,
    required String title,
    required String message,
    Duration? duration,
    List<Widget>? actions,
    bool dismissible = true,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: dismissible,
      builder: (context) => AkAlertDialog(
        title: title,
        message: message,
        type: AkDialogType.success,
        dismissible: dismissible,
        actions: actions,
        autoDismissDuration: duration ?? const Duration(seconds: 3),
      ),
    );
  }

  /// عرض تنبيه خطأ
  static Future<void> error({
    required BuildContext context,
    required String title,
    required String message,
    Duration? duration,
    List<Widget>? actions,
    bool dismissible = true,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: dismissible,
      builder: (context) => AkAlertDialog(
        title: title,
        message: message,
        type: AkDialogType.danger,
        dismissible: dismissible,
        actions: actions,
        autoDismissDuration: duration ?? const Duration(seconds: 4),
      ),
    );
  }

  /// عرض تنبيه تحذير
  static Future<void> warning({
    required BuildContext context,
    required String title,
    required String message,
    Duration? duration,
    List<Widget>? actions,
    bool dismissible = true,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: dismissible,
      builder: (context) => AkAlertDialog(
        title: title,
        message: message,
        type: AkDialogType.warning,
        dismissible: dismissible,
        actions: actions,
        autoDismissDuration: duration ?? const Duration(seconds: 3),
      ),
    );
  }

  /// عرض تنبيه معلومات
  static Future<void> info({
    required BuildContext context,
    required String title,
    required String message,
    Duration? duration,
    List<Widget>? actions,
    bool dismissible = true,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: dismissible,
      builder: (context) => AkAlertDialog(
        title: title,
        message: message,
        type: AkDialogType.info,
        dismissible: dismissible,
        actions: actions,
        autoDismissDuration: duration ?? const Duration(seconds: 3),
      ),
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● الدوال المساعدة السريعة (AkDialogs)
// ═══════════════════════════════════════════════════════════════════════════════

/// فئة الدوال المساعدة السريعة للحوارات
/// توفر طرق سريعة لعرض الحوارات الشائعة
class AkDialogs {
  AkDialogs._();

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال التأكيد السريعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// حوار تأكيد حذف سريع
  static Future<bool?> confirmDelete({
    required BuildContext context,
    String title = 'تأكيد الحذف',
    String content =
        'هل أنت متأكد من حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.',
    VoidCallback? onConfirm,
  }) {
    return AkConfirmDialog.show(
      context: context,
      title: title,
      content: content,
      type: AkDialogType.danger,
      icon: Icons.delete_forever,
      confirmText: 'حذف',
      cancelText: 'إلغاء',
      onConfirm: onConfirm,
    );
  }

  /// حوار تأكيد حفظ سريع
  static Future<bool?> confirmSave({
    required BuildContext context,
    String title = 'تأكيد الحفظ',
    String content = 'هل تريد حفظ التغييرات؟',
    VoidCallback? onConfirm,
  }) {
    return AkConfirmDialog.show(
      context: context,
      title: title,
      content: content,
      type: AkDialogType.success,
      icon: Icons.save,
      confirmText: 'حفظ',
      cancelText: 'إلغاء',
      onConfirm: onConfirm,
    );
  }

  /// حوار تأكيد خروج سريع
  static Future<bool?> confirmExit({
    required BuildContext context,
    String title = 'تأكيد الخروج',
    String content = 'هل تريد الخروج؟ قد تفقد التغييرات غير المحفوظة.',
    VoidCallback? onConfirm,
  }) {
    return AkConfirmDialog.show(
      context: context,
      title: title,
      content: content,
      type: AkDialogType.warning,
      icon: Icons.exit_to_app,
      confirmText: 'خروج',
      cancelText: 'إلغاء',
      onConfirm: onConfirm,
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال التنبيه السريعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// تنبيه نجاح سريع
  static Future<void> success({
    required BuildContext context,
    String title = 'تم بنجاح',
    required String message,
    Duration? duration,
  }) {
    return AkAlertDialog.success(
      context: context,
      title: title,
      message: message,
      duration: duration,
    );
  }

  /// تنبيه خطأ سريع
  static Future<void> error({
    required BuildContext context,
    String title = 'خطأ',
    required String message,
    Duration? duration,
  }) {
    return AkAlertDialog.error(
      context: context,
      title: title,
      message: message,
      duration: duration,
    );
  }

  /// تنبيه تحذير سريع
  static Future<void> warning({
    required BuildContext context,
    String title = 'تحذير',
    required String message,
    Duration? duration,
  }) {
    return AkAlertDialog.warning(
      context: context,
      title: title,
      message: message,
      duration: duration,
    );
  }

  /// تنبيه معلومات سريع
  static Future<void> info({
    required BuildContext context,
    String title = 'معلومات',
    required String message,
    Duration? duration,
  }) {
    return AkAlertDialog.info(
      context: context,
      title: title,
      message: message,
      duration: duration,
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال متخصصة للمشروع التجاري
  // ───────────────────────────────────────────────────────────────────────────────

  /// تنبيه نجاح عملية بيع
  static Future<void> saleSuccess({
    required BuildContext context,
    required String amount,
    String? customerName,
  }) {
    final message = customerName != null
        ? 'تمت عملية البيع بنجاح للعميل $customerName\nالمبلغ: $amount ر.ي'
        : 'تمت عملية البيع بنجاح\nالمبلغ: $amount ر.ي';

    return success(
      context: context,
      title: 'تمت عملية البيع',
      message: message,
    );
  }

  /// تنبيه نجاح عملية شراء
  static Future<void> purchaseSuccess({
    required BuildContext context,
    required String amount,
    String? supplierName,
  }) {
    final message = supplierName != null
        ? 'تمت عملية الشراء بنجاح من المورد $supplierName\nالمبلغ: $amount ر.ي'
        : 'تمت عملية الشراء بنجاح\nالمبلغ: $amount ر.ي';

    return success(
      context: context,
      title: 'تمت عملية الشراء',
      message: message,
    );
  }

  /// تحذير نقص المخزون
  static Future<void> lowStockWarning({
    required BuildContext context,
    required String productName,
    required int currentStock,
    required int minStock,
  }) {
    return warning(
      context: context,
      title: 'تحذير نقص مخزون',
      message:
          'المنتج: $productName\nالكمية الحالية: $currentStock\nالحد الأدنى: $minStock\nيرجى إعادة التزويد',
    );
  }

  /// خطأ في عملية الدفع
  static Future<void> paymentError({
    required BuildContext context,
    String? errorMessage,
  }) {
    return error(
      context: context,
      title: 'خطأ في عملية الدفع',
      message: errorMessage ??
          'حدث خطأ أثناء معالجة عملية الدفع. يرجى المحاولة مرة أخرى.',
    );
  }

  /// تأكيد إغلاق الكاشير
  static Future<bool?> confirmCashierClose({
    required BuildContext context,
    required String totalSales,
    required String totalCash,
  }) {
    return AkConfirmDialog.show(
      context: context,
      title: 'إغلاق الكاشير',
      content:
          'إجمالي المبيعات: $totalSales ر.ي\nإجمالي النقد: $totalCash ر.ي\n\nهل تريد إغلاق الكاشير؟',
      type: AkDialogType.warning,
      icon: Icons.point_of_sale,
      confirmText: 'إغلاق',
      cancelText: 'إلغاء',
    );
  }
}
