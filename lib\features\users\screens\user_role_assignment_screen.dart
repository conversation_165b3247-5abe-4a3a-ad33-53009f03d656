import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
// تم حذف features/shared/widgets/ - استخدم الأنظمة الموحدة
// تم حذف features/shared/widgets/ - استخدم الأنظمة الموحدة
// تم حذف features/shared/widgets/ - استخدم الأنظمة الموحدة
import '../models/user.dart';
import '../../../core/auth/models/user_role.dart';
import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';

/// شاشة تعيين الأدوار للمستخدمين
class UserRoleAssignmentScreen extends StatefulWidget {
  static const String routeName = '/user-role-assignment';

  const UserRoleAssignmentScreen({Key? key}) : super(key: key);

  @override
  State<UserRoleAssignmentScreen> createState() =>
      _UserRoleAssignmentScreenState();
}

class _UserRoleAssignmentScreenState extends State<UserRoleAssignmentScreen> {
  bool _isLoading = false;
  String _searchQuery = '';
  bool _showSearchField = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // إضافة مستمع للبحث
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });

    // استخدام addPostFrameCallback لتأجيل تحميل البيانات حتى يتم الانتهاء من بناء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadData();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
      final userPresenter = AppProviders.getUserPresenter();
      final permissionPresenter = AppProviders.getPermissionPresenter();

      await Future.wait([
        userPresenter.loadUsers(),
        permissionPresenter.loadRoles(),
      ]);
    } catch (e, stackTrace) {
      AppLogger.error('فشل في تحميل البيانات: $e');
      ErrorTracker.captureError(
        'فشل في تحميل البيانات في شاشة تعيين الأدوار',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'تعيين الأدوار للمستخدمين',
        showBackButton: true,
        actions: [
          // أيقونة البحث
          IconButton(
            icon: Icon(_showSearchField ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                  _searchQuery = '';
                }
              });
            },
            tooltip: _showSearchField ? 'إغلاق البحث' : 'البحث',
          ),
        ],
      ),
      body: _isLoading ? const AkLoadingIndicator() : _buildContent(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    return Column(
      children: [
        // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث)
        if (_showSearchField)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: DynamicColors.surface(context),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryContainer.withValues(alpha: 0.05),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: AkSearchInput(
              controller: _searchController,
              hint:
                  'البحث في المستخدمين (اسم المستخدم، الاسم الكامل، البريد الإلكتروني)...',
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              onClear: () {
                setState(() {
                  _searchController.clear();
                  _searchQuery = '';
                });
              },
            ),
          ),

        // قائمة المستخدمين
        Expanded(
          child: _buildUsersList(),
        ),
      ],
    );
  }

  /// بناء قائمة المستخدمين
  Widget _buildUsersList() {
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    final userPresenter = AppProviders.getUserPresenter();
    final users = _filterUsers(userPresenter.users);

    if (users.isEmpty) {
      return const Center(
        child: Text('لا يوجد مستخدمين'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        return _buildUserCard(user);
      },
    );
  }

  /// بناء بطاقة المستخدم
  Widget _buildUserCard(User user) {
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    final permissionPresenter = AppProviders.getPermissionPresenter();
    final roles = permissionPresenter.roles;

    // البحث عن الدور الحالي للمستخدم
    UserRole? currentRole;
    if (user.roleId != null && roles.isNotEmpty) {
      try {
        currentRole = roles.firstWhere(
          (role) => role.id == user.roleId,
          orElse: () => UserRole(
            id: user.roleId!,
            name: user.roleId!,
            displayName: 'غير معروف',
            permissions: [],
          ),
        );
      } catch (e) {
        // لم يتم العثور على الدور
      }
    }

    return AkCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
              child: const Icon(Icons.person),
            ),
            title: Text(
              user.username,
              style: const AppTypography(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(user.fullName),
            trailing: user.isActive
                ? const Icon(Icons.check_circle, color: AppColors.success)
                : const Icon(Icons.cancel, color: AppColors.error),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                const Text('الدور الحالي:'),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildRoleDropdown(user, currentRole, roles),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة منسدلة للأدوار
  Widget _buildRoleDropdown(
      User user, UserRole? currentRole, List<UserRole> roles) {
    // التحقق من وجود الدور في قائمة الأدوار
    final roleExists =
        user.roleId != null && roles.any((role) => role.id == user.roleId);

    // إذا كان الدور غير موجود، نعرض رسالة
    if (user.roleId != null && !roleExists) {
      AppLogger.warning('الدور ${user.roleId} غير موجود في قائمة الأدوار');
    }

    // استخدام القيمة فقط إذا كانت موجودة في قائمة الأدوار
    final effectiveValue = roleExists ? user.roleId : null;

    return DropdownButtonFormField<String>(
      value: effectiveValue,
      decoration: const InputDecoration(
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        border: OutlineInputBorder(),
      ),
      items: roles.map((role) {
        return DropdownMenuItem<String>(
          value: role.id,
          child: Text(role.displayName),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          _updateUserRole(user, value);
        }
      },
      hint: Text(currentRole?.displayName ?? 'اختر دوراً'),
    );
  }

  /// تحديث دور المستخدم
  Future<void> _updateUserRole(User user, String roleId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userPresenter = AppProviders.getUserPresenter();

      // استخدام الطريقة المخصصة لتعيين دور المستخدم
      final success = await userPresenter.setUserRole(user.id, roleId);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث دور المستخدم بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في تحديث دور المستخدم'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e, stackTrace) {
      AppLogger.error('فشل في تحديث دور المستخدم: $e');
      ErrorTracker.captureError(
        'فشل في تحديث دور المستخدم',
        error: e,
        stackTrace: stackTrace,
        context: {
          'userId': user.id,
          'roleId': roleId,
        },
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحديث دور المستخدم: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تصفية المستخدمين حسب استعلام البحث
  List<User> _filterUsers(List<User> users) {
    if (_searchQuery.isEmpty) {
      return users;
    }

    final query = _searchQuery.toLowerCase();
    return users.where((user) {
      return user.username.toLowerCase().contains(query) ||
          user.fullName.toLowerCase().contains(query) ||
          (user.email?.toLowerCase().contains(query) ?? false);
    }).toList();
  }
}
