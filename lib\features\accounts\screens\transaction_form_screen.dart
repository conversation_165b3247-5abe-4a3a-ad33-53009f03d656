import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/widgets/index.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/theme/index.dart';

class TransactionFormScreen extends StatefulWidget {
  final Map<String, dynamic>? transaction;
  final int? initialAccountId; // Optional: to pre-select an account

  const TransactionFormScreen({
    Key? key,
    this.transaction,
    this.initialAccountId,
  }) : super(key: key);

  @override
  State<TransactionFormScreen> createState() => _TransactionFormScreenState();
}

class _TransactionFormScreenState extends State<TransactionFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();

  String _selectedTransactionType = 'income';
  int? _selectedAccountId;
  int? _selectedRelatedAccountId;
  int? _selectedBranchId;
  DateTime _selectedDate = DateTime.now();

  List<Map<String, dynamic>> _accounts = [];
  List<Map<String, dynamic>> _branches = [];
  bool _isLoading = true;
  bool _isSaving = false;

  final List<Map<String, dynamic>> _transactionTypes = [
    {'value': 'income', 'label': 'إيراد'},
    {'value': 'expense', 'label': 'مصروف'},
    {'value': 'transfer', 'label': 'تحويل'},
    {'value': 'sale', 'label': 'مبيعات'},
    {'value': 'purchase', 'label': 'مشتريات'},
  ];

  @override
  void initState() {
    super.initState();
    _loadData();

    // Set today's date in the date controller
    _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate);

    // If editing an existing transaction, populate the form
    if (widget.transaction != null) {
      _populateForm();
    } else if (widget.initialAccountId != null) {
      _selectedAccountId = widget.initialAccountId;
    }
  }

  void _populateForm() {
    final transaction = widget.transaction!;

    _selectedTransactionType = transaction['transaction_type'] as String;
    _amountController.text = (transaction['amount'] as double).toString();
    _selectedAccountId = transaction['account_id'] as int;
    _selectedRelatedAccountId = transaction['related_account_id'] as int?;
    _selectedBranchId = transaction['branch_id'] as int;
    _selectedDate = DateTime.parse(transaction['transaction_date']);
    _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate);

    if (transaction['notes'] != null) {
      _notesController.text = transaction['notes'] as String;
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final db = await _databaseHelper.database;

      // Load accounts
      final List<Map<String, dynamic>> accounts = await db.query(
        'accounts',
        columns: ['id', 'name', 'type'],
        orderBy: 'name',
      );

      // Load branches
      final List<Map<String, dynamic>> branches = await db.query(
        'branches',
        columns: ['id', 'name'],
        orderBy: 'name',
      );

      // If we don't have a selected branch yet, use the first one
      if (_selectedBranchId == null && branches.isNotEmpty) {
        _selectedBranchId = branches.first['id'] as int;
      }

      setState(() {
        _accounts = accounts;
        _branches = branches;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to load data for transaction form',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'TransactionFormScreen'},
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load data')),
        );
      }
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate);
      });
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Additional validation for related account
    if (_selectedTransactionType == 'transfer' &&
        _selectedRelatedAccountId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار الحساب المقابل للتحويل')),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now().toIso8601String();

      // Get current user ID (in a real app, this would come from authentication)
      final List<Map<String, dynamic>> users =
          await db.query('users', limit: 1);
      final int userId = users.first['id'] as int;

      final Map<String, dynamic> transactionData = {
        'transaction_type': _selectedTransactionType,
        'amount': double.parse(_amountController.text),
        'account_id': _selectedAccountId,
        'related_account_id': _selectedRelatedAccountId,
        'branch_id': _selectedBranchId,
        'user_id': userId,
        'transaction_date': DateFormat('yyyy-MM-dd').format(_selectedDate),
        'notes': _notesController.text.isEmpty ? null : _notesController.text,
        'updated_at': now,
        'updated_by': 'current_user', // Replace with actual user
      };

      if (widget.transaction == null) {
        // Creating a new transaction
        transactionData['created_at'] = now;
        transactionData['created_by'] =
            'current_user'; // Replace with actual user

        await db.insert('transactions', transactionData);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إنشاء المعاملة بنجاح')),
          );
        }
      } else {
        // Updating an existing transaction
        await db.update(
          'transactions',
          transactionData,
          where: 'id = ?',
          whereArgs: [widget.transaction!['id']],
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تحديث المعاملة بنجاح')),
          );
        }
      }

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to save transaction',
        error: e,
        stackTrace: stackTrace,
        context: {
          'screen': 'TransactionFormScreen',
          'isNewTransaction': widget.transaction == null,
        },
      );

      setState(() {
        _isSaving = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to save transaction')),
        );
      }
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.transaction != null;

    return Scaffold(
      appBar: AkAppBar(
        title: isEditing ? 'تعديل معاملة' : 'إضافة معاملة جديدة',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: AkLoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DropdownButtonFormField<String>(
                      value: _selectedTransactionType,
                      decoration: const InputDecoration(
                        labelText: 'نوع المعاملة *',
                        border: OutlineInputBorder(),
                      ),
                      items: _transactionTypes.map((type) {
                        return DropdownMenuItem<String>(
                          value: type['value'] as String,
                          child: Text(type['label'] as String),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedTransactionType = value!;

                          // Reset related account if not a transfer
                          if (_selectedTransactionType != 'transfer') {
                            _selectedRelatedAccountId = null;
                          }
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى اختيار نوع المعاملة';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                    TextFormField(
                      controller: _amountController,
                      decoration: const InputDecoration(
                        labelText: 'المبلغ *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.attach_money),
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال المبلغ';
                        }
                        try {
                          final amount = double.parse(value);
                          if (amount <= 0) {
                            return 'يجب أن يكون المبلغ أكبر من صفر';
                          }
                        } catch (e) {
                          return 'يرجى إدخال رقم صحيح';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                    DropdownButtonFormField<int?>(
                      value: _selectedAccountId,
                      decoration: const InputDecoration(
                        labelText: 'الحساب *',
                        border: OutlineInputBorder(),
                      ),
                      items: _accounts.map((account) {
                        return DropdownMenuItem<int?>(
                          value: account['id'] as int,
                          child: Text(
                              '${account['name']} (${_getAccountTypeDisplayName(account['type'] as String)})'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedAccountId = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الحساب';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                    if (_selectedTransactionType == 'transfer')
                      DropdownButtonFormField<int?>(
                        value: _selectedRelatedAccountId,
                        decoration: const InputDecoration(
                          labelText: 'الحساب المقابل *',
                          border: OutlineInputBorder(),
                        ),
                        items: _accounts
                            .where((account) =>
                                account['id'] != _selectedAccountId)
                            .map((account) {
                          return DropdownMenuItem<int?>(
                            value: account['id'] as int,
                            child: Text(
                                '${account['name']} (${_getAccountTypeDisplayName(account['type'] as String)})'),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedRelatedAccountId = value;
                          });
                        },
                        validator: (value) {
                          if (_selectedTransactionType == 'transfer' &&
                              value == null) {
                            return 'يرجى اختيار الحساب المقابل للتحويل';
                          }
                          return null;
                        },
                      ),
                    const SizedBox(height: AppDimensions.spacing16),
                    DropdownButtonFormField<int?>(
                      value: _selectedBranchId,
                      decoration: const InputDecoration(
                        labelText: 'الفرع *',
                        border: OutlineInputBorder(),
                      ),
                      items: _branches.map((branch) {
                        return DropdownMenuItem<int?>(
                          value: branch['id'] as int,
                          child: Text(branch['name'] as String),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedBranchId = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الفرع';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                    TextFormField(
                      controller: _dateController,
                      decoration: InputDecoration(
                        labelText: 'التاريخ *',
                        border: const OutlineInputBorder(),
                        prefixIcon: const Icon(Icons.calendar_today),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.date_range),
                          onPressed: () => _selectDate(context),
                        ),
                      ),
                      readOnly: true,
                      onTap: () => _selectDate(context),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى اختيار التاريخ';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppDimensions.spacing16),
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: AppDimensions.spacing24),
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isSaving ? null : _saveTransaction,
                        child: _isSaving
                            ? const AkLoadingIndicator.small()
                            : Text(
                                isEditing ? 'تحديث المعاملة' : 'إضافة المعاملة',
                                style: AppTypography.createCustomStyle(
                                    fontSize: 16),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  String _getAccountTypeDisplayName(String type) {
    switch (type) {
      case 'asset':
        return 'أصول';
      case 'liability':
        return 'خصوم';
      case 'equity':
        return 'حقوق ملكية';
      case 'revenue':
        return 'إيرادات';
      case 'expense':
        return 'مصروفات';
      case 'customer':
        return 'عميل';
      case 'supplier':
        return 'مورد';
      case 'cash':
        return 'نقدية';
      default:
        return type;
    }
  }
}
