import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/utils/global_error_handler.dart';

void main() {
  group('GlobalErrorHandler Tests', () {
    test('analyzeProblem يقدم اقتراحات مناسبة لأخطاء قاعدة البيانات', () {
      final error = Exception('SQLite error: database is locked');
      final stackTrace = StackTrace.current;

      final suggestion = GlobalErrorHandler.analyzeProblem(error, stackTrace);

      expect(suggestion, contains('database issue'));
    });

    test('analyzeProblem يقدم اقتراحات مناسبة لأخطاء الشبكة', () {
      final error =
          Exception('Failed to connect to server: Connection refused');
      final stackTrace = StackTrace.current;

      final suggestion = GlobalErrorHandler.analyzeProblem(error, stackTrace);

      expect(suggestion, contains('network issue'));
    });

    test('analyzeProblem يقدم اقتراحات مناسبة لأخطاء إدارة الحالة', () {
      final error = Exception(
          'Could not find the correct Provider<Counter> above this Consumer Widget');
      final stackTrace = StackTrace.current;

      final suggestion = GlobalErrorHandler.analyzeProblem(error, stackTrace);

      expect(suggestion, contains('state management issue'));
    });

    test('analyzeProblem يقدم اقتراحات مناسبة لأخطاء عرض واجهة المستخدم', () {
      final error =
          Exception('A RenderFlex overflowed by 20 pixels on the bottom');
      final stackTrace = StackTrace.current;

      final suggestion = GlobalErrorHandler.analyzeProblem(error, stackTrace);

      expect(suggestion, contains('UI rendering issue'));
    });

    test('analyzeProblem يقدم اقتراحات افتراضية للأخطاء غير المعروفة', () {
      final error = Exception('Unknown error occurred');
      final stackTrace = StackTrace.current;

      final suggestion = GlobalErrorHandler.analyzeProblem(error, stackTrace);

      expect(suggestion, contains('Review the stack trace'));
    });

    testWidgets('ErrorWidget.builder يعرض واجهة خطأ مناسبة للمستخدم',
        (WidgetTester tester) async {
      // تهيئة معالج الأخطاء العام
      GlobalErrorHandler.init();

      // إنشاء تفاصيل خطأ للاختبار
      final FlutterErrorDetails errorDetails = FlutterErrorDetails(
        exception: Exception('Test error'),
        stack: StackTrace.current,
        library: 'test library',
        context: ErrorDescription('test context'),
      );

      // الحصول على الويدجت من ErrorWidget.builder
      final Widget errorWidget = ErrorWidget.builder(errorDetails);

      // عرض الويدجت
      await tester.pumpWidget(MaterialApp(home: errorWidget));

      // التحقق من وجود العناصر المتوقعة
      expect(find.text('Something went wrong'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);

      // في وضع التصحيح، يجب أن يظهر نص الخطأ
      expect(find.text('Exception: Test error'), findsOneWidget);
    });
  });
}
