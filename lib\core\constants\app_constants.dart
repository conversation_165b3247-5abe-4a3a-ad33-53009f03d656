import 'package:flutter/material.dart';

/// ثوابت التطبيق الموحدة
/// تم توحيد جميع الثوابت من مختلف ملفات المشروع في هذا الملف
class AppConstants {
  // منع إنشاء نسخ من هذا الصف
  AppConstants._();

  // معلومات التطبيق
  /// اسم التطبيق بالعربية
  static const String appName = 'تاجر بلس';

  /// اسم التطبيق بالإنجليزية
  static const String appNameEn = 'Tajer Plus';

  /// إصدار التطبيق
  static const String appVersion = '1.0.0';

  /// اسم حزمة التطبيق
  static const String packageName = 'com.ak_544.tajer_plus';

  // ثوابت العملة والتنسيق
  /// رمز العملة
  static const String appCurrency = 'ر.ي';

  /// كود العملة
  static const String appCurrencyCode = 'YER';

  /// تنسيق التاريخ
  static const String appDateFormat = 'yyyy-MM-dd';

  /// تنسيق التاريخ والوقت
  static const String appDateTimeFormat = 'yyyy-MM-dd HH:mm';

  /// تنسيق الوقت
  static const String appTimeFormat = 'HH:mm';

  /// فاصل العلامة العشرية
  static const String appDecimalSeparator = '.';

  /// فاصل الآلاف
  static const String appThousandSeparator = ',';

  /// عدد الخانات العشرية
  static const int appDecimalPlaces = 2;

  // ثوابت قاعدة البيانات
  /// اسم قاعدة البيانات
  static const String dbName = 'tajer_plus.db';

  /// إصدار قاعدة البيانات
  static const int dbVersion = 1;

  // ثوابت التخزين المحلي
  /// مفتاح رمز المصادقة
  static const String tokenKey = 'auth_token';

  /// مفتاح معرف المستخدم
  static const String userIdKey = 'user_id';

  /// مفتاح اسم المستخدم
  static const String userNameKey = 'user_name';

  /// مفتاح دور المستخدم
  static const String userRoleKey = 'user_role';

  /// مفتاح سمة التطبيق
  static const String themeKey = 'app_theme';

  /// مفتاح لغة التطبيق
  static const String languageKey = 'app_language';

  // ثوابت المستودعات
  /// رمز المستودع الافتراضي
  static const String defaultWarehouseCode = 'MAIN';

  /// اسم المستودع الافتراضي
  static const String defaultWarehouseName = 'المستودع الرئيسي';

  // ثوابت تحويلات المخزون
  /// بادئة نوع التحويل
  static const String transferTypePrefix = 'TRF';

  /// بادئة نوع التعديل
  static const String adjustmentTypePrefix = 'ADJ';

  /// بادئة نوع الاستلام
  static const String receiptTypePrefix = 'RCV';

  /// بادئة نوع الإصدار
  static const String issueTypePrefix = 'ISS';

  // ثوابت حالات تحويلات المخزون
  /// حالة المسودة
  static const String statusDraft = 'draft';

  /// حالة معلق
  static const String statusPending = 'pending';

  /// حالة قيد النقل
  static const String statusInTransit = 'inTransit';

  /// حالة مكتمل
  static const String statusCompleted = 'completed';

  /// حالة ملغي
  static const String statusCancelled = 'cancelled';

  /// حالة مرفوض
  static const String statusRejected = 'rejected';

  // ثوابت أنواع تحويلات المخزون
  /// نوع تحويل
  static const String typeTransfer = 'transfer';

  /// نوع تعديل
  static const String typeAdjustment = 'adjustment';

  /// نوع استلام
  static const String typeReceipt = 'receipt';

  /// نوع إصدار
  static const String typeIssue = 'issue';

  // ثوابت الأيقونات
  /// أيقونة المستودع
  static const String iconWarehouse = 'warehouse';

  /// أيقونة المخزون
  static const String iconInventory = 'inventory';

  /// أيقونة التحويل
  static const String iconTransfer = 'swap_horiz';

  /// أيقونة التعديل
  static const String iconAdjustment = 'edit';

  /// أيقونة الاستلام
  static const String iconReceipt = 'input';

  /// أيقونة الإصدار
  static const String iconIssue = 'output';

  // ثوابت الصلاحيات
  /// صلاحية إدارة المستودعات
  static const String permissionManageWarehouses = 'manage_warehouses';

  /// صلاحية إدارة المخزون
  static const String permissionManageInventory = 'manage_inventory';

  /// صلاحية إدارة التحويلات
  static const String permissionManageTransfers = 'manage_transfers';

  /// صلاحية تنفيذ التحويلات
  static const String permissionExecuteTransfers = 'execute_transfers';

  // ثوابت الإشعارات
  /// معرف قناة الإشعارات
  static const String notificationChannelId = 'tajer_plus_channel';

  /// اسم قناة الإشعارات
  static const String notificationChannelName = 'تاجر بلس';

  /// وصف قناة الإشعارات
  static const String notificationChannelDescription = 'إشعارات تطبيق تاجر بلس';

  // ثوابت المخزون
  /// الحد الأدنى الافتراضي للمخزون
  static const double defaultMinStock = 5.0;

  /// الحد الأقصى الافتراضي للمخزون
  static const double defaultMaxStock = 100.0;

  // ثوابت الوحدات
  /// اسم الوحدة الافتراضية
  static const String defaultUnitName = 'قطعة';

  /// رمز الوحدة الافتراضية
  static const String defaultUnitSymbol = 'ق';

  // ثوابت الفئات
  /// اسم الفئة الافتراضية
  static const String defaultCategoryName = 'عام';

  // ثوابت العملاء
  /// حد الائتمان الافتراضي
  static const double defaultCreditLimit = 0.0;

  // ثوابت المبيعات
  /// معدل الضريبة الافتراضي
  static const double defaultTaxRate = 0.15; // 15%

  /// معدل الخصم الافتراضي
  static const double defaultDiscountRate = 0.0;

  // ثوابت الحسابات
  /// نوع حساب الأصول
  static const String accountTypeAsset = 'asset';

  /// نوع حساب الخصوم
  static const String accountTypeLiability = 'liability';

  /// نوع حساب حقوق الملكية
  static const String accountTypeEquity = 'equity';

  /// نوع حساب الإيرادات
  static const String accountTypeRevenue = 'revenue';

  /// نوع حساب المصروفات
  static const String accountTypeExpense = 'expense';

  // ثوابت حالات الحسابات
  /// حالة حساب نشط
  static const String accountStatusActive = 'active';

  /// حالة حساب غير نشط
  static const String accountStatusInactive = 'inactive';

  /// حالة حساب مؤرشف
  static const String accountStatusArchived = 'archived';

  // إعدادات API
  /// عنوان API الأساسي
  static const String apiBaseUrl = 'https://api.tajerplus.com/api';

  /// معرف مشروع Firebase
  static const String firebaseProjectId = 'tajer-plus';

  // إعدادات المزامنة
  /// مزامنة البيانات عند بدء التطبيق
  static const bool syncOnStart = true;

  /// مزامنة البيانات عند الاستعلام
  static const bool syncOnQuery = true;

  /// البحث عبر الإنترنت
  static const bool searchOnline = true;

  /// فترة المزامنة التلقائية
  static const Duration syncInterval = Duration(minutes: 15);

  /// الحد الأقصى لمحاولات المزامنة
  static const int maxSyncRetries = 3;

  // إعدادات افتراضية
  /// اللغة الافتراضية
  static const Locale defaultLocale = Locale('ar');

  /// الوضع الداكن الافتراضي
  static const bool defaultDarkMode = true;

  // مدة الرسوم المتحركة
  /// مدة شاشة البداية
  static const Duration splashDuration = Duration(seconds: 3);

  /// مدة انتقال الصفحة
  static const Duration pageTransitionDuration = Duration(milliseconds: 300);

  // إعدادات التخزين المؤقت
  /// الحد الأقصى لعمر التخزين المؤقت (أيام)
  static const int maxCacheAge = 7;

  /// الحد الأقصى لحجم تخزين الصور المؤقت (ميجابايت)
  static const int maxImageCacheSize = 100;

  // إعدادات النسخ الاحتياطي
  /// فترة النسخ الاحتياطي التلقائي
  static const Duration autoBackupInterval = Duration(hours: 24);

  /// الحد الأقصى لعدد النسخ الاحتياطية
  static const int maxBackupCount = 5;

  /// دليل النسخ الاحتياطي
  static const String backupDirectory = 'backups';

  // إعدادات التسجيل
  /// تمكين التسجيل
  static const bool enableLogging = true;

  /// تسجيل في ملف
  static const bool logToFile = true;

  /// دليل ملفات السجل
  static const String logDirectory = 'logs';

  /// الحد الأقصى لعدد ملفات السجل
  static const int maxLogFiles = 5;

  // إعدادات الأمان
  /// مدة انتهاء صلاحية الرمز
  static const Duration tokenExpiration = Duration(hours: 24);

  /// مدة انتهاء صلاحية رمز التحديث
  static const Duration refreshTokenExpiration = Duration(days: 30);

  /// تشفير البيانات المحلية
  static const bool encryptLocalData = true;

  // إعدادات الاتصال
  /// مهلة الاتصال
  static const Duration connectionTimeout = Duration(seconds: 30);

  /// مهلة الاستلام
  static const Duration receiveTimeout = Duration(seconds: 30);

  // إعدادات وضع عدم الاتصال
  /// تمكين وضع عدم الاتصال
  static const bool enableOfflineMode = true;

  /// الحد الأقصى للعناصر غير المتصلة
  static const int maxOfflineItems = 1000;
}
