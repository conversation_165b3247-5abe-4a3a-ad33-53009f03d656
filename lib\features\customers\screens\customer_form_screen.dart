import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/models/customer.dart';
import '../presenters/customer_presenter.dart';
import '../../accounts/presenters/account_presenter.dart'; // إضافة مقدم الحسابات

import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

class CustomerFormScreen extends StatefulWidget {
  final Customer? customer;

  const CustomerFormScreen({Key? key, this.customer}) : super(key: key);

  @override
  State<CustomerFormScreen> createState() => _CustomerFormScreenState();
}

class _CustomerFormScreenState extends State<CustomerFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late CustomerPresenter _customerPresenter;
  late AccountPresenter _accountPresenter;

  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _balanceController = TextEditingController();
  bool _isActive = true;

  String? _selectedAccountId; // معرف الحساب المحدد
  List<Map<String, dynamic>> _accounts = []; // قائمة الحسابات
  bool _isLoadingAccounts = false; // حالة تحميل الحسابات

  @override
  void initState() {
    super.initState();
    _customerPresenter = AppProviders.getLazyPresenter<CustomerPresenter>(
        () => CustomerPresenter());
    _accountPresenter = AppProviders.getLazyPresenter<AccountPresenter>(
        () => AccountPresenter());
    _initializeFields();
    _loadAccounts();
  }

  void _initializeFields() {
    if (widget.customer != null) {
      _nameController.text = widget.customer!.name;
      _phoneController.text = widget.customer!.phone ?? '';
      _emailController.text = widget.customer!.email ?? '';
      _addressController.text = widget.customer!.address ?? '';
      _balanceController.text = widget.customer!.balance.toString();
      _isActive = widget.customer!.isActive;
      _selectedAccountId = widget.customer!.accountId; // تعيين معرف الحساب
    } else {
      _balanceController.text = '0.0';
    }
  }

  // دالة لتحميل الحسابات من شجرة الحسابات
  Future<void> _loadAccounts() async {
    setState(() {
      _isLoadingAccounts = true;
    });

    try {
      // تحميل الحسابات من مقدم الحسابات
      await _accountPresenter.loadAccounts();

      setState(() {
        // الحصول على الحسابات من نوع "receivable" (ذمم مدينة) أو "customer" (عملاء)
        _accounts = _accountPresenter.accounts.where((account) {
          final type = account['type']?.toString().toLowerCase() ?? '';
          return type == 'asset' || type == 'receivable' || type == 'customer';
        }).toList();

        _isLoadingAccounts = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingAccounts = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load accounts: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _balanceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.customer == null ? 'Add Customer' : 'Edit Customer'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  _buildBasicInfoSection(),
                  const SizedBox(height: AppDimensions.spacing24),
                  _buildContactInfoSection(),
                  const SizedBox(height: AppDimensions.spacing24),
                  _buildOptionsSection(),
                ],
              ),
            ),
            _buildBottomBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppDimensions.spacing16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Customer Name*',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter customer name';
                }
                return null;
              },
            ),
            const SizedBox(height: AppDimensions.spacing16),

            // حقل اختيار الحساب من شجرة الحسابات
            _buildAccountSelector(),
            const SizedBox(height: AppDimensions.spacing16),

            TextFormField(
              controller: _balanceController,
              decoration: const InputDecoration(
                labelText: 'Balance',
                border: OutlineInputBorder(),
              ),
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // دالة لبناء حقل اختيار الحساب
  Widget _buildAccountSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Account*',
              style: AppTypography(fontWeight: FontWeight.bold),
            ),
            const SizedBox(width: 8),
            if (_isLoadingAccounts)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacing8),
        DropdownButtonFormField<String?>(
          value: _selectedAccountId,
          decoration: const InputDecoration(
            hintText: 'Select an account',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          ),
          items: _accounts.map((account) {
            final accountName = account['name'] as String;
            final accountCode = account['code'] as String?;
            final accountType = account['type'] as String?;

            return DropdownMenuItem<String?>(
              value: account['id'].toString(),
              child: Row(
                children: [
                  Icon(
                    _getAccountIcon(accountType),
                    size: 16,
                    color: _getAccountColor(accountType),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      accountName,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (accountCode != null) ...[
                    const SizedBox(width: 8),
                    Text(
                      '#$accountCode',
                      style: const AppTypography(
                        fontSize: 12,
                        color: AppColors.lightTextSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedAccountId = value;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select an account';
            }
            return null;
          },
          isExpanded: true,
        ),
        const SizedBox(height: AppDimensions.spacing4),
        TextButton.icon(
          onPressed: () {
            // هنا يمكن إضافة الانتقال إلى شاشة إضافة حساب جديد
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Navigate to add new account screen'),
              ),
            );
          },
          icon: const Icon(Icons.add, size: 16),
          label: const Text('Add New Account'),
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: const Size(0, 32),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            alignment: Alignment.centerLeft,
          ),
        ),
      ],
    );
  }

  // دالة للحصول على أيقونة الحساب حسب نوعه
  IconData _getAccountIcon(String? accountType) {
    switch (accountType?.toLowerCase()) {
      case 'asset':
        return Icons.account_balance;
      case 'receivable':
        return Icons.account_balance_wallet;
      case 'customer':
        return Icons.person;
      default:
        return Icons.account_circle;
    }
  }

  // دالة للحصول على لون الحساب حسب نوعه
  Color _getAccountColor(String? accountType) {
    switch (accountType?.toLowerCase()) {
      case 'asset':
        return AppColors.info;
      case 'receivable':
        return AppColors.success;
      case 'customer':
        return AppColors.accent;
      default:
        return AppColors.lightTextSecondary;
    }
  }

  Widget _buildContactInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contact Information',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppDimensions.spacing16),
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: AppDimensions.spacing16),
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: AppDimensions.spacing16),
            TextFormField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'Address',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Options',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppDimensions.spacing16),
            SwitchListTile(
              title: const Text('Active'),
              subtitle: const Text('Customer will be available for selection'),
              value: _isActive,
              onChanged: (value) {
                setState(() => _isActive = value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightTextSecondary,
        boxShadow: [
          BoxShadow(
            color: AppColors.lightTextPrimary.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _submitForm,
              child: Text(widget.customer == null ? 'Create' : 'Update'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    // التحقق من اختيار حساب
    if (_selectedAccountId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select an account for the customer'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final customer = Customer(
      id: widget.customer?.id,
      name: _nameController.text,
      phone: _phoneController.text.isEmpty ? null : _phoneController.text,
      email: _emailController.text.isEmpty ? null : _emailController.text,
      address: _addressController.text.isEmpty ? null : _addressController.text,
      balance: double.tryParse(_balanceController.text) ?? 0.0,
      isActive: _isActive,
      accountId: _selectedAccountId, // إضافة معرف الحساب
    );

    bool success;
    if (widget.customer == null) {
      success = await _customerPresenter.addCustomer(customer);
    } else {
      success = await _customerPresenter.updateCustomer(customer);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
                ? 'Customer ${widget.customer == null ? 'created' : 'updated'} successfully'
                : 'Failed to ${widget.customer == null ? 'create' : 'update'} customer',
          ),
          backgroundColor: success ? AppColors.success : AppColors.error,
        ),
      );

      if (success) {
        Navigator.pop(context, true);
      }
    }
  }
}
