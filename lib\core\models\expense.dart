import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج المصروفات
class Expense extends BaseModel {
  /// معرف فئة المصروف
  final String? categoryId;

  /// اسم فئة المصروف
  final String? categoryName;

  /// المبلغ
  final double amount;

  /// الوصف
  final String? description;

  /// رقم المرجع
  final String? referenceNumber;

  /// طريقة الدفع
  final String paymentMethod;

  /// تاريخ المصروف
  final DateTime expenseDate;

  /// اسم المستخدم الذي أنشأ المصروف
  final String? userName;

  /// إنشاء مصروف جديد
  Expense({
    String? id,
    this.categoryId,
    this.categoryName,
    required this.amount,
    this.description,
    this.referenceNumber,
    required this.paymentMethod,
    DateTime? expenseDate,
    this.userName,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  })  : expenseDate = expenseDate ?? DateTime.now(),
        super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء مصروف من Map
  factory Expense.fromMap(Map<String, dynamic> map) {
    return Expense(
      id: map['id'],
      categoryId: map['category_id'],
      categoryName: map['category_name'],
      amount: map['amount'] is int
          ? (map['amount'] as int).toDouble()
          : map['amount'] ?? 0.0,
      description: map['description'],
      referenceNumber: map['reference_number'],
      paymentMethod: map['payment_method'] ?? 'cash',
      expenseDate: map['expense_date'] != null
          ? DateTime.parse(map['expense_date'])
          : DateTime.now(),
      userName: map['user_name'],
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل المصروف إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'category_id': categoryId,
      'amount': amount,
      'description': description,
      'reference_number': referenceNumber,
      'payment_method': paymentMethod,
      'expense_date': expenseDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// نسخة معدلة من المصروف
  Expense copyWith({
    String? id,
    String? categoryId,
    String? categoryName,
    double? amount,
    String? description,
    String? referenceNumber,
    String? paymentMethod,
    DateTime? expenseDate,
    String? userName,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Expense(
      id: id ?? this.id,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      expenseDate: expenseDate ?? this.expenseDate,
      userName: userName ?? this.userName,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  @override
  String toString() {
    return 'Expense{id: $id, categoryId: $categoryId, amount: $amount, description: $description, expenseDate: $expenseDate}';
  }
}
