import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';

import '../presenters/currency_presenter.dart';
import 'currency_buy_sell_screen.dart';
import 'currency_exchange_screen.dart';
import 'currency_management_screen.dart';
import 'currency_reports_screen.dart';
import '../../../core/theme/index.dart';

class CurrenciesScreen extends StatefulWidget {
  const CurrenciesScreen({Key? key}) : super(key: key);

  @override
  State<CurrenciesScreen> createState() => _CurrenciesScreenState();
}

class _CurrenciesScreenState extends State<CurrenciesScreen> {
  bool _isLoading = false;

  // استخدام التحميل الكسول
  late final CurrencyPresenter _currencyPresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _currencyPresenter = AppProviders.getLazyPresenter<CurrencyPresenter>(
        () => CurrencyPresenter());
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _currencyPresenter.loadCurrencies();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل البيانات: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العملات'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إدارة العملات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: AppDimensions.spacing16),

                  // عمليات العملات
                  Text(
                    'عمليات العملات',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: AppDimensions.spacing8),
                  Row(
                    children: [
                      // شراء العملات
                      Expanded(
                        child: _buildMenuCard(
                          context,
                          title: 'شراء العملات',
                          icon: Icons.arrow_downward,
                          color: AppColors.lightTextSecondary,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const CurrencyBuySellScreen(isBuy: true),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      // بيع العملات
                      Expanded(
                        child: _buildMenuCard(
                          context,
                          title: 'بيع العملات',
                          icon: Icons.arrow_upward,
                          color: AppColors.lightTextSecondary,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const CurrencyBuySellScreen(isBuy: false),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDimensions.spacing8),
                  Row(
                    children: [
                      // مصارفة العملات
                      Expanded(
                        child: _buildMenuCard(
                          context,
                          title: 'مصارفة العملات',
                          icon: Icons.currency_exchange,
                          color: AppColors.lightTextSecondary,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const CurrencyExchangeScreen(),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      // إدارة العملات
                      Expanded(
                        child: _buildMenuCard(
                          context,
                          title: 'إدارة العملات',
                          icon: Icons.settings,
                          color: AppColors.lightTextSecondary,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const CurrencyManagementScreen(),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDimensions.spacing8),
                  Row(
                    children: [
                      // تقارير العملات
                      Expanded(
                        child: _buildMenuCard(
                          context,
                          title: 'تقارير العملات',
                          icon: Icons.bar_chart,
                          color: AppColors.lightTextSecondary,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const CurrencyReportsScreen(),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      // مساحة فارغة للتوازن
                      Expanded(
                        child: Container(),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDimensions.spacing24),

                  // إحصائيات
                  _buildStatisticsCard(context),
                ],
              ),
            ),
    );
  }

  Widget _buildMenuCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
        child: InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48,
              color: color,
            ),
            const SizedBox(height: AppDimensions.spacing8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildStatisticsCard(BuildContext context) {
    return ListenableBuilder(
      listenable: _currencyPresenter,
      builder: (context, child) {
        final currencies = _currencyPresenter.currencies;
        final defaultCurrency = _currencyPresenter.defaultCurrency;

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إحصائيات العملات',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: AppDimensions.spacing16),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        context,
                        title: 'العملات النشطة',
                        value: currencies
                            .where((c) => c.isActive)
                            .length
                            .toString(),
                        icon: Icons.check_circle,
                        color: AppColors.lightTextSecondary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatItem(
                        context,
                        title: 'العملة الافتراضية',
                        value: defaultCurrency?.name ?? 'غير محدد',
                        icon: Icons.star,
                        color: AppColors.lightTextSecondary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatItem(
                        context,
                        title: 'إجمالي العملات',
                        value: currencies.length.toString(),
                        icon: Icons.monetization_on,
                        color: AppColors.lightTextSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(width: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacing4),
        Text(
          value,
          style: AppTypography.createCustomStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
