import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/index.dart';
import '../../../core/models/supplier.dart';
import '../presenters/supplier_presenter.dart';
import 'supplier_form_screen.dart';
import '../../../core/theme/index.dart';

/// شاشة إدارة الموردين
class SupplierManagementScreen extends StatefulWidget {
  const SupplierManagementScreen({Key? key}) : super(key: key);

  @override
  State<SupplierManagementScreen> createState() =>
      _SupplierManagementScreenState();
}

class _SupplierManagementScreenState extends State<SupplierManagementScreen> {
  // مقدم الموردين
  late SupplierPresenter _supplierPresenter;

  // حالة البحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // حالة الفرز
  String _sortField = 'name';
  bool _sortAscending = true;

  // حالة التحميل
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // تهيئة مقدم الموردين
    _supplierPresenter = AppProviders.getLazyPresenter<SupplierPresenter>(
        () => SupplierPresenter());

    // تحميل الموردين
    _loadSuppliers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل الموردين
  Future<void> _loadSuppliers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _supplierPresenter.loadSuppliers();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تحميل الموردين: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// فتح شاشة إضافة مورد جديد
  void _openAddSupplierScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SupplierFormScreen(),
      ),
    ).then((_) => _loadSuppliers());
  }

  /// فتح شاشة تعديل مورد
  void _openEditSupplierScreen(Supplier supplier) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SupplierFormScreen(supplier: supplier as dynamic),
      ),
    ).then((_) => _loadSuppliers());
  }

  /// حذف مورد
  Future<void> _deleteSupplier(Supplier supplier) async {
    final confirmed = await AkConfirmDialog.show(
      context: context,
      title: 'حذف المورد',
      content: 'هل أنت متأكد من حذف المورد "${supplier.name}"؟',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
      type: AkDialogType.danger,
      icon: Icons.delete_forever,
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final success = await _supplierPresenter.deleteSupplier(supplier.id);

        if (success) {
          _showSuccessSnackBar('تم حذف المورد بنجاح');
          _loadSuppliers();
        } else {
          _showErrorSnackBar('فشل حذف المورد');
        }
      } catch (e) {
        _showErrorSnackBar('حدث خطأ أثناء حذف المورد: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// فرز الموردين
  void _sortSuppliers(String field) {
    setState(() {
      if (_sortField == field) {
        _sortAscending = !_sortAscending;
      } else {
        _sortField = field;
        _sortAscending = true;
      }
    });
  }

  /// الحصول على الموردين المفلترة
  List<Supplier> _getFilteredSuppliers() {
    // فلترة الموردين حسب البحث
    final filteredSuppliers = _supplierPresenter.suppliers.where((supplier) {
      final name = supplier.name.toLowerCase();
      final phone = supplier.phone?.toLowerCase() ?? '';
      final email = supplier.email?.toLowerCase() ?? '';
      final query = _searchQuery.toLowerCase();

      return name.contains(query) ||
          phone.contains(query) ||
          email.contains(query);
    }).toList();

    // فرز الموردين
    filteredSuppliers.sort((a, b) {
      int compare;

      switch (_sortField) {
        case 'name':
          compare = a.name.compareTo(b.name);
          break;
        case 'phone':
          final aPhone = a.phone ?? '';
          final bPhone = b.phone ?? '';
          compare = aPhone.compareTo(bPhone);
          break;
        case 'email':
          final aEmail = a.email ?? '';
          final bEmail = b.email ?? '';
          compare = aEmail.compareTo(bEmail);
          break;
        case 'balance':
          compare = a.balance.compareTo(b.balance);
          break;
        default:
          compare = a.name.compareTo(b.name);
      }

      return _sortAscending ? compare : -compare;
    });

    return filteredSuppliers;
  }

  @override
  Widget build(BuildContext context) {
    return SafeLayout(
      title: 'إدارة الموردين',
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadSuppliers,
          tooltip: 'تحديث',
        ),
      ],
      floatingActionButton: AkFloatingButton(
        icon: Icons.add,
        onPressed: _openAddSupplierScreen,
        tooltip: 'إضافة مورد جديد',
      ),
      body: _buildContent(),
      child: Container(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // شريط البحث
        Padding(
          padding: const EdgeInsets.all(16),
          child: AkSearchInput(
            controller: _searchController,
            hint: 'بحث عن مورد...',
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
        ),

        // عدد الموردين
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'عدد الموردين: ${_getFilteredSuppliers().length}',
                style: AppTypography.lightTextTheme.titleMedium,
              ),
              const Spacer(),
              if (_searchQuery.isNotEmpty)
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                      _searchController.clear();
                    });
                  },
                  icon: const Icon(Icons.clear),
                  label: const Text('مسح البحث'),
                ),
            ],
          ),
        ),

        // قائمة الموردين
        Expanded(
          child: _buildSuppliersList(),
        ),
      ],
    );
  }

  /// بناء قائمة الموردين
  Widget _buildSuppliersList() {
    final suppliers = _getFilteredSuppliers();

    if (suppliers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.people_outline,
              size: 64,
              color: AppColors.lightTextSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty
                  ? 'لا يوجد موردين'
                  : 'لا يوجد موردين مطابقين للبحث',
              style: AppTypography.lightTextTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            if (_searchQuery.isEmpty)
              ElevatedButton.icon(
                onPressed: _openAddSupplierScreen,
                icon: const Icon(Icons.add),
                label: const Text('إضافة مورد جديد'),
              ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: [
            DataColumn(
              label: const Text('الاسم'),
              onSort: (_, __) => _sortSuppliers('name'),
            ),
            DataColumn(
              label: const Text('رقم الهاتف'),
              onSort: (_, __) => _sortSuppliers('phone'),
            ),
            DataColumn(
              label: const Text('البريد الإلكتروني'),
              onSort: (_, __) => _sortSuppliers('email'),
            ),
            DataColumn(
              label: const Text('الرصيد'),
              onSort: (_, __) => _sortSuppliers('balance'),
              numeric: true,
            ),
            const DataColumn(
              label: Text('الإجراءات'),
            ),
          ],
          rows: suppliers.map((supplier) {
            return DataRow(
              cells: [
                DataCell(Text(supplier.name)),
                DataCell(Text(supplier.phone ?? '-')),
                DataCell(Text(supplier.email ?? '-')),
                DataCell(
                  Text(
                    supplier.balance.toStringAsFixed(2),
                    style: AppTypography(
                      color: supplier.balance < 0 ? AppColors.error : null,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () => _openEditSupplierScreen(supplier),
                        tooltip: 'تعديل',
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _deleteSupplier(supplier),
                        tooltip: 'حذف',
                        color: AppColors.lightTextSecondary,
                      ),
                    ],
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
        ),
      );
    });
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.success,
        ),
      );
    });
  }
}
