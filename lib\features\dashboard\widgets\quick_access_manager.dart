import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/routes/app_routes.dart';
import 'dart:convert';
import '../../../core/theme/index.dart';

class QuickAccessManager extends StatefulWidget {
  const QuickAccessManager({Key? key}) : super(key: key);

  @override
  State<QuickAccessManager> createState() => _QuickAccessManagerState();
}

class _QuickAccessManagerState extends State<QuickAccessManager> {
  List<Map<String, dynamic>> _availableWidgets = [];
  List<Map<String, dynamic>> _selectedWidgets = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadWidgets();
  }

  Future<void> _loadWidgets() async {
    setState(() {
      _isLoading = true;
    });

    // قائمة الودجات المتاحة
    _availableWidgets = [
      {
        'id': 'new_sale',
        'title': 'بيع جديد',
        'icon': Icons.add_shopping_cart,
        'color': AppColors.success,
        'route': AppRoutes.saleForm,
      },
      {
        'id': 'add_product',
        'title': 'إضافة منتج',
        'icon': Icons.inventory_2_outlined,
        'color': AppColors.info,
        'route': AppRoutes.productForm,
      },
      {
        'id': 'add_customer',
        'title': 'إضافة عميل',
        'icon': Icons.person_add_outlined,
        'color': AppColors.warning,
        'route': AppRoutes.customerForm,
      },
      {
        'id': 'reports',
        'title': 'التقارير',
        'icon': Icons.bar_chart_outlined,
        'color': AppColors.accent,
        'route': AppRoutes.financialReports,
      },
      {
        'id': 'add_supplier',
        'title': 'إضافة مورد',
        'icon': Icons.business_outlined,
        'color': AppColors.teal,
        'route': AppRoutes.supplierForm,
      },
      {
        'id': 'receipt_voucher',
        'title': 'سند قبض',
        'icon': Icons.arrow_downward,
        'color': AppColors.indigo,
        'route': AppRoutes.receiptVoucher,
      },
      {
        'id': 'payment_voucher',
        'title': 'سند صرف',
        'icon': Icons.arrow_upward,
        'color': AppColors.error,
        'route': AppRoutes.paymentVoucher,
      },
      {
        'id': 'inventory_management',
        'title': 'إدارة المخزون',
        'icon': Icons.inventory_2_outlined,
        'color': AppColors.amber,
        'route': AppRoutes.inventoryManagement,
      },
      {
        'id': 'dashboard',
        'title': 'لوحة المعلومات',
        'icon': Icons.dashboard,
        'color': AppColors.accent,
        'route': AppRoutes.dashboard,
      },
      {
        'id': 'export_import',
        'title': 'تصدير/استيراد',
        'icon': Icons.import_export,
        'color': AppColors.brown,
        'route': AppRoutes.exportImport,
      },
    ];

    // استرجاع الودجات المحددة من التخزين المحلي
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedWidgets = prefs.getString('quick_access_widgets');

      if (savedWidgets != null) {
        final List<dynamic> decodedWidgets = jsonDecode(savedWidgets);

        // تحويل القائمة المسترجعة إلى قائمة من الخرائط
        _selectedWidgets = decodedWidgets.map<Map<String, dynamic>>((item) {
          return {
            'id': item['id'],
            'title': item['title'],
            'icon': _getIconFromString(item['icon']),
            'color': Color(item['color']),
            'route': item['route'],
          };
        }).toList();
      } else {
        // إذا لم يتم العثور على ودجات محفوظة، استخدم الأربعة الأولى كافتراضي
        _selectedWidgets = _availableWidgets.take(4).toList();
      }
    } catch (e) {
      // في حالة حدوث خطأ، استخدم الأربعة الأولى كافتراضي
      _selectedWidgets = _availableWidgets.take(4).toList();
    }

    setState(() {
      _isLoading = false;
    });
  }

  IconData _getIconFromString(dynamic iconData) {
    // تحويل النص إلى رمز
    if (iconData is int) {
      return IconData(iconData, fontFamily: 'MaterialIcons');
    }
    // إذا لم يتم التعرف على الرمز، استخدم رمز افتراضي
    return Icons.widgets;
  }

  Future<void> _saveWidgets() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحويل الودجات المحددة إلى تنسيق قابل للتخزين
      final List<Map<String, dynamic>> widgetsToSave =
          _selectedWidgets.map((widget) {
        return {
          'id': widget['id'],
          'title': widget['title'],
          'icon': (widget['icon'] as IconData).codePoint,
          'color': (widget['color'] as Color).toARGB32(),
          'route': widget['route'],
        };
      }).toList();

      await prefs.setString('quick_access_widgets', jsonEncode(widgetsToSave));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ التغييرات بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء الحفظ: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'تخصيص الوصول السريع',
                        style: AppTypography(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDimensions.spacing16),
                  const Text(
                    'الودجات المحددة',
                    style: AppTypography(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppDimensions.spacing8),
                  _buildSelectedWidgets(),
                  const SizedBox(height: AppDimensions.spacing16),
                  const Text(
                    'الودجات المتاحة',
                    style: AppTypography(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppDimensions.spacing8),
                  _buildAvailableWidgets(),
                  const SizedBox(height: AppDimensions.spacing16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('إلغاء'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          _saveWidgets();
                          Navigator.of(context).pop();
                        },
                        child: const Text('حفظ'),
                      ),
                    ],
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildSelectedWidgets() {
    if (_selectedWidgets.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: Text('لم يتم اختيار أي ودجات بعد'),
          ),
        ),
      );
    }

    return Container(
      constraints: const BoxConstraints(maxHeight: 200),
      child: ReorderableListView(
        shrinkWrap: true,
        onReorder: (oldIndex, newIndex) {
          setState(() {
            if (newIndex > oldIndex) {
              newIndex -= 1;
            }
            final item = _selectedWidgets.removeAt(oldIndex);
            _selectedWidgets.insert(newIndex, item);
          });
        },
        children: _selectedWidgets.map((widget) {
          return ListTile(
            key: Key(widget['id']),
            leading: CircleAvatar(
              backgroundColor: widget['color'],
              child: Icon(widget['icon'], color: AppColors.onPrimary),
            ),
            title: Text(widget['title']),
            trailing: IconButton(
              icon: const Icon(Icons.remove_circle_outline,
                  color: AppColors.error),
              onPressed: () {
                setState(() {
                  _selectedWidgets.remove(widget);
                });
              },
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildAvailableWidgets() {
    // فلترة الودجات المتاحة لإزالة تلك التي تم اختيارها بالفعل
    final availableWidgets = _availableWidgets.where((widget) {
      return !_selectedWidgets
          .any((selected) => selected['id'] == widget['id']);
    }).toList();

    if (availableWidgets.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: Text('تم اختيار جميع الودجات المتاحة'),
          ),
        ),
      );
    }

    return Container(
      constraints: const BoxConstraints(maxHeight: 200),
      child: ListView(
        shrinkWrap: true,
        children: availableWidgets.map((widget) {
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: widget['color'],
              child: Icon(widget['icon'], color: AppColors.onPrimary),
            ),
            title: Text(widget['title']),
            trailing: IconButton(
              icon: const Icon(Icons.add_circle_outline,
                  color: AppColors.success),
              onPressed: () {
                if (_selectedWidgets.length < 8) {
                  setState(() {
                    _selectedWidgets.add(widget);
                  });
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يمكنك اختيار 8 ودجات كحد أقصى'),
                    ),
                  );
                }
              },
            ),
          );
        }).toList(),
      ),
    );
  }
}
