import 'package:uuid/uuid.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/models/sale.dart';
import '../../../core/models/sale_item.dart';
import '../../products/services/product_service.dart';

/// خدمة المبيعات
class SaleService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final ProductService _productService = ProductService();

  /// إنشاء عملية بيع جديدة
  Future<String?> createSale({
    required List<SaleItem> items,
    String? customerId,
    String? customerName,
    required String paymentMethod,
    required double amountPaid,
    required double subtotal,
    required double discount,
    required bool isDiscountPercentage,
    required double tax,
    required double total,
    String? notes,
  }) async {
    final db = await _databaseHelper.database;

    try {
      // بدء المعاملة
      return await db.transaction((txn) async {
        // إنشاء معرف فريد للبيع
        final saleId = const Uuid().v4();
        final now = DateTime.now().toIso8601String();

        // إدخال البيع الرئيسي في جدول invoices
        await txn.insert(
          'invoices',
          {
            'id': saleId,
            'invoice_number': 'SALE-${DateTime.now().millisecondsSinceEpoch}',
            'invoice_type': 'sale',
            'customer_id': customerId,
            'warehouse_id':
                '048a7d62-3096-4b75-9ca0-c8927aae3c7e', // المخزن الافتراضي
            'date': now,
            'status': 'confirmed',
            'subtotal': subtotal,
            'discount_type': isDiscountPercentage ? 'percentage' : 'amount',
            'discount_value': discount,
            'tax_amount': tax,
            'total': total,
            'paid_amount': amountPaid,
            'balance': total - amountPaid,
            'notes': notes,
            'created_at': now,
            'updated_at': now,
            'is_deleted': 0,
          },
        );

        // إدخال عناصر البيع
        for (final item in items) {
          final itemId = const Uuid().v4();

          await txn.insert(
            'invoice_items',
            {
              'id': itemId,
              'invoice_id': saleId,
              'product_id': item.productId,
              'quantity': item.quantity,
              'unit_price': item.price,
              'discount_type':
                  item.isDiscountPercentage ? 'percentage' : 'amount',
              'discount_value': item.discount,
              'tax_rate': item.isTaxPercentage ? item.tax : 0,
              'tax_amount': item.isTaxPercentage
                  ? (item.subtotal * item.tax / 100)
                  : item.tax,
              'subtotal': item.subtotal,
              'total': item.total,
              'created_at': now,
              'is_deleted': 0,
            },
          );

          // تحديث مخزون المنتج
          if (item.productId.isNotEmpty) {
            await _productService.updateProductStock(
              item.productId,
              -item.quantity,
            );
          }
        }

        // إدخال الدفع
        await txn.insert(
          'payments',
          {
            'id': const Uuid().v4(),
            'sale_id': saleId,
            'customer_id': customerId,
            'amount': amountPaid,
            'method': paymentMethod,
            'status': 'completed',
            'created_at': now,
            'is_deleted': 0,
          },
        );

        // تحديث رصيد العميل إذا كان الدفع آجلاً
        if (customerId != null && paymentMethod == 'credit') {
          final remainingAmount = total - amountPaid;
          if (remainingAmount > 0) {
            await txn.insert(
              'customer_transactions',
              {
                'id': const Uuid().v4(),
                'customer_id': customerId,
                'amount': -remainingAmount,
                'type': 'sale',
                'reference_id': saleId,
                'notes': 'بيع آجل',
                'created_at': now,
                'is_deleted': 0,
              },
            );

            // تحديث رصيد العميل في جدول العملاء
            await txn.rawUpdate(
              'UPDATE customers SET balance = balance - ? WHERE id = ?',
              [remainingAmount, customerId],
            );
          }
        }

        return saleId;
      });
    } catch (e) {
      AppLogger.error('خطأ في إنشاء عملية بيع', error: e);
      return null;
    }
  }

  /// الحصول على جميع المبيعات
  Future<List<Sale>> getAllSales({Map<String, dynamic>? filters}) async {
    try {
      final db = await _databaseHelper.database;

      // بناء استعلام SQL
      String query =
          'SELECT * FROM invoices WHERE is_deleted = 0 AND invoice_type = ?';
      List<dynamic> args = ['sale'];

      // إضافة الفلاتر إذا كانت موجودة
      if (filters != null) {
        // فلتر نوع الفاتورة
        if (filters['type'] != null) {
          query += ' AND invoice_type = ?';
          args.add(filters['type']);
        }

        // فلتر البحث
        if (filters['search'] != null &&
            filters['search'].toString().isNotEmpty) {
          query += ' AND (reference_number LIKE ? OR customer_name LIKE ?)';
          final searchPattern = '%${filters['search']}%';
          args.addAll([searchPattern, searchPattern]);
        }

        // فلتر الحالة
        if (filters['status'] != null &&
            filters['status'].toString().isNotEmpty) {
          query += ' AND status = ?';
          args.add(filters['status']);
        }

        // فلتر العميل
        if (filters['customerId'] != null &&
            filters['customerId'].toString().isNotEmpty) {
          query += ' AND customer_id = ?';
          args.add(filters['customerId']);
        }

        // فلتر التاريخ
        if (filters['startDate'] != null) {
          query += ' AND created_at >= ?';
          args.add(filters['startDate'].toIso8601String());
        }

        if (filters['endDate'] != null) {
          query += ' AND created_at <= ?';
          args.add(filters['endDate'].toIso8601String());
        }
      }

      // إضافة الترتيب
      query += ' ORDER BY created_at DESC';

      // تنفيذ الاستعلام
      final List<Map<String, dynamic>> salesMaps =
          await db.rawQuery(query, args);

      // تحويل المبيعات إلى كائنات
      final List<Sale> sales = [];

      for (final saleMap in salesMaps) {
        // الحصول على عناصر البيع
        final List<Map<String, dynamic>> itemsMaps = await db.query(
          'invoice_items',
          where: 'invoice_id = ? AND is_deleted = ?',
          whereArgs: [saleMap['id'], 0],
        );

        // تحويل عناصر البيع إلى كائنات
        final List<SaleItem> items = itemsMaps.map((itemMap) {
          return SaleItem.fromMap(itemMap);
        }).toList();

        // إنشاء كائن البيع
        final sale = Sale.fromMap(saleMap, items: items);
        sales.add(sale);
      }

      return sales;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على المبيعات', error: e);
      return [];
    }
  }

  /// الحصول على مبيع بواسطة المعرف
  Future<Sale?> getSaleById(String id) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على البيع الرئيسي
      final List<Map<String, dynamic>> salesMaps = await db.query(
        'invoices',
        where: 'id = ? AND is_deleted = ? AND invoice_type = ?',
        whereArgs: [id, 0, 'sale'],
      );

      if (salesMaps.isEmpty) {
        return null;
      }

      // الحصول على عناصر البيع
      final List<Map<String, dynamic>> itemsMaps = await db.query(
        'invoice_items',
        where: 'invoice_id = ? AND is_deleted = ?',
        whereArgs: [id, 0],
      );

      // تحويل عناصر البيع إلى كائنات
      final List<SaleItem> items = itemsMaps.map((itemMap) {
        return SaleItem.fromMap(itemMap);
      }).toList();

      // إنشاء كائن البيع
      return Sale.fromMap(salesMaps.first, items: items);
    } catch (e) {
      AppLogger.error('خطأ في الحصول على المبيع', error: e);
      return null;
    }
  }

  /// الحصول على مبيعات عميل معين
  Future<List<Sale>> getSalesByCustomerId(String customerId) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على المبيعات الرئيسية
      final List<Map<String, dynamic>> salesMaps = await db.query(
        'invoices',
        where: 'customer_id = ? AND is_deleted = ? AND invoice_type = ?',
        whereArgs: [customerId, 0, 'sale'],
        orderBy: 'created_at DESC',
      );

      // تحويل المبيعات إلى كائنات
      final List<Sale> sales = [];

      for (final saleMap in salesMaps) {
        // الحصول على عناصر البيع
        final List<Map<String, dynamic>> itemsMaps = await db.query(
          'invoice_items',
          where: 'invoice_id = ? AND is_deleted = ?',
          whereArgs: [saleMap['id'], 0],
        );

        // تحويل عناصر البيع إلى كائنات
        final List<SaleItem> items = itemsMaps.map((itemMap) {
          return SaleItem.fromMap(itemMap);
        }).toList();

        // إنشاء كائن البيع
        final sale = Sale.fromMap(saleMap, items: items);
        sales.add(sale);
      }

      return sales;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على مبيعات العميل', error: e);
      return [];
    }
  }

  /// الحصول على مبيعات في فترة زمنية معينة
  Future<List<Sale>> getSalesByDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      final db = await _databaseHelper.database;

      // تحويل التواريخ إلى نص
      final startDateStr = startDate.toIso8601String();
      final endDateStr = endDate.toIso8601String();

      // الحصول على المبيعات الرئيسية
      final List<Map<String, dynamic>> salesMaps = await db.query(
        'invoices',
        where:
            'created_at BETWEEN ? AND ? AND is_deleted = ? AND invoice_type = ?',
        whereArgs: [startDateStr, endDateStr, 0, 'sale'],
        orderBy: 'created_at DESC',
      );

      // تحويل المبيعات إلى كائنات
      final List<Sale> sales = [];

      for (final saleMap in salesMaps) {
        // الحصول على عناصر البيع
        final List<Map<String, dynamic>> itemsMaps = await db.query(
          'invoice_items',
          where: 'invoice_id = ? AND is_deleted = ?',
          whereArgs: [saleMap['id'], 0],
        );

        // تحويل عناصر البيع إلى كائنات
        final List<SaleItem> items = itemsMaps.map((itemMap) {
          return SaleItem.fromMap(itemMap);
        }).toList();

        // إنشاء كائن البيع
        final sale = Sale.fromMap(saleMap, items: items);
        sales.add(sale);
      }

      return sales;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على المبيعات في الفترة الزمنية', error: e);
      return [];
    }
  }

  /// إلغاء مبيع
  Future<bool> cancelSale(String id) async {
    final db = await _databaseHelper.database;

    try {
      // بدء المعاملة
      await db.transaction((txn) async {
        final now = DateTime.now().toIso8601String();

        // الحصول على عناصر البيع
        final List<Map<String, dynamic>> itemsMaps = await txn.query(
          'invoice_items',
          where: 'invoice_id = ? AND is_deleted = ?',
          whereArgs: [id, 0],
        );

        // إعادة المخزون
        for (final itemMap in itemsMaps) {
          final productId = itemMap['product_id'];
          final quantity = itemMap['quantity'] as double;

          // التحقق مما إذا كان المنتج خدمة
          final List<Map<String, dynamic>> productsMaps = await txn.query(
            'products',
            columns: ['is_service'],
            where: 'id = ?',
            whereArgs: [productId],
          );

          if (productsMaps.isNotEmpty &&
              productsMaps.first['is_service'] == 0) {
            await _productService.updateProductStock(
              productId,
              quantity,
            );
          }
        }

        // الحصول على معلومات البيع
        final List<Map<String, dynamic>> salesMaps = await txn.query(
          'invoices',
          where: 'id = ? AND is_deleted = ? AND invoice_type = ?',
          whereArgs: [id, 0, 'sale'],
        );

        if (salesMaps.isEmpty) {
          return false;
        }

        final saleMap = salesMaps.first;
        final customerId = saleMap['customer_id'];

        // إذا كان البيع آجلاً، إعادة رصيد العميل
        if (customerId != null && saleMap['payment_method'] == 'credit') {
          final total = saleMap['total'] as double;
          final amountPaid = saleMap['amount_paid'] as double;
          final remainingAmount = total - amountPaid;

          if (remainingAmount > 0) {
            await txn.insert(
              'customer_transactions',
              {
                'id': const Uuid().v4(),
                'customer_id': customerId,
                'amount': remainingAmount,
                'type': 'sale_cancel',
                'reference_id': id,
                'notes': 'إلغاء بيع آجل',
                'created_at': now,
                'is_deleted': 0,
              },
            );

            // تحديث رصيد العميل في جدول العملاء
            await txn.rawUpdate(
              'UPDATE customers SET balance = balance + ? WHERE id = ?',
              [remainingAmount, customerId],
            );
          }
        }

        // تحديث حالة البيع
        await txn.update(
          'invoices',
          {
            'status': 'cancelled',
            'updated_at': now,
          },
          where: 'id = ?',
          whereArgs: [id],
        );
      });

      return true;
    } catch (e) {
      AppLogger.error('خطأ في إلغاء المبيع', error: e);
      return false;
    }
  }

  /// حذف مبيع (حذف منطقي)
  Future<bool> deleteSale(String id) async {
    try {
      final db = await _databaseHelper.database;

      await db.update(
        'invoices',
        {
          'is_deleted': 1,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ? AND invoice_type = ?',
        whereArgs: [id, 'sale'],
      );

      return true;
    } catch (e) {
      AppLogger.error('خطأ في حذف المبيع', error: e);
      return false;
    }
  }
}
