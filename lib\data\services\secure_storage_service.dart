import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// خدمة التخزين الآمن للبيانات الحساسة
class SecureStorageService {
  final FlutterSecureStorage _storage;

  /// إنشاء خدمة التخزين الآمن
  SecureStorageService() : _storage = const FlutterSecureStorage();

  /// حفظ قيمة نصية
  Future<void> write(String key, String value) async {
    await _storage.write(key: key, value: value);
  }

  /// قراءة قيمة نصية
  Future<String?> read(String key) async {
    return await _storage.read(key: key);
  }

  /// حذف قيمة
  Future<void> delete(String key) async {
    await _storage.delete(key: key);
  }

  /// حذف جميع القيم
  Future<void> deleteAll() async {
    await _storage.deleteAll();
  }

  /// حفظ كائن JSON
  Future<void> writeJson(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    await write(key, jsonString);
  }

  /// قراءة كائن JSON
  Future<Map<String, dynamic>?> readJson(String key) async {
    final jsonString = await read(key);
    if (jsonString == null) return null;
    return jsonDecode(jsonString) as Map<String, dynamic>;
  }

  /// الحصول على بيانات الاعتماد المحفوظة
  Future<Map<String, dynamic>?> getCredentials() async {
    return await readJson('user_credentials');
  }

  /// حفظ بيانات الاعتماد
  Future<void> saveCredentials(String email, String password) async {
    await writeJson('user_credentials', {
      'email': email,
      'password': password,
    });
  }

  /// حذف بيانات الاعتماد
  Future<void> clearCredentials() async {
    await delete('user_credentials');
  }
}
