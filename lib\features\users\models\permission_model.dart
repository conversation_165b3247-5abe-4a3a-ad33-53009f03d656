/// نموذج الصلاحية
class PermissionModel {
  final String id;
  final String name;
  final String? description;
  final String module;
  final String? code;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isDeleted;

  PermissionModel({
    required this.id,
    required this.name,
    this.description,
    required this.module,
    this.code,
    this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
  });

  /// إنشاء نموذج من خريطة (من قاعدة البيانات)
  factory PermissionModel.fromMap(Map<String, dynamic> map) {
    return PermissionModel(
      id: map['id'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      module: map['module'] as String,
      code: _extractCodeFromDescription(map['description'] as String?),
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
      isDeleted: (map['is_deleted'] as int?) == 1,
    );
  }

  /// استخراج رمز الصلاحية من الوصف
  static String? _extractCodeFromDescription(String? description) {
    if (description == null) return null;
    
    final match = RegExp(r'رمز: ([^)]+)').firstMatch(description);
    return match?.group(1);
  }

  /// تحويل النموذج إلى خريطة (لقاعدة البيانات)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'module': module,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// نسخة معدلة من النموذج
  PermissionModel copyWith({
    String? id,
    String? name,
    String? description,
    String? module,
    String? code,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
  }) {
    return PermissionModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      module: module ?? this.module,
      code: code ?? this.code,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  @override
  String toString() {
    return 'PermissionModel(id: $id, name: $name, module: $module, code: $code)';
  }
}
