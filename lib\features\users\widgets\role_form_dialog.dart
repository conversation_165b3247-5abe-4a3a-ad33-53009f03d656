import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../../core/auth/models/user_role.dart';
import '../../../core/utils/index.dart';
import '../../../core/theme/index.dart';

/// مربع حوار نموذج الدور
class RoleFormDialog extends StatefulWidget {
  final UserRole? role;

  const RoleFormDialog({
    Key? key,
    this.role,
  }) : super(key: key);

  @override
  State<RoleFormDialog> createState() => _RoleFormDialogState();
}

class _RoleFormDialogState extends State<RoleFormDialog> {
  // مفتاح النموذج
  final _formKey = GlobalKey<FormState>();

  // وحدات تحكم الحقول
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _displayNameController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // تعبئة النموذج إذا كان هناك دور
    if (widget.role != null) {
      _nameController.text = widget.role!.name;
      _displayNameController.text = widget.role!.displayName;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _displayNameController.dispose();
    super.dispose();
  }

  /// حفظ النموذج
  void _saveForm() {
    if (_formKey.currentState!.validate()) {
      final role = UserRole(
        id: widget.role?.id ?? const Uuid().v4(),
        name: _nameController.text,
        displayName: _displayNameController.text,
        permissions: widget.role?.permissions ?? [],
        isCustom: true,
        createdAt: widget.role?.createdAt,
        updatedAt: DateTime.now(),
      );

      Navigator.pop(context, role);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.role == null ? 'إضافة دور جديد' : 'تعديل الدور'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // اسم الدور
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الدور *',
                hintText: 'أدخل اسم الدور (بالإنجليزية)',
                prefixIcon: Icon(Icons.badge),
              ),
              validator: Validators.required('اسم الدور'),
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: 16),

            // اسم العرض
            TextFormField(
              controller: _displayNameController,
              decoration: const InputDecoration(
                labelText: 'اسم العرض *',
                hintText: 'أدخل اسم العرض (بالعربية)',
                prefixIcon: Icon(Icons.text_fields),
              ),
              validator: Validators.required('اسم العرض'),
              textInputAction: TextInputAction.done,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveForm,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
          ),
          child: Text(widget.role == null ? 'إضافة' : 'حفظ'),
        ),
      ],
    );
  }
}
