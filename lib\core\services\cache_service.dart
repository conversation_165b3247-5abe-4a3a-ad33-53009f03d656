import 'dart:async';
import 'dart:collection';
import '../utils/app_logger.dart';

/// أولوية التخزين المؤقت
enum CachePriority {
  /// أولوية منخفضة - يتم حذفها أولاً عند تنظيف التخزين المؤقت
  low,
  
  /// أولوية متوسطة
  medium,
  
  /// أولوية عالية - يتم الاحتفاظ بها لفترة أطول
  high,
  
  /// أولوية قصوى - لا يتم حذفها إلا عند الضرورة القصوى
  critical
}

/// نموذج عنصر التخزين المؤقت
class CacheEntry<T> {
  /// البيانات المخزنة
  final T data;
  
  /// وقت الإنشاء
  final DateTime timestamp;
  
  /// مدة الصلاحية
  final Duration expiration;
  
  /// أولوية التخزين المؤقت
  final CachePriority priority;
  
  /// عدد مرات الوصول
  int accessCount = 0;
  
  /// آخر وقت وصول
  DateTime lastAccessTime;

  CacheEntry({
    required this.data,
    required this.timestamp,
    required this.expiration,
    required this.priority,
  }) : lastAccessTime = DateTime.now();

  /// التحقق من انتهاء صلاحية العنصر
  bool isExpired() {
    return DateTime.now().difference(timestamp) > expiration;
  }

  /// تحديث عدد مرات الوصول وآخر وقت وصول
  void accessed() {
    accessCount++;
    lastAccessTime = DateTime.now();
  }
}

/// خدمة التخزين المؤقت
/// تقوم هذه الخدمة بتخزين البيانات المستخدمة بشكل متكرر في الذاكرة
/// لتحسين الأداء وتقليل الوصول إلى قاعدة البيانات
class CacheService {
  // Singleton pattern
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  /// الحد الأقصى لحجم التخزين المؤقت
  static const int _maxCacheSize = 1000;
  
  /// نسبة التنظيف عند امتلاء التخزين المؤقت
  static const double _cleanupRatio = 0.2;
  
  /// التخزين المؤقت
  final Map<String, CacheEntry> _cache = HashMap<String, CacheEntry>();
  
  /// مؤقت تنظيف التخزين المؤقت
  Timer? _cleanupTimer;

  /// تهيئة خدمة التخزين المؤقت
  void init() {
    // تنظيف التخزين المؤقت كل 15 دقيقة
    _cleanupTimer = Timer.periodic(const Duration(minutes: 15), (_) {
      cleanCache();
    });
    
    AppLogger.info('تم تهيئة خدمة التخزين المؤقت');
  }

  /// إيقاف خدمة التخزين المؤقت
  void dispose() {
    _cleanupTimer?.cancel();
    _cache.clear();
    
    AppLogger.info('تم إيقاف خدمة التخزين المؤقت');
  }

  /// تخزين بيانات في التخزين المؤقت
  void put<T>(
    String key,
    T data, {
    Duration expiration = const Duration(minutes: 30),
    CachePriority priority = CachePriority.medium,
  }) {
    // التحقق من حجم التخزين المؤقت
    if (_cache.length >= _maxCacheSize) {
      _performCleanup();
    }
    
    _cache[key] = CacheEntry<T>(
      data: data,
      timestamp: DateTime.now(),
      expiration: expiration,
      priority: priority,
    );
  }

  /// الحصول على بيانات من التخزين المؤقت
  T? get<T>(String key) {
    final entry = _cache[key];
    
    if (entry == null) {
      return null;
    }
    
    if (entry.isExpired()) {
      _cache.remove(key);
      return null;
    }
    
    // تحديث عدد مرات الوصول وآخر وقت وصول
    entry.accessed();
    
    return entry.data as T;
  }

  /// الحصول على بيانات من التخزين المؤقت أو تنفيذ دالة للحصول عليها
  Future<T> getOrFetch<T>({
    required String key,
    required Future<T> Function() fetchFunction,
    Duration expiration = const Duration(minutes: 30),
    CachePriority priority = CachePriority.medium,
  }) async {
    final cachedData = get<T>(key);
    
    if (cachedData != null) {
      return cachedData;
    }
    
    final data = await fetchFunction();
    put<T>(key, data, expiration: expiration, priority: priority);
    
    return data;
  }

  /// حذف بيانات من التخزين المؤقت
  void remove(String key) {
    _cache.remove(key);
  }

  /// حذف جميع البيانات من التخزين المؤقت
  void clear() {
    _cache.clear();
  }

  /// تنظيف التخزين المؤقت
  void cleanCache() {
    // حذف العناصر منتهية الصلاحية
    _cache.removeWhere((key, entry) => entry.isExpired());
    
    // التحقق من حجم التخزين المؤقت
    if (_cache.length >= _maxCacheSize) {
      _performCleanup();
    }
  }

  /// تنظيف التخزين المؤقت عند امتلائه
  void _performCleanup() {
    // عدد العناصر التي سيتم حذفها
    final itemsToRemove = (_maxCacheSize * _cleanupRatio).round();
    
    // ترتيب العناصر حسب الأولوية وعدد مرات الوصول وآخر وقت وصول
    final entries = _cache.entries.toList()
      ..sort((a, b) {
        // ترتيب حسب الأولوية أولاً
        final priorityComparison = a.value.priority.index.compareTo(b.value.priority.index);
        if (priorityComparison != 0) {
          return priorityComparison;
        }
        
        // ثم حسب عدد مرات الوصول
        final accessCountComparison = a.value.accessCount.compareTo(b.value.accessCount);
        if (accessCountComparison != 0) {
          return accessCountComparison;
        }
        
        // ثم حسب آخر وقت وصول
        return a.value.lastAccessTime.compareTo(b.value.lastAccessTime);
      });
    
    // حذف العناصر ذات الأولوية المنخفضة وعدد مرات الوصول الأقل وآخر وقت وصول الأقدم
    for (var i = 0; i < itemsToRemove && i < entries.length; i++) {
      _cache.remove(entries[i].key);
    }
  }
}
