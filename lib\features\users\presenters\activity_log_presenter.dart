import 'package:flutter/foundation.dart';
import '../../../core/utils/error_tracker.dart';
import '../models/activity_log.dart';
import '../services/activity_log_service.dart';

/// مقدم سجل النشاطات
class ActivityLogPresenter extends ChangeNotifier {
  final ActivityLogService _activityLogService = ActivityLogService();
  
  List<ActivityLog> _activities = [];
  bool _isLoading = false;
  String? _errorMessage;
  
  /// الحصول على قائمة النشاطات
  List<ActivityLog> get activities => _activities;
  
  /// حالة التحميل
  bool get isLoading => _isLoading;
  
  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;
  
  /// تحميل النشاطات
  Future<void> loadActivities({
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? action,
    String? module,
  }) async {
    _setLoading(true);
    
    try {
      _activities = await _activityLogService.getActivities(
        startDate: startDate,
        endDate: endDate,
        userId: userId,
        action: action,
        module: module,
      );
      notifyListeners();
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في تحميل سجل النشاطات: ${e.toString()}');
      ErrorTracker.captureError(
        'فشل في تحميل سجل النشاطات',
        error: e,
        stackTrace: stackTrace,
        context: {
          'startDate': startDate?.toIso8601String(),
          'endDate': endDate?.toIso8601String(),
          'userId': userId,
          'action': action,
          'module': module,
        },
      );
    } finally {
      _setLoading(false);
    }
  }
  
  /// تسجيل نشاط جديد
  Future<bool> logActivity({
    required String userId,
    required String userName,
    required String action,
    required String module,
    required String details,
  }) async {
    try {
      final success = await _activityLogService.logActivity(
        userId: userId,
        userName: userName,
        action: action,
        module: module,
        details: details,
      );
      
      if (success) {
        // إعادة تحميل النشاطات إذا كانت القائمة غير فارغة
        if (_activities.isNotEmpty) {
          await loadActivities();
        }
      }
      
      return success;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تسجيل النشاط',
        error: e,
        stackTrace: stackTrace,
        context: {
          'userId': userId,
          'userName': userName,
          'action': action,
          'module': module,
          'details': details,
        },
      );
      return false;
    }
  }
  
  /// الحصول على قائمة المستخدمين الفريدة
  List<String> getUniqueUsers() {
    final users = <String>{};
    for (final activity in _activities) {
      users.add(activity.userName);
    }
    return users.toList();
  }
  
  /// الحصول على قائمة الإجراءات الفريدة
  List<String> getUniqueActions() {
    final actions = <String>{};
    for (final activity in _activities) {
      actions.add(activity.action);
    }
    return actions.toList();
  }
  
  /// الحصول على قائمة الوحدات الفريدة
  List<String> getUniqueModules() {
    final modules = <String>{};
    for (final activity in _activities) {
      modules.add(activity.module);
    }
    return modules.toList();
  }
  
  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// تعيين رسالة الخطأ
  void _setErrorMessage(String? message) {
    _errorMessage = message;
    notifyListeners();
  }
}
