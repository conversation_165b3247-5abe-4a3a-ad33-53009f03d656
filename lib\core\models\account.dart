import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// أنواع الحسابات
enum AccountType {
  asset, // أصول
  liability, // خصوم
  equity, // حقوق ملكية
  revenue, // إيرادات
  expense, // مصروفات
  customer, // عميل
  supplier, // مورد
  cash, // نقدي
  bank, // بنك
  other, // أخرى
}

/// نموذج الحساب الموحد
/// تم توحيده من جميع نماذج الحسابات في المشروع
class Account extends BaseModel {
  // معلومات أساسية
  final String code;
  final String name;
  final String? description;
  final AccountType type;
  final String? category;
  final String? subcategory;

  // معلومات الهيكل الهرمي
  final String? parentId;
  final Account? parent; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final String? parentName; // للعرض فقط، لا يتم تخزينه في قاعدة البيانات
  final bool isParent;
  final int level;

  // معلومات الرصيد
  final double balance;
  final double? openingBalance;

  // معلومات إضافية
  final bool isCashEquivalent;
  final bool isBank;
  final bool isActive;
  final bool isDeletable;
  final bool isSystem;

  Account({
    String? id,
    required this.code,
    required this.name,
    this.description,
    required this.type,
    this.category,
    this.subcategory,
    this.parentId,
    this.parent,
    this.parentName,
    this.isParent = false,
    this.level = 0,
    this.balance = 0.0,
    this.openingBalance,
    this.isCashEquivalent = false,
    this.isBank = false,
    this.isActive = true,
    this.isDeletable = true,
    this.isSystem = false,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذا الحساب مع استبدال الحقول المحددة بقيم جديدة
  Account copyWith({
    String? id,
    String? code,
    String? name,
    String? description,
    AccountType? type,
    String? category,
    String? subcategory,
    String? parentId,
    Account? parent,
    String? parentName,
    bool? isParent,
    int? level,
    double? balance,
    double? openingBalance,
    bool? isCashEquivalent,
    bool? isBank,
    bool? isActive,
    bool? isDeletable,
    bool? isSystem,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Account(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      parentId: parentId ?? this.parentId,
      parent: parent ?? this.parent,
      parentName: parentName ?? this.parentName,
      isParent: isParent ?? this.isParent,
      level: level ?? this.level,
      balance: balance ?? this.balance,
      openingBalance: openingBalance ?? this.openingBalance,
      isCashEquivalent: isCashEquivalent ?? this.isCashEquivalent,
      isBank: isBank ?? this.isBank,
      isActive: isActive ?? this.isActive,
      isDeletable: isDeletable ?? this.isDeletable,
      isSystem: isSystem ?? this.isSystem,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل الحساب إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'category': category,
      'subcategory': subcategory,
      'parent_id': parentId,
      'parent_name': parentName,
      'is_parent': isParent ? 1 : 0,
      'level': level,
      'balance': balance,
      'opening_balance': openingBalance,
      'is_cash_equivalent': isCashEquivalent ? 1 : 0,
      'is_bank': isBank ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'is_deletable': isDeletable ? 1 : 0,
      'is_system': isSystem ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء حساب من Map
  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id'],
      code: map['code'] ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      type: _parseAccountType(map['type']),
      category: map['category'],
      subcategory: map['subcategory'],
      parentId: map['parent_id'],
      parentName: map['parent_name'],
      parent: map['parent'] != null ? Account.fromMap(map['parent']) : null,
      isParent: map['is_parent'] == 1,
      level: map['level'] ?? 0,
      balance: map['balance'] is int
          ? (map['balance'] as int).toDouble()
          : (map['balance'] as double? ?? 0.0),
      openingBalance: map['opening_balance'] is int
          ? (map['opening_balance'] as int).toDouble()
          : (map['opening_balance'] as double?),
      isCashEquivalent: map['is_cash_equivalent'] == 1,
      isBank: map['is_bank'] == 1,
      isActive: map['is_active'] == 1,
      isDeletable: map['is_deletable'] == 1,
      isSystem: map['is_system'] == 1,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحليل نوع الحساب من النص
  static AccountType _parseAccountType(String? typeString) {
    switch (typeString) {
      case 'asset':
        return AccountType.asset;
      case 'liability':
        return AccountType.liability;
      case 'equity':
        return AccountType.equity;
      case 'revenue':
        return AccountType.revenue;
      case 'expense':
        return AccountType.expense;
      case 'customer':
        return AccountType.customer;
      case 'supplier':
        return AccountType.supplier;
      case 'cash':
        return AccountType.cash;
      case 'bank':
        return AccountType.bank;
      case 'other':
        return AccountType.other;
      default:
        return AccountType.other;
    }
  }

  /// تحويل الحساب إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء حساب من JSON
  factory Account.fromJson(String source) =>
      Account.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'Account(id: $id, code: $code, name: $name, type: $type, balance: $balance)';
  }
}
