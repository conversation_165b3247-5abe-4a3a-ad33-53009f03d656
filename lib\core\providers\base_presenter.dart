import 'package:flutter/foundation.dart';
import '../utils/error_tracker.dart';
import '../utils/app_logger.dart';

/// BasePresenter موحد لجميع الـ Presenters في التطبيق
/// يوفر الوظائف الأساسية المشتركة ويقلل من تكرار الكود
abstract class BasePresenter extends ChangeNotifier {
  bool _isLoading = false;
  String? _errorMessage;
  bool _isDisposed = false;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// رسالة الخطأ (للتوافق مع الكود القديم)
  String? get error => _errorMessage;

  /// التحقق من حالة التخلص من الكائن
  bool get isDisposed => _isDisposed;

  /// تعيين حالة التحميل
  @protected
  void setLoading(bool loading) {
    if (_isDisposed) return;
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  @protected
  void setErrorMessage(String? message) {
    if (_isDisposed) return;
    _errorMessage = message;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  @protected
  void clearError() {
    if (_isDisposed) return;
    _errorMessage = null;
    notifyListeners();
  }

  /// تنفيذ عملية مع معالجة الأخطاء التلقائية
  @protected
  Future<T?> executeWithErrorHandling<T>(
    Future<T> Function() operation, {
    String? errorContext,
    bool showLoading = true,
  }) async {
    if (_isDisposed) return null;

    try {
      if (showLoading) setLoading(true);
      clearError();

      final result = await operation();
      return result;
    } catch (e, stackTrace) {
      final context = errorContext ?? runtimeType.toString();
      setErrorMessage('حدث خطأ في $context: $e');
      
      ErrorTracker.captureError(
        'خطأ في $context',
        error: e,
        stackTrace: stackTrace,
        context: {'presenter': runtimeType.toString()},
      );
      
      AppLogger.error('خطأ في $context: $e');
      return null;
    } finally {
      if (showLoading) setLoading(false);
    }
  }

  /// تنفيذ عملية بدون معالجة الأخطاء (للحالات الخاصة)
  @protected
  Future<T?> executeWithoutErrorHandling<T>(
    Future<T> Function() operation, {
    bool showLoading = true,
  }) async {
    if (_isDisposed) return null;

    try {
      if (showLoading) setLoading(true);
      return await operation();
    } finally {
      if (showLoading) setLoading(false);
    }
  }

  /// تهيئة الـ Presenter - يجب تنفيذها في كل presenter
  Future<void> init();

  /// تحديث البيانات - يجب تنفيذها في كل presenter
  Future<void> refresh();

  /// تنظيف الموارد
  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  /// إشعار المستمعين بأمان (يتحقق من حالة التخلص)
  @override
  void notifyListeners() {
    if (!_isDisposed) {
      super.notifyListeners();
    }
  }
}

/// BaseListPresenter للـ Presenters التي تتعامل مع قوائم البيانات
abstract class BaseListPresenter<T> extends BasePresenter {
  List<T> _items = [];
  String _searchQuery = '';
  
  /// قائمة العناصر
  List<T> get items => _getFilteredItems();
  
  /// جميع العناصر بدون تصفية
  List<T> get allItems => _items;
  
  /// استعلام البحث
  String get searchQuery => _searchQuery;
  
  /// عدد العناصر
  int get itemCount => items.length;
  
  /// هل القائمة فارغة؟
  bool get isEmpty => items.isEmpty;
  
  /// هل القائمة غير فارغة؟
  bool get isNotEmpty => items.isNotEmpty;

  /// تعيين قائمة العناصر
  @protected
  void setItems(List<T> newItems) {
    if (_isDisposed) return;
    _items = List.from(newItems);
    notifyListeners();
  }

  /// إضافة عنصر إلى القائمة
  @protected
  void addItem(T item) {
    if (_isDisposed) return;
    _items.add(item);
    notifyListeners();
  }

  /// إزالة عنصر من القائمة
  @protected
  void removeItem(T item) {
    if (_isDisposed) return;
    _items.remove(item);
    notifyListeners();
  }

  /// تحديث عنصر في القائمة
  @protected
  void updateItem(T oldItem, T newItem) {
    if (_isDisposed) return;
    final index = _items.indexOf(oldItem);
    if (index != -1) {
      _items[index] = newItem;
      notifyListeners();
    }
  }

  /// مسح جميع العناصر
  @protected
  void clearItems() {
    if (_isDisposed) return;
    _items.clear();
    notifyListeners();
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    if (_isDisposed) return;
    _searchQuery = query;
    notifyListeners();
  }

  /// مسح البحث
  void clearSearch() {
    setSearchQuery('');
  }

  /// تصفية العناصر حسب استعلام البحث
  @protected
  List<T> _getFilteredItems() {
    if (_searchQuery.isEmpty) {
      return _items;
    }
    return _items.where((item) => matchesSearch(item, _searchQuery)).toList();
  }

  /// فحص تطابق العنصر مع استعلام البحث - يجب تنفيذها في كل presenter
  @protected
  bool matchesSearch(T item, String query);

  /// تحميل العناصر - يجب تنفيذها في كل presenter
  @protected
  Future<List<T>> loadItemsFromSource();

  /// تحميل العناصر مع معالجة الأخطاء
  Future<void> loadItems() async {
    await executeWithErrorHandling<void>(
      () async {
        final items = await loadItemsFromSource();
        setItems(items);
      },
      errorContext: 'تحميل البيانات',
    );
  }

  @override
  Future<void> refresh() async {
    await loadItems();
  }
}

/// BaseFormPresenter للـ Presenters التي تتعامل مع النماذج
abstract class BaseFormPresenter<T> extends BasePresenter {
  T? _currentItem;
  bool _isEditing = false;
  
  /// العنصر الحالي
  T? get currentItem => _currentItem;
  
  /// هل في وضع التحرير؟
  bool get isEditing => _isEditing;
  
  /// هل في وضع الإنشاء؟
  bool get isCreating => !_isEditing;

  /// تعيين العنصر الحالي للتحرير
  void setCurrentItem(T? item) {
    if (_isDisposed) return;
    _currentItem = item;
    _isEditing = item != null;
    notifyListeners();
  }

  /// إنشاء عنصر جديد
  void createNew() {
    setCurrentItem(null);
  }

  /// حفظ العنصر - يجب تنفيذها في كل presenter
  @protected
  Future<bool> saveItem(T item);

  /// حذف العنصر - يجب تنفيذها في كل presenter
  @protected
  Future<bool> deleteItem(T item);

  /// حفظ العنصر مع معالجة الأخطاء
  Future<bool> save(T item) async {
    final result = await executeWithErrorHandling<bool>(
      () => saveItem(item),
      errorContext: _isEditing ? 'تحديث البيانات' : 'حفظ البيانات',
    );
    return result ?? false;
  }

  /// حذف العنصر مع معالجة الأخطاء
  Future<bool> delete(T item) async {
    final result = await executeWithErrorHandling<bool>(
      () => deleteItem(item),
      errorContext: 'حذف البيانات',
    );
    return result ?? false;
  }
}
