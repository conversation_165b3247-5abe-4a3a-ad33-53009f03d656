import 'package:flutter/material.dart';
import '../../../core/database/database_service.dart';
// import '../../../core/database/database_unifier.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/services/cache_manager.dart';

/// مقدم لوحة المعلومات
/// يستخدم لتحميل وإدارة بيانات لوحة المعلومات
class DashboardPresenter extends ChangeNotifier {
  final DatabaseService _db = DatabaseService.instance;
  final CacheManager _cacheManager = CacheManager();

  bool _isLoading = false;
  String? _error;

  // بيانات الملخص
  double _totalSales = 0;
  double _totalPurchases = 0;
  double _totalProfit = 0;
  int _invoiceCount = 0;

  // بيانات الرسوم البيانية
  List<double> _salesData = [];
  List<double> _purchasesData = [];
  List<double> _profitData = [];

  // بيانات المنتجات
  List<Map<String, dynamic>> _topProducts = [];
  List<Map<String, dynamic>> _lowStockProducts = [];

  // بيانات آخر العمليات
  List<Map<String, dynamic>> _recentTransactions = [];

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  double get totalSales => _totalSales;
  double get totalPurchases => _totalPurchases;
  double get totalProfit => _totalProfit;
  int get invoiceCount => _invoiceCount;
  List<double> get salesData => _salesData;
  List<double> get purchasesData => _purchasesData;
  List<double> get profitData => _profitData;
  List<Map<String, dynamic>> get topProducts => _topProducts;
  List<Map<String, dynamic>> get lowStockProducts => _lowStockProducts;
  List<Map<String, dynamic>> get recentTransactions => _recentTransactions;

  // متغير للتحكم في عدد محاولات إعادة التحميل
  int _retryCount = 0;
  static const int _maxRetries = 2;
  DateTime? _lastLoadAttempt;
  static const int _minLoadIntervalSeconds =
      5; // الحد الأدنى للوقت بين محاولات التحميل

  /// تحميل بيانات لوحة المعلومات
  Future<void> loadDashboardData({String period = 'month'}) async {
    // التحقق من الوقت المنقضي منذ آخر محاولة تحميل
    if (_lastLoadAttempt != null) {
      final timeSinceLastLoad = DateTime.now().difference(_lastLoadAttempt!);
      if (timeSinceLastLoad.inSeconds < _minLoadIntervalSeconds) {
        AppLogger.warning(
            'تم طلب تحميل البيانات بسرعة كبيرة. تجاهل الطلب. (${timeSinceLastLoad.inSeconds} ثوانٍ منذ آخر محاولة)');
        return;
      }
    }

    _lastLoadAttempt = DateTime.now();
    _setLoading(true);

    try {
      // محاولة الحصول على البيانات من التخزين المؤقت
      final cacheKey = 'dashboard_$period';
      final errorCacheKey = 'dashboard_error_$period';

      // التحقق من وجود خطأ مخزن مؤقتًا
      final cachedError = await _cacheManager.get<String>(errorCacheKey);
      if (cachedError != null && _retryCount >= _maxRetries) {
        // إذا كان هناك خطأ مخزن وتجاوزنا الحد الأقصى للمحاولات، نستخدم القيم الافتراضية
        AppLogger.warning(
            'تم تجاوز الحد الأقصى لمحاولات التحميل. استخدام القيم الافتراضية.');
        _setDefaultValues();
        _error = cachedError;
        _setLoading(false);
        return;
      }

      final cachedData =
          await _cacheManager.get<Map<String, dynamic>>(cacheKey);

      if (cachedData != null) {
        _loadFromCache(cachedData);
        _error = null; // مسح الخطأ إذا نجح التحميل من التخزين المؤقت
      } else {
        // التحقق من وجود الجداول المطلوبة
        final tablesExist = await _checkRequiredTables();

        if (tablesExist) {
          // تحميل البيانات من قاعدة البيانات
          await Future.wait([
            _loadSummaryData(period),
            _loadChartData(period),
            _loadTopProducts(period),
            _loadLowStockProducts(),
            _loadRecentTransactions(),
          ]);

          // تخزين البيانات في التخزين المؤقت
          await _cacheManager.set(
            cacheKey,
            {
              'totalSales': _totalSales,
              'totalPurchases': _totalPurchases,
              'totalProfit': _totalProfit,
              'invoiceCount': _invoiceCount,
              'salesData': _salesData,
              'purchasesData': _purchasesData,
              'profitData': _profitData,
              'topProducts': _topProducts,
              'lowStockProducts': _lowStockProducts,
              'recentTransactions': _recentTransactions,
            },
            expiryMinutes: 15, // صلاحية 15 دقيقة
          );

          // مسح خطأ التخزين المؤقت إذا نجح التحميل
          await _cacheManager.remove(errorCacheKey);
          _error = null;
          _retryCount = 0; // إعادة تعيين عداد المحاولات
        } else {
          // تعيين قيم افتراضية إذا لم تكن الجداول موجودة
          _setDefaultValues();
          _error = 'بعض الجداول المطلوبة غير موجودة في قاعدة البيانات';

          // تخزين الخطأ في التخزين المؤقت
          await _cacheManager.set(errorCacheKey, _error, expiryMinutes: 5);
          _retryCount++;
        }
      }
    } catch (e, stackTrace) {
      _error = 'حدث خطأ أثناء تحميل البيانات: $e';
      AppLogger.error('خطأ في تحميل بيانات لوحة المعلومات: $e');
      ErrorTracker.captureError(
        'خطأ في تحميل بيانات لوحة المعلومات',
        error: e,
        stackTrace: stackTrace,
        context: {'period': period, 'retryCount': _retryCount},
      );

      // تخزين الخطأ في التخزين المؤقت
      await _cacheManager.set('dashboard_error_$period', _error,
          expiryMinutes: 5);

      // تعيين قيم افتراضية في حالة حدوث خطأ
      _setDefaultValues();
      _retryCount++;
    } finally {
      _setLoading(false);
    }
  }

  /// التحقق من وجود الجداول المطلوبة
  Future<bool> _checkRequiredTables() async {
    try {
      final db = await _db.database;

      // التحقق من وجود جدول invoices
      final invoicesExists = await _tableExists(db, 'invoices');

      // التحقق من وجود جدول invoice_items
      final invoiceItemsExists = await _tableExists(db, 'invoice_items');

      // التحقق من وجود عمود min_stock في جدول products
      bool minStockExists = false;
      if (await _tableExists(db, 'products')) {
        final tableInfo = await db.rawQuery('PRAGMA table_info(products)');
        // التحقق من وجود عمود min_stock أو min_stock_quantity
        minStockExists = tableInfo.any((column) =>
            column['name'] == 'min_stock' ||
            column['name'] == 'min_stock_quantity');
      }

      // إذا كانت الجداول غير موجودة، نحاول إنشاءها
      if (!invoicesExists || !invoiceItemsExists || !minStockExists) {
        AppLogger.warning('بعض الجداول المطلوبة غير موجودة، محاولة إنشائها...');

        // استدعاء خدمة توحيد قاعدة البيانات
        // تم تعطيل هذا الكود لأن ملف database_unifier.dart غير موجود
        // final unifier = DatabaseUnifier();
        // await unifier.unifyDatabase();

        // التحقق مرة أخرى بعد محاولة الإنشاء
        final invoicesExistsNow = await _tableExists(db, 'invoices');
        final invoiceItemsExistsNow = await _tableExists(db, 'invoice_items');

        bool minStockExistsNow = false;
        if (await _tableExists(db, 'products')) {
          final tableInfoNow = await db.rawQuery('PRAGMA table_info(products)');
          minStockExistsNow = tableInfoNow.any((column) =>
              column['name'] == 'min_stock' ||
              column['name'] == 'min_stock_quantity');
        }

        return invoicesExistsNow && invoiceItemsExistsNow && minStockExistsNow;
      }

      return invoicesExists && invoiceItemsExists && minStockExists;
    } catch (e) {
      AppLogger.error('خطأ في التحقق من وجود الجداول: $e');
      return false;
    }
  }

  /// التحقق من وجود جدول
  Future<bool> _tableExists(dynamic db, String tableName) async {
    try {
      final result = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
          [tableName]);
      return result.isNotEmpty;
    } catch (e) {
      AppLogger.error('خطأ في التحقق من وجود جدول $tableName: $e');
      return false;
    }
  }

  /// تعيين قيم افتراضية
  void _setDefaultValues() {
    _totalSales = 0;
    _totalPurchases = 0;
    _totalProfit = 0;
    _invoiceCount = 0;
    _salesData = List<double>.filled(12, 0);
    _purchasesData = List<double>.filled(12, 0);
    _profitData = List<double>.filled(12, 0);
    _topProducts = [];
    _lowStockProducts = [];
    _recentTransactions = [];
  }

  /// تحميل البيانات من التخزين المؤقت
  void _loadFromCache(Map<String, dynamic> cachedData) {
    _totalSales = cachedData['totalSales'] ?? 0;
    _totalPurchases = cachedData['totalPurchases'] ?? 0;
    _totalProfit = cachedData['totalProfit'] ?? 0;
    _invoiceCount = cachedData['invoiceCount'] ?? 0;

    _salesData = List<double>.from(cachedData['salesData'] ?? []);
    _purchasesData = List<double>.from(cachedData['purchasesData'] ?? []);
    _profitData = List<double>.from(cachedData['profitData'] ?? []);

    _topProducts =
        List<Map<String, dynamic>>.from(cachedData['topProducts'] ?? []);
    _lowStockProducts =
        List<Map<String, dynamic>>.from(cachedData['lowStockProducts'] ?? []);
    _recentTransactions =
        List<Map<String, dynamic>>.from(cachedData['recentTransactions'] ?? []);
  }

  /// تحميل بيانات الملخص
  Future<void> _loadSummaryData(String period) async {
    // تنفيذ في ملف آخر
  }

  /// تحميل بيانات الرسوم البيانية
  Future<void> _loadChartData(String period) async {
    // تنفيذ في ملف آخر
  }

  /// تحميل بيانات أكثر المنتجات مبيعاً
  Future<void> _loadTopProducts(String period) async {
    // تنفيذ في ملف آخر
  }

  /// تحميل بيانات المنتجات منخفضة المخزون
  Future<void> _loadLowStockProducts() async {
    // تنفيذ في ملف آخر
  }

  /// تحميل بيانات آخر العمليات
  Future<void> _loadRecentTransactions() async {
    // تنفيذ في ملف آخر
  }

  /// تعيين حالة التحميل
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }
}
