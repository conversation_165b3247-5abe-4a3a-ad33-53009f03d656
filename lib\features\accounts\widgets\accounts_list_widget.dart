import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart' as intl;
import '../../../core/theme/index.dart';

/// مكون عرض قائمة الحسابات
class AccountsListWidget extends StatefulWidget {
  /// قائمة الحسابات للعرض
  final List<Map<String, dynamic>> accounts;

  /// معالج حدث التعديل
  final Function(Map<String, dynamic>) onEdit;

  /// معالج حدث التحديد
  final Function(Map<String, dynamic>) onSelect;

  const AccountsListWidget({
    Key? key,
    required this.accounts,
    required this.onEdit,
    required this.onSelect,
  }) : super(key: key);

  @override
  State<AccountsListWidget> createState() => _AccountsListWidgetState();
}

class _AccountsListWidgetState extends State<AccountsListWidget> {
  // حالة ترتيب الجدول
  String _sortColumn = 'code';
  bool _sortAscending = true;

  @override
  Widget build(BuildContext context) {
    // ترتيب الحسابات
    List<Map<String, dynamic>> sortedAccounts = List.from(widget.accounts);

    sortedAccounts.sort((a, b) {
      if (_sortColumn == 'code') {
        final aValue = a['code'] as String? ?? '';
        final bValue = b['code'] as String? ?? '';
        return _sortAscending
            ? aValue.compareTo(bValue)
            : bValue.compareTo(aValue);
      } else if (_sortColumn == 'name') {
        final aValue = a['name'] as String? ?? '';
        final bValue = b['name'] as String? ?? '';
        return _sortAscending
            ? aValue.compareTo(bValue)
            : bValue.compareTo(aValue);
      } else if (_sortColumn == 'balance') {
        final aValue = a['balance'] as double? ?? 0.0;
        final bValue = b['balance'] as double? ?? 0.0;
        return _sortAscending
            ? aValue.compareTo(bValue)
            : bValue.compareTo(aValue);
      } else {
        return 0;
      }
    });

    return widget.accounts.isEmpty
        ? const Center(child: Text('لا توجد حسابات للعرض'))
        : _buildAccountsDataTable(sortedAccounts);
  }

  Widget _buildAccountsDataTable(List<Map<String, dynamic>> accounts) {
    // استخدام ScrollController مشترك للتمرير الأفقي المتزامن
    final ScrollController horizontalScrollController = ScrollController();
    final ScrollController verticalScrollController = ScrollController();

    // عرض العمود الثابت (الرقم التسلسلي)
    const double fixedColumnWidth = 60.0;

    return Column(
      children: [
        // رأس الجدول بالكامل (ثابت عند التمرير للأسفل)
        Row(
          children: [
            // العمود الأول الثابت (الرقم التسلسلي) - رأس الجدول
            Container(
              width: fixedColumnWidth,
              height: 50,
              color: AppColors.errorLight,
              alignment: Alignment.center,
              child: const Text(
                '#',
                style: AppTypography(
                  fontWeight: FontWeight.bold,
                  color: AppColors.error,
                ),
              ),
            ),

            // باقي أعمدة رأس الجدول (قابلة للتمرير أفقيًا)
            Expanded(
              child: Container(
                color: AppColors.errorLight,
                child: SingleChildScrollView(
                  controller: horizontalScrollController,
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      // باقي أعمدة رأس الجدول
                      ...List.generate(
                        _buildTableColumns().length -
                            1, // كل الأعمدة ما عدا العمود الأول
                        (index) {
                          final column = _buildTableColumns()[
                              index + 1]; // +1 لتخطي العمود الأول
                          return Container(
                            width: 150, // عرض ثابت لكل عمود
                            height: 50,
                            alignment: Alignment.center,
                            child: column.label,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),

        // محتوى الجدول (قابل للتمرير رأسيًا وأفقيًا)
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العمود الأول الثابت (الرقم التسلسلي) - محتوى الجدول
              SingleChildScrollView(
                controller: verticalScrollController,
                child: Column(
                  children: List.generate(
                    accounts.length,
                    (index) {
                      final bool isAlternateRow = index % 2 == 1;
                      final Color rowColor = isAlternateRow
                          ? AppColors.indigoLight.withValues(alpha: 0.3)
                          : AppColors.onPrimary;

                      return Container(
                        width: fixedColumnWidth,
                        height: 60, // نفس ارتفاع الصف في الجدول الرئيسي
                        color: rowColor,
                        alignment: Alignment.center,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: AppColors.lightSurfaceVariant,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            (index + 1).toString(),
                            style: const AppTypography(
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),

              // باقي أعمدة محتوى الجدول (قابلة للتمرير أفقيًا ورأسيًا)
              Expanded(
                child: NotificationListener<ScrollNotification>(
                  onNotification: (ScrollNotification notification) {
                    // مزامنة التمرير الأفقي مع رأس الجدول
                    if (notification is ScrollUpdateNotification &&
                        notification.depth == 0 &&
                        notification.metrics.axis == Axis.horizontal) {
                      horizontalScrollController
                          .jumpTo(notification.metrics.pixels);
                    }

                    // مزامنة التمرير الرأسي مع العمود الثابت
                    if (notification is ScrollUpdateNotification &&
                        notification.depth == 1 &&
                        notification.metrics.axis == Axis.vertical) {
                      verticalScrollController
                          .jumpTo(notification.metrics.pixels);
                    }
                    return false;
                  },
                  child: SingleChildScrollView(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: DataTable(
                        columnSpacing: 20,
                        headingRowHeight: 0, // لا نريد عرض رأس الجدول هنا
                        dataRowMinHeight: 60,
                        dataRowMaxHeight: 60,
                        columns: List.generate(
                          _buildTableColumns().length -
                              1, // كل الأعمدة ما عدا العمود الأول
                          (index) => DataColumn(label: Container()),
                        ),
                        rows: accounts.map((account) {
                          final int index = accounts.indexOf(account);
                          final bool isAlternateRow = index % 2 == 1;
                          final Color rowColor = isAlternateRow
                              ? AppColors.indigoLight.withValues(alpha: 0.3)
                              : AppColors.onPrimary;

                          return DataRow(
                            color: WidgetStateProperty.all<Color>(rowColor),
                            cells: [
                              // 2. اسم الحساب
                              DataCell(
                                Text(
                                  account['name'] as String? ?? '',
                                  style: const AppTypography(
                                      fontWeight: FontWeight.bold),
                                ),
                              ),

                              // 3. الحساب الأب
                              DataCell(
                                account['parent_name'] != null
                                    ? Row(
                                        children: [
                                          const Icon(
                                              Icons.subdirectory_arrow_right,
                                              size: 14,
                                              color:
                                                  AppColors.lightTextSecondary),
                                          const SizedBox(width: 4),
                                          Expanded(
                                            child: Text(
                                              account['parent_name'] as String,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          if (account['parent_code'] !=
                                              null) ...[
                                            const SizedBox(width: 4),
                                            Text(
                                              '#${account['parent_code']}',
                                              style: const AppTypography(
                                                fontSize: 10,
                                                color: AppColors
                                                    .lightTextSecondary,
                                              ),
                                            ),
                                          ],
                                        ],
                                      )
                                    : const Text('-'),
                              ),

                              // 4. الكود
                              DataCell(
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: AppColors.infoLight,
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(color: AppColors.info),
                                  ),
                                  child: Text(
                                    account['code'] as String? ?? '',
                                    style: const AppTypography(
                                        color: AppColors.infoDark),
                                  ),
                                ),
                              ),

                              // 5. النوع
                              DataCell(
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: AppColors.lightSurfaceVariant,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        _getAccountTypeIcon(
                                            account['account_type']
                                                    as String? ??
                                                ''),
                                        size: 16,
                                        color: _getAccountTypeColor(
                                            account['account_type']
                                                    as String? ??
                                                ''),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        _getAccountTypeDisplayName(
                                            account['account_type']
                                                    as String? ??
                                                ''),
                                        style: AppTypography(
                                          color: _getAccountTypeColor(
                                              account['account_type']
                                                      as String? ??
                                                  ''),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              // 6. الرصيد
                              DataCell(
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color:
                                        (account['balance'] as num? ?? 0) >= 0
                                            ? AppColors.successLight
                                            : AppColors.errorLight,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    _formatBalance(account['balance']),
                                    style: AppTypography(
                                      fontWeight: FontWeight.bold,
                                      color:
                                          (account['balance'] as num? ?? 0) >= 0
                                              ? AppColors.successDark
                                              : AppColors.errorDark,
                                    ),
                                  ),
                                ),
                              ),

                              // 7. الهاتف
                              DataCell(
                                account['phone'] != null &&
                                        (account['phone'] as String).isNotEmpty
                                    ? InkWell(
                                        onTap: () => _makePhoneCall(
                                            account['phone'] as String),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            const Icon(Icons.phone,
                                                size: 16,
                                                color: AppColors.info),
                                            const SizedBox(width: 4),
                                            Text(
                                              account['phone'] as String,
                                              style: const AppTypography(
                                                color: AppColors.info,
                                                decoration:
                                                    TextDecoration.underline,
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    : const Text('-'),
                              ),

                              // 8. الملاحظات
                              DataCell(
                                account['notes'] != null &&
                                        (account['notes'] as String).isNotEmpty
                                    ? Tooltip(
                                        message: account['notes'] as String,
                                        child: Text(
                                          account['notes'] as String,
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 2,
                                        ),
                                      )
                                    : const Text('-'),
                              ),

                              // 9. الحالة
                              DataCell(
                                account['is_active'] == 1
                                    ? const Chip(
                                        label: Text('نشط'),
                                        backgroundColor: AppColors.success,
                                        labelStyle: AppTypography(
                                            color: AppColors.onPrimary),
                                        padding:
                                            EdgeInsets.symmetric(horizontal: 4),
                                        materialTapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                      )
                                    : const Chip(
                                        label: Text('غير نشط'),
                                        backgroundColor: AppColors.error,
                                        labelStyle: AppTypography(
                                            color: AppColors.onPrimary),
                                        padding:
                                            EdgeInsets.symmetric(horizontal: 4),
                                        materialTapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                      ),
                              ),

                              // 10. الإجراءات
                              DataCell(
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // تعديل
                                    IconButton(
                                      icon: const Icon(Icons.edit,
                                          color: AppColors.info),
                                      onPressed: () => widget.onEdit(account),
                                      tooltip: 'تعديل',
                                      constraints: const BoxConstraints(),
                                      padding: const EdgeInsets.all(4),
                                    ),

                                    // حذف
                                    IconButton(
                                      icon: const Icon(Icons.delete,
                                          color: AppColors.error),
                                      onPressed: () =>
                                          _showDeleteConfirmation(account),
                                      tooltip: 'حذف',
                                      constraints: const BoxConstraints(),
                                      padding: const EdgeInsets.all(4),
                                    ),

                                    // طباعة
                                    IconButton(
                                      icon: const Icon(Icons.print,
                                          color: AppColors.accent),
                                      onPressed: () => _printAccount(account),
                                      tooltip: 'طباعة',
                                      constraints: const BoxConstraints(),
                                      padding: const EdgeInsets.all(4),
                                    ),

                                    // اتصال (إذا كان هناك رقم هاتف)
                                    if (account['phone'] != null &&
                                        (account['phone'] as String).isNotEmpty)
                                      IconButton(
                                        icon: const Icon(Icons.phone,
                                            color: AppColors.success),
                                        onPressed: () => _makePhoneCall(
                                            account['phone'] as String),
                                        tooltip: 'اتصال',
                                        constraints: const BoxConstraints(),
                                        padding: const EdgeInsets.all(4),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // تم إزالة دالة _getSortColumnIndex لأنها لم تعد مستخدمة

  List<DataColumn> _buildTableColumns() {
    return [
      // 1. الرقم التسلسلي
      const DataColumn(
        label: Text('#', style: AppTypography(fontWeight: FontWeight.bold)),
      ),
      // 2. اسم الحساب
      DataColumn(
        label: const Text('اسم الحساب',
            style: AppTypography(fontWeight: FontWeight.bold)),
        onSort: (columnIndex, ascending) {
          setState(() {
            _sortColumn = 'name';
            _sortAscending = ascending;
          });
        },
      ),
      // 3. الحساب الأب
      const DataColumn(
        label: Text('الحساب الأب',
            style: AppTypography(fontWeight: FontWeight.bold)),
      ),
      // 4. الكود
      DataColumn(
        label: const Text('الكود',
            style: AppTypography(fontWeight: FontWeight.bold)),
        onSort: (columnIndex, ascending) {
          setState(() {
            _sortColumn = 'code';
            _sortAscending = ascending;
          });
        },
      ),
      // 5. النوع
      const DataColumn(
        label: Text('النوع', style: AppTypography(fontWeight: FontWeight.bold)),
      ),
      // 6. الرصيد
      DataColumn(
        label: const Text('الرصيد',
            style: AppTypography(fontWeight: FontWeight.bold)),
        numeric: true,
        onSort: (columnIndex, ascending) {
          setState(() {
            _sortColumn = 'balance';
            _sortAscending = ascending;
          });
        },
      ),
      // 7. الهاتف
      const DataColumn(
        label:
            Text('الهاتف', style: AppTypography(fontWeight: FontWeight.bold)),
      ),
      // 8. الملاحظات
      const DataColumn(
        label: Text('الملاحظات',
            style: AppTypography(fontWeight: FontWeight.bold)),
      ),
      // 9. الحالة
      const DataColumn(
        label:
            Text('الحالة', style: AppTypography(fontWeight: FontWeight.bold)),
      ),
      // 10. الإجراءات
      const DataColumn(
        label: Text('الإجراءات',
            style: AppTypography(fontWeight: FontWeight.bold)),
      ),
    ];
  }

  // دالة لفتح مكالمة هاتفية
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      throw 'لا يمكن الاتصال بالرقم $phoneNumber';
    }
  }

  // تم إزالة دالة _buildTableRow لأنها لم تعد مستخدمة

  // عرض تأكيد الحذف
  void _showDeleteConfirmation(Map<String, dynamic> account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الحساب "${account['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // هنا يمكن إضافة منطق الحذف الفعلي
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text('تم حذف الحساب "${account['name']}" بنجاح')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  // طباعة بيانات الحساب
  void _printAccount(Map<String, dynamic> account) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('جاري طباعة بيانات الحساب "${account['name']}"')),
    );
    // هنا يمكن إضافة منطق الطباعة الفعلي
  }

  String _getAccountTypeDisplayName(String type) {
    switch (type) {
      case 'asset':
        return 'أصول';
      case 'liability':
        return 'التزامات';
      case 'equity':
        return 'حقوق ملكية';
      case 'revenue':
        return 'إيرادات';
      case 'expense':
        return 'مصروفات';
      case 'customer':
        return 'عميل';
      case 'supplier':
        return 'مورد';
      case 'cash':
        return 'نقدية';
      default:
        return type;
    }
  }

  IconData _getAccountTypeIcon(String type) {
    switch (type) {
      case 'asset':
        return Icons.account_balance;
      case 'liability':
        return Icons.money_off;
      case 'equity':
        return Icons.pie_chart;
      case 'revenue':
        return Icons.trending_up;
      case 'expense':
        return Icons.trending_down;
      case 'customer':
        return Icons.person;
      case 'supplier':
        return Icons.business;
      case 'cash':
        return Icons.payments;
      default:
        return Icons.account_balance_wallet;
    }
  }

  Color _getAccountTypeColor(String type) {
    return AppColors.getAccountTypeColor(type);
  }

  String _formatBalance(dynamic balance) {
    if (balance == null) return '0.00';

    final value =
        balance is int ? balance.toDouble() : balance as double? ?? 0.0;

    // استخدام تنسيق العملة
    final formatter = intl.NumberFormat.currency(
      symbol: '', // بدون رمز العملة
      decimalDigits: 2,
    );

    return formatter.format(value);
  }
}
