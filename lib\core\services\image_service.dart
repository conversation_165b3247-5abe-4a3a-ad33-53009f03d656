import 'dart:io';
import 'dart:typed_data';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import '../utils/error_tracker.dart';
import '../utils/app_logger.dart';
import '../database/database_helper.dart';
import '../providers/app_providers.dart';
import '../../features/settings/presenters/settings_presenter.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';

/// خدمة لإدارة عمليات الصور
class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  final ImagePicker _picker = ImagePicker();
  final Uuid _uuid = const Uuid();

  /// التقاط صورة من الكاميرا
  Future<File?> captureImage({
    double? maxWidth = 800,
    double? maxHeight = 800,
    int quality = 85,
  }) async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        imageQuality: quality,
      );

      if (pickedFile == null) return null;
      return File(pickedFile.path);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error al capturar imagen',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// اختيار صورة من المعرض
  Future<File?> pickImage({
    double? maxWidth = 800,
    double? maxHeight = 800,
    int quality = 85,
  }) async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        imageQuality: quality,
      );

      if (pickedFile == null) return null;
      return File(pickedFile.path);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error al seleccionar imagen',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// ضغط الصورة وتحويلها إلى تنسيق WebP مع تحسين متقدم
  Future<File?> compressAndConvertToWebP(
    File imageFile, {
    int quality = 85,
    int minWidth = 800,
    int minHeight = 800,
  }) async {
    try {
      // الحصول على الدليل المؤقت
      final tempDir = await getTemporaryDirectory();
      final targetPath = path.join(
        tempDir.path,
        '${_uuid.v4()}.webp',
      );

      // الحصول على معلومات الصورة الأصلية
      final originalFileSize = await imageFile.length();
      AppLogger.info('حجم الصورة الأصلي: ${_formatFileSize(originalFileSize)}');

      // ضغط وتحويل إلى WebP
      final result = await FlutterImageCompress.compressAndGetFile(
        imageFile.path,
        targetPath,
        quality: quality,
        minWidth: minWidth,
        minHeight: minHeight,
        format: CompressFormat.webp,
      );

      if (result == null) {
        throw Exception('خطأ في ضغط الصورة');
      }

      // التحقق من الحجم بعد الضغط
      final compressedFile = File(result.path);
      final compressedFileSize = await compressedFile.length();
      final compressionRatio = originalFileSize / compressedFileSize;

      AppLogger.info('الحجم بعد الضغط: ${_formatFileSize(compressedFileSize)}');
      AppLogger.info('نسبة الضغط: ${compressionRatio.toStringAsFixed(2)}x');

      // Si la compresión no fue efectiva, intentar con calidad más baja
      if (compressionRatio < 1.5 && quality > 60) {
        AppLogger.info(
            'La compresión no fue suficiente, intentando con calidad más baja');

        // Eliminar el archivo comprimido anterior
        await compressedFile.delete();

        // Intentar con calidad más baja
        return compressAndConvertToWebP(
          imageFile,
          quality: quality - 15,
          minWidth: minWidth,
          minHeight: minHeight,
        );
      }

      return compressedFile;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error al comprimir imagen',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Formatear tamaño de archivo para mostrar en logs
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(2)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
  }

  /// حفظ الصورة حسب إعدادات المستخدم (محلي، قاعدة بيانات، أو الاثنين)
  Future<String?> saveImage(
    File imageFile,
    String entityId,
    String tableName,
    String columnName, {
    String folderPath = 'images',
    bool compressBeforeUpload = true,
    int quality = 85,
    int maxWidth = 800,
    int maxHeight = 800,
  }) async {
    try {
      // الحصول على إعداد حفظ الصور
      final settings = AppProviders.getLazyPresenter<SettingsPresenter>(
        () => SettingsPresenter(),
      );
      final storageMode = settings.getImageStorageMode();

      AppLogger.info('🖼️ بدء حفظ الصورة بطريقة: $storageMode');

      switch (storageMode) {
        case 'local_only':
          return await _saveImageLocally(
            imageFile,
            folderPath,
            compressBeforeUpload: compressBeforeUpload,
            quality: quality,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
          );

        case 'database_only':
          return await _saveImageToDatabase(
            imageFile,
            entityId,
            tableName,
            columnName,
            compressBeforeUpload: compressBeforeUpload,
            quality: quality,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
          );

        case 'both':
          return await _saveImageBoth(
            imageFile,
            entityId,
            tableName,
            columnName,
            folderPath,
            compressBeforeUpload: compressBeforeUpload,
            quality: quality,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
          );

        default:
          return await _saveImageLocally(
            imageFile,
            folderPath,
            compressBeforeUpload: compressBeforeUpload,
            quality: quality,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
          );
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حفظ الصورة',
        error: e,
        stackTrace: stackTrace,
        context: {
          'entityId': entityId,
          'tableName': tableName,
          'columnName': columnName,
          'imagePath': imageFile.path
        },
      );
      return null;
    }
  }

  /// حفظ الصورة محليًا مع تحسين متقدم
  Future<String?> _saveImageLocally(
    File imageFile,
    String folderPath, {
    bool compressBeforeUpload = true,
    int quality = 85,
    int maxWidth = 1200,
    int maxHeight = 1200,
  }) async {
    try {
      AppLogger.info('بدء عملية حفظ الصورة محليًا');
      File fileToUpload = imageFile;

      // ضغط الصورة قبل الحفظ إذا لزم الأمر
      if (compressBeforeUpload) {
        AppLogger.info('جاري ضغط الصورة قبل الحفظ...');

        // التحقق من نوع الملف
        final extension = path.extension(imageFile.path).toLowerCase();
        AppLogger.info('نوع الملف الأصلي: $extension');

        // ضغط وتحويل إلى WebP للتحسين
        final compressedFile = await compressAndConvertToWebP(
          imageFile,
          quality: quality,
          minWidth: maxWidth,
          minHeight: maxHeight,
        );

        if (compressedFile != null) {
          fileToUpload = compressedFile;
          AppLogger.info('تم ضغط الصورة بنجاح');
        } else {
          AppLogger.warning('تعذر ضغط الصورة، سيتم استخدام الصورة الأصلية');
        }
      }

      // إنشاء اسم فريد للصورة
      final fileName = '${_uuid.v4()}.webp';

      // الحصول على مسار مجلد التطبيق
      final appDir = await getApplicationDocumentsDirectory();
      final targetDir = Directory('${appDir.path}/$folderPath');

      // إنشاء المجلد إذا لم يكن موجودًا
      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      final targetPath = '${targetDir.path}/$fileName';
      AppLogger.info('مسار الحفظ: $targetPath');

      // نسخ الملف إلى المسار المستهدف
      await fileToUpload.copy(targetPath);
      AppLogger.info('تم حفظ الصورة بنجاح');

      return targetPath;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في حفظ الصورة محليًا',
        error: e,
        stackTrace: stackTrace,
        context: {
          'folderPath': folderPath,
          'fileName': path.basename(imageFile.path),
          'fileSize': _formatFileSize(await imageFile.length()),
        },
      );
      return null;
    }
  }

  /// حذف صورة محلية
  Future<bool> deleteLocalImage(String imagePath) async {
    try {
      if (imagePath.isEmpty) return false;

      // التحقق من وجود الملف
      final file = File(imagePath);
      if (await file.exists()) {
        // حذف الملف
        await file.delete();
        AppLogger.info('تم حذف الصورة بنجاح: $imagePath');
        return true;
      } else {
        AppLogger.warning('الصورة غير موجودة: $imagePath');
        return false;
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في حذف الصورة المحلية',
        error: e,
        stackTrace: stackTrace,
        context: {'imagePath': imagePath},
      );
      return false;
    }
  }

  /// Redimensionar imagen manteniendo la proporción
  Future<Uint8List?> resizeImage(
    File imageFile, {
    required int maxWidth,
    required int maxHeight,
    int quality = 85,
  }) async {
    try {
      return await FlutterImageCompress.compressWithFile(
        imageFile.path,
        minWidth: maxWidth,
        minHeight: maxHeight,
        quality: quality,
      );
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error al redimensionar imagen',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// حفظ الصورة في قاعدة البيانات فقط
  Future<String?> _saveImageToDatabase(
    File imageFile,
    String entityId,
    String tableName,
    String columnName, {
    required bool compressBeforeUpload,
    required int quality,
    required int maxWidth,
    required int maxHeight,
  }) async {
    try {
      AppLogger.info('🗄️ حفظ الصورة في قاعدة البيانات للكيان: $entityId');

      // ضغط الصورة إذا لزم الأمر
      File fileToProcess = imageFile;
      if (compressBeforeUpload) {
        final compressedFile = await compressAndConvertToWebP(
          imageFile,
          quality: quality,
          minWidth: maxWidth,
          minHeight: maxHeight,
        );
        if (compressedFile != null) {
          fileToProcess = compressedFile;
        }
      }

      // قراءة بيانات الصورة
      final Uint8List imageBytes = await fileToProcess.readAsBytes();

      // حفظ في قاعدة البيانات
      final db = await DatabaseHelper().database;
      await db.update(
        tableName,
        {
          '${columnName}_blob': imageBytes,
          columnName:
              'database_blob', // علامة تدل على أن الصورة في قاعدة البيانات
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [entityId],
      );

      AppLogger.info('✅ تم حفظ الصورة في قاعدة البيانات');
      return 'database_blob';
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حفظ الصورة في قاعدة البيانات',
        error: e,
        stackTrace: stackTrace,
        context: {'entityId': entityId, 'tableName': tableName},
      );
      return null;
    }
  }

  /// حفظ الصورة في الاثنين معاً
  Future<String?> _saveImageBoth(
    File imageFile,
    String entityId,
    String tableName,
    String columnName,
    String folderPath, {
    required bool compressBeforeUpload,
    required int quality,
    required int maxWidth,
    required int maxHeight,
  }) async {
    try {
      AppLogger.info('🔄 حفظ الصورة في الاثنين معاً للكيان: $entityId');

      // حفظ محلياً
      final localPath = await _saveImageLocally(
        imageFile,
        folderPath,
        compressBeforeUpload: compressBeforeUpload,
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (localPath == null) {
        throw Exception('فشل في حفظ الصورة محلياً');
      }

      // حفظ في قاعدة البيانات أيضاً
      await _saveImageToDatabase(
        imageFile,
        entityId,
        tableName,
        columnName,
        compressBeforeUpload: compressBeforeUpload,
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      AppLogger.info('✅ تم حفظ الصورة في الاثنين معاً');
      return localPath; // نعيد المسار المحلي كمرجع أساسي
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حفظ الصورة في الاثنين معاً',
        error: e,
        stackTrace: stackTrace,
        context: {'entityId': entityId, 'tableName': tableName},
      );
      return null;
    }
  }

  /// استرجاع الصورة حسب نوع الحفظ
  Future<dynamic> getImage(
    String entityId,
    String tableName,
    String columnName,
    String? imagePath,
  ) async {
    try {
      if (imagePath == null || imagePath.isEmpty) {
        return null;
      }

      // إذا كانت الصورة في قاعدة البيانات
      if (imagePath == 'database_blob') {
        return await _getImageFromDatabase(entityId, tableName, columnName);
      }

      // إذا كانت الصورة محلية
      if (await File(imagePath).exists()) {
        return File(imagePath);
      }

      // محاولة استرجاع من قاعدة البيانات كبديل
      return await _getImageFromDatabase(entityId, tableName, columnName);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في استرجاع الصورة',
        error: e,
        stackTrace: stackTrace,
        context: {
          'entityId': entityId,
          'tableName': tableName,
          'columnName': columnName,
          'imagePath': imagePath
        },
      );
      return null;
    }
  }

  /// استرجاع الصورة من قاعدة البيانات
  Future<Uint8List?> _getImageFromDatabase(
    String entityId,
    String tableName,
    String columnName,
  ) async {
    try {
      final db = await DatabaseHelper().database;
      final result = await db.query(
        tableName,
        columns: ['${columnName}_blob'],
        where: 'id = ?',
        whereArgs: [entityId],
        limit: 1,
      );

      if (result.isNotEmpty && result.first['${columnName}_blob'] != null) {
        return result.first['${columnName}_blob'] as Uint8List;
      }

      return null;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في استرجاع الصورة من قاعدة البيانات',
        error: e,
        stackTrace: stackTrace,
        context: {
          'entityId': entityId,
          'tableName': tableName,
          'columnName': columnName
        },
      );
      return null;
    }
  }

  /// دالة مساعدة للتوافق مع النظام القديم - حفظ الصورة محلياً
  Future<String?> saveImageLocally(
    File imageFile,
    String folderPath, {
    bool compressBeforeUpload = true,
    int quality = 85,
    int maxWidth = 1200,
    int maxHeight = 1200,
  }) async {
    return await _saveImageLocally(
      imageFile,
      folderPath,
      compressBeforeUpload: compressBeforeUpload,
      quality: quality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );
  }
}
