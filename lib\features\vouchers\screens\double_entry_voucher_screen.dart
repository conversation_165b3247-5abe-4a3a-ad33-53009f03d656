import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/utils/index.dart';

import '../../../features/accounts/models/voucher.dart';
import '../../accounts/presenters/account_presenter.dart';

import '../presenters/voucher_presenter.dart';
import '../../currencies/presenters/currency_presenter.dart';

import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';

class DoubleEntryVoucherScreen extends StatefulWidget {
  final Voucher? voucher;

  const DoubleEntryVoucherScreen({Key? key, this.voucher}) : super(key: key);

  @override
  State<DoubleEntryVoucherScreen> createState() =>
      _DoubleEntryVoucherScreenState();
}

class _DoubleEntryVoucherScreenState extends State<DoubleEntryVoucherScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _handlerController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  String? _selectedDebitAccountId;
  String? _selectedCreditAccountId;
  String? _selectedCurrencyId;
  double _exchangeRate = 1.0;
  bool _isLoading = false;
  bool _isSaving = false;
  List<Voucher> _filteredVouchers = [];
  bool _showSearchField = false;

  // استخدام التحميل الكسول
  late final AccountPresenter _accountPresenter;
  late final CurrencyPresenter _currencyPresenter;
  late final VoucherPresenter _voucherPresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _accountPresenter = AppProviders.getLazyPresenter<AccountPresenter>(
        () => AccountPresenter());
    _currencyPresenter = AppProviders.getLazyPresenter<CurrencyPresenter>(
        () => CurrencyPresenter());
    _voucherPresenter = AppProviders.getLazyPresenter<VoucherPresenter>(
        () => VoucherPresenter());
    _loadData();

    // إذا كان هناك سند للتعديل، قم بتعبئة النموذج
    if (widget.voucher != null) {
      _amountController.text = widget.voucher!.amount.toString();
      _handlerController.text = widget.voucher!.handler ?? '';
      _notesController.text = widget.voucher!.notes ?? '';
      _selectedDate = widget.voucher!.voucherDate;
      _selectedDebitAccountId = widget.voucher!.accountId;
      _selectedCurrencyId = widget.voucher!.currency;
      _exchangeRate = widget.voucher!.exchangeRate;
    }

    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    _filterVouchers();
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الحسابات
      await _accountPresenter.loadAccounts();

      if (!mounted) return;

      // تحميل العملات
      if (!mounted) return;
      await _currencyPresenter.loadCurrencies();

      if (!mounted) return;

      // تعيين العملة الافتراضية إذا لم يتم تحديد عملة
      if (_selectedCurrencyId == null &&
          _currencyPresenter.defaultCurrency != null) {
        _selectedCurrencyId = _currencyPresenter.defaultCurrency!.id;
      }

      // تحميل السندات
      if (!mounted) return;
      await _voucherPresenter.loadVouchers(voucherType: 'double_entry');

      if (!mounted) return;

      _filteredVouchers = _voucherPresenter.vouchers
          .where((v) => v.voucherType == 'double_entry')
          .toList();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل البيانات: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _filterVouchers() {
    final query = _searchController.text.toLowerCase();

    if (query.isEmpty) {
      setState(() {
        _filteredVouchers = _voucherPresenter.vouchers
            .where((v) => v.voucherType == 'double_entry')
            .toList();
      });
      return;
    }

    setState(() {
      _filteredVouchers = _voucherPresenter.vouchers.where((voucher) {
        return voucher.voucherType == 'double_entry' &&
            (voucher.referenceNumber?.toLowerCase().contains(query) == true ||
                voucher.accountName?.toLowerCase().contains(query) == true ||
                voucher.handler?.toLowerCase().contains(query) == true ||
                voucher.notes?.toLowerCase().contains(query) == true);
      }).toList();
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _updateExchangeRate(String? currencyId) {
    if (currencyId == null) return;

    final currency =
        _currencyPresenter.currencies.firstWhere((c) => c.id == currencyId);

    setState(() {
      _exchangeRate = currency.exchangeRate;
    });
  }

  Future<void> _saveVoucher() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من اختيار الحسابات
    if (_selectedDebitAccountId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار حساب المدين')),
      );
      return;
    }

    if (_selectedCreditAccountId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار حساب الدائن')),
      );
      return;
    }

    // التحقق من اختيار العملة
    if (_selectedCurrencyId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار العملة')),
      );
      return;
    }

    // عرض مربع حوار التأكيد
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحفظ'),
        content: const Text('هل تريد حفظ القيد المزدوج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (confirm != true) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      if (!mounted) return;
      final amount = double.parse(_amountController.text);

      // إنشاء سند للحساب المدين
      final debitVoucher = Voucher(
        id: widget.voucher?.id,
        voucherNumber: widget.voucher?.voucherNumber ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        voucherType: 'double_entry',
        voucherDate: _selectedDate,
        referenceNumber: widget.voucher?.referenceNumber,
        accountId: _selectedDebitAccountId!,
        amount: amount,
        currency: _selectedCurrencyId!,
        exchangeRate: _exchangeRate,
        handler:
            _handlerController.text.isEmpty ? null : _handlerController.text,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        createdAt: widget.voucher?.createdAt,
      );

      // إنشاء سند للحساب الدائن
      final creditVoucher = Voucher(
        voucherNumber: "${DateTime.now().millisecondsSinceEpoch}1",
        voucherType: 'double_entry',
        voucherDate: _selectedDate,
        accountId: _selectedCreditAccountId!,
        amount: -amount, // قيمة سالبة للحساب الدائن
        currency: _selectedCurrencyId!,
        exchangeRate: _exchangeRate,
        handler:
            _handlerController.text.isEmpty ? null : _handlerController.text,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
      );

      bool success;
      if (widget.voucher == null) {
        // إضافة سند جديد
        success = await _voucherPresenter.addVoucher(debitVoucher);
        if (success) {
          success = await _voucherPresenter.addVoucher(creditVoucher);
        }
      } else {
        // تعديل سند موجود
        success = await _voucherPresenter.updateVoucher(debitVoucher);
      }

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حفظ القيد المزدوج بنجاح')),
          );
        }

        // إعادة تحميل السندات
        await _voucherPresenter.loadVouchers(voucherType: 'double_entry');
        _filteredVouchers = _voucherPresenter.vouchers
            .where((v) => v.voucherType == 'double_entry')
            .toList();

        // إعادة تعيين النموذج إذا كان إضافة جديدة
        if (widget.voucher == null) {
          _resetForm();
        } else if (mounted) {
          Navigator.pop(context);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('فشل في حفظ القيد المزدوج')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حفظ القيد المزدوج: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _resetForm() {
    _amountController.clear();
    _handlerController.clear();
    _notesController.clear();
    setState(() {
      _selectedDate = DateTime.now();
      _selectedDebitAccountId = null;
      _selectedCreditAccountId = null;

      // إعادة تعيين العملة إلى العملة الافتراضية
      if (_currencyPresenter.defaultCurrency != null) {
        _selectedCurrencyId = _currencyPresenter.defaultCurrency!.id;
        _exchangeRate = _currencyPresenter.defaultCurrency!.exchangeRate;
      }
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _handlerController.dispose();
    _notesController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.voucher != null;

    return Scaffold(
      appBar: AkAppBar(
        title: isEditing ? 'تعديل قيد مزدوج' : 'قيد مزدوج',
        showBackButton: true,
        actions: [
          // أيقونة البحث
          IconButton(
            icon: Icon(_showSearchField ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                  _filterVouchers();
                }
              });
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث)
                if (_showSearchField)
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: AkSearchInput(
                      controller: _searchController,
                      hint: 'بحث عن قيد مزدوج...',
                      onChanged: (value) {
                        _filterVouchers();
                      },
                    ),
                  ),
                // نموذج إضافة قيد مزدوج
                Expanded(
                  flex: 1,
                  child: SingleChildScrollView(
                    padding: EdgeInsets.zero,
                    child: AkCard(
                      padding: EdgeInsets.zero,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4.0, vertical: 4.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // المبلغ والعملة
                              Row(
                                children: [
                                  // المبلغ
                                  Expanded(
                                    flex: 2,
                                    child: AkTextInput(
                                      controller: _amountController,
                                      label: 'المبلغ',
                                      hint: 'أدخل المبلغ',
                                      isRequired: true,
                                      keyboardType:
                                          const TextInputType.numberWithOptions(
                                              decimal: true),
                                      prefixIcon: Icons.attach_money,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'يرجى إدخال المبلغ';
                                        }
                                        if (double.tryParse(value) == null) {
                                          return 'يرجى إدخال رقم صحيح';
                                        }
                                        if (double.parse(value) <= 0) {
                                          return 'يجب أن يكون المبلغ أكبر من صفر';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  // العملة
                                  Expanded(
                                    flex: 1,
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedCurrencyId,
                                      decoration: InputDecoration(
                                        labelText: 'العملة *',
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                              Layout.defaultRadius),
                                        ),
                                        filled: true,
                                        prefixIcon:
                                            const Icon(Icons.currency_exchange),
                                      ),
                                      items: _currencyPresenter.currencies
                                          .map((currency) {
                                        return DropdownMenuItem<String>(
                                          value: currency.id,
                                          child: Text(
                                              '${currency.name} (${currency.symbol})'),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedCurrencyId = value;
                                          _updateExchangeRate(value);
                                        });
                                      },
                                      validator: (value) {
                                        if (value == null) {
                                          return 'يرجى اختيار العملة';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),

                              // حساب المدين
                              DropdownButtonFormField<String>(
                                value: _selectedDebitAccountId,
                                decoration: InputDecoration(
                                  labelText: 'حساب المدين *',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                        Layout.defaultRadius),
                                  ),
                                  filled: true,
                                  prefixIcon: const Icon(Icons.account_balance),
                                ),
                                items:
                                    _accountPresenter.accounts.map((account) {
                                  return DropdownMenuItem<String>(
                                    value: account['id'].toString(),
                                    child: Text(account['name']),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedDebitAccountId = value;
                                  });
                                },
                                validator: (value) {
                                  if (value == null) {
                                    return 'يرجى اختيار حساب المدين';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 4),

                              // حساب الدائن
                              DropdownButtonFormField<String>(
                                value: _selectedCreditAccountId,
                                decoration: InputDecoration(
                                  labelText: 'حساب الدائن *',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                        Layout.defaultRadius),
                                  ),
                                  filled: true,
                                  prefixIcon: const Icon(Icons.account_balance),
                                ),
                                items:
                                    _accountPresenter.accounts.map((account) {
                                  return DropdownMenuItem<String>(
                                    value: account['id'].toString(),
                                    child: Text(account['name']),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedCreditAccountId = value;
                                  });
                                },
                                validator: (value) {
                                  if (value == null) {
                                    return 'يرجى اختيار حساب الدائن';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 4),

                              // التاريخ والمناولة
                              Row(
                                children: [
                                  // المناولة
                                  Expanded(
                                    flex: 2,
                                    child: AkTextInput(
                                      controller: _handlerController,
                                      label: 'مناولة',
                                      hint: 'اسم المستلم/المسلم',
                                      prefixIcon: Icons.person,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  // التاريخ
                                  Expanded(
                                    flex: 1,
                                    child: InkWell(
                                      onTap: () => _selectDate(context),
                                      child: InputDecorator(
                                        decoration: InputDecoration(
                                          labelText: 'التاريخ',
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(
                                                Layout.defaultRadius),
                                          ),
                                          filled: true,
                                          prefixIcon:
                                              const Icon(Icons.calendar_today),
                                        ),
                                        child: Text(
                                          '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}',
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),

                              // الملاحظات
                              AkTextInput(
                                controller: _notesController,
                                label: 'ملاحظات',
                                hint: 'أدخل ملاحظات إضافية (اختياري)',
                                maxLines: 2,
                                prefixIcon: Icons.note,
                              ),
                              const SizedBox(height: 4),

                              // زر الحفظ
                              Center(
                                child: SizedBox(
                                  width:
                                      Layout.isTablet() ? 200 : double.infinity,
                                  height: 45,
                                  child: ElevatedButton.icon(
                                    onPressed: _isSaving ? null : _saveVoucher,
                                    icon: _isSaving
                                        ? const SizedBox(
                                            width: 18,
                                            height: 18,
                                            child: CircularProgressIndicator(
                                                color: AppColors
                                                    .lightTextSecondary,
                                                strokeWidth: 2))
                                        : const Icon(Icons.save, size: 20),
                                    label: Text(
                                      isEditing ? 'تحديث القيد' : 'حفظ',
                                      style: const AppTypography(fontSize: 15),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.primary,
                                      foregroundColor: AppColors.onPrimary,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // جدول القيود المزدوجة
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 4.0, vertical: 4.0),
                    child: AkCard(
                      padding: EdgeInsets.zero,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4.0, vertical: 4.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4.0, vertical: 2.0),
                              child: Text(
                                'قائمة القيود المزدوجة',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                            ),
                            Expanded(
                              child: AdvancedDataTable(
                                columns: const [
                                  DataColumn(label: Text('إجراءات')),
                                  DataColumn(label: Text('م')),
                                  DataColumn(label: Text('الرقم المرجعي')),
                                  DataColumn(label: Text('التاريخ')),
                                  DataColumn(label: Text('الحساب')),
                                  DataColumn(label: Text('المبلغ')),
                                  DataColumn(label: Text('العملة')),
                                  DataColumn(label: Text('المناولة')),
                                  DataColumn(label: Text('ملاحظات')),
                                ],
                                rows: _filteredVouchers
                                    .asMap()
                                    .entries
                                    .map((entry) {
                                  final voucher = entry.value;
                                  final isHighlighted =
                                      widget.voucher != null &&
                                          widget.voucher!.id == voucher.id;

                                  return DataRow(
                                    color: isHighlighted
                                        ? WidgetStateProperty.all(
                                            Theme.of(context)
                                                .primaryColor
                                                .withValues(alpha: 0.1))
                                        : null,
                                    cells: [
                                      DataCell(
                                        PopupMenuButton<String>(
                                          icon: const Icon(Icons.more_vert),
                                          onSelected: (value) async {
                                            if (value == 'edit') {
                                              // تحرير القيد
                                              Navigator.of(context)
                                                  .push(
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      DoubleEntryVoucherScreen(
                                                          voucher: voucher),
                                                ),
                                              )
                                                  .then((_) {
                                                _loadData();
                                              });
                                            } else if (value == 'delete') {
                                              // حذف القيد بعد التأكيد
                                              final confirm =
                                                  await showDialog<bool>(
                                                context: context,
                                                builder: (context) =>
                                                    AlertDialog(
                                                  title:
                                                      const Text('تأكيد الحذف'),
                                                  content: const Text(
                                                      'هل أنت متأكد من حذف هذا القيد؟'),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () =>
                                                          Navigator.of(context)
                                                              .pop(false),
                                                      child:
                                                          const Text('إلغاء'),
                                                    ),
                                                    ElevatedButton(
                                                      onPressed: () =>
                                                          Navigator.of(context)
                                                              .pop(true),
                                                      style: ElevatedButton
                                                          .styleFrom(
                                                        backgroundColor:
                                                            AppColors.error,
                                                      ),
                                                      child: const Text('حذف'),
                                                    ),
                                                  ],
                                                ),
                                              );

                                              if (confirm == true) {
                                                if (!mounted) return;

                                                // حفظ مراجع آمنة قبل العملية غير المتزامنة
                                                final scaffoldMessenger =
                                                    ScaffoldMessenger.of(
                                                        this.context);

                                                try {
                                                  final success =
                                                      await _voucherPresenter
                                                          .deleteVoucher(
                                                              voucher.id!);

                                                  if (mounted) {
                                                    if (success) {
                                                      scaffoldMessenger
                                                          .showSnackBar(
                                                        const SnackBar(
                                                            content: Text(
                                                                'تم حذف القيد بنجاح')),
                                                      );
                                                    } else {
                                                      scaffoldMessenger
                                                          .showSnackBar(
                                                        const SnackBar(
                                                          content: Text(
                                                              'فشل في حذف القيد'),
                                                          backgroundColor:
                                                              Colors.red,
                                                        ),
                                                      );
                                                    }
                                                  }
                                                } catch (e) {
                                                  if (mounted) {
                                                    scaffoldMessenger
                                                        .showSnackBar(
                                                      SnackBar(
                                                        content: Text(
                                                            'فشل في حذف القيد: ${e.toString()}'),
                                                        backgroundColor:
                                                            Colors.red,
                                                      ),
                                                    );
                                                  }
                                                }
                                              }
                                            }
                                          },
                                          itemBuilder: (context) => [
                                            const PopupMenuItem<String>(
                                              value: 'edit',
                                              child: Row(
                                                children: [
                                                  Icon(Icons.edit),
                                                  SizedBox(width: 8),
                                                  Text('تعديل'),
                                                ],
                                              ),
                                            ),
                                            const PopupMenuItem<String>(
                                              value: 'delete',
                                              child: Row(
                                                children: [
                                                  Icon(Icons.delete,
                                                      color: AppColors.error),
                                                  SizedBox(width: 8),
                                                  Text('حذف'),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      DataCell(Text('${entry.key + 1}')),
                                      DataCell(
                                          Text(voucher.referenceNumber ?? '-')),
                                      DataCell(Text(
                                          '${voucher.voucherDate.year}-${voucher.voucherDate.month.toString().padLeft(2, '0')}-${voucher.voucherDate.day.toString().padLeft(2, '0')}')),
                                      DataCell(
                                          Text(voucher.accountName ?? '-')),
                                      DataCell(Text(
                                          voucher.amount.toStringAsFixed(2))),
                                      DataCell(Text(voucher.currency ?? '-')),
                                      DataCell(Text(voucher.handler ?? '-')),
                                      DataCell(Text(voucher.notes ?? '-')),
                                    ],
                                  );
                                }).toList(),
                                isLoading: _voucherPresenter.isLoading,
                                showCellBorder: true,
                                zebraPattern: true,
                                headerBackgroundColor: AppColors.primary,
                                headerTextColor: AppColors.onPrimary,
                                showRowNumbers: false,
                                emptyMessage: 'لا توجد قيود مزدوجة',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
