/// النموذج الأساسي لجميع النماذج
class BaseModel {
  final String id;
  final DateTime createdAt;
  final String? createdBy;
  final DateTime? updatedAt;
  final String? updatedBy;
  final bool isDeleted;

  BaseModel({
    required this.id,
    required this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.isDeleted = false,
  });

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }
}
