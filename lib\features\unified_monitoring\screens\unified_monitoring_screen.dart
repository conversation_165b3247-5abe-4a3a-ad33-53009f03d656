import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../features/users/models/activity_log.dart';
import '../../../core/theme/index.dart';

/// شاشة موحدة لمراقبة الأخطاء والنشاطات
/// تجمع جميع أنظمة التتبع في مكان واحد لسهولة المراقبة والصيانة
class UnifiedMonitoringScreen extends StatefulWidget {
  const UnifiedMonitoringScreen({Key? key}) : super(key: key);

  @override
  State<UnifiedMonitoringScreen> createState() =>
      _UnifiedMonitoringScreenState();
}

class _UnifiedMonitoringScreenState extends State<UnifiedMonitoringScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  // بيانات الأخطاء
  List<ErrorRecord> _errors = [];
  Map<String, dynamic> _errorStats = {};

  // بيانات النشاطات
  List<ActivityLog> _activities = [];

  // فلاتر
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedModule;
  String? _selectedAction;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل جميع البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل بيانات الأخطاء
      _errors = ErrorTracker.getRecentErrors();
      _errorStats = ErrorTracker.getErrorStats();

      // تحميل بيانات النشاطات
      final activitiesData = await ErrorTracker.getActivities(
        startDate: _startDate,
        endDate: _endDate,
        action: _selectedAction,
        module: _selectedModule,
      );

      _activities = activitiesData.cast<ActivityLog>();
    } catch (e) {
      _showErrorSnackBar('فشل في تحميل البيانات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مراقبة النظام الموحدة'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'لوحة التحكم'),
            Tab(icon: Icon(Icons.error_outline), text: 'الأخطاء'),
            Tab(icon: Icon(Icons.history), text: 'النشاطات'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث البيانات',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'clear_errors':
                  _clearErrors();
                  break;
                case 'clear_activities':
                  _clearActivities();
                  break;
                case 'export':
                  _exportData();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem<String>(
                value: 'clear_errors',
                child: Text('مسح الأخطاء'),
              ),
              const PopupMenuItem<String>(
                value: 'clear_activities',
                child: Text('مسح النشاطات'),
              ),
              const PopupMenuItem<String>(
                value: 'export',
                child: Text('تصدير البيانات'),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDashboardTab(),
                _buildErrorsTab(),
                _buildActivitiesTab(),
              ],
            ),
    );
  }

  /// بناء تبويب لوحة التحكم
  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات النظام',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),

          // إحصائيات الأخطاء
          _buildStatsCard(
            'إحصائيات الأخطاء',
            [
              _buildStatItem(
                  'إجمالي الأخطاء', '${_errorStats['totalErrors'] ?? 0}'),
              _buildStatItem('معدل الأخطاء',
                  '${(_errorStats['recentErrorRate'] ?? 0).toStringAsFixed(2)}/دقيقة'),
              if (_errorStats['mostCommonError'] != null)
                _buildStatItem(
                    'الخطأ الأكثر شيوعاً', '${_errorStats['mostCommonError']}'),
            ],
            Icons.error_outline,
            AppColors.error,
          ),

          const SizedBox(height: 16),

          // إحصائيات النشاطات
          _buildStatsCard(
            'إحصائيات النشاطات',
            [
              _buildStatItem('إجمالي النشاطات', '${_activities.length}'),
              if (_activities.isNotEmpty)
                _buildStatItem(
                    'آخر نشاط',
                    DateFormat('yyyy/MM/dd HH:mm')
                        .format(_activities.first.timestamp)),
            ],
            Icons.history,
            AppColors.info,
          ),

          const SizedBox(height: 16),

          // أحدث الأخطاء
          if (_errors.isNotEmpty) ...[
            Text(
              'أحدث الأخطاء',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            ..._errors.take(3).map((error) => _buildErrorSummaryCard(error)),
          ],

          const SizedBox(height: 16),

          // أحدث النشاطات
          if (_activities.isNotEmpty) ...[
            Text(
              'أحدث النشاطات',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            ..._activities
                .take(3)
                .map((activity) => _buildActivitySummaryCard(activity)),
          ],
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائيات
  Widget _buildStatsCard(
    String title,
    List<Widget> stats,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...stats,
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const AppTypography(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة ملخص خطأ
  Widget _buildErrorSummaryCard(ErrorRecord error) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: const Icon(Icons.error_outline, color: AppColors.error),
        title: Text(
          error.message,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          DateFormat('yyyy/MM/dd HH:mm').format(error.timestamp),
          style: Theme.of(context).textTheme.bodySmall,
        ),
        onTap: () {
          _tabController.animateTo(1); // الانتقال إلى تبويب الأخطاء
        },
      ),
    );
  }

  /// بناء بطاقة ملخص نشاط
  Widget _buildActivitySummaryCard(ActivityLog activity) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: const Icon(Icons.history, color: AppColors.info),
        title: Text(
          '${activity.action} - ${activity.module}',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          '${activity.userName} - ${DateFormat('yyyy/MM/dd HH:mm').format(activity.timestamp)}',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        onTap: () {
          _tabController.animateTo(2); // الانتقال إلى تبويب النشاطات
        },
      ),
    );
  }

  /// بناء تبويب الأخطاء
  Widget _buildErrorsTab() {
    if (_errors.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_circle, size: 64, color: AppColors.success),
            SizedBox(height: 16),
            Text('لا توجد أخطاء مسجلة'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _errors.length,
      itemBuilder: (context, index) {
        final error = _errors[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ExpansionTile(
            leading: const Icon(Icons.error_outline, color: AppColors.error),
            title: Text(error.message),
            subtitle: Text(
              DateFormat('yyyy/MM/dd HH:mm:ss').format(error.timestamp),
              style: Theme.of(context).textTheme.bodySmall,
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (error.context != null) ...[
                      const Text(
                        'السياق:',
                        style: AppTypography(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(error.context.toString()),
                      const SizedBox(height: 16),
                    ],
                    const Text(
                      'تفاصيل الخطأ:',
                      style: AppTypography(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.lightSurfaceVariant,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        error.error.toString(),
                        style: const AppTypography(
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء تبويب النشاطات
  Widget _buildActivitiesTab() {
    if (_activities.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 64, color: AppColors.lightTextSecondary),
            SizedBox(height: 16),
            Text('لا توجد نشاطات مسجلة'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _activities.length,
      itemBuilder: (context, index) {
        final activity = _activities[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: _getActivityIcon(activity.action),
            title: Text('${activity.action} - ${activity.module}'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المستخدم: ${activity.userName}'),
                Text(
                  DateFormat('yyyy/MM/dd HH:mm:ss').format(activity.timestamp),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                if (activity.details.isNotEmpty)
                  Text(
                    'التفاصيل: ${activity.details}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
              ],
            ),
            onTap: () => _showActivityDetails(activity),
          ),
        );
      },
    );
  }

  /// الحصول على أيقونة النشاط
  Widget _getActivityIcon(String action) {
    IconData icon;
    Color color;

    if (action.contains('login') || action.contains('تسجيل دخول')) {
      icon = Icons.login;
      color = AppColors.success;
    } else if (action.contains('logout') || action.contains('تسجيل خروج')) {
      icon = Icons.logout;
      color = AppColors.info;
    } else if (action.contains('create') || action.contains('إضافة')) {
      icon = Icons.add_circle;
      color = AppColors.info;
    } else if (action.contains('update') || action.contains('تعديل')) {
      icon = Icons.edit;
      color = AppColors.warning;
    } else if (action.contains('delete') || action.contains('حذف')) {
      icon = Icons.delete;
      color = AppColors.error;
    } else {
      icon = Icons.info;
      color = AppColors.lightTextSecondary;
    }

    return Icon(icon, color: color);
  }

  /// عرض تفاصيل النشاط
  void _showActivityDetails(ActivityLog activity) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل النشاط'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('المعرف', activity.id),
              _buildDetailRow('المستخدم', activity.userName),
              _buildDetailRow('الإجراء', activity.action),
              _buildDetailRow('الوحدة', activity.module),
              _buildDetailRow('التفاصيل', activity.details),
              _buildDetailRow('عنوان IP', activity.ipAddress),
              _buildDetailRow(
                'التاريخ والوقت',
                DateFormat('yyyy/MM/dd HH:mm:ss').format(activity.timestamp),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء صف تفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label:',
            style: const AppTypography(fontWeight: FontWeight.bold),
          ),
          Text(value.isEmpty ? 'غير متوفر' : value),
          const Divider(),
        ],
      ),
    );
  }

  /// مسح الأخطاء
  void _clearErrors() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح الأخطاء'),
        content:
            const Text('هل أنت متأكد من رغبتك في مسح جميع الأخطاء المسجلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ErrorTracker.clearHistory();
              _loadData();
              _showSuccessSnackBar('تم مسح جميع الأخطاء بنجاح');
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  /// مسح النشاطات
  void _clearActivities() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح النشاطات'),
        content:
            const Text('هل أنت متأكد من رغبتك في مسح جميع النشاطات المسجلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await ErrorTracker.cleanupOldActivities(0);
              if (success) {
                _loadData();
                _showSuccessSnackBar('تم مسح جميع النشاطات بنجاح');
              } else {
                _showErrorSnackBar('فشل في مسح النشاطات');
              }
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  /// تصدير البيانات
  void _exportData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البيانات'),
        content: const Text('سيتم تصدير جميع الأخطاء والنشاطات إلى ملف.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performExport();
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  /// تنفيذ عملية التصدير
  void _performExport() {
    try {
      // هنا يمكن إضافة منطق التصدير الفعلي
      _showSuccessSnackBar('تم تصدير البيانات بنجاح');
    } catch (e) {
      _showErrorSnackBar('فشل في تصدير البيانات: $e');
    }
  }
}
