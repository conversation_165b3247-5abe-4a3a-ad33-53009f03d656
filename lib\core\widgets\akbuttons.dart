import 'package:flutter/material.dart';
import 'dart:async';
import '../theme/index.dart';
import 'akdialogs.dart';

// ═══════════════════════════════════════════════════════════════════════════════
// 🎯 نظام الأزرار الموحد والشامل (AK Buttons System)
// ═══════════════════════════════════════════════════════════════════════════════

/// **📋 فهرس الأزرار:**
///
/// **🔘 القسم الأول: الأزرار الأساسية** (الأسطر 50-1000)
/// ├── 1. AkButton               - الزر الأساسي الموحد
/// ├── 2. AkIconButton           - زر الأيقونة الموحد
/// └── 3. AkTextButton           - زر النص الموحد
///
/// **🎨 القسم الثاني: الأزرار المتخصصة** (الأسطر 1000-1400)
/// ├── 4. AkSaveButton           - زر الحفظ
/// ├── 5. AkDeleteButton         - زر الحذف
/// ├── 6. AkCancelButton         - زر الإلغاء
/// ├── 7. AkAddButton            - زر الإضافة
/// └── 8. AkEditButton           - زر التعديل
///
/// **🎯 القسم الثالث: الأزرار العائمة** (الأسطر 1400-1600)
/// └── 9. AkFloatingButton       - الزر العائم الموحد
///
/// **📱 القسم الرابع: أزرار التنقل** (الأسطر 1600-1700)
/// ├── 10. AkBackButton          - زر الرجوع
/// └── 11. AkNextButton          - زر التالي
///
/// **⚙️ القسم الخامس: الأزرار التفاعلية** (الأسطر 1700-2100)
/// ├── 12. AkToggleButton        - زر التبديل
/// └── 13. AkTimePeriodSelector  - مكون الفترات الزمنية
///
/// **🎯 أمثلة الاستخدام:**
/// ```dart
/// // زر أساسي
/// AkButton(
///   text: 'حفظ',
///   onPressed: () {},
///   type: AkButtonType.primary,
///   size: AkButtonSize.medium,
/// )
///
/// // زر حفظ متخصص
/// AkSaveButton(
///   onPressed: () {},
///   isLoading: false,
/// )
///
/// // زر عائم
/// AkFloatingButton(
///   icon: Icons.add,
///   onPressed: () {},
///   tooltip: 'إضافة عنصر جديد',
/// )
/// ```

// ═══════════════════════════════════════════════════════════════════════════════
// 🔧 الأنواع والثوابت
// ═══════════════════════════════════════════════════════════════════════════════

/// أنواع الأزرار
enum AkButtonType {
  /// زر أساسي (لون أساسي)
  primary,

  /// زر ثانوي (لون ثانوي)
  secondary,

  /// زر النجاح (لون أخضر)
  success,

  /// زر التحذير (لون برتقالي)
  warning,

  /// زر الخطر (لون أحمر)
  danger,

  /// زر المعلومات (لون أزرق)
  info,

  /// زر شفاف (بدون خلفية)
  transparent,
}

/// أحجام الأزرار
enum AkButtonSize {
  /// صغير
  small,

  /// متوسط
  medium,

  /// كبير
  large,

  /// كبير جداً
  extraLarge,
}

/// أشكال الأزرار
enum AkButtonShape {
  /// مستطيل مع زوايا مدورة
  rounded,

  /// دائري
  circular,

  /// مستطيل بزوايا حادة
  square,

  /// بيضاوي
  pill,
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🔘 القسم الأول: الأزرار الأساسية
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● 1. الزر الأساسي الموحد (AkButton)
// ───────────────────────────────────────────────────────────────────────────────

/// الزر الأساسي الموحد لجميع أنواع الأزرار
///
/// **المميزات:**
/// - دعم جميع أنواع الأزرار (أساسي، ثانوي، خطر، إلخ)
/// - أحجام مختلفة (صغير، متوسط، كبير)
/// - أشكال مختلفة (مستطيل، دائري، إلخ)
/// - حالات مختلفة (تفعيل، تعطيل، تحميل)
/// - تحميل كسول للمؤشرات والأيقونات
/// - تكامل مع نظام الثيمات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkButton(
///   text: 'حفظ البيانات',
///   onPressed: () => saveData(),
///   type: AkButtonType.primary,
///   size: AkButtonSize.medium,
///   icon: Icons.save,
///   isLoading: isLoading,
/// )
/// ```
class AkButton extends StatefulWidget {
  /// نص الزر
  final String text;

  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// أيقونة الزر
  final IconData? icon;

  /// نوع الزر
  final AkButtonType type;

  /// حجم الزر
  final AkButtonSize size;

  /// شكل الزر
  final AkButtonShape shape;

  /// هل الزر في حالة تحميل
  final bool isLoading;

  /// هل الزر يأخذ العرض الكامل
  final bool isFullWidth;

  /// لون مخصص للخلفية
  final Color? customBackgroundColor;

  /// لون مخصص للنص
  final Color? customTextColor;

  /// مسافة داخلية مخصصة
  final EdgeInsetsGeometry? customPadding;

  /// ارتفاع مخصص
  final double? customHeight;

  /// عرض مخصص
  final double? customWidth;

  /// نص التلميح
  final String? tooltip;

  const AkButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.type = AkButtonType.primary,
    this.size = AkButtonSize.medium,
    this.shape = AkButtonShape.rounded,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customBackgroundColor,
    this.customTextColor,
    this.customPadding,
    this.customHeight,
    this.customWidth,
    this.tooltip,
  });

  @override
  State<AkButton> createState() => _AkButtonState();
}

class _AkButtonState extends State<AkButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// الحصول على لون الخلفية حسب النوع
  Color _getBackgroundColor() {
    if (widget.customBackgroundColor != null) {
      return widget.customBackgroundColor!;
    }

    switch (widget.type) {
      case AkButtonType.primary:
        return AppColors.primary;
      case AkButtonType.secondary:
        return AppColors.lightTextSecondary;
      case AkButtonType.success:
        return AppColors.success;
      case AkButtonType.warning:
        return AppColors.warning;
      case AkButtonType.danger:
        return AppColors.error;
      case AkButtonType.info:
        return AppColors.info;
      case AkButtonType.transparent:
        return Colors.transparent;
    }
  }

  /// الحصول على لون النص حسب النوع
  Color _getTextColor() {
    if (widget.customTextColor != null) {
      return widget.customTextColor!;
    }

    switch (widget.type) {
      case AkButtonType.primary:
      case AkButtonType.success:
      case AkButtonType.warning:
      case AkButtonType.danger:
      case AkButtonType.info:
        return AppColors.lightBackground;
      case AkButtonType.secondary:
        return AppColors.lightTextPrimary;
      case AkButtonType.transparent:
        return AppColors.primary;
    }
  }

  /// الحصول على المسافة الداخلية حسب الحجم
  EdgeInsetsGeometry _getPadding() {
    if (widget.customPadding != null) {
      return widget.customPadding!;
    }

    switch (widget.size) {
      case AkButtonSize.small:
        return EdgeInsets.symmetric(
          horizontal: AppDimensions.smallMargin,
          vertical: AppDimensions.tinySpacing,
        );
      case AkButtonSize.medium:
        return EdgeInsets.symmetric(
          horizontal: AppDimensions.defaultMargin,
          vertical: AppDimensions.smallMargin,
        );
      case AkButtonSize.large:
        return EdgeInsets.symmetric(
          horizontal: AppDimensions.largeMargin,
          vertical: AppDimensions.defaultMargin,
        );
      case AkButtonSize.extraLarge:
        return EdgeInsets.symmetric(
          horizontal: AppDimensions.extraLargeMargin,
          vertical: AppDimensions.largeMargin,
        );
    }
  }

  /// الحصول على حجم الخط حسب حجم الزر
  double _getFontSize() {
    switch (widget.size) {
      case AkButtonSize.small:
        return AppTypography.fontSizeSmall;
      case AkButtonSize.medium:
        return AppTypography.fontSizeMedium;
      case AkButtonSize.large:
        return AppTypography.fontSizeLarge;
      case AkButtonSize.extraLarge:
        return AppTypography.fontSizeLarge;
    }
  }

  /// الحصول على حجم الأيقونة حسب حجم الزر
  double _getIconSize() {
    switch (widget.size) {
      case AkButtonSize.small:
        return AppDimensions.smallIconSize;
      case AkButtonSize.medium:
        return AppDimensions.mediumIconSize;
      case AkButtonSize.large:
        return AppDimensions.largeIconSize;
      case AkButtonSize.extraLarge:
        return AppDimensions.extraLargeIconSize;
    }
  }

  /// الحصول على نصف قطر الحواف حسب الشكل
  BorderRadius _getBorderRadius() {
    switch (widget.shape) {
      case AkButtonShape.rounded:
        return BorderRadius.circular(AppDimensions.mediumRadius);
      case AkButtonShape.circular:
        return BorderRadius.circular(100);
      case AkButtonShape.square:
        return BorderRadius.zero;
      case AkButtonShape.pill:
        return BorderRadius.circular(AppDimensions.largeRadius);
    }
  }

  /// بناء مؤشر التحميل مع التحميل الكسول
  Widget? _buildAkLoadingIndicator() {
    if (!widget.isLoading) return null;

    return SizedBox(
      width: _getIconSize(),
      height: _getIconSize(),
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(_getTextColor()),
      ),
    );
  }

  /// بناء الأيقونة مع التحميل الكسول
  Widget? _buildIcon() {
    if (widget.icon == null || widget.isLoading) return null;

    return Icon(
      widget.icon,
      size: _getIconSize(),
      color: _getTextColor(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = _getBackgroundColor();
    final textColor = _getTextColor();
    final isEnabled = widget.onPressed != null && !widget.isLoading;

    Widget button = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.isFullWidth ? double.infinity : widget.customWidth,
            height: widget.customHeight,
            decoration: BoxDecoration(
              color: isEnabled
                  ? backgroundColor
                  : backgroundColor.withValues(alpha: 0.5),
              borderRadius: _getBorderRadius(),
              border: widget.type == AkButtonType.transparent
                  ? Border.all(color: AppColors.lightBorder)
                  : null,
              boxShadow: widget.type != AkButtonType.transparent && isEnabled
                  ? [
                      BoxShadow(
                        color: backgroundColor.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: isEnabled ? widget.onPressed : null,
                onTapDown:
                    isEnabled ? (_) => _animationController.forward() : null,
                onTapUp:
                    isEnabled ? (_) => _animationController.reverse() : null,
                onTapCancel:
                    isEnabled ? () => _animationController.reverse() : null,
                borderRadius: _getBorderRadius(),
                child: Padding(
                  padding: _getPadding(),
                  child: Row(
                    mainAxisSize: widget.isFullWidth
                        ? MainAxisSize.max
                        : MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // مؤشر التحميل أو الأيقونة
                      if (widget.isLoading)
                        _buildAkLoadingIndicator()!
                      else if (widget.icon != null)
                        _buildIcon()!,

                      // مسافة بين الأيقونة والنص
                      if ((widget.icon != null || widget.isLoading) &&
                          widget.text.isNotEmpty)
                        SizedBox(width: AppDimensions.tinySpacing),

                      // النص مع Flexible لمنع overflow
                      if (widget.text.isNotEmpty)
                        Flexible(
                          child: Text(
                            widget.text,
                            style: AppTypography.createCustomStyle(
                              fontSize: _getFontSize(),
                              fontWeight: AppTypography.weightMedium,
                              color: isEnabled
                                  ? textColor
                                  : textColor.withValues(alpha: 0.5),
                            ),
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );

    // إضافة التلميح إذا كان متوفراً
    if (widget.tooltip != null) {
      button = Tooltip(
        message: widget.tooltip!,
        child: button,
      );
    }

    return button;
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● 2. زر الأيقونة الموحد (AkIconButton)
// ───────────────────────────────────────────────────────────────────────────────

/// زر أيقونة موحد مع تأثيرات تفاعلية
///
/// **المميزات:**
/// - أحجام مختلفة للأيقونات
/// - ألوان متنوعة حسب السياق
/// - تأثيرات تفاعلية مع التحميل الكسول
/// - دعم التلميحات
/// - تكامل مع نظام الثيمات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkIconButton(
///   icon: Icons.edit,
///   onPressed: () => editItem(),
///   type: AkButtonType.primary,
///   size: AkButtonSize.medium,
///   tooltip: 'تعديل العنصر',
/// )
/// ```
class AkIconButton extends StatefulWidget {
  /// أيقونة الزر
  final IconData icon;

  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// نوع الزر
  final AkButtonType type;

  /// حجم الزر
  final AkButtonSize size;

  /// شكل الزر
  final AkButtonShape shape;

  /// هل الزر في حالة تحميل
  final bool isLoading;

  /// لون مخصص للأيقونة
  final Color? customIconColor;

  /// لون مخصص للخلفية
  final Color? customBackgroundColor;

  /// نص التلميح
  final String? tooltip;

  /// هل يتم عرض خلفية
  final bool showBackground;

  const AkIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.type = AkButtonType.primary,
    this.size = AkButtonSize.medium,
    this.shape = AkButtonShape.circular,
    this.isLoading = false,
    this.customIconColor,
    this.customBackgroundColor,
    this.tooltip,
    this.showBackground = true,
  });

  @override
  State<AkIconButton> createState() => _AkIconButtonState();
}

class _AkIconButtonState extends State<AkIconButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// الحصول على لون الأيقونة
  Color _getIconColor() {
    if (widget.customIconColor != null) {
      return widget.customIconColor!;
    }

    if (!widget.showBackground) {
      switch (widget.type) {
        case AkButtonType.primary:
          return AppColors.primary;
        case AkButtonType.secondary:
          return AppColors.lightTextSecondary;
        case AkButtonType.success:
          return AppColors.success;
        case AkButtonType.warning:
          return AppColors.warning;
        case AkButtonType.danger:
          return AppColors.error;
        case AkButtonType.info:
          return AppColors.info;
        case AkButtonType.transparent:
          return AppColors.lightTextPrimary;
      }
    }

    return AppColors.lightBackground;
  }

  /// الحصول على لون الخلفية
  Color _getBackgroundColor() {
    if (!widget.showBackground) {
      return Colors.transparent;
    }

    if (widget.customBackgroundColor != null) {
      return widget.customBackgroundColor!;
    }

    switch (widget.type) {
      case AkButtonType.primary:
        return AppColors.primary;
      case AkButtonType.secondary:
        return AppColors.lightTextSecondary;
      case AkButtonType.success:
        return AppColors.success;
      case AkButtonType.warning:
        return AppColors.warning;
      case AkButtonType.danger:
        return AppColors.error;
      case AkButtonType.info:
        return AppColors.info;
      case AkButtonType.transparent:
        return Colors.transparent;
    }
  }

  /// الحصول على حجم الأيقونة
  double _getIconSize() {
    switch (widget.size) {
      case AkButtonSize.small:
        return AppDimensions.smallIconSize;
      case AkButtonSize.medium:
        return AppDimensions.mediumIconSize;
      case AkButtonSize.large:
        return AppDimensions.largeIconSize;
      case AkButtonSize.extraLarge:
        return AppDimensions.extraLargeIconSize;
    }
  }

  /// الحصول على حجم الحاوية
  double _getContainerSize() {
    switch (widget.size) {
      case AkButtonSize.small:
        return AppDimensions.smallWidgetSize;
      case AkButtonSize.medium:
        return AppDimensions.mediumWidgetSize;
      case AkButtonSize.large:
        return AppDimensions.largeWidgetSize;
      case AkButtonSize.extraLarge:
        return AppDimensions.extraLargeWidgetSize;
    }
  }

  /// الحصول على نصف قطر الحواف
  BorderRadius _getBorderRadius() {
    final containerSize = _getContainerSize();

    switch (widget.shape) {
      case AkButtonShape.rounded:
        return BorderRadius.circular(AppDimensions.mediumRadius);
      case AkButtonShape.circular:
        return BorderRadius.circular(containerSize / 2);
      case AkButtonShape.square:
        return BorderRadius.zero;
      case AkButtonShape.pill:
        return BorderRadius.circular(AppDimensions.largeRadius);
    }
  }

  /// بناء مؤشر التحميل مع التحميل الكسول
  Widget _buildContent() {
    if (widget.isLoading) {
      return SizedBox(
        width: _getIconSize(),
        height: _getIconSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(_getIconColor()),
        ),
      );
    }

    return Icon(
      widget.icon,
      size: _getIconSize(),
      color: _getIconColor(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = _getBackgroundColor();
    final isEnabled = widget.onPressed != null && !widget.isLoading;
    final containerSize = _getContainerSize();

    Widget button = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: containerSize,
            height: containerSize,
            decoration: BoxDecoration(
              color: isEnabled
                  ? backgroundColor
                  : backgroundColor.withValues(alpha: 0.5),
              borderRadius: _getBorderRadius(),
              border: widget.type == AkButtonType.transparent &&
                      widget.showBackground
                  ? Border.all(color: AppColors.lightBorder)
                  : null,
              boxShadow: widget.showBackground &&
                      widget.type != AkButtonType.transparent &&
                      isEnabled
                  ? [
                      BoxShadow(
                        color: backgroundColor.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: isEnabled ? widget.onPressed : null,
                onTapDown:
                    isEnabled ? (_) => _animationController.forward() : null,
                onTapUp:
                    isEnabled ? (_) => _animationController.reverse() : null,
                onTapCancel:
                    isEnabled ? () => _animationController.reverse() : null,
                borderRadius: _getBorderRadius(),
                child: Center(
                  child: _buildContent(),
                ),
              ),
            ),
          ),
        );
      },
    );

    // إضافة التلميح إذا كان متوفراً
    if (widget.tooltip != null) {
      button = Tooltip(
        message: widget.tooltip!,
        child: button,
      );
    }

    return button;
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● 3. زر النص الموحد (AkTextButton)
// ───────────────────────────────────────────────────────────────────────────────

/// زر نص موحد بدون خلفية مع تأثيرات تفاعلية
///
/// **المميزات:**
/// - تصميم نظيف بدون خلفية
/// - ألوان متنوعة حسب السياق
/// - تأثيرات تفاعلية خفيفة
/// - دعم الأيقونات الاختيارية
/// - تكامل مع نظام الثيمات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkTextButton(
///   text: 'تسجيل الدخول',
///   onPressed: () => login(),
///   type: AkButtonType.primary,
///   icon: Icons.login,
/// )
/// ```
class AkTextButton extends StatefulWidget {
  /// نص الزر
  final String text;

  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// أيقونة الزر (اختيارية)
  final IconData? icon;

  /// نوع الزر
  final AkButtonType type;

  /// حجم الزر
  final AkButtonSize size;

  /// هل الزر في حالة تحميل
  final bool isLoading;

  /// لون مخصص للنص
  final Color? customTextColor;

  /// نص التلميح
  final String? tooltip;

  const AkTextButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.type = AkButtonType.primary,
    this.size = AkButtonSize.medium,
    this.isLoading = false,
    this.customTextColor,
    this.tooltip,
  });

  @override
  State<AkTextButton> createState() => _AkTextButtonState();
}

class _AkTextButtonState extends State<AkTextButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// الحصول على لون النص حسب النوع
  Color _getTextColor() {
    if (widget.customTextColor != null) {
      return widget.customTextColor!;
    }

    switch (widget.type) {
      case AkButtonType.primary:
        return AppColors.primary;
      case AkButtonType.secondary:
        return AppColors.lightTextSecondary;
      case AkButtonType.success:
        return AppColors.success;
      case AkButtonType.warning:
        return AppColors.warning;
      case AkButtonType.danger:
        return AppColors.error;
      case AkButtonType.info:
        return AppColors.info;
      case AkButtonType.transparent:
        return AppColors.lightTextPrimary;
    }
  }

  /// الحصول على حجم الخط حسب حجم الزر
  double _getFontSize() {
    switch (widget.size) {
      case AkButtonSize.small:
        return AppTypography.fontSizeSmall;
      case AkButtonSize.medium:
        return AppTypography.fontSizeMedium;
      case AkButtonSize.large:
        return AppTypography.fontSizeLarge;
      case AkButtonSize.extraLarge:
        return AppTypography.fontSizeLarge;
    }
  }

  /// الحصول على حجم الأيقونة حسب حجم الزر
  double _getIconSize() {
    switch (widget.size) {
      case AkButtonSize.small:
        return AppDimensions.smallIconSize;
      case AkButtonSize.medium:
        return AppDimensions.mediumIconSize;
      case AkButtonSize.large:
        return AppDimensions.largeIconSize;
      case AkButtonSize.extraLarge:
        return AppDimensions.extraLargeIconSize;
    }
  }

  /// بناء مؤشر التحميل مع التحميل الكسول
  Widget? _buildAkLoadingIndicator() {
    if (!widget.isLoading) return null;

    return SizedBox(
      width: _getIconSize(),
      height: _getIconSize(),
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(_getTextColor()),
      ),
    );
  }

  /// بناء الأيقونة مع التحميل الكسول
  Widget? _buildIcon() {
    if (widget.icon == null || widget.isLoading) return null;

    return Icon(
      widget.icon,
      size: _getIconSize(),
      color: _getTextColor(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textColor = _getTextColor();
    final isEnabled = widget.onPressed != null && !widget.isLoading;

    Widget button = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: TextButton(
            onPressed: isEnabled ? widget.onPressed : null,
            style: TextButton.styleFrom(
              foregroundColor: textColor,
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.defaultMargin,
                vertical: AppDimensions.smallMargin,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // مؤشر التحميل أو الأيقونة
                if (widget.isLoading)
                  _buildAkLoadingIndicator()!
                else if (widget.icon != null)
                  _buildIcon()!,

                // مسافة بين الأيقونة والنص
                if ((widget.icon != null || widget.isLoading) &&
                    widget.text.isNotEmpty)
                  SizedBox(width: AppDimensions.tinySpacing),

                // النص مع Flexible لمنع overflow
                if (widget.text.isNotEmpty)
                  Flexible(
                    child: Text(
                      widget.text,
                      style: AppTypography.createCustomStyle(
                        fontSize: _getFontSize(),
                        fontWeight: AppTypography.weightMedium,
                        color: isEnabled
                            ? textColor
                            : textColor.withValues(alpha: 0.5),
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );

    // إضافة التلميح إذا كان متوفراً
    if (widget.tooltip != null) {
      button = Tooltip(
        message: widget.tooltip!,
        child: button,
      );
    }

    // إضافة التأثيرات التفاعلية
    return GestureDetector(
      onTapDown: isEnabled ? (_) => _animationController.forward() : null,
      onTapUp: isEnabled ? (_) => _animationController.reverse() : null,
      onTapCancel: isEnabled ? () => _animationController.reverse() : null,
      child: button,
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🎨 القسم الثاني: الأزرار المتخصصة
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● 4. زر الحفظ (AkSaveButton)
// ───────────────────────────────────────────────────────────────────────────────

/// زر حفظ متخصص مع تصميم موحد
///
/// **المميزات:**
/// - تصميم موحد لجميع أزرار الحفظ
/// - مؤشر تحميل تلقائي
/// - لون أخضر للنجاح
/// - أيقونة حفظ افتراضية
///
/// **مثال الاستخدام:**
/// ```dart
/// AkSaveButton(
///   onPressed: () => saveData(),
///   isLoading: isLoading,
///   text: 'حفظ البيانات',
/// )
/// ```
class AkSaveButton extends StatelessWidget {
  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// هل الزر في حالة تحميل
  final bool isLoading;

  /// نص الزر
  final String text;

  /// حجم الزر
  final AkButtonSize size;

  /// هل الزر يأخذ العرض الكامل
  final bool isFullWidth;

  const AkSaveButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
    this.text = 'حفظ',
    this.size = AkButtonSize.medium,
    this.isFullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.save,
      type: AkButtonType.success,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
      tooltip: 'حفظ البيانات',
    );
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● 5. زر الحذف (AkDeleteButton)
// ───────────────────────────────────────────────────────────────────────────────

/// زر حذف متخصص مع تصميم موحد
///
/// **المميزات:**
/// - تصميم موحد لجميع أزرار الحذف
/// - لون أحمر للخطر
/// - أيقونة حذف افتراضية
/// - تأكيد اختياري قبل الحذف
///
/// **مثال الاستخدام:**
/// ```dart
/// AkDeleteButton(
///   onPressed: () => deleteItem(),
///   text: 'حذف العنصر',
///   requireConfirmation: true,
/// )
/// ```
class AkDeleteButton extends StatelessWidget {
  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// هل الزر في حالة تحميل
  final bool isLoading;

  /// نص الزر
  final String text;

  /// حجم الزر
  final AkButtonSize size;

  /// هل الزر يأخذ العرض الكامل
  final bool isFullWidth;

  /// هل يتطلب تأكيد قبل الحذف
  final bool requireConfirmation;

  /// رسالة التأكيد
  final String confirmationMessage;

  const AkDeleteButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
    this.text = 'حذف',
    this.size = AkButtonSize.medium,
    this.isFullWidth = false,
    this.requireConfirmation = false,
    this.confirmationMessage = 'هل أنت متأكد من الحذف؟',
  });

  /// عرض حوار التأكيد مع التحميل الكسول
  Future<void> _showConfirmationDialog(BuildContext context) async {
    final confirmed = await AkConfirmDialog.show(
      context: context,
      title: 'تأكيد الحذف',
      content: confirmationMessage,
      confirmText: 'حذف',
      cancelText: 'إلغاء',
      type: AkDialogType.danger,
      icon: Icons.delete_forever,
    );

    if (confirmed == true && onPressed != null) {
      onPressed!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AkButton(
      text: text,
      onPressed: requireConfirmation
          ? () => _showConfirmationDialog(context)
          : onPressed,
      icon: Icons.delete,
      type: AkButtonType.danger,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
      tooltip: 'حذف العنصر',
    );
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● 6. زر الإلغاء (AkCancelButton)
// ───────────────────────────────────────────────────────────────────────────────

/// زر إلغاء متخصص مع تصميم موحد
///
/// **المميزات:**
/// - تصميم موحد لجميع أزرار الإلغاء
/// - نمط شفاف أو ثانوي
/// - أيقونة إلغاء افتراضية
/// - إغلاق تلقائي للحوارات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkCancelButton(
///   onPressed: () => Navigator.pop(context),
///   text: 'إلغاء',
/// )
/// ```
class AkCancelButton extends StatelessWidget {
  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// نص الزر
  final String text;

  /// حجم الزر
  final AkButtonSize size;

  /// هل الزر يأخذ العرض الكامل
  final bool isFullWidth;

  /// هل يتم إغلاق الحوار تلقائياً
  final bool autoClose;

  const AkCancelButton({
    super.key,
    this.onPressed,
    this.text = 'إلغاء',
    this.size = AkButtonSize.medium,
    this.isFullWidth = false,
    this.autoClose = true,
  });

  @override
  Widget build(BuildContext context) {
    return AkButton(
      text: text,
      onPressed:
          onPressed ?? (autoClose ? () => Navigator.of(context).pop() : null),
      icon: Icons.cancel,
      type: AkButtonType.transparent,
      size: size,
      isFullWidth: isFullWidth,
      tooltip: 'إلغاء العملية',
    );
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● 7. زر الإضافة (AkAddButton)
// ───────────────────────────────────────────────────────────────────────────────

/// زر إضافة متخصص مع تصميم موحد
///
/// **المميزات:**
/// - تصميم موحد لجميع أزرار الإضافة
/// - لون أخضر للنجاح
/// - أيقونة إضافة افتراضية
/// - دعم النصوص المخصصة
///
/// **مثال الاستخدام:**
/// ```dart
/// AkAddButton(
///   onPressed: () => addNewItem(),
///   text: 'إضافة عنصر جديد',
/// )
/// ```
class AkAddButton extends StatelessWidget {
  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// هل الزر في حالة تحميل
  final bool isLoading;

  /// نص الزر
  final String text;

  /// حجم الزر
  final AkButtonSize size;

  /// هل الزر يأخذ العرض الكامل
  final bool isFullWidth;

  const AkAddButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
    this.text = 'إضافة',
    this.size = AkButtonSize.medium,
    this.isFullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.add,
      type: AkButtonType.success,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
      tooltip: 'إضافة عنصر جديد',
    );
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● 8. زر التعديل (AkEditButton)
// ───────────────────────────────────────────────────────────────────────────────

/// زر تعديل متخصص مع تصميم موحد
///
/// **المميزات:**
/// - تصميم موحد لجميع أزرار التعديل
/// - لون أزرق للمعلومات
/// - أيقونة تعديل افتراضية
/// - دعم النصوص المخصصة
///
/// **مثال الاستخدام:**
/// ```dart
/// AkEditButton(
///   onPressed: () => editItem(),
///   text: 'تعديل العنصر',
/// )
/// ```
class AkEditButton extends StatelessWidget {
  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// هل الزر في حالة تحميل
  final bool isLoading;

  /// نص الزر
  final String text;

  /// حجم الزر
  final AkButtonSize size;

  /// هل الزر يأخذ العرض الكامل
  final bool isFullWidth;

  const AkEditButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
    this.text = 'تعديل',
    this.size = AkButtonSize.medium,
    this.isFullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.edit,
      type: AkButtonType.info,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
      tooltip: 'تعديل العنصر',
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🎯 القسم الثالث: الأزرار العائمة
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● 8. الزر العائم الموحد (AkFloatingButton)
// ───────────────────────────────────────────────────────────────────────────────

/// زر عائم موحد مع تأثيرات متقدمة
///
/// **المميزات:**
/// - تصميم موحد لجميع الأزرار العائمة
/// - أحجام مختلفة (عادي، صغير، كبير)
/// - ألوان متنوعة حسب السياق
/// - تأثيرات تفاعلية مع التحميل الكسول
/// - دعم الأيقونات المخصصة
///
/// **مثال الاستخدام:**
/// ```dart
/// AkFloatingButton(
///   icon: Icons.add,
///   onPressed: () => addNewItem(),
///   type: AkButtonType.primary,
///   tooltip: 'إضافة عنصر جديد',
/// )
/// ```
class AkFloatingButton extends StatefulWidget {
  /// أيقونة الزر
  final IconData icon;

  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// نوع الزر
  final AkButtonType type;

  /// حجم الزر
  final AkButtonSize size;

  /// هل الزر في حالة تحميل
  final bool isLoading;

  /// لون مخصص للخلفية
  final Color? customBackgroundColor;

  /// لون مخصص للأيقونة
  final Color? customIconColor;

  /// نص التلميح
  final String? tooltip;

  /// هل الزر صغير
  final bool mini;

  const AkFloatingButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.type = AkButtonType.primary,
    this.size = AkButtonSize.medium,
    this.isLoading = false,
    this.customBackgroundColor,
    this.customIconColor,
    this.tooltip,
    this.mini = false,
  });

  @override
  State<AkFloatingButton> createState() => _AkFloatingButtonState();
}

class _AkFloatingButtonState extends State<AkFloatingButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// الحصول على لون الخلفية
  Color _getBackgroundColor() {
    if (widget.customBackgroundColor != null) {
      return widget.customBackgroundColor!;
    }

    switch (widget.type) {
      case AkButtonType.primary:
        return AppColors.primary;
      case AkButtonType.secondary:
        return AppColors.lightTextSecondary;
      case AkButtonType.success:
        return AppColors.success;
      case AkButtonType.warning:
        return AppColors.warning;
      case AkButtonType.danger:
        return AppColors.error;
      case AkButtonType.info:
        return AppColors.info;
      case AkButtonType.transparent:
        return AppColors.lightBackground;
    }
  }

  /// الحصول على لون الأيقونة
  Color _getIconColor() {
    if (widget.customIconColor != null) {
      return widget.customIconColor!;
    }

    switch (widget.type) {
      case AkButtonType.primary:
      case AkButtonType.success:
      case AkButtonType.warning:
      case AkButtonType.danger:
      case AkButtonType.info:
        return AppColors.lightBackground;
      case AkButtonType.secondary:
        return AppColors.lightTextPrimary;
      case AkButtonType.transparent:
        return AppColors.primary;
    }
  }

  /// بناء محتوى الزر مع التحميل الكسول
  Widget _buildContent() {
    if (widget.isLoading) {
      return SizedBox(
        width: widget.mini ? 16 : 24,
        height: widget.mini ? 16 : 24,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(_getIconColor()),
        ),
      );
    }

    return Icon(
      widget.icon,
      color: _getIconColor(),
      size: widget.mini
          ? AppDimensions.smallIconSize
          : AppDimensions.mediumIconSize,
    );
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = _getBackgroundColor();
    final isEnabled = widget.onPressed != null && !widget.isLoading;

    Widget button = AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: FloatingActionButton(
              onPressed: isEnabled ? widget.onPressed : null,
              backgroundColor: isEnabled
                  ? backgroundColor
                  : backgroundColor.withValues(alpha: 0.5),
              foregroundColor: _getIconColor(),
              elevation: isEnabled ? 6 : 2,
              mini: widget.mini,
              child: _buildContent(),
            ),
          ),
        );
      },
    );

    // إضافة التلميح إذا كان متوفراً
    if (widget.tooltip != null) {
      button = Tooltip(
        message: widget.tooltip!,
        child: button,
      );
    }

    // إضافة التأثيرات التفاعلية
    return GestureDetector(
      onTapDown: isEnabled ? (_) => _animationController.forward() : null,
      onTapUp: isEnabled ? (_) => _animationController.reverse() : null,
      onTapCancel: isEnabled ? () => _animationController.reverse() : null,
      child: button,
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📱 القسم الرابع: أزرار التنقل
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● 10. زر الرجوع (AkBackButton)
// ───────────────────────────────────────────────────────────────────────────────

/// زر رجوع موحد مع تصميم متناسق
///
/// **المميزات:**
/// - تصميم موحد لجميع أزرار الرجوع
/// - إغلاق تلقائي للشاشة الحالية
/// - أيقونة رجوع افتراضية
/// - دعم النصوص المخصصة
///
/// **مثال الاستخدام:**
/// ```dart
/// AkBackButton(
///   onPressed: () => Navigator.pop(context),
///   text: 'رجوع',
/// )
/// ```
class AkBackButton extends StatelessWidget {
  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// نص الزر
  final String text;

  /// حجم الزر
  final AkButtonSize size;

  /// هل يتم إغلاق الشاشة تلقائياً
  final bool autoClose;

  const AkBackButton({
    super.key,
    this.onPressed,
    this.text = 'رجوع',
    this.size = AkButtonSize.medium,
    this.autoClose = true,
  });

  @override
  Widget build(BuildContext context) {
    return AkButton(
      text: text,
      onPressed:
          onPressed ?? (autoClose ? () => Navigator.of(context).pop() : null),
      icon: Icons.arrow_back,
      type: AkButtonType.secondary,
      size: size,
      tooltip: 'العودة للصفحة السابقة',
    );
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● 11. زر التالي (AkNextButton)
// ───────────────────────────────────────────────────────────────────────────────

/// زر التالي موحد مع تصميم متناسق
///
/// **المميزات:**
/// - تصميم موحد لجميع أزرار التالي
/// - أيقونة تالي افتراضية
/// - دعم النصوص المخصصة
/// - دعم حالة التحميل
///
/// **مثال الاستخدام:**
/// ```dart
/// AkNextButton(
///   onPressed: () => goToNextStep(),
///   text: 'التالي',
///   isLoading: isLoading,
/// )
/// ```
class AkNextButton extends StatelessWidget {
  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// نص الزر
  final String text;

  /// حجم الزر
  final AkButtonSize size;

  /// هل الزر في حالة تحميل
  final bool isLoading;

  /// هل الزر يأخذ العرض الكامل
  final bool isFullWidth;

  const AkNextButton({
    super.key,
    this.onPressed,
    this.text = 'التالي',
    this.size = AkButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.arrow_forward,
      type: AkButtonType.primary,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
      tooltip: 'الانتقال للخطوة التالية',
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// ⚙️ القسم الخامس: الأزرار التفاعلية
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● 12. زر التبديل (AkToggleButton)
// ───────────────────────────────────────────────────────────────────────────────

/// زر تبديل موحد مع حالات متعددة
///
/// **المميزات:**
/// - تبديل بين حالتين (مفعل/معطل)
/// - ألوان مختلفة للحالات
/// - تأثيرات تفاعلية
/// - دعم الأيقونات المختلفة للحالات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkToggleButton(
///   isToggled: isEnabled,
///   onToggle: (value) => setState(() => isEnabled = value),
///   activeText: 'مفعل',
///   inactiveText: 'معطل',
/// )
/// ```
class AkToggleButton extends StatefulWidget {
  /// هل الزر مفعل
  final bool isToggled;

  /// دالة تنفذ عند التبديل
  final ValueChanged<bool>? onToggle;

  /// نص الحالة المفعلة
  final String activeText;

  /// نص الحالة المعطلة
  final String inactiveText;

  /// أيقونة الحالة المفعلة
  final IconData? activeIcon;

  /// أيقونة الحالة المعطلة
  final IconData? inactiveIcon;

  /// حجم الزر
  final AkButtonSize size;

  const AkToggleButton({
    super.key,
    required this.isToggled,
    this.onToggle,
    this.activeText = 'مفعل',
    this.inactiveText = 'معطل',
    this.activeIcon,
    this.inactiveIcon,
    this.size = AkButtonSize.medium,
  });

  @override
  State<AkToggleButton> createState() => _AkToggleButtonState();
}

class _AkToggleButtonState extends State<AkToggleButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleToggle() {
    if (widget.onToggle != null) {
      widget.onToggle!(!widget.isToggled);
      _animationController.forward().then((_) {
        _animationController.reverse();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: AkButton(
            text: widget.isToggled ? widget.activeText : widget.inactiveText,
            onPressed: _handleToggle,
            icon: widget.isToggled
                ? (widget.activeIcon ?? Icons.toggle_on)
                : (widget.inactiveIcon ?? Icons.toggle_off),
            type: widget.isToggled
                ? AkButtonType.success
                : AkButtonType.secondary,
            size: widget.size,
            tooltip: widget.isToggled ? 'إلغاء التفعيل' : 'تفعيل',
          ),
        );
      },
    );
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● 13. مكون الفترات الزمنية (AkTimePeriodSelector)
// ───────────────────────────────────────────────────────────────────────────────

/// نموذج بيانات الفترة الزمنية
class TimePeriod {
  /// معرف الفترة
  final String id;

  /// اسم الفترة
  final String name;

  /// أيقونة الفترة
  final IconData icon;

  /// هل الفترة مخصصة
  final bool isCustom;

  const TimePeriod({
    required this.id,
    required this.name,
    required this.icon,
    this.isCustom = false,
  });
}

/// مكون عرض الفترات الزمنية مع إمكانية التحديد
///
/// **المميزات:**
/// - عرض الفترات الزمنية في شكل بطاقات أنيقة
/// - تحديد فترة واحدة مع تمييز بصري واضح
/// - دعم كامل للوضع الليلي والنهاري
/// - تأثيرات تفاعلية سلسة
/// - استخدام الأنظمة الموحدة (AkCard، AppColors، إلخ)
/// - دعم الاتجاه من اليمين لليسار (RTL)
///
/// **مثال الاستخدام:**
/// ```dart
/// AkTimePeriodSelector(
///   periods: [
///     TimePeriod(id: 'daily', name: 'يومي', icon: Icons.today),
///     TimePeriod(id: 'weekly', name: 'أسبوعي', icon: Icons.view_week),
///     TimePeriod(id: 'monthly', name: 'شهري', icon: Icons.calendar_month),
///     TimePeriod(id: 'yearly', name: 'سنوي', icon: Icons.calendar_view_year),
///   ],
///   selectedPeriodId: selectedPeriod,
///   onPeriodSelected: (period) => setState(() => selectedPeriod = period.id),
/// )
/// ```
class AkTimePeriodSelector extends StatefulWidget {
  /// قائمة الفترات الزمنية المتاحة
  final List<TimePeriod> periods;

  /// معرف الفترة المحددة حالياً
  final String? selectedPeriodId;

  /// دالة تنفذ عند تحديد فترة
  final ValueChanged<TimePeriod>? onPeriodSelected;

  /// عدد الأعمدة في الشبكة
  final int crossAxisCount;

  /// نسبة العرض إلى الارتفاع للبطاقات
  final double childAspectRatio;

  /// المسافة بين البطاقات
  final double spacing;

  /// هل يتم عرض النص أسفل الأيقونة
  final bool showText;

  const AkTimePeriodSelector({
    super.key,
    required this.periods,
    this.selectedPeriodId,
    this.onPeriodSelected,
    this.crossAxisCount = 4,
    this.childAspectRatio = 1.0,
    this.spacing = 12.0,
    this.showText = true,
  });

  @override
  State<AkTimePeriodSelector> createState() => _AkTimePeriodSelectorState();
}

class _AkTimePeriodSelectorState extends State<AkTimePeriodSelector>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  String? _pressedPeriodId;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
        crossAxisSpacing: widget.spacing,
        mainAxisSpacing: widget.spacing,
      ),
      itemCount: widget.periods.length,
      itemBuilder: (context, index) {
        final period = widget.periods[index];
        final isSelected = period.id == widget.selectedPeriodId;
        final isPressed = period.id == _pressedPeriodId;

        return AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: isPressed ? _scaleAnimation.value : 1.0,
              child: _buildPeriodCard(period, isSelected),
            );
          },
        );
      },
    );
  }

  /// بناء بطاقة الفترة الزمنية
  Widget _buildPeriodCard(TimePeriod period, bool isSelected) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // ألوان البطاقة حسب الحالة - استخدام الألوان الديناميكية
    final backgroundColor = isSelected
        ? Theme.of(context).primaryColor
        : (isDark ? AppColors.darkSurface : AppColors.lightSurface);

    final iconColor = isSelected
        ? AppColors.white
        : (isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary);

    final textColor = isSelected
        ? AppColors.white
        : (isDark ? AppColors.darkTextSecondary : AppColors.lightTextSecondary);

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
        border: isSelected
            ? Border.all(
                color: Theme.of(context).primaryColor,
                width: 2,
              )
            : Border.all(
                color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
                width: 1,
              ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : [
                BoxShadow(
                  color: isDark ? AppColors.darkShadow : AppColors.lightShadow,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handlePeriodTap(period),
          onTapDown: (_) => _handleTapDown(period.id),
          onTapUp: (_) => _handleTapUp(),
          onTapCancel: _handleTapUp,
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          child: Padding(
            padding: AppDimensions.cardPaddingMedium,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // الأيقونة
                Icon(
                  period.icon,
                  size: AppDimensions.iconSizeLarge,
                  color: iconColor,
                ),

                if (widget.showText) ...[
                  SizedBox(height: AppDimensions.smallSpacing),

                  // النص
                  Text(
                    period.name,
                    style: AppTypography(
                      fontSize: AppTypography.fontSizeSmall,
                      fontWeight: isSelected
                          ? AppTypography.weightSemiBold
                          : AppTypography.weightRegular,
                      color: textColor,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// معالجة الضغط على الفترة
  void _handlePeriodTap(TimePeriod period) {
    if (widget.onPeriodSelected != null) {
      widget.onPeriodSelected!(period);
    }
  }

  /// معالجة بداية الضغط
  void _handleTapDown(String periodId) {
    setState(() {
      _pressedPeriodId = periodId;
    });
    _animationController.forward();
  }

  /// معالجة نهاية الضغط
  void _handleTapUp() {
    setState(() {
      _pressedPeriodId = null;
    });
    _animationController.reverse();
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🛠️ الدوال المساعدة والمُنشئات السريعة
// ═══════════════════════════════════════════════════════════════════════════════

/// مجموعة من الدوال المساعدة لإنشاء الأزرار بسرعة
class AkButtons {
  AkButtons._(); // منع إنشاء كائنات من هذه الفئة

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال إنشاء سريعة للأزرار الشائعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// إنشاء زر حفظ سريع
  static Widget save({
    required VoidCallback? onPressed,
    String text = 'حفظ',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
    bool isFullWidth = false,
  }) {
    return AkSaveButton(
      onPressed: onPressed,
      text: text,
      isLoading: isLoading,
      size: size,
      isFullWidth: isFullWidth,
    );
  }

  /// إنشاء زر حذف سريع
  static Widget delete({
    required VoidCallback? onPressed,
    String text = 'حذف',
    bool isLoading = false,
    bool requireConfirmation = true,
    String confirmationMessage = 'هل أنت متأكد من الحذف؟',
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkDeleteButton(
      onPressed: onPressed,
      text: text,
      isLoading: isLoading,
      requireConfirmation: requireConfirmation,
      confirmationMessage: confirmationMessage,
      size: size,
    );
  }

  /// إنشاء زر إلغاء سريع
  static Widget cancel({
    VoidCallback? onPressed,
    String text = 'إلغاء',
    bool autoClose = true,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkCancelButton(
      onPressed: onPressed,
      text: text,
      autoClose: autoClose,
      size: size,
    );
  }

  /// إنشاء زر إضافة سريع
  static Widget add({
    required VoidCallback? onPressed,
    String text = 'إضافة',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
    bool isFullWidth = false,
  }) {
    return AkAddButton(
      onPressed: onPressed,
      text: text,
      isLoading: isLoading,
      size: size,
      isFullWidth: isFullWidth,
    );
  }

  /// إنشاء زر عائم سريع
  static Widget floating({
    required IconData icon,
    required VoidCallback? onPressed,
    AkButtonType type = AkButtonType.primary,
    String? tooltip,
    bool mini = false,
    bool isLoading = false,
  }) {
    return AkFloatingButton(
      icon: icon,
      onPressed: onPressed,
      type: type,
      tooltip: tooltip,
      mini: mini,
      isLoading: isLoading,
    );
  }

  /// إنشاء زر نص سريع
  static Widget text({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    AkButtonType type = AkButtonType.primary,
    AkButtonSize size = AkButtonSize.medium,
    bool isLoading = false,
  }) {
    return AkTextButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      type: type,
      size: size,
      isLoading: isLoading,
    );
  }

  /// إنشاء زر تعديل سريع
  static Widget edit({
    required VoidCallback? onPressed,
    String text = 'تعديل',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkEditButton(
      onPressed: onPressed,
      text: text,
      isLoading: isLoading,
      size: size,
    );
  }

  /// إنشاء زر رجوع سريع
  static Widget back({
    VoidCallback? onPressed,
    String text = 'رجوع',
    bool autoClose = true,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkBackButton(
      onPressed: onPressed,
      text: text,
      autoClose: autoClose,
      size: size,
    );
  }

  /// إنشاء زر التالي سريع
  static Widget next({
    required VoidCallback? onPressed,
    String text = 'التالي',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
    bool isFullWidth = false,
  }) {
    return AkNextButton(
      onPressed: onPressed,
      text: text,
      isLoading: isLoading,
      size: size,
      isFullWidth: isFullWidth,
    );
  }

  /// إنشاء زر تبديل سريع
  static Widget toggle({
    required bool isToggled,
    required ValueChanged<bool>? onToggle,
    String activeText = 'مفعل',
    String inactiveText = 'معطل',
    IconData? activeIcon,
    IconData? inactiveIcon,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkToggleButton(
      isToggled: isToggled,
      onToggle: onToggle,
      activeText: activeText,
      inactiveText: inactiveText,
      activeIcon: activeIcon,
      inactiveIcon: inactiveIcon,
      size: size,
    );
  }

  /// إنشاء مكون الفترات الزمنية سريع
  static Widget timePeriodSelector({
    required List<TimePeriod> periods,
    String? selectedPeriodId,
    ValueChanged<TimePeriod>? onPeriodSelected,
    int crossAxisCount = 4,
    double childAspectRatio = 1.0,
    double spacing = 12.0,
    bool showText = true,
  }) {
    return AkTimePeriodSelector(
      periods: periods,
      selectedPeriodId: selectedPeriodId,
      onPeriodSelected: onPeriodSelected,
      crossAxisCount: crossAxisCount,
      childAspectRatio: childAspectRatio,
      spacing: spacing,
      showText: showText,
    );
  }

  /// إنشاء فترات زمنية افتراضية للتطبيق التجاري
  static List<TimePeriod> getDefaultTimePeriods() {
    return [
      const TimePeriod(
        id: 'daily',
        name: 'يومي',
        icon: Icons.today,
      ),
      const TimePeriod(
        id: 'weekly',
        name: 'أسبوعي',
        icon: Icons.view_week,
      ),
      const TimePeriod(
        id: 'monthly',
        name: 'شهري',
        icon: Icons.calendar_month,
      ),
      const TimePeriod(
        id: 'yearly',
        name: 'سنوي',
        icon: Icons.date_range,
      ),
    ];
  }

  /// إنشاء زر إعادة المحاولة سريع
  static Widget retry({
    required VoidCallback? onPressed,
    String text = 'إعادة المحاولة',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.refresh,
      type: AkButtonType.primary,
      size: size,
      isLoading: isLoading,
      tooltip: 'إعادة المحاولة',
    );
  }

  /// إنشاء زر أساسي سريع
  static Widget primary({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
    bool isFullWidth = false,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      type: AkButtonType.primary,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال إنشاء مجموعات أزرار شائعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// إنشاء مجموعة أزرار حفظ وإلغاء
  static Widget saveCancel({
    required VoidCallback? onSave,
    VoidCallback? onCancel,
    String saveText = 'حفظ',
    String cancelText = 'إلغاء',
    bool isLoading = false,
    bool autoClose = true,
    MainAxisAlignment alignment = MainAxisAlignment.spaceBetween,
  }) {
    return Row(
      mainAxisAlignment: alignment,
      children: [
        AkCancelButton(
          onPressed: onCancel,
          text: cancelText,
          autoClose: autoClose,
        ),
        SizedBox(width: AppDimensions.defaultMargin),
        AkSaveButton(
          onPressed: onSave,
          text: saveText,
          isLoading: isLoading,
        ),
      ],
    );
  }

  /// إنشاء مجموعة أزرار تعديل وحذف
  static Widget editDelete({
    VoidCallback? onEdit,
    VoidCallback? onDelete,
    bool requireDeleteConfirmation = true,
    String deleteConfirmationMessage = 'هل أنت متأكد من الحذف؟',
    AkButtonSize size = AkButtonSize.small,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        AkIconButton(
          icon: Icons.edit,
          onPressed: onEdit,
          type: AkButtonType.info,
          size: size,
          tooltip: 'تعديل',
          showBackground: false,
        ),
        SizedBox(width: AppDimensions.smallMargin),
        AkIconButton(
          icon: Icons.delete,
          onPressed: requireDeleteConfirmation && onDelete != null
              ? () =>
                  _showDeleteConfirmation(onDelete, deleteConfirmationMessage)
              : onDelete,
          type: AkButtonType.danger,
          size: size,
          tooltip: 'حذف',
          showBackground: false,
        ),
      ],
    );
  }

  /// عرض حوار تأكيد الحذف (دالة مساعدة خاصة)
  static Future<void> _showDeleteConfirmation(
    VoidCallback onDelete,
    String message,
  ) async {
    // هذه دالة مساعدة ستحتاج إلى context
    // في الاستخدام الفعلي، يجب تمرير context أو استخدام AkDeleteButton مباشرة
    onDelete();
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📚 أمثلة الاستخدام والتوثيق
// ═══════════════════════════════════════════════════════════════════════════════

// ═══════════════════════════════════════════════════════════════════════════════
// ● أزرار AppBar المخصصة
// ═══════════════════════════════════════════════════════════════════════════════

/// زر أيقونة مخصص لـ AppBar مع تكيف كامل للثيمات
///
/// **المميزات:**
/// - تكيف تلقائي مع الوضع المظلم/الفاتح
/// - ألوان ديناميكية حسب خلفية AppBar
/// - تأثيرات تفاعلية محسنة
/// - دعم التلميحات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkAppBarIconButton(
///   icon: Icons.search,
///   onPressed: () => openSearch(),
///   tooltip: 'البحث',
/// )
/// ```
class AkAppBarIconButton extends StatelessWidget {
  /// أيقونة الزر
  final IconData icon;

  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// نص التلميح
  final String? tooltip;

  /// لون مخصص للأيقونة
  final Color? color;

  /// حجم الأيقونة
  final double? size;

  const AkAppBarIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.tooltip,
    this.color,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = DynamicColors.isDarkMode(context);

    // تحديد اللون التلقائي حسب الثيم
    final effectiveColor = color ??
        (isDark
            ? DynamicColors.onSurface(context)
            : DynamicColors.onSurface(context));

    return IconButton(
      icon: Icon(icon),
      onPressed: onPressed,
      tooltip: tooltip,
      color: effectiveColor,
      iconSize: size ?? AppDimensions.iconSizeMedium,
      splashRadius: AppDimensions.iconSizeMedium,
      padding: const EdgeInsets.all(AppDimensions.spacing8),
    );
  }
}

/// زر بحث مخصص لـ AppBar مع حالات متعددة
///
/// **المميزات:**
/// - تبديل تلقائي بين أيقونة البحث والإغلاق
/// - ألوان ديناميكية
/// - تأثيرات انتقالية سلسة
///
/// **مثال الاستخدام:**
/// ```dart
/// AkAppBarSearchButton(
///   isSearchActive: _showSearchField,
///   onPressed: () => toggleSearch(),
/// )
/// ```
class AkAppBarSearchButton extends StatelessWidget {
  /// هل البحث نشط
  final bool isSearchActive;

  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// لون مخصص
  final Color? color;

  const AkAppBarSearchButton({
    super.key,
    required this.isSearchActive,
    this.onPressed,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return AkAppBarIconButton(
      icon: isSearchActive ? Icons.close : Icons.search,
      onPressed: onPressed,
      tooltip: isSearchActive ? 'إغلاق البحث' : 'البحث',
      color: color,
    );
  }
}

/// زر تبديل العرض مخصص لـ AppBar
///
/// **المميزات:**
/// - تبديل تلقائي بين عرض القائمة والشبكة
/// - أيقونات واضحة ومفهومة
/// - ألوان ديناميكية
///
/// **مثال الاستخدام:**
/// ```dart
/// AkAppBarViewToggleButton(
///   isGridView: _isGridView,
///   onPressed: () => toggleViewMode(),
/// )
/// ```
class AkAppBarViewToggleButton extends StatelessWidget {
  /// هل العرض الحالي شبكة
  final bool isGridView;

  /// دالة تنفذ عند الضغط
  final VoidCallback? onPressed;

  /// لون مخصص
  final Color? color;

  const AkAppBarViewToggleButton({
    super.key,
    required this.isGridView,
    this.onPressed,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return AkAppBarIconButton(
      icon: isGridView ? Icons.view_list : Icons.grid_view,
      onPressed: onPressed,
      tooltip: isGridView ? 'عرض كقائمة' : 'عرض كشبكة',
      color: color,
    );
  }
}

/// مجموعة أزرار AppBar الجاهزة للاستخدام
///
/// **المميزات:**
/// - أزرار محددة مسبقاً للاستخدامات الشائعة
/// - تصميم موحد ومتناسق
/// - ألوان ديناميكية
///
/// **مثال الاستخدام:**
/// ```dart
/// AkAppBarButtons.filter(onPressed: () => showFilter())
/// AkAppBarButtons.categories(onPressed: () => showCategories())
/// AkAppBarButtons.reports(onPressed: () => showReports())
/// ```
class AkAppBarButtons {
  /// زر الفلترة
  static Widget filter({
    VoidCallback? onPressed,
    Color? color,
  }) {
    return AkAppBarIconButton(
      icon: Icons.filter_list,
      onPressed: onPressed,
      tooltip: 'فلترة',
      color: color,
    );
  }

  /// زر إدارة الفئات
  static Widget categories({
    VoidCallback? onPressed,
    Color? color,
  }) {
    return AkAppBarIconButton(
      icon: Icons.category_outlined,
      onPressed: onPressed,
      tooltip: 'إدارة الفئات',
      color: color,
    );
  }

  /// زر التقارير
  static Widget reports({
    VoidCallback? onPressed,
    Color? color,
  }) {
    return AkAppBarIconButton(
      icon: Icons.bar_chart,
      onPressed: onPressed,
      tooltip: 'التقارير',
      color: color,
    );
  }

  /// زر الإعدادات
  static Widget settings({
    VoidCallback? onPressed,
    Color? color,
  }) {
    return AkAppBarIconButton(
      icon: Icons.settings,
      onPressed: onPressed,
      tooltip: 'الإعدادات',
      color: color,
    );
  }

  /// زر الإشعارات
  static Widget notifications({
    VoidCallback? onPressed,
    Color? color,
    int? badgeCount,
  }) {
    return AkAppBarIconButton(
      icon: badgeCount != null && badgeCount > 0
          ? Icons.notifications_active
          : Icons.notifications_outlined,
      onPressed: onPressed,
      tooltip: 'الإشعارات',
      color: color,
    );
  }

  /// زر المزيد
  static Widget more({
    VoidCallback? onPressed,
    Color? color,
  }) {
    return AkAppBarIconButton(
      icon: Icons.more_vert,
      onPressed: onPressed,
      tooltip: 'المزيد',
      color: color,
    );
  }

  /// زر التحديث
  static Widget refresh({
    VoidCallback? onPressed,
    Color? color,
  }) {
    return AkAppBarIconButton(
      icon: Icons.refresh,
      onPressed: onPressed,
      tooltip: 'تحديث',
      color: color,
    );
  }

  /// زر المساعدة
  static Widget help({
    VoidCallback? onPressed,
    Color? color,
  }) {
    return AkAppBarIconButton(
      icon: Icons.help_outline,
      onPressed: onPressed,
      tooltip: 'المساعدة',
      color: color,
    );
  }
}

/// **📋 دليل الاستخدام الشامل:**
///
/// ```dart
/// // ═══ الأزرار الأساسية ═══
///
/// // زر أساسي بسيط
/// AkButton(
///   text: 'حفظ',
///   onPressed: () => print('تم الحفظ'),
///   type: AkButtonType.primary,
/// )
///
/// // زر مع أيقونة وتحميل
/// AkButton(
///   text: 'حفظ البيانات',
///   icon: Icons.save,
///   onPressed: () => saveData(),
///   isLoading: isLoading,
///   type: AkButtonType.success,
///   size: AkButtonSize.large,
/// )
///
/// // زر أيقونة فقط
/// AkIconButton(
///   icon: Icons.edit,
///   onPressed: () => editItem(),
///   type: AkButtonType.info,
///   tooltip: 'تعديل العنصر',
/// )
///
/// // ═══ أزرار AppBar المخصصة ═══
///
/// // زر بحث ذكي
/// AkAppBarSearchButton(
///   isSearchActive: _showSearchField,
///   onPressed: () => toggleSearch(),
/// )
///
/// // زر تبديل العرض
/// AkAppBarViewToggleButton(
///   isGridView: _isGridView,
///   onPressed: () => toggleViewMode(),
/// )
///
/// // أزرار جاهزة
/// AkAppBarButtons.filter(onPressed: () => showFilter())
/// AkAppBarButtons.categories(onPressed: () => showCategories())
/// AkAppBarButtons.reports(onPressed: () => showReports())
///
/// // ═══ الأزرار المتخصصة ═══
///
/// // زر حفظ متخصص
/// AkSaveButton(
///   onPressed: () => saveData(),
///   isLoading: isLoading,
///   text: 'حفظ البيانات',
/// )
///
/// // زر حذف مع تأكيد
/// AkDeleteButton(
///   onPressed: () => deleteItem(),
///   requireConfirmation: true,
///   confirmationMessage: 'هل تريد حذف هذا العنصر نهائياً؟',
/// )
///
/// // زر إلغاء مع إغلاق تلقائي
/// AkCancelButton(
///   text: 'إلغاء',
///   autoClose: true,
/// )
///
/// // ═══ الأزرار العائمة ═══
///
/// // زر عائم أساسي
/// AkFloatingButton(
///   icon: Icons.add,
///   onPressed: () => addNewItem(),
///   tooltip: 'إضافة عنصر جديد',
/// )
///
/// // زر عائم صغير
/// AkFloatingButton(
///   icon: Icons.edit,
///   onPressed: () => editItem(),
///   type: AkButtonType.info,
///   mini: true,
///   tooltip: 'تعديل سريع',
/// )
///
/// // ═══ الدوال المساعدة ═══
///
/// // إنشاء أزرار سريعة
/// AkButtons.save(
///   onPressed: () => saveData(),
///   isLoading: isLoading,
/// )
///
/// AkButtons.delete(
///   onPressed: () => deleteItem(),
///   requireConfirmation: true,
/// )
///
/// // مجموعة أزرار حفظ وإلغاء
/// AkButtons.saveCancel(
///   onSave: () => saveData(),
///   onCancel: () => Navigator.pop(context),
///   isLoading: isLoading,
/// )
///
/// // مجموعة أزرار تعديل وحذف
/// AkButtons.editDelete(
///   onEdit: () => editItem(),
///   onDelete: () => deleteItem(),
///   requireDeleteConfirmation: true,
/// )
///
/// // ═══ التخصيص المتقدم ═══
///
/// // زر بألوان مخصصة
/// AkButton(
///   text: 'زر مخصص',
///   onPressed: () {},
///   customBackgroundColor: Colors.purple,
///   customTextColor: Colors.white,
///   shape: AkButtonShape.pill,
/// )
///
/// // زر أيقونة بدون خلفية
/// AkIconButton(
///   icon: Icons.favorite,
///   onPressed: () => toggleFavorite(),
///   showBackground: false,
///   customIconColor: Colors.red,
/// )
///
/// // ═══ مكون الفترات الزمنية ═══
///
/// // مكون الفترات الزمنية الأساسي
/// AkTimePeriodSelector(
///   periods: [
///     TimePeriod(id: 'daily', name: 'يومي', icon: Icons.today),
///     TimePeriod(id: 'weekly', name: 'أسبوعي', icon: Icons.view_week),
///     TimePeriod(id: 'monthly', name: 'شهري', icon: Icons.calendar_month),
///     TimePeriod(id: 'yearly', name: 'سنوي', icon: Icons.date_range),
///   ],
///   selectedPeriodId: selectedPeriod,
///   onPeriodSelected: (period) => setState(() => selectedPeriod = period.id),
/// )
///
/// // استخدام الدالة المساعدة السريعة
/// AkButtons.timePeriodSelector(
///   periods: AkButtons.getDefaultTimePeriods(),
///   selectedPeriodId: selectedPeriod,
///   onPeriodSelected: (period) => handlePeriodChange(period),
///   crossAxisCount: 4,
///   spacing: 16.0,
/// )
/// ```

// ═══════════════════════════════════════════════════════════════════════════════
// 💼 القسم السادس: الأزرار المتخصصة للمشروع التجاري اليمني
// ═══════════════════════════════════════════════════════════════════════════════

/// مجموعة من الأزرار المتخصصة للمشروع التجاري اليمني
class AkCommercialButtons {
  AkCommercialButtons._();

  // ───────────────────────────────────────────────────────────────────────────────
  // ● أزرار نقطة البيع (POS)
  // ───────────────────────────────────────────────────────────────────────────────

  /// زر إضافة للسلة
  static Widget addToCart({
    required VoidCallback? onPressed,
    String text = 'إضافة للسلة',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.add_shopping_cart,
      type: AkButtonType.success,
      size: size,
      isLoading: isLoading,
      tooltip: 'إضافة المنتج للسلة',
    );
  }

  /// زر إتمام البيع
  static Widget completeSale({
    required VoidCallback? onPressed,
    String text = 'إتمام البيع',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.large,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.point_of_sale,
      type: AkButtonType.primary,
      size: size,
      isLoading: isLoading,
      isFullWidth: true,
      tooltip: 'إتمام عملية البيع',
    );
  }

  /// زر طباعة الفاتورة
  static Widget printInvoice({
    required VoidCallback? onPressed,
    String text = 'طباعة الفاتورة',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.print,
      type: AkButtonType.info,
      size: size,
      isLoading: isLoading,
      tooltip: 'طباعة الفاتورة',
    );
  }

  /// زر إرسال الفاتورة
  static Widget sendInvoice({
    required VoidCallback? onPressed,
    String text = 'إرسال الفاتورة',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.send,
      type: AkButtonType.primary,
      size: size,
      isLoading: isLoading,
      tooltip: 'إرسال الفاتورة للعميل',
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● أزرار إدارة المخزون
  // ───────────────────────────────────────────────────────────────────────────────

  /// زر إضافة مخزون
  static Widget addStock({
    required VoidCallback? onPressed,
    String text = 'إضافة مخزون',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.add_box,
      type: AkButtonType.success,
      size: size,
      isLoading: isLoading,
      tooltip: 'إضافة كمية للمخزون',
    );
  }

  /// زر تحديث المخزون
  static Widget updateStock({
    required VoidCallback? onPressed,
    String text = 'تحديث المخزون',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.inventory,
      type: AkButtonType.warning,
      size: size,
      isLoading: isLoading,
      tooltip: 'تحديث كمية المخزون',
    );
  }

  /// زر جرد المخزون
  static Widget stockAudit({
    required VoidCallback? onPressed,
    String text = 'جرد المخزون',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.fact_check,
      type: AkButtonType.info,
      size: size,
      isLoading: isLoading,
      tooltip: 'بدء عملية جرد المخزون',
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● أزرار المدفوعات
  // ───────────────────────────────────────────────────────────────────────────────

  /// زر دفع نقدي
  static Widget cashPayment({
    required VoidCallback? onPressed,
    String text = 'دفع نقدي',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.payments,
      type: AkButtonType.success,
      size: size,
      isLoading: isLoading,
      tooltip: 'دفع نقدي',
    );
  }

  /// زر دفع بالبطاقة
  static Widget cardPayment({
    required VoidCallback? onPressed,
    String text = 'دفع بالبطاقة',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.credit_card,
      type: AkButtonType.primary,
      size: size,
      isLoading: isLoading,
      tooltip: 'دفع بالبطاقة الائتمانية',
    );
  }

  /// زر دفع آجل
  static Widget deferredPayment({
    required VoidCallback? onPressed,
    String text = 'دفع آجل',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.schedule,
      type: AkButtonType.warning,
      size: size,
      isLoading: isLoading,
      tooltip: 'دفع آجل (دين)',
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● أزرار التقارير
  // ───────────────────────────────────────────────────────────────────────────────

  /// زر تصدير التقرير
  static Widget exportReport({
    required VoidCallback? onPressed,
    String text = 'تصدير التقرير',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.file_download,
      type: AkButtonType.info,
      size: size,
      isLoading: isLoading,
      tooltip: 'تصدير التقرير كملف',
    );
  }

  /// زر مشاركة التقرير
  static Widget shareReport({
    required VoidCallback? onPressed,
    String text = 'مشاركة التقرير',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.share,
      type: AkButtonType.primary,
      size: size,
      isLoading: isLoading,
      tooltip: 'مشاركة التقرير',
    );
  }

  /// زر طباعة التقرير
  static Widget printReport({
    required VoidCallback? onPressed,
    String text = 'طباعة التقرير',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.print,
      type: AkButtonType.secondary,
      size: size,
      isLoading: isLoading,
      tooltip: 'طباعة التقرير',
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● أزرار إدارة العملاء
  // ───────────────────────────────────────────────────────────────────────────────

  /// زر إضافة عميل جديد
  static Widget addCustomer({
    required VoidCallback? onPressed,
    String text = 'إضافة عميل',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.person_add,
      type: AkButtonType.success,
      size: size,
      isLoading: isLoading,
      tooltip: 'إضافة عميل جديد',
    );
  }

  /// زر عرض تاريخ العميل
  static Widget customerHistory({
    required VoidCallback? onPressed,
    String text = 'تاريخ العميل',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.medium,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.history,
      type: AkButtonType.info,
      size: size,
      isLoading: isLoading,
      tooltip: 'عرض تاريخ مشتريات العميل',
    );
  }

  /// زر اتصال بالعميل
  static Widget contactCustomer({
    required VoidCallback? onPressed,
    String text = 'اتصال',
    bool isLoading = false,
    AkButtonSize size = AkButtonSize.small,
  }) {
    return AkButton(
      text: text,
      onPressed: onPressed,
      icon: Icons.phone,
      type: AkButtonType.primary,
      size: size,
      isLoading: isLoading,
      tooltip: 'الاتصال بالعميل',
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● مجموعات أزرار متخصصة
  // ───────────────────────────────────────────────────────────────────────────────

  /// مجموعة أزرار نقطة البيع
  static Widget posActionButtons({
    VoidCallback? onAddToCart,
    VoidCallback? onCompleteSale,
    VoidCallback? onPrintInvoice,
    bool isLoading = false,
  }) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: addToCart(
                onPressed: onAddToCart,
                isLoading: isLoading,
              ),
            ),
            SizedBox(width: AppDimensions.smallMargin),
            printInvoice(
              onPressed: onPrintInvoice,
              size: AkButtonSize.medium,
            ),
          ],
        ),
        SizedBox(height: AppDimensions.smallMargin),
        completeSale(
          onPressed: onCompleteSale,
          isLoading: isLoading,
        ),
      ],
    );
  }

  /// مجموعة أزرار المدفوعات
  static Widget paymentButtons({
    VoidCallback? onCashPayment,
    VoidCallback? onCardPayment,
    VoidCallback? onDeferredPayment,
    bool isLoading = false,
  }) {
    return Row(
      children: [
        Expanded(
          child: cashPayment(
            onPressed: onCashPayment,
            isLoading: isLoading,
          ),
        ),
        SizedBox(width: AppDimensions.smallMargin),
        Expanded(
          child: cardPayment(
            onPressed: onCardPayment,
            isLoading: isLoading,
          ),
        ),
        SizedBox(width: AppDimensions.smallMargin),
        Expanded(
          child: deferredPayment(
            onPressed: onDeferredPayment,
            isLoading: isLoading,
          ),
        ),
      ],
    );
  }

  /// مجموعة أزرار التقارير
  static Widget reportButtons({
    VoidCallback? onExport,
    VoidCallback? onShare,
    VoidCallback? onPrint,
    bool isLoading = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        exportReport(
          onPressed: onExport,
          isLoading: isLoading,
          size: AkButtonSize.small,
        ),
        shareReport(
          onPressed: onShare,
          isLoading: isLoading,
          size: AkButtonSize.small,
        ),
        printReport(
          onPressed: onPrint,
          isLoading: isLoading,
          size: AkButtonSize.small,
        ),
      ],
    );
  }
}
