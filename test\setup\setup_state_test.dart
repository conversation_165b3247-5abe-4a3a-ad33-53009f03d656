import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/features/setup/models/setup_state.dart';

void main() {
  group('SetupState Tests', () {
    late SetupState setupState;

    setUp(() {
      setupState = SetupState();
    });

    test('initial state should be correct', () {
      expect(
          setupState.currentStep, equals(SetupStep.checkingExistingDatabase));
      expect(setupState.step, equals(SetupStep.checkingExistingDatabase));
      expect(setupState.progress, equals(0.0));
      expect(setupState.message,
          equals('جاري التحقق من قاعدة البيانات الحالية...'));
      expect(setupState.error, isNull);
    });

    test('updateState should update state correctly', () {
      setupState.updateState(
        step: SetupStep.creatingDatabase,
        progress: 0.3,
        message: 'جاري إنشاء قاعدة البيانات...',
      );

      expect(setupState.currentStep, equals(SetupStep.creatingDatabase));
      expect(setupState.step, equals(SetupStep.creatingDatabase));
      expect(setupState.progress, equals(0.3));
      expect(setupState.message, equals('جاري إنشاء قاعدة البيانات...'));
      expect(setupState.error, isNull);
    });

    test('updateState with error should set error state correctly', () {
      setupState.updateState(
        step: SetupStep.error,
        progress: 0.5,
        message: 'حدث خطأ أثناء الإعداد',
        error: 'تفاصيل الخطأ',
      );

      expect(setupState.currentStep, equals(SetupStep.error));
      expect(setupState.step, equals(SetupStep.error));
      expect(setupState.progress, equals(0.5));
      expect(setupState.message, equals('حدث خطأ أثناء الإعداد'));
      expect(setupState.error, equals('تفاصيل الخطأ'));
    });

    test('updateState with completed should set completed state correctly', () {
      setupState.updateState(
        step: SetupStep.completed,
        progress: 1.0,
        message: 'تم الإعداد بنجاح',
      );

      expect(setupState.currentStep, equals(SetupStep.completed));
      expect(setupState.step, equals(SetupStep.completed));
      expect(setupState.progress, equals(1.0));
      expect(setupState.message, equals('تم الإعداد بنجاح'));
      expect(setupState.error, isNull);
    });

    test('isStepCompleted should return correct value', () {
      // تهيئة الحالة
      setupState.updateState(
        step: SetupStep.creatingDatabase,
        progress: 0.3,
        message: 'جاري إنشاء قاعدة البيانات...',
      );

      // الانتقال إلى خطوة أخرى لإكمال الخطوة السابقة
      setupState.updateState(
        step: SetupStep.creatingTables,
        progress: 0.5,
        message: 'جاري إنشاء الجداول...',
      );

      // التحقق من أن الخطوة السابقة مكتملة
      expect(setupState.isStepCompleted(SetupStep.creatingDatabase), isTrue);
      expect(setupState.isStepCompleted(SetupStep.creatingTables), isFalse);
    });

    test('isStepFailed should return correct value', () {
      // تهيئة الحالة مع خطأ
      setupState.updateState(
        step: SetupStep.error,
        progress: 0.3,
        message: 'حدث خطأ أثناء إنشاء قاعدة البيانات',
        error: 'تفاصيل الخطأ',
      );

      // التحقق من أن الخطوة فاشلة
      expect(setupState.isStepFailed(SetupStep.error), isTrue);
      expect(setupState.isStepFailed(SetupStep.creatingDatabase), isFalse);
    });
  });
}
