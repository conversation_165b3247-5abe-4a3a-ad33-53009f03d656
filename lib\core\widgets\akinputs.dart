import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
//import 'dart:async';
//import 'dart:math' as math;
import '../theme/index.dart';
import '../utils/index.dart';

// ═══════════════════════════════════════════════════════════════════════════════
// 🎯 نظام حقول الإدخال الموحد والشامل (AK Inputs System)
// ═══════════════════════════════════════════════════════════════════════════════

/// **📋 فهرس الحقول:**
///
/// **🔢 القسم الأول: الحقول الرقمية** (الأسطر 50-800)
/// ├── 1. AkCurrencyInput         - حقل المبالغ المالية مع تنسيق العملة
/// ├── 2. AkPercentageInput       - حقل النسب المئوية (0-100%)
/// └── 3. AkNumericInput          - حقل الأرقام العامة مع نطاق مخصص
///
/// **📞 القسم الثاني: حقول الاتصال** (الأسطر 800-1400)
/// ├── 4. AkPhoneInput            - حقل أرقام الهاتف اليمني مع التنسيق التلقائي
/// └── 5. AkEmailInput            - حقل البريد الإلكتروني مع التحقق المتقدم
///
/// **🔐 القسم الثالث: حقول الأمان** (الأسطر 1400-1800)
/// └── 6. AkPasswordInput         - حقل كلمة المرور مع مؤشر القوة
///
/// **📝 القسم الرابع: حقول النصوص المتخصصة** (الأسطر 1800-2400)
/// ├── 7. AkLongTextInput         - حقل النصوص الطويلة للملاحظات
/// └── 8. AkSearchInput           - حقل البحث مع الاقتراحات التلقائية
///
/// **🔤 القسم الخامس: حقول الرموز والملفات** (الأسطر 2400-3000)
/// ├── 9. AkCodeInput             - حقل الرموز والباركود مع دعم المسح
/// └── 10. AkFileInput            - حقل رفع الملفات مع المعاينة
///
/// **📋 القسم السادس: حقول الاختيار المتقدمة** (الأسطر 3000-3800)
/// ├── 11. AkDropdownInput        - قائمة منسدلة محسنة مع البحث
/// └── 12. AkDateInput            - حقل التاريخ مع منتقي التاريخ المحلي
///
/// **🎯 أمثلة الاستخدام:**
/// ```dart
/// // حقل مبلغ مالي
/// AkCurrencyInput(
///   label: 'السعر',
///   controller: priceController,
///   currencySymbol: 'ر.ي',
///   isRequired: true,
/// )
///
/// // حقل رقم هاتف يمني
/// AkPhoneInput(
///   label: 'رقم الهاتف',
///   controller: phoneController,
///   defaultCountryCode: '+967',
/// )
///
/// // حقل البريد الإلكتروني
/// AkEmailInput(
///   label: 'البريد الإلكتروني',
///   controller: emailController,
///   showSuggestions: true,
/// )
/// ```

// ═══════════════════════════════════════════════════════════════════════════════
// 🔧 الأنواع والثوابت
// ═══════════════════════════════════════════════════════════════════════════════

/// أنواع الرموز المدعومة
enum AkCodeType {
  /// باركود عادي
  barcode,

  /// رمز QR
  qrCode,

  /// رمز مخصص
  custom,
}

/// أنواع حقول التاريخ
enum AkDateType {
  /// تاريخ واحد
  single,

  /// نطاق تاريخ
  range,

  /// تاريخ ووقت
  dateTime,
}

/// أنواع الملفات المدعومة
enum AkFileType {
  /// صور
  image,

  /// مستندات
  document,

  /// جميع الأنواع
  any,
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📝 القسم الأساسي: حقل النص العام (AkTextInput)
// ═══════════════════════════════════════════════════════════════════════════════

/// حقل إدخال النص العام الموحد مع الدعم الكامل للوضع المظلم/الفاتح
///
/// **المميزات:**
/// - تصميم موحد لجميع حقول النص
/// - دعم كامل للوضع المظلم والفاتح
/// - تكامل مع نظام الثيمات
/// - تحقق مرن من صحة البيانات
/// - دعم الأيقونات والتلميحات
/// - تحميل كسول للعناصر الثقيلة
///
/// **مثال الاستخدام:**
/// ```dart
/// AkTextInput(
///   label: 'اسم المنتج',
///   controller: nameController,
///   hint: 'أدخل اسم المنتج',
///   prefixIcon: Icons.inventory,
///   isRequired: true,
/// )
/// ```
class AkTextInput extends StatefulWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// نوع لوحة المفاتيح
  final TextInputType keyboardType;

  /// عدد الأسطر الأقصى
  final int? maxLines;

  /// عدد الأحرف الأقصى
  final int? maxLength;

  /// دالة التحقق المخصصة
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<String>? onChanged;

  /// دالة تنفذ عند إرسال النص
  final ValueChanged<String>? onSubmitted;

  /// هل الحقل للقراءة فقط
  final bool readOnly;

  /// أيقونة البداية
  final IconData? prefixIcon;

  /// أيقونة النهاية
  final IconData? suffixIcon;

  /// دالة تنفذ عند الضغط على أيقونة النهاية
  final VoidCallback? onSuffixIconPressed;

  /// نص البداية
  final String? prefixText;

  /// نص النهاية
  final String? suffixText;

  /// هل يتم إخفاء النص (لكلمات المرور)
  final bool obscureText;

  /// هل يتم تفعيل التصحيح التلقائي
  final bool autocorrect;

  /// هل يتم تفعيل الاقتراحات
  final bool enableSuggestions;

  /// محاذاة النص
  final TextAlign textAlign;

  /// قائمة منسقات الإدخال
  final List<TextInputFormatter>? inputFormatters;

  /// عمل الإدخال (مثل done, next, search)
  final TextInputAction? textInputAction;

  /// دالة تنفذ عند الضغط على زر العمل
  final VoidCallback? onEditingComplete;

  /// هل يتم التركيز تلقائياً
  final bool autofocus;

  /// عقدة التركيز
  final FocusNode? focusNode;

  const AkTextInput({
    super.key,
    this.controller,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.keyboardType = TextInputType.text,
    this.maxLines = 1,
    this.maxLength,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.readOnly = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
    this.prefixText,
    this.suffixText,
    this.obscureText = false,
    this.autocorrect = true,
    this.enableSuggestions = true,
    this.textAlign = TextAlign.start,
    this.inputFormatters,
    this.textInputAction,
    this.onEditingComplete,
    this.autofocus = false,
    this.focusNode,
  });

  @override
  State<AkTextInput> createState() => _AkTextInputState();
}

class _AkTextInputState extends State<AkTextInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _obscureText = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();
    _obscureText = widget.obscureText;
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// التحقق من صحة النص باستخدام الدوال المساعدة
  String? _validateText(String? value) {
    // استخدام دالة التحقق المطلوب من النظام المساعد
    if (widget.isRequired) {
      final requiredValidation = Validators.required(widget.label)(value);
      if (requiredValidation != null) return requiredValidation;
    }

    return widget.validator?.call(value);
  }

  /// تبديل إظهار/إخفاء النص
  void _toggleObscureText() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return TextFormField(
      controller: _controller,
      focusNode: _focusNode,
      readOnly: widget.readOnly,
      keyboardType: widget.keyboardType,
      maxLines: widget.obscureText ? 1 : widget.maxLines,
      maxLength: widget.maxLength,
      obscureText: _obscureText,
      autocorrect: widget.autocorrect,
      enableSuggestions: widget.enableSuggestions,
      textAlign: widget.textAlign,
      inputFormatters: widget.inputFormatters,
      textInputAction: widget.textInputAction,
      autofocus: widget.autofocus,
      style: AppTypography.createCustomStyle(
        fontSize: AppTypography.fontSizeMedium,
        fontWeight: AppTypography.weightRegular,
        color: isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
      ),
      decoration: InputDecoration(
        labelText: widget.label,
        hintText: widget.hint,
        filled: true,
        fillColor: isDark
            ? AppColors.darkSurfaceVariant
            : AppColors.lightSurfaceVariant,
        prefixIcon: widget.prefixIcon != null
            ? Icon(
                widget.prefixIcon,
                size: AppDimensions.mediumIconSize,
                color: isDark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              )
            : null,
        suffixIcon: _buildSuffixIcon(isDark),
        prefixText: widget.prefixText,
        suffixText: widget.suffixText,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppDimensions.defaultMargin,
          vertical: AppDimensions.smallMargin,
        ),
        labelStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
        hintStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
        ),
        counterStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeSmall,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
      ),
      validator: _validateText,
      onChanged: widget.onChanged,
      onFieldSubmitted: widget.onSubmitted,
      onEditingComplete: widget.onEditingComplete,
      // حماية من خطأ Flutter framework مع مفاتيح الأسهم
      onTapOutside: (event) {
        // إزالة التركيز عند النقر خارج الحقل لتجنب خطأ VerticalCaretMovementRun
        FocusScope.of(context).unfocus();
      },
    );
  }

  /// بناء أيقونة النهاية
  Widget? _buildSuffixIcon(bool isDark) {
    // إذا كان حقل كلمة مرور، أضف زر إظهار/إخفاء
    if (widget.obscureText) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility : Icons.visibility_off,
          size: AppDimensions.mediumIconSize,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
        onPressed: _toggleObscureText,
        tooltip: _obscureText ? 'إظهار كلمة المرور' : 'إخفاء كلمة المرور',
      );
    }

    // إذا كان هناك أيقونة مخصصة
    if (widget.suffixIcon != null) {
      return IconButton(
        icon: Icon(
          widget.suffixIcon,
          size: AppDimensions.mediumIconSize,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
        onPressed: widget.onSuffixIconPressed,
      );
    }

    return null;
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// � القسم الثاني: حقول الأمان (AkPasswordInput)
// ═══════════════════════════════════════════════════════════════════════════════

/// حقل إدخال كلمة المرور مع مؤشر القوة والدعم الكامل للوضع المظلم/الفاتح
///
/// **المميزات:**
/// - إخفاء/إظهار كلمة المرور
/// - مؤشر قوة كلمة المرور
/// - تحقق من صحة كلمة المرور
/// - دعم كامل للوضع المظلم والفاتح
/// - تكامل مع نظام الثيمات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkPasswordInput(
///   controller: passwordController,
///   label: 'كلمة المرور',
///   hint: 'أدخل كلمة المرور',
///   showStrengthIndicator: true,
///   isRequired: true,
/// )
/// ```
class AkPasswordInput extends StatefulWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// هل يتم عرض مؤشر قوة كلمة المرور
  final bool showStrengthIndicator;

  /// دالة التحقق المخصصة
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<String>? onChanged;

  /// هل الحقل للقراءة فقط
  final bool readOnly;

  /// عقدة التركيز
  final FocusNode? focusNode;

  /// هل يتم التركيز تلقائياً
  final bool autofocus;

  const AkPasswordInput({
    super.key,
    this.controller,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.showStrengthIndicator = false,
    this.validator,
    this.onChanged,
    this.readOnly = false,
    this.focusNode,
    this.autofocus = false,
  });

  @override
  State<AkPasswordInput> createState() => _AkPasswordInputState();
}

class _AkPasswordInputState extends State<AkPasswordInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _obscureText = true;
  double _passwordStrength = 0.0;
  String _strengthText = '';
  Color _strengthColor = AppColors.error;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();

    if (widget.showStrengthIndicator) {
      _controller.addListener(_updatePasswordStrength);
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// تحديث مؤشر قوة كلمة المرور
  void _updatePasswordStrength() {
    final password = _controller.text;
    double strength = 0.0;
    String strengthText = '';
    Color strengthColor = AppColors.error;

    if (password.isEmpty) {
      strength = 0.0;
      strengthText = '';
    } else if (password.length < 6) {
      strength = 0.2;
      strengthText = 'ضعيفة جداً';
      strengthColor = AppColors.error;
    } else if (password.length < 8) {
      strength = 0.4;
      strengthText = 'ضعيفة';
      strengthColor = AppColors.warning;
    } else {
      // فحص معايير القوة
      bool hasUppercase = password.contains(RegExp(r'[A-Z]'));
      bool hasLowercase = password.contains(RegExp(r'[a-z]'));
      bool hasDigits = password.contains(RegExp(r'[0-9]'));
      bool hasSpecialCharacters =
          password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

      int criteriaCount = 0;
      if (hasUppercase) criteriaCount++;
      if (hasLowercase) criteriaCount++;
      if (hasDigits) criteriaCount++;
      if (hasSpecialCharacters) criteriaCount++;

      if (criteriaCount >= 3 && password.length >= 12) {
        strength = 1.0;
        strengthText = 'قوية جداً';
        strengthColor = AppColors.success;
      } else if (criteriaCount >= 2 && password.length >= 10) {
        strength = 0.8;
        strengthText = 'قوية';
        strengthColor = AppColors.success;
      } else if (criteriaCount >= 2) {
        strength = 0.6;
        strengthText = 'متوسطة';
        strengthColor = AppColors.info;
      } else {
        strength = 0.4;
        strengthText = 'ضعيفة';
        strengthColor = AppColors.warning;
      }
    }

    setState(() {
      _passwordStrength = strength;
      _strengthText = strengthText;
      _strengthColor = strengthColor;
    });
  }

  /// التحقق من صحة كلمة المرور باستخدام الدوال المساعدة
  String? _validatePassword(String? value) {
    // استخدام دالة التحقق المطلوب من النظام المساعد
    if (widget.isRequired) {
      final requiredValidation = Validators.required(widget.label)(value);
      if (requiredValidation != null) return requiredValidation;
    }

    if (value != null && value.isNotEmpty) {
      // التحقق من الحد الأدنى لطول كلمة المرور
      if (value.length < 6) {
        return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      }
    }

    return widget.validator?.call(value);
  }

  /// تبديل إظهار/إخفاء كلمة المرور
  void _toggleObscureText() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controller,
          focusNode: _focusNode,
          readOnly: widget.readOnly,
          obscureText: _obscureText,
          autofocus: widget.autofocus,
          style: AppTypography.createCustomStyle(
            fontSize: AppTypography.fontSizeMedium,
            fontWeight: AppTypography.weightRegular,
            color:
                isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
          ),
          decoration: InputDecoration(
            labelText: widget.label,
            hintText: widget.hint ?? 'أدخل كلمة المرور',
            filled: true,
            fillColor: isDark
                ? AppColors.darkSurfaceVariant
                : AppColors.lightSurfaceVariant,
            prefixIcon: Icon(
              Icons.lock,
              size: AppDimensions.mediumIconSize,
              color: isDark
                  ? AppColors.darkTextSecondary
                  : AppColors.lightTextSecondary,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscureText ? Icons.visibility : Icons.visibility_off,
                size: AppDimensions.mediumIconSize,
                color: isDark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              ),
              onPressed: _toggleObscureText,
              tooltip: _obscureText ? 'إظهار كلمة المرور' : 'إخفاء كلمة المرور',
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
              borderSide: BorderSide(
                color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
              borderSide: BorderSide(
                color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
              borderSide: const BorderSide(color: AppColors.error),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
              borderSide: const BorderSide(color: AppColors.error, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: AppDimensions.defaultMargin,
              vertical: AppDimensions.smallMargin,
            ),
            labelStyle: AppTypography.createCustomStyle(
              fontSize: AppTypography.fontSizeMedium,
              color: isDark
                  ? AppColors.darkTextSecondary
                  : AppColors.lightTextSecondary,
            ),
            hintStyle: AppTypography.createCustomStyle(
              fontSize: AppTypography.fontSizeMedium,
              color: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
            ),
          ),
          validator: _validatePassword,
          onChanged: widget.onChanged,
        ),

        // مؤشر قوة كلمة المرور
        if (widget.showStrengthIndicator && _controller.text.isNotEmpty) ...[
          SizedBox(height: AppDimensions.smallMargin),
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: _passwordStrength,
                  backgroundColor: DynamicColors.surfaceVariant(context),
                  valueColor: AlwaysStoppedAnimation<Color>(_strengthColor),
                  minHeight: 4,
                ),
              ),
              SizedBox(width: AppDimensions.smallMargin),
              Text(
                _strengthText,
                style: AppTypography.createCustomStyle(
                  fontSize: AppTypography.fontSizeSmall,
                  color: _strengthColor,
                  fontWeight: AppTypography.weightMedium,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🔢 القسم الثالث: الحقول الرقمية
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● 1. حقل المبالغ المالية (AkCurrencyInput)
// ───────────────────────────────────────────────────────────────────────────────

/// حقل إدخال المبالغ المالية مع التنسيق التلقائي والدعم الكامل للوضع المظلم/الفاتح
///
/// **المميزات:**
/// - تنسيق تلقائي للمبالغ مع فاصل الآلاف
/// - رمز العملة القابل للتخصيص (ر.ي، $، €، إلخ)
/// - تحقق من صحة المبلغ مع رسائل خطأ واضحة
/// - دعم كامل للوضع المظلم والفاتح
/// - تحميل كسول لمنسق الأرقام
/// - تكامل مع نظام الثيمات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkCurrencyInput(
///   label: 'سعر المنتج',
///   controller: priceController,
///   currencySymbol: 'ر.ي',
///   minAmount: 0.01,
///   maxAmount: 999999.99,
///   isRequired: true,
/// )
/// ```
class AkCurrencyInput extends StatefulWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// رمز العملة (افتراضي: ر.ي - ريال يمني)
  final String currencySymbol;

  /// هل يتم عرض رمز العملة في البداية أم النهاية
  final bool currencyAtStart;

  /// هل يتم عرض فاصل الآلاف
  final bool showThousandsSeparator;

  /// عدد الأرقام العشرية المسموحة
  final int decimalPlaces;

  /// الحد الأدنى للمبلغ
  final double? minAmount;

  /// الحد الأقصى للمبلغ
  final double? maxAmount;

  /// دالة التحقق المخصصة
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<double?>? onChanged;

  /// هل الحقل للقراءة فقط
  final bool readOnly;

  /// أيقونة الحقل
  final IconData? prefixIcon;

  const AkCurrencyInput({
    super.key,
    this.controller,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.currencySymbol = 'ر.ي', // ريال يمني
    this.currencyAtStart = false,
    this.showThousandsSeparator = true,
    this.decimalPlaces = 2,
    this.minAmount,
    this.maxAmount,
    this.validator,
    this.onChanged,
    this.readOnly = false,
    this.prefixIcon,
  });

  @override
  State<AkCurrencyInput> createState() => _AkCurrencyInputState();
}

class _AkCurrencyInputState extends State<AkCurrencyInput> {
  late TextEditingController _controller;
  NumberFormat? _numberFormat; // تحميل كسول للتنسيق

  /// الحصول على منسق الأرقام مع التحميل الكسول
  NumberFormat get numberFormat {
    _numberFormat ??= NumberFormat('#,##0.00', 'ar');
    return _numberFormat!;
  }

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();

    // تنسيق القيمة الأولية إذا كانت موجودة
    if (_controller.text.isNotEmpty) {
      _formatCurrency(_controller.text);
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  /// تنسيق المبلغ المالي
  void _formatCurrency(String value) {
    if (value.isEmpty) return;

    // إزالة جميع الأحرف غير الرقمية والنقطة
    String cleanValue = value.replaceAll(RegExp(r'[^\d.]'), '');

    if (cleanValue.isEmpty) {
      _controller.clear();
      return;
    }

    try {
      double amount = double.parse(cleanValue);

      // تطبيق الحد الأدنى والأقصى
      if (widget.minAmount != null && amount < widget.minAmount!) {
        amount = widget.minAmount!;
      }
      if (widget.maxAmount != null && amount > widget.maxAmount!) {
        amount = widget.maxAmount!;
      }

      // تنسيق المبلغ
      String formattedAmount;
      if (widget.showThousandsSeparator) {
        formattedAmount = numberFormat.format(amount); // استخدام التحميل الكسول
      } else {
        formattedAmount = amount.toStringAsFixed(widget.decimalPlaces);
      }

      // تحديث النص مع الحفاظ على موضع المؤشر
      _controller.text = formattedAmount;
      _controller.selection = TextSelection.fromPosition(
        TextPosition(offset: formattedAmount.length),
      );

      // استدعاء دالة التغيير
      widget.onChanged?.call(amount);
    } catch (e) {
      // في حالة خطأ في التحويل، لا نفعل شيء
    }
  }

  /// الحصول على القيمة الرقمية من النص
  double? _getNumericValue(String text) {
    if (text.isEmpty) return null;

    String cleanValue = text.replaceAll(RegExp(r'[^\d.]'), '');
    if (cleanValue.isEmpty) return null;

    try {
      return double.parse(cleanValue);
    } catch (e) {
      return null;
    }
  }

  /// التحقق من صحة المبلغ باستخدام الدوال المساعدة
  String? _validateAmount(String? value) {
    // استخدام دالة التحقق المطلوب من النظام المساعد
    if (widget.isRequired) {
      final requiredValidation = Validators.required(widget.label)(value);
      if (requiredValidation != null) return requiredValidation;
    }

    if (value != null && value.isNotEmpty) {
      // استخدام دالة التحقق من الأرقام الموجبة
      final positiveValidation = Validators.positiveNumber(widget.label)(value);
      if (positiveValidation != null) return positiveValidation;

      double? amount = _getNumericValue(value);

      if (amount != null) {
        // التحقق من النطاق باستخدام الدوال المساعدة
        if (widget.minAmount != null && widget.maxAmount != null) {
          final rangeValidation = Validators.numberRange(
              widget.minAmount!, widget.maxAmount!, widget.label)(value);
          if (rangeValidation != null) return rangeValidation;
        }
      }
    }

    return widget.validator?.call(value);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return TextFormField(
      controller: _controller,
      readOnly: widget.readOnly,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      textAlign: widget.currencyAtStart ? TextAlign.start : TextAlign.end,
      style: AppTypography.createCustomStyle(
        fontSize: AppTypography.fontSizeMedium,
        fontWeight: AppTypography.weightRegular,
        color: isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
      ),
      decoration: InputDecoration(
        labelText: widget.label,
        hintText: widget.hint ?? 'أدخل المبلغ',
        filled: true,
        fillColor: isDark
            ? AppColors.darkSurfaceVariant
            : AppColors.lightSurfaceVariant,
        prefixIcon: widget.prefixIcon != null
            ? Icon(
                widget.prefixIcon,
                size: AppDimensions.mediumIconSize,
                color: isDark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              )
            : Icon(
                Icons.attach_money,
                size: AppDimensions.mediumIconSize,
                color: isDark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              ),
        prefixText: widget.currencyAtStart ? '${widget.currencySymbol} ' : null,
        suffixText:
            !widget.currencyAtStart ? ' ${widget.currencySymbol}' : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppDimensions.defaultMargin,
          vertical: AppDimensions.smallMargin,
        ),
        labelStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
        hintStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
        ),
      ),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
        LengthLimitingTextInputFormatter(15), // حد أقصى 15 رقم
      ],
      validator: _validateAmount,
      onChanged: (value) {
        _formatCurrency(value);
      },
    );
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● 2. حقل النسب المئوية (AkPercentageInput)
// ───────────────────────────────────────────────────────────────────────────────

/// حقل إدخال النسب المئوية مع التحقق من النطاق والدعم الكامل للوضع المظلم/الفاتح
///
/// **المميزات:**
/// - تحقق تلقائي من النطاق (0-100% افتراضياً)
/// - تنسيق تلقائي مع رمز النسبة المئوية
/// - دعم كامل للوضع المظلم والفاتح
/// - تكامل مع نظام الثيمات
/// - رسائل خطأ واضحة ومفهومة
///
/// **مثال الاستخدام:**
/// ```dart
/// AkPercentageInput(
///   label: 'نسبة الخصم',
///   controller: discountController,
///   minPercentage: 0.0,
///   maxPercentage: 50.0,
///   isRequired: true,
/// )
/// ```
class AkPercentageInput extends StatefulWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// الحد الأدنى للنسبة (افتراضي: 0)
  final double minPercentage;

  /// الحد الأقصى للنسبة (افتراضي: 100)
  final double maxPercentage;

  /// عدد الأرقام العشرية المسموحة
  final int decimalPlaces;

  /// دالة التحقق المخصصة
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<double?>? onChanged;

  /// هل الحقل للقراءة فقط
  final bool readOnly;

  /// أيقونة الحقل
  final IconData? prefixIcon;

  const AkPercentageInput({
    super.key,
    this.controller,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.minPercentage = 0.0,
    this.maxPercentage = 100.0,
    this.decimalPlaces = 2,
    this.validator,
    this.onChanged,
    this.readOnly = false,
    this.prefixIcon,
  });

  @override
  State<AkPercentageInput> createState() => _AkPercentageInputState();
}

class _AkPercentageInputState extends State<AkPercentageInput> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  /// الحصول على القيمة الرقمية من النص
  double? _getNumericValue(String text) {
    if (text.isEmpty) return null;

    String cleanValue = text.replaceAll(RegExp(r'[^\d.]'), '');
    if (cleanValue.isEmpty) return null;

    try {
      return double.parse(cleanValue);
    } catch (e) {
      return null;
    }
  }

  /// التحقق من صحة النسبة المئوية باستخدام الدوال المساعدة
  String? _validatePercentage(String? value) {
    // استخدام دالة التحقق المطلوب من النظام المساعد
    if (widget.isRequired) {
      final requiredValidation = Validators.required(widget.label)(value);
      if (requiredValidation != null) return requiredValidation;
    }

    if (value != null && value.isNotEmpty) {
      // استخدام دالة التحقق من الأرقام الموجبة
      final positiveValidation = Validators.positiveNumber(widget.label)(value);
      if (positiveValidation != null) return positiveValidation;

      // التحقق من النطاق باستخدام الدوال المساعدة
      final rangeValidation = Validators.numberRange(
          widget.minPercentage, widget.maxPercentage, widget.label)(value);
      if (rangeValidation != null) return rangeValidation;
    }

    return widget.validator?.call(value);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return TextFormField(
      controller: _controller,
      readOnly: widget.readOnly,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      textAlign: TextAlign.end,
      style: AppTypography.createCustomStyle(
        fontSize: AppTypography.fontSizeMedium,
        fontWeight: AppTypography.weightRegular,
        color: isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
      ),
      decoration: InputDecoration(
        labelText: widget.label,
        hintText: widget.hint ?? 'أدخل النسبة المئوية',
        filled: true,
        fillColor: isDark
            ? AppColors.darkSurfaceVariant
            : AppColors.lightSurfaceVariant,
        prefixIcon: widget.prefixIcon != null
            ? Icon(
                widget.prefixIcon,
                size: AppDimensions.mediumIconSize,
                color: isDark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              )
            : Icon(
                Icons.percent,
                size: AppDimensions.mediumIconSize,
                color: isDark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              ),
        suffixText: '%',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppDimensions.defaultMargin,
          vertical: AppDimensions.smallMargin,
        ),
        labelStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
        hintStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
        ),
        suffixStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
      ),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
        LengthLimitingTextInputFormatter(6), // حد أقصى 6 أرقام (100.00)
      ],
      validator: _validatePercentage,
      onChanged: (value) {
        final numericValue = _getNumericValue(value);
        widget.onChanged?.call(numericValue);
      },
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📞 القسم الثاني: حقول الاتصال
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● 3. حقل رقم الهاتف (AkPhoneInput)
// ───────────────────────────────────────────────────────────────────────────────

/// حقل إدخال أرقام الهاتف مع التنسيق التلقائي والدعم الكامل للوضع المظلم/الفاتح
///
/// **المميزات:**
/// - تنسيق تلقائي لأرقام الهاتف اليمنية
/// - رمز الدولة القابل للتخصيص (+967 افتراضي لليمن)
/// - تحقق من صحة رقم الهاتف
/// - دعم كامل للوضع المظلم والفاتح
/// - تكامل مع نظام الثيمات
/// - تحميل كسول للتنسيق
///
/// **مثال الاستخدام:**
/// ```dart
/// AkPhoneInput(
///   label: 'رقم الهاتف',
///   controller: phoneController,
///   defaultCountryCode: '+967',
///   isRequired: true,
/// )
/// ```
class AkPhoneInput extends StatefulWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// رمز الدولة الافتراضي
  final String defaultCountryCode;

  /// هل يتم عرض رمز الدولة
  final bool showCountryCode;

  /// دالة التحقق المخصصة
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<String?>? onChanged;

  /// هل الحقل للقراءة فقط
  final bool readOnly;

  /// أيقونة الحقل
  final IconData? prefixIcon;

  const AkPhoneInput({
    super.key,
    this.controller,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.defaultCountryCode = '+967', // اليمن
    this.showCountryCode = true,
    this.validator,
    this.onChanged,
    this.readOnly = false,
    this.prefixIcon,
  });

  @override
  State<AkPhoneInput> createState() => _AkPhoneInputState();
}

class _AkPhoneInputState extends State<AkPhoneInput> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();

    // تنسيق الرقم الأولي إذا كان موجوداً
    if (_controller.text.isNotEmpty) {
      _formatPhoneNumber(_controller.text);
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  /// تنسيق رقم الهاتف اليمني
  void _formatPhoneNumber(String value) {
    if (value.isEmpty) return;

    // إزالة جميع الأحرف غير الرقمية
    String cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');

    if (cleanValue.isEmpty) {
      _controller.clear();
      return;
    }

    // تنسيق الرقم اليمني (9 أرقام)
    String formattedNumber = cleanValue;

    if (cleanValue.length >= 3) {
      formattedNumber = cleanValue.substring(0, 3);
      if (cleanValue.length >= 6) {
        formattedNumber += ' ${cleanValue.substring(3, 6)}';
        if (cleanValue.length >= 9) {
          formattedNumber += ' ${cleanValue.substring(6, 9)}';
        } else if (cleanValue.length > 6) {
          formattedNumber += ' ${cleanValue.substring(6)}';
        }
      } else if (cleanValue.length > 3) {
        formattedNumber += ' ${cleanValue.substring(3)}';
      }
    }

    // تحديث النص مع الحفاظ على موضع المؤشر
    _controller.text = formattedNumber;
    _controller.selection = TextSelection.fromPosition(
      TextPosition(offset: formattedNumber.length),
    );

    // استدعاء دالة التغيير
    widget.onChanged?.call(formattedNumber);
  }

  /// التحقق من صحة رقم الهاتف باستخدام الدوال المساعدة
  String? _validatePhoneNumber(String? value) {
    // استخدام دالة التحقق المطلوب من النظام المساعد
    if (widget.isRequired) {
      final requiredValidation = Validators.required(widget.label)(value);
      if (requiredValidation != null) return requiredValidation;
    }

    if (value != null && value.isNotEmpty) {
      // التحقق من طول رقم الهاتف اليمني (9 أرقام)
      String cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
      if (cleanValue.length != 9) {
        return 'رقم الهاتف يجب أن يكون 9 أرقام';
      }

      // التحقق من أن الرقم يبدأ بأرقام صحيحة للشبكات اليمنية
      if (!cleanValue.startsWith(RegExp(r'^(77|73|70|71|78)'))) {
        return 'رقم الهاتف غير صحيح للشبكات اليمنية';
      }
    }

    return widget.validator?.call(value);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return TextFormField(
      controller: _controller,
      readOnly: widget.readOnly,
      keyboardType: TextInputType.phone,
      //textDirection: TextDirection.ltr, // أرقام الهاتف من اليسار لليمين
      style: AppTypography.createCustomStyle(
        fontSize: AppTypography.fontSizeMedium,
        fontWeight: AppTypography.weightRegular,
        color: isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
      ),
      decoration: InputDecoration(
        labelText: widget.label,
        hintText: widget.hint ?? '770119544',
        filled: true,
        fillColor: isDark
            ? AppColors.darkSurfaceVariant
            : AppColors.lightSurfaceVariant,
        prefixIcon: widget.prefixIcon != null
            ? Icon(
                widget.prefixIcon,
                size: AppDimensions.mediumIconSize,
                color: isDark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              )
            : Icon(
                Icons.phone,
                size: AppDimensions.mediumIconSize,
                color: isDark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              ),
        prefixText:
            widget.showCountryCode ? '${widget.defaultCountryCode} ' : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppDimensions.defaultMargin,
          vertical: AppDimensions.smallMargin,
        ),
        labelStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
        hintStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
        ),
        prefixStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
      ),
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(9), // 9 أرقام للهاتف اليمني
      ],
      validator: _validatePhoneNumber,
      onChanged: (value) {
        _formatPhoneNumber(value);
      },
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📅 القسم الجديد: حقول التاريخ والوقت المتقدمة
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● حقل نطاق التاريخ (AkDateRangeInput)
// ───────────────────────────────────────────────────────────────────────────────

/// حقل إدخال نطاق التاريخ مع منتقي التاريخ المحلي
///
/// **المميزات:**
/// - منتقي تاريخ محلي باللغة العربية
/// - دعم نطاق التاريخ (من - إلى)
/// - تنسيق التاريخ حسب المنطقة
/// - دعم كامل للوضع المظلم/الفاتح
/// - تحقق من صحة النطاق
///
/// **مثال الاستخدام:**
/// ```dart
/// AkDateRangeInput(
///   label: 'فترة التقرير',
///   onDateRangeSelected: (start, end) {
///     print('من: $start إلى: $end');
///   },
/// )
/// ```
class AkDateRangeInput extends StatefulWidget {
  /// تسمية الحقل
  final String label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// التاريخ الأولي للبداية
  final DateTime? initialStartDate;

  /// التاريخ الأولي للنهاية
  final DateTime? initialEndDate;

  /// أول تاريخ متاح للاختيار
  final DateTime? firstDate;

  /// آخر تاريخ متاح للاختيار
  final DateTime? lastDate;

  /// دالة تنفذ عند اختيار نطاق التاريخ
  final Function(DateTime?, DateTime?)? onDateRangeSelected;

  /// دالة التحقق المخصصة
  final String? Function(DateTime?, DateTime?)? validator;

  /// هل الحقل للقراءة فقط
  final bool readOnly;

  /// أيقونة الحقل
  final IconData? prefixIcon;

  const AkDateRangeInput({
    super.key,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.initialStartDate,
    this.initialEndDate,
    this.firstDate,
    this.lastDate,
    this.onDateRangeSelected,
    this.validator,
    this.readOnly = false,
    this.prefixIcon,
  });

  @override
  State<AkDateRangeInput> createState() => _AkDateRangeInputState();
}

class _AkDateRangeInputState extends State<AkDateRangeInput> {
  DateTime? _startDate;
  DateTime? _endDate;
  late DateFormat _dateFormat;

  @override
  void initState() {
    super.initState();
    _startDate = widget.initialStartDate;
    _endDate = widget.initialEndDate;
    _dateFormat = DateFormat('yyyy/MM/dd', 'ar');
  }

  /// اختيار نطاق التاريخ
  Future<void> _selectDateRange() async {
    if (widget.readOnly) return;

    final initialDateRange = _startDate != null && _endDate != null
        ? DateTimeRange(start: _startDate!, end: _endDate!)
        : null;

    final pickedDateRange = await showDateRangePicker(
      context: context,
      initialDateRange: initialDateRange,
      firstDate: widget.firstDate ?? DateTime(2000),
      lastDate: widget.lastDate ?? DateTime(2100),
      locale: const Locale('ar'),
      builder: (context, child) {
        final theme = Theme.of(context);
        final isDark = theme.brightness == Brightness.dark;

        return Theme(
          data: theme.copyWith(
            colorScheme: theme.colorScheme.copyWith(
              primary: AppColors.primary,
              surface: isDark ? AppColors.darkSurface : AppColors.lightSurface,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDateRange != null) {
      setState(() {
        _startDate = pickedDateRange.start;
        _endDate = pickedDateRange.end;
      });

      widget.onDateRangeSelected?.call(_startDate, _endDate);
    }
  }

  /// مسح نطاق التاريخ
  void _clearDateRange() {
    if (widget.readOnly) return;

    setState(() {
      _startDate = null;
      _endDate = null;
    });

    widget.onDateRangeSelected?.call(null, null);
  }

  /// الحصول على نص العرض
  String _getDisplayText() {
    if (_startDate != null && _endDate != null) {
      return '${_dateFormat.format(_startDate!)} - ${_dateFormat.format(_endDate!)}';
    }
    return widget.hint ?? 'اختر الفترة الزمنية';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return InkWell(
      onTap: _selectDateRange,
      borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppDimensions.defaultMargin,
          vertical: AppDimensions.smallMargin,
        ),
        decoration: BoxDecoration(
          color: isDark
              ? AppColors.darkSurfaceVariant
              : AppColors.lightSurfaceVariant,
          border: Border.all(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
        ),
        child: Row(
          children: [
            // الأيقونة
            Icon(
              widget.prefixIcon ?? Icons.date_range,
              size: AppDimensions.mediumIconSize,
              color: isDark
                  ? AppColors.darkTextSecondary
                  : AppColors.lightTextSecondary,
            ),
            SizedBox(width: AppDimensions.smallSpacing),

            // النص
            Expanded(
              child: Text(
                _getDisplayText(),
                style: AppTypography.createCustomStyle(
                  fontSize: AppTypography.fontSizeMedium,
                  fontWeight: AppTypography.weightRegular,
                  color: _startDate != null && _endDate != null
                      ? (isDark
                          ? AppColors.darkTextPrimary
                          : AppColors.lightTextPrimary)
                      : (isDark
                          ? AppColors.darkTextHint
                          : AppColors.lightTextHint),
                ),
              ),
            ),

            // زر المسح
            if (_startDate != null && _endDate != null && !widget.readOnly)
              InkWell(
                onTap: _clearDateRange,
                borderRadius: BorderRadius.circular(AppDimensions.smallRadius),
                child: Padding(
                  padding: EdgeInsets.all(AppDimensions.tinySpacing),
                  child: Icon(
                    Icons.clear,
                    size: AppDimensions.smallIconSize,
                    color: isDark
                        ? AppColors.darkTextSecondary
                        : AppColors.lightTextSecondary,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📋 القسم السادس: حقول الاختيار المتقدمة
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● حقل القائمة المنسدلة (AkDropdownInput)
// ───────────────────────────────────────────────────────────────────────────────

/// حقل قائمة منسدلة محسن مع البحث والدعم الكامل للوضع المظلم/الفاتح
///
/// **المميزات:**
/// - قائمة منسدلة مع خيارات متعددة
/// - دعم البحث داخل الخيارات (اختياري)
/// - تصميم موحد مع باقي الحقول
/// - دعم كامل للوضع المظلم والفاتح
/// - تكامل مع نظام الثيمات
/// - تحقق من صحة الاختيار
///
/// **مثال الاستخدام:**
/// ```dart
/// AkDropdownInput<String>(
///   label: 'الفئة',
///   hint: 'اختر فئة المنتج',
///   value: selectedCategory,
///   items: [
///     DropdownMenuItem(value: 'electronics', child: Text('إلكترونيات')),
///     DropdownMenuItem(value: 'clothing', child: Text('ملابس')),
///   ],
///   onChanged: (value) => setState(() => selectedCategory = value),
///   isRequired: true,
/// )
/// ```
class AkDropdownInput<T> extends StatefulWidget {
  /// تسمية الحقل
  final String label;

  /// القيمة المختارة حالياً
  final T? value;

  /// قائمة الخيارات
  final List<DropdownMenuItem<T>> items;

  /// دالة تنفذ عند تغيير الاختيار
  final ValueChanged<T?> onChanged;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// أيقونة الحقل
  final IconData? prefixIcon;

  /// هل يتم توسيع القائمة لملء العرض
  final bool isExpanded;

  /// المسافة الداخلية للمحتوى
  final EdgeInsetsGeometry? contentPadding;

  /// دالة التحقق المخصصة
  final String? Function(T?)? validator;

  /// هل الحقل للقراءة فقط
  final bool readOnly;

  /// هل يتم تمكين البحث
  final bool enableSearch;

  /// دالة البحث المخصصة
  final bool Function(DropdownMenuItem<T>, String)? searchMatcher;

  const AkDropdownInput({
    super.key,
    required this.label,
    required this.value,
    required this.items,
    required this.onChanged,
    this.hint,
    this.isRequired = false,
    this.prefixIcon,
    this.isExpanded = true,
    this.contentPadding,
    this.validator,
    this.readOnly = false,
    this.enableSearch = false,
    this.searchMatcher,
  });

  @override
  State<AkDropdownInput<T>> createState() => _AkDropdownInputState<T>();
}

class _AkDropdownInputState<T> extends State<AkDropdownInput<T>> {
  /// التحقق من صحة الاختيار باستخدام الدوال المساعدة
  String? _validateSelection(T? value) {
    // استخدام دالة التحقق المطلوب من النظام المساعد
    if (widget.isRequired && value == null) {
      return 'الرجاء اختيار ${widget.label}';
    }

    return widget.validator?.call(value);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // تسمية الحقل
        if (widget.label.isNotEmpty) ...[
          Text(
            widget.isRequired ? '${widget.label} *' : widget.label,
            style: AppTypography.createCustomStyle(
              fontSize: AppTypography.fontSizeMedium,
              fontWeight: AppTypography.weightMedium,
              color: isDark
                  ? AppColors.darkTextSecondary
                  : AppColors.lightTextSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.tinySpacing),
        ],

        // القائمة المنسدلة
        DropdownButtonFormField<T>(
          value: widget.value,
          items: widget.items,
          onChanged: widget.readOnly ? null : widget.onChanged,
          isExpanded: widget.isExpanded,
          style: AppTypography.createCustomStyle(
            fontSize: AppTypography.fontSizeMedium,
            fontWeight: AppTypography.weightRegular,
            color:
                isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
          ),
          decoration: InputDecoration(
            hintText: widget.hint,
            filled: true,
            fillColor: isDark
                ? AppColors.darkSurfaceVariant
                : AppColors.lightSurfaceVariant,
            prefixIcon: widget.prefixIcon != null
                ? Icon(
                    widget.prefixIcon,
                    size: AppDimensions.mediumIconSize,
                    color: isDark
                        ? AppColors.darkTextSecondary
                        : AppColors.lightTextSecondary,
                  )
                : null,
            contentPadding: widget.contentPadding ??
                EdgeInsets.symmetric(
                  horizontal: AppDimensions.defaultMargin,
                  vertical: AppDimensions.smallMargin,
                ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
              borderSide: BorderSide(
                color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
              borderSide: BorderSide(
                color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
              borderSide: const BorderSide(color: AppColors.error),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
              borderSide: const BorderSide(color: AppColors.error, width: 2),
            ),
            hintStyle: AppTypography.createCustomStyle(
              fontSize: AppTypography.fontSizeMedium,
              color: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
            ),
          ),
          icon: Icon(
            Icons.arrow_drop_down,
            color: isDark
                ? AppColors.darkTextSecondary
                : AppColors.lightTextSecondary,
          ),
          dropdownColor:
              isDark ? AppColors.darkSurface : AppColors.lightSurface,
          validator: _validateSelection,
        ),
      ],
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📝 القسم الثامن: حقل البحث مع الاقتراحات التلقائية (AkSearchInput)
// ═══════════════════════════════════════════════════════════════════════════════

/// حقل البحث مع الاقتراحات التلقائية والدعم الكامل للوضع المظلم/الفاتح
///
/// **المميزات:**
/// - أيقونة بحث مدمجة
/// - زر مسح النص
/// - تصميم مخصص للبحث
/// - دعم كامل للوضع المظلم والفاتح
/// - تكامل مع نظام الثيمات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkSearchInput(
///   controller: searchController,
///   hint: 'بحث عن منتج...',
///   onChanged: (value) => performSearch(value),
///   onSubmitted: (value) => executeSearch(value),
/// )
/// ```
class AkSearchInput extends StatefulWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// نص التلميح
  final String? hint;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<String>? onChanged;

  /// دالة تنفذ عند إرسال النص
  final ValueChanged<String>? onSubmitted;

  /// دالة تنفذ عند مسح النص
  final VoidCallback? onClear;

  /// هل الحقل للقراءة فقط
  final bool readOnly;

  /// عقدة التركيز
  final FocusNode? focusNode;

  /// هل يتم التركيز تلقائياً
  final bool autofocus;

  /// هل يتم عرض زر المسح
  final bool showClearButton;

  const AkSearchInput({
    super.key,
    this.controller,
    this.hint,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.readOnly = false,
    this.focusNode,
    this.autofocus = false,
    this.showClearButton = true,
  });

  @override
  State<AkSearchInput> createState() => _AkSearchInputState();
}

class _AkSearchInputState extends State<AkSearchInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// مسح النص
  void _clearText() {
    _controller.clear();
    widget.onChanged?.call('');
    widget.onClear?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return TextFormField(
      controller: _controller,
      focusNode: _focusNode,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      textInputAction: TextInputAction.search,
      style: AppTypography.createCustomStyle(
        fontSize: AppTypography.fontSizeMedium,
        fontWeight: AppTypography.weightRegular,
        color: isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
      ),
      decoration: InputDecoration(
        hintText: widget.hint ?? 'بحث...',
        filled: true,
        fillColor: isDark
            ? AppColors.darkSurfaceVariant
            : AppColors.lightSurfaceVariant,
        prefixIcon: Icon(
          Icons.search,
          size: AppDimensions.mediumIconSize,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
        suffixIcon: widget.showClearButton && _controller.text.isNotEmpty
            ? IconButton(
                icon: Icon(
                  Icons.clear,
                  size: AppDimensions.mediumIconSize,
                  color: isDark
                      ? AppColors.darkTextSecondary
                      : AppColors.lightTextSecondary,
                ),
                onPressed: _clearText,
                tooltip: 'مسح النص',
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.largeRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.largeRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.largeRadius),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppDimensions.defaultMargin,
          vertical: AppDimensions.smallMargin,
        ),
        hintStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
        ),
      ),
      onChanged: (value) {
        setState(() {}); // لتحديث زر المسح
        widget.onChanged?.call(value);
      },
      onFieldSubmitted: widget.onSubmitted,
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🔢 القسم الثالث: الحقول الرقمية المتخصصة (AkNumericInput)
// ═══════════════════════════════════════════════════════════════════════════════

/// حقل إدخال الأرقام العامة مع نطاق مخصص والدعم الكامل للوضع المظلم/الفاتح
///
/// **المميزات:**
/// - تحقق من النطاق (الحد الأدنى والأقصى)
/// - دعم الأرقام العشرية مع تحديد عدد المنازل
/// - تنسيق تلقائي للأرقام
/// - وحدة قياس مخصصة
/// - دعم كامل للوضع المظلم والفاتح
/// - تكامل مع نظام الثيمات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkNumericInput(
///   label: 'الكمية',
///   controller: quantityController,
///   minValue: 1,
///   maxValue: 999,
///   decimalPlaces: 0,
///   unit: 'قطعة',
///   isRequired: true,
/// )
/// ```
class AkNumericInput extends StatefulWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// الحد الأدنى للقيمة
  final double? minValue;

  /// الحد الأقصى للقيمة
  final double? maxValue;

  /// عدد المنازل العشرية المسموحة
  final int decimalPlaces;

  /// وحدة القياس (مثل: قطعة، كيلو، متر)
  final String? unit;

  /// دالة التحقق المخصصة
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<double?>? onChanged;

  /// هل الحقل للقراءة فقط
  final bool readOnly;

  /// أيقونة البداية
  final IconData? prefixIcon;

  /// هل يتم عرض أزرار الزيادة والنقصان
  final bool showSteppers;

  /// قيمة الخطوة للزيادة والنقصان
  final double stepValue;

  /// عقدة التركيز
  final FocusNode? focusNode;

  /// هل يتم التركيز تلقائياً
  final bool autofocus;

  const AkNumericInput({
    super.key,
    this.controller,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.minValue,
    this.maxValue,
    this.decimalPlaces = 2,
    this.unit,
    this.validator,
    this.onChanged,
    this.readOnly = false,
    this.prefixIcon,
    this.showSteppers = false,
    this.stepValue = 1.0,
    this.focusNode,
    this.autofocus = false,
  });

  @override
  State<AkNumericInput> createState() => _AkNumericInputState();
}

class _AkNumericInputState extends State<AkNumericInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  NumberFormat? _numberFormat; // تحميل كسول للتنسيق

  /// الحصول على منسق الأرقام مع التحميل الكسول
  NumberFormat get numberFormat {
    _numberFormat ??= NumberFormat.decimalPattern('ar');
    return _numberFormat!;
  }

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// التحقق من صحة الرقم
  String? _validateNumber(String? value) {
    // استخدام دالة التحقق المطلوب من النظام المساعد
    if (widget.isRequired) {
      final requiredValidation = Validators.required(widget.label)(value);
      if (requiredValidation != null) return requiredValidation;
    }

    if (value != null && value.isNotEmpty) {
      final number = double.tryParse(value);
      if (number == null) {
        return 'الرجاء إدخال رقم صحيح';
      }

      // التحقق من النطاق
      if (widget.minValue != null && number < widget.minValue!) {
        return 'القيمة يجب أن تكون أكبر من أو تساوي ${widget.minValue}';
      }

      if (widget.maxValue != null && number > widget.maxValue!) {
        return 'القيمة يجب أن تكون أقل من أو تساوي ${widget.maxValue}';
      }

      // التحقق من عدد المنازل العشرية
      final decimalParts = value.split('.');
      if (decimalParts.length > 1 &&
          decimalParts[1].length > widget.decimalPlaces) {
        return 'الحد الأقصى ${widget.decimalPlaces} منازل عشرية';
      }
    }

    return widget.validator?.call(value);
  }

  /// تنسيق الرقم عند تغيير القيمة
  void _onChanged(String value) {
    final number = double.tryParse(value);
    widget.onChanged?.call(number);
  }

  /// زيادة القيمة
  void _incrementValue() {
    final currentValue = double.tryParse(_controller.text) ?? 0;
    final newValue = currentValue + widget.stepValue;

    if (widget.maxValue == null || newValue <= widget.maxValue!) {
      _controller.text = newValue.toStringAsFixed(widget.decimalPlaces);
      _onChanged(_controller.text);
    }
  }

  /// تقليل القيمة
  void _decrementValue() {
    final currentValue = double.tryParse(_controller.text) ?? 0;
    final newValue = currentValue - widget.stepValue;

    if (widget.minValue == null || newValue >= widget.minValue!) {
      _controller.text = newValue.toStringAsFixed(widget.decimalPlaces);
      _onChanged(_controller.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return TextFormField(
      controller: _controller,
      focusNode: _focusNode,
      readOnly: widget.readOnly,
      keyboardType: TextInputType.numberWithOptions(
        decimal: widget.decimalPlaces > 0,
        signed: widget.minValue == null || widget.minValue! < 0,
      ),
      autofocus: widget.autofocus,
      inputFormatters: [
        FilteringTextInputFormatter.allow(
          RegExp(widget.decimalPlaces > 0
              ? r'^\d*\.?\d{0,' + widget.decimalPlaces.toString() + r'}'
              : r'^\d*'),
        ),
      ],
      style: AppTypography.createCustomStyle(
        fontSize: AppTypography.fontSizeMedium,
        fontWeight: AppTypography.weightRegular,
        color: isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
      ),
      decoration: InputDecoration(
        labelText: widget.label,
        hintText: widget.hint ?? 'أدخل ${widget.label}',
        filled: true,
        fillColor: isDark
            ? AppColors.darkSurfaceVariant
            : AppColors.lightSurfaceVariant,
        prefixIcon: widget.prefixIcon != null
            ? Icon(
                widget.prefixIcon,
                size: AppDimensions.mediumIconSize,
                color: isDark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              )
            : null,
        suffixIcon: widget.showSteppers ? _buildSteppers(isDark) : null,
        suffixText: widget.unit,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.mediumRadius),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppDimensions.defaultMargin,
          vertical: AppDimensions.smallMargin,
        ),
        labelStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
        hintStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeMedium,
          color: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
        ),
        suffixStyle: AppTypography.createCustomStyle(
          fontSize: AppTypography.fontSizeSmall,
          color: isDark
              ? AppColors.darkTextSecondary
              : AppColors.lightTextSecondary,
        ),
      ),
      validator: _validateNumber,
      onChanged: _onChanged,
    );
  }

  /// بناء أزرار الزيادة والنقصان
  Widget _buildSteppers(bool isDark) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // زر النقصان
        IconButton(
          icon: Icon(
            Icons.remove,
            size: AppDimensions.smallIconSize,
            color: isDark
                ? AppColors.darkTextSecondary
                : AppColors.lightTextSecondary,
          ),
          onPressed: widget.readOnly ? null : _decrementValue,
          tooltip: 'تقليل',
        ),
        // زر الزيادة
        IconButton(
          icon: Icon(
            Icons.add,
            size: AppDimensions.smallIconSize,
            color: isDark
                ? AppColors.darkTextSecondary
                : AppColors.lightTextSecondary,
          ),
          onPressed: widget.readOnly ? null : _incrementValue,
          tooltip: 'زيادة',
        ),
      ],
    );
  }
}

/// استخدم AkDateRangeInput مع mode: AkDateType.single للتاريخ الواحد
/// أو mode: AkDateType.range لنطاق التاريخ
//typedef AkDateInput = AkDateRangeInput;
