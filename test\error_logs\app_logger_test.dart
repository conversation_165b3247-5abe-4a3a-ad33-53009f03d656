import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/utils/app_logger.dart';

void main() {
  group('AppLogger Tests', () {
    test('يمكن تسجيل رسائل معلومات', () {
      // لا يمكن اختبار الطباعة مباشرة، لكن يمكننا التأكد من أن الدالة لا تسبب أخطاء
      expect(() => AppLogger.info('رسالة معلومات للاختبار'), returnsNormally);
    });

    test('يمكن تسجيل رسائل تحذير', () {
      expect(() => AppLogger.warning('رسالة تحذير للاختبار'), returnsNormally);
    });

    test('يمكن تسجيل رسائل خطأ', () {
      expect(() => AppLogger.error('رسالة خطأ للاختبار'), returnsNormally);
    });

    test('يمكن تسجيل رسائل خطأ مع استثناء', () {
      final error = Exception('استثناء للاختبار');
      expect(() => AppLogger.error('رسالة خطأ مع استثناء', error: error),
          returnsNormally);
    });

    test('يمكن تسجيل رسائل خطأ مع استثناء وتتبع المكدس', () {
      final error = Exception('استثناء للاختبار');
      final stackTrace = StackTrace.current;
      expect(
        () => AppLogger.error(
          'رسالة خطأ مع استثناء وتتبع المكدس',
          error: error,
          stackTrace: stackTrace,
        ),
        returnsNormally,
      );
    });
  });
}
