import '../../../core/auth/models/user_role.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/auth/roles_schema.dart';

/// خدمة الأدوار
class RoleService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على معرف الدور من رمز الدور
  /// يستخدم هذا للحصول على معرف الدور الفعلي (UUID) من رمز الدور (مثل "admin")
  Future<String?> getRoleIdByCode(String roleCode) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على اسم الدور من RolesSchema
      final roleName = RolesSchema.roles[roleCode];
      if (roleName == null) {
        AppLogger.warning('لم يتم العثور على اسم الدور للرمز: $roleCode');
        return null;
      }

      try {
        // البحث عن الدور بالاسم
        final List<Map<String, dynamic>> maps = await db.rawQuery(
          'SELECT id FROM roles WHERE name = ? AND is_deleted = 0 LIMIT 1',
          [roleName],
        );

        if (maps.isNotEmpty) {
          final roleId = maps.first['id'] as String;
          AppLogger.info('تم العثور على معرف الدور: $roleId للرمز: $roleCode');
          return roleId;
        }

        AppLogger.warning('لم يتم العثور على دور باسم: $roleName');
        return null;
      } catch (queryError) {
        AppLogger.warning('فشل الاستعلام عن الدور: $queryError');

        // محاولة استعلام أبسط
        final List<Map<String, dynamic>> simpleMaps = await db.rawQuery(
          'SELECT id FROM roles WHERE name = "$roleName" AND is_deleted = 0 LIMIT 1',
        );

        if (simpleMaps.isNotEmpty) {
          final roleId = simpleMaps.first['id'] as String;
          AppLogger.info(
              'تم العثور على معرف الدور: $roleId للرمز: $roleCode (استعلام بسيط)');
          return roleId;
        }

        AppLogger.warning(
            'لم يتم العثور على دور باسم: $roleName (استعلام بسيط)');
        return null;
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على معرف الدور من الرمز',
        error: e,
        stackTrace: stackTrace,
        context: {'roleCode': roleCode},
      );
      AppLogger.error('فشل في الحصول على معرف الدور من الرمز: $e');
      return null;
    }
  }

  /// الحصول على جميع الأدوار
  Future<List<UserRole>> getAllRoles() async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود جدول الأدوار
      final tableExists = await _databaseHelper.tableExists('roles');
      if (!tableExists) {
        AppLogger.error('فشل في الحصول على الأدوار: جدول الأدوار غير موجود');
        return [];
      }

      try {
        final rolesMaps = await db.query('roles');
        AppLogger.info('تم استرجاع ${rolesMaps.length} دور من قاعدة البيانات');

        // تحميل الصلاحيات لكل دور
        final roles = <UserRole>[];
        for (final map in rolesMaps) {
          final role = UserRole.fromMap(map);

          // تحميل صلاحيات الدور من جدول role_permissions
          final permissionIds = await _getRolePermissionIds(role.id);

          // إنشاء دور جديد مع الصلاحيات المحملة
          final roleWithPermissions = role.copyWith(permissions: permissionIds);
          roles.add(roleWithPermissions);

          AppLogger.info(
              '✅ تم تحميل ${permissionIds.length} صلاحية للدور: ${role.displayName}');
        }

        return roles;
      } catch (e) {
        AppLogger.error('خطأ في استعلام جدول الأدوار: $e');
        return [];
      }
    } catch (e, stackTrace) {
      AppLogger.error('فشل في الحصول على الأدوار: $e');
      ErrorTracker.captureError(
        'فشل في الحصول على الأدوار',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على معرفات صلاحيات الدور
  Future<List<String>> _getRolePermissionIds(String roleId) async {
    try {
      final db = await _databaseHelper.database;

      final permissionMaps = await db.query(
        'role_permissions',
        columns: ['permission_id'],
        where: 'role_id = ?',
        whereArgs: [roleId],
      );

      return permissionMaps
          .map((map) => map['permission_id'] as String)
          .toList();
    } catch (e) {
      AppLogger.error('فشل في تحميل صلاحيات الدور $roleId: $e');
      return [];
    }
  }

  /// الحصول على دور بواسطة المعرف
  Future<UserRole?> getRoleById(String id) async {
    try {
      final db = await _databaseHelper.database;

      final rolesMaps = await db.query(
        'roles',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (rolesMaps.isNotEmpty) {
        final role = UserRole.fromMap(rolesMaps.first);

        // تحميل صلاحيات الدور من جدول role_permissions
        final permissionIds = await _getRolePermissionIds(role.id);

        // إرجاع الدور مع الصلاحيات المحملة
        return role.copyWith(permissions: permissionIds);
      }

      return null;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على الدور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'id': id,
        },
      );
      return null;
    }
  }

  /// الحصول على دور بواسطة الاسم
  Future<UserRole?> getRoleByName(String name) async {
    try {
      final db = await _databaseHelper.database;

      final rolesMaps = await db.query(
        'roles',
        where: 'name = ?',
        whereArgs: [name],
      );

      if (rolesMaps.isNotEmpty) {
        final role = UserRole.fromMap(rolesMaps.first);

        // تحميل صلاحيات الدور من جدول role_permissions
        final permissionIds = await _getRolePermissionIds(role.id);

        // إرجاع الدور مع الصلاحيات المحملة
        return role.copyWith(permissions: permissionIds);
      }

      return null;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على الدور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'name': name,
        },
      );
      return null;
    }
  }

  /// إنشاء دور جديد
  Future<bool> createRole(UserRole role) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم وجود دور بنفس الاسم
      final existingRole = await getRoleByName(role.name);

      if (existingRole != null) {
        return false;
      }

      // التحقق من وجود الأعمدة المطلوبة
      try {
        // الحصول على معلومات الجدول
        final tableInfo = await db.rawQuery('PRAGMA table_info(roles)');

        // التحقق من وجود عمود display_name
        final hasDisplayName =
            tableInfo.any((column) => column['name'] == 'display_name');
        if (!hasDisplayName) {
          AppLogger.info('إضافة عمود display_name إلى جدول الأدوار...');
          await db.execute('ALTER TABLE roles ADD COLUMN display_name TEXT');
        }

        // التحقق من وجود عمود is_custom
        final hasIsCustom =
            tableInfo.any((column) => column['name'] == 'is_custom');
        if (!hasIsCustom) {
          AppLogger.info('إضافة عمود is_custom إلى جدول الأدوار...');
          await db.execute(
              'ALTER TABLE roles ADD COLUMN is_custom INTEGER NOT NULL DEFAULT 0');
        }
      } catch (alterError) {
        AppLogger.error('فشل في تعديل جدول الأدوار: $alterError');
        // نستمر في التنفيذ حتى لو فشل تعديل الجدول
      }

      // إدخال الدور الجديد (بدون عمود permissions)
      final roleMap = {
        'id': role.id,
        'name': role.name,
        'display_name': role.displayName,
        'description': role.description,
        'is_custom': role.isCustom ? 1 : 0,
        'is_active': 1,
        'created_at': DateTime.now().toIso8601String(),
        'is_deleted': 0,
      };

      AppLogger.info('إدخال الدور الجديد: ${roleMap.toString()}');
      await db.insert('roles', roleMap);

      // إدراج الصلاحيات في جدول role_permissions منفصل
      if (role.permissions.isNotEmpty) {
        for (final permissionId in role.permissions) {
          await db.insert('role_permissions', {
            'id':
                '${DateTime.now().millisecondsSinceEpoch}_${permissionId.hashCode}',
            'role_id': role.id,
            'permission_id': permissionId,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
            'is_deleted': 0,
          });
        }
        AppLogger.info(
            'تم إدراج ${role.permissions.length} صلاحية للدور الجديد');
      }

      return true;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في إنشاء الدور: $e');
      ErrorTracker.captureError(
        'فشل في إنشاء الدور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'role': role.toMap(),
        },
      );
      return false;
    }
  }

  /// تحديث دور
  Future<bool> updateRole(UserRole role) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود الدور
      final existingRole = await getRoleById(role.id);

      if (existingRole == null) {
        AppLogger.warning('محاولة تحديث دور غير موجود: ${role.id}');
        return false;
      }

      // التحقق من عدم وجود دور آخر بنفس الاسم (إلا إذا كان نفس الدور)
      if (role.name != existingRole.name) {
        final roleWithSameName = await getRoleByName(role.name);
        if (roleWithSameName != null && roleWithSameName.id != role.id) {
          AppLogger.warning('يوجد دور آخر بنفس الاسم: ${role.name}');
          return false;
        }
      }

      // ملاحظة: الصلاحيات ستُحدث في جدول role_permissions منفصل

      // التحقق من وجود الأعمدة المطلوبة
      try {
        // الحصول على معلومات الجدول
        final tableInfo = await db.rawQuery('PRAGMA table_info(roles)');

        // التحقق من وجود عمود display_name
        final hasDisplayName =
            tableInfo.any((column) => column['name'] == 'display_name');
        if (!hasDisplayName) {
          AppLogger.info('إضافة عمود display_name إلى جدول الأدوار...');
          await db.execute('ALTER TABLE roles ADD COLUMN display_name TEXT');
        }

        // التحقق من وجود عمود is_custom
        final hasIsCustom =
            tableInfo.any((column) => column['name'] == 'is_custom');
        if (!hasIsCustom) {
          AppLogger.info('إضافة عمود is_custom إلى جدول الأدوار...');
          await db.execute(
              'ALTER TABLE roles ADD COLUMN is_custom INTEGER NOT NULL DEFAULT 0');
        }
      } catch (alterError) {
        AppLogger.error('فشل في تعديل جدول الأدوار: $alterError');
        // نستمر في التنفيذ حتى لو فشل تعديل الجدول
      }

      // تحديث الدور (بدون عمود permissions)
      final roleMap = {
        'name': role.name,
        'display_name': role.displayName,
        'description': role.description,
        'is_custom': role.isCustom ? 1 : 0,
        'updated_at': DateTime.now().toIso8601String(),
      };

      AppLogger.info('تحديث الدور: ${role.name} (${role.id})');

      // استخدام معاملة لضمان اكتمال العملية
      await db.transaction((txn) async {
        // تحديث بيانات الدور الأساسية
        await txn.update(
          'roles',
          roleMap,
          where: 'id = ?',
          whereArgs: [role.id],
        );

        // تحديث الصلاحيات في جدول role_permissions منفصل
        // حذف الصلاحيات الحالية
        await txn.delete(
          'role_permissions',
          where: 'role_id = ?',
          whereArgs: [role.id],
        );

        // إدراج الصلاحيات الجديدة
        for (final permissionId in role.permissions) {
          await txn.insert('role_permissions', {
            'id':
                '${DateTime.now().millisecondsSinceEpoch}_${permissionId.hashCode}',
            'role_id': role.id,
            'permission_id': permissionId,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
            'is_deleted': 0,
          });
        }
      });

      AppLogger.info('✅ تم تحديث الدور بنجاح: ${role.name}');
      AppLogger.info('✅ تم تحديث ${role.permissions.length} صلاحية للدور');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في تحديث الدور: $e');
      ErrorTracker.captureError(
        'فشل في تحديث الدور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'role': role.toMap(),
        },
      );
      return false;
    }
  }

  /// حذف دور
  Future<bool> deleteRole(String roleId) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود الدور
      final existingRole = await getRoleById(roleId);

      if (existingRole == null) {
        AppLogger.warning('محاولة حذف دور غير موجود: $roleId');
        return false;
      }

      // التحقق من أن الدور ليس دوراً افتراضياً
      if (!existingRole.isCustom) {
        AppLogger.error(
            '⛔ محاولة حذف دور أساسي غير مسموحة: ${existingRole.name}');
        return false;
      }

      // التحقق من أن الدور ليس مستخدماً من قبل أي مستخدم
      final usersWithRole = await db.query(
        'users',
        where: 'role_id = ? AND is_deleted = 0',
        whereArgs: [roleId],
      );

      if (usersWithRole.isNotEmpty) {
        AppLogger.error(
            '⛔ لا يمكن حذف الدور لأنه مستخدم من قبل ${usersWithRole.length} مستخدم');
        return false;
      }

      // التحقق من أن الدور ليس من الأدوار الأساسية بناءً على الاسم
      final isSystemRole = _isSystemRole(existingRole.name);
      if (isSystemRole) {
        AppLogger.error(
            '⛔ محاولة حذف دور أساسي غير مسموحة: ${existingRole.name}');
        return false;
      }

      // حذف الدور (حذف منطقي)
      await db.update(
        'roles',
        {
          'is_deleted': 1,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [roleId],
      );

      AppLogger.info('✅ تم حذف الدور بنجاح: ${existingRole.name}');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('فشل في حذف الدور: $e');
      ErrorTracker.captureError(
        'فشل في حذف الدور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'roleId': roleId,
        },
      );
      return false;
    }
  }

  /// التحقق مما إذا كان الدور من الأدوار الأساسية
  bool _isSystemRole(String roleName) {
    final lowerName = roleName.toLowerCase();

    // قائمة بأسماء الأدوار الأساسية
    final systemRoleNames = [
      'مالك',
      'مدير النظام',
      'admin',
      'owner',
      'مدير',
      'manager',
      'محاسب',
      'accountant',
      'أمين صندوق',
      'cashier',
      'مستخدم',
      'user',
      'مدير مخزون',
      'inventory manager',
      'مدير مبيعات',
      'sales manager',
      'مدير مشتريات',
      'purchase manager',
    ];

    return systemRoleNames
        .any((name) => lowerName.contains(name.toLowerCase()));
  }

  /// إعادة تعيين الأدوار الافتراضية (تم تعطيله - يتم في BasicDataInitializer)
  Future<bool> resetDefaultRoles() async {
    AppLogger.info('تم تعطيل إعادة تعيين الأدوار الافتراضية في RoleService');
    AppLogger.info('يتم تهيئة الأدوار في BasicDataInitializer فقط');

    // لا نقوم بأي عملية هنا لتجنب التداخل مع BasicDataInitializer
    return true;
  }

  /// إصلاح الأدوار (تم تعطيله - يتم في BasicDataInitializer)
  /// تستخدم هذه الدالة لإصلاح الأدوار في حالة وجود مشاكل
  Future<bool> repairRoles() async {
    AppLogger.info('تم تعطيل إصلاح الأدوار في RoleService');
    AppLogger.info('يتم تهيئة وإصلاح الأدوار في BasicDataInitializer فقط');

    // لا نقوم بأي عملية هنا لتجنب التداخل مع BasicDataInitializer
    return true;
  }
}
