import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج العملة الموحد
/// تم توحيده من جميع نماذج العملات في المشروع
class Currency extends BaseModel {
  // معلومات أساسية
  final String code;
  final String name;
  final String? symbol;
  final String? nameAr;
  final String? symbolAr;
  
  // معلومات التنسيق
  final int decimalPlaces;
  final String? decimalSeparator;
  final String? thousandsSeparator;
  final bool symbolOnLeft;
  final bool spaceBetweenAmountAndSymbol;
  
  // معلومات الحالة
  final bool isDefault;
  final bool isActive;
  
  // معلومات سعر الصرف
  final double exchangeRate;
  final DateTime? lastUpdated;

  Currency({
    String? id,
    required this.code,
    required this.name,
    this.symbol,
    this.nameAr,
    this.symbolAr,
    this.decimalPlaces = 2,
    this.decimalSeparator = '.',
    this.thousandsSeparator = ',',
    this.symbolOnLeft = true,
    this.spaceBetweenAmountAndSymbol = false,
    this.isDefault = false,
    this.isActive = true,
    this.exchangeRate = 1.0,
    this.lastUpdated,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          updatedAt: updatedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذه العملة مع استبدال الحقول المحددة بقيم جديدة
  Currency copyWith({
    String? id,
    String? code,
    String? name,
    String? symbol,
    String? nameAr,
    String? symbolAr,
    int? decimalPlaces,
    String? decimalSeparator,
    String? thousandsSeparator,
    bool? symbolOnLeft,
    bool? spaceBetweenAmountAndSymbol,
    bool? isDefault,
    bool? isActive,
    double? exchangeRate,
    DateTime? lastUpdated,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Currency(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      nameAr: nameAr ?? this.nameAr,
      symbolAr: symbolAr ?? this.symbolAr,
      decimalPlaces: decimalPlaces ?? this.decimalPlaces,
      decimalSeparator: decimalSeparator ?? this.decimalSeparator,
      thousandsSeparator: thousandsSeparator ?? this.thousandsSeparator,
      symbolOnLeft: symbolOnLeft ?? this.symbolOnLeft,
      spaceBetweenAmountAndSymbol:
          spaceBetweenAmountAndSymbol ?? this.spaceBetweenAmountAndSymbol,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل العملة إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'symbol': symbol,
      'name_ar': nameAr,
      'symbol_ar': symbolAr,
      'decimal_places': decimalPlaces,
      'decimal_separator': decimalSeparator,
      'thousands_separator': thousandsSeparator,
      'symbol_on_left': symbolOnLeft ? 1 : 0,
      'space_between_amount_and_symbol': spaceBetweenAmountAndSymbol ? 1 : 0,
      'is_default': isDefault ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'exchange_rate': exchangeRate,
      'last_updated': lastUpdated?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء عملة من Map
  factory Currency.fromMap(Map<String, dynamic> map) {
    return Currency(
      id: map['id'],
      code: map['code'] ?? '',
      name: map['name'] ?? '',
      symbol: map['symbol'],
      nameAr: map['name_ar'],
      symbolAr: map['symbol_ar'],
      decimalPlaces: map['decimal_places'] ?? 2,
      decimalSeparator: map['decimal_separator'] ?? '.',
      thousandsSeparator: map['thousands_separator'] ?? ',',
      symbolOnLeft: map['symbol_on_left'] == 1 || map['symbol_on_left'] == true,
      spaceBetweenAmountAndSymbol: map['space_between_amount_and_symbol'] == 1 ||
          map['space_between_amount_and_symbol'] == true,
      isDefault: map['is_default'] == 1 || map['is_default'] == true,
      isActive: map['is_active'] == 1 || map['is_active'] == true,
      exchangeRate: map['exchange_rate'] is int
          ? (map['exchange_rate'] as int).toDouble()
          : (map['exchange_rate'] as double? ?? 1.0),
      lastUpdated: map['last_updated'] != null
          ? DateTime.parse(map['last_updated'])
          : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1 || map['is_deleted'] == true,
    );
  }

  /// تحويل العملة إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء عملة من JSON
  factory Currency.fromJson(String source) =>
      Currency.fromMap(jsonDecode(source));

  /// تنسيق المبلغ حسب إعدادات العملة
  String formatAmount(double amount) {
    // تقريب المبلغ حسب عدد المنازل العشرية
    final roundedAmount = amount.toStringAsFixed(decimalPlaces);
    
    // تقسيم المبلغ إلى جزء صحيح وجزء عشري
    final parts = roundedAmount.split('.');
    final wholePart = parts[0];
    final decimalPart = parts.length > 1 ? parts[1] : '';
    
    // إضافة فواصل الآلاف للجزء الصحيح
    final formattedWholePart = _addThousandsSeparator(wholePart, thousandsSeparator ?? ',');
    
    // دمج الجزء الصحيح والجزء العشري
    final formattedAmount = decimalPart.isEmpty
        ? formattedWholePart
        : '$formattedWholePart${decimalSeparator ?? '.'}$decimalPart';
    
    // إضافة رمز العملة
    final space = spaceBetweenAmountAndSymbol ? ' ' : '';
    return symbolOnLeft
        ? '${symbol ?? ''}$space$formattedAmount'
        : '$formattedAmount$space${symbol ?? ''}';
  }
  
  /// إضافة فواصل الآلاف للرقم
  String _addThousandsSeparator(String number, String separator) {
    final result = StringBuffer();
    final length = number.length;
    
    for (var i = 0; i < length; i++) {
      if (i > 0 && (length - i) % 3 == 0) {
        result.write(separator);
      }
      result.write(number[i]);
    }
    
    return result.toString();
  }

  @override
  String toString() {
    return 'Currency(id: $id, code: $code, name: $name, symbol: $symbol)';
  }
}
