import 'package:flutter_test/flutter_test.dart';
import 'dart:convert';

// إنشاء نموذج مصغر للاختبار
class MockActivityLog {
  final String id;
  final String? userid;
  final String action;
  final String entityType;
  final String? entityId;
  final String? details;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  MockActivityLog({
    required this.id,
    this.userid,
    required this.action,
    required this.entityType,
    this.entityId,
    this.details,
    DateTime? timestamp,
    this.metadata,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userid,
      'action': action,
      'entity_type': entityType,
      'entity_id': entityId,
      'details': details,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata != null ? jsonEncode(metadata) : null,
    };
  }
}

class MockActivityLogService {
  final List<MockActivityLog> _logs = [];

  Future<String> logActivity({
    String? userid,
    required String action,
    required String entityType,
    String? entityId,
    String? details,
    Map<String, dynamic>? metadata,
  }) async {
    final id = 'test-${DateTime.now().millisecondsSinceEpoch}';

    final log = MockActivityLog(
      id: id,
      userid: userid,
      action: action,
      entityType: entityType,
      entityId: entityId,
      details: details,
      metadata: metadata,
    );

    _logs.add(log);
    return id;
  }

  Future<List<MockActivityLog>> getActivityLogs({
    int limit = 100,
    int offset = 0,
    String? userid,
    String? action,
    String? entityType,
    String? entityId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    var filteredLogs = _logs;

    if (userid != null) {
      filteredLogs = filteredLogs.where((log) => log.userid == userid).toList();
    }

    if (action != null) {
      filteredLogs = filteredLogs.where((log) => log.action == action).toList();
    }

    if (entityType != null) {
      filteredLogs =
          filteredLogs.where((log) => log.entityType == entityType).toList();
    }

    if (entityId != null) {
      filteredLogs =
          filteredLogs.where((log) => log.entityId == entityId).toList();
    }

    if (startDate != null) {
      filteredLogs = filteredLogs
          .where((log) => log.timestamp.isAfter(startDate))
          .toList();
    }

    if (endDate != null) {
      filteredLogs =
          filteredLogs.where((log) => log.timestamp.isBefore(endDate)).toList();
    }

    // تطبيق الترتيب والحد والإزاحة
    filteredLogs.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    if (offset >= filteredLogs.length) {
      return [];
    }

    final end = (offset + limit) > filteredLogs.length
        ? filteredLogs.length
        : (offset + limit);
    return filteredLogs.sublist(offset, end);
  }

  Future<int> deleteOldLogs(DateTime olderThan) async {
    final initialCount = _logs.length;
    _logs.removeWhere((log) => log.timestamp.isBefore(olderThan));
    return initialCount - _logs.length;
  }

  Future<int> clearAllLogs() async {
    final count = _logs.length;
    _logs.clear();
    return count;
  }

  Future<String> exportLogsToJson({
    String? userid,
    String? action,
    String? entityType,
    String? entityId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final logs = await getActivityLogs(
      userid: userid,
      action: action,
      entityType: entityType,
      entityId: entityId,
      startDate: startDate,
      endDate: endDate,
    );

    final List<Map<String, dynamic>> jsonLogs =
        logs.map((log) => log.toJson()).toList();
    return jsonEncode(jsonLogs);
  }

  Future<Map<String, dynamic>> getActivityStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final logs = await getActivityLogs(
      startDate: startDate,
      endDate: endDate,
    );

    if (logs.isEmpty) {
      return {
        'totalCount': 0,
        'actionCounts': {},
        'entityCounts': {},
        'startDate': startDate?.toIso8601String() ??
            DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        'endDate':
            endDate?.toIso8601String() ?? DateTime.now().toIso8601String(),
      };
    }

    // حساب عدد الإجراءات
    final Map<String, int> actionCounts = {};
    for (var log in logs) {
      actionCounts[log.action] = (actionCounts[log.action] ?? 0) + 1;
    }

    // حساب عدد أنواع الكيانات
    final Map<String, int> entityCounts = {};
    for (var log in logs) {
      entityCounts[log.entityType] = (entityCounts[log.entityType] ?? 0) + 1;
    }

    return {
      'totalCount': logs.length,
      'actionCounts': actionCounts,
      'entityCounts': entityCounts,
      'startDate': startDate?.toIso8601String() ??
          DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
      'endDate': endDate?.toIso8601String() ?? DateTime.now().toIso8601String(),
    };
  }
}

void main() {
  group('ActivityLogService Tests', () {
    late MockActivityLogService activityLogService;

    setUp(() {
      activityLogService = MockActivityLogService();
    });

    test('logActivity يقوم بتسجيل نشاط جديد', () async {
      // تسجيل نشاط جديد
      final id = await activityLogService.logActivity(
        userid: 'user123',
        action: 'create',
        entityType: 'product',
        entityId: 'product456',
        details: 'تم إنشاء منتج جديد',
        metadata: {'price': 100, 'quantity': 5},
      );

      // التحقق من وجود السجل في قاعدة البيانات
      final logs = await activityLogService.getActivityLogs(limit: 1);

      expect(logs.length, 1);
      expect(logs[0].id, id);
      expect(logs[0].userid, 'user123');
      expect(logs[0].action, 'create');
      expect(logs[0].entityType, 'product');
      expect(logs[0].entityId, 'product456');
      expect(logs[0].details, 'تم إنشاء منتج جديد');
      expect(logs[0].metadata?['price'], 100);
      expect(logs[0].metadata?['quantity'], 5);
    });

    test('getActivityLogs يقوم باسترجاع سجلات النشاط بناءً على المعايير',
        () async {
      // تسجيل عدة أنشطة
      await activityLogService.logActivity(
        userid: 'user123',
        action: 'create',
        entityType: 'product',
        entityId: 'product1',
      );

      await activityLogService.logActivity(
        userid: 'user123',
        action: 'update',
        entityType: 'product',
        entityId: 'product1',
      );

      await activityLogService.logActivity(
        userid: 'user456',
        action: 'create',
        entityType: 'customer',
        entityId: 'customer1',
      );

      // استرجاع السجلات بناءً على معايير مختلفة
      final allLogs = await activityLogService.getActivityLogs();
      expect(allLogs.length, 3);

      final userLogs =
          await activityLogService.getActivityLogs(userid: 'user123');
      expect(userLogs.length, 2);

      final actionLogs =
          await activityLogService.getActivityLogs(action: 'create');
      expect(actionLogs.length, 2);

      final entityLogs =
          await activityLogService.getActivityLogs(entityType: 'product');
      expect(entityLogs.length, 2);

      final combinedLogs = await activityLogService.getActivityLogs(
        userid: 'user123',
        action: 'update',
      );
      expect(combinedLogs.length, 1);
    });

    test('deleteOldLogs يقوم بحذف السجلات القديمة', () async {
      // تسجيل نشاط بتاريخ قديم
      final oldLog = MockActivityLog(
        id: 'old123',
        userid: 'user123',
        action: 'create',
        entityType: 'product',
        timestamp: DateTime.now().subtract(const Duration(days: 100)),
      );

      activityLogService._logs.add(oldLog);

      // تسجيل نشاط بتاريخ حديث
      await activityLogService.logActivity(
        userid: 'user123',
        action: 'create',
        entityType: 'product',
      );

      // التحقق من وجود سجلين
      final initialLogs = await activityLogService.getActivityLogs();
      expect(initialLogs.length, 2);

      // حذف السجلات الأقدم من 30 يوم
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final deletedCount = await activityLogService.deleteOldLogs(cutoffDate);

      // التحقق من حذف سجل واحد
      expect(deletedCount, 1);

      // التحقق من بقاء السجل الحديث فقط
      final remainingLogs = await activityLogService.getActivityLogs();
      expect(remainingLogs.length, 1);
      expect(remainingLogs[0].id != 'old123', true);
    });

    test('clearAllLogs يقوم بحذف جميع السجلات', () async {
      // تسجيل عدة أنشطة
      await activityLogService.logActivity(
        action: 'create',
        entityType: 'product',
      );

      await activityLogService.logActivity(
        action: 'update',
        entityType: 'product',
      );

      // التحقق من وجود سجلين
      final initialLogs = await activityLogService.getActivityLogs();
      expect(initialLogs.length, 2);

      // حذف جميع السجلات
      final deletedCount = await activityLogService.clearAllLogs();

      // التحقق من حذف سجلين
      expect(deletedCount, 2);

      // التحقق من عدم وجود سجلات
      final remainingLogs = await activityLogService.getActivityLogs();
      expect(remainingLogs.length, 0);
    });

    test('exportLogsToJson يقوم بتصدير السجلات بتنسيق JSON', () async {
      // تسجيل نشاط
      await activityLogService.logActivity(
        userid: 'user123',
        action: 'create',
        entityType: 'product',
        entityId: 'product1',
        details: 'تفاصيل المنتج',
      );

      // تصدير السجلات بتنسيق JSON
      final jsonData = await activityLogService.exportLogsToJson();

      // التحقق من صحة البيانات المصدرة
      final List<dynamic> exportedLogs = jsonDecode(jsonData);
      expect(exportedLogs.length, 1);
      expect(exportedLogs[0]['user_id'], 'user123');
      expect(exportedLogs[0]['action'], 'create');
      expect(exportedLogs[0]['entity_type'], 'product');
      expect(exportedLogs[0]['entity_id'], 'product1');
      expect(exportedLogs[0]['details'], 'تفاصيل المنتج');
    });

    test('getActivityStats يقوم بحساب إحصائيات سجلات النشاط', () async {
      // تسجيل عدة أنشطة
      await activityLogService.logActivity(
        action: 'create',
        entityType: 'product',
      );

      await activityLogService.logActivity(
        action: 'update',
        entityType: 'product',
      );

      await activityLogService.logActivity(
        action: 'create',
        entityType: 'customer',
      );

      // الحصول على الإحصائيات
      final stats = await activityLogService.getActivityStats();

      // التحقق من صحة الإحصائيات
      expect(stats['totalCount'], 3);
      expect(stats['actionCounts']['create'], 2);
      expect(stats['actionCounts']['update'], 1);
      expect(stats['entityCounts']['product'], 2);
      expect(stats['entityCounts']['customer'], 1);
    });
  });
}
