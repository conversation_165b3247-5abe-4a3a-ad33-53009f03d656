import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/utils/index.dart';
import '../../../core/widgets/index.dart';
import '../presenters/currency_presenter.dart';
import '../../../core/widgets/data_table_widget.dart';
import '../../../core/theme/index.dart';

class CurrencyReportsScreen extends StatefulWidget {
  const CurrencyReportsScreen({Key? key}) : super(key: key);

  @override
  State<CurrencyReportsScreen> createState() => _CurrencyReportsScreenState();
}

class _CurrencyReportsScreenState extends State<CurrencyReportsScreen> {
  final TextEditingController _searchController = TextEditingController();

  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String? _selectedCurrencyId;
  String _selectedReportType = 'all'; // 'all', 'buy', 'sell'
  bool _isLoading = false;
  List<Map<String, dynamic>> _transactions = [];
  bool _showSearchField = false;

  // استخدام التحميل الكسول
  late final CurrencyPresenter _currencyPresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _currencyPresenter = AppProviders.getLazyPresenter<CurrencyPresenter>(
        () => CurrencyPresenter());
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل العملات
      await _currencyPresenter.loadCurrencies();

      // تحميل المعاملات (هذا مجرد مثال، يجب تنفيذ هذا بشكل صحيح)
      _loadTransactions();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل البيانات: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _loadTransactions() {
    // هذه دالة وهمية لتحميل المعاملات
    // في التطبيق الحقيقي، يجب استرداد هذه البيانات من قاعدة البيانات
    setState(() {
      _transactions = [
        {
          'id': '1',
          'date': DateTime.now().subtract(const Duration(days: 1)),
          'fromCurrency': 'دولار أمريكي',
          'toCurrency': 'ريال يمني',
          'amount': 100.0,
          'rate': 500.0,
          'total': 50000.0,
          'type': 'شراء',
        },
        {
          'id': '2',
          'date': DateTime.now().subtract(const Duration(days: 2)),
          'fromCurrency': 'يورو',
          'toCurrency': 'ريال يمني',
          'amount': 50.0,
          'rate': 550.0,
          'total': 27500.0,
          'type': 'بيع',
        },
        {
          'id': '3',
          'date': DateTime.now().subtract(const Duration(days: 3)),
          'fromCurrency': 'دولار أمريكي',
          'toCurrency': 'ريال يمني',
          'amount': 200.0,
          'rate': 505.0,
          'total': 101000.0,
          'type': 'شراء',
        },
        {
          'id': '4',
          'date': DateTime.now().subtract(const Duration(days: 4)),
          'fromCurrency': 'ريال سعودي',
          'toCurrency': 'ريال يمني',
          'amount': 500.0,
          'rate': 133.0,
          'total': 66500.0,
          'type': 'بيع',
        },
      ];
    });
  }

  List<Map<String, dynamic>> _getFilteredTransactions() {
    return _transactions.where((transaction) {
      final date = transaction['date'] as DateTime;
      final isInDateRange =
          date.isAfter(_startDate.subtract(const Duration(days: 1))) &&
              date.isBefore(_endDate.add(const Duration(days: 1)));

      final matchesType = _selectedReportType == 'all' ||
          (_selectedReportType == 'buy' && transaction['type'] == 'شراء') ||
          (_selectedReportType == 'sell' && transaction['type'] == 'بيع');

      final matchesCurrency = _selectedCurrencyId == null ||
          transaction['fromCurrency'] == _selectedCurrencyId ||
          transaction['toCurrency'] == _selectedCurrencyId;

      final matchesSearch = _searchController.text.isEmpty ||
          transaction['fromCurrency']
              .toString()
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()) ||
          transaction['toCurrency']
              .toString()
              .toLowerCase()
              .contains(_searchController.text.toLowerCase());

      return isInDateRange && matchesType && matchesCurrency && matchesSearch;
    }).toList();
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // تأكد من أن تاريخ البداية لا يتجاوز تاريخ النهاية
          if (_startDate.isAfter(_endDate)) {
            _endDate = _startDate;
          }
        } else {
          _endDate = picked;
          // تأكد من أن تاريخ النهاية لا يسبق تاريخ البداية
          if (_endDate.isBefore(_startDate)) {
            _startDate = _endDate;
          }
        }
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // استخدام المتغير المحلي
    final filteredTransactions = _getFilteredTransactions();

    // حساب الإجماليات
    double totalBuy = 0;
    double totalSell = 0;

    for (var transaction in filteredTransactions) {
      if (transaction['type'] == 'شراء') {
        totalBuy += transaction['total'] as double;
      } else {
        totalSell += transaction['total'] as double;
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير العملات'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          // أيقونة البحث
          IconButton(
            icon: Icon(_showSearchField ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearchField = !_showSearchField;
                if (!_showSearchField) {
                  _searchController.clear();
                }
              });
            },
          ),
          // أيقونة الطباعة
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () {
              // تنفيذ طباعة التقرير
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('جاري تحضير التقرير للطباعة...')),
              );
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // حقل البحث (يظهر فقط عند الضغط على أيقونة البحث)
                if (_showSearchField)
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: TextField(
                      controller: _searchController,
                      decoration: const InputDecoration(
                        hintText: 'بحث في التقرير...',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        setState(() {
                          // سيتم تحديث القائمة تلقائيًا عند استدعاء _getFilteredTransactions
                        });
                      },
                    ),
                  ),

                // فلاتر التقرير
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Card(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: Text(
                            'فلاتر التقرير',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ),

                        // الفترة الزمنية
                        Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectDate(context, true),
                                child: InputDecorator(
                                  decoration: InputDecoration(
                                    labelText: 'من تاريخ',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          Layout.defaultRadius),
                                    ),
                                    filled: true,
                                    prefixIcon:
                                        const Icon(Icons.calendar_today),
                                  ),
                                  child: Text(
                                    '${_startDate.year}-${_startDate.month.toString().padLeft(2, '0')}-${_startDate.day.toString().padLeft(2, '0')}',
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectDate(context, false),
                                child: InputDecorator(
                                  decoration: InputDecoration(
                                    labelText: 'إلى تاريخ',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          Layout.defaultRadius),
                                    ),
                                    filled: true,
                                    prefixIcon:
                                        const Icon(Icons.calendar_today),
                                  ),
                                  child: Text(
                                    '${_endDate.year}-${_endDate.month.toString().padLeft(2, '0')}-${_endDate.day.toString().padLeft(2, '0')}',
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppDimensions.spacing8),

                        // نوع المعاملة والعملة
                        Row(
                          children: [
                            Expanded(
                              child: DropdownButtonFormField<String>(
                                value: _selectedReportType,
                                decoration: InputDecoration(
                                  labelText: 'نوع المعاملة',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                        Layout.defaultRadius),
                                  ),
                                  filled: true,
                                  prefixIcon: const Icon(Icons.category),
                                ),
                                items: const [
                                  DropdownMenuItem<String>(
                                    value: 'all',
                                    child: Text('الكل'),
                                  ),
                                  DropdownMenuItem<String>(
                                    value: 'buy',
                                    child: Text('شراء'),
                                  ),
                                  DropdownMenuItem<String>(
                                    value: 'sell',
                                    child: Text('بيع'),
                                  ),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _selectedReportType = value!;
                                  });
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: DropdownButtonFormField<String?>(
                                value: _selectedCurrencyId,
                                decoration: InputDecoration(
                                  labelText: 'العملة',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                        Layout.defaultRadius),
                                  ),
                                  filled: true,
                                  prefixIcon:
                                      const Icon(Icons.currency_exchange),
                                ),
                                items: [
                                  const DropdownMenuItem<String?>(
                                    value: null,
                                    child: Text('كل العملات'),
                                  ),
                                  ..._currencyPresenter.currencies
                                      .map((currency) {
                                    return DropdownMenuItem<String?>(
                                      value: currency.name,
                                      child: Text(currency.name),
                                    );
                                  }).toList(),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _selectedCurrencyId = value;
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // ملخص التقرير
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Card(
                          color: AppColors.successLight,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              children: [
                                const Text('إجمالي المبيعات'),
                                const SizedBox(height: AppDimensions.spacing4),
                                Text(
                                  totalSell.toStringAsFixed(2),
                                  style: const AppTypography(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Card(
                          color: AppColors.errorLight,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              children: [
                                const Text('إجمالي المشتريات'),
                                const SizedBox(height: AppDimensions.spacing4),
                                Text(
                                  totalBuy.toStringAsFixed(2),
                                  style: const AppTypography(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Card(
                          color: AppColors.infoLight,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              children: [
                                const Text('الصافي'),
                                const SizedBox(height: AppDimensions.spacing4),
                                Text(
                                  (totalSell - totalBuy).toStringAsFixed(2),
                                  style: const AppTypography(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // جدول المعاملات
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Card(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              'تفاصيل المعاملات (${filteredTransactions.length})',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                          Expanded(
                            child: AdvancedDataTable(
                              columns: const [
                                DataColumn(label: Text('م')),
                                DataColumn(label: Text('التاريخ')),
                                DataColumn(label: Text('النوع')),
                                DataColumn(label: Text('من')),
                                DataColumn(label: Text('إلى')),
                                DataColumn(label: Text('المبلغ')),
                                DataColumn(label: Text('السعر')),
                                DataColumn(label: Text('الإجمالي')),
                              ],
                              rows: filteredTransactions
                                  .asMap()
                                  .entries
                                  .map((entry) {
                                final transaction = entry.value;
                                return DataRow(
                                  color: transaction['type'] == 'شراء'
                                      ? WidgetStateProperty.all(
                                          AppColors.errorLight)
                                      : WidgetStateProperty.all(
                                          AppColors.successLight),
                                  cells: [
                                    DataCell(Text('${entry.key + 1}')),
                                    DataCell(Text(
                                        '${(transaction['date'] as DateTime).year}-${(transaction['date'] as DateTime).month.toString().padLeft(2, '0')}-${(transaction['date'] as DateTime).day.toString().padLeft(2, '0')}')),
                                    DataCell(
                                        Text(transaction['type'] as String)),
                                    DataCell(Text(
                                        transaction['fromCurrency'] as String)),
                                    DataCell(Text(
                                        transaction['toCurrency'] as String)),
                                    DataCell(Text(
                                        (transaction['amount'] as double)
                                            .toStringAsFixed(2))),
                                    DataCell(Text(
                                        (transaction['rate'] as double)
                                            .toStringAsFixed(2))),
                                    DataCell(Text(
                                        (transaction['total'] as double)
                                            .toStringAsFixed(2))),
                                  ],
                                );
                              }).toList(),
                              isLoading: false,
                              showCellBorder: true,
                              zebraPattern: true,
                              headerBackgroundColor: AppColors.primary,
                              headerTextColor: AppColors.onPrimary,
                              showRowNumbers: false,
                              emptyMessage:
                                  'لا توجد معاملات تطابق الفلاتر المحددة',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
