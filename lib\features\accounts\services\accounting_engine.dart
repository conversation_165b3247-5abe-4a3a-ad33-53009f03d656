import 'package:uuid/uuid.dart';
import 'package:intl/intl.dart';
import 'dart:convert';

import '../../../core/database/database_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../services/journal_entry_service.dart';
import '../services/accounting_posting_service.dart';
import '../../users/services/activity_log_service.dart';

/// أنواع الإجراءات
enum ActionType {
  create,
  update,
  delete,
  view,
  post,
  unpost,
  approve,
  reject,
  cancel,
  restore,
  export,
  import,
  login,
  logout,
  sync,
}

/// أنواع الكيانات
enum EntityType {
  user,
  role,
  permission,
  account,
  customer,
  supplier,
  product,
  inventory,
  sale,
  purchase,
  payment,
  receipt,
  journalEntry,
  invoice,
  report,
  setting,
  backup,
}

/// أنواع القيود المحاسبية
enum JournalEntryType {
  /// قيد عادي
  standard,

  /// قيد فاتورة مبيعات
  salesInvoice,

  /// قيد فاتورة مشتريات
  purchaseInvoice,

  /// قيد إيصال قبض
  receipt,

  /// قيد إيصال دفع
  payment,

  /// قيد مخزون
  inventory,

  /// قيد تسوية
  adjustment,

  /// قيد افتتاحي
  opening,

  /// قيد إقفال
  closing,
}

/// حالة القيد المحاسبي
enum JournalEntryStatus {
  /// مسودة
  draft,

  /// معتمد
  posted,

  /// معلق
  suspended,

  /// ملغي
  canceled,
}

/// بند في القيد المحاسبي
class JournalEntryLine {
  final String accountId;
  final String description;
  final double debit;
  final double credit;

  JournalEntryLine({
    required this.accountId,
    required this.description,
    required this.debit,
    required this.credit,
  });

  Map<String, dynamic> toJson() {
    return {
      'accountId': accountId,
      'description': description,
      'debit': debit,
      'credit': credit,
    };
  }
}

/// عنصر في فاتورة مبيعات
class SalesInvoiceItem {
  final String productId;
  final String description;
  final double quantity;
  final double unitPrice;
  final double discount;
  final double tax;
  final double total;

  SalesInvoiceItem({
    required this.productId,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    this.discount = 0,
    this.tax = 0,
    required this.total,
  });

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'description': description,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'discount': discount,
      'tax': tax,
      'total': total,
    };
  }
}

/// عنصر في فاتورة مشتريات
/// هذا النموذج مستخدم فقط داخل محرك المحاسبة
class PurchaseInvoiceItem {
  final String productId;
  final String description;
  final double quantity;
  final double unitPrice;
  final double discount;
  final double tax;
  final double total;

  PurchaseInvoiceItem({
    required this.productId,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    this.discount = 0,
    this.tax = 0,
    required this.total,
  });

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'description': description,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'discount': discount,
      'tax': tax,
      'total': total,
    };
  }
}

/// محرك المحاسبة
/// يقوم بالعمليات المحاسبية الأساسية وإنشاء القيود المحاسبية
class AccountingEngine {
  static final AccountingEngine _instance = AccountingEngine._internal();
  factory AccountingEngine() => _instance;
  AccountingEngine._internal();

  final AccountService _accountService = AccountService();
  final ActivityLogService _logService = ActivityLogService();
  final DatabaseService _dbService = DatabaseService.instance;
  final Uuid _uuid = const Uuid();
  final JournalEntryService _journalService = JournalEntryService();
  final AccountingPostingService _postingService = AccountingPostingService();

  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  /// إنشاء قيد محاسبي
  Future<String> createJournalEntry({
    required DateTime date,
    required String reference,
    required String description,
    List<Map<String, dynamic>>? debitAccounts,
    List<Map<String, dynamic>>? creditAccounts,
    double? amount,
    String? customerAccountId,
    String? supplierAccountId,
    String? cashAccountId,
    String? paymentMethodAccountId,
    Map<String, dynamic>? metadata,
    String? userId,
  }) async {
    try {
      // التحقق من وجود القيم المطلوبة
      if ((debitAccounts == null || debitAccounts.isEmpty) &&
          (creditAccounts == null || creditAccounts.isEmpty)) {
        throw Exception('يجب توفير حسابات المدين أو الدائن للقيد المحاسبي');
      }

      // تجهيز خطوط القيد
      final List<Map<String, dynamic>> lines = [];

      // إضافة حسابات المدين (Debit)
      if (debitAccounts != null) {
        for (var account in debitAccounts) {
          lines.add({
            'accountId': account['accountId'],
            'description': account['description'] ?? description,
            'debit': account['amount'],
            'credit': 0.0,
          });
        }
      }

      // إضافة حسابات الدائن (Credit)
      if (creditAccounts != null) {
        for (var account in creditAccounts) {
          lines.add({
            'accountId': account['accountId'],
            'description': account['description'] ?? description,
            'debit': 0.0,
            'credit': account['amount'],
          });
        }
      }

      // إنشاء بيانات وصفية للقيد
      final Map<String, dynamic> entryMetadata = metadata ?? {};

      // إضافة معلومات إضافية للبيانات الوصفية
      if (customerAccountId != null) {
        entryMetadata['customerAccountId'] = customerAccountId;
      }

      if (supplierAccountId != null) {
        entryMetadata['supplierAccountId'] = supplierAccountId;
      }

      if (cashAccountId != null) {
        entryMetadata['cashAccountId'] = cashAccountId;
      }

      if (paymentMethodAccountId != null) {
        entryMetadata['paymentMethodAccountId'] = paymentMethodAccountId;
      }

      // إنشاء القيد المحاسبي باستخدام خدمة القيود
      return await _journalService.createJournalEntry(
        date: date,
        reference: reference,
        description: description,
        lines: lines,
        userId: userId,
        metadata: entryMetadata,
      );
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إنشاء القيد المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {
          'reference': reference,
          'description': description,
        },
      );
      rethrow;
    }
  }

  /// إلغاء قيد محاسبي (وضع علامة محذوف)
  Future<bool> cancelJournalEntry(String entryId, {String? userId}) async {
    try {
      // إلغاء ترحيل القيد أولاً إذا كان مرحلاً
      await _postingService.unpostJournalEntry(entryId, userId: userId);

      // ثم إلغاء القيد (وضع علامة محذوف)
      return await _journalService.cancelJournalEntry(entryId, userId: userId);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إلغاء القيد المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'entryId': entryId},
      );
      return false;
    }
  }

  /// ترحيل قيد محاسبي
  Future<bool> postJournalEntry(String entryId, {String? userId}) async {
    try {
      return await _postingService.postJournalEntry(entryId, userId: userId);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في ترحيل القيد المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'entryId': entryId},
      );
      return false;
    }
  }

  /// إلغاء ترحيل قيد محاسبي
  Future<bool> unpostJournalEntry(String entryId, {String? userId}) async {
    try {
      return await _postingService.unpostJournalEntry(entryId, userId: userId);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إلغاء ترحيل القيد المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {'entryId': entryId},
      );
      return false;
    }
  }

  /// التحقق من توازن القيد المحاسبي
  bool isEntryBalanced(List<Map<String, dynamic>> lines) {
    double totalDebit = 0.0;
    double totalCredit = 0.0;

    for (var line in lines) {
      totalDebit += line['debit'] ?? 0.0;
      totalCredit += line['credit'] ?? 0.0;
    }

    // التحقق من المساواة مع هامش خطأ صغير بسبب الفاصلة العشرية
    return (totalDebit - totalCredit).abs() < 0.001;
  }

  /// إنشاء قيد محاسبي جديد
  /// * [date] تاريخ القيد
  /// * [reference] المرجع (مثل رقم الفاتورة)
  /// * [description] وصف القيد
  /// * [lines] بنود القيد
  /// * [type] نوع القيد
  /// * [status] حالة القيد (افتراضياً مسودة)
  /// * [metadata] بيانات إضافية
  /// * [userId] معرف المستخدم
  Future<String?> createLegacyJournalEntry({
    required DateTime date,
    required String reference,
    required String description,
    required List<JournalEntryLine> lines,
    JournalEntryType type = JournalEntryType.standard,
    JournalEntryStatus status = JournalEntryStatus.draft,
    Map<String, dynamic>? metadata,
    String? userId,
  }) async {
    try {
      // التأكد من توازن القيد
      if (!isEntryBalanced(lines.map((e) => e.toJson()).toList())) {
        throw Exception(
            'القيد المحاسبي غير متوازن. مجموع المدين يجب أن يساوي مجموع الدائن.');
      }

      // التأكد من وجود حسابين على الأقل
      if (lines.length < 2) {
        throw Exception(
            'يجب أن يحتوي القيد على حسابين على الأقل (مدين ودائن).');
      }

      final db = await _dbService.database;
      final entryId = _uuid.v4();
      final now = DateTime.now().toIso8601String();
      final userIdToUse = userId ?? 'system';

      // إنشاء رأس القيد
      await db.insert(
        'journal_entries',
        {
          'id': entryId,
          'date': _dateFormat.format(date),
          'reference': reference,
          'description': description,
          'type': type.toString().split('.').last,
          'status': JournalEntryStatus.draft
              .toString()
              .split('.')
              .last, // دائماً نبدأ بحالة مسودة
          'metadata': metadata != null ? jsonEncode(metadata) : null,
          'created_at': now,
          'created_by': userIdToUse,
        },
      );

      // إنشاء تفاصيل القيد
      for (final line in lines) {
        await db.insert(
          'journal_entry_lines',
          {
            'id': _uuid.v4(),
            'journal_entry_id': entryId,
            'account_id': line.accountId,
            'description': line.description,
            'debit': line.debit,
            'credit': line.credit,
            'created_at': now,
          },
        );
      }

      // تسجيل النشاط
      await _logService.logActivity(
        userId: userIdToUse,
        userName: 'النظام',
        action: 'create',
        module: 'journal_entry',
        details: 'إنشاء قيد محاسبي: $reference - $description',
      );

      // إذا كان القيد يجب أن يكون مرحلاً، نقوم بترحيله باستخدام خدمة الترحيل المحاسبي
      if (status == JournalEntryStatus.posted) {
        final success = await _postingService.postJournalEntry(entryId,
            userId: userIdToUse);
        if (!success) {
          AppLogger.error('فشل في ترحيل القيد المحاسبي: $entryId');
          // لا نقوم بإلغاء إنشاء القيد، بل نتركه في حالة مسودة
        }
      }

      return entryId;
    } catch (e) {
      AppLogger.error('خطأ في إنشاء القيد المحاسبي: $e');
      return null;
    }
  }

  /// إنشاء قيد مبيعات
  Future<String?> createSalesInvoiceEntry({
    required DateTime date,
    required String reference,
    required String description,
    required String customerAccountId,
    required double amount,
    required double taxAmount,
    required List<SalesInvoiceItem> items,
    Map<String, dynamic>? metadata,
    String? userId,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (amount <= 0) {
        throw Exception('يجب أن يكون مبلغ الفاتورة أكبر من صفر');
      }

      if (customerAccountId.isEmpty) {
        throw Exception('معرّف حساب العميل مطلوب');
      }

      if (items.isEmpty) {
        throw Exception('يجب أن تحتوي الفاتورة على الأقل على عنصر واحد');
      }

      if (reference.isEmpty) {
        throw Exception('يجب توفير رقم مرجعي للفاتورة');
      }

      // التحقق من وجود حساب العميل
      final customerAccount =
          await _accountService.getAccountById(customerAccountId);
      if (customerAccount == null) {
        throw Exception('حساب العميل غير موجود: $customerAccountId');
      }

      // التأكد من توفر الحسابات الافتراضية
      final salesAccount = await _accountService.getAccountByCode('4110');
      if (salesAccount == null) {
        throw Exception('لم يتم العثور على حساب المبيعات (كود: 4110)');
      }

      final cosAccount = await _accountService.getAccountByCode('5110');
      if (cosAccount == null) {
        throw Exception('لم يتم العثور على حساب تكلفة المبيعات (كود: 5110)');
      }

      final inventoryAccount = await _accountService.getAccountByCode('1131');
      if (inventoryAccount == null) {
        throw Exception('لم يتم العثور على حساب المخزون (كود: 1131)');
      }

      // حساب ضريبة القيمة المضافة إذا كان مبلغ الضريبة أكبر من الصفر
      String? vatAccountId;
      if (taxAmount > 0) {
        final vatAccount = await _accountService.getAccountByCode('2123');
        vatAccountId = vatAccount?.id;
      }

      // تسجيل معلومات العملية
      AppLogger.info(
          'إنشاء قيد فاتورة مبيعات: المبلغ $amount، الضريبة $taxAmount، العميل ${customerAccount.name}');

      // تحضير سطور القيد المحاسبي
      final lines = <JournalEntryLine>[
        // مدين - حساب العميل
        JournalEntryLine(
          accountId: customerAccountId,
          debit: amount + taxAmount,
          credit: 0,
          description: 'فاتورة مبيعات: $reference',
        ),
        // دائن - حساب المبيعات
        JournalEntryLine(
          accountId: salesAccount.id,
          debit: 0,
          credit: amount,
          description: 'إيرادات المبيعات',
        ),
      ];

      // إضافة سطر الضريبة إذا كان هناك ضريبة
      if (taxAmount > 0 && vatAccountId != null) {
        lines.add(JournalEntryLine(
          accountId: vatAccountId,
          debit: 0,
          credit: taxAmount,
          description: 'ضريبة القيمة المضافة',
        ));
      }

      // إنشاء القيد المحاسبي
      final entryId = await createLegacyJournalEntry(
        date: date,
        reference: reference,
        description: description,
        lines: lines,
        type: JournalEntryType.salesInvoice,
        status: JournalEntryStatus.posted,
        metadata: {
          'invoiceType': 'sales',
          'itemsCount': items.length,
          ...metadata ?? {},
        },
        userId: userId,
      );

      if (entryId != null) {
        AppLogger.info('تم إنشاء قيد فاتورة المبيعات بنجاح: $entryId');
      }

      return entryId;
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في إنشاء قيد فاتورة المبيعات: $e');
      ErrorTracker.captureError(
        'خطأ في إنشاء قيد فاتورة المبيعات',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// إنشاء قيد مشتريات
  Future<String?> createPurchaseInvoiceEntry({
    required DateTime date,
    required String reference,
    required String description,
    required String supplierAccountId,
    required double amount,
    required double taxAmount,
    required List<PurchaseInvoiceItem> items,
    Map<String, dynamic>? metadata,
    String? userId,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (amount <= 0) {
        throw Exception('يجب أن يكون مبلغ الفاتورة أكبر من صفر');
      }

      if (supplierAccountId.isEmpty) {
        throw Exception('معرّف حساب المورد مطلوب');
      }

      if (items.isEmpty) {
        throw Exception('يجب أن تحتوي الفاتورة على الأقل على عنصر واحد');
      }

      if (reference.isEmpty) {
        throw Exception('يجب توفير رقم مرجعي للفاتورة');
      }

      // التحقق من وجود حساب المورد
      final supplierAccount =
          await _accountService.getAccountById(supplierAccountId);
      if (supplierAccount == null) {
        throw Exception('حساب المورد غير موجود: $supplierAccountId');
      }

      // التأكد من توفر حسابات افتراضية
      final defaultInventoryAccount =
          await _accountService.getAccountByCode('1131');
      if (defaultInventoryAccount == null) {
        throw Exception('لم يتم العثور على حساب المخزون الافتراضي (كود: 1131)');
      }

      // حساب ضريبة المدخلات إذا كان مبلغ الضريبة أكبر من الصفر
      String? vatInputAccountId;
      if (taxAmount > 0) {
        final vatInputAccount = await _accountService.getAccountByCode('1143');
        vatInputAccountId = vatInputAccount?.id;
      }

      // تسجيل معلومات العملية
      AppLogger.info(
          'إنشاء قيد فاتورة مشتريات: المبلغ $amount، الضريبة $taxAmount، المورد ${supplierAccount.name}');

      // تحضير سطور القيد المحاسبي
      final lines = <JournalEntryLine>[
        // مدين - حساب المشتريات
        JournalEntryLine(
          accountId: defaultInventoryAccount.id,
          debit: amount,
          credit: 0,
          description: 'مشتريات من المورد',
        ),
        // دائن - حساب المورد
        JournalEntryLine(
          accountId: supplierAccountId,
          debit: 0,
          credit: amount + taxAmount,
          description: 'فاتورة مشتريات: $reference',
        ),
      ];

      // إضافة سطر الضريبة إذا كان هناك ضريبة
      if (taxAmount > 0 && vatInputAccountId != null) {
        lines.add(JournalEntryLine(
          accountId: vatInputAccountId,
          debit: taxAmount,
          credit: 0,
          description: 'ضريبة المدخلات',
        ));
      }

      // إنشاء القيد المحاسبي
      final entryId = await createLegacyJournalEntry(
        date: date,
        reference: reference,
        description: description,
        lines: lines,
        type: JournalEntryType.purchaseInvoice,
        status: JournalEntryStatus.posted,
        metadata: {
          'invoiceType': 'purchase',
          'itemsCount': items.length,
          ...metadata ?? {},
        },
        userId: userId,
      );

      if (entryId != null) {
        AppLogger.info('تم إنشاء قيد فاتورة المشتريات بنجاح: $entryId');
      }

      return entryId;
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في إنشاء قيد فاتورة المشتريات: $e');
      ErrorTracker.captureError(
        'خطأ في إنشاء قيد فاتورة المشتريات',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// إنشاء قيد محاسبي للحركة المخزنية
  Future<String?> createInventoryMovementEntry({
    required DateTime date,
    required String reference,
    required String description,
    required double cost,
    required String inventoryAccountId,
    required String contraAccountId,
    Map<String, dynamic>? metadata,
    String? userId,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (cost <= 0) {
        throw Exception('يجب أن تكون تكلفة المخزون أكبر من صفر');
      }

      // التحقق من وجود الحسابات
      final inventoryAccount =
          await _accountService.getAccountById(inventoryAccountId);
      if (inventoryAccount == null) {
        throw Exception('حساب المخزون غير موجود');
      }

      final contraAccount =
          await _accountService.getAccountById(contraAccountId);
      if (contraAccount == null) {
        throw Exception('الحساب المقابل غير موجود');
      }

      // إنشاء عنوان مناسب
      final String entryDescription =
          description.isNotEmpty ? description : 'حركة مخزنية - $reference';

      // إنشاء سطور القيد المحاسبي
      final lines = <JournalEntryLine>[
        // مدين - حساب المخزون
        JournalEntryLine(
          accountId: inventoryAccountId,
          debit: cost,
          credit: 0,
          description: 'إضافة للمخزون: $reference',
        ),
        // دائن - الحساب المقابل
        JournalEntryLine(
          accountId: contraAccountId,
          debit: 0,
          credit: cost,
          description: 'تكلفة المخزون: $reference',
        ),
      ];

      // إنشاء القيد المحاسبي
      return createLegacyJournalEntry(
        date: date,
        reference: reference,
        description: entryDescription,
        lines: lines,
        type: JournalEntryType.inventory,
        status: JournalEntryStatus.posted,
        metadata: metadata,
        userId: userId,
      );
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في إنشاء قيد الحركة المخزنية: $e');
      ErrorTracker.captureError(
        'خطأ في إنشاء قيد الحركة المخزنية',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// إنشاء قيد استلام (قبض)
  Future<String?> createReceiptEntry({
    required DateTime date,
    required String reference,
    required String customerAccountId,
    required String cashAccountId,
    required double amount,
    String? description,
    Map<String, dynamic>? metadata,
    String? userId,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (amount <= 0) {
        throw Exception('يجب أن يكون المبلغ أكبر من صفر');
      }

      if (customerAccountId.isEmpty) {
        throw Exception('معرّف حساب العميل مطلوب');
      }

      if (cashAccountId.isEmpty) {
        throw Exception('معرّف حساب النقدية مطلوب');
      }

      // التحقق من وجود الحسابات
      final customerAccount =
          await _accountService.getAccountById(customerAccountId);
      if (customerAccount == null) {
        throw Exception('حساب العميل غير موجود: $customerAccountId');
      }

      final cashAccount = await _accountService.getAccountById(cashAccountId);
      if (cashAccount == null) {
        throw Exception('حساب النقدية غير موجود: $cashAccountId');
      }

      // إنشاء وصف افتراضي إذا لم يتم توفيره
      final String customerName = customerAccount.name;
      final effectiveDescription = description ??
          (customerName.isEmpty
              ? 'استلام دفعة من عميل'
              : 'استلام دفعة من $customerName');

      // تسجيل النشاط
      AppLogger.info(
          'إنشاء قيد استلام: $amount من $customerAccountId إلى $cashAccountId');

      // إنشاء سطور القيد المحاسبي
      final lines = <JournalEntryLine>[
        // مدين - حساب الصندوق
        JournalEntryLine(
          accountId: cashAccountId,
          debit: amount,
          credit: 0,
          description: 'استلام نقدية من العميل',
        ),
        // دائن - حساب العميل
        JournalEntryLine(
          accountId: customerAccountId,
          debit: 0,
          credit: amount,
          description: 'تسديد رصيد فاتورة',
        ),
      ];

      // إنشاء القيد المحاسبي
      return createLegacyJournalEntry(
        date: date,
        reference: reference,
        description: effectiveDescription,
        lines: lines,
        type: JournalEntryType.receipt,
        status: JournalEntryStatus.posted,
        metadata: metadata,
        userId: userId,
      );
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في إنشاء قيد استلام: $e');
      ErrorTracker.captureError(
        'خطأ في إنشاء قيد استلام',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// إنشاء قيد دفع (صرف)
  Future<String?> createPaymentEntry({
    required DateTime date,
    required String reference,
    required String supplierAccountId,
    required String paymentMethodAccountId,
    required double amount,
    String? description,
    Map<String, dynamic>? metadata,
    String? userId,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (amount <= 0) {
        throw Exception('يجب أن يكون المبلغ أكبر من صفر');
      }

      if (supplierAccountId.isEmpty) {
        throw Exception('معرّف حساب المورد مطلوب');
      }

      if (paymentMethodAccountId.isEmpty) {
        throw Exception('معرّف حساب طريقة الدفع مطلوب');
      }

      // التحقق من وجود الحسابات
      final supplierAccount =
          await _accountService.getAccountById(supplierAccountId);
      if (supplierAccount == null) {
        throw Exception('حساب المورد غير موجود: $supplierAccountId');
      }

      final paymentMethodAccount =
          await _accountService.getAccountById(paymentMethodAccountId);
      if (paymentMethodAccount == null) {
        throw Exception('حساب طريقة الدفع غير موجود: $paymentMethodAccountId');
      }

      // إنشاء وصف افتراضي إذا لم يتم توفيره
      final String supplierName = supplierAccount.name;
      final effectiveDescription = description ??
          (supplierName.isEmpty
              ? 'دفع مبلغ لمورد'
              : 'دفع مبلغ إلى $supplierName');

      // تسجيل النشاط
      AppLogger.info(
          'إنشاء قيد دفع: $amount من $paymentMethodAccountId إلى $supplierAccountId');

      // إنشاء سطور القيد المحاسبي
      final lines = <JournalEntryLine>[
        // مدين - حساب المورد
        JournalEntryLine(
          accountId: supplierAccountId,
          debit: amount,
          credit: 0,
          description: 'تسديد رصيد فاتورة للمورد',
        ),
        // دائن - حساب طريقة الدفع (نقدية أو بنك)
        JournalEntryLine(
          accountId: paymentMethodAccountId,
          debit: 0,
          credit: amount,
          description: 'دفع مبلغ للمورد',
        ),
      ];

      // إنشاء القيد المحاسبي
      return createLegacyJournalEntry(
        date: date,
        reference: reference,
        description: effectiveDescription,
        lines: lines,
        type: JournalEntryType.payment,
        status: JournalEntryStatus.posted,
        metadata: metadata,
        userId: userId,
      );
    } catch (e, stackTrace) {
      AppLogger.error('خطأ في إنشاء قيد سداد دفعة: $e');
      ErrorTracker.captureError(
        'خطأ في إنشاء قيد سداد دفعة',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }
}
