import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;

/// فئة مساعدة لتسجيل الأحداث والأخطاء في التطبيق
class AppLogger {
  /// منع إنشاء نسخ من هذا الصف
  AppLogger._();

  /// تحكم في تفعيل/تعطيل التسجيل
  static bool _loggingEnabled = true; // تم تغييره إلى true لتفعيل التسجيل

  /// تفعيل أو تعطيل التسجيل
  static void setLoggingEnabled(bool enabled) {
    _loggingEnabled = enabled;
    debugPrint('🔄 تم تغيير حالة التسجيل إلى: $enabled');
  }

  /// تسجيل معلومات
  static void info(String message) {
    if (_loggingEnabled) {
      debugPrint('ℹ️ INFO: $message');
      developer.log(message, name: 'TajerPlus.INFO');
    }
  }

  /// تسجيل تحذير
  static void warning(String message) {
    if (_loggingEnabled) {
      debugPrint('⚠️ WARNING: $message');
      developer.log(message, name: 'TajerPlus.WARNING');
    }
  }

  /// تسجيل خطأ - نحتفظ بتسجيل الأخطاء الهامة فقط
  static void error(String message, {dynamic error, StackTrace? stackTrace}) {
    // دائماً نطبع الأخطاء بغض النظر عن حالة التسجيل
    debugPrint('❌ ERROR: $message');
    if (error != null) {
      debugPrint('❌ ERROR DETAILS: $error');
    }
    if (stackTrace != null) {
      debugPrint('❌ STACK TRACE: $stackTrace');
    }

    developer.log(
      message,
      error: error,
      stackTrace: stackTrace,
      name: 'TajerPlus.ERROR',
    );
  }

  /// تسجيل معلومات تصحيح
  static void debug(String message) {
    if (_loggingEnabled && kDebugMode) {
      debugPrint('🔍 DEBUG: $message');
      developer.log(message, name: 'TajerPlus.DEBUG');
    }
  }
}
