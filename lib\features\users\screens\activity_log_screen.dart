import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/index.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/widgets/index.dart';
import '../models/activity_log.dart';
import '../services/activity_log_service.dart';

/// شاشة سجل النشاطات
class ActivityLogScreen extends StatefulWidget {
  const ActivityLogScreen({Key? key}) : super(key: key);

  @override
  State<ActivityLogScreen> createState() => _ActivityLogScreenState();
}

class _ActivityLogScreenState extends State<ActivityLogScreen> {
  // حالة التحميل
  bool _isLoading = false;

  // قائمة النشاطات
  List<ActivityLog> _activities = [];

  // حالة الفلترة
  String _searchQuery = '';
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedUser;
  String? _selectedAction;
  String? _selectedModule;

  // وحدات تحكم البحث
  final TextEditingController _searchController = TextEditingController();

  // خدمة سجل النشاطات
  final ActivityLogService _activityLogService = ActivityLogService();

  @override
  void initState() {
    super.initState();

    // تعيين تاريخ البداية والنهاية الافتراضي (آخر 7 أيام)
    _endDate = DateTime.now();
    _startDate = _endDate!.subtract(const Duration(days: 7));

    // تحميل البيانات
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // استدعاء خدمة الحصول على سجل النشاطات
      _activities = await _activityLogService.getActivities(
        startDate: _startDate,
        endDate: _endDate,
        userId: null, // سيتم تطبيق فلتر المستخدم في الواجهة
        action: _selectedAction,
        module: _selectedModule,
      );

      AppLogger.info('تم تحميل ${_activities.length} نشاط');
    } catch (e, stackTrace) {
      AppLogger.error('فشل في تحميل سجل النشاطات: $e');
      ErrorTracker.captureError(
        'فشل في تحميل سجل النشاطات',
        error: e,
        stackTrace: stackTrace,
      );

      // عرض رسالة خطأ للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل سجل النشاطات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// فلترة النشاطات
  List<ActivityLog> _getFilteredActivities() {
    return _activities.where((activity) {
      // فلترة حسب البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!activity.userName.toLowerCase().contains(query) &&
            !activity.action.toLowerCase().contains(query) &&
            !activity.module.toLowerCase().contains(query) &&
            !activity.details.toLowerCase().contains(query)) {
          return false;
        }
      }

      // فلترة حسب التاريخ
      if (_startDate != null && activity.timestamp.isBefore(_startDate!)) {
        return false;
      }

      if (_endDate != null) {
        final endOfDay = DateTime(
            _endDate!.year, _endDate!.month, _endDate!.day, 23, 59, 59);
        if (activity.timestamp.isAfter(endOfDay)) {
          return false;
        }
      }

      // فلترة حسب المستخدم
      if (_selectedUser != null && activity.userName != _selectedUser) {
        return false;
      }

      // فلترة حسب الإجراء
      if (_selectedAction != null && activity.action != _selectedAction) {
        return false;
      }

      // فلترة حسب الوحدة
      if (_selectedModule != null && activity.module != _selectedModule) {
        return false;
      }

      return true;
    }).toList();
  }

  /// عرض مربع حوار اختيار التاريخ
  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppColors.primary,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadData();
    }
  }

  /// عرض مربع حوار اختيار المستخدم
  void _showUserPicker() {
    final users =
        _activities.map((activity) => activity.userName).toSet().toList();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار المستخدم'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              ListTile(
                title: const Text('الكل'),
                onTap: () {
                  setState(() {
                    _selectedUser = null;
                  });
                  Navigator.pop(context);
                },
              ),
              ...users.map((user) => ListTile(
                    title: Text(user),
                    selected: _selectedUser == user,
                    onTap: () {
                      setState(() {
                        _selectedUser = user;
                      });
                      Navigator.pop(context);
                    },
                  )),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض مربع حوار اختيار الإجراء
  void _showActionPicker() {
    final actions =
        _activities.map((activity) => activity.action).toSet().toList();

    // ترتيب الإجراءات أبجدياً
    actions.sort();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار الإجراء'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              ListTile(
                title: const Text('الكل'),
                onTap: () {
                  setState(() {
                    _selectedAction = null;
                  });
                  Navigator.pop(context);
                },
              ),
              ...actions.map((action) => ListTile(
                    title: Text(action),
                    selected: _selectedAction == action,
                    onTap: () {
                      setState(() {
                        _selectedAction = action;
                      });
                      Navigator.pop(context);
                    },
                  )),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض مربع حوار اختيار الوحدة
  void _showModulePicker() {
    final modules =
        _activities.map((activity) => activity.module).toSet().toList();

    // ترتيب الوحدات أبجدياً
    modules.sort();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار الوحدة'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              ListTile(
                title: const Text('الكل'),
                onTap: () {
                  setState(() {
                    _selectedModule = null;
                  });
                  Navigator.pop(context);
                },
              ),
              ...modules.map((module) => ListTile(
                    title: Text(module),
                    selected: _selectedModule == module,
                    onTap: () {
                      setState(() {
                        _selectedModule = module;
                      });
                      Navigator.pop(context);
                    },
                  )),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'سجل النشاطات',
        showBackButton: true,
        backgroundColor: DynamicColors.surface(context),
      ),
      backgroundColor: DynamicColors.background(context),
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child:
                _isLoading ? const AkLoadingIndicator() : _buildActivityList(),
          ),
        ],
      ),
    );
  }

  /// بناء شريط الفلترة
  Widget _buildFilterBar() {
    return Container(
      padding: AppDimensions.defaultPadding,
      color: DynamicColors.surfaceVariant(context),
      child: AkColumn(
        spacing: AkSpacingSize.medium,
        children: [
          // حقل البحث
          AkSearchInput(
            controller: _searchController,
            hint: 'بحث في سجل النشاطات...',
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),

          // أزرار الفلترة - الصف الأول
          AkRow(
            spacing: AkSpacingSize.small,
            children: [
              // زر اختيار التاريخ
              Expanded(
                child: AkButton(
                  text: _startDate != null && _endDate != null
                      ? '${DateFormat('yyyy/MM/dd').format(_startDate!)} - ${DateFormat('yyyy/MM/dd').format(_endDate!)}'
                      : 'اختيار التاريخ',
                  icon: Icons.date_range,
                  onPressed: _showDateRangePicker,
                  type: AkButtonType.primary,
                  size: AkButtonSize.small,
                ),
              ),

              // زر اختيار المستخدم
              Expanded(
                child: AkButton(
                  text: _selectedUser ?? 'جميع المستخدمين',
                  icon: Icons.person,
                  onPressed: _showUserPicker,
                  type: _selectedUser != null
                      ? AkButtonType.primary
                      : AkButtonType.secondary,
                  size: AkButtonSize.small,
                ),
              ),
            ],
          ),

          // أزرار الفلترة - الصف الثاني
          AkRow(
            spacing: AkSpacingSize.small,
            children: [
              // زر اختيار الإجراء
              Expanded(
                child: AkButton(
                  text: _selectedAction ?? 'جميع الإجراءات',
                  icon: Icons.category,
                  onPressed: _showActionPicker,
                  type: _selectedAction != null
                      ? AkButtonType.primary
                      : AkButtonType.secondary,
                  size: AkButtonSize.small,
                ),
              ),

              // زر اختيار الوحدة
              Expanded(
                child: AkButton(
                  text: _selectedModule ?? 'جميع الوحدات',
                  icon: Icons.dashboard,
                  onPressed: _showModulePicker,
                  type: _selectedModule != null
                      ? AkButtonType.primary
                      : AkButtonType.secondary,
                  size: AkButtonSize.small,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قائمة النشاطات
  Widget _buildActivityList() {
    final filteredActivities = _getFilteredActivities();

    if (filteredActivities.isEmpty) {
      return const AkEmptyState(
        message: 'لا توجد نشاطات مطابقة للبحث',
        icon: Icons.history,
      );
    }

    return ListView.builder(
      padding: AppDimensions.defaultPadding,
      itemCount: filteredActivities.length,
      itemBuilder: (context, index) {
        final activity = filteredActivities[index];
        return _buildActivityItem(activity);
      },
    );
  }

  /// بناء عنصر النشاط
  Widget _buildActivityItem(ActivityLog activity) {
    // تحديد لون الإجراء وأيقونته
    Color actionColor;
    IconData actionIcon;

    // تحديد اللون والأيقونة بناءً على نوع الإجراء
    if (activity.action.contains('login') ||
        activity.action.contains('تسجيل دخول')) {
      actionColor = AppColors.success;
      actionIcon = Icons.login;
    } else if (activity.action.contains('logout') ||
        activity.action.contains('تسجيل خروج')) {
      actionColor = AppColors.info;
      actionIcon = Icons.logout;
    } else if (activity.action.contains('create') ||
        activity.action.contains('add') ||
        activity.action.contains('إضافة') ||
        activity.action.contains('إنشاء')) {
      actionColor = AppColors.info;
      actionIcon = Icons.add_circle;
    } else if (activity.action.contains('update') ||
        activity.action.contains('edit') ||
        activity.action.contains('تعديل') ||
        activity.action.contains('تحديث')) {
      actionColor = AppColors.warning;
      actionIcon = Icons.edit;
    } else if (activity.action.contains('delete') ||
        activity.action.contains('remove') ||
        activity.action.contains('حذف')) {
      actionColor = AppColors.error;
      actionIcon = Icons.delete;
    } else if (activity.action.contains('change_password') ||
        activity.action.contains('تغيير كلمة المرور')) {
      actionColor = AppColors.moduleAuth;
      actionIcon = Icons.password;
    } else {
      actionColor = AppColors.lightTextSecondary;
      actionIcon = Icons.info;
    }

    // تحديد لون الخلفية بناءً على الوحدة
    Color moduleColor;
    if (activity.module.contains('auth') ||
        activity.module.contains('المصادقة')) {
      moduleColor = AppColors.moduleAuth.withValues(alpha: 0.1);
    } else if (activity.module.contains('user') ||
        activity.module.contains('المستخدمين')) {
      moduleColor = AppColors.moduleUsers.withValues(alpha: 0.1);
    } else if (activity.module.contains('product') ||
        activity.module.contains('المنتجات')) {
      moduleColor = AppColors.moduleProducts.withValues(alpha: 0.1);
    } else if (activity.module.contains('sale') ||
        activity.module.contains('المبيعات')) {
      moduleColor = AppColors.moduleSales.withValues(alpha: 0.1);
    } else if (activity.module.contains('purchase') ||
        activity.module.contains('المشتريات')) {
      moduleColor = AppColors.modulePurchases.withValues(alpha: 0.1);
    } else {
      moduleColor = AppColors.lightSurfaceVariant;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacing8),
      decoration: BoxDecoration(
        color: moduleColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(
          color: DynamicColors.border(context).withValues(alpha: 0.2),
        ),
      ),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: actionColor.withValues(alpha: 0.2),
          radius: AppDimensions.iconSizeSmall / 2,
          child: Icon(
            actionIcon,
            color: actionColor,
            size: AppDimensions.iconSizeSmall,
          ),
        ),
        title: Text(
          '${activity.action} - ${activity.module}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DynamicColors.textPrimary(context),
                fontSize: AppDimensions.mediumFontSize,
              ),
        ),
        subtitle: Text(
          'المستخدم: ${activity.userName} - ${DateFormat('yyyy/MM/dd HH:mm').format(activity.timestamp)}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: DynamicColors.textSecondary(context),
                fontSize: AppDimensions.smallFontSize,
              ),
        ),
        children: [
          Padding(
            padding: AppDimensions.defaultPadding,
            child: AkColumn(
              spacing: AkSpacingSize.small,
              children: [
                Text(
                  'التفاصيل:',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: DynamicColors.textPrimary(context),
                        fontSize: AppDimensions.mediumFontSize,
                      ),
                ),
                Text(
                  activity.details,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: DynamicColors.textPrimary(context),
                        fontSize: AppDimensions.defaultFontSize,
                      ),
                ),
                AkRow(
                  alignment: AkAlignment.spaceBetween,
                  children: [
                    Text(
                      'معرف المستخدم: ${activity.userId}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: DynamicColors.textSecondary(context),
                            fontSize: AppDimensions.smallFontSize,
                          ),
                    ),
                    Text(
                      'IP: ${activity.ipAddress}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: DynamicColors.textSecondary(context),
                            fontSize: AppDimensions.smallFontSize,
                          ),
                    ),
                  ],
                ),
                Text(
                  'معرف النشاط: ${activity.id}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: DynamicColors.textSecondary(context),
                        fontSize: AppDimensions.tinyFontSize,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
