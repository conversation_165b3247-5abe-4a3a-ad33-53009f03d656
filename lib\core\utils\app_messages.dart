/// فئة تحتوي على رسائل التطبيق المشتركة
/// توفر رسائل موحدة للاستخدام في جميع أنحاء التطبيق
class AppMessages {
  // منع إنشاء نسخ من هذا الصف
  AppMessages._();

  // رسائل عامة
  static const String appName = 'تاجر بلس';
  static const String comingSoon = 'سيتم تنفيذ هذه الميزة قريباً';
  // رسائل النجاح
  static const String successTitle = 'نجاح';
  static const String saveSuccess = 'تم الحفظ بنجاح';
  static const String updateSuccess = 'تم التحديث بنجاح';
  static const String deleteSuccess = 'تم الحذف بنجاح';

  // رسائل الخطأ
  static const String errorTitle = 'خطأ';
  static const String generalError = 'حدث خطأ ما. يرجى المحاولة مرة أخرى';
  static const String connectionError =
      'خطأ في الاتصال. يرجى التحقق من اتصالك بالإنترنت';
  static const String dataLoadError = 'فشل تحميل البيانات';
  static const String dataSaveError = 'فشل حفظ البيانات';
  static const String dataUpdateError = 'فشل تحديث البيانات';
  static const String dataDeleteError = 'فشل حذف البيانات';
  static const String invalidInput = 'بيانات غير صالحة';
  static const String requiredField = 'هذا الحقل مطلوب';

  // رسائل المخازن
  static const String warehouseCreated = 'تم إنشاء المخزن بنجاح';
  static const String warehouseUpdated = 'تم تحديث المخزن بنجاح';
  static const String warehouseDeleted = 'تم حذف المخزن بنجاح';
  static const String warehouseDefaultError = 'لا يمكن حذف المخزن الافتراضي';
  static const String warehouseHasInventoryError =
      'لا يمكن حذف المخزن لأنه يحتوي على مخزون';
  static const String warehouseSetDefault = 'تم تعيين المخزن كافتراضي';

  // رسائل المخزون
  static const String inventoryUpdated = 'تم تحديث المخزون بنجاح';
  static const String inventoryAdjusted = 'تم تعديل المخزون بنجاح';
  static const String insufficientStock = 'الكمية المتوفرة غير كافية';

  // رسائل تحويلات المخزون
  static const String transferCreated = 'تم إنشاء التحويل بنجاح';
  static const String transferExecuted = 'تم تنفيذ التحويل بنجاح';
  static const String transferCancelled = 'تم إلغاء التحويل بنجاح';
  static const String transferDeleted = 'تم حذف التحويل بنجاح';
  static const String transferCompletedError =
      'لا يمكن تعديل التحويل لأنه مكتمل بالفعل';
  static const String transferSourceRequired = 'يجب تحديد المخزن المصدر';
  static const String transferTargetRequired = 'يجب تحديد المخزن الهدف';
  static const String transferItemsRequired = 'يجب إضافة عنصر واحد على الأقل';

  // رسائل التأكيد
  static const String confirmTitle = 'تأكيد';
  static const String confirmDelete = 'هل أنت متأكد من الحذف؟';
  static const String confirmExecute = 'هل أنت متأكد من تنفيذ هذه العملية؟';
  static const String confirmCancel = 'هل أنت متأكد من الإلغاء؟';

  // أزرار
  static const String ok = 'موافق';
  static const String cancel = 'إلغاء';
  static const String save = 'حفظ';
  static const String update = 'تحديث';
  static const String delete = 'حذف';
  static const String add = 'إضافة';
  static const String edit = 'تعديل';
  static const String close = 'إغلاق';
  static const String retry = 'إعادة المحاولة';
  static const String execute = 'تنفيذ';

  // عناوين الشاشات
  static const String warehouses = 'المخازن';
  static const String warehouseDetails = 'تفاصيل المخزن';
  static const String addWarehouse = 'إضافة مخزن جديد';
  static const String editWarehouse = 'تعديل المخزن';
  static const String inventory = 'المخزون';
  static const String adjustInventory = 'تعديل المخزون';
  static const String stockTransfers = 'تحويلات المخزون';
  static const String transferDetails = 'تفاصيل التحويل';
  static const String createTransfer = 'إنشاء تحويل مخزون';

  // أخرى
  static const String loading = 'جاري التحميل...';
  static const String noData = 'لا توجد بيانات';
  static const String noResults = 'لا توجد نتائج';
  static const String search = 'بحث';
  static const String filter = 'تصفية';
  static const String sort = 'ترتيب';
  static const String details = 'التفاصيل';
  static const String status = 'الحالة';
  static const String active = 'نشط';
  static const String inactive = 'غير نشط';
  static const String default_ = 'افتراضي';
  static const String quantity = 'الكمية';
  static const String available = 'المتاح';
  static const String reserved = 'المحجوز';
  static const String lowStock = 'منخفض المخزون';
  static const String notes = 'ملاحظات';
  static const String optional = 'اختياري';
  static const String selectProduct = 'اختيار منتج';
  static const String selectWarehouse = 'اختيار مخزن';
  static const String allStatuses = 'جميع الحالات';
  static const String allTypes = 'جميع الأنواع';
  static const String showAll = 'عرض الكل';
  static const String showLowStock = 'عرض المنتجات منخفضة المخزون';
  static const String showInactive = 'عرض غير النشط';
  static const String hideInactive = 'إخفاء غير النشط';

  // رسائل التحقق
  static const String invalidEmail = 'البريد الإلكتروني غير صالح';
  static const String invalidPhone = 'رقم الهاتف غير صالح';
  static const String invalidPassword = 'كلمة المرور غير صالحة';
  static const String passwordMismatch = 'كلمات المرور غير متطابقة';
  static const String invalidNumber = 'الرقم غير صالح';
  static const String invalidDate = 'التاريخ غير صالح';

  // رسائل المنتجات
  static const String productAdded = 'تمت إضافة المنتج بنجاح';
  static const String productUpdated = 'تم تحديث المنتج بنجاح';
  static const String productDeleted = 'تم حذف المنتج بنجاح';
  static const String productNotFound = 'لم يتم العثور على المنتج';

  // رسائل العملاء
  static const String customerAdded = 'تمت إضافة العميل بنجاح';
  static const String customerUpdated = 'تم تحديث بيانات العميل بنجاح';
  static const String customerDeleted = 'تم حذف العميل بنجاح';
  static const String customerNotFound = 'لم يتم العثور على العميل';

  // رسائل الموردين
  static const String supplierAdded = 'تمت إضافة المورد بنجاح';
  static const String supplierUpdated = 'تم تحديث بيانات المورد بنجاح';
  static const String supplierDeleted = 'تم حذف المورد بنجاح';
  static const String supplierNotFound = 'لم يتم العثور على المورد';

  // رسائل الوحدات
  static const String unitAdded = 'تمت إضافة الوحدة بنجاح';
  static const String unitUpdated = 'تم تحديث بيانات الوحدة بنجاح';
  static const String unitDeleted = 'تم حذف الوحدة بنجاح';
  static const String unitNotFound = 'لم يتم العثور على الوحدة';

  // رسائل التصنيفات
  static const String categoryAdded = 'تمت إضافة التصنيف بنجاح';
  static const String categoryUpdated = 'تم تحديث بيانات التصنيف بنجاح';
  static const String categoryDeleted = 'تم حذف التصنيف بنجاح';
  static const String categoryNotFound = 'لم يتم العثور على التصنيف';

  // رسائل الإعدادات
  static const String settingsSaved = 'تم حفظ الإعدادات بنجاح';
  static const String settingsError = 'حدث خطأ أثناء حفظ الإعدادات';
  static const String settingsReset = 'تم إعادة تعيين الإعدادات بنجاح';

  // رسائل التقارير
  static const String reportGenerated = 'تم إنشاء التقرير بنجاح';
  static const String reportError = 'حدث خطأ أثناء إنشاء التقرير';
  static const String noReportData = 'لا توجد بيانات للتقرير';

  // رسائل النسخ الاحتياطي
  static const String backupCreated = 'تم إنشاء نسخة احتياطية بنجاح';
  static const String backupRestored = 'تم استعادة النسخة الاحتياطية بنجاح';
  static const String backupError = 'حدث خطأ أثناء عملية النسخ الاحتياطي';

  // رسائل الاتصال
  static const String connectionLost = 'تم فقدان الاتصال';
  static const String connectionRestored = 'تم استعادة الاتصال';

  // رسائل الأذونات
  static const String permissionDenied = 'تم رفض الإذن';
  static const String permissionRequired = 'الإذن مطلوب';
  static const String permissionGranted = 'تم منح الإذن';

  // رسائل التحديثات
  static const String updateAvailable = 'يوجد تحديث جديد';
  static const String updateDownloaded = 'تم تنزيل التحديث';
  static const String updateInstalled = 'تم تثبيت التحديث';
  static const String updateError = 'حدث خطأ أثناء التحديث';

  // رسائل التنبيهات
  static const String warningTitle = 'تحذير';
  static const String infoTitle = 'معلومات';
}
