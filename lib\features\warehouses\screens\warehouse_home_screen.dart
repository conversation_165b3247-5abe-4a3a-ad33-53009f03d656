import 'package:flutter/material.dart';
import '../../../core/utils/index.dart';
import '../../../core/providers/app_providers.dart';

import '../presenters/warehouse_presenter.dart';
import '../../products/presenters/product_presenter.dart';
import 'warehouse_management_screen.dart';
import 'inventory_management_screen.dart';
import 'inventory_alerts_screen.dart';
import '../../../core/theme/index.dart';

/// شاشة الرئيسية للمخازن
class WarehouseHomeScreen extends StatefulWidget {
  const WarehouseHomeScreen({Key? key}) : super(key: key);

  @override
  State<WarehouseHomeScreen> createState() => _WarehouseHomeScreenState();
}

class _WarehouseHomeScreenState extends State<WarehouseHomeScreen> {
  late WarehousePresenter _warehousePresenter;
  late ProductPresenter _productPresenter;

  @override
  void initState() {
    super.initState();
    // 🚀 استخدام التحميل الكسول بدلاً من Provider.of
    _warehousePresenter = AppProviders.getWarehousePresenter();
    _productPresenter = AppProviders.getProductPresenter();

    // استخدام addPostFrameCallback لتأخير استدعاء _loadData حتى اكتمال بناء الإطار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await _warehousePresenter.loadWarehouses();
    await _productPresenter.loadProducts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المخازن'),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ملخص المخزون
              _buildInventorySummary(),

              const SizedBox(height: 24),

              // بطاقات الوظائف
              GridView.count(
                crossAxisCount: Layout.isMobile() ? 2 : 3,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  // إدارة المخازن
                  _buildFeatureCard(
                    title: 'إدارة المخازن',
                    icon: Icons.warehouse,
                    color: AppColors.lightTextSecondary,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const WarehouseManagementScreen(),
                      ),
                    ),
                  ),

                  // إدارة المخزون
                  _buildFeatureCard(
                    title: 'إدارة المخزون',
                    icon: Icons.inventory_2,
                    color: AppColors.lightTextSecondary,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const InventoryManagementScreen(),
                      ),
                    ),
                  ),

                  // تقرير المخزون
                  _buildFeatureCard(
                    title: 'تقرير المخزون',
                    icon: Icons.assessment,
                    color: AppColors.lightTextSecondary,
                    onTap: () =>
                        Navigator.pushNamed(context, '/inventory-report'),
                  ),

                  // تنبيهات المخزون
                  _buildFeatureCard(
                    title: 'تنبيهات المخزون',
                    icon: Icons.notifications,
                    color: AppColors.lightTextSecondary,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const InventoryAlertsScreen(),
                      ),
                    ),
                    badge: _getLowStockCount(),
                  ),

                  // لوحة معلومات المخزون
                  _buildFeatureCard(
                    title: 'لوحة المعلومات',
                    icon: Icons.dashboard,
                    color: AppColors.lightTextSecondary,
                    onTap: () =>
                        Navigator.pushNamed(context, '/inventory-dashboard'),
                  ),

                  // جرد المخزون
                  _buildFeatureCard(
                    title: 'جرد المخزون',
                    icon: Icons.checklist,
                    color: AppColors.lightTextSecondary,
                    onTap: () =>
                        Navigator.pushNamed(context, '/inventory-count'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء ملخص المخزون
  Widget _buildInventorySummary() {
    return ListenableBuilder(
      listenable: _productPresenter,
      builder: (context, child) {
        final totalProducts = _productPresenter.products.length;
        // في هذا المثال، نقوم بمحاكاة كمية المخزون
        // في التطبيق الفعلي، يجب الحصول على الكمية من المخزون
        const lowStockProducts = 5; // قيمة محاكاة
        const outOfStockProducts = 2; // قيمة محاكاة

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'ملخص المخزون',
                  style: AppTypography(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                // إحصائيات المخزون
                Row(
                  children: [
                    Expanded(
                      child: _buildSummaryItem(
                        title: 'إجمالي المنتجات',
                        value: totalProducts.toString(),
                        icon: Icons.inventory,
                        color: AppColors.lightTextSecondary,
                      ),
                    ),
                    Expanded(
                      child: _buildSummaryItem(
                        title: 'منتجات منخفضة',
                        value: lowStockProducts.toString(),
                        icon: Icons.warning,
                        color: AppColors.lightTextSecondary,
                      ),
                    ),
                    Expanded(
                      child: _buildSummaryItem(
                        title: 'منتجات نافدة',
                        value: outOfStockProducts.toString(),
                        icon: Icons.error,
                        color: AppColors.lightTextSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء عنصر ملخص
  Widget _buildSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 32,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTypography(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const AppTypography(
            fontSize: 14,
            color: AppColors.lightTextSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بناء بطاقة وظيفة
  Widget _buildFeatureCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    int? badge,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    title,
                    style: const AppTypography(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            if (badge != null && badge > 0)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppColors.lightTextSecondary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                  child: Text(
                    badge.toString(),
                    style: const AppTypography(
                      color: AppColors.lightTextSecondary,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// الحصول على عدد المنتجات منخفضة المخزون
  int _getLowStockCount() {
    // في هذا المثال، نقوم بمحاكاة كمية المخزون
    // في التطبيق الفعلي، يجب الحصول على الكمية من المخزون
    return 5; // قيمة محاكاة
  }
}
