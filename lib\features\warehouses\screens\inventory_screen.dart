import 'package:flutter/material.dart';

import '../presenters/inventory_presenter.dart';
import '../../products/presenters/product_presenter.dart';
import 'inventory_adjustment_form_screen.dart';
import 'low_stock_report_screen.dart';
import 'inventory_movement_report_screen.dart';
import '../../../core/theme/index.dart';
import '../../../core/providers/app_providers.dart'; // للتحميل الكسول

/// شاشة المخزون
class InventoryScreen extends StatefulWidget {
  final String warehouseId;

  const InventoryScreen({Key? key, required this.warehouseId})
      : super(key: key);

  @override
  State<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends State<InventoryScreen> {
  bool _isLoading = false;
  bool _showLowStock = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // استخدام التحميل الكسول
  late final InventoryPresenter _inventoryPresenter;
  late final ProductPresenter _productPresenter;

  @override
  void initState() {
    super.initState();
    _inventoryPresenter = AppProviders.getLazyPresenter<InventoryPresenter>(
        () => InventoryPresenter());
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
        () => ProductPresenter());
    _loadInventory();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadInventory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (_showLowStock) {
        // تحميل المنتجات منخفضة المخزون فقط
        await _inventoryPresenter.loadLowStockProducts();
      } else {
        // تحميل جميع المنتجات
        await _inventoryPresenter.loadWarehouseInventory(widget.warehouseId);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: _buildInventoryList(),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'بحث عن منتج...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: Icon(
              _showLowStock ? Icons.warning_amber : Icons.inventory,
              color: _showLowStock ? AppColors.warning : null,
            ),
            tooltip: _showLowStock
                ? 'عرض جميع المنتجات'
                : 'عرض المنتجات منخفضة المخزون',
            onPressed: () {
              setState(() {
                _showLowStock = !_showLowStock;
              });
              _loadInventory();
            },
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(
              Icons.analytics,
              color: AppColors.lightTextSecondary,
            ),
            tooltip: 'تقرير المنتجات منخفضة المخزون',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const LowStockReportScreen(),
                ),
              );
            },
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(
              Icons.timeline,
              color: AppColors.lightTextSecondary,
            ),
            tooltip: 'تقرير حركة المخزون',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => InventoryMovementReportScreen(
                    warehouseId: widget.warehouseId,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryList() {
    return ListenableBuilder(
      listenable: _inventoryPresenter,
      builder: (context, child) {
        if (_isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_inventoryPresenter.errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'حدث خطأ أثناء تحميل المخزون',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(_inventoryPresenter.errorMessage!),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadInventory,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        // تصفية المنتجات حسب البحث
        final filteredItems =
            _inventoryPresenter.inventoryItemsDetailed.where((item) {
          if (_searchQuery.isEmpty) return true;

          final productName = item['product_name'] as String? ?? '';
          final barcode = item['barcode'] as String? ?? '';
          final code = item['code'] as String? ?? '';

          return productName
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              barcode.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              code.toLowerCase().contains(_searchQuery.toLowerCase());
        }).toList();

        if (filteredItems.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _searchQuery.isNotEmpty
                      ? 'لا توجد نتائج للبحث'
                      : _showLowStock
                          ? 'لا توجد منتجات منخفضة المخزون'
                          : 'لا توجد منتجات في المخزن',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 16),
                if (_searchQuery.isNotEmpty)
                  ElevatedButton(
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _searchQuery = '';
                      });
                    },
                    child: const Text('مسح البحث'),
                  )
                else if (_showLowStock)
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _showLowStock = false;
                      });
                      _loadInventory();
                    },
                    child: const Text('عرض جميع المنتجات'),
                  )
                else
                  ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => InventoryAdjustmentFormScreen(
                            initialWarehouseId: widget.warehouseId,
                            initialAdjustmentType: 'addition',
                          ),
                        ),
                      ).then((_) => _loadInventory());
                    },
                    child: const Text('إضافة مخزون'),
                  ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadInventory,
          child: ListView.builder(
            itemCount: filteredItems.length,
            itemBuilder: (context, index) {
              final item = filteredItems[index];
              return _buildInventoryItem(item);
            },
          ),
        );
      },
    );
  }

  Widget _buildInventoryItem(Map<String, dynamic> item) {
    final productName = item['product_name'] as String? ?? 'منتج غير معروف';
    final barcode = item['barcode'] as String?;
    final code = item['code'] as String?;
    final quantity = item['quantity'] as double? ?? 0.0;
    final reservedQuantity = item['reserved_quantity'] as double? ?? 0.0;
    final availableQuantity = quantity - reservedQuantity;
    final minStock = item['min_stock'] as double?;
    final isLowStock = minStock != null && quantity <= minStock;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(productName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (barcode != null) Text('الباركود: $barcode'),
            if (code != null) Text('الرمز: $code'),
            const SizedBox(height: 4),
            Row(
              children: [
                Text('الكمية: $quantity'),
                const SizedBox(width: 16),
                Text('المحجوز: $reservedQuantity'),
                const SizedBox(width: 16),
                Text('المتاح: $availableQuantity'),
              ],
            ),
            if (minStock != null)
              Row(
                children: [
                  Text('الحد الأدنى: $minStock'),
                  const SizedBox(width: 8),
                  if (isLowStock)
                    const Chip(
                      label: Text(
                        'منخفض المخزون',
                        style: AppTypography(
                            color: AppColors.lightTextSecondary, fontSize: 12),
                      ),
                      backgroundColor: AppColors.warning,
                    ),
                ],
              ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.edit),
          tooltip: 'تعديل المخزون',
          onPressed: () => _adjustInventory(item),
        ),
      ),
    );
  }

  void _adjustInventory(Map<String, dynamic> item) async {
    // Get the product from the item
    final productId = item['product_id'] as String;
    final product = _productPresenter.getProductById(productId);

    if (product == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لم يتم العثور على المنتج'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InventoryAdjustmentFormScreen(
          initialWarehouseId: widget.warehouseId,
          initialProduct: product,
          initialAdjustmentType: 'adjustment',
        ),
      ),
    ).then((_) => _loadInventory());
  }
}
