import 'dart:convert';
import 'package:uuid/uuid.dart';

/// نموذج تفاصيل القيد المحاسبي
class JournalEntryDetail {
  final String? id;
  final String? journalEntryId;
  final String? accountId;
  final String? accountName;
  final String? accountCode;
  final double debit;
  final double credit;
  final String? description;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? createdBy;
  final String? updatedBy;
  final bool isDeleted;

  /// إنشاء تفاصيل قيد محاسبي جديد
  JournalEntryDetail({
    this.id,
    this.journalEntryId,
    this.accountId,
    this.accountName,
    this.accountCode,
    this.debit = 0.0,
    this.credit = 0.0,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.isDeleted = false,
  });

  /// إنشاء نسخة من هذه التفاصيل مع استبدال الحقول المحددة بقيم جديدة
  JournalEntryDetail copyWith({
    String? id,
    String? journalEntryId,
    String? accountId,
    String? accountName,
    String? accountCode,
    double? debit,
    double? credit,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return JournalEntryDetail(
      id: id ?? this.id,
      journalEntryId: journalEntryId ?? this.journalEntryId,
      accountId: accountId ?? this.accountId,
      accountName: accountName ?? this.accountName,
      accountCode: accountCode ?? this.accountCode,
      debit: debit ?? this.debit,
      credit: credit ?? this.credit,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// إنشاء تفاصيل قيد محاسبي من خريطة
  factory JournalEntryDetail.fromMap(Map<String, dynamic> map) {
    return JournalEntryDetail(
      id: map['id'],
      journalEntryId: map['journal_entry_id'],
      accountId: map['account_id'],
      accountName: map['account_name'],
      accountCode: map['account_code'],
      debit: map['debit']?.toDouble() ?? 0.0,
      credit: map['credit']?.toDouble() ?? 0.0,
      description: map['description'],
      createdAt: map['created_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'])
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updated_at'])
          : null,
      createdBy: map['created_by'],
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل تفاصيل القيد المحاسبي إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id ?? const Uuid().v4(),
      'journal_entry_id': journalEntryId,
      'account_id': accountId,
      'account_name': accountName,
      'account_code': accountCode,
      'debit': debit,
      'credit': credit,
      'description': description,
      'created_at': createdAt?.millisecondsSinceEpoch ??
          DateTime.now().millisecondsSinceEpoch,
      'updated_at': updatedAt?.millisecondsSinceEpoch,
      'created_by': createdBy,
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// تحويل تفاصيل القيد المحاسبي إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء تفاصيل قيد محاسبي من JSON
  factory JournalEntryDetail.fromJson(String source) =>
      JournalEntryDetail.fromMap(json.decode(source));

  /// إنشاء تفاصيل قيد محاسبي فارغة
  factory JournalEntryDetail.empty() {
    return JournalEntryDetail();
  }

  @override
  String toString() {
    return 'JournalEntryDetail(id: $id, journalEntryId: $journalEntryId, accountId: $accountId, accountName: $accountName, accountCode: $accountCode, debit: $debit, credit: $credit, description: $description, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, updatedBy: $updatedBy, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JournalEntryDetail &&
        other.id == id &&
        other.journalEntryId == journalEntryId &&
        other.accountId == accountId &&
        other.accountName == accountName &&
        other.accountCode == accountCode &&
        other.debit == debit &&
        other.credit == credit &&
        other.description == description &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.createdBy == createdBy &&
        other.updatedBy == updatedBy &&
        other.isDeleted == isDeleted;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        journalEntryId.hashCode ^
        accountId.hashCode ^
        accountName.hashCode ^
        accountCode.hashCode ^
        debit.hashCode ^
        credit.hashCode ^
        description.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        createdBy.hashCode ^
        updatedBy.hashCode ^
        isDeleted.hashCode;
  }
}
