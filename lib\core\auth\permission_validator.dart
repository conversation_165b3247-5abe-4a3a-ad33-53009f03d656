import 'package:tajer_plus/core/auth/roles_schema.dart';
import '../utils/app_logger.dart';

/// أداة للتحقق من صحة تعريفات الصلاحيات والأدوار
class PermissionValidator {
  /// التحقق من عدم وجود تكرارات في أسماء الصلاحيات
  static bool validateUniquePermissionNames() {
    final permissionNames = <String>[];
    final duplicates = <String>[];

    // جمع جميع أسماء الصلاحيات والبحث عن التكرارات
    RolesSchema.permissions.forEach((module, perms) {
      perms.forEach((code, name) {
        if (permissionNames.contains(name)) {
          duplicates.add(name);
          AppLogger.error('❌ تم العثور على تكرار في اسم الصلاحية: "$name"');
        } else {
          permissionNames.add(name);
        }
      });
    });

    // طباعة نتائج التحقق
    if (duplicates.isEmpty) {
      AppLogger.info(
          '✅ جميع أسماء الصلاحيات فريدة (${permissionNames.length} صلاحية)');
      return true;
    } else {
      AppLogger.error(
          '❌ تم العثور على ${duplicates.length} تكرارات في أسماء الصلاحيات:');
      for (final duplicate in duplicates) {
        // البحث عن الوحدات والرموز التي تستخدم نفس الاسم
        final modulesAndCodes = <String>[];
        RolesSchema.permissions.forEach((module, perms) {
          perms.forEach((code, name) {
            if (name == duplicate) {
              modulesAndCodes.add('$module.$code');
            }
          });
        });
        AppLogger.error(
            '  - "$duplicate" مستخدم في: ${modulesAndCodes.join(', ')}');
      }
      return false;
    }
  }

  /// التحقق من صحة رموز الصلاحيات في تعريفات الأدوار
  static bool validateRolePermissionCodes() {
    final allPermissionCodes = <String>[];
    final invalidCodes = <String>[];

    // جمع جميع رموز الصلاحيات المتاحة
    RolesSchema.permissions.forEach((module, perms) {
      perms.forEach((code, name) {
        allPermissionCodes.add(code);
      });
    });

    // التحقق من صحة رموز الصلاحيات في تعريفات الأدوار
    RolesSchema.defaultRolePermissions.forEach((role, permissions) {
      for (final permissionCode in permissions) {
        if (!allPermissionCodes.contains(permissionCode)) {
          invalidCodes.add(permissionCode);
          AppLogger.error(
              '❌ رمز صلاحية غير صالح "$permissionCode" في دور "$role"');
        }
      }
    });

    // طباعة نتائج التحقق
    if (invalidCodes.isEmpty) {
      AppLogger.info('✅ جميع رموز الصلاحيات في تعريفات الأدوار صالحة');
      return true;
    } else {
      AppLogger.error(
          '❌ تم العثور على ${invalidCodes.length} رموز صلاحيات غير صالحة في تعريفات الأدوار');
      return false;
    }
  }

  /// تنفيذ جميع عمليات التحقق
  static bool validateAll() {
    AppLogger.info('🔍 بدء التحقق من صحة تعريفات الصلاحيات والأدوار...');

    final uniqueNames = validateUniquePermissionNames();
    final validCodes = validateRolePermissionCodes();

    final isValid = uniqueNames && validCodes;

    if (isValid) {
      AppLogger.info('✅ تم التحقق من صحة تعريفات الصلاحيات والأدوار بنجاح');
    } else {
      AppLogger.error('❌ فشل التحقق من صحة تعريفات الصلاحيات والأدوار');
    }

    return isValid;
  }
}
