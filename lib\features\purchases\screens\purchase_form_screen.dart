import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/models/purchase.dart';
import '../../../core/models/product.dart';
import '../../../core/models/supplier.dart';
import '../../../core/models/warehouse.dart';
import '../presenters/purchase_presenter.dart';
import '../../products/presenters/product_presenter.dart';
import '../../suppliers/presenters/supplier_presenter.dart';
import '../../warehouses/presenters/warehouse_presenter.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart';

/// شاشة نموذج إنشاء/تعديل فاتورة المشتريات
class PurchaseFormScreen extends StatefulWidget {
  final Purchase? purchase;

  const PurchaseFormScreen({Key? key, this.purchase}) : super(key: key);

  @override
  State<PurchaseFormScreen> createState() => _PurchaseFormScreenState();
}

class _PurchaseFormScreenState extends State<PurchaseFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late PurchasePresenter _purchasePresenter;
  late ProductPresenter _productPresenter;
  late SupplierPresenter _supplierPresenter;
  late WarehousePresenter _warehousePresenter;

  String? _selectedSupplierId;
  String? _selectedWarehouseId;
  final List<PurchaseItem> _items = [];
  final _notesController = TextEditingController();
  double _subtotal = 0.0;
  double _discount = 0.0;
  double _tax = 0.0;
  double _total = 0.0;
  double _paid = 0.0;
  double? _remaining = 0.0;
  String _status = 'pending';
  String _paymentType = 'cash';
  DateTime? _dueDate;
  final _discountController = TextEditingController(text: '0.0');
  final _taxController = TextEditingController(text: '0.0');
  final _paidController = TextEditingController(text: '0.0');
  final _searchController = TextEditingController();
  bool _isLoading = false;

  List<Supplier> _suppliers = [];
  List<Warehouse> _warehouses = [];
  List<Product> _products = [];
  // We'll use the products list directly instead of a filtered copy
  // List<Product> _filteredProducts = [];

  @override
  void initState() {
    super.initState();
    _purchasePresenter = AppProviders.getLazyPresenter<PurchasePresenter>(
        () => PurchasePresenter());
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
        () => ProductPresenter());
    _supplierPresenter = AppProviders.getLazyPresenter<SupplierPresenter>(
        () => SupplierPresenter());
    _warehousePresenter = AppProviders.getLazyPresenter<WarehousePresenter>(
        () => WarehousePresenter());

    _loadData();
    _initializeFields();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل المنتجات والموردين والمستودعات
      await _productPresenter.loadProducts();
      await _supplierPresenter.loadSuppliers();
      await _warehousePresenter.loadWarehouses();

      // الحصول على البيانات من المقدمين
      if (mounted) {
        setState(() {
          _products = _productPresenter.products;
          _suppliers = _supplierPresenter.suppliers;
          _warehouses = _warehousePresenter.warehouses;

          // تحديد المستودع الافتراضي إذا كان متاحاً
          if (_selectedWarehouseId == null && _warehouses.isNotEmpty) {
            final defaultWarehouse = _warehouses.firstWhere(
              (w) => w.isDefault,
              orElse: () => _warehouses.first,
            );
            _selectedWarehouseId = defaultWarehouse.id;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل تحميل البيانات: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _initializeFields() {
    if (widget.purchase != null) {
      _selectedSupplierId = widget.purchase!.supplierId;
      _selectedWarehouseId = widget.purchase!.warehouseId;
      _items.addAll(widget.purchase!.items);
      _notesController.text = widget.purchase!.notes ?? '';
      _subtotal = widget.purchase!.subtotal;
      _discount = widget.purchase!.discount;
      _tax = widget.purchase!.tax;
      _total = widget.purchase!.total;
      _paid = widget.purchase!.paid;
      _remaining = widget.purchase!.remaining;
      _status = widget.purchase!.status;
      _paymentType = widget.purchase!.paymentType ?? 'cash';
      _dueDate = widget.purchase!.dueDate;
      _discountController.text = _discount.toString();
      _taxController.text = _tax.toString();
      _paidController.text = _paid.toString();
    }
    _calculateTotals();
  }

  void _calculateTotals() {
    _subtotal = _items.fold(0, (sum, item) => sum + item.total);
    _discount = double.tryParse(_discountController.text) ?? 0.0;
    _tax = double.tryParse(_taxController.text) ?? 0.0;
    _total = _subtotal - _discount + _tax;
    _paid = double.tryParse(_paidController.text) ?? 0.0;
    _remaining = _total - _paid;

    setState(() {});
  }

  @override
  void dispose() {
    _notesController.dispose();
    _discountController.dispose();
    _taxController.dispose();
    _paidController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: widget.purchase == null
            ? 'فاتورة مشتريات جديدة'
            : 'تعديل فاتورة المشتريات',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: ListView(
                      padding: const EdgeInsets.all(16),
                      children: [
                        _buildSupplierSection(),
                        const SizedBox(height: 24),
                        _buildItemsSection(),
                        const SizedBox(height: 24),
                        _buildTotalsSection(),
                        const SizedBox(height: 24),
                        _buildPaymentSection(),
                        const SizedBox(height: 24),
                        _buildNotesSection(),
                      ],
                    ),
                  ),
                  _buildBottomBar(),
                ],
              ),
            ),
    );
  }

  Widget _buildSupplierSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات المورد والمستودع',
              style: AppTypography(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String?>(
              decoration: const InputDecoration(
                labelText: 'المورد',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              value: _selectedSupplierId,
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('اختر المورد'),
                ),
                ..._suppliers.map((supplier) {
                  return DropdownMenuItem<String>(
                    value: supplier.id,
                    child: Text(supplier.name),
                  );
                }).toList(),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedSupplierId = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار المورد';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String?>(
              decoration: const InputDecoration(
                labelText: 'المستودع',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.warehouse),
              ),
              value: _selectedWarehouseId,
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('اختر المستودع'),
                ),
                ..._warehouses.map((warehouse) {
                  return DropdownMenuItem<String>(
                    value: warehouse.id,
                    child: Text(warehouse.name),
                  );
                }).toList(),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedWarehouseId = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار المستودع';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'المنتجات',
                  style:
                      AppTypography(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                ElevatedButton.icon(
                  onPressed: _showAddItemDialog,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة منتج'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_items.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text('لا توجد منتجات مضافة'),
                ),
              )
            else
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  columnSpacing: 20,
                  headingTextStyle: const AppTypography(
                    fontWeight: FontWeight.bold,
                    color: AppColors.lightTextSecondary,
                  ),
                  columns: const [
                    DataColumn(label: Text('#')),
                    DataColumn(label: Text('المنتج')),
                    DataColumn(label: Text('الوحدة')),
                    DataColumn(label: Text('الكمية')),
                    DataColumn(label: Text('السعر')),
                    DataColumn(label: Text('الإجمالي')),
                    DataColumn(label: Text('إجراءات')),
                  ],
                  rows: List<DataRow>.generate(
                    _items.length,
                    (index) {
                      final item = _items[index];
                      return DataRow(
                        cells: [
                          DataCell(Text('${index + 1}')),
                          DataCell(Text(item.productName ?? 'غير محدد')),
                          DataCell(Text(item.unitName ?? 'غير محدد')),
                          DataCell(Text('${item.quantity}')),
                          DataCell(Text('${item.price} ر.س')),
                          DataCell(Text('${item.total} ر.س')),
                          DataCell(
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.edit, size: 20),
                                  onPressed: () => _editItem(index),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete,
                                      size: 20, color: AppColors.error),
                                  onPressed: () => _removeItem(index),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalsSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإجماليات',
              style: AppTypography(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('المجموع الفرعي:'),
                Text(
                  '$_subtotal ر.س',
                  style: const AppTypography(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _discountController,
              decoration: const InputDecoration(
                labelText: 'الخصم',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.discount),
                suffixText: 'ر.س',
              ),
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
              ],
              onChanged: (value) {
                setState(() {
                  _calculateTotals();
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _taxController,
              decoration: const InputDecoration(
                labelText: 'الضريبة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.receipt),
                suffixText: 'ر.س',
              ),
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
              ],
              onChanged: (value) {
                setState(() {
                  _calculateTotals();
                });
              },
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'الإجمالي:',
                    style: AppTypography(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '$_total ر.س',
                    style: const AppTypography(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: AppColors.lightTextSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الدفع',
              style: AppTypography(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('نقدي'),
                    value: 'cash',
                    groupValue: _paymentType,
                    onChanged: (value) {
                      setState(() {
                        _paymentType = value!;
                        if (_paymentType == 'cash') {
                          _paidController.text = _total.toString();
                          _calculateTotals();
                        }
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('آجل'),
                    value: 'credit',
                    groupValue: _paymentType,
                    onChanged: (value) {
                      setState(() {
                        _paymentType = value!;
                        if (_paymentType == 'credit') {
                          _paidController.text = '0.0';
                          _calculateTotals();
                        }
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _paidController,
              decoration: const InputDecoration(
                labelText: 'المبلغ المدفوع',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.payments),
                suffixText: 'ر.س',
              ),
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
              ],
              onChanged: (value) {
                setState(() {
                  _calculateTotals();
                });
              },
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('المبلغ المتبقي:'),
                Text(
                  '$_remaining ر.س',
                  style: AppTypography(
                    fontWeight: FontWeight.bold,
                    color: (_remaining ?? 0) > 0
                        ? AppColors.error
                        : AppColors.success,
                  ),
                ),
              ],
            ),
            if (_paymentType == 'credit') ...[
              const SizedBox(height: 16),
              InkWell(
                onTap: _selectDueDate,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'تاريخ الاستحقاق',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    _dueDate != null
                        ? '${_dueDate!.day}/${_dueDate!.month}/${_dueDate!.year}'
                        : 'اختر تاريخ الاستحقاق',
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return AkCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات',
              style: AppTypography(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                hintText: 'أدخل ملاحظات إضافية هنا...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightTextSecondary,
        boxShadow: [
          BoxShadow(
            color: AppColors.lightTextPrimary.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('إلغاء'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _savePurchase,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: Text(
                  widget.purchase == null ? 'حفظ الفاتورة' : 'تحديث الفاتورة'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDueDate() async {
    final initialDate = _dueDate ?? DateTime.now().add(const Duration(days: 7));
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (pickedDate != null) {
      setState(() {
        _dueDate = pickedDate;
      });
    }
  }

  void _showAddItemDialog() {
    showDialog(
      context: context,
      builder: (context) => ProductSelectionDialog(
        products: _products,
      ),
    ).then((result) {
      if (result != null && result is PurchaseItem) {
        setState(() {
          _items.add(result);
          _calculateTotals();
        });
      }
    });
  }

  void _editItem(int index) {
    final item = _items[index];
    final product = _products.firstWhere(
      (p) => p.id == item.productId,
      orElse: () => Product(
        id: item.productId,
        name: item.productName ?? '',
        barcode: item.productCode,
        description: '',
        categoryId: '',
        unitId: item.unitId ?? '',
        salePrice: item.price,
        purchasePrice: item.cost ?? 0,
      ),
    );

    showDialog(
      context: context,
      builder: (context) => ProductSelectionDialog(
        products: _products,
        initialProduct: product,
        initialQuantity: item.quantity,
        initialPrice: item.price,
      ),
    ).then((result) {
      if (result != null && result is PurchaseItem) {
        setState(() {
          _items[index] = result;
          _calculateTotals();
        });
      }
    });
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
      _calculateTotals();
    });
  }

  Future<void> _savePurchase() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة منتج واحد على الأقل')),
      );
      return;
    }

    if (_paymentType == 'credit' && _dueDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('يرجى تحديد تاريخ الاستحقاق للفواتير الآجلة')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final purchase = Purchase(
        id: widget.purchase?.id,
        referenceNumber: widget.purchase?.referenceNumber,
        date: DateTime.now(),
        supplierId: _selectedSupplierId,
        warehouseId: _selectedWarehouseId,
        subtotal: _subtotal,
        discount: _discount,
        tax: _tax,
        total: _total,
        paid: _paid,
        remaining: _remaining,
        status: _status,
        paymentType: _paymentType,
        dueDate: _dueDate,
        notes: _notesController.text,
        items: _items,
      );

      bool success;
      if (widget.purchase == null) {
        final id = await _purchasePresenter.addPurchase(purchase);
        success = id != null;
      } else {
        success = await _purchasePresenter.updatePurchase(purchase);
      }

      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(widget.purchase == null
                  ? 'تم إضافة الفاتورة بنجاح'
                  : 'تم تحديث الفاتورة بنجاح')),
        );
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(_purchasePresenter.error ?? 'حدث خطأ أثناء حفظ الفاتورة'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: ${e.toString()}'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

/// مربع حوار اختيار المنتج
class ProductSelectionDialog extends StatefulWidget {
  final List<Product> products;
  final Product? initialProduct;
  final double? initialQuantity;
  final double? initialPrice;

  const ProductSelectionDialog({
    Key? key,
    required this.products,
    this.initialProduct,
    this.initialQuantity,
    this.initialPrice,
  }) : super(key: key);

  @override
  State<ProductSelectionDialog> createState() => _ProductSelectionDialogState();
}

class _ProductSelectionDialogState extends State<ProductSelectionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _searchController = TextEditingController();
  final _quantityController = TextEditingController(text: '1');
  final _priceController = TextEditingController();

  Product? _selectedProduct;
  List<Product> _filteredProducts = [];

  @override
  void initState() {
    super.initState();
    _filteredProducts = widget.products;

    if (widget.initialProduct != null) {
      _selectedProduct = widget.initialProduct;
      _quantityController.text = (widget.initialQuantity ?? 1).toString();
      _priceController.text =
          (widget.initialPrice ?? _selectedProduct!.purchasePrice).toString();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _quantityController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  void _filterProducts(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredProducts = widget.products;
      } else {
        _filteredProducts = widget.products.where((product) {
          final nameMatch =
              product.name.toLowerCase().contains(query.toLowerCase());
          final barcodeMatch =
              product.barcode?.toLowerCase().contains(query.toLowerCase()) ??
                  false;
          return nameMatch || barcodeMatch;
        }).toList();
      }
    });
  }

  void _selectProduct(Product product) {
    setState(() {
      _selectedProduct = product;
      _priceController.text = product.purchasePrice.toString();
    });
  }

  void _addItem() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedProduct == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار منتج')),
      );
      return;
    }

    final quantity = double.tryParse(_quantityController.text) ?? 0;
    final price = double.tryParse(_priceController.text) ?? 0;

    if (quantity <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب أن تكون الكمية أكبر من صفر')),
      );
      return;
    }

    if (price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب أن يكون السعر أكبر من صفر')),
      );
      return;
    }

    final item = PurchaseItem(
      purchaseId: '',
      productId: _selectedProduct!.id,
      productName: _selectedProduct!.name,
      productCode: _selectedProduct!.barcode,
      unitId: _selectedProduct!.unitId,
      unitName: _getUnitName(_selectedProduct!.unitId),
      quantity: quantity,
      price: price,
      cost: _selectedProduct!.purchasePrice,
    );

    Navigator.pop(context, item);
  }

  String _getUnitName(String? unitId) {
    // Get unit name from product presenter or use a default value
    return 'قطعة'; // Default value, replace with actual unit name lookup
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة منتج'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search field
              TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  labelText: 'بحث عن منتج',
                  prefixIcon: Icon(Icons.search),
                ),
                onChanged: _filterProducts,
              ),
              const SizedBox(height: 16),

              // Product list
              Container(
                constraints: const BoxConstraints(maxHeight: 200),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _filteredProducts.length,
                  itemBuilder: (context, index) {
                    final product = _filteredProducts[index];
                    return ListTile(
                      title: Text(product.name),
                      subtitle: Text('السعر: ${product.purchasePrice}'),
                      selected: _selectedProduct?.id == product.id,
                      onTap: () => _selectProduct(product),
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),

              // Quantity field
              TextFormField(
                controller: _quantityController,
                decoration: const InputDecoration(labelText: 'الكمية'),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال الكمية';
                  }
                  if (double.tryParse(value) == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Price field
              TextFormField(
                controller: _priceController,
                decoration: const InputDecoration(labelText: 'سعر الشراء'),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال السعر';
                  }
                  if (double.tryParse(value) == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _addItem,
          child: const Text('إضافة'),
        ),
      ],
    );
  }
}
