import 'package:flutter/material.dart';
import '../theme/index.dart';

/// نظام الحالات الموحد لتطبيق تاجر بلس
/// يحتوي على جميع حالات الواجهة المستخدمة في التطبيق
///
/// **المميزات:**
/// - تصميم موحد ومتناسق لجميع الحالات
/// - دعم كامل للوضع المظلم/الفاتح
/// - تأثيرات تفاعلية متقدمة
/// - تحميل كسول للعناصر الثقيلة
/// - دوال مساعدة سريعة
/// - تعليقات شاملة باللغة العربية

// ═══════════════════════════════════════════════════════════════════════════════
// ● الأنواع والثوابت
// ═══════════════════════════════════════════════════════════════════════════════

/// أنواع الحالات المختلفة
enum AkStateType {
  /// حالة فارغة
  empty,

  /// حالة خطأ
  error,

  /// حالة تحميل
  loading,

  /// حالة عدم وجود اتصال
  noConnection,

  /// حالة عدم وجود صلاحية
  noPermission,
}

/// أحجام عرض الحالات
enum AkStateSize {
  /// حجم صغير
  small,

  /// حجم متوسط
  medium,

  /// حجم كبير
  large,
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● 1. حالة فارغة موحدة (AkEmptyState)
// ═══════════════════════════════════════════════════════════════════════════════

/// حالة فارغة موحدة مع تصميم متناسق
///
/// **المميزات:**
/// - تصميم موحد لجميع الحالات الفارغة
/// - دعم أيقونات ورسائل مخصصة
/// - أزرار إجراءات قابلة للتخصيص
/// - دعم كامل للوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkEmptyState(
///   message: 'لا توجد منتجات',
///   icon: Icons.inventory_2,
///   onRefresh: () => loadProducts(),
///   actionText: 'إضافة منتج',
///   onAction: () => addProduct(),
/// )
/// ```
class AkEmptyState extends StatelessWidget {
  /// رسالة الحالة الفارغة
  final String message;

  /// أيقونة الحالة
  final IconData icon;

  /// دالة التحديث
  final VoidCallback? onRefresh;

  /// دالة الإجراء المخصص
  final VoidCallback? onAction;

  /// نص زر الإجراء
  final String? actionText;

  /// نص زر التحديث
  final String refreshText;

  /// حجم عرض الحالة
  final AkStateSize size;

  /// لون مخصص للأيقونة
  final Color? customIconColor;

  /// عنوان إضافي
  final String? title;

  /// وصف إضافي
  final String? description;

  const AkEmptyState({
    super.key,
    required this.message,
    this.icon = Icons.inbox_outlined,
    this.onRefresh,
    this.onAction,
    this.actionText,
    this.refreshText = 'تحديث',
    this.size = AkStateSize.medium,
    this.customIconColor,
    this.title,
    this.description,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // تحديد الأحجام حسب حجم العرض
    final iconSize = _getIconSize();
    final titleFontSize = _getTitleFontSize();
    final messageFontSize = _getMessageFontSize();
    final spacing = _getSpacing();

    // تحديد الألوان حسب الوضع
    final iconColor = customIconColor ??
        (isDark ? AppColors.darkTextHint : AppColors.lightTextHint);
    final titleColor =
        isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary;
    final messageColor =
        isDark ? AppColors.darkTextSecondary : AppColors.lightTextSecondary;
    final descriptionColor =
        isDark ? AppColors.darkTextHint : AppColors.lightTextHint;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.defaultMargin),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة الحالة الفارغة
            Container(
              padding: EdgeInsets.all(AppDimensions.defaultMargin),
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: iconSize,
                color: iconColor,
              ),
            ),
            SizedBox(height: spacing),

            // العنوان (إذا كان موجوداً)
            if (title != null) ...[
              Text(
                title!,
                style: AppTypography.createCustomStyle(
                  fontSize: titleFontSize,
                  fontWeight: AppTypography.weightMedium,
                  color: titleColor,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: spacing / 2),
            ],

            // رسالة الحالة الفارغة
            Text(
              message,
              style: AppTypography.createCustomStyle(
                fontSize: messageFontSize,
                fontWeight: AppTypography.weightRegular,
                color: messageColor,
              ),
              textAlign: TextAlign.center,
            ),

            // الوصف (إذا كان موجوداً)
            if (description != null) ...[
              SizedBox(height: spacing / 2),
              Text(
                description!,
                style: AppTypography.createCustomStyle(
                  fontSize: AppDimensions.smallFontSize,
                  fontWeight: AppTypography.weightLight,
                  color: descriptionColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],

            SizedBox(height: spacing),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زر التحديث
                if (onRefresh != null) ...[
                  ElevatedButton.icon(
                    onPressed: onRefresh,
                    icon: Icon(
                      Icons.refresh,
                      size: AppDimensions.smallIconSize,
                    ),
                    label: Text(
                      refreshText,
                      style: AppTypography.createCustomStyle(
                        fontSize: AppDimensions.defaultFontSize,
                        fontWeight: AppTypography.weightMedium,
                        color: AppColors.onPrimary,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(AppDimensions.smallRadius),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.defaultMargin,
                        vertical: AppDimensions.smallMargin,
                      ),
                    ),
                  ),
                  if (onAction != null && actionText != null)
                    SizedBox(width: AppDimensions.defaultSpacing),
                ],

                // زر الإجراء المخصص
                if (onAction != null && actionText != null)
                  OutlinedButton.icon(
                    onPressed: onAction,
                    icon: Icon(
                      Icons.add,
                      size: AppDimensions.smallIconSize,
                    ),
                    label: Text(
                      actionText!,
                      style: AppTypography.createCustomStyle(
                        fontSize: AppDimensions.defaultFontSize,
                        fontWeight: AppTypography.weightMedium,
                        color: AppColors.primary,
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: const BorderSide(color: AppColors.primary),
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(AppDimensions.smallRadius),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.defaultMargin,
                        vertical: AppDimensions.smallMargin,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على حجم الأيقونة حسب حجم العرض
  double _getIconSize() {
    switch (size) {
      case AkStateSize.small:
        return AppDimensions.largeIconSize;
      case AkStateSize.medium:
        return AppDimensions.extraLargeIconSize;
      case AkStateSize.large:
        return AppDimensions.extraLargeIconSize * 1.5;
    }
  }

  /// الحصول على حجم خط العنوان حسب حجم العرض
  double _getTitleFontSize() {
    switch (size) {
      case AkStateSize.small:
        return AppDimensions.largeFontSize;
      case AkStateSize.medium:
        return AppDimensions.titleFontSize;
      case AkStateSize.large:
        return AppDimensions.headingFontSize;
    }
  }

  /// الحصول على حجم خط الرسالة حسب حجم العرض
  double _getMessageFontSize() {
    switch (size) {
      case AkStateSize.small:
        return AppDimensions.defaultFontSize;
      case AkStateSize.medium:
        return AppDimensions.mediumFontSize;
      case AkStateSize.large:
        return AppDimensions.largeFontSize;
    }
  }

  /// الحصول على المسافات حسب حجم العرض
  double _getSpacing() {
    switch (size) {
      case AkStateSize.small:
        return AppDimensions.defaultSpacing;
      case AkStateSize.medium:
        return AppDimensions.mediumSpacing;
      case AkStateSize.large:
        return AppDimensions.largeSpacing;
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● 2. حالة خطأ موحدة (AkErrorState)
// ═══════════════════════════════════════════════════════════════════════════════

/// حالة خطأ موحدة مع تصميم متناسق
///
/// **المميزات:**
/// - تصميم موحد لجميع حالات الأخطاء
/// - دعم أنواع مختلفة من الأخطاء
/// - أزرار إعادة المحاولة والإجراءات
/// - دعم كامل للوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkErrorState(
///   message: 'فشل في تحميل البيانات',
///   onRetry: () => loadData(),
///   errorDetails: 'خطأ في الاتصال بالخادم',
/// )
/// ```
class AkErrorState extends StatelessWidget {
  /// رسالة الخطأ
  final String message;

  /// دالة إعادة المحاولة
  final VoidCallback? onRetry;

  /// دالة الإجراء المخصص
  final VoidCallback? onAction;

  /// نص زر الإجراء
  final String? actionText;

  /// نص زر إعادة المحاولة
  final String retryText;

  /// تفاصيل الخطأ (اختيارية)
  final String? errorDetails;

  /// حجم عرض الحالة
  final AkStateSize size;

  /// أيقونة الخطأ
  final IconData icon;

  /// عنوان الخطأ
  final String? title;

  /// هل يتم عرض تفاصيل الخطأ
  final bool showDetails;

  const AkErrorState({
    super.key,
    required this.message,
    this.onRetry,
    this.onAction,
    this.actionText,
    this.retryText = 'إعادة المحاولة',
    this.errorDetails,
    this.size = AkStateSize.medium,
    this.icon = Icons.error_outline,
    this.title,
    this.showDetails = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // تحديد الأحجام حسب حجم العرض
    final iconSize = _getIconSize();
    final titleFontSize = _getTitleFontSize();
    final messageFontSize = _getMessageFontSize();
    final spacing = _getSpacing();

    // تحديد الألوان حسب الوضع
    const errorColor = AppColors.error;
    final titleColor =
        isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary;
    final messageColor =
        isDark ? AppColors.darkTextSecondary : AppColors.lightTextSecondary;
    final detailsColor =
        isDark ? AppColors.darkTextHint : AppColors.lightTextHint;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.defaultMargin),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة الخطأ
            Container(
              padding: EdgeInsets.all(AppDimensions.defaultMargin),
              decoration: BoxDecoration(
                color: errorColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: iconSize,
                color: errorColor,
              ),
            ),
            SizedBox(height: spacing),

            // عنوان الخطأ (إذا كان موجوداً)
            if (title != null) ...[
              Text(
                title!,
                style: AppTypography.createCustomStyle(
                  fontSize: titleFontSize,
                  fontWeight: AppTypography.weightMedium,
                  color: titleColor,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: spacing / 2),
            ],

            // رسالة الخطأ
            Text(
              message,
              style: AppTypography.createCustomStyle(
                fontSize: messageFontSize,
                fontWeight: AppTypography.weightRegular,
                color: messageColor,
              ),
              textAlign: TextAlign.center,
            ),

            // تفاصيل الخطأ (إذا كانت موجودة ومطلوب عرضها)
            if (showDetails && errorDetails != null) ...[
              SizedBox(height: spacing / 2),
              Container(
                padding: EdgeInsets.all(AppDimensions.smallMargin),
                decoration: BoxDecoration(
                  color: errorColor.withValues(alpha: 0.05),
                  borderRadius:
                      BorderRadius.circular(AppDimensions.smallRadius),
                  border: Border.all(
                    color: errorColor.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Text(
                  errorDetails!,
                  style: AppTypography.createCustomStyle(
                    fontSize: AppDimensions.smallFontSize,
                    fontWeight: AppTypography.weightLight,
                    color: detailsColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],

            SizedBox(height: spacing),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زر إعادة المحاولة
                if (onRetry != null) ...[
                  ElevatedButton.icon(
                    onPressed: onRetry,
                    icon: Icon(
                      Icons.refresh,
                      size: AppDimensions.smallIconSize,
                    ),
                    label: Text(
                      retryText,
                      style: AppTypography.createCustomStyle(
                        fontSize: AppDimensions.defaultFontSize,
                        fontWeight: AppTypography.weightMedium,
                        color: AppColors.onPrimary,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: errorColor,
                      foregroundColor: AppColors.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(AppDimensions.smallRadius),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.defaultMargin,
                        vertical: AppDimensions.smallMargin,
                      ),
                    ),
                  ),
                  if (onAction != null && actionText != null)
                    SizedBox(width: AppDimensions.defaultSpacing),
                ],

                // زر الإجراء المخصص
                if (onAction != null && actionText != null)
                  OutlinedButton.icon(
                    onPressed: onAction,
                    icon: Icon(
                      Icons.settings,
                      size: AppDimensions.smallIconSize,
                    ),
                    label: Text(
                      actionText!,
                      style: AppTypography.createCustomStyle(
                        fontSize: AppDimensions.defaultFontSize,
                        fontWeight: AppTypography.weightMedium,
                        color: errorColor,
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: errorColor,
                      side: const BorderSide(color: errorColor),
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(AppDimensions.smallRadius),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.defaultMargin,
                        vertical: AppDimensions.smallMargin,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على حجم الأيقونة حسب حجم العرض
  double _getIconSize() {
    switch (size) {
      case AkStateSize.small:
        return AppDimensions.largeIconSize;
      case AkStateSize.medium:
        return AppDimensions.extraLargeIconSize;
      case AkStateSize.large:
        return AppDimensions.extraLargeIconSize * 1.5;
    }
  }

  /// الحصول على حجم خط العنوان حسب حجم العرض
  double _getTitleFontSize() {
    switch (size) {
      case AkStateSize.small:
        return AppDimensions.largeFontSize;
      case AkStateSize.medium:
        return AppDimensions.titleFontSize;
      case AkStateSize.large:
        return AppDimensions.headingFontSize;
    }
  }

  /// الحصول على حجم خط الرسالة حسب حجم العرض
  double _getMessageFontSize() {
    switch (size) {
      case AkStateSize.small:
        return AppDimensions.defaultFontSize;
      case AkStateSize.medium:
        return AppDimensions.mediumFontSize;
      case AkStateSize.large:
        return AppDimensions.largeFontSize;
    }
  }

  /// الحصول على المسافات حسب حجم العرض
  double _getSpacing() {
    switch (size) {
      case AkStateSize.small:
        return AppDimensions.defaultSpacing;
      case AkStateSize.medium:
        return AppDimensions.mediumSpacing;
      case AkStateSize.large:
        return AppDimensions.largeSpacing;
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● الدوال المساعدة السريعة (AkStates)
// ═══════════════════════════════════════════════════════════════════════════════

/// فئة الدوال المساعدة السريعة للحالات
/// توفر طرق سريعة لعرض الحالات الشائعة
class AkStates {
  AkStates._();

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال الحالات الفارغة السريعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// حالة فارغة للمنتجات
  static Widget emptyProducts({
    VoidCallback? onRefresh,
    VoidCallback? onAddProduct,
  }) {
    return AkEmptyState(
      title: 'لا توجد منتجات',
      message: 'لم يتم العثور على أي منتجات في المخزن',
      description: 'ابدأ بإضافة منتجات جديدة لإدارة مخزونك',
      icon: Icons.inventory_2_outlined,
      onRefresh: onRefresh,
      onAction: onAddProduct,
      actionText: 'إضافة منتج',
    );
  }

  /// حالة فارغة للعملاء
  static Widget emptyCustomers({
    VoidCallback? onRefresh,
    VoidCallback? onAddCustomer,
  }) {
    return AkEmptyState(
      title: 'لا يوجد عملاء',
      message: 'لم يتم العثور على أي عملاء مسجلين',
      description: 'ابدأ بإضافة عملاء جدد لإدارة قاعدة عملائك',
      icon: Icons.people_outline,
      onRefresh: onRefresh,
      onAction: onAddCustomer,
      actionText: 'إضافة عميل',
    );
  }

  /// حالة فارغة للمبيعات
  static Widget emptySales({
    VoidCallback? onRefresh,
    VoidCallback? onNewSale,
  }) {
    return AkEmptyState(
      title: 'لا توجد مبيعات',
      message: 'لم يتم تسجيل أي عمليات بيع اليوم',
      description: 'ابدأ ببيع منتجاتك وتسجيل العمليات',
      icon: Icons.point_of_sale_outlined,
      onRefresh: onRefresh,
      onAction: onNewSale,
      actionText: 'بيع جديد',
    );
  }

  /// حالة فارغة للمشتريات
  static Widget emptyPurchases({
    VoidCallback? onRefresh,
    VoidCallback? onNewPurchase,
  }) {
    return AkEmptyState(
      title: 'لا توجد مشتريات',
      message: 'لم يتم تسجيل أي عمليات شراء',
      description: 'ابدأ بتسجيل مشترياتك من الموردين',
      icon: Icons.shopping_cart_outlined,
      onRefresh: onRefresh,
      onAction: onNewPurchase,
      actionText: 'شراء جديد',
    );
  }

  /// حالة فارغة للتقارير
  static Widget emptyReports({
    VoidCallback? onRefresh,
    VoidCallback? onGenerateReport,
  }) {
    return AkEmptyState(
      title: 'لا توجد تقارير',
      message: 'لم يتم إنشاء أي تقارير بعد',
      description: 'قم بإنشاء تقارير لمتابعة أداء عملك',
      icon: Icons.assessment_outlined,
      onRefresh: onRefresh,
      onAction: onGenerateReport,
      actionText: 'إنشاء تقرير',
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال حالات الأخطاء السريعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// خطأ في الاتصال بالإنترنت
  static Widget connectionError({
    VoidCallback? onRetry,
    VoidCallback? onSettings,
  }) {
    return AkErrorState(
      title: 'خطأ في الاتصال',
      message: 'تعذر الاتصال بالإنترنت',
      errorDetails: 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى',
      icon: Icons.wifi_off,
      onRetry: onRetry,
      onAction: onSettings,
      actionText: 'إعدادات الشبكة',
      showDetails: true,
    );
  }

  /// خطأ في الخادم
  static Widget serverError({
    VoidCallback? onRetry,
    String? errorCode,
  }) {
    return AkErrorState(
      title: 'خطأ في الخادم',
      message: 'حدث خطأ في الخادم',
      errorDetails: errorCode != null
          ? 'رمز الخطأ: $errorCode'
          : 'يرجى المحاولة مرة أخرى لاحقاً',
      icon: Icons.dns_outlined,
      onRetry: onRetry,
      showDetails: true,
    );
  }

  /// خطأ في تحميل البيانات
  static Widget dataLoadError({
    VoidCallback? onRetry,
    String? specificError,
  }) {
    return AkErrorState(
      title: 'فشل في تحميل البيانات',
      message: 'تعذر تحميل البيانات المطلوبة',
      errorDetails: specificError ?? 'حدث خطأ أثناء تحميل البيانات',
      icon: Icons.error_outline,
      onRetry: onRetry,
      showDetails: specificError != null,
    );
  }

  /// خطأ في الصلاحيات
  static Widget permissionError({
    VoidCallback? onRetry,
    VoidCallback? onRequestPermission,
  }) {
    return AkErrorState(
      title: 'ليس لديك صلاحية',
      message: 'ليس لديك صلاحية للوصول إلى هذه الميزة',
      errorDetails: 'تواصل مع المدير للحصول على الصلاحيات المطلوبة',
      icon: Icons.lock_outline,
      onRetry: onRetry,
      onAction: onRequestPermission,
      actionText: 'طلب صلاحية',
      showDetails: true,
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال متخصصة للمشروع التجاري
  // ───────────────────────────────────────────────────────────────────────────────

  /// حالة فارغة للمخزون المنخفض
  static Widget lowStockEmpty({
    VoidCallback? onRefresh,
    VoidCallback? onManageStock,
  }) {
    return AkEmptyState(
      title: 'لا توجد منتجات منخفضة المخزون',
      message: 'جميع منتجاتك لديها مخزون كافي',
      description: 'هذا أمر جيد! مخزونك في حالة ممتازة',
      icon: Icons.check_circle_outline,
      customIconColor: AppColors.success,
      onRefresh: onRefresh,
      onAction: onManageStock,
      actionText: 'إدارة المخزون',
    );
  }

  /// خطأ في عملية الدفع
  static Widget paymentError({
    VoidCallback? onRetry,
    VoidCallback? onContactSupport,
    String? errorMessage,
  }) {
    return AkErrorState(
      title: 'فشل في عملية الدفع',
      message: 'تعذر إتمام عملية الدفع',
      errorDetails: errorMessage ?? 'حدث خطأ أثناء معالجة عملية الدفع',
      icon: Icons.payment,
      onRetry: onRetry,
      onAction: onContactSupport,
      actionText: 'تواصل مع الدعم',
      showDetails: true,
    );
  }

  /// حالة فارغة للإشعارات
  static Widget emptyNotifications({
    VoidCallback? onRefresh,
  }) {
    return AkEmptyState(
      title: 'لا توجد إشعارات',
      message: 'لم تتلق أي إشعارات جديدة',
      description: 'ستظهر الإشعارات الجديدة هنا عند وصولها',
      icon: Icons.notifications_none,
      onRefresh: onRefresh,
      size: AkStateSize.small,
    );
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🔄 القسم الخامس: مؤشرات التحميل المتقدمة (منقولة من shared_widgets.dart)
// ═══════════════════════════════════════════════════════════════════════════════

// ───────────────────────────────────────────────────────────────────────────────
// ● مؤشر التحميل الموحد (AkLoadingIndicator)
// ───────────────────────────────────────────────────────────────────────────────

/// مؤشر التحميل الموحد والمحسن
/// يدعم عرض رسالة اختيارية مع المؤشر ودعم كامل للوضع المظلم/الفاتح
///
/// **المميزات:**
/// - أحجام متعددة قابلة للتخصيص
/// - دعم كامل للوضع المظلم والفاتح
/// - رسائل تحميل اختيارية
/// - تصميم موحد مع باقي النظام
/// - عدم وجود قيم صريحة
///
/// **مثال الاستخدام:**
/// ```dart
/// AkLoadingIndicator(
///   size: AppDimensions.largeIconSize,
///   message: 'جاري تحميل البيانات...',
///   showMessage: true,
/// )
///
/// // أو استخدام المُنشئات المبسطة
/// AkLoadingIndicator.small()
/// AkLoadingIndicator.large(message: 'جاري الحفظ...')
/// ```
class AkLoadingIndicator extends StatelessWidget {
  /// حجم المؤشر
  final double size;

  /// لون المؤشر (null للون التلقائي)
  final Color? color;

  /// سمك خط المؤشر
  final double strokeWidth;

  /// رسالة التحميل الاختيارية
  final String? message;

  /// هل يتم عرض الرسالة أسفل المؤشر
  final bool showMessage;

  const AkLoadingIndicator({
    super.key,
    this.size = 48.0, // AppDimensions.largeIconSize
    this.color,
    this.strokeWidth = 4.0, // AppDimensions.spacing4
    this.message,
    this.showMessage = true,
  });

  /// مُنشئ مبسط لمؤشر صغير
  const AkLoadingIndicator.small({
    super.key,
    this.color,
    this.message,
  })  : size = 32.0, // AppDimensions.mediumIconSize
        strokeWidth = 2.0, // AppDimensions.spacing2
        showMessage = false;

  /// مُنشئ مبسط لمؤشر كبير
  const AkLoadingIndicator.large({
    super.key,
    this.color,
    this.message,
  })  : size = 64.0, // AppDimensions.extraLargeIconSize
        strokeWidth = 4.0, // AppDimensions.spacing4
        showMessage = true;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final effectiveColor = color ?? AppColors.primary;

    // مؤشر بسيط بدون رسالة
    if (!showMessage || message == null) {
      return SizedBox(
        width: size,
        height: size,
        child: CircularProgressIndicator(
          strokeWidth: strokeWidth,
          valueColor: AlwaysStoppedAnimation<Color>(effectiveColor),
        ),
      );
    }

    // مؤشر مع رسالة
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: strokeWidth,
            valueColor: AlwaysStoppedAnimation<Color>(effectiveColor),
          ),
        ),
        SizedBox(height: AppDimensions.defaultMargin),
        Text(
          message!,
          textAlign: TextAlign.center,
          style: AppTypography.createCustomStyle(
            fontSize: AppTypography.fontSizeMedium,
            color: isDark
                ? AppColors.darkTextSecondary
                : AppColors.lightTextSecondary,
            fontWeight: AppTypography.weightMedium,
          ),
        ),
      ],
    );
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// ● طبقة التحميل الموحدة (AkLoadingOverlay)
// ───────────────────────────────────────────────────────────────────────────────

/// طبقة التحميل الموحدة والمحسنة
/// تعرض مؤشر التحميل فوق المحتوى مع دعم كامل للوضع المظلم/الفاتح
///
/// **المميزات:**
/// - طبقة تحميل شفافة فوق المحتوى
/// - دعم كامل للوضع المظلم والفاتح
/// - رسائل تحميل اختيارية
/// - تصميم موحد مع باقي النظام
/// - تحكم في الشفافية والألوان
///
/// **مثال الاستخدام:**
/// ```dart
/// AkLoadingOverlay(
///   isLoading: isLoadingData,
///   message: 'جاري حفظ البيانات...',
///   child: YourContentWidget(),
/// )
/// ```
class AkLoadingOverlay extends StatelessWidget {
  /// هل يتم عرض التحميل
  final bool isLoading;

  /// المحتوى الأساسي
  final Widget child;

  /// رسالة التحميل
  final String? message;

  /// لون الخلفية (null للون التلقائي)
  final Color? backgroundColor;

  /// لون المؤشر (null للون التلقائي)
  final Color? progressColor;

  /// شفافية الخلفية (0.0 - 1.0)
  final double opacity;

  const AkLoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
    this.backgroundColor,
    this.progressColor,
    this.opacity = 0.5,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // تحديد لون الخلفية حسب الوضع
    final effectiveBackgroundColor = backgroundColor ??
        (isDark ? AppColors.darkSurface : AppColors.lightSurface);

    return Stack(
      children: [
        child,
        if (isLoading)
          Positioned.fill(
            child: Container(
              color: effectiveBackgroundColor.withValues(alpha: opacity),
              child: Center(
                child: AkLoadingIndicator(
                  color: progressColor,
                  message: message,
                  showMessage: message != null,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
