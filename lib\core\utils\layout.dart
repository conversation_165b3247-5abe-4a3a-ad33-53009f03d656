import 'dart:math';
import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';

/// نظام التخطيط والاستجابة
/// يوفر أدوات شاملة لإنشاء واجهات متجاوبة وآمنة
///
/// الميزات:
/// - أبعاد متجاوبة (عرض، ارتفاع، نسب مئوية)
/// - فحص نوع الجهاز (هاتف، تابلت، سطح مكتب)
/// - أحجام متجاوبة (خطوط، أيقونات، شبكات)
/// - هوامش ومسافات ذكية
/// - عناصر آمنة (نص، زر، حقل إدخال، بطاقة)
/// - تخطيط مرن (صف، عمود، شبكة)
///
/// الاستخدام:
/// ```dart
/// // تهيئة النظام
/// Layout.init(context);
///
/// // الحصول على أبعاد متجاوبة
/// final width = Layout.w(50); // 50% من عرض الشاشة
/// final height = Layout.h(30); // 30% من ارتفاع الشاشة
///
/// // إنشاء عناصر آمنة
/// Layout.safeText('النص هنا')
/// Layout.safeButton(label: 'زر', onPressed: () {})
/// ```
class Layout {
  // ========== المتغيرات الأساسية ==========

  static late MediaQueryData _mediaQueryData;
  static double screenWidth = 360.0;
  static double screenHeight = 640.0;
  static double blockSizeHorizontal = 3.6;
  static double blockSizeVertical = 6.4;
  static double safeBlockHorizontal = 3.6;
  static double safeBlockVertical = 6.4;

  // ========== الثوابت ==========

  // أحجام الشاشات
  static const double mobileScreenSize = 600;
  static const double tabletScreenSize = 900;
  static const double desktopScreenSize = 1200;

  // نسب الشاشات
  static const double mobileScreenRatio = 0.6;
  static const double tabletScreenRatio = 0.9;
  static const double desktopScreenRatio = 1.5;

  // الحد الأدنى للأبعاد
  static const double minButtonWidth = 80;
  static const double minButtonHeight = 40;
  static const double minRowHeight = 48;

  // المسافات الافتراضية
  static const double defaultSpacing = 16.0;
  static const double smallSpacing = 8.0;
  static const double largeSpacing = 24.0;

  // أنصاف الأقطار (Border Radius)
  static const double defaultRadius = 8.0;
  static const double smallRadius = 4.0;
  static const double mediumRadius = 12.0;
  static const double largeRadius = 16.0;

  // أحجام الأيقونات
  static const double smallIconSize = 16.0;
  static const double mediumIconSize = 24.0;
  static const double largeIconSize = 32.0;
  static const double quickAccessIconSize = 40.0;

  // أحجام الخطوط
  static const double smallFontSize = 12.0;
  static const double mediumFontSize = 16.0;
  static const double defaultFontSize = 16.0; // نفس mediumFontSize للتوافق
  static const double largeFontSize = 20.0;
  static const double titleFontSize = 24.0;

  // أحجام البطاقات والعناصر
  static const double mediumCardHeight = 120.0;
  static const double quickAccessItemHeight = 100.0;

  // هوامش
  static const double mediumMargin = 16.0;

  // مسافات الشبكة
  static const double quickAccessGridSpacing = 16.0;
  static const double quickAccessGridPadding = 16.0;

  // ========== التهيئة ==========

  /// تهيئة نظام التخطيط
  static void init(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);
    screenWidth = _mediaQueryData.size.width;
    screenHeight = _mediaQueryData.size.height;
    blockSizeHorizontal = screenWidth / 100;
    blockSizeVertical = screenHeight / 100;

    final safeAreaHorizontal =
        _mediaQueryData.padding.left + _mediaQueryData.padding.right;
    final safeAreaVertical =
        _mediaQueryData.padding.top + _mediaQueryData.padding.bottom;
    safeBlockHorizontal = (screenWidth - safeAreaHorizontal) / 100;
    safeBlockVertical = (screenHeight - safeAreaVertical) / 100;
  }

  // ========== الأبعاد المتجاوبة ==========

  /// إرجاع نسبة عرض مئوية من عرض الشاشة
  static double w(double val) => blockSizeHorizontal * val;

  /// إرجاع نسبة ارتفاع مئوية من ارتفاع الشاشة
  static double h(double val) => blockSizeVertical * val;

  /// إرجاع عرض آمن (مع مراعاة المناطق الآمنة)
  static double safeW(double val) => safeBlockHorizontal * val;

  /// إرجاع ارتفاع آمن (مع مراعاة المناطق الآمنة)
  static double safeH(double val) => safeBlockVertical * val;

  // ========== فحص نوع الجهاز ==========

  /// اختبار إذا كان الجهاز هاتف محمول
  static bool isMobile() => screenWidth < mobileScreenSize;

  /// اختبار إذا كان الجهاز تابلت
  static bool isTablet() =>
      screenWidth >= mobileScreenSize && screenWidth < tabletScreenSize;

  /// اختبار إذا كان الجهاز سطح مكتب
  static bool isDesktop() => screenWidth >= tabletScreenSize;

  // ========== الأحجام المتجاوبة ==========

  /// إرجاع حجم الخط المتجاوب
  static double getResponsiveFontSize(double fontSize) {
    double scaleFactor = isMobile() ? 1 : (isTablet() ? 1.2 : 1.5);
    double responsiveSize = fontSize * scaleFactor;

    // ضمان الحدود
    return responsiveSize.clamp(12.0, 32.0);
  }

  /// إرجاع حجم الأيقونة المتجاوب
  static double getResponsiveIconSize(double size) {
    if (isMobile()) return size * mobileScreenRatio;
    if (isTablet()) return size * tabletScreenRatio;
    return size * desktopScreenRatio;
  }

  /// إرجاع عدد الأعمدة المناسب للشبكة
  static int getGridColumnCount([BuildContext? context]) {
    if (isMobile()) return 2;
    if (isTablet()) return 3;
    return 4;
  }

  /// إرجاع نسبة العرض إلى الارتفاع للشبكة
  static double getGridAspectRatio() {
    if (isMobile()) return 1.2;
    return 1.5;
  }

  // ========== الهوامش والمسافات ==========

  /// الحصول على حشوة متجاوبة
  static EdgeInsetsGeometry getResponsivePadding({
    double? horizontal,
    double? vertical,
    double? all,
  }) {
    if (all != null) {
      return EdgeInsets.all(w(all));
    }
    return EdgeInsets.symmetric(
      horizontal: horizontal != null ? w(horizontal) : 0,
      vertical: vertical != null ? h(vertical) : 0,
    );
  }

  /// الحصول على هامش متجاوب
  static EdgeInsetsGeometry getResponsiveMargin({
    double? horizontal,
    double? vertical,
    double? all,
  }) {
    if (all != null) {
      return EdgeInsets.all(w(all));
    }
    return EdgeInsets.symmetric(
      horizontal: horizontal != null ? w(horizontal) : 0,
      vertical: vertical != null ? h(vertical) : 0,
    );
  }

  // ========== العناصر الآمنة ==========

  /// إنشاء نص آمن يتكيف مع المساحة المتاحة
  static Widget safeText(
    String text, {
    AppTypography? style,
    TextAlign? textAlign,
    int maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final effectiveStyle = style ?? const AppTypography();
        final fontSize = effectiveStyle.fontSize ?? 14;

        return Text(
          text,
          style: effectiveStyle.copyWith(
            fontSize: getResponsiveFontSize(fontSize),
          ),
          textAlign: textAlign,
          maxLines: maxLines,
          overflow: overflow,
        );
      },
    );
  }

  /// إنشاء زر آمن يتكيف مع المساحة المتاحة
  static Widget safeButton({
    required String label,
    required VoidCallback? onPressed,
    AppTypography? textStyle,
    Color? backgroundColor,
    Color? foregroundColor,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    BorderRadiusGeometry? borderRadius,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final effectiveWidth =
            width ?? max(minButtonWidth, constraints.maxWidth * 0.8);
        final effectiveHeight = height ?? max(minButtonHeight, h(6));

        return SizedBox(
          width: min(effectiveWidth, constraints.maxWidth),
          height: effectiveHeight,
          child: ElevatedButton(
            onPressed: onPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: backgroundColor ?? AppColors.primary,
              foregroundColor: foregroundColor ?? AppColors.onPrimary,
              padding:
                  padding ?? getResponsivePadding(horizontal: 4, vertical: 2),
              shape: RoundedRectangleBorder(
                borderRadius: borderRadius ?? BorderRadius.circular(8),
              ),
            ),
            child: safeText(
              label,
              style: textStyle ??
                  const AppTypography(
                    fontWeight: FontWeight.w500,
                    color: AppColors.onPrimary,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
    );
  }

  /// إنشاء حقل إدخال آمن
  static Widget safeTextField({
    TextEditingController? controller,
    String? labelText,
    String? hintText,
    IconData? prefixIcon,
    Widget? suffixIcon,
    TextInputType keyboardType = TextInputType.text,
    bool obscureText = false,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    int maxLines = 1,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return TextFormField(
          controller: controller,
          decoration: InputDecoration(
            labelText: labelText,
            hintText: hintText,
            prefixIcon: prefixIcon != null
                ? Icon(prefixIcon, size: getResponsiveIconSize(20))
                : null,
            suffixIcon: suffixIcon,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: getResponsivePadding(horizontal: 3, vertical: 2),
          ),
          keyboardType: keyboardType,
          obscureText: obscureText,
          validator: validator,
          onChanged: onChanged,
          maxLines: maxLines,
          style: AppTypography(
            fontSize: getResponsiveFontSize(14),
          ),
        );
      },
    );
  }

  /// إنشاء بطاقة آمنة
  static Widget safeCard({
    required Widget child,
    double? width,
    double? height,
    double? elevation,
    Color? color,
    BorderRadiusGeometry? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        Widget card = Card(
          elevation: elevation ?? 2,
          color: color,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(12),
          ),
          margin: margin ?? getResponsiveMargin(all: 2),
          child: Container(
            width: width,
            height: height,
            padding: padding ?? getResponsivePadding(all: 4),
            child: child,
          ),
        );

        if (onTap != null) {
          card = InkWell(
            onTap: onTap,
            borderRadius:
                borderRadius as BorderRadius? ?? BorderRadius.circular(12),
            child: card,
          );
        }

        return card;
      },
    );
  }

  /// إنشاء شاشة آمنة
  static Widget safeScreen({
    PreferredSizeWidget? appBar,
    required Widget body,
    Widget? floatingActionButton,
    Widget? bottomNavigationBar,
    Color? backgroundColor,
  }) {
    return Scaffold(
      appBar: appBar,
      body: body,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
      backgroundColor: backgroundColor,
    );
  }

  /// الحصول على هامش آمن
  static EdgeInsetsGeometry getSafeMargin({
    double? horizontal,
    double? vertical,
    double? all,
  }) {
    return getResponsiveMargin(
      horizontal: horizontal,
      vertical: vertical,
      all: all,
    );
  }

  /// الحصول على حشوة آمنة
  static EdgeInsetsGeometry getSafePadding({
    double? horizontal,
    double? vertical,
    double? all,
  }) {
    return getResponsivePadding(
      horizontal: horizontal,
      vertical: vertical,
      all: all,
    );
  }

  /// إنشاء صف آمن
  static Widget safeRow({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    double spacing = 8.0,
  }) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: _addSpacingBetweenItems(children, spacing),
    );
  }

  /// إنشاء صف آمن يتحول إلى Wrap عند الحاجة
  static Widget flexibleRow({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    double spacing = 8.0,
    double runSpacing = 8.0,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // إذا كان العرض يكفي، استخدم Row
        if (constraints.maxWidth > 600) {
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: _addSpacingBetweenItems(children, spacing),
          );
        }

        // وإلا استخدم Wrap
        return Wrap(
          spacing: spacing,
          runSpacing: runSpacing,
          alignment: WrapAlignment.start,
          crossAxisAlignment: WrapCrossAlignment.center,
          children: children,
        );
      },
    );
  }

  /// إنشاء عمود آمن مع مسافات
  static Widget safeColumn({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    double spacing = 8.0,
  }) {
    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: _addVerticalSpacingBetweenItems(children, spacing),
    );
  }

  /// إنشاء شبكة آمنة
  static Widget safeGrid({
    required List<Widget> children,
    int? crossAxisCount,
    double spacing = 8.0,
    double childAspectRatio = 1.0,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final effectiveCrossAxisCount = crossAxisCount ?? getGridColumnCount();

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: effectiveCrossAxisCount,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }

  // ========== دوال مساعدة ==========

  /// إضافة مسافات أفقية بين العناصر
  static List<Widget> _addSpacingBetweenItems(
      List<Widget> items, double spacing) {
    if (items.isEmpty || items.length == 1) return items;

    final result = <Widget>[];
    for (int i = 0; i < items.length; i++) {
      result.add(items[i]);
      if (i < items.length - 1) {
        result.add(SizedBox(width: spacing));
      }
    }
    return result;
  }

  /// إضافة مسافات عمودية بين العناصر
  static List<Widget> _addVerticalSpacingBetweenItems(
      List<Widget> items, double spacing) {
    if (items.isEmpty || items.length == 1) return items;

    final result = <Widget>[];
    for (int i = 0; i < items.length; i++) {
      result.add(items[i]);
      if (i < items.length - 1) {
        result.add(SizedBox(height: spacing));
      }
    }
    return result;
  }

  /// حساب العدد الأمثل للأعمدة بناءً على العرض المتاح
  static int calculateOptimalColumnCount(double availableWidth) {
    if (availableWidth < 300) return 1;
    if (availableWidth < 600) return 2;
    if (availableWidth < 900) return 3;
    return 4;
  }

  /// الحصول على عرض مناسب للعناصر
  static double getResponsiveItemWidth({int itemsPerRow = 3}) {
    double itemWidth;

    if (screenWidth < 320) {
      itemWidth = screenWidth - 32; // عنصر واحد
    } else if (screenWidth < 480) {
      itemWidth = (screenWidth - 48) / 2; // عنصرين
    } else if (screenWidth < 720) {
      itemWidth = (screenWidth - 64) / itemsPerRow; // العدد المحدد
    } else {
      itemWidth = (screenWidth - 96) / (itemsPerRow + 1); // عناصر أكثر
    }

    return itemWidth;
  }
}
