import '../../../core/models/base_model.dart';

/// نموذج تفاصيل العملية
/// يستخدم لتخزين تفاصيل العمليات مثل بنود الفواتير والسندات
class OperationDetail extends BaseModel {
  final String operationId; // معرف العملية الرئيسية
  final int lineNumber; // رقم السطر
  final String? productId; // معرف المنتج (للفواتير)
  final String? accountId; // معرف الحساب (للقيود المحاسبية)
  final double quantity; // الكمية
  final double unitPrice; // سعر الوحدة
  final double discount; // قيمة الخصم
  final double tax; // قيمة الضريبة
  final double amount; // المبلغ الإجمالي
  final String? unitId; // معرف وحدة القياس
  final String? description; // وصف
  final Map<String, dynamic>? metadata; // بيانات إضافية

  OperationDetail({
    String? id,
    required this.operationId,
    required this.lineNumber,
    this.productId,
    this.accountId,
    this.quantity = 1.0,
    this.unitPrice = 0.0,
    this.discount = 0.0,
    this.tax = 0.0,
    required this.amount,
    this.unitId,
    this.description,
    this.metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isDeleted = false,
  }) : super(
          id: id ?? '',
          createdAt: createdAt ?? DateTime.now(),
          updatedAt: updatedAt,
          isDeleted: isDeleted,
        );

  factory OperationDetail.fromJson(Map<String, dynamic> json) {
    return OperationDetail(
      id: json['id'],
      operationId: json['operationId'],
      lineNumber: json['lineNumber'],
      productId: json['productId'],
      accountId: json['accountId'],
      quantity: json['quantity']?.toDouble() ?? 1.0,
      unitPrice: json['unitPrice']?.toDouble() ?? 0.0,
      discount: json['discount']?.toDouble() ?? 0.0,
      tax: json['tax']?.toDouble() ?? 0.0,
      amount: json['amount']?.toDouble() ?? 0.0,
      unitId: json['unitId'],
      description: json['description'],
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
      createdAt:
          json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      isDeleted: json['isDeleted'] ?? false,
    );
  }

  factory OperationDetail.fromMap(Map<String, dynamic> map) {
    return OperationDetail(
      id: map['id']?.toString(),
      operationId: map['operation_id'],
      lineNumber: map['line_number'],
      productId: map['product_id'],
      accountId: map['account_id'],
      quantity: map['quantity']?.toDouble() ?? 1.0,
      unitPrice: map['unit_price']?.toDouble() ?? 0.0,
      discount: map['discount']?.toDouble() ?? 0.0,
      tax: map['tax']?.toDouble() ?? 0.0,
      amount: map['amount']?.toDouble() ?? 0.0,
      unitId: map['unit_id'],
      description: map['description'],
      metadata: map['metadata'] != null
          ? Map<String, dynamic>.from(map['metadata'])
          : null,
      createdAt:
          map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      isDeleted: map['is_deleted'] == 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'operationId': operationId,
      'lineNumber': lineNumber,
      'productId': productId,
      'accountId': accountId,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'discount': discount,
      'tax': tax,
      'amount': amount,
      'unitId': unitId,
      'description': description,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isDeleted': isDeleted,
    };
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'operation_id': operationId,
      'line_number': lineNumber,
      'product_id': productId,
      'account_id': accountId,
      'quantity': quantity,
      'unit_price': unitPrice,
      'discount': discount,
      'tax': tax,
      'amount': amount,
      'unit_id': unitId,
      'description': description,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  OperationDetail copyWith({
    String? id,
    String? operationId,
    int? lineNumber,
    String? productId,
    String? accountId,
    double? quantity,
    double? unitPrice,
    double? discount,
    double? tax,
    double? amount,
    String? unitId,
    String? description,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
  }) {
    return OperationDetail(
      id: id ?? this.id,
      operationId: operationId ?? this.operationId,
      lineNumber: lineNumber ?? this.lineNumber,
      productId: productId ?? this.productId,
      accountId: accountId ?? this.accountId,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      discount: discount ?? this.discount,
      tax: tax ?? this.tax,
      amount: amount ?? this.amount,
      unitId: unitId ?? this.unitId,
      description: description ?? this.description,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }
}
