import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../theme/app_dimensions.dart';
import '../theme/index.dart';
import '../utils/app_logger.dart';

/// خدمة توحيد قاعدة البيانات
/// توفر واجهة لتوحيد قاعدة البيانات وإصلاح المشاكل
/// ملاحظة: تم تحديث هذه الخدمة لاستخدام DatabaseHelper بدلاً من DatabaseInitializer
class DatabaseUnifierService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// حالة التوحيد
  bool _isUnifying = false;

  /// الحصول على حالة التوحيد
  bool get isUnifying => _isUnifying;

  /// توحيد قاعدة البيانات
  /// يقوم بإصلاح مشاكل قاعدة البيانات والتحقق من صحتها
  /// يمكن تقديم دالة callback لعرض تقدم العملية
  Future<bool> unifyDatabase({
    Function(String message, double progress)? onProgress,
  }) async {
    if (_isUnifying) {
      AppLogger.warning('عملية توحيد قاعدة البيانات قيد التنفيذ بالفعل');
      return false;
    }

    _isUnifying = true;

    try {
      // إخطار بدء العملية
      onProgress?.call('بدء توحيد قاعدة البيانات...', 0.0);

      // التحقق من صحة قاعدة البيانات
      onProgress?.call('التحقق من صحة قاعدة البيانات...', 0.3);
      final result = await _databaseHelper.checkDatabaseHealth();

      // إخطار انتهاء العملية
      onProgress?.call(
          result
              ? 'تم التحقق من صحة قاعدة البيانات بنجاح'
              : 'تم إصلاح بعض المشاكل في قاعدة البيانات',
          1.0);

      return true;
    } catch (e) {
      AppLogger.error('خطأ أثناء توحيد قاعدة البيانات: $e');
      onProgress?.call('حدث خطأ أثناء توحيد قاعدة البيانات', 1.0);
      return false;
    } finally {
      _isUnifying = false;
    }
  }

  /// عرض حوار توحيد قاعدة البيانات
  /// يعرض حوار يسمح للمستخدم ببدء عملية توحيد قاعدة البيانات
  Future<bool?> showUnifyDatabaseDialog(BuildContext context) async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => _UnifyDatabaseDialog(
        databaseUnifierService: this,
      ),
    );
  }
}

/// حوار توحيد قاعدة البيانات
class _UnifyDatabaseDialog extends StatefulWidget {
  final DatabaseUnifierService databaseUnifierService;

  const _UnifyDatabaseDialog({
    Key? key,
    required this.databaseUnifierService,
  }) : super(key: key);

  @override
  _UnifyDatabaseDialogState createState() => _UnifyDatabaseDialogState();
}

class _UnifyDatabaseDialogState extends State<_UnifyDatabaseDialog> {
  bool _isUnifying = false;
  String _message = 'هل تريد توحيد قاعدة البيانات؟';
  double _progress = 0.0;
  bool _isCompleted = false;
  bool _isSuccess = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
          _isUnifying ? 'جاري توحيد قاعدة البيانات' : 'توحيد قاعدة البيانات'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(_message),
          const SizedBox(height: AppDimensions.spacing16),
          if (_isUnifying || _isCompleted)
            LinearProgressIndicator(value: _progress),
        ],
      ),
      actions: [
        if (!_isUnifying && !_isCompleted)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(false);
            },
            child: const Text('إلغاء'),
          ),
        if (!_isUnifying && !_isCompleted)
          TextButton(
            onPressed: _startUnifying,
            child: const Text('توحيد'),
          ),
        if (_isCompleted)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(_isSuccess);
            },
            child: const Text('إغلاق'),
          ),
      ],
    );
  }

  /// بدء عملية توحيد قاعدة البيانات
  Future<void> _startUnifying() async {
    setState(() {
      _isUnifying = true;
      _message = 'جاري توحيد قاعدة البيانات...';
      _progress = 0.0;
    });

    final result = await widget.databaseUnifierService.unifyDatabase(
      onProgress: (message, progress) {
        setState(() {
          _message = message;
          _progress = progress;
        });
      },
    );

    setState(() {
      _isUnifying = false;
      _isCompleted = true;
      _isSuccess = result;
      _message = result
          ? 'تم توحيد قاعدة البيانات بنجاح'
          : 'فشل في توحيد قاعدة البيانات';
    });
  }
}
