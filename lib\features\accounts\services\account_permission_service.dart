import 'package:uuid/uuid.dart';
import '../../../core/database/database_service.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../models/account_permission.dart';

/// خدمة إدارة صلاحيات الحسابات
class AccountPermissionService {
  final DatabaseService _databaseService = DatabaseService.instance;
  static const String _accountPermissionsTable = 'account_permissions';

  /// إنشاء جدول صلاحيات الحسابات إذا لم يكن موجوداً
  Future<void> createTable() async {
    try {
      final db = await _databaseService.database;
      await db.execute('''
        CREATE TABLE IF NOT EXISTS $_accountPermissionsTable (
          id TEXT PRIMARY KEY,
          user_id TEXT NOT NULL,
          account_id TEXT NOT NULL,
          can_view INTEGER NOT NULL DEFAULT 1,
          can_edit INTEGER NOT NULL DEFAULT 0,
          can_delete INTEGER NOT NULL DEFAULT 0,
          can_create_transactions INTEGER NOT NULL DEFAULT 0,
          created_at TEXT,
          updated_at TEXT,
          is_deleted INTEGER NOT NULL DEFAULT 0,
          FOREIGN KEY (user_id) REFERENCES users (id),
          FOREIGN KEY (account_id) REFERENCES accounts (id)
        )
      ''');

      // إنشاء فهرس للبحث السريع
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_account_permissions_user 
        ON $_accountPermissionsTable (user_id)
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_account_permissions_account 
        ON $_accountPermissionsTable (account_id)
      ''');

      AppLogger.info('تم إنشاء جدول صلاحيات الحسابات بنجاح');
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إنشاء جدول صلاحيات الحسابات',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// إضافة صلاحية حساب جديدة
  Future<bool> addPermission(AccountPermission permission) async {
    try {
      final db = await _databaseService.database;

      // التحقق من وجود الصلاحية
      final existingPermission = await db.query(
        _accountPermissionsTable,
        where: 'user_id = ? AND account_id = ? AND is_deleted = 0',
        whereArgs: [permission.userId, permission.accountId],
      );

      if (existingPermission.isNotEmpty) {
        throw Exception('الصلاحية موجودة بالفعل');
      }

      // إنشاء معرف جديد للصلاحية
      final permissionId = permission.id ?? const Uuid().v4();
      final now = DateTime.now().toIso8601String();

      // إدراج الصلاحية الجديدة
      await db.insert(
        _accountPermissionsTable,
        {
          'id': permissionId,
          'user_id': permission.userId,
          'account_id': permission.accountId,
          'can_view': permission.canView ? 1 : 0,
          'can_edit': permission.canEdit ? 1 : 0,
          'can_delete': permission.canDelete ? 1 : 0,
          'can_create_transactions': permission.canCreateTransactions ? 1 : 0,
          'created_at': now,
          'updated_at': now,
          'is_deleted': 0,
        },
      );

      AppLogger.info('تم إضافة صلاحية الحساب بنجاح: $permissionId');
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة صلاحية الحساب',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// الحصول على صلاحيات حساب بواسطة معرف المستخدم
  Future<List<AccountPermission>> getPermissionsByUserId(String userId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        _accountPermissionsTable,
        where: 'user_id = ? AND is_deleted = 0',
        whereArgs: [userId],
      );

      return List.generate(
          maps.length, (i) => AccountPermission.fromMap(maps[i]));
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على صلاحيات الحساب',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على صلاحيات حساب بواسطة معرف الحساب
  Future<List<AccountPermission>> getPermissionsByAccountId(
      String accountId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        _accountPermissionsTable,
        where: 'account_id = ? AND is_deleted = 0',
        whereArgs: [accountId],
      );

      return List.generate(
          maps.length, (i) => AccountPermission.fromMap(maps[i]));
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على صلاحيات الحساب',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// تحديث صلاحية حساب
  Future<bool> updatePermission(AccountPermission permission) async {
    try {
      final db = await _databaseService.database;

      if (permission.id == null) {
        throw Exception('معرف الصلاحية غير موجود');
      }

      final now = DateTime.now().toIso8601String();

      // تحديث الصلاحية
      await db.update(
        _accountPermissionsTable,
        {
          'can_view': permission.canView ? 1 : 0,
          'can_edit': permission.canEdit ? 1 : 0,
          'can_delete': permission.canDelete ? 1 : 0,
          'can_create_transactions': permission.canCreateTransactions ? 1 : 0,
          'updated_at': now,
        },
        where: 'id = ?',
        whereArgs: [permission.id],
      );

      AppLogger.info('تم تحديث صلاحية الحساب بنجاح: ${permission.id}');
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث صلاحية الحساب',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// حذف صلاحية حساب (حذف منطقي)
  Future<bool> deletePermission(String id) async {
    try {
      final db = await _databaseService.database;
      final now = DateTime.now().toIso8601String();

      // حذف منطقي للصلاحية
      await db.update(
        _accountPermissionsTable,
        {
          'is_deleted': 1,
          'updated_at': now,
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      AppLogger.info('تم حذف صلاحية الحساب بنجاح: $id');
      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف صلاحية الحساب',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }
}
