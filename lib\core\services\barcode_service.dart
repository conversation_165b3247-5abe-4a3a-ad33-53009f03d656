import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:audioplayers/audioplayers.dart';
import '../utils/error_tracker.dart';
import '../utils/app_logger.dart';
import '../../../core/theme/app_colors.dart';

/// خدمة لمسح الباركود
class BarcodeService {
  static final BarcodeService _instance = BarcodeService._internal();
  factory BarcodeService() => _instance;
  BarcodeService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  final MobileScannerController _controller = MobileScannerController();

  /// مسح الباركود
  Future<String?> scanBarcode({required BuildContext context}) async {
    try {
      final completer = Completer<String?>();

      // عرض الماسح في حوار
      final result = await showDialog<String?>(
        context: context,
        builder: (dialogContext) => AlertDialog(
          contentPadding: EdgeInsets.zero,
          content: SizedBox(
            width: 300,
            height: 300,
            child: Stack(
              children: [
                MobileScanner(
                  controller: _controller,
                  onDetect: (capture) {
                    final List<Barcode> barcodes = capture.barcodes;
                    if (barcodes.isNotEmpty && !completer.isCompleted) {
                      final String code = barcodes.first.rawValue ?? '';
                      if (code.isNotEmpty) {
                        _playBeepSound();
                        completer.complete(code);
                        Navigator.pop(context, code);
                      }
                    }
                  },
                ),
                Positioned(
                  top: 10,
                  right: 10,
                  child: IconButton(
                    icon: const Icon(Icons.close, color: AppColors.onPrimary),
                    onPressed: () {
                      Navigator.pop(context);
                      if (!completer.isCompleted) {
                        completer.complete(null);
                      }
                    },
                  ),
                ),
                Positioned(
                  bottom: 10,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.flash_on,
                            color: AppColors.onPrimary),
                        onPressed: () => _controller.toggleTorch(),
                      ),
                      IconButton(
                        icon: const Icon(Icons.flip_camera_ios,
                            color: AppColors.onPrimary),
                        onPressed: () => _controller.switchCamera(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      // Si el diálogo se cierra sin un resultado, completar el completer
      if (!completer.isCompleted) {
        completer.complete(result);
      }

      final barcode = await completer.future;

      if (barcode != null) {
        AppLogger.info('Código de barras escaneado: $barcode');
      }

      return barcode;
    } on PlatformException catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error al escanear código de barras',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Error inesperado al escanear código de barras',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Reproducir sonido de éxito al escanear
  Future<void> _playBeepSound() async {
    try {
      await _audioPlayer.play(AssetSource('sounds/beep.mp3'));
    } catch (e) {
      // Ignorar errores al reproducir sonido
      AppLogger.warning('No se pudo reproducir el sonido de escaneo: $e');
    }
  }

  /// تحرير الموارد
  void dispose() {
    _controller.dispose();
  }
}
