import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج القيد البسيط
/// 
/// يمثل هذا النموذج قيد محاسبي بسيط بين حسابين (مدين ودائن)
class SimpleTie extends BaseModel {
  /// رقم القيد
  final String? number;

  /// تاريخ القيد
  final DateTime date;

  /// المبلغ
  final double amount;

  /// معرف الحساب المدين (من)
  final String fromAccountId;

  /// معرف الحساب الدائن (إلى)
  final String toAccountId;

  /// معرف العملة
  final String currencyId;

  /// سعر الصرف (مقابل العملة الأساسية)
  final double exchangeRate;

  /// ملاحظات
  final String? notes;

  /// معرف المستخدم الذي أنشأ القيد
  final String userId;

  /// اسم المستخدم (للعرض فقط)
  final String? userName;

  /// إنشاء قيد بسيط جديد
  SimpleTie({
    String? id,
    this.number,
    DateTime? date,
    required this.amount,
    required this.fromAccountId,
    required this.toAccountId,
    required this.currencyId,
    this.exchangeRate = 1.0,
    this.notes,
    required this.userId,
    this.userName,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : date = date ?? DateTime.now(),
       super(
         id: id ?? const Uuid().v4(),
         createdAt: createdAt ?? DateTime.now(),
         createdBy: createdBy,
         updatedAt: updatedAt,
         updatedBy: updatedBy,
         isDeleted: isDeleted,
       );

  /// نسخ القيد مع تعديل بعض الخصائص
  SimpleTie copyWith({
    String? id,
    String? number,
    DateTime? date,
    double? amount,
    String? fromAccountId,
    String? toAccountId,
    String? currencyId,
    double? exchangeRate,
    String? notes,
    String? userId,
    String? userName,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return SimpleTie(
      id: id ?? this.id,
      number: number ?? this.number,
      date: date ?? this.date,
      amount: amount ?? this.amount,
      fromAccountId: fromAccountId ?? this.fromAccountId,
      toAccountId: toAccountId ?? this.toAccountId,
      currencyId: currencyId ?? this.currencyId,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      notes: notes ?? this.notes,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل القيد إلى خريطة
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'number': number,
      'date': date.toIso8601String(),
      'amount': amount,
      'fromAccountId': fromAccountId,
      'toAccountId': toAccountId,
      'currencyId': currencyId,
      'exchangeRate': exchangeRate,
      'notes': notes,
      'userId': userId,
      'userName': userName,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'updatedAt': updatedAt?.toIso8601String(),
      'updatedBy': updatedBy,
      'isDeleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء قيد من خريطة
  factory SimpleTie.fromJson(Map<String, dynamic> json) {
    return SimpleTie(
      id: json['id'],
      number: json['number'],
      date: DateTime.parse(json['date']),
      amount: json['amount'] is int ? (json['amount'] as int).toDouble() : json['amount'],
      fromAccountId: json['fromAccountId'],
      toAccountId: json['toAccountId'],
      currencyId: json['currencyId'],
      exchangeRate: json['exchangeRate'] is int ? (json['exchangeRate'] as int).toDouble() : json['exchangeRate'] ?? 1.0,
      notes: json['notes'],
      userId: json['userId'],
      userName: json['userName'],
      createdAt: DateTime.parse(json['createdAt']),
      createdBy: json['createdBy'],
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      updatedBy: json['updatedBy'],
      isDeleted: json['isDeleted'] == 1,
    );
  }
}
