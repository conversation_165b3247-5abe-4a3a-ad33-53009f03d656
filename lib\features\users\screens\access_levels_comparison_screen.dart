import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';

import '../../../core/theme/index.dart';
import '../presenters/role_presenter.dart';
import 'simplified_access_management_screen.dart';

/// شاشة مقارنة مستويات الوصول
/// تتيح للمستخدم مقارنة مستويات الوصول المختلفة
class AccessLevelsComparisonScreen extends StatefulWidget {
  static const String routeName = '/access-levels-comparison';

  const AccessLevelsComparisonScreen({Key? key}) : super(key: key);

  @override
  State<AccessLevelsComparisonScreen> createState() =>
      _AccessLevelsComparisonScreenState();
}

class _AccessLevelsComparisonScreenState
    extends State<AccessLevelsComparisonScreen> {
  bool _isLoading = true;
  String? _error;

  // مستويات الوصول المتاحة
  List<AccessLevel> _accessLevels = [];

  // مستويات الوصول المحددة للمقارنة
  final List<AccessLevel?> _selectedAccessLevels = [null, null];

  // الوظائف المتاحة في النظام
  final List<JobFunction> _jobFunctions = [
    JobFunction(
      id: 'sales',
      name: 'المبيعات',
      icon: Icons.point_of_sale,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المبيعات والفواتير والعملاء',
    ),
    JobFunction(
      id: 'inventory',
      name: 'المخزون',
      icon: Icons.inventory_2,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المنتجات والمخزون والمستودعات',
    ),
    JobFunction(
      id: 'purchases',
      name: 'المشتريات',
      icon: Icons.shopping_cart,
      color: AppColors.lightTextSecondary,
      description: 'إدارة المشتريات والموردين',
    ),
    JobFunction(
      id: 'finance',
      name: 'المالية',
      icon: Icons.account_balance,
      color: AppColors.lightTextSecondary,
      description: 'إدارة الحسابات والمصروفات والإيرادات',
    ),
    JobFunction(
      id: 'reports',
      name: 'التقارير',
      icon: Icons.bar_chart,
      color: AppColors.lightTextSecondary,
      description: 'عرض وطباعة التقارير المختلفة',
    ),
    JobFunction(
      id: 'settings',
      name: 'الإعدادات',
      icon: Icons.settings,
      color: AppColors.lightTextSecondary,
      description: 'إدارة إعدادات النظام والمستخدمين',
    ),
  ];

  @override
  void initState() {
    super.initState();

    // تحميل البيانات بعد بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // الحصول على مقدم الأدوار
      final rolePresenter =
          AppProviders.getLazyPresenter<RolePresenter>(() => RolePresenter());

      // تحميل الأدوار
      await rolePresenter.loadRoles();

      // تحويل الأدوار إلى مستويات وصول
      _accessLevels = _convertRolesToAccessLevels(rolePresenter.roles);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'حدث خطأ أثناء تحميل البيانات: $e';
      });
    }
  }

  /// تحويل الأدوار إلى مستويات وصول
  List<AccessLevel> _convertRolesToAccessLevels(List<dynamic> roles) {
    // سيتم تنفيذها لاحقاً
    // هذه محاكاة للبيانات
    return [
      AccessLevel(
        id: 'admin',
        name: 'مدير النظام',
        isCustom: false,
        functionLevels: {
          'sales': AccessLevelType.full,
          'inventory': AccessLevelType.full,
          'purchases': AccessLevelType.full,
          'finance': AccessLevelType.full,
          'reports': AccessLevelType.full,
          'settings': AccessLevelType.full,
        },
      ),
      AccessLevel(
        id: 'sales_manager',
        name: 'مدير مبيعات',
        isCustom: true,
        functionLevels: {
          'sales': AccessLevelType.full,
          'inventory': AccessLevelType.view,
          'purchases': AccessLevelType.none,
          'finance': AccessLevelType.view,
          'reports': AccessLevelType.view,
          'settings': AccessLevelType.none,
        },
      ),
      AccessLevel(
        id: 'accountant',
        name: 'محاسب',
        isCustom: true,
        functionLevels: {
          'sales': AccessLevelType.view,
          'inventory': AccessLevelType.view,
          'purchases': AccessLevelType.view,
          'finance': AccessLevelType.full,
          'reports': AccessLevelType.full,
          'settings': AccessLevelType.none,
        },
      ),
      AccessLevel(
        id: 'inventory_manager',
        name: 'مدير مخزون',
        isCustom: true,
        functionLevels: {
          'sales': AccessLevelType.view,
          'inventory': AccessLevelType.full,
          'purchases': AccessLevelType.edit,
          'finance': AccessLevelType.none,
          'reports': AccessLevelType.view,
          'settings': AccessLevelType.none,
        },
      ),
      AccessLevel(
        id: 'cashier',
        name: 'أمين صندوق',
        isCustom: true,
        functionLevels: {
          'sales': AccessLevelType.edit,
          'inventory': AccessLevelType.view,
          'purchases': AccessLevelType.none,
          'finance': AccessLevelType.edit,
          'reports': AccessLevelType.view,
          'settings': AccessLevelType.none,
        },
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مقارنة مستويات الوصول'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const AppTypography(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_accessLevels.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.no_accounts,
              size: 64,
              color: AppColors.lightTextSecondary,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد مستويات وصول',
              style: AppTypography(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // محددات مستويات الوصول
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.lightTextSecondary,
            boxShadow: [
              BoxShadow(
                color: AppColors.lightTextPrimary.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'اختر مستويات الوصول للمقارنة:',
                style: AppTypography(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildAccessLevelSelector(0),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildAccessLevelSelector(1),
                  ),
                ],
              ),
            ],
          ),
        ),

        // جدول المقارنة
        Expanded(
          child: _selectedAccessLevels[0] == null ||
                  _selectedAccessLevels[1] == null
              ? _buildNoSelectionMessage()
              : _buildComparisonTable(),
        ),
      ],
    );
  }

  /// بناء محدد مستوى الوصول
  Widget _buildAccessLevelSelector(int index) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: AppColors.lightTextSecondary,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.lightTextSecondary.withValues(alpha: 0.4),
        ),
      ),
      child: DropdownButton<String>(
        value: _selectedAccessLevels[index]?.id,
        isExpanded: true,
        hint: Text('اختر مستوى الوصول ${index + 1}'),
        underline: const SizedBox(),
        onChanged: (value) {
          setState(() {
            if (value == null) {
              _selectedAccessLevels[index] = null;
            } else {
              _selectedAccessLevels[index] =
                  _accessLevels.firstWhere((level) => level.id == value);
            }
          });
        },
        items: [
          const DropdownMenuItem<String>(
            value: null,
            child: Text('-- اختر مستوى الوصول --'),
          ),
          ..._accessLevels.map((level) {
            return DropdownMenuItem<String>(
              value: level.id,
              child: Text(level.name),
            );
          }).toList(),
        ],
      ),
    );
  }

  /// بناء رسالة عدم اختيار مستويات الوصول
  Widget _buildNoSelectionMessage() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.compare_arrows,
            size: 64,
            color: AppColors.lightTextSecondary,
          ),
          SizedBox(height: 16),
          Text(
            'اختر مستويي وصول للمقارنة',
            style: AppTypography(fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء جدول المقارنة
  Widget _buildComparisonTable() {
    final level1 = _selectedAccessLevels[0]!;
    final level2 = _selectedAccessLevels[1]!;

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // عنوان الجدول
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.12),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
          ),
          child: Row(
            children: [
              const Expanded(
                flex: 2,
                child: Text(
                  'الوظيفة',
                  style: AppTypography(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  level1.name,
                  style: const AppTypography(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  level2.name,
                  style: const AppTypography(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),

        // صفوف الجدول
        ...List.generate(_jobFunctions.length, (index) {
          final jobFunction = _jobFunctions[index];
          final level1Type =
              level1.functionLevels[jobFunction.id] ?? AccessLevelType.none;
          final level2Type =
              level2.functionLevels[jobFunction.id] ?? AccessLevelType.none;

          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.lightTextSecondary,
              border: Border(
                bottom: BorderSide(
                  color: AppColors.lightTextSecondary.withValues(alpha: 0.4),
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Row(
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: jobFunction.color.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          jobFunction.icon,
                          color: jobFunction.color,
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              jobFunction.name,
                              style: const AppTypography(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              jobFunction.description,
                              style: const AppTypography(
                                fontSize: 12,
                                color: AppColors.lightTextSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: _buildAccessLevelBadge(level1Type),
                ),
                Expanded(
                  flex: 1,
                  child: _buildAccessLevelBadge(level2Type),
                ),
              ],
            ),
          );
        }),

        // ملخص المقارنة
        Container(
          margin: const EdgeInsets.only(top: 24),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.amber.withValues(alpha: 0.12),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'ملخص المقارنة:',
                style: AppTypography(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _getComparisonSummary(level1, level2),
                style: const AppTypography(
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء شارة مستوى الوصول
  Widget _buildAccessLevelBadge(AccessLevelType type) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getAccessLevelColor(type).withValues(alpha: 0.12),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        _getAccessLevelName(type),
        style: AppTypography(
          fontSize: 12,
          color: _getAccessLevelColor(type),
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// الحصول على اسم مستوى الوصول
  String _getAccessLevelName(AccessLevelType type) {
    switch (type) {
      case AccessLevelType.none:
        return 'لا وصول';
      case AccessLevelType.view:
        return 'عرض فقط';
      case AccessLevelType.edit:
        return 'تعديل';
      case AccessLevelType.full:
        return 'وصول كامل';
    }
  }

  /// الحصول على لون مستوى الوصول
  Color _getAccessLevelColor(AccessLevelType type) {
    switch (type) {
      case AccessLevelType.none:
        return AppColors.accessNone;
      case AccessLevelType.view:
        return AppColors.accessView;
      case AccessLevelType.edit:
        return AppColors.accessEdit;
      case AccessLevelType.full:
        return AppColors.accessFull;
    }
  }

  /// الحصول على ملخص المقارنة
  String _getComparisonSummary(AccessLevel level1, AccessLevel level2) {
    // عدد الوظائف التي لها وصول أعلى في كل مستوى
    int level1Higher = 0;
    int level2Higher = 0;
    int equal = 0;

    for (final jobFunction in _jobFunctions) {
      final level1Type =
          level1.functionLevels[jobFunction.id] ?? AccessLevelType.none;
      final level2Type =
          level2.functionLevels[jobFunction.id] ?? AccessLevelType.none;

      if (_getAccessLevelValue(level1Type) > _getAccessLevelValue(level2Type)) {
        level1Higher++;
      } else if (_getAccessLevelValue(level1Type) <
          _getAccessLevelValue(level2Type)) {
        level2Higher++;
      } else {
        equal++;
      }
    }

    return '${level1.name} لديه وصول أعلى في $level1Higher وظائف.\n'
        '${level2.name} لديه وصول أعلى في $level2Higher وظائف.\n'
        'كلا المستويين متساويان في $equal وظائف.';
  }

  /// الحصول على قيمة مستوى الوصول
  int _getAccessLevelValue(AccessLevelType type) {
    switch (type) {
      case AccessLevelType.none:
        return 0;
      case AccessLevelType.view:
        return 1;
      case AccessLevelType.edit:
        return 2;
      case AccessLevelType.full:
        return 3;
    }
  }
}
