import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../models/promotion.dart';
import '../presenter/promotion_presenter.dart';
import '../../../core/widgets/index.dart';
import 'promotion_form_screen.dart';

import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

class PromotionsScreen extends StatefulWidget {
  const PromotionsScreen({Key? key}) : super(key: key);

  @override
  State<PromotionsScreen> createState() => _PromotionsScreenState();
}

class _PromotionsScreenState extends State<PromotionsScreen> {
  late PromotionPresenter _presenter;

  @override
  void initState() {
    super.initState();
    _presenter = AppProviders.getLazyPresenter<PromotionPresenter>(
        () => PromotionPresenter());
    _loadPromotions();
  }

  Future<void> _loadPromotions() async {
    await _presenter.loadActivePromotions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'العروض والإعلانات',
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToPromotionForm(),
          ),
        ],
      ),
      body: ListenableBuilder(
        listenable: _presenter,
        builder: (context, child) {
          if (_presenter.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (_presenter.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('خطأ: ${_presenter.error}',
                      style: const AppTypography(color: AppColors.error)),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadPromotions,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          final promotions = _presenter.promotions;
          if (promotions.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('لا توجد عروض'),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة عرض جديد'),
                    onPressed: () => _navigateToPromotionForm(),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: promotions.length,
            itemBuilder: (context, index) {
              final promotion = promotions[index];
              return _buildPromotionCard(promotion);
            },
          );
        },
      ),
      floatingActionButton: AkFloatingButton(
        icon: Icons.add,
        onPressed: () => _navigateToPromotionForm(),
        tooltip: 'إضافة عرض جديد',
      ),
    );
  }

  void _navigateToPromotionForm([Promotion? promotion]) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PromotionFormScreen(promotion: promotion),
      ),
    ).then((_) => _loadPromotions());
  }

  Widget _buildPromotionCard(Promotion promotion) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _navigateToPromotionForm(promotion),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            // الشريط العلوي مع العنوان
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Color(int.parse(
                    (promotion.colorHex ?? '#D32F2F').replaceAll('#', '0xff'))),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getIconData(promotion.iconName ?? 'local_offer'),
                    color: AppColors.lightTextSecondary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      promotion.title,
                      style: const AppTypography(
                        color: AppColors.lightTextSecondary,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // تفاصيل العرض
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    promotion.description,
                    style: const AppTypography(
                        fontSize: 14, color: AppColors.lightTextSecondary),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      const Icon(Icons.calendar_today,
                          size: 16, color: AppColors.lightTextSecondary),
                      const SizedBox(width: 8),
                      Text(
                        'ينتهي في: ${promotion.expiryDate}',
                        style: const AppTypography(
                            fontSize: 12, color: AppColors.lightTextSecondary),
                      ),
                      const Spacer(),
                      Text(
                        promotion.isActive ? 'نشط' : 'غير نشط',
                        style: AppTypography(
                          color: promotion.isActive
                              ? AppColors.success
                              : AppColors.error,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      OutlinedButton(
                        onPressed: () => _navigateToPromotionForm(promotion),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Color(int.parse(
                              (promotion.colorHex ?? '#D32F2F')
                                  .replaceAll('#', '0xff'))),
                          side: BorderSide(
                              color: Color(int.parse(
                                  (promotion.colorHex ?? '#D32F2F')
                                      .replaceAll('#', '0xff')))),
                        ),
                        child: const Text('تعديل'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () => _deletePromotion(promotion),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.error,
                          foregroundColor: AppColors.onPrimary,
                        ),
                        child: const Text('حذف'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'local_offer':
        return Icons.local_offer;
      case 'new_releases':
        return Icons.new_releases;
      case 'local_shipping':
        return Icons.local_shipping;
      case 'discount':
        return Icons.discount;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'star':
        return Icons.star;
      case 'trending_up':
        return Icons.trending_up;
      case 'celebration':
        return Icons.celebration;
      case 'emoji_events':
        return Icons.emoji_events;
      case 'card_giftcard':
        return Icons.card_giftcard;
      default:
        return Icons.local_offer;
    }
  }

  Future<void> _deletePromotion(Promotion promotion) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف العرض "${promotion.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child:
                const Text('حذف', style: AppTypography(color: AppColors.error)),
          ),
        ],
      ),
    );

    if (confirm == true) {
      final success = await _presenter.deletePromotion(promotion.id!);
      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف العرض بنجاح')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('فشل في حذف العرض: ${_presenter.error}')),
          );
        }
      }
    }
  }
}
