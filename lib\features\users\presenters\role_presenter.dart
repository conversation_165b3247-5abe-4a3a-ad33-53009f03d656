import 'package:flutter/foundation.dart';
import '../../../core/auth/models/user_role.dart';
import '../../../core/auth/roles_schema.dart';
import '../../../core/utils/error_tracker.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/providers/app_providers.dart';
import '../services/role_service.dart';

/// مقدم الأدوار
class RolePresenter extends ChangeNotifier {
  final RoleService _roleService = RoleService();

  List<UserRole> _roles = [];
  bool _isLoading = false;
  String? _errorMessage;

  /// الحصول على قائمة الأدوار
  List<UserRole> get roles => _roles;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// تحميل الأدوار
  Future<void> loadRoles() async {
    _setLoading(true);

    try {
      _roles = await _roleService.getAllRoles();

      // إذا كانت قائمة الأدوار فارغة، نقوم بإعادة تهيئة الأدوار الافتراضية
      if (_roles.isEmpty) {
        AppLogger.warning(
            'قائمة الأدوار فارغة، جاري إعادة تهيئة الأدوار الافتراضية...');
        await resetDefaultRoles();
        _roles = await _roleService.getAllRoles();
      }

      notifyListeners();
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في تحميل الأدوار: ${e.toString()}');
      ErrorTracker.captureError(
        'فشل في تحميل الأدوار',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على دور بواسطة المعرف
  UserRole? getRoleById(String id) {
    try {
      return _roles.firstWhere((role) => role.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على دور بواسطة الاسم
  UserRole? getRoleByName(String name) {
    try {
      return _roles.firstWhere((role) => role.name == name);
    } catch (e) {
      return null;
    }
  }

  /// إنشاء دور جديد
  Future<bool> createRole(UserRole role) async {
    _setLoading(true);

    try {
      final success = await _roleService.createRole(role);

      if (success) {
        await loadRoles();
      }

      return success;
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في إنشاء الدور: ${e.toString()}');
      ErrorTracker.captureError(
        'فشل في إنشاء الدور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'role': role.toMap(),
        },
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث دور
  Future<bool> updateRole(UserRole role) async {
    _setLoading(true);

    try {
      final success = await _roleService.updateRole(role);

      if (success) {
        await loadRoles();
      }

      return success;
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في تحديث الدور: ${e.toString()}');
      ErrorTracker.captureError(
        'فشل في تحديث الدور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'role': role.toMap(),
        },
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف دور
  Future<bool> deleteRole(String roleId) async {
    _setLoading(true);

    try {
      final success = await _roleService.deleteRole(roleId);

      if (success) {
        await loadRoles();
      }

      return success;
    } catch (e, stackTrace) {
      _setErrorMessage('فشل في حذف الدور: ${e.toString()}');
      ErrorTracker.captureError(
        'فشل في حذف الدور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'roleId': roleId,
        },
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث صلاحيات الدور
  Future<bool> updateRolePermissions(
      String roleId, List<String> permissionIds) async {
    _setLoading(true);

    try {
      // التحقق من وجود الدور
      final role = getRoleById(roleId);
      if (role == null) {
        _setErrorMessage('الدور غير موجود');
        AppLogger.error('محاولة تحديث صلاحيات دور غير موجود: $roleId');
        return false;
      }

      AppLogger.info('🔄 بدء تحديث صلاحيات الدور: ${role.name} (${role.id})');
      AppLogger.info('📊 عدد الصلاحيات الجديدة: ${permissionIds.length}');
      AppLogger.info(
          '🔗 أول 3 معرفات: ${permissionIds.take(3).join(', ')}${permissionIds.length > 3 ? '...' : ''}');

      // استخدام PermissionPresenter لتحديث الصلاحيات في جدول role_permissions
      final permissionPresenter = AppProviders.getPermissionPresenter();
      final success = await permissionPresenter.updateRolePermissions(
          roleId, permissionIds);

      if (success) {
        AppLogger.info('✅ تم تحديث صلاحيات الدور بنجاح');
        // إعادة تحميل الأدوار لتحديث القائمة
        await loadRoles();
      } else {
        AppLogger.error('❌ فشل في تحديث صلاحيات الدور');
        _setErrorMessage('فشل في تحديث صلاحيات الدور');
      }

      return success;
    } catch (e, stackTrace) {
      final errorMessage = 'فشل في تحديث صلاحيات الدور: ${e.toString()}';
      _setErrorMessage(errorMessage);
      AppLogger.error(errorMessage);
      ErrorTracker.captureError(
        'فشل في تحديث صلاحيات الدور',
        error: e,
        stackTrace: stackTrace,
        context: {
          'roleId': roleId,
          'permissionIds': permissionIds,
        },
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إعادة تعيين الأدوار الافتراضية
  Future<bool> resetDefaultRoles() async {
    _setLoading(true);

    try {
      AppLogger.info('جاري إعادة تعيين الأدوار الافتراضية...');

      // محاولة إعادة تعيين الأدوار الافتراضية
      final success = await _roleService.resetDefaultRoles();

      if (success) {
        AppLogger.info('تم إعادة تعيين الأدوار الافتراضية بنجاح');
        await loadRoles();
      } else {
        AppLogger.warning(
            'فشل في إعادة تعيين الأدوار الافتراضية من خلال الخدمة');

        // محاولة إصلاح الأدوار باستخدام طريقة بديلة
        try {
          AppLogger.info('محاولة إصلاح الأدوار باستخدام طريقة بديلة...');

          // استخدام دالة إصلاح البيانات المفقودة
          await _roleService.repairRoles();

          // إعادة تحميل الأدوار
          await loadRoles();

          // التحقق من نجاح الإصلاح
          if (_roles.isNotEmpty) {
            AppLogger.info('تم إصلاح الأدوار بنجاح باستخدام الطريقة البديلة');
            return true;
          } else {
            AppLogger.error('فشل في إصلاح الأدوار باستخدام الطريقة البديلة');
            return false;
          }
        } catch (repairError) {
          AppLogger.error('فشل في إصلاح الأدوار: $repairError');
          return false;
        }
      }

      return success;
    } catch (e, stackTrace) {
      _setErrorMessage(
          'فشل في إعادة تعيين الأدوار الافتراضية: ${e.toString()}');
      ErrorTracker.captureError(
        'فشل في إعادة تعيين الأدوار الافتراضية',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على الصلاحيات المتاحة
  Map<String, Map<String, String>> getAvailablePermissions() {
    return RolesSchema.permissions;
  }

  /// الحصول على الأدوار المتاحة
  Map<String, String> getAvailableRoles() {
    return RolesSchema.roles;
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setErrorMessage(String? message) {
    _errorMessage = message;
    notifyListeners();
  }
}
