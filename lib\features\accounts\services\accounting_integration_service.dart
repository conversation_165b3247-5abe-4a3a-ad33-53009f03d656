import '../../../core/utils/app_logger.dart';
import '../../../core/utils/error_tracker.dart';
import '../../users/services/activity_log_service.dart';
import 'accounting_engine.dart';
import '../../../core/database/database_service.dart';
// سيتم استخدام هذه الخدمة في المستقبل
// import '../services/journal_entry_service.dart';

/// خدمة تكامل المحاسبة
/// مسؤولة عن ربط العمليات التجارية بالنظام المحاسبي
class AccountingIntegrationService {
  static final AccountingIntegrationService _instance =
      AccountingIntegrationService._internal();
  factory AccountingIntegrationService() => _instance;
  AccountingIntegrationService._internal();

  final AccountingEngine _accountingEngine = AccountingEngine();
  final ActivityLogService _activityLogService = ActivityLogService();
  final DatabaseService _db = DatabaseService.instance;
  // سيتم استخدام هذه الخدمة في المستقبل لتنفيذ وظائف متقدمة
  // final JournalEntryService _journalService = JournalEntryService();

  /// معالجة فاتورة مبيعات
  /// إنشاء القيود المحاسبية المرتبطة بفاتورة المبيعات
  Future<String?> handleSalesInvoice(Map<String, dynamic> invoice,
      {String? userId}) async {
    try {
      AppLogger.info(
          'معالجة فاتورة مبيعات في النظام المحاسبي: ${invoice['invoice_number']}');

      // تحويل عناصر الفاتورة إلى النموذج المطلوب
      final items = _convertToSalesInvoiceItems(invoice['items'] ?? []);

      // إنشاء قيد محاسبي لفاتورة المبيعات
      final entryId = await _accountingEngine.createSalesInvoiceEntry(
        date: DateTime.parse(invoice['invoice_date']),
        reference: invoice['invoice_number'],
        description: 'فاتورة مبيعات: ${invoice['invoice_number']}',
        customerAccountId: invoice['customer_id'],
        amount: invoice['total_amount'],
        taxAmount: invoice['tax_amount'] ?? 0.0,
        items: items,
        metadata: {
          'invoiceId': invoice['id'],
          'customerId': invoice['customer_id'],
          'customerName': invoice['customer_name'],
          'items': invoice['items'],
        },
        userId: userId,
      );

      // تسجيل النشاط
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'create',
        module: 'journal_entry',
        details:
            'تم إنشاء قيد محاسبي لفاتورة المبيعات: ${invoice['invoice_number']}',
      );

      return entryId;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في معالجة فاتورة المبيعات في النظام المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {
          'invoiceId': invoice['id'],
          'invoiceNumber': invoice['invoice_number'],
        },
      );

      // تسجيل الخطأ في سجل الأنشطة
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'error',
        module: 'journal_entry',
        details:
            'فشل في إنشاء قيد محاسبي لفاتورة المبيعات: ${invoice['invoice_number']}',
      );

      return null;
    }
  }

  /// معالجة فاتورة مشتريات
  /// إنشاء القيود المحاسبية المرتبطة بفاتورة المشتريات
  Future<String?> handlePurchaseInvoice(Map<String, dynamic> purchase,
      {String? userId}) async {
    try {
      AppLogger.info(
          'معالجة فاتورة مشتريات في النظام المحاسبي: ${purchase['reference_number']}');

      // تحويل عناصر الفاتورة إلى النموذج المطلوب
      final items = _convertToPurchaseInvoiceItems(purchase['items'] ?? []);

      // إنشاء قيد محاسبي لفاتورة المشتريات
      final entryId = await _accountingEngine.createPurchaseInvoiceEntry(
        date: DateTime.parse(purchase['date']),
        reference: purchase['reference_number'],
        description: 'فاتورة مشتريات: ${purchase['reference_number']}',
        supplierAccountId: purchase['supplier_id'],
        amount: purchase['total'],
        taxAmount: purchase['tax'] ?? 0.0,
        items: items,
        metadata: {
          'purchaseId': purchase['id'],
          'supplierId': purchase['supplier_id'],
          'supplierName': purchase['supplier_name'],
          'items': purchase['items'],
        },
        userId: userId,
      );

      // تسجيل النشاط
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'create',
        module: 'journal_entry',
        details:
            'تم إنشاء قيد محاسبي لفاتورة المشتريات: ${purchase['reference_number']}',
      );

      return entryId;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في معالجة فاتورة المشتريات في النظام المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {
          'purchaseId': purchase['id'],
          'referenceNumber': purchase['reference_number'],
        },
      );

      // تسجيل الخطأ في سجل الأنشطة
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'error',
        module: 'journal_entry',
        details:
            'فشل في إنشاء قيد محاسبي لفاتورة المشتريات: ${purchase['reference_number']}',
      );

      return null;
    }
  }

  /// معالجة حركة المخزون
  /// إنشاء القيود المحاسبية المرتبطة بحركة المخزون
  Future<String?> handleInventoryMovement(
    Map<String, dynamic> movement, {
    String? userId,
  }) async {
    try {
      AppLogger.info(
          'معالجة حركة مخزون في النظام المحاسبي: ${movement['reference_number']}');

      // إنشاء قيد محاسبي لحركة المخزون
      final entryId = await _accountingEngine.createInventoryMovementEntry(
        date: DateTime.parse(movement['movement_date']),
        reference: movement['reference_number'],
        description: movement['description'] ?? 'حركة مخزون',
        cost: movement['total_amount'],
        inventoryAccountId: _getInventoryAccountId(movement['to_warehouse_id']),
        contraAccountId: _getInventoryAccountId(movement['from_warehouse_id']),
        metadata: {
          'movementId': movement['id'],
          'referenceNumber': movement['reference_number'],
          'fromWarehouseId': movement['from_warehouse_id'],
          'toWarehouseId': movement['to_warehouse_id'],
          'movementType': movement['movement_type'],
          'items': movement['items'],
        },
        userId: userId,
      );

      // تسجيل النشاط
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'create',
        module: 'journal_entry',
        details:
            'تم إنشاء قيد محاسبي لحركة المخزون: ${movement['reference_number']}',
      );

      return entryId;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في معالجة حركة المخزون في النظام المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {
          'movementId': movement['id'],
          'referenceNumber': movement['reference_number'],
        },
      );

      // تسجيل الخطأ في سجل الأنشطة
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'error',
        module: 'journal_entry',
        details:
            'فشل في إنشاء قيد محاسبي لحركة المخزون: ${movement['reference_number']}',
      );

      return null;
    }
  }

  /// معالجة إيصال قبض
  /// إنشاء القيود المحاسبية المرتبطة بإيصال القبض
  Future<String?> handleReceiptVoucher(
    Map<String, dynamic> receipt, {
    String? userId,
  }) async {
    try {
      AppLogger.info(
          'معالجة إيصال قبض في النظام المحاسبي: ${receipt['voucher_number']}');

      // إنشاء قيد محاسبي لإيصال القبض
      final entryId = await _accountingEngine.createReceiptEntry(
        date: DateTime.parse(receipt['voucher_date']),
        reference: receipt['voucher_number'],
        customerAccountId: receipt['customer_id'],
        cashAccountId: receipt['account_id'],
        amount: receipt['amount'],
        description: receipt['description'] ?? 'إيصال قبض',
        metadata: {
          'voucherId': receipt['id'],
          'voucherNumber': receipt['voucher_number'],
          'customerId': receipt['customer_id'],
          'customerName': receipt['customer_name'],
          'paymentMethod': receipt['payment_method'],
          'referenceNumber': receipt['reference_number'],
        },
        userId: userId,
      );

      // تسجيل النشاط
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'create',
        module: 'journal_entry',
        details:
            'تم إنشاء قيد محاسبي لإيصال القبض: ${receipt['voucher_number']}',
      );

      return entryId;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في معالجة إيصال القبض في النظام المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {
          'voucherId': receipt['id'],
          'voucherNumber': receipt['voucher_number'],
        },
      );

      // تسجيل الخطأ في سجل الأنشطة
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'error',
        module: 'journal_entry',
        details:
            'فشل في إنشاء قيد محاسبي لإيصال القبض: ${receipt['voucher_number']}',
      );

      return null;
    }
  }

  /// معالجة إيصال دفع
  /// إنشاء القيود المحاسبية المرتبطة بإيصال الدفع
  Future<String?> handlePaymentVoucher(
    Map<String, dynamic> payment, {
    String? userId,
  }) async {
    try {
      AppLogger.info(
          'معالجة إيصال دفع في النظام المحاسبي: ${payment['voucher_number']}');

      // إنشاء قيد محاسبي لإيصال الدفع
      final entryId = await _accountingEngine.createPaymentEntry(
        date: DateTime.parse(payment['voucher_date']),
        reference: payment['voucher_number'],
        supplierAccountId: payment['supplier_id'],
        paymentMethodAccountId: payment['account_id'],
        amount: payment['amount'],
        description: payment['description'] ?? 'إيصال دفع',
        metadata: {
          'voucherId': payment['id'],
          'voucherNumber': payment['voucher_number'],
          'supplierId': payment['supplier_id'],
          'supplierName': payment['supplier_name'],
          'paymentMethod': payment['payment_method'],
          'referenceNumber': payment['reference_number'],
        },
        userId: userId,
      );

      // تسجيل النشاط
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'create',
        module: 'journal_entry',
        details:
            'تم إنشاء قيد محاسبي لإيصال الدفع: ${payment['voucher_number']}',
      );

      return entryId;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في معالجة إيصال الدفع في النظام المحاسبي',
        error: e,
        stackTrace: stackTrace,
        context: {
          'voucherId': payment['id'],
          'voucherNumber': payment['voucher_number'],
        },
      );

      // تسجيل الخطأ في سجل الأنشطة
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'error',
        module: 'journal_entry',
        details:
            'فشل في إنشاء قيد محاسبي لإيصال الدفع: ${payment['voucher_number']}',
      );

      return null;
    }
  }

  /// عكس القيود المحاسبية لفاتورة
  /// يستخدم عند إلغاء الفاتورة
  Future<bool> reverseEntriesForInvoice(
    String invoiceId,
    String invoiceType, {
    String? userId,
  }) async {
    try {
      AppLogger.info('عكس القيود المحاسبية للفاتورة: $invoiceId');

      // البحث عن القيود المرتبطة بالفاتورة
      final entries = await _findEntriesForReference(invoiceId, invoiceType);

      if (entries.isEmpty) {
        AppLogger.warning(
            'لم يتم العثور على قيود محاسبية للفاتورة: $invoiceId');
        return false;
      }

      // إلغاء كل قيد
      for (var entry in entries) {
        await _accountingEngine.cancelJournalEntry(entry['id'], userId: userId);

        // تسجيل النشاط
        await _activityLogService.logActivity(
          userId: userId ?? 'system',
          userName: 'النظام',
          action: 'update',
          module: 'journal_entry',
          details: 'تم إلغاء قيد محاسبي للفاتورة: $invoiceId',
        );
      }

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في عكس القيود المحاسبية للفاتورة',
        error: e,
        stackTrace: stackTrace,
        context: {
          'invoiceId': invoiceId,
          'invoiceType': invoiceType,
        },
      );

      // تسجيل الخطأ في سجل الأنشطة
      await _activityLogService.logActivity(
        userId: userId ?? 'system',
        userName: 'النظام',
        action: 'error',
        module: 'journal_entry',
        details: 'فشل في عكس القيود المحاسبية للفاتورة: $invoiceId',
      );

      return false;
    }
  }

  /// البحث عن القيود المرتبطة بمرجع معين
  Future<List<Map<String, dynamic>>> _findEntriesForReference(
    String referenceId,
    String referenceType,
  ) async {
    try {
      return await _db.query(
        'journal_entries',
        where: 'reference_id = ? AND reference_type = ? AND is_deleted = 0',
        whereArgs: [referenceId, referenceType],
      );
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في البحث عن القيود المحاسبية للمرجع',
        error: e,
        stackTrace: stackTrace,
        context: {
          'referenceId': referenceId,
          'referenceType': referenceType,
        },
      );
      return [];
    }
  }

  /// تحويل بنود فاتورة المبيعات إلى نموذج SalesInvoiceItem
  List<SalesInvoiceItem> _convertToSalesInvoiceItems(List<dynamic> items) {
    return items.map((item) {
      return SalesInvoiceItem(
        productId: item['product_id'] ?? '',
        description: item['description'] ?? '',
        quantity: (item['quantity'] is int)
            ? (item['quantity'] as int).toDouble()
            : item['quantity'] ?? 0.0,
        unitPrice: (item['unit_price'] is int)
            ? (item['unit_price'] as int).toDouble()
            : item['unit_price'] ?? 0.0,
        discount: (item['discount'] is int)
            ? (item['discount'] as int).toDouble()
            : item['discount'] ?? 0.0,
        tax: (item['tax'] is int)
            ? (item['tax'] as int).toDouble()
            : item['tax'] ?? 0.0,
        total: (item['total'] is int)
            ? (item['total'] as int).toDouble()
            : item['total'] ?? 0.0,
      );
    }).toList();
  }

  /// تحويل بنود فاتورة المشتريات إلى نموذج PurchaseInvoiceItem
  List<PurchaseInvoiceItem> _convertToPurchaseInvoiceItems(
      List<dynamic> items) {
    return items.map((item) {
      return PurchaseInvoiceItem(
        productId: item['product_id'] ?? '',
        description: item['description'] ?? '',
        quantity: (item['quantity'] is int)
            ? (item['quantity'] as int).toDouble()
            : item['quantity'] ?? 0.0,
        unitPrice: (item['unit_price'] is int)
            ? (item['unit_price'] as int).toDouble()
            : item['unit_price'] ?? 0.0,
        discount: (item['discount'] is int)
            ? (item['discount'] as int).toDouble()
            : item['discount'] ?? 0.0,
        tax: (item['tax'] is int)
            ? (item['tax'] as int).toDouble()
            : item['tax'] ?? 0.0,
        total: (item['total'] is int)
            ? (item['total'] as int).toDouble()
            : item['total'] ?? 0.0,
      );
    }).toList();
  }

  /// الحصول على معرف الحساب الافتراضي
  /// سيتم استخدام هذه الدالة في المستقبل عند تنفيذ وظائف متقدمة
  // String _getDefaultAccountId(String accountKey) {
  //   final defaultAccounts = {
  //     'cash': 'CASH001',
  //     'bank': 'BANK001',
  //     'accounts_receivable': 'AR001',
  //     'accounts_payable': 'AP001',
  //     'sales_revenue': 'REV001',
  //     'sales_tax': 'TAX001',
  //     'purchases': 'PURCH001',
  //     'purchase_tax': 'PTAX001',
  //     'inventory': 'INV001',
  //   };
  //
  //   return defaultAccounts[accountKey] ?? 'UNKNOWN';
  // }

  /// الحصول على معرف حساب المخزون للمستودع
  String _getInventoryAccountId(String warehouseId) {
    // هنا يمكننا استعلام قاعدة البيانات للحصول على حساب المخزون المرتبط بالمستودع
    // للتبسيط، سنقوم بإنشاء معرف افتراضي
    return 'INV_$warehouseId';
  }
}
