import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

import '../presenters/reports_presenter.dart';
import '../../../core/widgets/index.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد

/// شاشة تقرير المنتجات الأكثر ربحية
///
/// تعرض هذه الشاشة تقرير المنتجات الأكثر ربحية مع رسوم بيانية لتوضيح الأرباح
class MostProfitableProductsReportScreen extends StatefulWidget {
  const MostProfitableProductsReportScreen({Key? key}) : super(key: key);

  @override
  State<MostProfitableProductsReportScreen> createState() =>
      _MostProfitableProductsReportScreenState();
}

class _MostProfitableProductsReportScreenState
    extends State<MostProfitableProductsReportScreen> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _mostProfitableProducts = [];
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  int _limit = 10; // عدد المنتجات المراد عرضها

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final presenter = AppProviders.getLazyPresenter<ReportsPresenter>(
          () => ReportsPresenter());

      // تحميل المنتجات الأكثر ربحية
      _mostProfitableProducts = await presenter.getMostProfitableProducts(
        startDate: _startDate,
        endDate: _endDate,
        limit: _limit,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'تقرير المنتجات الأكثر ربحية',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildReportContent(),
    );
  }

  Widget _buildReportContent() {
    if (_mostProfitableProducts.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات للعرض'),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDateRangeSelector(),
            const SizedBox(height: 20),
            _buildPieChart(),
            const SizedBox(height: 20),
            _buildProductsTable(),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نطاق التاريخ',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDateSelector(
                    label: 'من',
                    date: _startDate,
                    onDateSelected: (date) {
                      setState(() {
                        _startDate = date;
                        _loadData();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateSelector(
                    label: 'إلى',
                    date: _endDate,
                    onDateSelected: (date) {
                      setState(() {
                        _endDate = date;
                        _loadData();
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('عدد المنتجات:'),
                const SizedBox(width: 16),
                DropdownButton<int>(
                  value: _limit,
                  items: [5, 10, 20, 50].map((value) {
                    return DropdownMenuItem<int>(
                      value: value,
                      child: Text(value.toString()),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _limit = value;
                        _loadData();
                      });
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector({
    required String label,
    required DateTime date,
    required Function(DateTime) onDateSelected,
  }) {
    return InkWell(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date,
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
        );

        if (selectedDate != null) {
          onDateSelected(selectedDate);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.lightTextSecondary),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const AppTypography(
                fontSize: 14,
                color: AppColors.lightTextSecondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              DateFormat('yyyy/MM/dd').format(date),
              style: const AppTypography(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPieChart() {
    // تحضير بيانات الرسم البياني
    final List<PieChartSectionData> sections = [];
    double totalProfit = 0;

    // حساب إجمالي الربح
    for (var product in _mostProfitableProducts) {
      final profit = product['total_profit'] as double? ?? 0.0;
      totalProfit += profit;
    }

    // إنشاء قطاعات الرسم البياني
    for (int i = 0; i < _mostProfitableProducts.length; i++) {
      final product = _mostProfitableProducts[i];
      // productName غير مستخدم هنا
      final profit = product['total_profit'] as double? ?? 0.0;
      final percentage = totalProfit > 0 ? (profit / totalProfit) * 100 : 0.0;

      // إضافة قطاع فقط إذا كانت النسبة أكبر من 1%
      if (percentage >= 1.0) {
        sections.add(
          PieChartSectionData(
            color: _getSectionColor(i),
            value: profit,
            title: '${percentage.toStringAsFixed(1)}%',
            radius: 100,
            titleStyle: const AppTypography(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.lightTextSecondary,
            ),
          ),
        );
      }
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع الأرباح حسب المنتج',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: sections.isEmpty
                  ? const Center(child: Text('لا توجد بيانات للعرض'))
                  : Row(
                      children: [
                        Expanded(
                          child: PieChart(
                            PieChartData(
                              sectionsSpace: 2,
                              centerSpaceRadius: 40,
                              sections: sections,
                              pieTouchData: PieTouchData(
                                touchCallback:
                                    (FlTouchEvent event, pieTouchResponse) {
                                  setState(() {
                                    // يمكن إضافة تفاعل هنا إذا لزم الأمر
                                  });
                                },
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 150,
                          child: _buildLegend(),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegend() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(
          _mostProfitableProducts.length > 10
              ? 10
              : _mostProfitableProducts.length,
          (index) {
            final product = _mostProfitableProducts[index];
            final productName =
                product['product_name'] as String? ?? 'منتج غير معروف';
            // profit غير مستخدم هنا

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    color: _getSectionColor(index),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _shortenProductName(productName),
                      style: const AppTypography(fontSize: 12),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Color _getSectionColor(int index) {
    final colors = [
      AppColors.info,
      AppColors.success,
      AppColors.warning,
      AppColors.accent,
      AppColors.error,
      AppColors.secondary,
      AppColors.warning,
      AppColors.info,
      AppColors.accent,
      AppColors.secondary,
    ];

    return colors[index % colors.length];
  }

  String _shortenProductName(String name) {
    if (name.length <= 15) {
      return name;
    }
    return '${name.substring(0, 12)}...';
  }

  Widget _buildProductsTable() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'قائمة المنتجات الأكثر ربحية',
              style: AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('المنتج')),
                  DataColumn(label: Text('الكمية المباعة')),
                  DataColumn(label: Text('إجمالي المبيعات')),
                  DataColumn(label: Text('إجمالي التكلفة')),
                  DataColumn(label: Text('إجمالي الربح')),
                  DataColumn(label: Text('هامش الربح')),
                ],
                rows: _mostProfitableProducts.map((product) {
                  final productName =
                      product['product_name'] as String? ?? 'منتج غير معروف';
                  final quantity = product['quantity'] as double? ?? 0.0;
                  final totalSales = product['total_sales'] as double? ?? 0.0;
                  final totalCost = product['total_cost'] as double? ?? 0.0;
                  final totalProfit = product['total_profit'] as double? ?? 0.0;
                  final profitMargin =
                      product['profit_margin'] as double? ?? 0.0;

                  return DataRow(
                    cells: [
                      DataCell(Text(productName)),
                      DataCell(Text(quantity.toStringAsFixed(2))),
                      DataCell(Text('${totalSales.toStringAsFixed(2)} ر.س')),
                      DataCell(Text('${totalCost.toStringAsFixed(2)} ر.س')),
                      DataCell(Text('${totalProfit.toStringAsFixed(2)} ر.س')),
                      DataCell(Text('${profitMargin.toStringAsFixed(2)}%')),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
