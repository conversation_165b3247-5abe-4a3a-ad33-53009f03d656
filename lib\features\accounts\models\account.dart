import 'dart:convert';
import 'package:uuid/uuid.dart';

/// نوع الحساب
enum AccountType {
  asset,
  liability,
  equity,
  revenue,
  expense,
  customer,
  supplier,
  cash,
  bank,
  other,
}

/// نموذج الحساب
class Account {
  final String? id;
  final String name;
  final String code;
  final String? description;
  final AccountType type;
  final String? parentId;
  final String? parentName;
  final double? balance;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? createdBy;
  final String? updatedBy;
  final bool isDeleted;

  /// إنشاء حساب جديد
  Account({
    this.id,
    required this.name,
    required this.code,
    this.description,
    required this.type,
    this.parentId,
    this.parentName,
    this.balance,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.isDeleted = false,
  });

  /// إنشاء نسخة من هذا الحساب مع استبدال الحقول المحددة بقيم جديدة
  Account copyWith({
    String? id,
    String? name,
    String? code,
    String? description,
    AccountType? type,
    String? parentId,
    String? parentName,
    double? balance,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return Account(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      description: description ?? this.description,
      type: type ?? this.type,
      parentId: parentId ?? this.parentId,
      parentName: parentName ?? this.parentName,
      balance: balance ?? this.balance,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// إنشاء حساب من خريطة
  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id'],
      name: map['name'] ?? '',
      code: map['code'] ?? '',
      description: map['description'],
      type: AccountType.values[map['type'] ?? 0],
      parentId: map['parent_id'],
      parentName: map['parent_name'],
      balance: map['balance']?.toDouble(),
      isActive: map['is_active'] == 1,
      createdAt: map['created_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'])
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updated_at'])
          : null,
      createdBy: map['created_by'],
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل الحساب إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id ?? const Uuid().v4(),
      'name': name,
      'code': code,
      'description': description,
      'type': type.index,
      'parent_id': parentId,
      'parent_name': parentName,
      'balance': balance,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt?.millisecondsSinceEpoch ??
          DateTime.now().millisecondsSinceEpoch,
      'updated_at': updatedAt?.millisecondsSinceEpoch,
      'created_by': createdBy,
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// تحويل الحساب إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء حساب من JSON
  factory Account.fromJson(String source) => Account.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Account(id: $id, name: $name, code: $code, description: $description, type: $type, parentId: $parentId, parentName: $parentName, balance: $balance, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, updatedBy: $updatedBy, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Account &&
        other.id == id &&
        other.name == name &&
        other.code == code &&
        other.description == description &&
        other.type == type &&
        other.parentId == parentId &&
        other.parentName == parentName &&
        other.balance == balance &&
        other.isActive == isActive &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.createdBy == createdBy &&
        other.updatedBy == updatedBy &&
        other.isDeleted == isDeleted;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        code.hashCode ^
        description.hashCode ^
        type.hashCode ^
        parentId.hashCode ^
        parentName.hashCode ^
        balance.hashCode ^
        isActive.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        createdBy.hashCode ^
        updatedBy.hashCode ^
        isDeleted.hashCode;
  }
}
