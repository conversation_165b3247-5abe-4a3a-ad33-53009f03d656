import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';
import 'roles_schema.dart';

/// مهيئ الصلاحيات الموحد
/// يستخدم للتحقق من صحة تعريفات الصلاحيات وتهيئتها في قاعدة البيانات
/// يجمع بين وظائف التحقق من الصلاحيات وتهيئتها في قاعدة البيانات
class PermissionInitializer {
  // متغير مؤقت لتخزين معرفات الصلاحيات
  static final Map<String, String> _permissionIds = {};

  // متغير مؤقت لتخزين معرفات الأدوار
  static final Map<String, String> _roleIds = {};

  /// التحقق من وجود تكرارات في أسماء الصلاحيات وإصلاحها
  static void checkDuplicatePermissionNames() {
    AppLogger.info('🔍 التحقق من وجود تكرارات في أسماء الصلاحيات...');

    // جمع جميع أسماء الصلاحيات والبحث عن التكرارات
    final permissionNameCount = <String, int>{};
    final duplicateNames = <String>[];

    // البحث عن التكرارات في أسماء الصلاحيات
    for (final module in RolesSchema.permissions.keys) {
      final modulePermissions = RolesSchema.permissions[module]!;

      for (final permissionCode in modulePermissions.keys) {
        final permissionName = modulePermissions[permissionCode]!;

        permissionNameCount[permissionName] =
            (permissionNameCount[permissionName] ?? 0) + 1;

        if (permissionNameCount[permissionName]! > 1 &&
            !duplicateNames.contains(permissionName)) {
          duplicateNames.add(permissionName);
        }
      }
    }

    // إذا كانت هناك تكرارات، نطبع تحذيراً
    if (duplicateNames.isNotEmpty) {
      AppLogger.warning(
          '⚠️ تم العثور على ${duplicateNames.length} تكرارات في أسماء الصلاحيات:');

      // طباعة التكرارات بالتفصيل
      for (final name in duplicateNames) {
        final modulesAndCodes = <String>[];

        for (final module in RolesSchema.permissions.keys) {
          final modulePermissions = RolesSchema.permissions[module]!;

          for (final code in modulePermissions.keys) {
            if (modulePermissions[code] == name) {
              modulesAndCodes.add('$module.$code');
            }
          }
        }

        AppLogger.warning(
            '  - "$name" مستخدم في: ${modulesAndCodes.join(', ')}');
      }

      AppLogger.warning('⚠️ سيتم استخدام أول تعريف لكل صلاحية مكررة');
    } else {
      AppLogger.info('✅ لا توجد تكرارات في أسماء الصلاحيات');
    }
  }

  /// التحقق من صحة رموز الصلاحيات في تعريفات الأدوار
  static void validateRolePermissionCodes() {
    AppLogger.info('🔍 التحقق من صحة رموز الصلاحيات في تعريفات الأدوار...');

    // جمع جميع رموز الصلاحيات المتاحة
    final allPermissionCodes = <String>[];
    final invalidCodes = <String, List<String>>{};

    // جمع جميع رموز الصلاحيات
    for (final module in RolesSchema.permissions.keys) {
      final modulePermissions = RolesSchema.permissions[module]!;

      for (final code in modulePermissions.keys) {
        allPermissionCodes.add(code);
      }
    }

    // التحقق من صحة رموز الصلاحيات في تعريفات الأدوار
    for (final role in RolesSchema.defaultRolePermissions.keys) {
      final permissions = RolesSchema.defaultRolePermissions[role]!;

      for (final permissionCode in permissions) {
        if (!allPermissionCodes.contains(permissionCode)) {
          if (!invalidCodes.containsKey(role)) {
            invalidCodes[role] = [];
          }
          invalidCodes[role]!.add(permissionCode);
        }
      }
    }

    // طباعة الرموز غير الصالحة
    if (invalidCodes.isNotEmpty) {
      AppLogger.warning(
          '⚠️ تم العثور على رموز صلاحيات غير صالحة في تعريفات الأدوار:');

      for (final role in invalidCodes.keys) {
        final codes = invalidCodes[role]!;
        AppLogger.warning(
            '  - الدور "$role" يحتوي على ${codes.length} رموز غير صالحة: ${codes.join(', ')}');
      }

      AppLogger.warning(
          '⚠️ يرجى تعديل ملف roles_schema.dart يدويًا لإصلاح الرموز غير الصالحة');
    } else {
      AppLogger.info('✅ جميع رموز الصلاحيات في تعريفات الأدوار صالحة');
    }
  }

  /// تهيئة الصلاحيات في قاعدة البيانات
  static Future<Map<String, String>> initializePermissions(
      Transaction txn) async {
    AppLogger.info('🔄 تهيئة الصلاحيات في قاعدة البيانات...');

    try {
      // الحصول على جميع الصلاحيات الموجودة (بما في ذلك المحذوفة)
      final allPermissions = await txn.query('permissions');

      // إنشاء قواميس للصلاحيات الموجودة حسب الاسم والمعرف
      final existingPermissionsByName = <String, Map<String, dynamic>>{};
      final existingPermissionsById = <String, Map<String, dynamic>>{};

      for (final permission in allPermissions) {
        final id = permission['id'] as String;
        final name = permission['name'] as String;
        final isDeleted = permission['is_deleted'] == 1;

        existingPermissionsById[id] = permission;

        // نخزن فقط الصلاحيات غير المحذوفة في قاموس الأسماء
        if (!isDeleted) {
          existingPermissionsByName[name] = permission;
        }
      }

      // إنشاء قاموس لتتبع الصلاحيات التي تمت معالجتها
      final processedPermissionNames = <String>{};

      // إنشاء قاموس لتخزين معرفات الصلاحيات
      final permissionIds = <String, String>{};

      // إنشاء دفعة للعمليات المتعددة
      final batch = txn.batch();
      var addedCount = 0;
      var updatedCount = 0;
      var skippedCount = 0;

      // معالجة جميع الصلاحيات من RolesSchema
      for (final module in RolesSchema.permissions.keys) {
        final modulePermissions = RolesSchema.permissions[module]!;

        for (final permissionCode in modulePermissions.keys) {
          final permissionName = modulePermissions[permissionCode]!;

          // تخطي الصلاحيات المكررة التي تمت معالجتها بالفعل
          if (processedPermissionNames.contains(permissionName)) {
            skippedCount++;
            continue;
          }

          processedPermissionNames.add(permissionName);

          // التحقق مما إذا كانت الصلاحية موجودة بالفعل
          if (existingPermissionsByName.containsKey(permissionName)) {
            // الصلاحية موجودة بالفعل، نستخدم معرفها الحالي
            final existingPermission =
                existingPermissionsByName[permissionName]!;
            final permissionId = existingPermission['id'] as String;

            permissionIds[permissionCode] = permissionId;

            // تحديث وصف الصلاحية ووحدتها إذا لزم الأمر
            final currentDescription =
                existingPermission['description'] as String?;
            final currentModule = existingPermission['module'] as String?;

            final newDescription =
                'صلاحية $permissionName في وحدة $module (رمز: $permissionCode)';

            if (currentDescription != newDescription ||
                currentModule != module) {
              batch.update(
                'permissions',
                {
                  'description': newDescription,
                  'module': module,
                  'updated_at': DateTime.now().toIso8601String(),
                },
                where: 'id = ?',
                whereArgs: [permissionId],
              );
              updatedCount++;
            }
          } else {
            // البحث عن صلاحية محذوفة بنفس الاسم
            final deletedPermission = allPermissions.firstWhere(
              (p) => p['name'] == permissionName && p['is_deleted'] == 1,
              orElse: () => <String, dynamic>{},
            );

            if (deletedPermission.isNotEmpty) {
              // إعادة تفعيل الصلاحية المحذوفة
              final permissionId = deletedPermission['id'] as String;

              permissionIds[permissionCode] = permissionId;

              batch.update(
                'permissions',
                {
                  'description':
                      'صلاحية $permissionName في وحدة $module (رمز: $permissionCode)',
                  'module': module,
                  'updated_at': DateTime.now().toIso8601String(),
                  'is_deleted': 0,
                },
                where: 'id = ?',
                whereArgs: [permissionId],
              );
              updatedCount++;
            } else {
              // إنشاء صلاحية جديدة
              final permissionId = const Uuid().v4();

              permissionIds[permissionCode] = permissionId;

              batch.insert('permissions', {
                'id': permissionId,
                'name': permissionName,
                'description':
                    'صلاحية $permissionName في وحدة $module (رمز: $permissionCode)',
                'module': module,
                'created_at': DateTime.now().toIso8601String(),
                'is_deleted': 0,
              });
              addedCount++;
            }
          }
        }
      }

      // تنفيذ الدفعة إذا كانت هناك عمليات
      if (addedCount > 0 || updatedCount > 0) {
        await batch.commit(noResult: true);

        final summary = [];
        if (addedCount > 0) summary.add('إضافة $addedCount صلاحية جديدة');
        if (updatedCount > 0) summary.add('تحديث $updatedCount صلاحية');
        if (skippedCount > 0) summary.add('تخطي $skippedCount صلاحية مكررة');

        AppLogger.info('✅ تم ${summary.join(' و')}');
      } else {
        AppLogger.info(
            '✅ جميع الصلاحيات موجودة بالفعل ومحدثة، لم تكن هناك حاجة لأي تغييرات');
      }

      // تخزين معرفات الصلاحيات في المتغير المؤقت للاستخدام في ربط الصلاحيات بالأدوار
      _permissionIds.clear();
      _permissionIds.addAll(permissionIds);

      AppLogger.info('✅ تم استخراج معرفات ${permissionIds.length} صلاحية');

      // إعادة معرفات الصلاحيات
      return permissionIds;
    } catch (e, stackTrace) {
      AppLogger.error('❌ فشل في تهيئة الصلاحيات: $e');
      ErrorTracker.captureError(
        'فشل في تهيئة الصلاحيات',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// تهيئة الأدوار في قاعدة البيانات
  static Future<Map<String, String>> initializeRoles(Transaction txn) async {
    AppLogger.info('🔄 تهيئة الأدوار في قاعدة البيانات...');

    try {
      // الحصول على جميع الأدوار الموجودة
      final existingRoles = await txn.query('roles');

      // إنشاء قواميس للأدوار الموجودة حسب الاسم والمعرف
      final existingRolesByName = <String, Map<String, dynamic>>{};
      final existingRolesById = <String, Map<String, dynamic>>{};

      for (final role in existingRoles) {
        final id = role['id'] as String;
        final name = role['name'] as String;
        final isDeleted = role['is_deleted'] == 1;

        existingRolesById[id] = role;

        // نخزن فقط الأدوار غير المحذوفة في قاموس الأسماء
        if (!isDeleted) {
          existingRolesByName[name] = role;
        }
      }

      // إنشاء قاموس لتخزين معرفات الأدوار
      final roleIds = <String, String>{};

      // إنشاء دفعة للعمليات المتعددة
      final batch = txn.batch();
      var addedCount = 0;
      var updatedCount = 0;

      // معالجة جميع الأدوار من RolesSchema
      for (final roleCode in RolesSchema.roles.keys) {
        final roleName = RolesSchema.roles[roleCode]!;

        // التحقق مما إذا كان الدور موجوداً بالفعل
        if (existingRolesByName.containsKey(roleName)) {
          // الدور موجود بالفعل، نستخدم معرفه الحالي
          final existingRole = existingRolesByName[roleName]!;
          final roleId = existingRole['id'] as String;

          roleIds[roleCode] = roleId;

          // تحديث وصف الدور إذا لزم الأمر
          final currentDescription = existingRole['description'] as String?;
          final newDescription = 'دور $roleName في النظام (رمز: $roleCode)';

          if (currentDescription != newDescription) {
            batch.update(
              'roles',
              {
                'description': newDescription,
                'updated_at': DateTime.now().toIso8601String(),
              },
              where: 'id = ?',
              whereArgs: [roleId],
            );
            updatedCount++;
          }
        } else {
          // البحث عن دور محذوف بنفس الاسم
          final deletedRole = existingRoles.firstWhere(
            (r) => r['name'] == roleName && r['is_deleted'] == 1,
            orElse: () => <String, dynamic>{},
          );

          if (deletedRole.isNotEmpty) {
            // إعادة تفعيل الدور المحذوف
            final roleId = deletedRole['id'] as String;

            roleIds[roleCode] = roleId;

            batch.update(
              'roles',
              {
                'description': 'دور $roleName في النظام (رمز: $roleCode)',
                'updated_at': DateTime.now().toIso8601String(),
                'is_deleted': 0,
              },
              where: 'id = ?',
              whereArgs: [roleId],
            );
            updatedCount++;
          } else {
            // إنشاء دور جديد
            final roleId = const Uuid().v4();

            roleIds[roleCode] = roleId;

            batch.insert('roles', {
              'id': roleId,
              'name': roleName,
              'description': 'دور $roleName في النظام (رمز: $roleCode)',
              'is_active': 1,
              'created_at': DateTime.now().toIso8601String(),
              'is_deleted': 0,
            });
            addedCount++;
          }
        }
      }

      // تنفيذ الدفعة إذا كانت هناك عمليات
      if (addedCount > 0 || updatedCount > 0) {
        await batch.commit(noResult: true);

        final summary = [];
        if (addedCount > 0) summary.add('إضافة $addedCount دور جديد');
        if (updatedCount > 0) summary.add('تحديث $updatedCount دور');

        AppLogger.info('✅ تم ${summary.join(' و')}');
      } else {
        AppLogger.info(
            '✅ جميع الأدوار موجودة بالفعل ومحدثة، لم تكن هناك حاجة لأي تغييرات');
      }

      // تخزين معرفات الأدوار في المتغير المؤقت للاستخدام في ربط الصلاحيات بالأدوار
      _roleIds.clear();
      _roleIds.addAll(roleIds);

      AppLogger.info('✅ تم استخراج معرفات ${roleIds.length} دور');

      // إعادة معرفات الأدوار
      return roleIds;
    } catch (e, stackTrace) {
      AppLogger.error('❌ فشل في تهيئة الأدوار: $e');
      ErrorTracker.captureError(
        'فشل في تهيئة الأدوار',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// ربط الصلاحيات بالأدوار
  static Future<void> linkPermissionsToRoles(Transaction txn) async {
    AppLogger.info('🔄 ربط الصلاحيات بالأدوار...');

    try {
      // التحقق من وجود معرفات الصلاحيات والأدوار
      if (_permissionIds.isEmpty || _roleIds.isEmpty) {
        AppLogger.error('❌ لم يتم تهيئة معرفات الصلاحيات أو الأدوار');
        return;
      }

      // حذف جميع روابط الصلاحيات بالأدوار الحالية
      await txn.delete('role_permissions');
      AppLogger.info('✅ تم حذف جميع روابط الصلاحيات بالأدوار السابقة');

      // إنشاء دفعة للعمليات المتعددة
      final batch = txn.batch();
      var totalLinks = 0;
      Map<String, int> rolePermissionCounts = {};
      Map<String, List<String>> missingPermissions = {};

      // إحصائيات مفصلة
      final totalAvailablePermissions = _permissionIds.length;
      AppLogger.info('📊 إجمالي الصلاحيات المتاحة: $totalAvailablePermissions');

      // ربط الصلاحيات بالأدوار حسب RolesSchema.defaultRolePermissions
      for (final roleCode in RolesSchema.defaultRolePermissions.keys) {
        final roleId = _roleIds[roleCode];
        if (roleId == null) {
          AppLogger.warning('⚠️ لم يتم العثور على دور برمز: $roleCode');
          continue;
        }

        final permissions = RolesSchema.defaultRolePermissions[roleCode]!;
        int rolePermissionCount = 0;
        List<String> roleMissingPermissions = [];

        AppLogger.info(
            '🔗 ربط صلاحيات الدور: ${RolesSchema.roles[roleCode]} ($roleCode)');
        AppLogger.info('   📋 عدد الصلاحيات المطلوبة: ${permissions.length}');

        for (final permissionCode in permissions) {
          final permissionId = _permissionIds[permissionCode];
          if (permissionId == null) {
            AppLogger.warning('   ⚠️ صلاحية مفقودة: $permissionCode');
            roleMissingPermissions.add(permissionCode);
            continue;
          }

          // إضافة الربط إلى الدفعة
          batch.insert('role_permissions', {
            'id': const Uuid().v4(),
            'role_id': roleId,
            'permission_id': permissionId,
            'created_at': DateTime.now().toIso8601String(),
            'is_deleted': 0,
          });

          totalLinks++;
          rolePermissionCount++;
        }

        rolePermissionCounts[roleCode] = rolePermissionCount;
        if (roleMissingPermissions.isNotEmpty) {
          missingPermissions[roleCode] = roleMissingPermissions;
        }

        AppLogger.info('   ✅ تم ربط $rolePermissionCount صلاحية بنجاح');
      }

      // تنفيذ الدفعة
      await batch.commit(noResult: true);

      AppLogger.info('✅ تم ربط $totalLinks صلاحية بالأدوار بنجاح');

      // طباعة تقرير مفصل عن توزيع الصلاحيات
      _printDetailedPermissionReport(
          rolePermissionCounts, missingPermissions, totalAvailablePermissions);
    } catch (e, stackTrace) {
      AppLogger.error('❌ فشل في ربط الصلاحيات بالأدوار: $e');
      ErrorTracker.captureError(
        'فشل في ربط الصلاحيات بالأدوار',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// طباعة تقرير مفصل عن توزيع الصلاحيات
  static void _printDetailedPermissionReport(
    Map<String, int> rolePermissionCounts,
    Map<String, List<String>> missingPermissions,
    int totalAvailablePermissions,
  ) {
    AppLogger.info('');
    AppLogger.info(
        '📊 ═══════════════════════════════════════════════════════════');
    AppLogger.info('📊 تقرير مفصل عن توزيع الصلاحيات على الأدوار');
    AppLogger.info(
        '📊 ═══════════════════════════════════════════════════════════');
    AppLogger.info(
        '📊 إجمالي الصلاحيات المتاحة في النظام: $totalAvailablePermissions');
    AppLogger.info('📊 إجمالي الأدوار: ${rolePermissionCounts.length}');
    AppLogger.info('');

    // ترتيب الأدوار حسب عدد الصلاحيات (من الأكثر إلى الأقل)
    final sortedRoles = rolePermissionCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    for (final entry in sortedRoles) {
      final roleCode = entry.key;
      final permissionCount = entry.value;
      final roleName = RolesSchema.roles[roleCode] ?? roleCode;
      final expectedCount =
          RolesSchema.defaultRolePermissions[roleCode]?.length ?? 0;

      // تحديد حالة الدور
      String status = '✅';
      if (permissionCount != expectedCount) {
        status = '⚠️';
      }
      if (missingPermissions.containsKey(roleCode)) {
        status = '❌';
      }

      AppLogger.info('$status $roleName ($roleCode):');
      AppLogger.info(
          '   📋 الصلاحيات المربوطة: $permissionCount من أصل $expectedCount');

      if (permissionCount == totalAvailablePermissions) {
        AppLogger.info('   🔓 صلاحيات كاملة (جميع الصلاحيات)');
      } else {
        final percentage = ((permissionCount / totalAvailablePermissions) * 100)
            .toStringAsFixed(1);
        AppLogger.info('   📊 نسبة التغطية: $percentage%');
      }

      // طباعة الصلاحيات المفقودة إن وجدت
      if (missingPermissions.containsKey(roleCode)) {
        final missing = missingPermissions[roleCode]!;
        AppLogger.info(
            '   ❌ صلاحيات مفقودة (${missing.length}): ${missing.join(', ')}');
      }

      AppLogger.info('');
    }

    // ملخص نهائي
    final totalLinkedPermissions =
        rolePermissionCounts.values.fold(0, (sum, count) => sum + count);
    final totalMissingPermissions =
        missingPermissions.values.fold(0, (sum, list) => sum + list.length);

    AppLogger.info(
        '📊 ═══════════════════════════════════════════════════════════');
    AppLogger.info('📊 ملخص التقرير:');
    AppLogger.info('📊   إجمالي الروابط المنشأة: $totalLinkedPermissions');
    AppLogger.info('📊   إجمالي الصلاحيات المفقودة: $totalMissingPermissions');

    if (totalMissingPermissions == 0) {
      AppLogger.info('📊   ✅ جميع الأدوار تم ربطها بصلاحياتها بنجاح');
    } else {
      AppLogger.info('📊   ⚠️ يوجد صلاحيات مفقودة تحتاج مراجعة');
    }

    AppLogger.info(
        '📊 ═══════════════════════════════════════════════════════════');
    AppLogger.info('');
  }

  /// الحصول على معرفات الصلاحيات
  static Map<String, String> getPermissionIds() {
    return Map.unmodifiable(_permissionIds);
  }

  /// الحصول على معرفات الأدوار
  static Map<String, String> getRoleIds() {
    return Map.unmodifiable(_roleIds);
  }

  /// تهيئة الصلاحيات والأدوار
  static void initialize() {
    AppLogger.info('🚀 بدء تهيئة الصلاحيات والأدوار...');

    // التحقق من وجود تكرارات في أسماء الصلاحيات
    checkDuplicatePermissionNames();

    // التحقق من صحة رموز الصلاحيات في تعريفات الأدوار
    validateRolePermissionCodes();

    AppLogger.info('✅ تم الانتهاء من تهيئة الصلاحيات والأدوار');
  }

  /// تهيئة الصلاحيات والأدوار في قاعدة البيانات
  static Future<void> initializeDatabase(Transaction txn) async {
    AppLogger.info('🚀 بدء تهيئة الصلاحيات والأدوار في قاعدة البيانات...');

    try {
      final stopwatch = Stopwatch()..start();

      // تهيئة الصلاحيات
      await initializePermissions(txn);

      // تهيئة الأدوار
      await initializeRoles(txn);

      // ربط الصلاحيات بالأدوار
      await linkPermissionsToRoles(txn);

      stopwatch.stop();

      AppLogger.info('');
      AppLogger.info(
          '🎉 ═══════════════════════════════════════════════════════════');
      AppLogger.info('🎉 تم الانتهاء من تهيئة الصلاحيات والأدوار بنجاح');
      AppLogger.info(
          '🎉 ═══════════════════════════════════════════════════════════');
      AppLogger.info('📊 إحصائيات التهيئة:');
      AppLogger.info('   📋 إجمالي الصلاحيات: ${_permissionIds.length}');
      AppLogger.info('   👥 إجمالي الأدوار: ${_roleIds.length}');
      AppLogger.info(
          '   ⏱️ وقت التهيئة: ${stopwatch.elapsedMilliseconds} مللي ثانية');
      AppLogger.info(
          '🎉 ═══════════════════════════════════════════════════════════');
      AppLogger.info('');
    } catch (e, stackTrace) {
      AppLogger.error(
          '❌ فشل في تهيئة الصلاحيات والأدوار في قاعدة البيانات: $e');
      ErrorTracker.captureError(
        'فشل في تهيئة الصلاحيات والأدوار في قاعدة البيانات',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
