import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';

/// شاشة الأسئلة الشائعة حول نظام إدارة الوصول
class AccessFAQScreen extends StatefulWidget {
  static const String routeName = '/access-faq';

  const AccessFAQScreen({Key? key}) : super(key: key);

  @override
  State<AccessFAQScreen> createState() => _AccessFAQScreenState();
}

class _AccessFAQScreenState extends State<AccessFAQScreen> {
  // قائمة الأسئلة الشائعة
  final List<FAQ> _faqs = [
    FAQ(
      question: 'ما هو نظام إدارة الوصول؟',
      answer:
          'نظام إدارة الوصول هو نظام يتيح لك تحديد ما يمكن لكل مستخدم الوصول إليه في التطبيق. '
          'يمكنك إنشاء مستويات وصول مختلفة وتعيينها للمستخدمين حسب وظائفهم.',
    ),
    FAQ(
      question: 'ما هو مستوى الوصول؟',
      answer:
          'مستوى الوصول هو مجموعة من الصلاحيات التي تحدد ما يمكن للمستخدم فعله في التطبيق. '
          'على سبيل المثال، يمكنك إنشاء مستوى وصول "مدير مبيعات" يتيح للمستخدم إدارة المبيعات والعملاء.',
    ),
    FAQ(
      question: 'ما هي أنواع الوصول المتاحة؟',
      answer: 'هناك أربعة أنواع من الوصول لكل وظيفة في النظام:\n\n'
          '1. لا وصول: لا يمكن للمستخدم الوصول إلى هذه الوظيفة نهائياً.\n'
          '2. عرض فقط: يمكن للمستخدم عرض البيانات فقط دون تعديلها.\n'
          '3. تعديل: يمكن للمستخدم عرض وتعديل البيانات دون حذفها.\n'
          '4. وصول كامل: يمكن للمستخدم عرض وتعديل وحذف البيانات.',
    ),
    FAQ(
      question: 'كيف يمكنني إنشاء مستوى وصول جديد؟',
      answer: 'لإنشاء مستوى وصول جديد، اتبع الخطوات التالية:\n\n'
          '1. انتقل إلى شاشة "إدارة الوصول"\n'
          '2. اضغط على زر "إضافة مستوى وصول جديد"\n'
          '3. أدخل اسم مستوى الوصول (مثل "مدير مبيعات")\n'
          '4. حدد مستوى الوصول لكل وظيفة (لا وصول، عرض فقط، تعديل، وصول كامل)\n'
          '5. اضغط على زر "حفظ"',
    ),
    FAQ(
      question: 'كيف يمكنني تعيين مستوى وصول لمستخدم؟',
      answer: 'لتعيين مستوى وصول لمستخدم، اتبع الخطوات التالية:\n\n'
          '1. انتقل إلى شاشة "وصول المستخدمين"\n'
          '2. ابحث عن المستخدم المطلوب\n'
          '3. اختر مستوى الوصول المناسب من القائمة المنسدلة\n'
          '4. سيتم حفظ التغييرات تلقائياً',
    ),
    FAQ(
      question: 'هل يمكنني تعديل مستوى وصول موجود؟',
      answer: 'نعم، يمكنك تعديل مستويات الوصول المخصصة (التي أنشأتها أنت). '
          'لا يمكنك تعديل مستويات الوصول الافتراضية (مثل "مدير النظام"). '
          'لتعديل مستوى وصول، اضغط على زر "تعديل" بجانب مستوى الوصول المطلوب في شاشة "إدارة الوصول".',
    ),
    FAQ(
      question: 'هل يمكنني حذف مستوى وصول؟',
      answer: 'نعم، يمكنك حذف مستويات الوصول المخصصة (التي أنشأتها أنت). '
          'لا يمكنك حذف مستويات الوصول الافتراضية (مثل "مدير النظام"). '
          'لحذف مستوى وصول، اضغط على زر "حذف" بجانب مستوى الوصول المطلوب في شاشة "إدارة الوصول".',
    ),
    FAQ(
      question: 'ماذا يحدث إذا حذفت مستوى وصول مستخدم من قبل مستخدمين؟',
      answer:
          'إذا حذفت مستوى وصول مستخدم من قبل مستخدمين، سيتم تعيين مستوى الوصول الافتراضي "مستخدم عادي" لهؤلاء المستخدمين. '
          'يجب عليك تعيين مستوى وصول جديد لهؤلاء المستخدمين.',
    ),
    FAQ(
      question: 'هل يمكنني إنشاء نسخة من مستوى وصول موجود؟',
      answer:
          'نعم، يمكنك إنشاء نسخة من مستوى وصول موجود عن طريق تعديله ثم حفظه باسم جديد. '
          'هذه طريقة سريعة لإنشاء مستويات وصول متشابهة مع بعض الاختلافات البسيطة.',
    ),
    FAQ(
      question: 'كيف يمكنني معرفة من لديه وصول إلى وظيفة معينة؟',
      answer:
          'يمكنك الانتقال إلى شاشة "وصول المستخدمين" ومراجعة مستويات الوصول المعينة لكل مستخدم. '
          'يمكنك أيضاً استخدام وظيفة البحث للعثور على مستخدمين محددين.',
    ),
    FAQ(
      question: 'هل يمكنني تعطيل مستخدم دون حذفه؟',
      answer:
          'نعم، يمكنك تعطيل حساب مستخدم دون حذفه عن طريق إيقاف تفعيل حسابه في شاشة "وصول المستخدمين". '
          'هذا مفيد للمستخدمين الذين قد يحتاجون إلى الوصول مرة أخرى في المستقبل.',
    ),
    FAQ(
      question: 'ما هي أفضل الممارسات لإدارة الوصول؟',
      answer: 'إليك بعض أفضل الممارسات لإدارة الوصول:\n\n'
          '1. امنح المستخدمين الحد الأدنى من الصلاحيات التي يحتاجونها لأداء وظائفهم.\n'
          '2. قم بإنشاء مستويات وصول محددة لكل وظيفة في مؤسستك.\n'
          '3. راجع مستويات الوصول بشكل دوري للتأكد من أنها لا تزال مناسبة.\n'
          '4. قم بتعطيل حسابات المستخدمين الذين لم يعودوا يعملون في المؤسسة.\n'
          '5. تجنب مشاركة حسابات المستخدمين بين عدة أشخاص.',
    ),
  ];

  // الأسئلة المفتوحة
  final Set<int> _expandedIndices = {};

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأسئلة الشائعة حول إدارة الوصول'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // مقدمة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.12),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.help_outline,
                  size: 48,
                  color: AppColors.lightTextSecondary,
                ),
                const SizedBox(height: 16),
                const Text(
                  'الأسئلة الشائعة حول نظام إدارة الوصول',
                  style: AppTypography(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'هنا ستجد إجابات على الأسئلة الشائعة حول نظام إدارة الوصول في تطبيق تاجر بلس.',
                  style: AppTypography(
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'عدد الأسئلة: ${_faqs.length}',
                  style: const AppTypography(
                    fontSize: 14,
                    color: AppColors.lightTextSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // قائمة الأسئلة
          ...List.generate(_faqs.length, (index) {
            final faq = _faqs[index];
            final isExpanded = _expandedIndices.contains(index);

            return _buildFAQItem(
              faq: faq,
              isExpanded: isExpanded,
              onTap: () {
                setState(() {
                  if (isExpanded) {
                    _expandedIndices.remove(index);
                  } else {
                    _expandedIndices.add(index);
                  }
                });
              },
            );
          }),

          // زر العودة
          Container(
            margin: const EdgeInsets.symmetric(vertical: 24),
            child: ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back),
              label: const Text('العودة إلى إدارة الوصول'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر السؤال الشائع
  Widget _buildFAQItem({
    required FAQ faq,
    required bool isExpanded,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.lightTextSecondary,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.lightTextPrimary.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // السؤال
          InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    isExpanded ? Icons.remove_circle : Icons.add_circle,
                    color: AppColors.lightTextSecondary,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      faq.question,
                      style: const AppTypography(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // الإجابة
          if (isExpanded)
            Container(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Divider(),
                  const SizedBox(height: 8),
                  Text(
                    faq.answer,
                    style: const AppTypography(
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}

/// نموذج السؤال الشائع
class FAQ {
  final String question;
  final String answer;

  FAQ({
    required this.question,
    required this.answer,
  });
}
