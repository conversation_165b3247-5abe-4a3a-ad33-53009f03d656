import 'package:flutter/material.dart';
import '../../../core/models/inventory_transaction.dart';
import '../../../core/models/product.dart';
import '../../../core/models/warehouse.dart';
import '../../../core/models/inventory_transfer.dart';
import '../../../core/models/inventory_transfer_item.dart';
import '../../../core/services/inventory_service.dart';
import '../../../core/services/product_service.dart';
import '../../../core/services/warehouse_service.dart';

import '../../../core/utils/error_tracker.dart';

/// مقدم تحويل المخزون
/// يتعامل مع منطق إدارة تحويلات المخزون
class InventoryTransferPresenter extends ChangeNotifier {
  final InventoryService _inventoryService = InventoryService();
  final ProductService _productService = ProductService();
  final WarehouseService _warehouseService = WarehouseService();

  List<InventoryTransfer> _transfers = [];
  List<Product> _products = [];
  List<Warehouse> _warehouses = [];

  bool _isLoading = false;
  String? _errorMessage;

  /// الحصول على قائمة التحويلات
  List<InventoryTransfer> get transfers => _transfers;

  /// الحصول على قائمة المنتجات
  List<Product> get products => _products;

  /// الحصول على قائمة المستودعات
  List<Warehouse> get warehouses => _warehouses;

  /// التحقق من حالة التحميل
  bool get isLoading => _isLoading;

  /// الحصول على رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// تحميل التحويلات
  Future<void> loadTransfers() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // ملاحظة: هذا تنفيذ مؤقت، سيتم تحسينه في المستقبل
      // لتحميل التحويلات من قاعدة البيانات
      _transfers = [];

      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل التحويلات: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل التحويلات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
    }
  }

  /// تحميل المنتجات
  Future<void> loadProducts() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // الحصول على المنتجات من خدمة المنتجات
      final products = await _productService.getAllProducts();

      _products = products;
      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المنتجات: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل المنتجات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
    }
  }

  /// تحميل المستودعات
  Future<void> loadWarehouses() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // الحصول على المستودعات من خدمة المستودعات
      final warehouses = await _warehouseService.getAllWarehouses();

      _warehouses = warehouses;
      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحميل المستودعات: $e';
      ErrorTracker.captureError(
        'خطأ في تحميل المستودعات',
        error: e,
        stackTrace: stackTrace,
      );
      notifyListeners();
    }
  }

  /// إضافة تحويل جديد
  Future<InventoryTransfer?> addTransfer(
      InventoryTransfer transfer, List<InventoryTransferItem> items,
      {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // التحقق من صحة البيانات
      if (transfer.sourceWarehouseId == transfer.destinationWarehouseId) {
        _isLoading = false;
        _errorMessage = 'لا يمكن التحويل إلى نفس المستودع';
        notifyListeners();
        return null;
      }

      if (items.isEmpty) {
        _isLoading = false;
        _errorMessage = 'يجب إضافة منتج واحد على الأقل';
        notifyListeners();
        return null;
      }

      // إنشاء رقم مرجعي إذا لم يكن موجودًا
      String referenceNumber = transfer.referenceNumber ?? '';
      if (referenceNumber.isEmpty) {
        final now = DateTime.now();
        referenceNumber =
            'TRF-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${_transfers.length + 1}';
      }

      // إنشاء نسخة جديدة من التحويل مع الرقم المرجعي
      final newTransfer = transfer.copyWith(referenceNumber: referenceNumber);

      // ملاحظة: هذا تنفيذ مؤقت، سيتم تحسينه في المستقبل
      // لإضافة التحويل إلى قاعدة البيانات

      // تحديث المخزون
      await _updateInventory(newTransfer, items, userId: userId);

      // تحديث قائمة التحويلات
      await loadTransfers();

      _isLoading = false;
      notifyListeners();

      return newTransfer;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء إضافة التحويل: $e';
      ErrorTracker.captureError(
        'خطأ في إضافة تحويل',
        error: e,
        stackTrace: stackTrace,
        context: {'transfer': transfer.toString()},
      );
      notifyListeners();
      return null;
    }
  }

  /// تحديث تحويل
  Future<bool> updateTransfer(
      InventoryTransfer transfer, List<InventoryTransferItem> items,
      {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // التحقق من صحة البيانات
      if (transfer.sourceWarehouseId == transfer.destinationWarehouseId) {
        _isLoading = false;
        _errorMessage = 'لا يمكن التحويل إلى نفس المستودع';
        notifyListeners();
        return false;
      }

      if (items.isEmpty) {
        _isLoading = false;
        _errorMessage = 'يجب إضافة منتج واحد على الأقل';
        notifyListeners();
        return false;
      }

      // ملاحظة: هذا تنفيذ مؤقت، سيتم تحسينه في المستقبل
      // لتحديث التحويل في قاعدة البيانات

      // تحديث المخزون
      await _updateInventory(transfer, items, userId: userId);

      // تحديث قائمة التحويلات
      await loadTransfers();

      _isLoading = false;
      notifyListeners();

      return true;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء تحديث التحويل: $e';
      ErrorTracker.captureError(
        'خطأ في تحديث تحويل',
        error: e,
        stackTrace: stackTrace,
        context: {'transfer': transfer.toString()},
      );
      notifyListeners();
      return false;
    }
  }

  /// حذف تحويل
  Future<bool> deleteTransfer(
      InventoryTransfer transfer, List<InventoryTransferItem> items,
      {String? userId}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // إلغاء تأثير التحويل على المخزون
      await _reverseInventoryUpdate(transfer, items, userId: userId);

      // ملاحظة: هذا تنفيذ مؤقت، سيتم تحسينه في المستقبل
      // لحذف التحويل من قاعدة البيانات

      // تحديث قائمة التحويلات
      await loadTransfers();

      _isLoading = false;
      notifyListeners();

      return true;
    } catch (e, stackTrace) {
      _isLoading = false;
      _errorMessage = 'حدث خطأ أثناء حذف التحويل: $e';
      ErrorTracker.captureError(
        'خطأ في حذف تحويل',
        error: e,
        stackTrace: stackTrace,
        context: {'transfer': transfer.toString()},
      );
      notifyListeners();
      return false;
    }
  }

  /// تحديث المخزون
  Future<void> _updateInventory(
      InventoryTransfer transfer, List<InventoryTransferItem> items,
      {String? userId}) async {
    try {
      // إجراء تحويل لكل منتج
      for (final item in items) {
        // خفض الكمية من المستودع المصدر
        await _inventoryService.updateProductQuantity(
          item.productId,
          transfer.sourceWarehouseId,
          -item.quantity, // كمية سالبة للخفض
          InventoryTransactionType.transfer,
          referenceId: transfer.id,
          referenceType: 'transfer_out',
          notes: transfer.notes,
          userId: userId,
        );

        // زيادة الكمية في المستودع الوجهة
        await _inventoryService.updateProductQuantity(
          item.productId,
          transfer.destinationWarehouseId,
          item.quantity, // كمية موجبة للزيادة
          InventoryTransactionType.transfer,
          referenceId: transfer.id,
          referenceType: 'transfer_in',
          notes: transfer.notes,
          userId: userId,
        );
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {'transferId': transfer.id},
      );
      rethrow;
    }
  }

  /// إلغاء تأثير التحويل على المخزون
  Future<void> _reverseInventoryUpdate(
      InventoryTransfer transfer, List<InventoryTransferItem> items,
      {String? userId}) async {
    try {
      // إلغاء تأثير التحويل لكل منتج
      for (final item in items) {
        // إعادة الكمية إلى المستودع المصدر
        await _inventoryService.updateProductQuantity(
          item.productId,
          transfer.sourceWarehouseId,
          item.quantity, // كمية موجبة للزيادة
          InventoryTransactionType.adjustment,
          referenceId: transfer.id,
          referenceType: 'transfer_reverse',
          notes: 'إلغاء تحويل: ${transfer.referenceNumber}',
          userId: userId,
        );

        // خفض الكمية من المستودع الوجهة
        await _inventoryService.updateProductQuantity(
          item.productId,
          transfer.destinationWarehouseId,
          -item.quantity, // كمية سالبة للخفض
          InventoryTransactionType.adjustment,
          referenceId: transfer.id,
          referenceType: 'transfer_reverse',
          notes: 'إلغاء تحويل: ${transfer.referenceNumber}',
          userId: userId,
        );
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إلغاء تأثير التحويل على المخزون',
        error: e,
        stackTrace: stackTrace,
        context: {'transferId': transfer.id},
      );
      rethrow;
    }
  }

  /// البحث عن تحويلات المخزون
  ///
  /// يقوم بالبحث في تحويلات المخزون باستخدام نص البحث المدخل
  /// ويبحث في:
  /// - رقم المرجع (referenceNumber)
  /// - الملاحظات (notes)
  ///
  /// ملاحظة: هذه دالة مؤقتة وستتم ترقيتها في المستقبل لتشمل البحث في المزيد من الحقول
  /// وللتفاعل مع قاعدة البيانات بشكل مباشر
  ///
  /// المعاملات:
  /// - query: نص البحث المراد البحث عنه
  ///
  /// يعيد:
  /// - قائمة بتحويلات المخزون التي تطابق معايير البحث
  /// - قائمة فارغة إذا لم يتم العثور على نتائج أو حدث خطأ
  Future<List<InventoryTransfer>> searchTransfers(String query) async {
    try {
      if (query.isEmpty) {
        return _transfers;
      }

      // ملاحظة: هذا تنفيذ مؤقت للبحث، سيتم تحسينه في المستقبل
      // للبحث في قاعدة البيانات مباشرة بدلاً من البحث في الذاكرة
      return _transfers
          .where((transfer) =>
              transfer.referenceNumber?.contains(query) == true ||
              transfer.notes?.contains(query) == true)
          .toList();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'خطأ في البحث عن تحويلات',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على اسم المستودع بواسطة المعرف
  String getWarehouseName(String warehouseId) {
    final warehouse = _warehouses.firstWhere(
      (w) => w.id == warehouseId,
      orElse: () => Warehouse(
        id: warehouseId,
        name: 'مستودع غير معروف',
        code: '',
      ),
    );
    return warehouse.name;
  }

  /// الحصول على اسم المنتج بواسطة المعرف
  String getProductName(String productId) {
    final product = _products.firstWhere(
      (p) => p.id == productId,
      orElse: () => Product(
        id: productId,
        name: 'منتج غير معروف',
        code: '',
      ),
    );
    return product.name;
  }
}
