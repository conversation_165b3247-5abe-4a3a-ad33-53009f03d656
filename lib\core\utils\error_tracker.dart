import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'app_logger.dart';
import '../../features/users/services/activity_log_service.dart';

/// نظام موحد لتتبع الأخطاء والنشاطات في التطبيق
/// يوفر تتبع شامل للأخطاء وتسجيل النشاطات مع إحصائيات مفصلة
class UnifiedErrorAndActivityTracker {
  static final List<ErrorRecord> _errorHistory = [];
  static const int _maxHistorySize = 100;

  // خدمة تسجيل النشاطات
  static final ActivityLogService _activityLogService = ActivityLogService();

  /// تحكم في تفعيل/تعطيل التسجيل (مفعل افتراضياً للتطوير)
  static bool _loggingEnabled = true;

  /// تفعيل أو تعطيل التسجيل
  static void setLoggingEnabled(bool enabled) {
    _loggingEnabled = enabled;
    AppLogger.info(
        'UnifiedTracker logging ${enabled ? 'enabled' : 'disabled'}');
  }

  /// تتبع الخطأ مع معلومات السياق
  static void trackError(
    Object error,
    StackTrace stackTrace,
    String source, {
    Map<String, dynamic>? context,
  }) {
    captureError(
      'Error in $source',
      error: error,
      stackTrace: stackTrace,
      context: context ?? {'source': source},
    );
  }

  /// تسجيل نشاط في النظام (للتوافق مع النظام القديم)
  static Future<void> logActivity({
    required String userId,
    required String userName,
    required String action,
    required String module,
    String? details,
  }) async {
    try {
      await _activityLogService.logActivity(
        userId: userId,
        userName: userName,
        action: action,
        module: module,
        details: details,
      );
    } catch (e, stackTrace) {
      captureError(
        'فشل في تسجيل النشاط',
        error: e,
        stackTrace: stackTrace,
        context: {
          'userId': userId,
          'action': action,
          'module': module,
        },
      );
    }
  }

  /// Captures and logs an error with optional context information
  static void captureError(
    String message, {
    required Object error,
    required StackTrace stackTrace,
    Map<String, dynamic>? context,
  }) {
    final errorRecord = ErrorRecord(
      message: message,
      error: error,
      stackTrace: stackTrace,
      context: context,
      timestamp: DateTime.now(),
    );

    // Add to history, maintaining max size
    _errorHistory.insert(0, errorRecord);
    if (_errorHistory.length > _maxHistorySize) {
      _errorHistory.removeLast();
    }

    // Always log errors to terminal/console for debugging
    _logToTerminal(errorRecord);

    // Log the error using developer.log if logging is enabled
    if (_loggingEnabled) {
      developer.log(
        message,
        error: error,
        stackTrace: stackTrace,
        time: errorRecord.timestamp,
        name: 'TajerPlus',
      );
    }
  }

  /// طباعة الخطأ في الترمنال مع تفاصيل كاملة
  static void _logToTerminal(ErrorRecord errorRecord) {
    final timestamp = errorRecord.timestamp.toIso8601String();
    final separator = '=' * 80;

    // طباعة الخطأ في الترمنال باستخدام debugPrint للتطوير
    debugPrint('\n$separator');
    debugPrint('🚨 ERROR DETECTED - ${errorRecord.error.runtimeType}');
    debugPrint(separator);
    debugPrint('⏰ Time: $timestamp');
    debugPrint('📝 Message: ${errorRecord.message}');
    debugPrint('🔍 Error Type: ${errorRecord.error.runtimeType}');
    debugPrint('💥 Error Details: ${errorRecord.error}');

    if (errorRecord.context != null && errorRecord.context!.isNotEmpty) {
      debugPrint('📋 Context:');
      errorRecord.context!.forEach((key, value) {
        debugPrint('   • $key: $value');
      });
    }

    debugPrint('📍 Stack Trace:');
    final stackLines = errorRecord.stackTrace.toString().split('\n');
    for (int i = 0; i < stackLines.length && i < 10; i++) {
      if (stackLines[i].trim().isNotEmpty) {
        debugPrint('   ${i + 1}. ${stackLines[i].trim()}');
      }
    }

    if (stackLines.length > 10) {
      debugPrint('   ... (${stackLines.length - 10} more lines)');
    }

    debugPrint('$separator\n');

    // أيضاً استخدام AppLogger للتسجيل
    AppLogger.error('❌ ERROR: ${errorRecord.message}');
    AppLogger.error('❌ Details: ${errorRecord.error}');
    if (errorRecord.context != null) {
      AppLogger.error('❌ Context: ${errorRecord.context}');
    }
  }

  /// Get recent errors
  static List<ErrorRecord> getRecentErrors() {
    return List.unmodifiable(_errorHistory);
  }

  /// Clear error history
  static void clearHistory() {
    _errorHistory.clear();
  }

  /// Get error statistics
  static Map<String, dynamic> getErrorStats() {
    if (_errorHistory.isEmpty) {
      return {
        'totalErrors': 0,
        'mostCommonError': null,
        'recentErrorRate': 0.0,
      };
    }

    // Count error types
    final errorCounts = <String, int>{};
    for (final record in _errorHistory) {
      final errorType = record.error.runtimeType.toString();
      errorCounts[errorType] = (errorCounts[errorType] ?? 0) + 1;
    }

    // Find most common error
    var mostCommonError = errorCounts.entries.reduce(
      (a, b) => a.value > b.value ? a : b,
    );

    // Calculate error rate for last hour
    final now = DateTime.now();
    final lastHour = now.subtract(const Duration(hours: 1));
    final recentErrors = _errorHistory
        .where(
          (record) => record.timestamp.isAfter(lastHour),
        )
        .length;

    return {
      'totalErrors': _errorHistory.length,
      'mostCommonError': mostCommonError.key,
      'mostCommonErrorCount': mostCommonError.value,
      'recentErrorRate': recentErrors / 60.0, // Errors per minute
      'lastErrorTimestamp': _errorHistory.first.timestamp.toIso8601String(),
    };
  }

  /// الحصول على سجلات النشاطات (للتوافق مع النظام القديم)
  static Future<List<dynamic>> getActivities({
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? action,
    String? module,
  }) async {
    try {
      return await _activityLogService.getActivities(
        startDate: startDate,
        endDate: endDate,
        userId: userId,
        action: action,
        module: module,
      );
    } catch (e, stackTrace) {
      captureError(
        'فشل في الحصول على سجلات النشاطات',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// تنظيف سجلات النشاطات القديمة
  static Future<bool> cleanupOldActivities(int daysToKeep) async {
    try {
      return await _activityLogService.cleanupOldActivities(daysToKeep);
    } catch (e, stackTrace) {
      captureError(
        'فشل في تنظيف سجلات النشاطات القديمة',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }
}

/// Alias للتوافق مع الكود الموجود
/// يوجه جميع الاستدعاءات إلى النظام الموحد الجديد
class ErrorTracker {
  static void setLoggingEnabled(bool enabled) =>
      UnifiedErrorAndActivityTracker.setLoggingEnabled(enabled);

  static void trackError(
    Object error,
    StackTrace stackTrace,
    String source, {
    Map<String, dynamic>? context,
  }) =>
      UnifiedErrorAndActivityTracker.trackError(
        error,
        stackTrace,
        source,
        context: context,
      );

  static void captureError(
    String message, {
    required Object error,
    required StackTrace stackTrace,
    Map<String, dynamic>? context,
  }) =>
      UnifiedErrorAndActivityTracker.captureError(
        message,
        error: error,
        stackTrace: stackTrace,
        context: context,
      );

  static List<ErrorRecord> getRecentErrors() =>
      UnifiedErrorAndActivityTracker.getRecentErrors();

  static Map<String, dynamic> getErrorStats() =>
      UnifiedErrorAndActivityTracker.getErrorStats();

  static void clearHistory() => UnifiedErrorAndActivityTracker.clearHistory();

  static Future<void> logActivity({
    required String userId,
    required String userName,
    required String action,
    required String module,
    String? details,
  }) =>
      UnifiedErrorAndActivityTracker.logActivity(
        userId: userId,
        userName: userName,
        action: action,
        module: module,
        details: details,
      );

  static Future<List<dynamic>> getActivities({
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? action,
    String? module,
  }) =>
      UnifiedErrorAndActivityTracker.getActivities(
        startDate: startDate,
        endDate: endDate,
        userId: userId,
        action: action,
        module: module,
      );

  static Future<bool> cleanupOldActivities(int daysToKeep) =>
      UnifiedErrorAndActivityTracker.cleanupOldActivities(daysToKeep);
}

class ErrorRecord {
  final String message;
  final Object error;
  final StackTrace stackTrace;
  final Map<String, dynamic>? context;
  final DateTime timestamp;

  const ErrorRecord({
    required this.message,
    required this.error,
    required this.stackTrace,
    this.context,
    required this.timestamp,
  });

  @override
  String toString() {
    return '''
Error: $message
Time: $timestamp
Type: ${error.runtimeType}
Context: $context
Stack trace:
$stackTrace
''';
  }
}
