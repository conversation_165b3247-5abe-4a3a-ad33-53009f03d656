import 'package:flutter/material.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/validators.dart';
import '../../../core/widgets/index.dart';
import '../models/branch.dart';
import '../services/branch_service.dart';
import '../../../core/theme/index.dart';

/// شاشة نموذج الفرع (إضافة/تعديل)
class BranchFormScreen extends StatefulWidget {
  final Branch? branch;

  const BranchFormScreen({Key? key, this.branch}) : super(key: key);

  @override
  State<BranchFormScreen> createState() => _BranchFormScreenState();
}

class _BranchFormScreenState extends State<BranchFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _codeController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();
  bool _isActive = true;
  bool _isMain = false;
  bool _isLoading = false;
  final _branchService = BranchService();

  @override
  void initState() {
    super.initState();
    _initForm();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  /// تهيئة النموذج
  void _initForm() {
    if (widget.branch != null) {
      _nameController.text = widget.branch!.name;
      _codeController.text = widget.branch!.code ?? '';
      _addressController.text = widget.branch!.address ?? '';
      _phoneController.text = widget.branch!.phone ?? '';
      _isActive = widget.branch!.isActive;
      _isMain = widget.branch!.isMain;
    }
  }

  /// حفظ الفرع
  Future<void> _saveBranch() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final branch = widget.branch?.copyWith(
            name: _nameController.text,
            code: _codeController.text.isEmpty ? null : _codeController.text,
            address: _addressController.text.isEmpty
                ? null
                : _addressController.text,
            phone: _phoneController.text.isEmpty ? null : _phoneController.text,
            isActive: _isActive,
            isMain: _isMain,
          ) ??
          Branch(
            id: '',
            name: _nameController.text,
            code: _codeController.text.isEmpty ? null : _codeController.text,
            address: _addressController.text.isEmpty
                ? null
                : _addressController.text,
            phone: _phoneController.text.isEmpty ? null : _phoneController.text,
            isActive: _isActive,
            isMain: _isMain,
          );

      bool success;
      if (widget.branch == null) {
        final branchId = await _branchService.addBranch(branch);
        success = branchId != null;
      } else {
        success = await _branchService.updateBranch(branch);
      }

      if (success && mounted) {
        Navigator.pop(context, true);
      } else {
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('حدث خطأ أثناء حفظ الفرع')),
          );
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في حفظ الفرع: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ أثناء حفظ الفرع')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.branch != null;

    return Scaffold(
      appBar: AkAppBar(
        title: isEditing ? 'تعديل فرع' : 'إضافة فرع جديد',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: AkLoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم الفرع
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم الفرع *',
                        hintText: 'أدخل اسم الفرع',
                        prefixIcon: Icon(Icons.store),
                      ),
                      validator: Validators.required('اسم الفرع'),
                      textInputAction: TextInputAction.next,
                    ),
                    const SizedBox(height: AppDimensions.spacing16),

                    // رمز الفرع
                    TextFormField(
                      controller: _codeController,
                      decoration: const InputDecoration(
                        labelText: 'رمز الفرع',
                        hintText: 'أدخل رمز الفرع',
                        prefixIcon: Icon(Icons.code),
                      ),
                      textInputAction: TextInputAction.next,
                    ),
                    const SizedBox(height: AppDimensions.spacing16),

                    // عنوان الفرع
                    TextFormField(
                      controller: _addressController,
                      decoration: const InputDecoration(
                        labelText: 'عنوان الفرع',
                        hintText: 'أدخل عنوان الفرع',
                        prefixIcon: Icon(Icons.location_on),
                      ),
                      textInputAction: TextInputAction.next,
                    ),
                    const SizedBox(height: AppDimensions.spacing16),

                    // رقم هاتف الفرع
                    TextFormField(
                      controller: _phoneController,
                      decoration: const InputDecoration(
                        labelText: 'رقم هاتف الفرع',
                        hintText: 'أدخل رقم هاتف الفرع',
                        prefixIcon: Icon(Icons.phone),
                      ),
                      keyboardType: TextInputType.phone,
                      textInputAction: TextInputAction.done,
                    ),
                    const SizedBox(height: AppDimensions.spacing24),

                    // حالة الفرع (نشط/غير نشط)
                    SwitchListTile(
                      title: const Text('فرع نشط'),
                      subtitle: const Text('يمكن استخدام الفرع في العمليات'),
                      value: _isActive,
                      onChanged: (value) {
                        setState(() {
                          _isActive = value;
                        });
                      },
                    ),

                    // الفرع الرئيسي
                    SwitchListTile(
                      title: const Text('الفرع الرئيسي'),
                      subtitle: const Text('تعيين هذا الفرع كفرع رئيسي للشركة'),
                      value: _isMain,
                      onChanged: isEditing && widget.branch!.isMain
                          ? null // لا يمكن تغيير حالة الفرع الرئيسي إذا كان بالفعل فرع رئيسي
                          : (value) {
                              setState(() {
                                _isMain = value;
                              });
                            },
                    ),
                    const SizedBox(height: AppDimensions.spacing32),

                    // زر الحفظ
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveBranch,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                        ),
                        child: Text(
                          isEditing ? 'حفظ التغييرات' : 'إضافة الفرع',
                          style: AppTypography.createCustomStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
