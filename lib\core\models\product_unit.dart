import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'base_model.dart';

/// نموذج وحدة المنتج الموحد
/// تم توحيده من جميع نماذج وحدات المنتجات في المشروع
class ProductUnit extends BaseModel {
  // معلومات المنتج
  final String? productId;
  final String? productName;
  final String? productCode;

  // معلومات الوحدة
  final String? unitId;
  final String? unitName;
  final String? unitSymbol;
  final double conversionFactor;

  // معلومات الأسعار
  final double purchasePrice;
  final double salePrice;
  final double? wholesalePrice;

  // معلومات إضافية
  final String? barcode;
  final bool isDefault;
  final bool isActive;

  ProductUnit({
    String? id,
    this.productId,
    this.productName,
    this.productCode,
    this.unitId,
    this.unitName,
    this.unitSymbol,
    this.conversionFactor = 1.0,
    this.purchasePrice = 0.0,
    this.salePrice = 0.0,
    this.wholesalePrice,
    this.barcode,
    this.isDefault = false,
    this.isActive = true,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  /// إنشاء نسخة من هذه الوحدة مع استبدال الحقول المحددة بقيم جديدة
  ProductUnit copyWith({
    String? id,
    String? productId,
    String? productName,
    String? productCode,
    String? unitId,
    String? unitName,
    String? unitSymbol,
    double? conversionFactor,
    double? purchasePrice,
    double? salePrice,
    double? wholesalePrice,
    String? barcode,
    bool? isDefault,
    bool? isActive,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return ProductUnit(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      unitId: unitId ?? this.unitId,
      unitName: unitName ?? this.unitName,
      unitSymbol: unitSymbol ?? this.unitSymbol,
      conversionFactor: conversionFactor ?? this.conversionFactor,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      salePrice: salePrice ?? this.salePrice,
      wholesalePrice: wholesalePrice ?? this.wholesalePrice,
      barcode: barcode ?? this.barcode,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل وحدة المنتج إلى Map
  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'product_name': productName,
      'product_code': productCode,
      'unit_id': unitId,
      'unit_name': unitName,
      'unit_symbol': unitSymbol,
      'conversion_factor': conversionFactor,
      'purchase_price': purchasePrice,
      'sale_price': salePrice,
      'wholesale_price': wholesalePrice,
      'barcode': barcode,
      'is_default': isDefault ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء وحدة منتج من Map
  factory ProductUnit.fromMap(Map<String, dynamic> map) {
    return ProductUnit(
      id: map['id'],
      productId: map['product_id'],
      productName: map['product_name'],
      productCode: map['product_code'],
      unitId: map['unit_id'],
      unitName: map['unit_name'],
      unitSymbol: map['unit_symbol'],
      conversionFactor: map['conversion_factor'] is int
          ? (map['conversion_factor'] as int).toDouble()
          : (map['conversion_factor'] as double? ?? 1.0),
      purchasePrice: map['purchase_price'] is int
          ? (map['purchase_price'] as int).toDouble()
          : (map['purchase_price'] as double? ?? 0.0),
      salePrice: map['sale_price'] is int
          ? (map['sale_price'] as int).toDouble()
          : (map['sale_price'] as double? ?? 0.0),
      wholesalePrice: map['wholesale_price'] is int
          ? (map['wholesale_price'] as int).toDouble()
          : map['wholesale_price'] as double?,
      barcode: map['barcode'],
      isDefault: map['is_default'] == 1,
      isActive: map['is_active'] == null || map['is_active'] == 1,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل وحدة المنتج إلى Map
  Map<String, dynamic> toJson() => toMap();

  /// إنشاء وحدة منتج من JSON
  factory ProductUnit.fromJson(String source) =>
      ProductUnit.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'ProductUnit(id: $id, productId: $productId, unitId: $unitId, unitName: $unitName, conversionFactor: $conversionFactor, salePrice: $salePrice)';
  }
}
