import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/theme/index.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/utils/error_tracker.dart';
import '../presenters/account_presenter.dart';
import '../../../core/widgets/index.dart';

/// شاشة إضافة وتعديل قيود اليومية
class JournalEntryFormScreen extends StatefulWidget {
  final Map<String, dynamic>? journalEntry;

  const JournalEntryFormScreen({Key? key, this.journalEntry}) : super(key: key);

  @override
  State<JournalEntryFormScreen> createState() => _JournalEntryFormScreenState();
}

class _JournalEntryFormScreenState extends State<JournalEntryFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  final TextEditingController _entryNumberController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _referenceController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  int? _selectedBranchId;
  bool _isLoading = true;
  bool _isSaving = false;
  bool _isBalanced = false;

  List<Map<String, dynamic>> _branches = [];
  List<Map<String, dynamic>> _accounts = [];
  List<JournalEntryDetailItem> _entryDetails = [];

  double _totalDebit = 0.0;
  double _totalCredit = 0.0;

  // استخدام التحميل الكسول
  late final AccountPresenter _accountPresenter;

  @override
  void initState() {
    super.initState();
    // تهيئة التحميل الكسول
    _accountPresenter = AppProviders.getLazyPresenter<AccountPresenter>(
        () => AccountPresenter());
    _loadData();

    // تعيين تاريخ اليوم في حقل التاريخ
    _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate);

    // إذا كان هناك قيد موجود، قم بتعبئة النموذج
    if (widget.journalEntry != null) {
      _populateForm();
    } else {
      // إضافة سطرين فارغين افتراضيًا للقيد الجديد
      _entryDetails.add(JournalEntryDetailItem());
      _entryDetails.add(JournalEntryDetailItem());
    }
  }

  void _populateForm() async {
    final entry = widget.journalEntry!;

    _entryNumberController.text = entry['entry_number'] ?? '';
    _descriptionController.text = entry['description'] ?? '';
    _referenceController.text = entry['reference'] ?? '';
    _selectedDate = DateTime.parse(entry['entry_date']);
    _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate);
    _selectedBranchId = entry['branch_id'];

    // تحميل تفاصيل القيد
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> details = await db.rawQuery('''
        SELECT * FROM journal_entry_details
        WHERE journal_entry_id = ? AND is_deleted = 0
        ORDER BY id
      ''', [entry['id']]);

      setState(() {
        _entryDetails = details.map((detail) {
          return JournalEntryDetailItem(
            id: detail['id'],
            accountId: detail['account_id'],
            debit: detail['debit'],
            credit: detail['credit'],
            description: detail['description'],
          );
        }).toList();

        // إضافة سطر فارغ إذا كانت القائمة فارغة
        if (_entryDetails.isEmpty) {
          _entryDetails.add(JournalEntryDetailItem());
        }

        _calculateTotals();
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحميل تفاصيل القيد',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'JournalEntryFormScreen', 'entry_id': entry['id']},
      );
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final db = await _databaseHelper.database;

      // تحميل الحسابات - نتأكد من أن الشاشة لا تزال مثبتة قبل استخدام context
      if (!mounted) return;
      await _accountPresenter.loadAccounts();
      _accounts = _accountPresenter.accounts;

      // تحميل الفروع
      final List<Map<String, dynamic>> branches = await db.query(
        'branches',
        columns: ['id', 'name'],
        orderBy: 'name',
      );

      // تحميل رقم القيد التالي إذا كان قيد جديد
      if (widget.journalEntry == null) {
        final List<Map<String, dynamic>> lastEntry = await db.rawQuery('''
          SELECT entry_number FROM journal_entries
          ORDER BY id DESC LIMIT 1
        ''');

        String nextEntryNumber = 'JE-1';
        if (lastEntry.isNotEmpty && lastEntry[0]['entry_number'] != null) {
          final lastNumber = lastEntry[0]['entry_number'].toString();
          final parts = lastNumber.split('-');
          if (parts.length > 1) {
            final number = int.tryParse(parts[1]);
            if (number != null) {
              nextEntryNumber = 'JE-${number + 1}';
            }
          }
        }

        _entryNumberController.text = nextEntryNumber;
      }

      setState(() {
        _branches = branches;
        _isLoading = false;

        // تعيين الفرع الافتراضي إذا كان متاحًا ولم يتم تحديد فرع
        if (_selectedBranchId == null && _branches.isNotEmpty) {
          _selectedBranchId = _branches[0]['id'];
        }
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحميل البيانات',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'JournalEntryFormScreen'},
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل في تحميل البيانات')),
        );
      }
    }
  }

  void _calculateTotals() {
    double totalDebit = 0.0;
    double totalCredit = 0.0;

    for (var detail in _entryDetails) {
      totalDebit += detail.debit ?? 0.0;
      totalCredit += detail.credit ?? 0.0;
    }

    setState(() {
      _totalDebit = totalDebit;
      _totalCredit = totalCredit;
      _isBalanced = (totalDebit - totalCredit).abs() <
          0.001; // مقارنة مع هامش صغير للتعامل مع أخطاء التقريب
    });
  }

  void _addEntryDetail() {
    setState(() {
      _entryDetails.add(JournalEntryDetailItem());
    });
  }

  void _removeEntryDetail(int index) {
    setState(() {
      _entryDetails.removeAt(index);
      _calculateTotals();
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate);
      });
    }
  }

  Future<void> _saveJournalEntry() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_entryDetails.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب إضافة تفاصيل للقيد')),
      );
      return;
    }

    if (!_isBalanced) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('يجب أن يكون القيد متوازنًا (المدين = الدائن)')),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final db = await _databaseHelper.database;
      await db.transaction((txn) async {
        int journalEntryId;

        // إنشاء أو تحديث القيد الرئيسي
        if (widget.journalEntry == null) {
          // إنشاء قيد جديد
          journalEntryId = await txn.insert('journal_entries', {
            'entry_number': _entryNumberController.text,
            'description': _descriptionController.text,
            'reference': _referenceController.text,
            'entry_date': _selectedDate.toIso8601String(),
            'branch_id': _selectedBranchId,
            'user_id': 1, // يجب استبداله بمعرف المستخدم الحالي
            'created_at': DateTime.now().toIso8601String(),
            'is_deleted': 0,
          });
        } else {
          // تحديث قيد موجود
          journalEntryId = widget.journalEntry!['id'];
          await txn.update(
            'journal_entries',
            {
              'entry_number': _entryNumberController.text,
              'description': _descriptionController.text,
              'reference': _referenceController.text,
              'entry_date': _selectedDate.toIso8601String(),
              'branch_id': _selectedBranchId,
              'updated_at': DateTime.now().toIso8601String(),
            },
            where: 'id = ?',
            whereArgs: [journalEntryId],
          );

          // حذف التفاصيل القديمة (حذف منطقي)
          await txn.update(
            'journal_entry_details',
            {'is_deleted': 1},
            where: 'journal_entry_id = ?',
            whereArgs: [journalEntryId],
          );
        }

        // إضافة تفاصيل القيد
        for (var detail in _entryDetails) {
          if (detail.accountId != null &&
              (detail.debit != null || detail.credit != null)) {
            await txn.insert('journal_entry_details', {
              'journal_entry_id': journalEntryId,
              'account_id': detail.accountId,
              'debit': detail.debit ?? 0.0,
              'credit': detail.credit ?? 0.0,
              'description': detail.description,
              'created_at': DateTime.now().toIso8601String(),
              'is_deleted': 0,
            });
          }
        }
      });

      setState(() {
        _isSaving = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ القيد بنجاح')),
        );
        Navigator.pop(context, true); // العودة مع إشارة إلى نجاح العملية
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حفظ القيد',
        error: e,
        stackTrace: stackTrace,
        context: {'screen': 'JournalEntryFormScreen'},
      );

      setState(() {
        _isSaving = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل في حفظ القيد')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // نستخدم theme في بناء الواجهة لاحقًا
    final theme = Theme.of(context);
    // نحتفظ بهذا المتغير للاستخدام المستقبلي عند تنفيذ واجهة متجاوبة
    // final isTablet = Layout.isTablet();

    return Scaffold(
      appBar: AkAppBar(
        title: widget.journalEntry == null
            ? 'إضافة قيد يومية جديد'
            : 'تعديل قيد يومية',
        showBackButton: true,
      ),
      body: AkLoadingOverlay(
        isLoading: _isLoading || _isSaving,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات القيد الأساسية
                AkCard(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'معلومات القيد',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: AppDimensions.spacing16),
                      // صف رقم القيد والتاريخ
                      Row(
                        children: [
                          // رقم القيد
                          Expanded(
                            child: TextFormField(
                              controller: _entryNumberController,
                              decoration: const InputDecoration(
                                labelText: 'رقم القيد',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال رقم القيد';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          // التاريخ
                          Expanded(
                            child: TextFormField(
                              controller: _dateController,
                              decoration: InputDecoration(
                                labelText: 'التاريخ',
                                border: const OutlineInputBorder(),
                                suffixIcon: IconButton(
                                  icon: const Icon(Icons.calendar_today),
                                  onPressed: () => _selectDate(context),
                                ),
                              ),
                              readOnly: true,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء اختيار التاريخ';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppDimensions.spacing16),
                      // الوصف
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'الوصف',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'الرجاء إدخال وصف للقيد';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: AppDimensions.spacing16),
                      // صف المرجع والفرع
                      Row(
                        children: [
                          // المرجع
                          Expanded(
                            child: TextFormField(
                              controller: _referenceController,
                              decoration: const InputDecoration(
                                labelText: 'المرجع',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          // الفرع
                          Expanded(
                            child: DropdownButtonFormField<int>(
                              value: _selectedBranchId,
                              decoration: const InputDecoration(
                                labelText: 'الفرع',
                                border: OutlineInputBorder(),
                              ),
                              items: _branches.map((branch) {
                                return DropdownMenuItem<int>(
                                  value: branch['id'],
                                  child: Text(branch['name']),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedBranchId = value;
                                });
                              },
                              validator: (value) {
                                if (value == null) {
                                  return 'الرجاء اختيار الفرع';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppDimensions.spacing16),
                // تفاصيل القيد
                AkCard(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تفاصيل القيد',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: AppDimensions.spacing16),
                      // زر إضافة سطر
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.add_circle),
                            onPressed: _addEntryDetail,
                            tooltip: 'إضافة سطر',
                          ),
                        ],
                      ),
                      // عناوين الأعمدة
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Row(
                          children: [
                            const SizedBox(width: 40), // مساحة للزر الحذف
                            Expanded(
                                flex: 3,
                                child: Text('الحساب',
                                    style: theme.textTheme.titleSmall)),
                            Expanded(
                                child: Text('المدين',
                                    style: theme.textTheme.titleSmall)),
                            Expanded(
                                child: Text('الدائن',
                                    style: theme.textTheme.titleSmall)),
                            Expanded(
                                flex: 2,
                                child: Text('الوصف',
                                    style: theme.textTheme.titleSmall)),
                          ],
                        ),
                      ),
                      const Divider(),
                      // قائمة التفاصيل
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _entryDetails.length,
                        itemBuilder: (context, index) {
                          return _buildEntryDetailRow(index);
                        },
                      ),
                      const Divider(),
                      // إجماليات المدين والدائن
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Row(
                          children: [
                            const SizedBox(width: 40),
                            Expanded(
                                flex: 3,
                                child: Text('الإجمالي',
                                    style: theme.textTheme.titleSmall)),
                            Expanded(
                              child: Text(
                                _totalDebit.toStringAsFixed(2),
                                style: theme.textTheme.titleSmall!.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: _isBalanced ? null : AppColors.error,
                                ),
                              ),
                            ),
                            Expanded(
                              child: Text(
                                _totalCredit.toStringAsFixed(2),
                                style: theme.textTheme.titleSmall!.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: _isBalanced ? null : AppColors.error,
                                ),
                              ),
                            ),
                            Expanded(flex: 2, child: Container()),
                          ],
                        ),
                      ),
                      // رسالة التوازن
                      if (!_isBalanced)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            'القيد غير متوازن. الفرق: ${(_totalDebit - _totalCredit).abs().toStringAsFixed(2)}',
                            style: const AppTypography(color: AppColors.error),
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(height: AppDimensions.spacing24),
                // زر الحفظ
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isSaving ? null : _saveJournalEntry,
                    child: Text(
                      _isSaving ? 'جاري الحفظ...' : 'حفظ القيد',
                      style: const AppTypography(fontSize: 18),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEntryDetailRow(int index) {
    final detail = _entryDetails[index];
    // نستخدم theme في المستقبل عند تحسين واجهة المستخدم
    // final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // زر الحذف
          SizedBox(
            width: 40,
            child: IconButton(
              icon: const Icon(Icons.remove_circle, color: AppColors.error),
              onPressed: () => _removeEntryDetail(index),
              tooltip: 'حذف السطر',
            ),
          ),
          // الحساب
          Expanded(
            flex: 3,
            child: DropdownButtonFormField<int>(
              value: detail.accountId,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 10, vertical: 15),
              ),
              items: _accounts.map((account) {
                return DropdownMenuItem<int>(
                  value: account['id'],
                  child: Text('${account['code']} - ${account['name']}'),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  detail.accountId = value;
                });
              },
              isExpanded: true,
            ),
          ),
          const SizedBox(width: 8),
          // المدين
          Expanded(
            child: TextFormField(
              initialValue: detail.debit?.toString() ?? '',
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 10, vertical: 15),
              ),
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              onChanged: (value) {
                setState(() {
                  detail.debit =
                      value.isEmpty ? null : double.tryParse(value) ?? 0.0;
                  if (detail.debit != null && detail.debit! > 0) {
                    detail.credit =
                        null; // إذا كان هناك قيمة مدين، نلغي قيمة الدائن
                  }
                  _calculateTotals();
                });
              },
            ),
          ),
          const SizedBox(width: 8),
          // الدائن
          Expanded(
            child: TextFormField(
              initialValue: detail.credit?.toString() ?? '',
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 10, vertical: 15),
              ),
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              onChanged: (value) {
                setState(() {
                  detail.credit =
                      value.isEmpty ? null : double.tryParse(value) ?? 0.0;
                  if (detail.credit != null && detail.credit! > 0) {
                    detail.debit =
                        null; // إذا كان هناك قيمة دائن، نلغي قيمة المدين
                  }
                  _calculateTotals();
                });
              },
            ),
          ),
          const SizedBox(width: 8),
          // الوصف
          Expanded(
            flex: 2,
            child: TextFormField(
              initialValue: detail.description ?? '',
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                hintText: 'وصف اختياري',
              ),
              onChanged: (value) {
                setState(() {
                  detail.description = value;
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}

class JournalEntryDetailItem {
  int? id;
  int? accountId;
  double? debit;
  double? credit;
  String? description;

  JournalEntryDetailItem({
    this.id,
    this.accountId,
    this.debit,
    this.credit,
    this.description,
  });
}
