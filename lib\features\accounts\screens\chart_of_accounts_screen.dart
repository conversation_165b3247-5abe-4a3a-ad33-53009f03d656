import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import '../../../core/theme/index.dart';

import '../presenters/account_presenter.dart';
import '../../../core/widgets/index.dart';
import '../widgets/account_tree_widget.dart';
import 'simple_account_form_screen.dart';
import '../../../core/providers/app_providers.dart';

/// شاشة مخطط الحسابات مع دعم العلاقات الهرمية
class ChartOfAccountsScreen extends StatefulWidget {
  const ChartOfAccountsScreen({Key? key}) : super(key: key);

  @override
  State<ChartOfAccountsScreen> createState() => _ChartOfAccountsScreenState();
}

class _ChartOfAccountsScreenState extends State<ChartOfAccountsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedAccountType = 'all';
  bool _isExpanded = false;

  // تم إزالة الحساب المحدد لأنه غير مستخدم

  // استخدام التحميل الكسول
  late final AccountPresenter _accountPresenter;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);

    // تهيئة التحميل الكسول
    _accountPresenter = AppProviders.getLazyPresenter<AccountPresenter>(
        () => AccountPresenter());

    // تهيئة مقدم الحسابات وتحميل البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _accountPresenter.loadAccounts();
    });

    // تمكين جميع اتجاهات الشاشة
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();

    // إعادة ضبط اتجاه الشاشة إلى الوضع العمودي فقط عند الخروج من الشاشة
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AkAppBar(
        title: 'مخطط الحسابات',
        actions: [
          // زر الطباعة
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: 'طباعة مخطط الحسابات',
            onPressed: _printChartOfAccounts,
          ),
          // زر توسيع/طي الشجرة
          IconButton(
            icon: Icon(_isExpanded ? Icons.unfold_less : Icons.unfold_more),
            onPressed: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            tooltip: _isExpanded ? 'طي الكل' : 'توسيع الكل',
          ),
        ],
      ),
      body: ListenableBuilder(
        listenable: _accountPresenter,
        builder: (context, child) {
          if (_accountPresenter.isLoading) {
            return const Center(child: AkLoadingIndicator());
          }

          if (_accountPresenter.error != null) {
            return AkErrorState(
              title: 'خطأ في تحميل الحسابات',
              message: 'حدث خطأ أثناء تحميل بيانات الحسابات',
              errorDetails: _accountPresenter.error,
              icon: Icons.error_outline,
              onRetry: () => _accountPresenter.loadAccounts(),
              showDetails: true,
            );
          }

          // تصفية الحسابات حسب البحث ونوع الحساب
          final filteredAccounts = _accountPresenter.accounts.where((account) {
            // تصفية حسب البحث
            bool matchesSearch = true;
            if (_searchQuery.isNotEmpty) {
              final name = (account['name'] as String? ?? '').toLowerCase();
              final code = (account['code'] as String? ?? '').toLowerCase();
              final description =
                  (account['description'] as String? ?? '').toLowerCase();
              final query = _searchQuery.toLowerCase();
              matchesSearch = name.contains(query) ||
                  code.contains(query) ||
                  description.contains(query);
            }

            // تصفية حسب نوع الحساب
            bool matchesType = true;
            if (_selectedAccountType != 'all') {
              matchesType = (account['type'] as String? ??
                      account['account_type'] as String? ??
                      '') ==
                  _selectedAccountType;
            }

            return matchesSearch && matchesType;
          }).toList();

          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    // حقل البحث
                    Expanded(
                      flex: 3,
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'بحث عن حساب...',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          contentPadding:
                              const EdgeInsets.symmetric(vertical: 12.0),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // قائمة منسدلة لتصفية نوع الحساب
                    Expanded(
                      flex: 2,
                      child: DropdownButtonFormField<String>(
                        value: _selectedAccountType,
                        decoration: InputDecoration(
                          labelText: 'نوع الحساب',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          contentPadding:
                              const EdgeInsets.symmetric(horizontal: 16),
                        ),
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'asset', child: Text('أصول')),
                          DropdownMenuItem(
                              value: 'liability', child: Text('خصوم')),
                          DropdownMenuItem(
                              value: 'equity', child: Text('حقوق ملكية')),
                          DropdownMenuItem(
                              value: 'revenue', child: Text('إيرادات')),
                          DropdownMenuItem(
                              value: 'expense', child: Text('مصروفات')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedAccountType = value ?? 'all';
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ),

              // عرض الحسابات في هيكل شجري
              Expanded(
                child: filteredAccounts.isEmpty
                    ? Center(
                        child: Text(
                          _accountPresenter.accounts.isEmpty
                              ? 'لا توجد حسابات للعرض. أضف حساباً جديداً للبدء.'
                              : 'لا توجد حسابات تطابق معايير البحث.',
                          style: theme.textTheme.titleMedium,
                          textAlign: TextAlign.center,
                        ),
                      )
                    : Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: AccountTreeWidget(
                          accounts: filteredAccounts,
                          onSelect: (account) {
                            _showAccountActions(account);
                          },
                        ),
                      ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: AkFloatingButton(
        onPressed: _addNewAccount,
        tooltip: 'إضافة حساب جديد',
        icon: Icons.add,
      ),
    );
  }

  /// عرض قائمة إجراءات الحساب (تعديل، حذف)
  void _showAccountActions(Map<String, dynamic> account) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: Text('${account['name']}'),
                subtitle: Text(
                    '${account['code'] ?? ''} - ${_getAccountTypeArabicName(account['type'] as String? ?? '')}'),
                leading: _getAccountTypeIcon(account['type'] as String? ?? ''),
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.edit, color: AppColors.info),
                title: const Text('تعديل الحساب'),
                onTap: () {
                  Navigator.pop(context);
                  _editAccount(account);
                },
              ),
              ListTile(
                leading: const Icon(Icons.add_circle, color: AppColors.success),
                title: const Text('إضافة حساب فرعي'),
                onTap: () {
                  Navigator.pop(context);
                  _addSubAccount(account);
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: AppColors.error),
                title: const Text('حذف الحساب'),
                onTap: () {
                  Navigator.pop(context);
                  _deleteAccount(account);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// الحصول على أيقونة نوع الحساب
  Widget _getAccountTypeIcon(String accountType) {
    IconData iconData;
    Color iconColor;

    switch (accountType) {
      case 'asset':
        iconData = Icons.account_balance;
        iconColor = AppColors.info;
        break;
      case 'liability':
        iconData = Icons.money_off;
        iconColor = AppColors.error;
        break;
      case 'equity':
        iconData = Icons.pie_chart;
        iconColor = AppColors.accent;
        break;
      case 'revenue':
        iconData = Icons.trending_up;
        iconColor = AppColors.success;
        break;
      case 'expense':
        iconData = Icons.trending_down;
        iconColor = AppColors.warning;
        break;
      default:
        iconData = Icons.folder;
        iconColor = AppColors.lightTextSecondary;
    }

    return Icon(iconData, size: 24, color: iconColor);
  }

  /// فتح شاشة إضافة حساب جديد
  void _addNewAccount() {
    // تخزين مرجع للمقدم قبل الانتقال
    final presenter = _accountPresenter;

    Navigator.of(context)
        .push(
      MaterialPageRoute(
        builder: (context) => const SimpleAccountFormScreen(),
      ),
    )
        .then((_) {
      // إعادة تحميل الحسابات بعد العودة من شاشة إضافة/تعديل الحساب
      if (mounted) {
        presenter.loadAccounts();
      }
    });
  }

  /// فتح شاشة إضافة حساب فرعي
  void _addSubAccount(Map<String, dynamic> parentAccount) {
    // تخزين مرجع للمقدم قبل الانتقال
    final presenter = _accountPresenter;

    Navigator.of(context)
        .push(
      MaterialPageRoute(
        builder: (context) => SimpleAccountFormScreen(
          account: {
            'parent_id': parentAccount['id'],
            'parent_name': parentAccount['name'],
            'type': parentAccount['type'],
            'account_type': 'sub', // حساب فرعي
          },
        ),
      ),
    )
        .then((_) {
      // إعادة تحميل الحسابات بعد العودة من شاشة إضافة/تعديل الحساب
      if (mounted) {
        presenter.loadAccounts();
      }
    });
  }

  /// فتح شاشة تعديل حساب
  void _editAccount(Map<String, dynamic> account) {
    // تخزين مرجع للمقدم قبل الانتقال
    final presenter = _accountPresenter;

    Navigator.of(context)
        .push(
      MaterialPageRoute(
        builder: (context) => SimpleAccountFormScreen(
          account: account,
        ),
      ),
    )
        .then((_) {
      // إعادة تحميل الحسابات بعد العودة من شاشة إضافة/تعديل الحساب
      if (mounted) {
        presenter.loadAccounts();
      }
    });
  }

  /// حذف حساب
  Future<void> _deleteAccount(Map<String, dynamic> account) async {
    final accountName = account['name'] as String;
    final accountId = account['id'] as String;

    // عرض مربع حوار لتأكيد الحذف
    final confirmed = await AkConfirmDialog.show(
      context: context,
      title: 'تأكيد الحذف',
      content:
          'هل أنت متأكد من حذف الحساب "$accountName"؟\nلا يمكن التراجع عن هذه العملية.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
      type: AkDialogType.danger,
      icon: Icons.delete,
    );

    if (confirmed == true) {
      // تنفيذ عملية الحذف
      if (!mounted) return;
      final presenter = _accountPresenter;
      final success = await presenter.deleteAccount(accountId);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم حذف الحساب "$accountName" بنجاح')),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(presenter.error ?? 'فشل في حذف الحساب'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// طباعة مخطط الحسابات
  Future<void> _printChartOfAccounts() async {
    final accounts = _accountPresenter.accounts;

    if (accounts.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا توجد حسابات للطباعة')),
      );
      return;
    }

    // إنشاء مستند PDF
    final pdf = pw.Document();

    // إضافة صفحة للمستند
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return [
            // العنوان
            pw.Header(
              level: 0,
              child: pw.Text('مخطط الحسابات',
                  textAlign: pw.TextAlign.center,
                  style: const pw.TextStyle(fontSize: 24)),
            ),
            pw.SizedBox(height: 20),

            // جدول الحسابات
            pw.TableHelper.fromTextArray(
              headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              headerDecoration: const pw.BoxDecoration(
                color: PdfColors.grey300,
              ),
              headers: <String>[
                'الرمز',
                'الاسم',
                'النوع',
                'الرصيد',
                'الحساب الرئيسي',
              ],
              data: accounts.map((account) {
                return [
                  account['code'] ?? '',
                  account['name'] ?? '',
                  _getAccountTypeArabicName(account['type'] as String? ?? ''),
                  (account['current_balance'] ?? 0.0).toString(),
                  account['parent_name'] ?? '',
                ];
              }).toList(),
            ),
          ];
        },
      ),
    );

    // عرض معاينة الطباعة
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
      name: 'مخطط الحسابات',
    );
  }

  /// الحصول على اسم نوع الحساب بالعربية
  String _getAccountTypeArabicName(String accountType) {
    switch (accountType) {
      case 'asset':
        return 'أصول';
      case 'liability':
        return 'خصوم';
      case 'equity':
        return 'حقوق ملكية';
      case 'revenue':
        return 'إيرادات';
      case 'expense':
        return 'مصروفات';
      default:
        return 'غير محدد';
    }
  }
}
