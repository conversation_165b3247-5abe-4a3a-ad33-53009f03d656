import 'package:flutter/material.dart';
import '../../../core/auth/models/user_role.dart';
import '../models/user.dart';
import '../models/user_group.dart';
import '../../branches/models/branch.dart';
import '../services/user_service.dart';
import '../services/user_group_service.dart';
import '../services/role_service.dart';
import '../../branches/services/branch_service.dart';

/// مقدم خدمة إدارة المستخدمين المحسن
/// يدعم البيانات المرتبطة من الفروع والأدوار ومجموعات المستخدمين
class UserPresenter extends ChangeNotifier {
  final UserService _userService = UserService();
  final UserGroupService _userGroupService = UserGroupService();
  final RoleService _roleService = RoleService();
  final BranchService _branchService = BranchService();

  List<User> _users = [];
  List<UserGroup> _groups = [];
  List<UserRole> _roles = [];
  List<Branch> _branches = [];
  bool _isLoading = false;
  String _searchQuery = '';
  bool _includeInactive = false;
  String? _errorMessage;

  /// الحصول على قائمة المستخدمين
  List<User> get users => _filterUsers();

  /// الحصول على قائمة مجموعات المستخدمين
  List<UserGroup> get groups => _groups;

  /// الحصول على قائمة الأدوار
  List<UserRole> get roles => _roles;

  /// الحصول على قائمة الفروع
  List<Branch> get branches => _branches;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// استعلام البحث
  String get searchQuery => _searchQuery;

  /// تضمين المستخدمين غير النشطين
  bool get includeInactive => _includeInactive;

  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// تحميل المستخدمين
  Future<void> loadUsers() async {
    _setLoading(true);
    try {
      _users =
          await _userService.getAllUsers(includeInactive: _includeInactive);
      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في تحميل المستخدمين: $e';
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل مجموعات المستخدمين
  Future<void> loadGroups() async {
    _setLoading(true);
    try {
      _groups = await _userGroupService.getAllGroups(
          includeInactive: _includeInactive);
      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في تحميل مجموعات المستخدمين: $e';
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل الأدوار
  Future<void> loadRoles() async {
    _setLoading(true);
    try {
      _roles = await _roleService.getAllRoles();
      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في تحميل الأدوار: $e';
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل الفروع
  Future<void> loadBranches() async {
    _setLoading(true);
    try {
      _branches =
          await _branchService.getBranches(includeInactive: _includeInactive);
      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في تحميل الفروع: $e';
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل البيانات (محسن لتشمل الفروع)
  Future<void> loadData() async {
    _setLoading(true);
    try {
      await Future.wait([
        loadUsers(),
        loadGroups(),
        loadRoles(),
        loadBranches(),
      ]);
      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في تحميل البيانات: $e';
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة مستخدم جديد
  Future<bool> addUser(User user) async {
    _setLoading(true);
    try {
      final result = await _userService.addUser(user);
      if (result) {
        await loadUsers();
      }
      return result;
    } catch (e) {
      _errorMessage = 'فشل في إضافة المستخدم: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث مستخدم
  Future<bool> updateUser(User user, {bool updatePassword = false}) async {
    _setLoading(true);
    try {
      final result = await _userService.updateUser(user);
      if (result) {
        await loadUsers();
      }
      return result;
    } catch (e) {
      _errorMessage = 'فشل في تحديث المستخدم: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف مستخدم
  Future<bool> deleteUser(String id) async {
    _setLoading(true);
    try {
      final result = await _userService.deleteUser(id);
      if (result) {
        await loadUsers();
      }
      return result;
    } catch (e) {
      _errorMessage = 'فشل في حذف المستخدم: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تغيير حالة نشاط المستخدم
  Future<bool> toggleUserStatus(String id, bool isActive) async {
    _setLoading(true);
    try {
      final result = await _userService.toggleUserStatus(id, isActive);
      if (result) {
        await loadUsers();
      }
      return result;
    } catch (e) {
      _errorMessage = 'فشل في تغيير حالة المستخدم: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة مجموعة مستخدمين جديدة
  Future<bool> addGroup(UserGroup group) async {
    _setLoading(true);
    try {
      final result = await _userGroupService.addGroup(group);
      if (result) {
        await loadGroups();
      }
      return result;
    } catch (e) {
      _errorMessage = 'فشل في إضافة مجموعة المستخدمين: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث مجموعة مستخدمين
  Future<bool> updateGroup(UserGroup group) async {
    _setLoading(true);
    try {
      final result = await _userGroupService.updateGroup(group);
      if (result) {
        await loadGroups();
      }
      return result;
    } catch (e) {
      _errorMessage = 'فشل في تحديث مجموعة المستخدمين: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف مجموعة مستخدمين
  Future<bool> deleteGroup(String id) async {
    _setLoading(true);
    try {
      final result = await _userGroupService.deleteGroup(id);
      if (result) {
        await loadGroups();
      }
      return result;
    } catch (e) {
      _errorMessage = 'فشل في حذف مجموعة المستخدمين: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تغيير حالة نشاط مجموعة المستخدمين
  Future<bool> toggleGroupStatus(String id, bool isActive) async {
    _setLoading(true);
    try {
      final result = await _userGroupService.toggleGroupStatus(id, isActive);
      if (result) {
        await loadGroups();
      }
      return result;
    } catch (e) {
      _errorMessage = 'فشل في تغيير حالة مجموعة المستخدمين: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  /// تبديل تضمين المستخدمين غير النشطين
  void toggleIncludeInactive(bool value) {
    _includeInactive = value;
    loadData();
  }

  /// تصفية المستخدمين حسب استعلام البحث (محسن للبحث في البيانات المرتبطة)
  List<User> _filterUsers() {
    if (_searchQuery.isEmpty) {
      return _users;
    }

    final query = _searchQuery.toLowerCase();
    return _users.where((user) {
      return user.username.toLowerCase().contains(query) ||
          user.fullName.toLowerCase().contains(query) ||
          (user.email?.toLowerCase().contains(query) ?? false) ||
          (user.phone?.toLowerCase().contains(query) ?? false) ||
          (user.roleName?.toLowerCase().contains(query) ?? false) ||
          (user.userGroupName?.toLowerCase().contains(query) ?? false) ||
          (user.branchName?.toLowerCase().contains(query) ?? false);
    }).toList();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// الحصول على مجموعة المستخدمين بواسطة المعرف
  UserGroup? getGroupById(String id) {
    try {
      return _groups.firstWhere((group) => group.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على الدور بواسطة المعرف
  UserRole? getRoleById(String id) {
    try {
      return _roles.firstWhere((role) => role.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على الفرع بواسطة المعرف
  Branch? getBranchById(String id) {
    try {
      return _branches.firstWhere((branch) => branch.id == id);
    } catch (e) {
      return null;
    }
  }

  /// تعيين مجموعة المستخدم
  Future<bool> setUserGroup(String userId, String userGroupId) async {
    _setLoading(true);
    try {
      final result = await _userService.setUserGroup(userId, userGroupId);
      if (result) {
        await loadUsers();
      }
      return result;
    } catch (e) {
      _errorMessage = 'فشل في تعيين مجموعة المستخدم: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تعيين دور المستخدم
  Future<bool> setUserRole(String userId, String roleId) async {
    _setLoading(true);
    try {
      final result = await _userService.setUserRole(userId, roleId);
      if (result) {
        await loadUsers();
      }
      return result;
    } catch (e) {
      _errorMessage = 'فشل في تعيين دور المستخدم: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }
}
