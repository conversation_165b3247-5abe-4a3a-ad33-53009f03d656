import '../database/database_service.dart';
import '../models/account.dart';
import '../models/account_category.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';

/// خدمة للعمليات المتعلقة بالحسابات
class AccountService {
  // نمط Singleton
  static final AccountService _instance = AccountService._internal();
  factory AccountService() => _instance;
  AccountService._internal();

  final DatabaseService _db = DatabaseService.instance;

  /// الحصول على جميع الحسابات
  Future<List<Account>> getAllAccounts({
    bool includeInactive = false,
    String? searchQuery,
    AccountType? type,
    String? category,
    String? parentId,
  }) async {
    try {
      AppLogger.info('الحصول على جميع الحسابات');

      // بناء شرط WHERE
      String whereClause = 'a.is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (!includeInactive) {
        whereClause += ' AND a.is_active = 1';
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause += ' AND (a.name LIKE ? OR a.code LIKE ?)';
        whereArgs.addAll(['%$searchQuery%', '%$searchQuery%']);
      }

      if (type != null) {
        whereClause += ' AND a.type = ?';
        whereArgs.add(type.toString().split('.').last);
      }

      if (category != null) {
        whereClause += ' AND a.category = ?';
        whereArgs.add(category);
      }

      if (parentId != null) {
        whereClause += ' AND a.parent_id = ?';
        whereArgs.add(parentId);
      }

      // استعلام قاعدة البيانات مع JOIN للحصول على معلومات الحساب الأب
      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          a.*,
          p.name as parent_name,
          p.code as parent_code
        FROM ${DatabaseService.tableAccounts} a
        LEFT JOIN ${DatabaseService.tableAccounts} p ON a.parent_id = p.id
        WHERE $whereClause
        ORDER BY a.code ASC
      ''', whereArgs);

      // تحويل إلى كائنات Account
      return List.generate(maps.length, (i) {
        final map = maps[i];

        // إضافة معلومات الحساب الأب إلى الخريطة
        if (map['parent_id'] != null && map['parent_name'] != null) {
          map['parent'] = {
            'id': map['parent_id'],
            'name': map['parent_name'],
            'code': map['parent_code'],
          };
        }

        return Account.fromMap(map);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع الحسابات',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على حساب بواسطة المعرف
  Future<Account?> getAccountById(String id) async {
    try {
      AppLogger.info('الحصول على حساب بواسطة المعرف: $id');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          a.*,
          p.name as parent_name,
          p.code as parent_code
        FROM ${DatabaseService.tableAccounts} a
        LEFT JOIN ${DatabaseService.tableAccounts} p ON a.parent_id = p.id
        WHERE a.id = ? AND a.is_deleted = 0
      ''', [id]);

      if (maps.isEmpty) {
        return null;
      }

      final map = maps.first;

      // إضافة معلومات الحساب الأب إلى الخريطة
      if (map['parent_id'] != null && map['parent_name'] != null) {
        map['parent'] = {
          'id': map['parent_id'],
          'name': map['parent_name'],
          'code': map['parent_code'],
        };
      }

      return Account.fromMap(map);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على حساب بواسطة المعرف',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return null;
    }
  }

  /// الحصول على حساب بواسطة الكود
  Future<Account?> getAccountByCode(String code) async {
    try {
      AppLogger.info('الحصول على حساب بواسطة الكود: $code');

      final List<Map<String, dynamic>> maps = await _db.rawQuery('''
        SELECT
          a.*,
          p.name as parent_name,
          p.code as parent_code
        FROM ${DatabaseService.tableAccounts} a
        LEFT JOIN ${DatabaseService.tableAccounts} p ON a.parent_id = p.id
        WHERE a.code = ? AND a.is_deleted = 0
      ''', [code]);

      if (maps.isEmpty) {
        return null;
      }

      final map = maps.first;

      // إضافة معلومات الحساب الأب إلى الخريطة
      if (map['parent_id'] != null && map['parent_name'] != null) {
        map['parent'] = {
          'id': map['parent_id'],
          'name': map['parent_name'],
          'code': map['parent_code'],
        };
      }

      return Account.fromMap(map);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على حساب بواسطة الكود',
        error: e,
        stackTrace: stackTrace,
        context: {'code': code},
      );
      return null;
    }
  }

  /// إضافة حساب جديد
  Future<bool> addAccount(Account account, {String? userId}) async {
    try {
      AppLogger.info('إضافة حساب جديد: ${account.name}');

      // التحقق من عدم وجود حساب بنفس الكود
      final existingAccount = await getAccountByCode(account.code);
      if (existingAccount != null) {
        AppLogger.warning('كود الحساب موجود بالفعل: ${account.code}');
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        final accountMap = account.toMap();

        // تعيين created_by إذا تم توفيره
        if (userId != null) {
          accountMap['created_by'] = userId;
        }

        // إذا كان هذا الحساب له حساب أب، نقوم بتحديث الحساب الأب ليكون حساب أب
        if (account.parentId != null) {
          await txn.update(
            DatabaseService.tableAccounts,
            {'is_parent': 1, 'updated_at': DateTime.now().toIso8601String()},
            where: 'id = ?',
            whereArgs: [account.parentId],
          );
        }

        // إضافة الحساب
        await txn.insert(DatabaseService.tableAccounts, accountMap);

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة حساب',
        error: e,
        stackTrace: stackTrace,
        context: {'account': account.toString()},
      );
      return false;
    }
  }

  /// تحديث حساب موجود
  Future<bool> updateAccount(Account account, {String? userId}) async {
    try {
      AppLogger.info('تحديث حساب: ${account.name}');

      // التحقق من عدم وجود حساب آخر بنفس الكود
      final existingAccount = await getAccountByCode(account.code);
      if (existingAccount != null && existingAccount.id != account.id) {
        AppLogger.warning('كود الحساب موجود بالفعل: ${account.code}');
        return false;
      }

      // الحصول على الحساب الحالي
      final currentAccount = await getAccountById(account.id);
      if (currentAccount == null) {
        AppLogger.warning('الحساب غير موجود: ${account.id}');
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        final accountMap = account.toMap();

        // تعيين updated_at و updated_by
        accountMap['updated_at'] = DateTime.now().toIso8601String();
        if (userId != null) {
          accountMap['updated_by'] = userId;
        }

        // إذا تم تغيير الحساب الأب
        if (account.parentId != currentAccount.parentId) {
          // إذا كان هناك حساب أب جديد، نقوم بتحديثه ليكون حساب أب
          if (account.parentId != null) {
            await txn.update(
              DatabaseService.tableAccounts,
              {'is_parent': 1, 'updated_at': DateTime.now().toIso8601String()},
              where: 'id = ?',
              whereArgs: [account.parentId],
            );
          }

          // إذا كان هناك حساب أب قديم، نتحقق مما إذا كان لا يزال لديه حسابات فرعية
          if (currentAccount.parentId != null) {
            final childrenCount =
                await _getChildAccountsCount(txn, currentAccount.parentId!);
            if (childrenCount <= 1) {
              // 1 لأن الحساب الحالي لا يزال مرتبطًا بالحساب الأب
              await txn.update(
                DatabaseService.tableAccounts,
                {
                  'is_parent': 0,
                  'updated_at': DateTime.now().toIso8601String()
                },
                where: 'id = ?',
                whereArgs: [currentAccount.parentId],
              );
            }
          }
        }

        // تحديث الحساب
        await txn.update(
          DatabaseService.tableAccounts,
          accountMap,
          where: 'id = ?',
          whereArgs: [account.id],
        );

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث حساب',
        error: e,
        stackTrace: stackTrace,
        context: {'account': account.toString()},
      );
      return false;
    }
  }

  /// حذف حساب (حذف منطقي)
  Future<bool> deleteAccount(String id, {String? userId}) async {
    try {
      AppLogger.info('حذف حساب: $id');

      // التحقق من عدم وجود حسابات فرعية
      final childrenCount = await _getChildAccountsCount(null, id);
      if (childrenCount > 0) {
        AppLogger.warning('لا يمكن حذف الحساب لأنه يحتوي على حسابات فرعية');
        return false;
      }

      // التحقق من عدم وجود حركات مرتبطة بالحساب
      final transactionsCount = await _getAccountTransactionsCount(id);
      if (transactionsCount > 0) {
        AppLogger.warning('لا يمكن حذف الحساب لأنه يحتوي على حركات');
        return false;
      }

      // الحصول على الحساب
      final account = await getAccountById(id);
      if (account == null) {
        return false;
      }

      // بدء معاملة قاعدة البيانات
      return await _db.transaction<bool>((txn) async {
        final now = DateTime.now().toIso8601String();

        // حذف الحساب (حذف منطقي)
        await txn.update(
          DatabaseService.tableAccounts,
          {
            'is_deleted': 1,
            'is_active': 0,
            'updated_at': now,
            'updated_by': userId,
          },
          where: 'id = ?',
          whereArgs: [id],
        );

        // إذا كان الحساب له حساب أب، نتحقق مما إذا كان لا يزال لديه حسابات فرعية
        if (account.parentId != null) {
          final childrenCount =
              await _getChildAccountsCount(txn, account.parentId!);
          if (childrenCount <= 1) {
            // 1 لأن الحساب الحالي لا يزال مرتبطًا بالحساب الأب
            await txn.update(
              DatabaseService.tableAccounts,
              {'is_parent': 0, 'updated_at': now},
              where: 'id = ?',
              whereArgs: [account.parentId],
            );
          }
        }

        return true;
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف حساب',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// الحصول على جميع فئات الحسابات
  Future<List<AccountCategory>> getAllAccountCategories({
    bool includeInactive = false,
    AccountType? type,
  }) async {
    try {
      AppLogger.info('الحصول على جميع فئات الحسابات');

      // بناء شرط WHERE
      String whereClause = 'is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (!includeInactive) {
        whereClause += ' AND is_active = 1';
      }

      if (type != null) {
        whereClause += ' AND account_type = ?';
        whereArgs.add(type.toString().split('.').last);
      }

      // استعلام قاعدة البيانات
      final List<Map<String, dynamic>> maps = await _db.query(
        DatabaseService.tableCategories,
        where: '$whereClause AND type = ?',
        whereArgs: [...whereArgs, 'account'],
        orderBy: 'name ASC',
      );

      // تحويل إلى كائنات AccountCategory
      return List.generate(maps.length, (i) {
        return AccountCategory.fromMap(maps[i]);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على جميع فئات الحسابات',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// إضافة فئة حساب جديدة
  Future<bool> addAccountCategory(AccountCategory category,
      {String? userId}) async {
    try {
      AppLogger.info('إضافة فئة حساب جديدة: ${category.name}');

      // التحقق من عدم وجود فئة بنفس الاسم ونفس النوع
      final existingCategory = await _getAccountCategoryByNameAndType(
        category.name,
        category.accountType,
      );

      if (existingCategory != null) {
        AppLogger.warning('فئة الحساب موجودة بالفعل: ${category.name}');
        return false;
      }

      final categoryMap = category.toMap();

      // تعيين created_by إذا تم توفيره
      if (userId != null) {
        categoryMap['created_by'] = userId;
      }

      await _db.insert(DatabaseService.tableCategories, categoryMap);

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إضافة فئة حساب',
        error: e,
        stackTrace: stackTrace,
        context: {'category': category.toString()},
      );
      return false;
    }
  }

  /// تحديث فئة حساب موجودة
  Future<bool> updateAccountCategory(AccountCategory category,
      {String? userId}) async {
    try {
      AppLogger.info('تحديث فئة حساب: ${category.name}');

      // التحقق من عدم وجود فئة أخرى بنفس الاسم ونفس النوع
      final existingCategory = await _getAccountCategoryByNameAndType(
        category.name,
        category.accountType,
      );

      if (existingCategory != null && existingCategory.id != category.id) {
        AppLogger.warning('فئة الحساب موجودة بالفعل: ${category.name}');
        return false;
      }

      final categoryMap = category.toMap();

      // تعيين updated_at و updated_by
      categoryMap['updated_at'] = DateTime.now().toIso8601String();
      if (userId != null) {
        categoryMap['updated_by'] = userId;
      }

      await _db.update(
        DatabaseService.tableCategories,
        categoryMap,
        where: 'id = ? AND type = ?',
        whereArgs: [category.id, 'account'],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث فئة حساب',
        error: e,
        stackTrace: stackTrace,
        context: {'category': category.toString()},
      );
      return false;
    }
  }

  /// حذف فئة حساب (حذف منطقي)
  Future<bool> deleteAccountCategory(String id, {String? userId}) async {
    try {
      AppLogger.info('حذف فئة حساب: $id');

      // التحقق من عدم وجود حسابات مرتبطة بالفئة
      final accountsCount = await _getAccountsCountByCategory(id);
      if (accountsCount > 0) {
        AppLogger.warning('لا يمكن حذف الفئة لأنها مرتبطة بحسابات');
        return false;
      }

      final now = DateTime.now().toIso8601String();

      await _db.update(
        DatabaseService.tableCategories,
        {
          'is_deleted': 1,
          'is_active': 0,
          'updated_at': now,
          'updated_by': userId,
        },
        where: 'id = ? AND type = ?',
        whereArgs: [id, 'account'],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف فئة حساب',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// الحصول على عدد الحسابات الفرعية لحساب معين
  Future<int> _getChildAccountsCount(dynamic db, String parentId) async {
    try {
      final result = db != null
          ? await db.rawQuery(
              'SELECT COUNT(*) as count FROM ${DatabaseService.tableAccounts} WHERE parent_id = ? AND is_deleted = 0',
              [parentId],
            )
          : await _db.rawQuery(
              'SELECT COUNT(*) as count FROM ${DatabaseService.tableAccounts} WHERE parent_id = ? AND is_deleted = 0',
              [parentId],
            );

      return result.first['count'] as int;
    } catch (e) {
      AppLogger.error('فشل في الحصول على عدد الحسابات الفرعية: $e');
      return 0;
    }
  }

  /// الحصول على عدد الحركات المرتبطة بحساب معين
  Future<int> _getAccountTransactionsCount(String accountId) async {
    try {
      final result = await _db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseService.tableJournalEntryDetails} WHERE account_id = ? AND is_deleted = 0',
        [accountId],
      );

      return result.first['count'] as int;
    } catch (e) {
      AppLogger.error('فشل في الحصول على عدد الحركات المرتبطة بالحساب: $e');
      return 0;
    }
  }

  /// الحصول على فئة حساب بواسطة الاسم والنوع
  Future<AccountCategory?> _getAccountCategoryByNameAndType(
    String name,
    AccountType type,
  ) async {
    try {
      final List<Map<String, dynamic>> maps = await _db.query(
        DatabaseService.tableCategories,
        where: 'name = ? AND type = ? AND is_deleted = 0',
        whereArgs: [name, 'account'],
      );

      if (maps.isEmpty) {
        return null;
      }

      return AccountCategory.fromMap(maps.first);
    } catch (e) {
      AppLogger.error('فشل في الحصول على فئة الحساب بواسطة الاسم والنوع: $e');
      return null;
    }
  }

  /// الحصول على عدد الحسابات المرتبطة بفئة معينة
  Future<int> _getAccountsCountByCategory(String categoryId) async {
    try {
      final result = await _db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseService.tableAccounts} WHERE category = ? AND is_deleted = 0',
        [categoryId],
      );

      return result.first['count'] as int;
    } catch (e) {
      AppLogger.error('فشل في الحصول على عدد الحسابات المرتبطة بالفئة: $e');
      return 0;
    }
  }
}
