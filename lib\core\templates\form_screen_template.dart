import 'package:flutter/material.dart';
import '../widgets/index.dart';
import '../utils/index.dart';

/// نموذج شاشة النماذج المستخدمة في التطبيق
/// يوفر هيكل موحد لجميع شاشات إضافة/تعديل البيانات
class FormScreenTemplate extends StatelessWidget {
  /// عنوان الشاشة
  final String title;

  /// مفتاح النموذج للتحقق من صحة المدخلات
  final GlobalKey<FormState> formKey;

  /// أقسام النموذج (كل قسم يمثل مجموعة من الحقول)
  final List<Widget> formSections;

  /// شريط الأزرار في أسفل الشاشة
  final Widget bottomBar;

  /// هل الشاشة للتعديل أم للإضافة
  final bool isEditing;

  /// المسافة بين أقسام النموذج
  final double sectionSpacing;

  const FormScreenTemplate({
    Key? key,
    required this.title,
    required this.formKey,
    required this.formSections,
    required this.bottomBar,
    this.isEditing = false,
    this.sectionSpacing = 24,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: isEditing ? 'تعديل $title' : 'إضافة $title',
        showBackButton: true,
      ),
      body: Form(
        key: formKey,
        child: Column(
          children: [
            Expanded(
              child: ListView.separated(
                padding: const EdgeInsets.all(Layout.defaultSpacing),
                itemCount: formSections.length,
                separatorBuilder: (context, index) =>
                    SizedBox(height: sectionSpacing),
                itemBuilder: (context, index) => formSections[index],
              ),
            ),
            bottomBar,
          ],
        ),
      ),
    );
  }
}

/// نموذج شاشة القوائم المستخدمة في التطبيق
/// يوفر هيكل موحد لجميع شاشات عرض القوائم
class ListScreenTemplate extends StatelessWidget {
  /// عنوان الشاشة
  final String title;

  /// محتوى الشاشة
  final Widget body;

  /// الإجراءات في شريط التطبيق
  final List<Widget>? actions;

  /// زر العائم (إن وجد)
  final Widget? floatingActionButton;

  /// هل يتم عرض زر الرجوع
  final bool showBackButton;

  /// دالة يتم تنفيذها عند الضغط على زر القائمة
  final VoidCallback? onMenuPressed;

  const ListScreenTemplate({
    Key? key,
    required this.title,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.showBackButton = true,
    this.onMenuPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: title,
        showBackButton: showBackButton,
        onMenuPressed: onMenuPressed,
        actions: actions,
      ),
      body: body,
      floatingActionButton: floatingActionButton,
    );
  }
}

/// نموذج شاشة التفاصيل المستخدمة في التطبيق
/// يوفر هيكل موحد لجميع شاشات عرض تفاصيل العناصر
class DetailScreenTemplate extends StatelessWidget {
  /// عنوان الشاشة
  final String title;

  /// أقسام التفاصيل
  final List<Widget> sections;

  /// الإجراءات في شريط التطبيق
  final List<Widget>? actions;

  /// شريط الأزرار في أسفل الشاشة (إن وجد)
  final Widget? bottomBar;

  /// المسافة بين الأقسام
  final double sectionSpacing;

  const DetailScreenTemplate({
    Key? key,
    required this.title,
    required this.sections,
    this.actions,
    this.bottomBar,
    this.sectionSpacing = 16,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: title,
        showBackButton: true,
        actions: actions,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.all(Layout.defaultSpacing),
              itemCount: sections.length,
              separatorBuilder: (context, index) =>
                  SizedBox(height: sectionSpacing),
              itemBuilder: (context, index) => sections[index],
            ),
          ),
          if (bottomBar != null) bottomBar!,
        ],
      ),
    );
  }
}
