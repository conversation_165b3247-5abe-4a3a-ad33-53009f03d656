import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:math' as math;
import '../../../core/theme/index.dart';

/// رسم بياني متحرك
class AnimatedBarChart extends StatefulWidget {
  /// بيانات الرسم البياني
  final List<double> data;

  /// عناوين المحور الأفقي
  final List<String> xLabels;

  /// عنوان الرسم البياني
  final String? title;

  /// وصف الرسم البياني
  final String? description;

  /// لون الرسم البياني
  final Color color;

  /// ارتفاع الرسم البياني
  final double height;

  /// مدة الحركة
  final Duration duration;

  /// عرض الشريط
  final double barWidth;

  /// نصف قطر الحواف العلوية للشريط
  final double topRadius;

  /// ما إذا كان يعرض القيم فوق الأشرطة
  final bool showValuesOnTop;

  /// دالة تنسيق القيم
  final String Function(double)? valueFormatter;

  const AnimatedBarChart({
    Key? key,
    required this.data,
    required this.xLabels,
    this.title,
    this.description,
    this.color = AppColors.info,
    this.height = 300,
    this.duration = const Duration(milliseconds: 1500),
    this.barWidth = 22,
    this.topRadius = 6,
    this.showValuesOnTop = true,
    this.valueFormatter,
  })  : assert(data.length == xLabels.length,
            'Data and labels must have the same length'),
        super(key: key);

  @override
  State<AnimatedBarChart> createState() => _AnimatedBarChartState();
}

class _AnimatedBarChartState extends State<AnimatedBarChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _initializeAnimations();
    _controller.forward();
  }

  void _initializeAnimations() {
    _animations = List.generate(
      widget.data.length,
      (index) => Tween<double>(begin: 0, end: widget.data[index]).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(
            index * (0.5 / widget.data.length),
            0.5 + index * (0.5 / widget.data.length),
            curve: Curves.easeOutCubic,
          ),
        ),
      ),
    );
  }

  @override
  void didUpdateWidget(AnimatedBarChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data) {
      _animations = List.generate(
        widget.data.length,
        (index) {
          final oldValue =
              index < oldWidget.data.length ? oldWidget.data[index] : 0;
          return Tween<double>(
                  begin: oldValue.toDouble(), end: widget.data[index])
              .animate(
            CurvedAnimation(
              parent: _controller,
              curve: Interval(
                index * (0.5 / widget.data.length),
                0.5 + index * (0.5 / widget.data.length),
                curve: Curves.easeOutCubic,
              ),
            ),
          );
        },
      );
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _formatValue(double value) {
    if (widget.valueFormatter != null) {
      return widget.valueFormatter!(value);
    }
    return value.toStringAsFixed(1);
  }

  double _getMaxY() {
    if (widget.data.isEmpty) return 10;
    final maxValue = widget.data.reduce(math.max);
    return maxValue * 1.2; // إضافة هامش 20%
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.title != null || widget.description != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (widget.title != null)
                      Text(
                        widget.title!,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                    if (widget.description != null) ...[
                      const SizedBox(height: AppDimensions.spacing4),
                      Text(
                        widget.description!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.lightTextSecondary,
                            ),
                      ),
                    ],
                  ],
                ),
              ),
            SizedBox(
              height: widget.height,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: _getMaxY(),
                  barTouchData: BarTouchData(
                    enabled: true,
                    touchTooltipData: BarTouchTooltipData(
                      getTooltipItem: (group, groupIndex, rod, rodIndex) {
                        return BarTooltipItem(
                          '${widget.xLabels[groupIndex]}\n',
                          const AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                          children: <TextSpan>[
                            TextSpan(
                              text: _formatValue(_animations[groupIndex].value),
                              style: const AppTypography(
                                color: AppColors.lightTextSecondary,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index < 0 || index >= widget.xLabels.length) {
                            return const SizedBox.shrink();
                          }
                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              widget.xLabels[index],
                              style: const AppTypography(
                                color: AppColors.lightTextSecondary,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          );
                        },
                        reservedSize: 38,
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            value.toInt().toString(),
                            style: const AppTypography(
                              color: AppColors.lightTextSecondary,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          );
                        },
                        reservedSize: 40,
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: false,
                  ),
                  barGroups: List.generate(
                    widget.data.length,
                    (index) => BarChartGroupData(
                      x: index,
                      barRods: [
                        BarChartRodData(
                          toY: _animations[index].value,
                          color: widget.color,
                          width: widget.barWidth,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(widget.topRadius),
                            topRight: Radius.circular(widget.topRadius),
                          ),
                        ),
                      ],
                      showingTooltipIndicators:
                          widget.showValuesOnTop ? [0] : [],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

/// رسم بياني خطي متحرك
class AnimatedLineChart extends StatefulWidget {
  /// بيانات الرسم البياني
  final List<double> data;

  /// عناوين المحور الأفقي
  final List<String> xLabels;

  /// عنوان الرسم البياني
  final String? title;

  /// وصف الرسم البياني
  final String? description;

  /// لون الرسم البياني
  final Color color;

  /// ارتفاع الرسم البياني
  final double height;

  /// مدة الحركة
  final Duration duration;

  /// سمك الخط
  final double lineWidth;

  /// ما إذا كان يعرض المنطقة تحت الخط
  final bool showArea;

  /// ما إذا كان يعرض النقاط
  final bool showDots;

  /// دالة تنسيق القيم
  final String Function(double)? valueFormatter;

  const AnimatedLineChart({
    Key? key,
    required this.data,
    required this.xLabels,
    this.title,
    this.description,
    this.color = AppColors.info,
    this.height = 300,
    this.duration = const Duration(milliseconds: 1500),
    this.lineWidth = 3,
    this.showArea = true,
    this.showDots = true,
    this.valueFormatter,
  })  : assert(data.length == xLabels.length,
            'Data and labels must have the same length'),
        super(key: key);

  @override
  State<AnimatedLineChart> createState() => _AnimatedLineChartState();
}

class _AnimatedLineChartState extends State<AnimatedLineChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic),
    );
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedLineChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data) {
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _formatValue(double value) {
    if (widget.valueFormatter != null) {
      return widget.valueFormatter!(value);
    }
    return value.toStringAsFixed(1);
  }

  double _getMaxY() {
    if (widget.data.isEmpty) return 10;
    final maxValue = widget.data.reduce(math.max);
    return maxValue * 1.2; // إضافة هامش 20%
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.title != null || widget.description != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (widget.title != null)
                      Text(
                        widget.title!,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                    if (widget.description != null) ...[
                      const SizedBox(height: AppDimensions.spacing4),
                      Text(
                        widget.description!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.lightTextSecondary,
                            ),
                      ),
                    ],
                  ],
                ),
              ),
            SizedBox(
              height: widget.height,
              child: LineChart(
                LineChartData(
                  lineTouchData: LineTouchData(
                    touchTooltipData: LineTouchTooltipData(
                      getTooltipItems: (touchedSpots) {
                        return touchedSpots.map((spot) {
                          return LineTooltipItem(
                            '${widget.xLabels[spot.x.toInt()]}\n',
                            const AppTypography(
                              color: AppColors.lightTextSecondary,
                              fontWeight: FontWeight.bold,
                            ),
                            children: [
                              TextSpan(
                                text: _formatValue(spot.y),
                                style: const AppTypography(
                                  color: AppColors.lightTextSecondary,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          );
                        }).toList();
                      },
                    ),
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index < 0 || index >= widget.xLabels.length) {
                            return const SizedBox.shrink();
                          }
                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              widget.xLabels[index],
                              style: const AppTypography(
                                color: AppColors.lightTextSecondary,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          );
                        },
                        reservedSize: 38,
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            value.toInt().toString(),
                            style: const AppTypography(
                              color: AppColors.lightTextSecondary,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          );
                        },
                        reservedSize: 40,
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: false,
                  ),
                  maxY: _getMaxY(),
                  minY: 0,
                  lineBarsData: [
                    LineChartBarData(
                      spots: List.generate(
                        widget.data.length,
                        (index) => FlSpot(
                          index.toDouble(),
                          widget.data[index] * _animation.value,
                        ),
                      ),
                      isCurved: true,
                      color: widget.color,
                      barWidth: widget.lineWidth,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: widget.showDots,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: widget.color,
                            strokeWidth: 2,
                            strokeColor: AppColors.onPrimary,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: widget.showArea,
                        color: widget.color.withValues(alpha: 0.2),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
