import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/models/inventory_adjustment.dart';
import '../../../core/models/inventory_transfer.dart';
import '../../../core/models/warehouse.dart';

import '../../../core/widgets/safe_layout.dart';
import '../presenters/inventory_adjustment_presenter.dart';
import '../presenters/inventory_transfer_presenter.dart';
import '../presenters/warehouse_presenter.dart';
import 'inventory_adjustment_form_screen.dart';
import 'inventory_transfer_form_screen.dart';
import 'inventory_operation_details_screen.dart';
import '../../../core/theme/index.dart';

/// شاشة إدارة المخزون الموحدة
class InventoryManagementScreen extends StatefulWidget {
  const InventoryManagementScreen({Key? key}) : super(key: key);

  @override
  State<InventoryManagementScreen> createState() =>
      _InventoryManagementScreenState();
}

class _InventoryManagementScreenState extends State<InventoryManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  // المقدمون
  late InventoryAdjustmentPresenter _adjustmentPresenter;
  late InventoryTransferPresenter _transferPresenter;
  late WarehousePresenter _warehousePresenter;

  // المستودع المحدد
  Warehouse? _selectedWarehouse;

  // نوع العملية المحدد
  String _selectedOperationType = 'all'; // 'all', 'adjustment', 'transfer'

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // تهيئة المقدمين باستخدام التحميل الكسول
    _adjustmentPresenter =
        AppProviders.getLazyPresenter<InventoryAdjustmentPresenter>(
            () => InventoryAdjustmentPresenter());
    _transferPresenter =
        AppProviders.getLazyPresenter<InventoryTransferPresenter>(
            () => InventoryTransferPresenter());
    _warehousePresenter = AppProviders.getLazyPresenter<WarehousePresenter>(
        () => WarehousePresenter());

    // تحميل البيانات بعد اكتمال بناء الإطار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await _warehousePresenter.loadWarehouses();
    await _adjustmentPresenter.loadAdjustments();
    await _transferPresenter.loadTransfers();
  }

  @override
  Widget build(BuildContext context) {
    return SafeLayout(
      title: 'إدارة المخزون',
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadData,
          tooltip: 'تحديث',
        ),
      ],
      body: Column(
        children: [
          _buildFilters(),
          _buildTabs(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllOperationsTab(),
                _buildAdjustmentsTab(),
                _buildTransfersTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddOperationDialog,
        tooltip: 'إضافة عملية جديدة',
        child: const Icon(Icons.add),
      ),
      child: Container(),
    );
  }

  /// بناء مرشحات البحث
  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    labelText: 'بحث',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    // تنفيذ البحث
                    setState(() {});
                  },
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: _showFilterDialog,
                icon: const Icon(Icons.filter_list),
                label: const Text('تصفية'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                ),
              ),
            ],
          ),
          if (_selectedWarehouse != null || _selectedOperationType != 'all')
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Wrap(
                spacing: 8,
                children: [
                  if (_selectedWarehouse != null)
                    Chip(
                      label: Text('المستودع: ${_selectedWarehouse!.name}'),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () {
                        setState(() {
                          _selectedWarehouse = null;
                        });
                      },
                    ),
                  if (_selectedOperationType != 'all')
                    Chip(
                      label: Text(
                          'النوع: ${_getOperationTypeName(_selectedOperationType)}'),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () {
                        setState(() {
                          _selectedOperationType = 'all';
                        });
                      },
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// بناء علامات التبويب
  Widget _buildTabs() {
    return Container(
      color: AppColors.lightTextSecondary,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.lightTextSecondary,
        indicatorColor: AppColors.primary,
        tabs: const [
          Tab(text: 'جميع العمليات'),
          Tab(text: 'تعديلات المخزون'),
          Tab(text: 'تحويلات المخزون'),
        ],
      ),
    );
  }

  /// بناء علامة تبويب جميع العمليات
  Widget _buildAllOperationsTab() {
    final adjustments = _filterAdjustments();
    final transfers = _filterTransfers();

    // دمج العمليات وترتيبها حسب التاريخ
    final allOperations = [
      ...adjustments.map((adj) => {
            'type': 'adjustment',
            'data': adj,
            'date': adj.date,
          }),
      ...transfers.map((transfer) => {
            'type': 'transfer',
            'data': transfer,
            'date': transfer.date,
          }),
    ];

    // ترتيب العمليات حسب التاريخ (الأحدث أولاً)
    allOperations.sort(
        (a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime));

    return _buildOperationsList(allOperations);
  }

  /// بناء علامة تبويب تعديلات المخزون
  Widget _buildAdjustmentsTab() {
    final adjustments = _filterAdjustments();

    final operations = adjustments
        .map((adj) => {
              'type': 'adjustment',
              'data': adj,
              'date': adj.date,
            })
        .toList();

    return _buildOperationsList(operations);
  }

  /// بناء علامة تبويب تحويلات المخزون
  Widget _buildTransfersTab() {
    final transfers = _filterTransfers();

    final operations = transfers
        .map((transfer) => {
              'type': 'transfer',
              'data': transfer,
              'date': transfer.date,
            })
        .toList();

    return _buildOperationsList(operations);
  }

  /// بناء قائمة العمليات
  Widget _buildOperationsList(List<Map<String, dynamic>> operations) {
    if (operations.isEmpty) {
      return Center(
        child: Text(
          'لا توجد عمليات',
          style: AppTypography.createCustomStyle(fontSize: 18),
        ),
      );
    }

    return ListView.builder(
      itemCount: operations.length,
      itemBuilder: (context, index) {
        final operation = operations[index];
        final type = operation['type'] as String;

        if (type == 'adjustment') {
          final adjustment = operation['data'] as InventoryAdjustment;
          return _buildAdjustmentCard(adjustment);
        } else if (type == 'transfer') {
          final transfer = operation['data'] as InventoryTransfer;
          return _buildTransferCard(transfer);
        }

        return const SizedBox.shrink();
      },
    );
  }

  /// بناء بطاقة تعديل المخزون
  Widget _buildAdjustmentCard(InventoryAdjustment adjustment) {
    final warehouseName =
        _adjustmentPresenter.getWarehouseName(adjustment.warehouseId);
    final adjustmentTypeName =
        _adjustmentPresenter.getAdjustmentTypeName(adjustment.adjustmentType);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(
          adjustment.referenceNumber ?? 'تعديل مخزون',
          style: AppTypography.lightTextTheme.titleMedium,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('التاريخ: ${_formatDate(adjustment.date)}'),
            Text('المستودع: $warehouseName'),
            Text('النوع: $adjustmentTypeName'),
            if (adjustment.notes != null && adjustment.notes!.isNotEmpty)
              Text('ملاحظات: ${adjustment.notes}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, color: AppColors.info),
              onPressed: () => _editAdjustment(adjustment),
              tooltip: 'تعديل',
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: AppColors.error),
              onPressed: () => _deleteAdjustment(adjustment),
              tooltip: 'حذف',
            ),
          ],
        ),
        onTap: () => _viewAdjustmentDetails(adjustment),
      ),
    );
  }

  /// بناء بطاقة تحويل المخزون
  Widget _buildTransferCard(InventoryTransfer transfer) {
    final sourceWarehouseName =
        _transferPresenter.getWarehouseName(transfer.sourceWarehouseId);
    final destinationWarehouseName =
        _transferPresenter.getWarehouseName(transfer.destinationWarehouseId);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(
          transfer.referenceNumber ?? 'تحويل مخزون',
          style: AppTypography.lightTextTheme.titleMedium,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('التاريخ: ${_formatDate(transfer.date)}'),
            Text('من: $sourceWarehouseName'),
            Text('إلى: $destinationWarehouseName'),
            if (transfer.notes != null && transfer.notes!.isNotEmpty)
              Text('ملاحظات: ${transfer.notes}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, color: AppColors.info),
              onPressed: () => _editTransfer(transfer),
              tooltip: 'تعديل',
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: AppColors.error),
              onPressed: () => _deleteTransfer(transfer),
              tooltip: 'حذف',
            ),
          ],
        ),
        onTap: () => _viewTransferDetails(transfer),
      ),
    );
  }

  /// تصفية تعديلات المخزون
  List<InventoryAdjustment> _filterAdjustments() {
    List<InventoryAdjustment> adjustments = _adjustmentPresenter.adjustments;

    // تصفية حسب المستودع
    if (_selectedWarehouse != null) {
      adjustments = adjustments
          .where((adj) => adj.warehouseId == _selectedWarehouse!.id)
          .toList();
    }

    // تصفية حسب النص المدخل
    final searchText = _searchController.text.trim().toLowerCase();
    if (searchText.isNotEmpty) {
      adjustments = adjustments
          .where((adj) =>
              (adj.referenceNumber?.toLowerCase().contains(searchText) ??
                  false) ||
              (adj.notes?.toLowerCase().contains(searchText) ?? false))
          .toList();
    }

    return adjustments;
  }

  /// تصفية تحويلات المخزون
  List<InventoryTransfer> _filterTransfers() {
    List<InventoryTransfer> transfers = _transferPresenter.transfers;

    // تصفية حسب المستودع
    if (_selectedWarehouse != null) {
      transfers = transfers
          .where((transfer) =>
              transfer.sourceWarehouseId == _selectedWarehouse!.id ||
              transfer.destinationWarehouseId == _selectedWarehouse!.id)
          .toList();
    }

    // تصفية حسب النص المدخل
    final searchText = _searchController.text.trim().toLowerCase();
    if (searchText.isNotEmpty) {
      transfers = transfers
          .where((transfer) =>
              (transfer.referenceNumber?.toLowerCase().contains(searchText) ??
                  false) ||
              (transfer.notes?.toLowerCase().contains(searchText) ?? false))
          .toList();
    }

    return transfers;
  }

  /// عرض مربع حوار التصفية
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        Warehouse? tempSelectedWarehouse = _selectedWarehouse;
        String tempSelectedOperationType = _selectedOperationType;

        return AlertDialog(
          title: const Text('تصفية العمليات'),
          content: StatefulBuilder(
            builder: (context, setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // اختيار المستودع
                  DropdownButtonFormField<Warehouse?>(
                    decoration: const InputDecoration(
                      labelText: 'المستودع',
                      border: OutlineInputBorder(),
                    ),
                    value: tempSelectedWarehouse,
                    items: [
                      const DropdownMenuItem<Warehouse?>(
                        value: null,
                        child: Text('جميع المستودعات'),
                      ),
                      ..._warehousePresenter.warehouses.map((warehouse) {
                        return DropdownMenuItem<Warehouse?>(
                          value: warehouse,
                          child: Text(warehouse.name),
                        );
                      }).toList(),
                    ],
                    onChanged: (value) {
                      setState(() {
                        tempSelectedWarehouse = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  // اختيار نوع العملية
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'نوع العملية',
                      border: OutlineInputBorder(),
                    ),
                    value: tempSelectedOperationType,
                    items: const [
                      DropdownMenuItem<String>(
                        value: 'all',
                        child: Text('جميع العمليات'),
                      ),
                      DropdownMenuItem<String>(
                        value: 'adjustment',
                        child: Text('تعديلات المخزون'),
                      ),
                      DropdownMenuItem<String>(
                        value: 'transfer',
                        child: Text('تحويلات المخزون'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        tempSelectedOperationType = value!;
                      });
                    },
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _selectedWarehouse = tempSelectedWarehouse;
                  _selectedOperationType = tempSelectedOperationType;
                });
                Navigator.pop(context);
              },
              child: const Text('تطبيق'),
            ),
          ],
        );
      },
    );
  }

  /// عرض مربع حوار إضافة عملية جديدة
  void _showAddOperationDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('إضافة عملية جديدة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('تعديل مخزون'),
                onTap: () {
                  Navigator.pop(context);
                  _addAdjustment();
                },
              ),
              ListTile(
                leading: const Icon(Icons.swap_horiz),
                title: const Text('تحويل مخزون'),
                onTap: () {
                  Navigator.pop(context);
                  _addTransfer();
                },
              ),
              ListTile(
                leading: const Icon(Icons.inventory_2),
                title: const Text('جرد مخزون'),
                onTap: () {
                  Navigator.pop(context);
                  _addInventoryCount();
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  /// إضافة تعديل مخزون جديد
  void _addAdjustment() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const InventoryAdjustmentFormScreen(),
      ),
    );

    if (result == true) {
      await _adjustmentPresenter.loadAdjustments();
      setState(() {});
    }
  }

  /// إضافة تحويل مخزون جديد
  void _addTransfer() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const InventoryTransferFormScreen(),
      ),
    );

    if (result == true) {
      await _transferPresenter.loadTransfers();
      setState(() {});
    }
  }

  /// إضافة جرد مخزون جديد
  void _addInventoryCount() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const InventoryAdjustmentFormScreen(
          initialAdjustmentType: 'inventory',
        ),
      ),
    );

    if (result == true) {
      await _adjustmentPresenter.loadAdjustments();
      setState(() {});
    }
  }

  /// تعديل تعديل مخزون
  void _editAdjustment(InventoryAdjustment adjustment) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InventoryAdjustmentFormScreen(
          adjustment: adjustment,
        ),
      ),
    );

    if (result == true) {
      await _adjustmentPresenter.loadAdjustments();
      setState(() {});
    }
  }

  /// تعديل تحويل مخزون
  void _editTransfer(InventoryTransfer transfer) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InventoryTransferFormScreen(
          transfer: transfer,
        ),
      ),
    );

    if (result == true) {
      await _transferPresenter.loadTransfers();
      setState(() {});
    }
  }

  /// حذف تعديل مخزون
  void _deleteAdjustment(InventoryAdjustment adjustment) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: const Text('هل أنت متأكد من حذف هذا التعديل؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);

                final success =
                    await _adjustmentPresenter.deleteAdjustment(adjustment.id);

                if (success) {
                  await _adjustmentPresenter.loadAdjustments();
                  setState(() {});
                } else {
                  _showErrorSnackBar(_adjustmentPresenter.errorMessage ??
                      'فشل في حذف التعديل');
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  /// حذف تحويل مخزون
  void _deleteTransfer(InventoryTransfer transfer) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: const Text('هل أنت متأكد من حذف هذا التحويل؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);

                // الحصول على عناصر التحويل
                final items = transfer.items;

                // حذف التحويل
                final success = await _transferPresenter.deleteTransfer(
                  transfer,
                  items,
                );

                if (success) {
                  await _transferPresenter.loadTransfers();
                  setState(() {});
                } else {
                  _showErrorSnackBar(
                      _transferPresenter.errorMessage ?? 'فشل في حذف التحويل');
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  /// عرض تفاصيل تعديل المخزون
  void _viewAdjustmentDetails(InventoryAdjustment adjustment) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InventoryOperationDetailsScreen(
          operation: adjustment,
          operationType: 'adjustment',
        ),
      ),
    );
  }

  /// عرض تفاصيل تحويل المخزون
  void _viewTransferDetails(InventoryTransfer transfer) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InventoryOperationDetailsScreen(
          operation: transfer,
          operationType: 'transfer',
        ),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  /// الحصول على اسم نوع العملية
  String _getOperationTypeName(String type) {
    switch (type) {
      case 'adjustment':
        return 'تعديلات المخزون';
      case 'transfer':
        return 'تحويلات المخزون';
      default:
        return 'جميع العمليات';
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    // تأكد من أن الحالة لا تزال مرتبطة بالشجرة
    if (!mounted) return;

    // استخدام WidgetsBinding لتجنب مشاكل التزامن
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
        ),
      );
    });
  }
}
