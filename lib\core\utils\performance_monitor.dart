import 'package:flutter/foundation.dart';
import 'app_logger.dart';
import '../providers/app_providers.dart';

/// مراقب الأداء للتطبيق
/// يراقب استخدام الذاكرة والـ presenters المحملة
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  static bool _isEnabled = kDebugMode;
  static DateTime? _lastReport;
  static const Duration _reportInterval = Duration(minutes: 5);

  /// تفعيل/إلغاء تفعيل مراقبة الأداء
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (enabled) {
      AppLogger.info('🔍 تم تفعيل مراقبة الأداء');
    }
  }

  /// تقرير الأداء الحالي
  static Map<String, dynamic> getCurrentReport() {
    final stats = AppProviders.getPerformanceStats();
    final now = DateTime.now();
    
    return {
      'timestamp': now.toIso8601String(),
      'app_performance': stats,
      'memory_status': _getMemoryStatus(),
      'recommendations': _getRecommendations(stats),
    };
  }

  /// طباعة تقرير الأداء
  static void printReport() {
    if (!_isEnabled) return;

    final now = DateTime.now();
    if (_lastReport != null && 
        now.difference(_lastReport!) < _reportInterval) {
      return;
    }

    _lastReport = now;
    final report = getCurrentReport();
    
    AppLogger.info('📊 === تقرير الأداء ===');
    AppLogger.info('⏰ الوقت: ${report['timestamp']}');
    
    final appPerf = report['app_performance'] as Map<String, dynamic>;
    AppLogger.info('🚀 التحميل الكسول: ${appPerf['lazy_loading_enabled']}');
    AppLogger.info('📦 الـ presenters المحملة: ${appPerf['loaded_presenters_count']}');
    AppLogger.info('💾 استهلاك الذاكرة: ${appPerf['memory_usage_estimate']}');
    AppLogger.info('⭐ تقييم الأداء: ${appPerf['performance_improvement']}');
    
    final recommendations = report['recommendations'] as List<String>;
    if (recommendations.isNotEmpty) {
      AppLogger.info('💡 التوصيات:');
      for (final rec in recommendations) {
        AppLogger.info('  - $rec');
      }
    }
    
    AppLogger.info('========================');
  }

  /// الحصول على حالة الذاكرة
  static Map<String, dynamic> _getMemoryStatus() {
    final stats = AppProviders.getPerformanceStats();
    final loadedCount = stats['loaded_presenters_count'] as int;
    
    return {
      'status': loadedCount < 10 ? 'ممتاز' : loadedCount < 20 ? 'جيد' : 'يحتاج تحسين',
      'loaded_presenters': loadedCount,
      'estimated_usage': stats['memory_usage_estimate'],
    };
  }

  /// الحصول على التوصيات
  static List<String> _getRecommendations(Map<String, dynamic> stats) {
    final recommendations = <String>[];
    final loadedCount = stats['loaded_presenters_count'] as int;
    
    if (loadedCount > 15) {
      recommendations.add('عدد الـ presenters المحملة مرتفع. استخدم smartCleanup()');
    }
    
    if (loadedCount > 25) {
      recommendations.add('استهلاك الذاكرة مرتفع جداً. راجع استخدام الـ presenters');
    }
    
    if (loadedCount < 5) {
      recommendations.add('الأداء ممتاز! استمر في استخدام التحميل الكسول');
    }
    
    return recommendations;
  }

  /// تنظيف ذكي تلقائي
  static void autoCleanup() {
    if (!_isEnabled) return;
    
    final stats = AppProviders.getPerformanceStats();
    final loadedCount = stats['loaded_presenters_count'] as int;
    
    if (loadedCount > 20) {
      AppLogger.info('🧹 بدء التنظيف التلقائي للذاكرة...');
      AppProviders.smartCleanup();
      AppLogger.info('✅ تم التنظيف التلقائي');
    }
  }

  /// مراقبة مستمرة (للاستخدام في التطوير)
  static void startContinuousMonitoring() {
    if (!_isEnabled || !kDebugMode) return;
    
    AppLogger.info('🔄 بدء المراقبة المستمرة للأداء');
    
    // طباعة تقرير كل 5 دقائق
    Stream.periodic(_reportInterval).listen((_) {
      printReport();
      autoCleanup();
    });
  }

  /// إحصائيات مفصلة للتطوير
  static Map<String, dynamic> getDetailedStats() {
    if (!kDebugMode) return {};
    
    final stats = AppProviders.getPerformanceStats();
    final detailedStats = stats['detailed_stats'] as Map<String, dynamic>;
    
    return {
      'basic_stats': stats,
      'detailed_stats': detailedStats,
      'memory_analysis': _getMemoryAnalysis(detailedStats),
      'performance_tips': _getPerformanceTips(),
    };
  }

  /// تحليل الذاكرة
  static Map<String, dynamic> _getMemoryAnalysis(Map<String, dynamic> detailed) {
    final loadedTypes = detailed['types'] as List<dynamic>? ?? [];
    final uptimeMinutes = detailed['uptime_minutes'] as Map<String, dynamic>? ?? {};
    
    return {
      'total_types': loadedTypes.length,
      'oldest_presenter': _getOldestPresenter(uptimeMinutes),
      'newest_presenter': _getNewestPresenter(uptimeMinutes),
      'average_uptime': _getAverageUptime(uptimeMinutes),
    };
  }

  /// الحصول على أقدم presenter
  static String? _getOldestPresenter(Map<String, dynamic> uptimes) {
    if (uptimes.isEmpty) return null;
    
    String? oldest;
    int maxUptime = 0;
    
    uptimes.forEach((type, uptime) {
      if (uptime is int && uptime > maxUptime) {
        maxUptime = uptime;
        oldest = type;
      }
    });
    
    return oldest;
  }

  /// الحصول على أحدث presenter
  static String? _getNewestPresenter(Map<String, dynamic> uptimes) {
    if (uptimes.isEmpty) return null;
    
    String? newest;
    int minUptime = 999999;
    
    uptimes.forEach((type, uptime) {
      if (uptime is int && uptime < minUptime) {
        minUptime = uptime;
        newest = type;
      }
    });
    
    return newest;
  }

  /// الحصول على متوسط وقت التشغيل
  static double _getAverageUptime(Map<String, dynamic> uptimes) {
    if (uptimes.isEmpty) return 0.0;
    
    int total = 0;
    int count = 0;
    
    uptimes.forEach((type, uptime) {
      if (uptime is int) {
        total += uptime;
        count++;
      }
    });
    
    return count > 0 ? total / count : 0.0;
  }

  /// نصائح الأداء
  static List<String> _getPerformanceTips() {
    return [
      'استخدم AppProviders.getLazyPresenter() بدلاً من Provider.of',
      'استخدم ListenableBuilder بدلاً من Consumer',
      'استدعي AppProviders.smartCleanup() دورياً',
      'راقب عدد الـ presenters المحملة',
      'استخدم LazyProviderWrapper للشاشات المعقدة',
    ];
  }
}
