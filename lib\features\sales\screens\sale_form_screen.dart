import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/index.dart';
import '../../../core/models/sale.dart';
import '../../../core/models/sale_item.dart';
import '../../../core/models/sale_status.dart';
import '../../../core/models/product.dart';
import '../presenters/sale_presenter.dart';
import '../../products/presenters/product_presenter.dart';
import '../../customers/presenters/customer_presenter.dart';
import '../../../core/theme/index.dart';

class SaleFormScreen extends StatefulWidget {
  final Sale? sale;
  final String? invoiceType; // إضافة نوع الفاتورة

  const SaleFormScreen({Key? key, this.sale, this.invoiceType = 'sale'})
      : super(key: key);

  @override
  State<SaleFormScreen> createState() => _SaleFormScreenState();
}

class _SaleFormScreenState extends State<SaleFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late SalePresenter _salePresenter;
  late ProductPresenter _productPresenter;
  late CustomerPresenter _customerPresenter;

  String? _selectedCustomerId;
  final List<SaleItem> _items = [];
  final _notesController = TextEditingController();
  double _subtotal = 0.0;
  double _discount = 0.0;
  double _tax = 0.0;
  double _total = 0.0;
  SaleStatus _status = SaleStatus.draft;
  final _discountController = TextEditingController(text: '0.0');
  final _taxController = TextEditingController(text: '0.0');

  @override
  void initState() {
    super.initState();
    _salePresenter =
        AppProviders.getLazyPresenter<SalePresenter>(() => SalePresenter());
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
        () => ProductPresenter());
    _customerPresenter = AppProviders.getLazyPresenter<CustomerPresenter>(
        () => CustomerPresenter());

    // تحميل البيانات باستخدام Future.microtask لتجنب استدعاء setState أثناء البناء
    Future.microtask(() {
      _productPresenter.loadProducts();
      _customerPresenter.loadCustomers();
    });
    _initializeFields();
  }

  void _initializeFields() {
    if (widget.sale != null) {
      _selectedCustomerId = widget.sale!.customerId;
      _items.addAll(widget.sale!.items);
      _notesController.text = widget.sale!.notes ?? '';
      _subtotal = widget.sale!.subtotal;
      _discount = widget.sale!.discount;
      _tax = widget.sale!.tax;
      _total = widget.sale!.total;
      _status = widget.sale!.status;
      _discountController.text = _discount.toString();
      _taxController.text = _tax.toString();
    }
    _calculateTotals();
  }

  void _calculateTotals() {
    _subtotal = _items.fold(0, (sum, item) => sum + item.total);
    _discount = double.tryParse(_discountController.text) ?? 0.0;
    _tax = double.tryParse(_taxController.text) ?? 0.0;
    _total = _subtotal - _discount + _tax;

    setState(() {});
  }

  @override
  void dispose() {
    _notesController.dispose();
    _discountController.dispose();
    _taxController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تحديد عنوان الشاشة بناءً على نوع الفاتورة
    String title = '';
    if (widget.sale == null) {
      if (widget.invoiceType == 'sale') {
        title = 'فاتورة مبيعات جديدة';
      } else if (widget.invoiceType == 'sale_return') {
        title = 'فاتورة مرتجع مبيعات جديدة';
      } else if (widget.invoiceType == 'purchase') {
        title = 'فاتورة مشتريات جديدة';
      } else if (widget.invoiceType == 'purchase_return') {
        title = 'فاتورة مرتجع مشتريات جديدة';
      } else {
        title = 'فاتورة جديدة';
      }
    } else {
      title = 'تعديل الفاتورة';
    }

    return Scaffold(
      appBar: AkAppBar(
        title: title,
        showBackButton: true,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  _buildCustomerSection(),
                  const SizedBox(height: 24),
                  _buildItemsSection(),
                  const SizedBox(height: 24),
                  _buildTotalsSection(),
                  const SizedBox(height: 24),
                  _buildNotesSection(),
                  const SizedBox(height: 24),
                  _buildStatusSection(),
                ],
              ),
            ),
            _buildBottomBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Customer',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          ListenableBuilder(
            listenable: _customerPresenter,
            builder: (context, child) {
              final customers = _customerPresenter.customers;
              return DropdownButtonFormField<String>(
                value: _selectedCustomerId,
                decoration: const InputDecoration(
                  labelText: 'Select Customer',
                  border: OutlineInputBorder(),
                ),
                items: [
                  const DropdownMenuItem(
                    value: null,
                    child: Text('Walk-in Customer'),
                  ),
                  ...customers.map(
                    (customer) => DropdownMenuItem(
                      value: customer.id,
                      child: Text(customer.name),
                    ),
                  ),
                ],
                onChanged: (value) {
                  setState(() => _selectedCustomerId = value);
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildItemsSection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Items',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              ElevatedButton.icon(
                icon: const Icon(Icons.add),
                label: const Text('Add Item'),
                onPressed: _showAddItemDialog,
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_items.isEmpty)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Text('No items added yet'),
              ),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _items.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final item = _items[index];
                final product = _getProductById(item.productId);
                return ListTile(
                  title: Text(product?.name ?? 'Unknown Product'),
                  subtitle: Text('${item.quantity} x ${item.price}'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '${item.total}',
                        style: const AppTypography(fontWeight: FontWeight.bold),
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () => _showEditItemDialog(index),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () {
                          setState(() {
                            _items.removeAt(index);
                            _calculateTotals();
                          });
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildTotalsSection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Totals',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Expanded(
                flex: 2,
                child: Text('Subtotal:'),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  _subtotal.toStringAsFixed(2),
                  style: const AppTypography(fontWeight: FontWeight.bold),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Expanded(
                flex: 2,
                child: Text('Discount:'),
              ),
              Expanded(
                flex: 1,
                child: TextFormField(
                  controller: _discountController,
                  decoration: const InputDecoration(
                    isDense: true,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    border: OutlineInputBorder(),
                  ),
                  textAlign: TextAlign.end,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  onChanged: (value) {
                    _calculateTotals();
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Expanded(
                flex: 2,
                child: Text('Tax:'),
              ),
              Expanded(
                flex: 1,
                child: TextFormField(
                  controller: _taxController,
                  decoration: const InputDecoration(
                    isDense: true,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    border: OutlineInputBorder(),
                  ),
                  textAlign: TextAlign.end,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  onChanged: (value) {
                    _calculateTotals();
                  },
                ),
              ),
            ],
          ),
          const Divider(thickness: 1),
          Row(
            children: [
              const Expanded(
                flex: 2,
                child: Text(
                  'Total:',
                  style: AppTypography(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  _total.toStringAsFixed(2),
                  style: const AppTypography(
                      fontWeight: FontWeight.bold, fontSize: 18),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notes',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _notesController,
            decoration: const InputDecoration(
              hintText: 'Add notes about the sale',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusSection() {
    return AkCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Status',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<SaleStatus>(
            value: _status,
            decoration: const InputDecoration(
              labelText: 'Sale Status',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: SaleStatus.draft, child: Text('Draft')),
              DropdownMenuItem(
                  value: SaleStatus.pending, child: Text('Pending')),
              DropdownMenuItem(
                  value: SaleStatus.completed, child: Text('Completed')),
              DropdownMenuItem(
                  value: SaleStatus.cancelled, child: Text('Cancelled')),
              DropdownMenuItem(
                  value: SaleStatus.returned, child: Text('Returned')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() => _status = value);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightTextSecondary,
        boxShadow: [
          BoxShadow(
            color: AppColors.lightTextPrimary.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _items.isEmpty ? null : _submitForm,
              child: Text(widget.sale == null ? 'Create Sale' : 'Update Sale'),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddItemDialog() {
    Product? selectedProduct;
    double quantity = 1.0;
    double price = 0.0;
    double discount = 0.0;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Add Item'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ListenableBuilder(
                    listenable: _productPresenter,
                    builder: (context, child) {
                      final products = _productPresenter.products
                          .where((p) => p.isActive && p.quantity > 0)
                          .toList();
                      return DropdownButtonFormField<Product>(
                        value: selectedProduct,
                        decoration: const InputDecoration(
                          labelText: 'Select Product',
                          border: OutlineInputBorder(),
                        ),
                        items: products
                            .map(
                              (product) => DropdownMenuItem(
                                value: product,
                                child: Text(product.name),
                              ),
                            )
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            selectedProduct = value;
                            if (value != null) {
                              price = value.salePrice;
                            }
                          });
                        },
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          initialValue: quantity.toString(),
                          decoration: const InputDecoration(
                            labelText: 'Quantity',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              quantity = double.tryParse(value) ?? 1;
                            });
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          initialValue: price.toString(),
                          decoration: const InputDecoration(
                            labelText: 'Price',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              price = double.tryParse(value) ?? 0;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    initialValue: discount.toString(),
                    decoration: const InputDecoration(
                      labelText: 'Item Discount',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        discount = double.tryParse(value) ?? 0;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: selectedProduct == null
                      ? null
                      : () {
                          Navigator.pop(context);
                          setState(() {
                            _items.add(SaleItem(
                              productId: selectedProduct!.id,
                              productName: selectedProduct!.name,
                              quantity: quantity,
                              price: price,
                              discount: discount,
                            ));
                            _calculateTotals();
                          });
                        },
                  child: const Text('Add'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showEditItemDialog(int index) {
    final item = _items[index];
    final product = _getProductById(item.productId);
    double quantity = item.quantity;
    double price = item.price;
    double discount = item.discount;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Edit Item'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    product?.name ?? 'Unknown Product',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          initialValue: quantity.toString(),
                          decoration: const InputDecoration(
                            labelText: 'Quantity',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              quantity = double.tryParse(value) ?? 1;
                            });
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          initialValue: price.toString(),
                          decoration: const InputDecoration(
                            labelText: 'Price',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              price = double.tryParse(value) ?? 0;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    initialValue: discount.toString(),
                    decoration: const InputDecoration(
                      labelText: 'Item Discount',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        discount = double.tryParse(value) ?? 0;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    setState(() {
                      _items[index] = SaleItem(
                        productId: item.productId,
                        productName: item.productName,
                        quantity: quantity,
                        price: price,
                        discount: discount,
                      );
                      _calculateTotals();
                    });
                  },
                  child: const Text('Update'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Product? _getProductById(String id) {
    try {
      return _productPresenter.products.firstWhere((p) => p.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate() || _items.isEmpty) return;

    final sale = Sale(
      id: widget.sale?.id,
      customerId: _selectedCustomerId,
      items: _items,
      subtotal: _subtotal,
      total: _total,
      discount: _discount,
      tax: _tax,
      status: _status,
      paymentMethod: 'cash',
      notes: _notesController.text.isEmpty ? null : _notesController.text,
    );

    // حفظ مرجع للسياق قبل العملية غير المتزامنة
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    bool success;
    if (widget.sale == null) {
      success = await _salePresenter.createSale(sale);
    } else {
      success = await _salePresenter.updateSale(sale);
    }

    if (mounted) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            success
                ? 'تم ${widget.sale == null ? 'إنشاء' : 'تحديث'} الفاتورة بنجاح'
                : 'فشل ${widget.sale == null ? 'إنشاء' : 'تحديث'} الفاتورة',
          ),
          backgroundColor: success ? AppColors.success : AppColors.error,
        ),
      );

      if (success) {
        Navigator.pop(context, true);
      }
    }
  }
}
