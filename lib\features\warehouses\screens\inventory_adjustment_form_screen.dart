import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/widgets/index.dart';
import '../../../core/utils/safe_layout_manager_widget.dart';
import '../presenters/inventory_adjustment_presenter.dart';
import '../../../core/models/inventory_adjustment.dart';
import '../../../core/models/inventory_adjustment_item.dart';
import '../../../core/models/product.dart';
import '../../../core/theme/index.dart';

/// شاشة نموذج تعديل المخزون
class InventoryAdjustmentFormScreen extends StatefulWidget {
  final InventoryAdjustment? adjustment;
  final bool isReadOnly;
  final Product? initialProduct;
  final String? initialWarehouseId;
  final String? initialAdjustmentType;

  const InventoryAdjustmentFormScreen({
    Key? key,
    this.adjustment,
    this.isReadOnly = false,
    this.initialProduct,
    this.initialWarehouseId,
    this.initialAdjustmentType,
  }) : super(key: key);

  @override
  State<InventoryAdjustmentFormScreen> createState() =>
      _InventoryAdjustmentFormScreenState();
}

class _InventoryAdjustmentFormScreenState
    extends State<InventoryAdjustmentFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _referenceNumberController =
      TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  DateTime _date = DateTime.now();
  String? _warehouseId;
  String _type = 'increase';

  final List<InventoryAdjustmentItem> _items = [];

  bool _isLoading = false;

  late InventoryAdjustmentPresenter _presenter;

  @override
  void initState() {
    super.initState();
    _presenter = AppProviders.getLazyPresenter<InventoryAdjustmentPresenter>(
        () => InventoryAdjustmentPresenter());

    _loadData();

    if (widget.adjustment != null) {
      // تحميل بيانات التعديل الموجود
      _referenceNumberController.text =
          widget.adjustment!.referenceNumber ?? '';
      _date = widget.adjustment!.date;
      _warehouseId = widget.adjustment!.warehouseId;
      _type = widget.adjustment!.adjustmentType;
      _notesController.text = widget.adjustment!.notes ?? '';

      // تحميل عناصر التعديل
      if (widget.adjustment!.items.isNotEmpty) {
        setState(() {
          _items.clear();
          _items.addAll(widget.adjustment!.items);
        });
      }
    } else {
      // استخدام البيانات الأولية إذا كانت متوفرة
      if (widget.initialWarehouseId != null) {
        _warehouseId = widget.initialWarehouseId;
      }

      if (widget.initialAdjustmentType != null) {
        _type = widget.initialAdjustmentType!;
      }

      if (widget.initialProduct != null) {
        // إضافة المنتج المحدد إلى قائمة العناصر
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _items.add(InventoryAdjustmentItem(
              productId: widget.initialProduct!.id,
              quantity: 1.0, // كمية افتراضية
              adjustmentId: '',
            ));
          });
        });
      }
    }
  }

  @override
  void dispose() {
    _referenceNumberController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await _presenter.loadWarehouses();
    await _presenter.loadProducts();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.adjustment != null;
    final isReadOnly = widget.isReadOnly;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isReadOnly
              ? 'عرض تعديل'
              : isEditing
                  ? 'تعديل تعديل'
                  : 'إضافة تعديل',
        ),
      ),
      body: SafeLayoutManagerWidget(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات التعديل
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'معلومات التعديل',
                          style: AppTypography.createCustomStyle(
                            fontSize: 18,
                            fontWeight: AppTypography.weightBold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // رقم المرجع
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'رقم المرجع',
                              style: AppTypography.createCustomStyle(
                                fontSize: 14,
                                fontWeight: AppTypography.weightBold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextFormField(
                              controller: _referenceNumberController,
                              readOnly: isReadOnly,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // التاريخ
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'التاريخ *',
                            border: OutlineInputBorder(),
                            suffixIcon: Icon(Icons.calendar_today),
                          ),
                          readOnly: true,
                          controller: TextEditingController(
                            text: DateFormat('yyyy/MM/dd').format(_date),
                          ),
                          onTap: isReadOnly
                              ? null
                              : () async {
                                  final date = await showDatePicker(
                                    context: context,
                                    initialDate: _date,
                                    firstDate: DateTime(2020),
                                    lastDate: DateTime.now()
                                        .add(const Duration(days: 365)),
                                  );
                                  if (date != null) {
                                    setState(() {
                                      _date = date;
                                    });
                                  }
                                },
                        ),
                        const SizedBox(height: 16),

                        // المخزن
                        AkDropdownInput<String>(
                          label: 'المخزن',
                          value: _warehouseId,
                          items: _presenter.warehouses
                              .map((warehouse) => DropdownMenuItem(
                                    value: warehouse.id,
                                    child: Text(warehouse.name),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            if (!isReadOnly && value != null) {
                              setState(() {
                                _warehouseId = value;
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 16),

                        // نوع التعديل
                        AkDropdownInput<String>(
                          label: 'نوع التعديل',
                          value: _type,
                          items: const [
                            DropdownMenuItem(
                              value: 'increase',
                              child: Text('زيادة'),
                            ),
                            DropdownMenuItem(
                              value: 'decrease',
                              child: Text('نقصان'),
                            ),
                            DropdownMenuItem(
                              value: 'inventory',
                              child: Text('جرد'),
                            ),
                          ],
                          onChanged: (value) {
                            if (!isReadOnly && value != null) {
                              setState(() {
                                _type = value;
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 16),

                        // ملاحظات
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'ملاحظات',
                              style: AppTypography(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextFormField(
                              controller: _notesController,
                              readOnly: isReadOnly,
                              maxLines: 3,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // عناصر التعديل
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'عناصر التعديل',
                              style: AppTypography(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (!isReadOnly)
                              ElevatedButton.icon(
                                onPressed: _addItem,
                                icon: const Icon(Icons.add),
                                label: const Text('إضافة منتج'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.lightTextSecondary,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // جدول العناصر
                        _buildItemsTable(),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // أزرار الإجراءات
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.arrow_back),
                      label: const Text('رجوع'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.lightTextSecondary,
                      ),
                    ),
                    if (!isReadOnly) ...[
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        onPressed: _isLoading ? null : _saveAdjustment,
                        icon: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              )
                            : Icon(isEditing ? Icons.update : Icons.save),
                        label: Text(isEditing ? 'تحديث' : 'حفظ'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.lightTextSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء جدول العناصر
  Widget _buildItemsTable() {
    if (_items.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'لا توجد عناصر',
            style: AppTypography.createCustomStyle(
                color: AppColors.lightTextSecondary),
          ),
        ),
      );
    }

    final columns = [
      const FinancialTableColumn(
        title: 'المنتج',
        field: 'product_name',
        alignment: ColumnAlignment.start,
      ),
      const FinancialTableColumn(
        title: 'الكمية',
        field: 'quantity',
        alignment: ColumnAlignment.center,
        isNumeric: true,
      ),
      const FinancialTableColumn(
        title: 'الإجراءات',
        field: 'actions',
        alignment: ColumnAlignment.center,
      ),
    ];

    final data = _items.map((item) {
      return {
        'id': item.id,
        'product_name': _presenter.getProductName(item.productId),
        'quantity': item.quantity,
        'actions': item,
      };
    }).toList();

    return FinancialDataTable(
      columns: columns,
      data: data,
      customCellBuilder: (context, rowIndex, columnIndex, value, field) {
        if (field == 'actions' && !widget.isReadOnly) {
          final item = value as InventoryAdjustmentItem;
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, color: AppColors.info),
                onPressed: () => _editItem(item),
                tooltip: 'تعديل',
              ),
              IconButton(
                icon: const Icon(Icons.delete, color: AppColors.error),
                onPressed: () => _deleteItem(item),
                tooltip: 'حذف',
              ),
            ],
          );
        }
        return null;
      },
    );
  }

  /// إضافة عنصر
  void _addItem() {
    showDialog(
      context: context,
      builder: (context) => _ItemDialog(
        products: _presenter.products,
        onSave: (productId, quantity) {
          setState(() {
            _items.add(InventoryAdjustmentItem(
              productId: productId,
              quantity: quantity,
              adjustmentId: widget.adjustment?.id ?? '',
            ));
          });
        },
      ),
    );
  }

  /// تعديل عنصر
  void _editItem(InventoryAdjustmentItem item) {
    showDialog(
      context: context,
      builder: (context) => _ItemDialog(
        products: _presenter.products,
        productId: item.productId,
        quantity: item.quantity,
        onSave: (productId, quantity) {
          setState(() {
            final index = _items.indexOf(item);
            _items[index] = InventoryAdjustmentItem(
              id: item.id,
              productId: productId,
              quantity: quantity,
              adjustmentId: item.adjustmentId,
            );
          });
        },
      ),
    );
  }

  /// حذف عنصر
  void _deleteItem(InventoryAdjustmentItem item) {
    setState(() {
      _items.remove(item);
    });
  }

  /// حفظ التعديل
  void _saveAdjustment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إضافة منتج واحد على الأقل'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final adjustment = InventoryAdjustment(
      id: widget.adjustment?.id,
      referenceNumber: _referenceNumberController.text.isEmpty
          ? null
          : _referenceNumberController.text,
      date: _date,
      warehouseId: _warehouseId!,
      adjustmentType: _type,
      notes: _notesController.text.isEmpty ? null : _notesController.text,
    );

    bool success;

    try {
      if (widget.adjustment == null) {
        // إضافة تعديل جديد
        final addedAdjustment =
            await _presenter.addAdjustment(adjustment, _items);
        success = addedAdjustment != null;
      } else {
        // تحديث تعديل موجود
        success = await _presenter.updateAdjustment(adjustment, _items);
      }

      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;

      if (success) {
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_presenter.errorMessage ?? 'فشل في حفظ التعديل'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}

/// مربع حوار إضافة/تعديل عنصر
class _ItemDialog extends StatefulWidget {
  final List<Product> products;
  final String? productId;
  final double? quantity;
  final Function(String, double) onSave;

  const _ItemDialog({
    Key? key,
    required this.products,
    this.productId,
    this.quantity,
    required this.onSave,
  }) : super(key: key);

  @override
  State<_ItemDialog> createState() => _ItemDialogState();
}

class _ItemDialogState extends State<_ItemDialog> {
  String? _productId;
  final TextEditingController _quantityController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _productId = widget.productId;
    _quantityController.text = widget.quantity?.toString() ?? '1.0';
  }

  @override
  void dispose() {
    _quantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.productId == null ? 'إضافة منتج' : 'تعديل منتج'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // المنتج
          AkDropdownInput<String>(
            label: 'المنتج',
            value: _productId,
            items: widget.products
                .map((product) => DropdownMenuItem(
                      value: product.id,
                      child: Text(product.name),
                    ))
                .toList(),
            onChanged: (value) {
              setState(() {
                _productId = value;
              });
            },
          ),
          const SizedBox(height: 16),

          // الكمية
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'الكمية',
                style: AppTypography(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _quantityController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_productId == null) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('يجب اختيار منتج'),
                  backgroundColor: AppColors.error,
                ),
              );
              return;
            }

            final quantity = double.tryParse(_quantityController.text);
            if (quantity == null || quantity <= 0) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('يجب إدخال كمية صحيحة'),
                  backgroundColor: AppColors.error,
                ),
              );
              return;
            }

            widget.onSave(_productId!, quantity);
            Navigator.pop(context);
          },
          child: const Text('حفظ'),
        ),
      ],
    );
  }
}
