/// نموذج صلاحيات الحسابات
class AccountPermission {
  final String? id;
  final String userId;
  final String accountId;
  final bool canView;
  final bool canEdit;
  final bool canDelete;
  final bool canCreateTransactions;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isDeleted;

  AccountPermission({
    this.id,
    required this.userId,
    required this.accountId,
    this.canView = true,
    this.canEdit = false,
    this.canDelete = false,
    this.canCreateTransactions = false,
    this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
  });

  /// إنشاء نسخة جديدة مع تحديث بعض الخصائص
  AccountPermission copyWith({
    String? id,
    String? userId,
    String? accountId,
    bool? canView,
    bool? canEdit,
    bool? canDelete,
    bool? canCreateTransactions,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
  }) {
    return AccountPermission(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      accountId: accountId ?? this.accountId,
      canView: canView ?? this.canView,
      canEdit: canEdit ?? this.canEdit,
      canDelete: canDelete ?? this.canDelete,
      canCreateTransactions: canCreateTransactions ?? this.canCreateTransactions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل النموذج إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'account_id': accountId,
      'can_view': canView ? 1 : 0,
      'can_edit': canEdit ? 1 : 0,
      'can_delete': canDelete ? 1 : 0,
      'can_create_transactions': canCreateTransactions ? 1 : 0,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء نموذج من خريطة
  factory AccountPermission.fromMap(Map<String, dynamic> map) {
    return AccountPermission(
      id: map['id'],
      userId: map['user_id'],
      accountId: map['account_id'],
      canView: map['can_view'] == 1,
      canEdit: map['can_edit'] == 1,
      canDelete: map['can_delete'] == 1,
      canCreateTransactions: map['can_create_transactions'] == 1,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : null,
      isDeleted: map['is_deleted'] == 1,
    );
  }

  @override
  String toString() {
    return 'AccountPermission(id: $id, userId: $userId, accountId: $accountId, canView: $canView, canEdit: $canEdit, canDelete: $canDelete, canCreateTransactions: $canCreateTransactions)';
  }
}
