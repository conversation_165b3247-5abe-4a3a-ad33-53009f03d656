import 'package:uuid/uuid.dart';

import '../../../core/utils/app_logger.dart';
import '../models/branch.dart';
import '../../../core/database/database_service.dart';

/// خدمة إدارة الفروع
class BranchService {
  final DatabaseService _db = DatabaseService.instance;
  final Uuid _uuid = const Uuid();

  /// الحصول على قائمة الفروع
  Future<List<Branch>> getBranches({bool includeInactive = false}) async {
    try {
      // بناء استعلام SQL
      String whereClause = 'is_deleted = 0';
      List<dynamic> whereArgs = [];

      // إضافة شرط الحالة النشطة
      if (!includeInactive) {
        whereClause += ' AND is_active = 1';
      }

      final result = await _db.query(
        'branches',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'is_main DESC, name ASC',
      );

      // إذا لم تكن هناك فروع، إرجاع قائمة فارغة
      // (تم نقل إنشاء الفرع الافتراضي إلى BasicDataInitializer)
      if (result.isEmpty) {
        AppLogger.info('لا توجد فروع في قاعدة البيانات');
        return [];
      }

      return result.map((map) => Branch.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الفروع: $e');
      // إرجاع فرع افتراضي في حالة الخطأ
      return [Branch(id: 'default', name: 'الفرع الرئيسي', isMain: true)];
    }
  }

  /// الحصول على فرع بواسطة المعرف
  Future<Branch?> getBranchById(String id) async {
    try {
      final result = await _db.query(
        'branches',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isEmpty) {
        return null;
      }

      return Branch.fromMap(result.first);
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الفرع بالمعرف: $e');
      return null;
    }
  }

  /// إنشاء فرع جديد
  Future<Branch> createBranch(Branch branch) async {
    try {
      final branchWithId =
          branch.id.isEmpty ? branch.copyWith(id: _uuid.v4()) : branch;

      await _db.insert('branches', branchWithId.toMap());

      return branchWithId;
    } catch (e) {
      AppLogger.error('خطأ في إنشاء الفرع: $e');
      rethrow;
    }
  }

  /// إضافة فرع جديد (واجهة بديلة لـ createBranch)
  Future<String?> addBranch(Branch branch) async {
    try {
      final createdBranch = await createBranch(branch);
      return createdBranch.id;
    } catch (e) {
      AppLogger.error('خطأ في إضافة الفرع: $e');
      return null;
    }
  }

  /// تحديث فرع
  Future<bool> updateBranch(Branch branch) async {
    try {
      final count = await _db.update(
        'branches',
        branch.toMap(),
        where: 'id = ?',
        whereArgs: [branch.id],
      );

      return count > 0;
    } catch (e) {
      AppLogger.error('خطأ في تحديث الفرع: $e');
      return false;
    }
  }

  /// حذف فرع
  Future<bool> deleteBranch(String id) async {
    try {
      // التحقق مما إذا كان الفرع رئيسيًا
      final branch = await getBranchById(id);
      if (branch != null && branch.isMain) {
        AppLogger.warning('لا يمكن حذف الفرع الرئيسي');
        return false;
      }

      final count = await _db.delete(
        'branches',
        where: 'id = ?',
        whereArgs: [id],
      );

      return count > 0;
    } catch (e) {
      AppLogger.error('خطأ في حذف الفرع: $e');
      return false;
    }
  }

  /// تغيير حالة الفرع (نشط/غير نشط)
  Future<bool> updateBranchStatus(String id, bool isActive) async {
    try {
      // التحقق من وجود الفرع
      final branch = await getBranchById(id);
      if (branch == null) {
        AppLogger.warning('الفرع غير موجود: $id');
        return false;
      }

      // تحديث حالة الفرع
      final updatedBranch = branch.copyWith(isActive: isActive);

      return await updateBranch(updatedBranch);
    } catch (e) {
      AppLogger.error('خطأ في تغيير حالة الفرع: $e');
      return false;
    }
  }
}
