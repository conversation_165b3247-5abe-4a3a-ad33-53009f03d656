import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/index.dart';

/// نظام شريط التطبيق الموحد لتطبيق تاجر بلس
/// يحتوي على جميع أنواع أشرطة التطبيق المستخدمة في التطبيق
///
/// **المميزات:**
/// - تصميم موحد ومتناسق لجميع أشرطة التطبيق
/// - دعم كامل للوضع المظلم/الفاتح
/// - تأثيرات تفاعلية متقدمة
/// - تحميل كسول للعناصر الثقيلة
/// - دوال مساعدة سريعة
/// - تعليقات شاملة باللغة العربية

// ═══════════════════════════════════════════════════════════════════════════════
// ● الأنواع والثوابت
// ═══════════════════════════════════════════════════════════════════════════════

/// أنواع أشرطة التطبيق المختلفة
enum AkAppBarType {
  /// شريط تطبيق عادي
  normal,

  /// شريط تطبيق مع بحث
  search,

  /// شريط تطبيق مع تبويبات
  tabbed,

  /// شريط تطبيق قابل للطي
  sliver,
}

/// أحجام أشرطة التطبيق
enum AkAppBarSize {
  /// حجم صغير
  small,

  /// حجم متوسط
  medium,

  /// حجم كبير
  large,
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● 1. شريط التطبيق الأساسي الموحد (AkAppBar)
// ═══════════════════════════════════════════════════════════════════════════════

/// شريط التطبيق الأساسي الموحد مع تصميم متناسق
///
/// **المميزات:**
/// - تصميم موحد لجميع أشرطة التطبيق
/// - دعم كامل للوضع المظلم/الفاتح
/// - ألوان ذكية تتكيف مع الخلفية
/// - أحجام متعددة قابلة للتخصيص
/// - دعم الأيقونات والإجراءات
///
/// **مثال الاستخدام:**
/// ```dart
/// AkAppBar(
///   title: 'إدارة المنتجات',
///   showBackButton: true,
///   actions: [
///     IconButton(icon: Icon(Icons.search), onPressed: () {}),
///     IconButton(icon: Icon(Icons.more_vert), onPressed: () {}),
///   ],
/// )
/// ```
class AkAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// عنوان شريط التطبيق
  final String title;

  /// قائمة الإجراءات في الشريط
  final List<Widget>? actions;

  /// هل يتم عرض زر الرجوع
  final bool showBackButton;

  /// دالة عند الضغط على زر القائمة
  final VoidCallback? onMenuPressed;

  /// دالة عند الضغط على زر الرجوع
  final VoidCallback? onBackPressed;

  /// ودجت مخصص في بداية الشريط
  final Widget? leading;

  /// ارتفاع الظل
  final double? elevation;

  /// لون الخلفية المخصص
  final Color? backgroundColor;

  /// لون النص والأيقونات المخصص
  final Color? foregroundColor;

  /// ودجت في أسفل الشريط (مثل TabBar)
  final PreferredSizeWidget? bottom;

  /// هل يتم توسيط العنوان
  final bool centerTitle;

  /// حجم شريط التطبيق
  final AkAppBarSize size;

  /// هل يتم إخفاء الشريط عند التمرير
  final bool floating;

  /// هل يتم تثبيت الشريط
  final bool pinned;

  /// هل يتم توسيع الشريط
  final bool snap;

  const AkAppBar({
    super.key,
    required this.title,
    this.actions,
    this.showBackButton = true,
    this.onMenuPressed,
    this.onBackPressed,
    this.leading,
    this.elevation,
    this.backgroundColor,
    this.foregroundColor,
    this.bottom,
    this.centerTitle = true,
    this.size = AkAppBarSize.medium,
    this.floating = false,
    this.pinned = true,
    this.snap = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // تحديد الألوان الذكية
    final effectiveBackgroundColor = backgroundColor ??
        (isDark ? AppColors.darkSurface : AppColors.lightSurface);
    final effectiveForegroundColor = foregroundColor ??
        AppColors.getTextColorForBackground(effectiveBackgroundColor);

    // تحديد الارتفاع حسب الحجم
    final toolbarHeight = _getToolbarHeight();

    return AppBar(
      title: Text(
        title,
        style: AppTypography.createCustomStyle(
          fontSize: _getTitleFontSize(),
          fontWeight: AppTypography.weightSemiBold,
          color: effectiveForegroundColor,
        ),
      ),
      centerTitle: centerTitle,
      elevation: elevation ?? _getElevation(),
      backgroundColor: effectiveBackgroundColor,
      foregroundColor: effectiveForegroundColor,
      toolbarHeight: toolbarHeight,
      bottom: bottom,

      // تحديد الودجت في بداية الشريط
      leading: leading ?? _buildLeading(context, effectiveForegroundColor),

      // تطبيق تصميم متجاوب للإجراءات لمنع overflow
      actions:
          _buildResponsiveActions(context, actions, effectiveForegroundColor),
      iconTheme: IconThemeData(
        color: effectiveForegroundColor,
        size: _getIconSize(),
      ),
      actionsIconTheme: IconThemeData(
        color: effectiveForegroundColor,
        size: _getIconSize(),
      ),
      titleTextStyle: AppTypography.createCustomStyle(
        fontSize: _getTitleFontSize(),
        fontWeight: AppTypography.weightSemiBold,
        color: effectiveForegroundColor,
      ),
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
      ),
    );
  }

  /// بناء الودجت في بداية الشريط
  Widget? _buildLeading(BuildContext context, Color foregroundColor) {
    if (showBackButton && Navigator.of(context).canPop()) {
      return IconButton(
        icon: const Icon(Icons.arrow_back_ios),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
        color: foregroundColor,
        tooltip: 'رجوع',
        iconSize: _getIconSize(),
      );
    }

    if (onMenuPressed != null) {
      return IconButton(
        icon: const Icon(Icons.menu),
        onPressed: onMenuPressed,
        color: foregroundColor,
        tooltip: 'القائمة الرئيسية',
        iconSize: _getIconSize(),
      );
    }

    return null;
  }

  /// بناء الإجراءات بتصميم متجاوب لمنع overflow
  List<Widget>? _buildResponsiveActions(
      BuildContext context, List<Widget>? actions, Color foregroundColor) {
    if (actions == null || actions.isEmpty) return null;

    // إذا كان عدد الإجراءات أقل من أو يساوي 3، عرضها كما هي
    if (actions.length <= 3) {
      return actions;
    }

    // إذا كان عدد الإجراءات أكثر من 3، عرض أول 2 وباقي في قائمة منسدلة
    final visibleActions = actions.take(2).toList();
    final hiddenActions = actions.skip(2).toList();

    return [
      ...visibleActions,
      PopupMenuButton<int>(
        icon: Icon(
          Icons.more_vert,
          color: foregroundColor,
          size: _getIconSize(),
        ),
        tooltip: 'المزيد',
        onSelected: (index) {
          // تنفيذ الإجراء المحدد من القائمة المخفية
          if (index < hiddenActions.length) {
            final widget = hiddenActions[index];
            if (widget is IconButton && widget.onPressed != null) {
              widget.onPressed!();
            }
          }
        },
        itemBuilder: (context) {
          return hiddenActions.asMap().entries.map((entry) {
            final index = entry.key;
            final widget = entry.value;

            // استخراج الأيقونة والتلميح من IconButton
            IconData iconData = Icons.more_horiz;
            String tooltip = 'إجراء ${index + 1}';

            if (widget is IconButton) {
              if (widget.icon is Icon) {
                iconData = (widget.icon as Icon).icon ?? Icons.more_horiz;
              }
              tooltip = widget.tooltip ?? tooltip;
            }

            return PopupMenuItem<int>(
              value: index,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(iconData, size: AppDimensions.smallIconSize),
                  const SizedBox(width: AppDimensions.spacing8),
                  Text(tooltip),
                ],
              ),
            );
          }).toList();
        },
      ),
    ];
  }

  /// الحصول على ارتفاع شريط الأدوات حسب الحجم
  double _getToolbarHeight() {
    switch (size) {
      case AkAppBarSize.small:
        return 48;
      case AkAppBarSize.medium:
        return kToolbarHeight;
      case AkAppBarSize.large:
        return 72;
    }
  }

  /// الحصول على حجم الخط للعنوان حسب الحجم
  double _getTitleFontSize() {
    switch (size) {
      case AkAppBarSize.small:
        return AppDimensions.mediumFontSize;
      case AkAppBarSize.medium:
        return AppDimensions.titleFontSize;
      case AkAppBarSize.large:
        return AppDimensions.largeFontSize;
    }
  }

  /// الحصول على حجم الأيقونات حسب الحجم
  double _getIconSize() {
    switch (size) {
      case AkAppBarSize.small:
        return AppDimensions.smallIconSize;
      case AkAppBarSize.medium:
        return AppDimensions.mediumIconSize;
      case AkAppBarSize.large:
        return AppDimensions.largeIconSize;
    }
  }

  /// الحصول على ارتفاع الظل حسب الحجم
  double _getElevation() {
    switch (size) {
      case AkAppBarSize.small:
        return 1;
      case AkAppBarSize.medium:
        return 2;
      case AkAppBarSize.large:
        return 4;
    }
  }

  @override
  Size get preferredSize => Size.fromHeight(
        _getToolbarHeight() + (bottom?.preferredSize.height ?? 0.0),
      );
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● 2. شريط التطبيق مع البحث (AkSearchAppBar)
// ═══════════════════════════════════════════════════════════════════════════════

/// شريط التطبيق مع البحث المدمج
///
/// **المميزات:**
/// - بحث مدمج مع تأثيرات انتقالية
/// - دعم البحث الفوري والمؤجل
/// - تصميم متناسق مع باقي الأشرطة
/// - دعم كامل للوضع المظلم/الفاتح
///
/// **مثال الاستخدام:**
/// ```dart
/// AkSearchAppBar(
///   title: 'البحث في المنتجات',
///   onSearch: (query) => searchProducts(query),
///   searchHint: 'ابحث عن منتج...',
/// )
/// ```
class AkSearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  /// عنوان شريط التطبيق
  final String title;

  /// دالة البحث
  final Function(String)? onSearch;

  /// نص تلميح البحث
  final String? searchHint;

  /// قائمة الإجراءات الإضافية
  final List<Widget>? actions;

  /// هل يتم عرض زر الرجوع
  final bool showBackButton;

  /// دالة عند الضغط على زر الرجوع
  final VoidCallback? onBackPressed;

  /// لون الخلفية المخصص
  final Color? backgroundColor;

  /// لون النص والأيقونات المخصص
  final Color? foregroundColor;

  /// حجم شريط التطبيق
  final AkAppBarSize size;

  /// هل البحث نشط افتراضياً
  final bool initialSearchActive;

  /// مدة التأخير للبحث الفوري (بالميلي ثانية)
  final int searchDelay;

  const AkSearchAppBar({
    super.key,
    required this.title,
    this.onSearch,
    this.searchHint,
    this.actions,
    this.showBackButton = true,
    this.onBackPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.size = AkAppBarSize.medium,
    this.initialSearchActive = false,
    this.searchDelay = 500,
  });

  @override
  State<AkSearchAppBar> createState() => _AkSearchAppBarState();

  @override
  Size get preferredSize => Size.fromHeight(_getToolbarHeight());

  /// الحصول على ارتفاع شريط الأدوات حسب الحجم
  double _getToolbarHeight() {
    switch (size) {
      case AkAppBarSize.small:
        return 48;
      case AkAppBarSize.medium:
        return kToolbarHeight;
      case AkAppBarSize.large:
        return 72;
    }
  }
}

class _AkSearchAppBarState extends State<AkSearchAppBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late TextEditingController _searchController;
  late FocusNode _searchFocusNode;
  bool _isSearchActive = false;
  Timer? _searchTimer;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _searchController = TextEditingController();
    _searchFocusNode = FocusNode();
    _isSearchActive = widget.initialSearchActive;

    if (_isSearchActive) {
      _animationController.forward();
    }

    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    _searchTimer?.cancel();
    super.dispose();
  }

  /// تفعيل البحث
  void _activateSearch() {
    setState(() {
      _isSearchActive = true;
    });
    _animationController.forward();
    _searchFocusNode.requestFocus();
  }

  /// إلغاء البحث
  void _deactivateSearch() {
    setState(() {
      _isSearchActive = false;
    });
    _animationController.reverse();
    _searchController.clear();
    _searchFocusNode.unfocus();
    widget.onSearch?.call('');
  }

  /// معالجة تغيير نص البحث
  void _onSearchChanged() {
    _searchTimer?.cancel();
    _searchTimer = Timer(Duration(milliseconds: widget.searchDelay), () {
      widget.onSearch?.call(_searchController.text);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // تحديد الألوان الذكية
    final effectiveBackgroundColor = widget.backgroundColor ??
        (isDark ? AppColors.darkSurface : AppColors.lightSurface);
    final effectiveForegroundColor = widget.foregroundColor ??
        AppColors.getTextColorForBackground(effectiveBackgroundColor);

    return AppBar(
      title: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return _isSearchActive
              ? _buildAkSearchInput(effectiveForegroundColor, isDark)
              : Text(
                  widget.title,
                  style: AppTypography.createCustomStyle(
                    fontSize: _getTitleFontSize(),
                    fontWeight: AppTypography.weightSemiBold,
                    color: effectiveForegroundColor,
                  ),
                );
        },
      ),
      backgroundColor: effectiveBackgroundColor,
      foregroundColor: effectiveForegroundColor,
      elevation: _getElevation(),
      toolbarHeight: widget._getToolbarHeight(),
      centerTitle: !_isSearchActive,

      // زر الرجوع أو إلغاء البحث
      leading: _isSearchActive
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: _deactivateSearch,
              color: effectiveForegroundColor,
              tooltip: 'إلغاء البحث',
            )
          : (widget.showBackButton && Navigator.of(context).canPop())
              ? IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed:
                      widget.onBackPressed ?? () => Navigator.of(context).pop(),
                  color: effectiveForegroundColor,
                  tooltip: 'رجوع',
                )
              : null,

      // الإجراءات
      actions: _isSearchActive
          ? [
              if (_searchController.text.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    widget.onSearch?.call('');
                  },
                  color: effectiveForegroundColor,
                  tooltip: 'مسح البحث',
                ),
            ]
          : [
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: _activateSearch,
                color: effectiveForegroundColor,
                tooltip: 'بحث',
              ),
              ...?widget.actions,
            ],

      iconTheme: IconThemeData(
        color: effectiveForegroundColor,
        size: _getIconSize(),
      ),
    );
  }

  /// بناء حقل البحث
  Widget _buildAkSearchInput(Color foregroundColor, bool isDark) {
    return TextField(
      controller: _searchController,
      focusNode: _searchFocusNode,
      style: AppTypography.createCustomStyle(
        fontSize: _getTitleFontSize(),
        color: foregroundColor,
      ),
      decoration: InputDecoration(
        hintText: widget.searchHint ?? 'بحث...',
        hintStyle: AppTypography.createCustomStyle(
          fontSize: _getTitleFontSize(),
          color: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
        ),
        border: InputBorder.none,
        contentPadding:
            EdgeInsets.symmetric(vertical: AppDimensions.smallSpacing),
      ),
      textInputAction: TextInputAction.search,
      onSubmitted: (value) => widget.onSearch?.call(value),
    );
  }

  /// الحصول على حجم الخط للعنوان حسب الحجم
  double _getTitleFontSize() {
    switch (widget.size) {
      case AkAppBarSize.small:
        return AppDimensions.mediumFontSize;
      case AkAppBarSize.medium:
        return AppDimensions.titleFontSize;
      case AkAppBarSize.large:
        return AppDimensions.largeFontSize;
    }
  }

  /// الحصول على حجم الأيقونات حسب الحجم
  double _getIconSize() {
    switch (widget.size) {
      case AkAppBarSize.small:
        return AppDimensions.smallIconSize;
      case AkAppBarSize.medium:
        return AppDimensions.mediumIconSize;
      case AkAppBarSize.large:
        return AppDimensions.largeIconSize;
    }
  }

  /// الحصول على ارتفاع الظل حسب الحجم
  double _getElevation() {
    switch (widget.size) {
      case AkAppBarSize.small:
        return 1;
      case AkAppBarSize.medium:
        return 2;
      case AkAppBarSize.large:
        return 4;
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// ● الدوال المساعدة السريعة (AkAppBars)
// ═══════════════════════════════════════════════════════════════════════════════

/// فئة الدوال المساعدة السريعة لأشرطة التطبيق
/// توفر طرق سريعة لإنشاء أشرطة التطبيق الشائعة
class AkAppBars {
  AkAppBars._();

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال أشرطة التطبيق السريعة
  // ───────────────────────────────────────────────────────────────────────────────

  /// شريط لوحة التحكم الرئيسية
  static PreferredSizeWidget dashboard({
    VoidCallback? onMenuPressed,
    List<Widget>? actions,
  }) {
    return AkAppBar(
      title: 'لوحة التحكم',
      showBackButton: false,
      onMenuPressed: onMenuPressed,
      actions: actions ??
          [
            IconButton(
              icon: const Icon(Icons.notifications),
              onPressed: () {},
              tooltip: 'الإشعارات',
            ),
            IconButton(
              icon: const Icon(Icons.account_circle),
              onPressed: () {},
              tooltip: 'الملف الشخصي',
            ),
          ],
      size: AkAppBarSize.large,
    );
  }

  /// شريط إدارة المنتجات
  static PreferredSizeWidget products({
    VoidCallback? onBackPressed,
    VoidCallback? onAddProduct,
    VoidCallback? onSearch,
  }) {
    return AkAppBar(
      title: 'إدارة المنتجات',
      onBackPressed: onBackPressed,
      actions: [
        if (onSearch != null)
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: onSearch,
            tooltip: 'بحث في المنتجات',
          ),
        if (onAddProduct != null)
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: onAddProduct,
            tooltip: 'إضافة منتج جديد',
          ),
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () {},
          tooltip: 'المزيد',
        ),
      ],
    );
  }

  /// شريط المبيعات
  static PreferredSizeWidget sales({
    VoidCallback? onBackPressed,
    VoidCallback? onNewSale,
    VoidCallback? onReports,
  }) {
    return AkAppBar(
      title: 'المبيعات',
      onBackPressed: onBackPressed,
      actions: [
        if (onNewSale != null)
          IconButton(
            icon: const Icon(Icons.add_shopping_cart),
            onPressed: onNewSale,
            tooltip: 'بيع جديد',
          ),
        if (onReports != null)
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: onReports,
            tooltip: 'تقارير المبيعات',
          ),
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () {},
          tooltip: 'المزيد',
        ),
      ],
    );
  }

  /// شريط نقطة البيع (POS)
  static PreferredSizeWidget pos({
    VoidCallback? onBackPressed,
    VoidCallback? onCustomers,
    VoidCallback? onSettings,
  }) {
    return AkAppBar(
      title: 'نقطة البيع',
      onBackPressed: onBackPressed,
      actions: [
        if (onCustomers != null)
          IconButton(
            icon: const Icon(Icons.people),
            onPressed: onCustomers,
            tooltip: 'العملاء',
          ),
        if (onSettings != null)
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: onSettings,
            tooltip: 'إعدادات نقطة البيع',
          ),
      ],
      size: AkAppBarSize.large,
    );
  }

  /// شريط التقارير
  static PreferredSizeWidget reports({
    VoidCallback? onBackPressed,
    VoidCallback? onExport,
    VoidCallback? onFilter,
  }) {
    return AkAppBar(
      title: 'التقارير',
      onBackPressed: onBackPressed,
      actions: [
        if (onFilter != null)
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: onFilter,
            tooltip: 'تصفية التقارير',
          ),
        if (onExport != null)
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: onExport,
            tooltip: 'تصدير التقرير',
          ),
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () {},
          tooltip: 'المزيد',
        ),
      ],
    );
  }

  /// شريط البحث في المنتجات
  static PreferredSizeWidget productSearch({
    required Function(String) onSearch,
    VoidCallback? onBackPressed,
  }) {
    return AkSearchAppBar(
      title: 'البحث في المنتجات',
      searchHint: 'ابحث عن منتج بالاسم أو الباركود...',
      onSearch: onSearch,
      onBackPressed: onBackPressed,
      initialSearchActive: true,
    );
  }

  /// شريط البحث في العملاء
  static PreferredSizeWidget customerSearch({
    required Function(String) onSearch,
    VoidCallback? onBackPressed,
  }) {
    return AkSearchAppBar(
      title: 'البحث في العملاء',
      searchHint: 'ابحث عن عميل بالاسم أو الهاتف...',
      onSearch: onSearch,
      onBackPressed: onBackPressed,
      initialSearchActive: true,
    );
  }

  /// شريط الإعدادات
  static PreferredSizeWidget settings({
    VoidCallback? onBackPressed,
    VoidCallback? onSave,
  }) {
    return AkAppBar(
      title: 'الإعدادات',
      onBackPressed: onBackPressed,
      actions: [
        if (onSave != null)
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: onSave,
            tooltip: 'حفظ الإعدادات',
          ),
      ],
    );
  }

  /// شريط الملف الشخصي
  static PreferredSizeWidget profile({
    VoidCallback? onBackPressed,
    VoidCallback? onEdit,
  }) {
    return AkAppBar(
      title: 'الملف الشخصي',
      onBackPressed: onBackPressed,
      actions: [
        if (onEdit != null)
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: onEdit,
            tooltip: 'تعديل الملف الشخصي',
          ),
      ],
    );
  }

  // ───────────────────────────────────────────────────────────────────────────────
  // ● دوال متخصصة للمشروع التجاري
  // ───────────────────────────────────────────────────────────────────────────────

  /// شريط إدارة المخزون
  static PreferredSizeWidget inventory({
    VoidCallback? onBackPressed,
    VoidCallback? onAddStock,
    VoidCallback? onLowStock,
  }) {
    return AkAppBar(
      title: 'إدارة المخزون',
      onBackPressed: onBackPressed,
      actions: [
        if (onLowStock != null)
          IconButton(
            icon: const Icon(Icons.warning),
            onPressed: onLowStock,
            tooltip: 'المنتجات منخفضة المخزون',
          ),
        if (onAddStock != null)
          IconButton(
            icon: const Icon(Icons.add_box),
            onPressed: onAddStock,
            tooltip: 'إضافة مخزون',
          ),
      ],
    );
  }

  /// شريط إدارة العملاء
  static PreferredSizeWidget customers({
    VoidCallback? onBackPressed,
    VoidCallback? onAddCustomer,
    VoidCallback? onSearch,
  }) {
    return AkAppBar(
      title: 'إدارة العملاء',
      onBackPressed: onBackPressed,
      actions: [
        if (onSearch != null)
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: onSearch,
            tooltip: 'بحث في العملاء',
          ),
        if (onAddCustomer != null)
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: onAddCustomer,
            tooltip: 'إضافة عميل جديد',
          ),
      ],
    );
  }

  /// شريط المشتريات
  static PreferredSizeWidget purchases({
    VoidCallback? onBackPressed,
    VoidCallback? onNewPurchase,
    VoidCallback? onSuppliers,
  }) {
    return AkAppBar(
      title: 'المشتريات',
      onBackPressed: onBackPressed,
      actions: [
        if (onSuppliers != null)
          IconButton(
            icon: const Icon(Icons.business),
            onPressed: onSuppliers,
            tooltip: 'الموردين',
          ),
        if (onNewPurchase != null)
          IconButton(
            icon: const Icon(Icons.add_shopping_cart),
            onPressed: onNewPurchase,
            tooltip: 'شراء جديد',
          ),
      ],
    );
  }
}
