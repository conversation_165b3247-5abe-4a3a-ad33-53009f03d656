# 🎨 أزرار AppBar المخصصة (AK AppBar Buttons)

نظام أزرار AppBar مخصص ومتطور لتطبيق تاجر بلس، مصمم خصيصاً للتكيف مع الوضع المظلم/الفاتح وتوفير تجربة مستخدم متناسقة.

## 🎯 **المميزات الرئيسية**

- ✅ **تكيف تلقائي مع الثيمات** - ألوان ديناميكية تتغير حسب الوضع المظلم/الفاتح
- ✅ **أزرار ذكية** - تبديل تلقائي للأيقونات حسب الحالة
- ✅ **تصميم موحد** - نفس التصميم والتأثيرات في جميع أنحاء التطبيق
- ✅ **تلميحات واضحة** - نصوص مساعدة باللغة العربية
- ✅ **أداء محسن** - تحميل كسول وتأثيرات سلسة
- ✅ **سهولة الاستخدام** - API بسيط ومفهوم

## 📱 **الأزرار المتوفرة**

### 🔍 **1. زر البحث الذكي (AkAppBarSearchButton)**
زر يتبدل تلقائياً بين أيقونة البحث والإغلاق حسب حالة البحث.

```dart
AkAppBarSearchButton(
  isSearchActive: _showSearchField,
  onPressed: () => toggleSearch(),
)
```

**المميزات:**
- تبديل تلقائي للأيقونة (🔍 ↔ ✖️)
- تلميحات ديناميكية ("البحث" / "إغلاق البحث")
- ألوان تتكيف مع الثيم

### 🔄 **2. زر تبديل العرض (AkAppBarViewToggleButton)**
زر لتبديل طريقة العرض بين القائمة والشبكة.

```dart
AkAppBarViewToggleButton(
  isGridView: _isGridView,
  onPressed: () => toggleViewMode(),
)
```

**المميزات:**
- تبديل تلقائي للأيقونة (📋 ↔ ⊞)
- تلميحات واضحة ("عرض كقائمة" / "عرض كشبكة")
- حفظ تلقائي لتفضيل المستخدم

### 🎛️ **3. زر أيقونة عام (AkAppBarIconButton)**
زر أيقونة أساسي مع تكيف كامل للثيمات.

```dart
AkAppBarIconButton(
  icon: Icons.settings,
  onPressed: () => openSettings(),
  tooltip: 'الإعدادات',
)
```

**المميزات:**
- ألوان ديناميكية تلقائية
- أحجام قابلة للتخصيص
- تأثيرات تفاعلية محسنة

## 🏭 **الأزرار الجاهزة (AkAppBarButtons)**

مجموعة من الأزرار المحددة مسبقاً للاستخدامات الشائعة:

### **زر الفلترة:**
```dart
AkAppBarButtons.filter(
  onPressed: () => showFilterDialog(),
)
```

### **زر إدارة الفئات:**
```dart
AkAppBarButtons.categories(
  onPressed: () => showCategoriesDialog(),
)
```

### **زر التقارير:**
```dart
AkAppBarButtons.reports(
  onPressed: () => showReportsScreen(),
)
```

### **زر الإعدادات:**
```dart
AkAppBarButtons.settings(
  onPressed: () => openSettings(),
)
```

### **زر الإشعارات:**
```dart
AkAppBarButtons.notifications(
  onPressed: () => showNotifications(),
  badgeCount: 5, // عدد الإشعارات الجديدة
)
```

### **زر التحديث:**
```dart
AkAppBarButtons.refresh(
  onPressed: () => refreshData(),
)
```

### **زر المساعدة:**
```dart
AkAppBarButtons.help(
  onPressed: () => showHelp(),
)
```

### **زر المزيد:**
```dart
AkAppBarButtons.more(
  onPressed: () => showMoreOptions(),
)
```

## 🎨 **التخصيص المتقدم**

### **ألوان مخصصة:**
```dart
AkAppBarIconButton(
  icon: Icons.star,
  onPressed: () => addToFavorites(),
  color: Colors.amber, // لون مخصص
  tooltip: 'إضافة للمفضلة',
)
```

### **أحجام مخصصة:**
```dart
AkAppBarIconButton(
  icon: Icons.notifications,
  onPressed: () => showNotifications(),
  size: 28.0, // حجم مخصص
)
```

## 📋 **مثال شامل للاستخدام**

```dart
class ProductsScreen extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AkAppBar(
        title: 'المنتجات',
        actions: [
          // زر البحث الذكي
          AkAppBarSearchButton(
            isSearchActive: _showSearchField,
            onPressed: _toggleSearch,
          ),
          
          // زر الفلترة
          AkAppBarButtons.filter(
            onPressed: _showFilterDialog,
          ),
          
          // زر تبديل العرض
          AkAppBarViewToggleButton(
            isGridView: _isGridView,
            onPressed: _toggleViewMode,
          ),
          
          // زر إدارة الفئات
          AkAppBarButtons.categories(
            onPressed: _showCategoriesDialog,
          ),
          
          // زر التقارير
          AkAppBarButtons.reports(
            onPressed: () => Navigator.pushNamed(context, '/reports'),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }
}
```

## 🔄 **التكامل مع الأنظمة الأخرى**

### **مع نظام الثيمات:**
- الأزرار تتكيف تلقائياً مع `DynamicColors`
- دعم كامل للوضع المظلم/الفاتح
- ألوان متناسقة مع باقي التطبيق

### **مع نظام التنقل:**
- تكامل سلس مع `AkAppBar`
- دعم التلميحات والإرشادات
- تأثيرات انتقالية محسنة

### **مع نظام الحالة:**
- حفظ تلقائي لحالة الأزرار
- استرجاع التفضيلات عند إعادة فتح التطبيق
- تزامن مع `SharedPreferences`

## 🎯 **أفضل الممارسات**

### **ترتيب الأزرار:**
1. **البحث** - دائماً في المقدمة
2. **الفلترة** - بعد البحث مباشرة
3. **تبديل العرض** - للشاشات التي تدعم عدة أنماط عرض
4. **الإجراءات المتخصصة** - حسب السياق
5. **المزيد** - دائماً في النهاية

### **الألوان:**
- استخدم الألوان الافتراضية للتناسق
- استخدم ألوان مخصصة فقط للإجراءات المهمة
- تجنب الألوان الزاهية في AppBar

### **التلميحات:**
- استخدم نصوص واضحة ومختصرة
- تأكد من الترجمة الصحيحة للعربية
- اجعل التلميحات وصفية للإجراء

## 🚀 **الترقيات المستقبلية**

- [ ] دعم الرسوم المتحركة المتقدمة
- [ ] أزرار مع عدادات (Badges)
- [ ] دعم الإيماءات (Gestures)
- [ ] تخصيص متقدم للألوان
- [ ] دعم الأيقونات المخصصة

---

**تم تطوير هذا النظام خصيصاً لتطبيق تاجر بلس لضمان تجربة مستخدم متناسقة ومتطورة.**
