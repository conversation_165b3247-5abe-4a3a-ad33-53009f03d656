import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/utils/error_tracker.dart';

void main() {
  group('ErrorTracker Tests', () {
    setUp(() {
      // تنظيف السجل قبل كل اختبار
      ErrorTracker.clearHistory();
    });

    test('يقوم بتسجيل واسترجاع الأخطاء بشكل صحيح', () {
      // تسجيل خطأ
      ErrorTracker.captureError(
        "خطأ تجريبي",
        error: Exception("هذا خطأ للاختبار"),
        stackTrace: StackTrace.current,
      );

      // التحقق من تسجيل الخطأ
      final errors = ErrorTracker.getRecentErrors();
      expect(errors.length, 1);
      expect(errors[0].message, "خطأ تجريبي");
      expect(errors[0].error.toString(), contains("هذا خطأ للاختبار"));
    });

    test('يحتفظ بالحد الأقصى من الأخطاء', () {
      // تسجيل عدد كبير من الأخطاء
      for (int i = 0; i < 150; i++) {
        ErrorTracker.captureError(
          "خطأ رقم $i",
          error: Exception("خطأ للاختبار رقم $i"),
          stackTrace: StackTrace.current,
        );
      }

      // التحقق من أن عدد الأخطاء لا يتجاوز الحد الأقصى (100)
      final errors = ErrorTracker.getRecentErrors();
      expect(errors.length, 100);

      // التحقق من أن الأخطاء الأحدث هي المحفوظة
      expect(errors[0].message, "خطأ رقم 149");
      expect(errors[99].message, "خطأ رقم 50");
    });

    test('يقوم بحساب إحصائيات الأخطاء بشكل صحيح', () {
      // تسجيل أنواع مختلفة من الأخطاء
      for (int i = 0; i < 5; i++) {
        ErrorTracker.captureError(
          "خطأ من النوع A",
          error: ArgumentError("خطأ من النوع A"),
          stackTrace: StackTrace.current,
        );
      }

      for (int i = 0; i < 3; i++) {
        ErrorTracker.captureError(
          "خطأ من النوع B",
          error: const FormatException("خطأ من النوع B"),
          stackTrace: StackTrace.current,
        );
      }

      // التحقق من الإحصائيات
      final stats = ErrorTracker.getErrorStats();
      expect(stats['totalErrors'], 8);
      expect(stats['mostCommonError'], 'ArgumentError');
      expect(stats['mostCommonErrorCount'], 5);
    });

    test('يقوم بمسح سجل الأخطاء بشكل صحيح', () {
      // تسجيل بعض الأخطاء
      ErrorTracker.captureError(
        "خطأ تجريبي",
        error: Exception("هذا خطأ للاختبار"),
        stackTrace: StackTrace.current,
      );

      // التحقق من وجود الأخطاء
      expect(ErrorTracker.getRecentErrors().length, 1);

      // مسح السجل
      ErrorTracker.clearHistory();

      // التحقق من خلو السجل
      expect(ErrorTracker.getRecentErrors().length, 0);

      // التحقق من الإحصائيات بعد المسح
      final stats = ErrorTracker.getErrorStats();
      expect(stats['totalErrors'], 0);
      expect(stats['mostCommonError'], null);
    });
  });
}
