import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';

/// مكون لعرض الحسابات في شكل شجرة هرمية
class AccountTreeWidget extends StatefulWidget {
  /// قائمة الحسابات للعرض في الشجرة
  final List<Map<String, dynamic>> accounts;

  /// معالج حدث تحديد الحساب
  final Function(Map<String, dynamic>) onSelect;

  const AccountTreeWidget({
    Key? key,
    required this.accounts,
    required this.onSelect,
  }) : super(key: key);

  @override
  State<AccountTreeWidget> createState() => _AccountTreeWidgetState();
}

class _AccountTreeWidgetState extends State<AccountTreeWidget> {
  // قائمة بمعرفات الحسابات المفتوحة
  final Set<String> _expandedNodeIds = {};

  @override
  Widget build(BuildContext context) {
    // بناء الشجرة الهرمية
    List<Map<String, dynamic>> rootAccounts = widget.accounts
        .where((account) =>
            account['parent_id'] == null ||
            account['parent_id'] == 0 ||
            account['parent_id'] == '')
        .toList();

    // تحقق من وجود حسابات جذرية
    if (rootAccounts.isEmpty && widget.accounts.isNotEmpty) {
      // لا توجد حسابات جذرية رغم وجود حسابات
      // قد يكون هناك مشكلة في هيكل البيانات
    }

    return rootAccounts.isEmpty
        ? const Center(child: Text('لا توجد حسابات للعرض'))
        : SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: rootAccounts
                  .map((account) => _buildAccountNode(account, 0))
                  .toList(),
            ),
          );
  }

  Widget _buildAccountNode(Map<String, dynamic> account, int level) {
    // تحويل معرف الحساب إلى سلسلة نصية لضمان المقارنة الصحيحة
    final accountId = account['id'].toString();

    // معلومات الحساب الحالي
    // accountId: $accountId, name: ${account['name']}, type: ${account['type'] ?? account['account_type']}

    final hasChildren =
        widget.accounts.any((a) => a['parent_id'].toString() == accountId);
    final isExpanded = _expandedNodeIds.contains(accountId);

    // الحصول على الحسابات الفرعية
    List<Map<String, dynamic>> childAccounts = hasChildren
        ? widget.accounts
            .where((a) => a['parent_id'].toString() == accountId)
            .toList()
        : [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: level * 24.0),
          child: Row(
            children: [
              if (hasChildren)
                IconButton(
                  icon: Icon(
                    isExpanded ? Icons.expand_more : Icons.chevron_right,
                    size: 20,
                  ),
                  onPressed: () {
                    setState(() {
                      if (isExpanded) {
                        _expandedNodeIds.remove(accountId);
                      } else {
                        _expandedNodeIds.add(accountId);
                      }
                    });
                  },
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  iconSize: 20,
                )
              else
                const SizedBox(width: 20),
              const SizedBox(width: 4),
              _getAccountTypeIcon(account['type'] as String? ??
                  account['account_type'] as String? ??
                  ''),
              const SizedBox(width: 8),
              InkWell(
                onTap: () => widget.onSelect(account),
                child: Text(
                  '${account['code'] ?? ''} - ${account['name'] ?? ''}',
                  style: AppTypography(
                    fontWeight:
                        level == 0 ? FontWeight.bold : FontWeight.normal,
                    color: account['is_active'] == 0
                        ? AppColors.lightTextSecondary
                        : null,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                _formatBalance(account['current_balance'] ??
                    account['opening_balance'] ??
                    0),
                style: AppTypography(
                  color: _getBalanceColor(
                      account['current_balance'] ??
                          account['opening_balance'] ??
                          0,
                      account['type'] as String? ??
                          account['account_type'] as String? ??
                          ''),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        if (isExpanded && hasChildren)
          ...childAccounts
              .map((childAccount) => _buildAccountNode(childAccount, level + 1))
              .toList(),
      ],
    );
  }

  Widget _getAccountTypeIcon(String accountType) {
    IconData iconData;
    Color iconColor;

    switch (accountType) {
      case 'asset':
        iconData = Icons.account_balance;
        iconColor = AppColors.info;
        break;
      case 'liability':
        iconData = Icons.money_off;
        iconColor = AppColors.error;
        break;
      case 'equity':
        iconData = Icons.pie_chart;
        iconColor = AppColors.accent;
        break;
      case 'revenue':
        iconData = Icons.trending_up;
        iconColor = AppColors.success;
        break;
      case 'expense':
        iconData = Icons.trending_down;
        iconColor = AppColors.warning;
        break;
      default:
        iconData = Icons.folder;
        iconColor = AppColors.lightTextSecondary;
    }

    return Icon(iconData, size: 16, color: iconColor);
  }

  Color _getBalanceColor(dynamic balance, String accountType) {
    if (balance == null) return AppColors.lightTextSecondary;

    double value;
    if (balance is int) {
      value = balance.toDouble();
    } else if (balance is double) {
      value = balance;
    } else if (balance is String) {
      value = double.tryParse(balance) ?? 0.0;
    } else {
      value = 0.0;
    }

    if (value == 0) return AppColors.lightTextSecondary;

    // الألوان حسب نوع الحساب
    switch (accountType) {
      case 'asset':
      case 'expense':
        return value > 0 ? AppColors.success : AppColors.error;
      case 'liability':
      case 'equity':
      case 'revenue':
        return value > 0 ? AppColors.info : AppColors.warning;
      default:
        return value > 0 ? AppColors.success : AppColors.error;
    }
  }

  String _formatBalance(dynamic balance) {
    if (balance == null) return '0.00';

    double value;
    if (balance is int) {
      value = balance.toDouble();
    } else if (balance is double) {
      value = balance;
    } else if (balance is String) {
      value = double.tryParse(balance) ?? 0.0;
    } else {
      value = 0.0;
    }

    return value.toStringAsFixed(2);
  }
}
