import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';
import '../screens/simplified_access_management_screen.dart';
import '../screens/user_access_screen.dart';
import '../screens/access_levels_comparison_screen.dart';
import '../screens/access_quick_guide_screen.dart';
import '../screens/access_faq_screen.dart';

/// القائمة الجانبية لإدارة الوصول
class AccessManagementDrawer extends StatelessWidget {
  const AccessManagementDrawer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // رأس القائمة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: AppColors.lightTextSecondary,
            ),
            child: const SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 16),
                  Icon(
                    Icons.security,
                    color: AppColors.lightTextSecondary,
                    size: 48,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'إدارة الوصول',
                    style: AppTypography(
                      color: AppColors.lightTextSecondary,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'تحكم في وصول المستخدمين إلى النظام',
                    style: AppTypography(
                      color: AppColors.lightTextSecondary,
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(height: 16),
                ],
              ),
            ),
          ),

          // عناصر القائمة
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // مستويات الوصول
                _buildDrawerItem(
                  context: context,
                  icon: Icons.admin_panel_settings,
                  title: 'مستويات الوصول',
                  subtitle: 'إدارة مستويات الوصول المختلفة',
                  route: SimplifiedAccessManagementScreen.routeName,
                ),

                // وصول المستخدمين
                _buildDrawerItem(
                  context: context,
                  icon: Icons.people,
                  title: 'وصول المستخدمين',
                  subtitle: 'تعيين مستويات الوصول للمستخدمين',
                  route: UserAccessScreen.routeName,
                ),

                // مقارنة مستويات الوصول
                _buildDrawerItem(
                  context: context,
                  icon: Icons.compare_arrows,
                  title: 'مقارنة مستويات الوصول',
                  subtitle: 'مقارنة بين مستويات الوصول المختلفة',
                  route: AccessLevelsComparisonScreen.routeName,
                ),

                const Divider(),

                // الدليل السريع
                _buildDrawerItem(
                  context: context,
                  icon: Icons.help_outline,
                  title: 'الدليل السريع',
                  subtitle: 'دليل سريع لاستخدام نظام إدارة الوصول',
                  route: AccessQuickGuideScreen.routeName,
                ),

                // الأسئلة الشائعة
                _buildDrawerItem(
                  context: context,
                  icon: Icons.question_answer,
                  title: 'الأسئلة الشائعة',
                  subtitle: 'إجابات على الأسئلة الشائعة',
                  route: AccessFAQScreen.routeName,
                ),

                const Divider(),

                // العودة إلى الرئيسية
                ListTile(
                  leading: const Icon(Icons.home),
                  title: const Text('العودة إلى الرئيسية'),
                  onTap: () {
                    Navigator.of(context).pushNamedAndRemoveUntil(
                      '/dashboard',
                      (route) => false,
                    );
                  },
                ),
              ],
            ),
          ),

          // معلومات الإصدار
          Container(
            padding: const EdgeInsets.all(16),
            child: const Text(
              'نظام إدارة الوصول - الإصدار 1.0',
              style: AppTypography(
                color: AppColors.lightTextSecondary,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر القائمة
  Widget _buildDrawerItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required String route,
  }) {
    // التحقق مما إذا كان العنصر الحالي هو الشاشة النشطة
    final isActive = ModalRoute.of(context)?.settings.name == route;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isActive
            ? AppColors.primary.withValues(alpha: 0.12)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isActive ? AppColors.primary : null,
        ),
        title: Text(
          title,
          style: AppTypography(
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            color: isActive ? AppColors.primary : null,
          ),
        ),
        subtitle: Text(subtitle),
        onTap: () {
          if (!isActive) {
            Navigator.of(context).pushReplacementNamed(route);
          } else {
            Navigator.of(context).pop();
          }
        },
      ),
    );
  }
}
