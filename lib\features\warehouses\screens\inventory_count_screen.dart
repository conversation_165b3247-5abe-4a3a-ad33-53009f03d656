import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/models/warehouse.dart';
import '../../../core/theme/index.dart';
import '../../../core/widgets/safe_layout.dart';
import '../../../core/widgets/index.dart';
import '../presenters/warehouse_presenter.dart';
import '../../products/presenters/product_presenter.dart';
import '../../../core/providers/app_providers.dart'; // للتحميل الكسول

/// شاشة جرد المخزون
class InventoryCountScreen extends StatefulWidget {
  const InventoryCountScreen({Key? key}) : super(key: key);

  @override
  State<InventoryCountScreen> createState() => _InventoryCountScreenState();
}

class _InventoryCountScreenState extends State<InventoryCountScreen> {
  // المقدمون
  late WarehousePresenter _warehousePresenter;
  late ProductPresenter _productPresenter;

  // المستودع المحدد
  Warehouse? _selectedWarehouse;

  // تاريخ الجرد
  DateTime _countDate = DateTime.now();

  // حالة التحميل
  bool _isLoading = false;

  // بيانات المخزون الحالي
  List<Map<String, dynamic>> _currentStock = [];

  // بيانات الجرد
  List<Map<String, dynamic>> _countItems = [];

  // حالة البحث
  final TextEditingController _searchController = TextEditingController();

  // حالة الجرد
  bool _isCountStarted = false;

  // رقم الجرد
  String _countNumber = '';

  // ملاحظات الجرد
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // 🚀 تهيئة المقدمين باستخدام التحميل الكسول
    _warehousePresenter = AppProviders.getLazyPresenter<WarehousePresenter>(
      () => WarehousePresenter(),
    );
    _productPresenter = AppProviders.getLazyPresenter<ProductPresenter>(
      () => ProductPresenter(),
    );

    // تحميل البيانات
    _loadData();

    // توليد رقم الجرد
    _generateCountNumber();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل المستودعات
      await _warehousePresenter.loadWarehouses();

      // تحميل المنتجات
      await _productPresenter.loadProducts();

      // إذا كان هناك مستودع واحد فقط، يتم اختياره تلقائياً
      if (_warehousePresenter.warehouses.length == 1) {
        setState(() {
          _selectedWarehouse = _warehousePresenter.warehouses.first;
        });

        // تحميل المخزون الحالي
        await _loadCurrentStock();
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل المخزون الحالي
  Future<void> _loadCurrentStock() async {
    if (_selectedWarehouse == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار مستودع أولاً'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // في الإصدار الكامل، سيتم استدعاء الدالة الفعلية
      // final stockData = await _inventoryPresenter.getCurrentStock(
      //   warehouseId: _selectedWarehouse!.id,
      // );

      // بيانات تجريبية للعرض
      final stockData = [
        {
          'product_id': '1',
          'product_name': 'منتج 1',
          'product_code': 'SKU001',
          'product_barcode': 'BARCODE001',
          'quantity': 10.0,
        },
        {
          'product_id': '2',
          'product_name': 'منتج 2',
          'product_code': 'SKU002',
          'product_barcode': 'BARCODE002',
          'quantity': 20.0,
        },
        {
          'product_id': '3',
          'product_name': 'منتج 3',
          'product_code': 'SKU003',
          'product_barcode': 'BARCODE003',
          'quantity': 30.0,
        },
      ];

      setState(() {
        _currentStock = stockData;

        // إنشاء عناصر الجرد من المخزون الحالي
        _countItems = stockData.map((item) {
          // الحصول على المنتج من المقدم
          final productId = item['product_id'] as String?;
          final product = productId != null
              ? _productPresenter.getProductById(productId)
              : null;

          return {
            'product_id': item['product_id'],
            'product_name': product != null
                ? product.name
                : (item['product_name'] ?? 'غير معروف'),
            'product_code':
                product != null ? product.sku : (item['product_code'] ?? ''),
            'product_barcode': product != null
                ? product.barcode
                : (item['product_barcode'] ?? ''),
            'system_quantity': item['quantity'] ?? 0.0,
            'counted_quantity': 0.0,
            'difference': 0.0,
            'unit_name': product != null && product.unitId != null
                ? _productPresenter.getUnitName(product.unitId!)
                : '',
            'notes': '',
            'is_counted': false,
          };
        }).toList();
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحميل المخزون الحالي: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// توليد رقم الجرد
  void _generateCountNumber() {
    final now = DateTime.now();
    final dateStr = DateFormat('yyyyMMdd').format(now);
    final randomStr = (1000 + now.millisecond).toString().substring(1);

    setState(() {
      _countNumber = 'COUNT-$dateStr-$randomStr';
    });
  }

  /// بدء الجرد
  void _startCount() {
    if (_selectedWarehouse == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار مستودع أولاً'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isCountStarted = true;
    });
  }

  /// إنهاء الجرد
  void _completeCount() {
    // التحقق من أن جميع العناصر تم جردها
    final uncountedItems =
        _countItems.where((item) => !item['is_counted']).toList();

    if (uncountedItems.isNotEmpty) {
      showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: const Text('تأكيد إنهاء الجرد'),
            content: Text(
              'هناك ${uncountedItems.length} عنصر لم يتم جرده بعد. هل تريد إنهاء الجرد على أي حال؟',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _finalizeCount();
                },
                child: const Text('إنهاء الجرد'),
              ),
            ],
          );
        },
      );
    } else {
      _finalizeCount();
    }
  }

  /// إنهاء الجرد وحفظ النتائج
  void _finalizeCount() {
    // حساب الفروقات
    for (final item in _countItems) {
      final systemQuantity = item['system_quantity'] as double;
      final countedQuantity = item['counted_quantity'] as double;

      item['difference'] = countedQuantity - systemQuantity;
    }

    // عرض ملخص الجرد
    _showCountSummary();
  }

  /// عرض ملخص الجرد
  void _showCountSummary() {
    final totalItems = _countItems.length;
    final countedItems = _countItems.where((item) => item['is_counted']).length;
    final itemsWithDifference =
        _countItems.where((item) => (item['difference'] as double) != 0).length;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('ملخص الجرد'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('إجمالي العناصر: $totalItems'),
              Text('العناصر المجرودة: $countedItems'),
              Text('العناصر ذات الفروقات: $itemsWithDifference'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _saveCountResults();
              },
              child: const Text('حفظ النتائج'),
            ),
          ],
        );
      },
    );
  }

  /// حفظ نتائج الجرد
  Future<void> _saveCountResults() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: حفظ نتائج الجرد في قاعدة البيانات

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ نتائج الجرد بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );

      // العودة إلى الشاشة السابقة
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء حفظ نتائج الجرد: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeLayout(
      title: 'جرد المخزون',
      actions: [
        if (!_isCountStarted)
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCurrentStock,
            tooltip: 'تحديث',
          ),
      ],
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
      child: Container(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    if (!_isCountStarted) {
      return _buildSetupScreen();
    } else {
      // في الإصدار الكامل، سيتم إضافة شاشات الجرد والنتائج
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.inventory_2,
              size: 64,
              color: AppColors.lightTextSecondary,
            ),
            const SizedBox(height: 16),
            const Text(
              'جاري تنفيذ الجرد...',
              style: AppTypography(fontSize: 24),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _completeCount,
              child: const Text('إنهاء الجرد'),
            ),
          ],
        ),
      );
    }
  }

  /// بناء شاشة الإعداد
  Widget _buildSetupScreen() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إعداد الجرد',
                    style: AppTypography.lightTextTheme.headlineMedium,
                  ),
                  const SizedBox(height: 16),

                  // اختيار المستودع
                  DropdownButtonFormField<Warehouse?>(
                    decoration: const InputDecoration(
                      labelText: 'المستودع',
                      border: OutlineInputBorder(),
                    ),
                    value: _selectedWarehouse,
                    items: _warehousePresenter.warehouses.map((warehouse) {
                      return DropdownMenuItem<Warehouse?>(
                        value: warehouse,
                        child: Text(warehouse.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedWarehouse = value;
                      });
                      _loadCurrentStock();
                    },
                  ),
                  const SizedBox(height: 16),

                  // تاريخ الجرد
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'تاريخ الجرد',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    readOnly: true,
                    controller: TextEditingController(
                      text:
                          '${_countDate.day}/${_countDate.month}/${_countDate.year}',
                    ),
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _countDate,
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now(),
                      );
                      if (date != null) {
                        setState(() {
                          _countDate = date;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16),

                  // رقم الجرد
                  TextFormField(
                    initialValue: _countNumber,
                    decoration: const InputDecoration(
                      labelText: 'رقم الجرد',
                      border: OutlineInputBorder(),
                    ),
                    readOnly: true,
                  ),
                  const SizedBox(height: 16),

                  // ملاحظات
                  TextFormField(
                    controller: _notesController,
                    decoration: const InputDecoration(
                      labelText: 'ملاحظات',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // عرض المخزون الحالي
          if (_currentStock.isNotEmpty) ...[
            Text(
              'المخزون الحالي',
              style: AppTypography.lightTextTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'عدد العناصر: ${_currentStock.length}',
              style: AppTypography.lightTextTheme.titleMedium,
            ),
            const SizedBox(height: 16),
          ],

          // زر بدء الجرد
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _selectedWarehouse != null ? _startCount : null,
              icon: const Icon(Icons.play_arrow),
              label: const Text('بدء الجرد'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
