import 'package:shared_preferences/shared_preferences.dart';
import '../../database/database_helper.dart';
import '../../utils/app_logger.dart';

/// خدمة إدارة إعدادات تسجيل الدخول
/// تتحكم في سلوك تسجيل الدخول والخروج التلقائي
class LoginSettingsService {
  static const String _rememberLoginKey = 'remember_login';
  static const String _autoLogoutEnabledKey = 'auto_logout_enabled';
  static const String _autoLogoutDurationKey = 'auto_logout_duration';
  static const String _lastActivityKey = 'last_activity_timestamp';

  /// الحصول على إعداد "تذكر تسجيل الدخول"
  static Future<bool> getRememberLogin() async {
    try {
      // محاولة الحصول على الإعداد من قاعدة البيانات أولاً
      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;
      final result = await db.query(
        'settings',
        where: 'key = ? AND is_deleted = 0',
        whereArgs: [_rememberLoginKey],
        limit: 1,
      );

      if (result.isNotEmpty) {
        final value = result.first['value'] as String?;
        return value?.toLowerCase() == 'true';
      }

      // إذا لم يوجد في قاعدة البيانات، استخدم SharedPreferences كبديل
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_rememberLoginKey) ??
          false; // افتراضي: false (إظهار شاشة تسجيل الدخول)
    } catch (e) {
      AppLogger.error('خطأ في الحصول على إعداد تذكر تسجيل الدخول: $e');
      return false; // القيمة الافتراضية (إظهار شاشة تسجيل الدخول)
    }
  }

  /// تحديث إعداد "تذكر تسجيل الدخول"
  static Future<bool> setRememberLogin(bool value) async {
    try {
      // تحديث في قاعدة البيانات
      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;
      final updateCount = await db.update(
        'settings',
        {
          'value': value.toString(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'key = ? AND is_deleted = 0',
        whereArgs: [_rememberLoginKey],
      );

      // تحديث في SharedPreferences كنسخة احتياطية
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_rememberLoginKey, value);

      AppLogger.info('تم تحديث إعداد تذكر تسجيل الدخول: $value');
      return updateCount > 0;
    } catch (e) {
      AppLogger.error('خطأ في تحديث إعداد تذكر تسجيل الدخول: $e');
      return false;
    }
  }

  /// الحصول على إعداد "تفعيل تسجيل الخروج التلقائي"
  static Future<bool> getAutoLogoutEnabled() async {
    try {
      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;
      final result = await db.query(
        'settings',
        where: 'key = ? AND is_deleted = 0',
        whereArgs: [_autoLogoutEnabledKey],
        limit: 1,
      );

      if (result.isNotEmpty) {
        final value = result.first['value'] as String?;
        return value?.toLowerCase() == 'true';
      }

      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_autoLogoutEnabledKey) ?? false; // افتراضي: false
    } catch (e) {
      AppLogger.error('خطأ في الحصول على إعداد تسجيل الخروج التلقائي: $e');
      return false;
    }
  }

  /// تحديث إعداد "تفعيل تسجيل الخروج التلقائي"
  static Future<bool> setAutoLogoutEnabled(bool value) async {
    try {
      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;
      final updateCount = await db.update(
        'settings',
        {
          'value': value.toString(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'key = ? AND is_deleted = 0',
        whereArgs: [_autoLogoutEnabledKey],
      );

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoLogoutEnabledKey, value);

      AppLogger.info('تم تحديث إعداد تسجيل الخروج التلقائي: $value');
      return updateCount > 0;
    } catch (e) {
      AppLogger.error('خطأ في تحديث إعداد تسجيل الخروج التلقائي: $e');
      return false;
    }
  }

  /// الحصول على مدة تسجيل الخروج التلقائي (بالدقائق)
  static Future<int> getAutoLogoutDuration() async {
    try {
      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;
      final result = await db.query(
        'settings',
        where: 'key = ? AND is_deleted = 0',
        whereArgs: [_autoLogoutDurationKey],
        limit: 1,
      );

      if (result.isNotEmpty) {
        final value = result.first['value'] as String?;
        return int.tryParse(value ?? '60') ?? 60;
      }

      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_autoLogoutDurationKey) ?? 60; // افتراضي: 60 دقيقة
    } catch (e) {
      AppLogger.error('خطأ في الحصول على مدة تسجيل الخروج التلقائي: $e');
      return 60;
    }
  }

  /// تحديث مدة تسجيل الخروج التلقائي
  static Future<bool> setAutoLogoutDuration(int minutes) async {
    try {
      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;
      final updateCount = await db.update(
        'settings',
        {
          'value': minutes.toString(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'key = ? AND is_deleted = 0',
        whereArgs: [_autoLogoutDurationKey],
      );

      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_autoLogoutDurationKey, minutes);

      AppLogger.info('تم تحديث مدة تسجيل الخروج التلقائي: $minutes دقيقة');
      return updateCount > 0;
    } catch (e) {
      AppLogger.error('خطأ في تحديث مدة تسجيل الخروج التلقائي: $e');
      return false;
    }
  }

  /// تحديث وقت آخر نشاط
  static Future<void> updateLastActivity() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
          _lastActivityKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      AppLogger.error('خطأ في تحديث وقت آخر نشاط: $e');
    }
  }

  /// التحقق من انتهاء مهلة الجلسة
  static Future<bool> isSessionExpired() async {
    try {
      final autoLogoutEnabled = await getAutoLogoutEnabled();
      if (!autoLogoutEnabled) return false;

      final prefs = await SharedPreferences.getInstance();
      final lastActivity = prefs.getInt(_lastActivityKey);
      if (lastActivity == null) return false;

      final duration = await getAutoLogoutDuration();
      final expirationTime =
          lastActivity + (duration * 60 * 1000); // تحويل إلى مللي ثانية
      final currentTime = DateTime.now().millisecondsSinceEpoch;

      return currentTime > expirationTime;
    } catch (e) {
      AppLogger.error('خطأ في التحقق من انتهاء مهلة الجلسة: $e');
      return false;
    }
  }

  /// التحقق من ضرورة عرض شاشة تسجيل الدخول
  static Future<bool> shouldShowLoginScreen() async {
    try {
      // التحقق من إعداد "تذكر تسجيل الدخول"
      final rememberLogin = await getRememberLogin();
      if (!rememberLogin) {
        AppLogger.info(
            'إعداد "تذكر تسجيل الدخول" معطل - سيتم عرض شاشة تسجيل الدخول');
        return true;
      }

      // التحقق من انتهاء مهلة الجلسة
      final sessionExpired = await isSessionExpired();
      if (sessionExpired) {
        AppLogger.info('انتهت مهلة الجلسة - سيتم عرض شاشة تسجيل الدخول');
        return true;
      }

      AppLogger.info('لا حاجة لعرض شاشة تسجيل الدخول');
      return false;
    } catch (e) {
      AppLogger.error('خطأ في التحقق من ضرورة عرض شاشة تسجيل الدخول: $e');
      return true; // في حالة الخطأ، اعرض شاشة تسجيل الدخول للأمان
    }
  }

  /// مسح بيانات الجلسة (عند تسجيل الخروج)
  static Future<void> clearSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastActivityKey);
      AppLogger.info('تم مسح بيانات الجلسة');
    } catch (e) {
      AppLogger.error('خطأ في مسح بيانات الجلسة: $e');
    }
  }

  /// الحصول على جميع إعدادات تسجيل الدخول
  static Future<Map<String, dynamic>> getAllLoginSettings() async {
    return {
      'remember_login': await getRememberLogin(),
      'auto_logout_enabled': await getAutoLogoutEnabled(),
      'auto_logout_duration': await getAutoLogoutDuration(),
    };
  }
}
