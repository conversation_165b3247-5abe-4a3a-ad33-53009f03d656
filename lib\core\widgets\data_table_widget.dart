import 'package:flutter/material.dart';
import '../utils/index.dart';
import '../theme/index.dart';

/// ودجت جدول البيانات المتقدم
/// يستخدم لعرض البيانات في شكل جدول مع دعم الترتيب والتصفية والتخصيص
class AdvancedDataTable extends StatelessWidget {
  /// عناوين الأعمدة
  final List<DataColumn> columns;

  /// صفوف البيانات
  final List<DataRow> rows;

  /// هل يتم عرض حدود الخلايا
  final bool showCellBorder;

  /// هل يتم عرض الصفوف بألوان متناوبة
  final bool zebraPattern;

  /// لون الصف الزوجي (في حالة zebraPattern = true)
  final Color? evenRowColor;

  /// لون الصف الفردي (في حالة zebraPattern = true)
  final Color? oddRowColor;

  /// لون حدود الخلايا
  final Color? borderColor;

  /// سماكة حدود الخلايا
  final double borderWidth;

  /// لون خلفية رأس الجدول
  final Color? headerBackgroundColor;

  /// لون نص رأس الجدول
  final Color? headerTextColor;

  /// نمط نص رأس الجدول
  final AppTypography? headerAppTypography;

  /// محاذاة نص رأس الجدول
  final TextAlign headerTextAlign;

  /// ارتفاع صف رأس الجدول
  final double? headerHeight;

  /// ارتفاع صفوف البيانات
  final double? dataRowHeight;

  /// هل يتم عرض أرقام الصفوف
  final bool showRowNumbers;

  /// عنوان عمود رقم الصف
  final String rowNumberColumnTitle;

  /// عرض عمود رقم الصف
  final double rowNumberColumnWidth;

  /// دالة تنفذ عند النقر على صف
  final Function(int index)? onRowTap;

  /// هل يتم عرض مؤشر التحميل
  final bool isLoading;

  /// رسالة عندما تكون البيانات فارغة
  final String emptyMessage;

  /// هل يمكن التمرير أفقيًا
  final bool horizontalScrollEnabled;

  /// هل يمكن التمرير عموديًا
  final bool verticalScrollEnabled;

  /// ارتفاع الجدول
  final double? tableHeight;

  /// عرض الجدول
  final double? tableWidth;

  /// نصف قطر زوايا الجدول
  final double borderRadius;

  /// المسافة الداخلية للجدول
  final EdgeInsetsGeometry padding;

  /// هل يتم تقليص الجدول
  final bool shrinkWrap;

  /// هل يتم عرض ظل للجدول
  final bool showShadow;

  /// لون الظل
  final Color? shadowColor;

  /// انتشار الظل
  final double shadowSpread;

  /// تمويه الظل
  final double shadowBlur;

  /// إزاحة الظل
  final Offset shadowOffset;

  /// هل يتم إضافة حدود إعادة الرسم
  final bool addRepaintBoundaries;

  /// هل يتم إضافة فهارس семантиكية
  final bool addSemanticIndexes;

  const AdvancedDataTable({
    Key? key,
    required this.columns,
    required this.rows,
    this.showCellBorder = true,
    this.zebraPattern = true,
    this.evenRowColor,
    this.oddRowColor,
    this.borderColor,
    this.borderWidth = 0.5,
    this.headerBackgroundColor,
    this.headerTextColor,
    this.headerAppTypography,
    this.headerTextAlign = TextAlign.center,
    this.headerHeight,
    this.dataRowHeight,
    this.showRowNumbers = false,
    this.rowNumberColumnTitle = '#',
    this.rowNumberColumnWidth = 40,
    this.onRowTap,
    this.isLoading = false,
    this.emptyMessage = 'لا توجد بيانات',
    this.horizontalScrollEnabled = true,
    this.verticalScrollEnabled = true,
    this.tableHeight,
    this.tableWidth,
    this.borderRadius = 8.0,
    this.padding = const EdgeInsets.all(0),
    this.shrinkWrap = false,
    this.showShadow = true,
    this.shadowColor,
    this.shadowSpread = 0.0,
    this.shadowBlur = 4.0,
    this.shadowOffset = const Offset(0, 2),
    this.addRepaintBoundaries = true,
    this.addSemanticIndexes = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (rows.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            emptyMessage,
            style: const AppTypography(
              fontSize: Layout.mediumFontSize,
              color: AppColors.lightTextSecondary,
            ),
          ),
        ),
      );
    }

    // إضافة عمود رقم الصف إذا كان مطلوبًا
    List<DataColumn> effectiveColumns = showRowNumbers
        ? [
            DataColumn(
              label: Container(
                width: rowNumberColumnWidth,
                alignment: Alignment.center,
                child: Text(
                  rowNumberColumnTitle,
                  style: headerAppTypography,
                  textAlign: headerTextAlign,
                ),
              ),
            ),
            ...columns,
          ]
        : columns;

    // إضافة خلية رقم الصف لكل صف إذا كان مطلوبًا
    List<DataRow> effectiveRows = [];
    for (int i = 0; i < rows.length; i++) {
      final row = rows[i];

      if (showRowNumbers) {
        final rowNumberCell = DataCell(
          Container(
            width: rowNumberColumnWidth,
            alignment: Alignment.center,
            child: Text(
              '${i + 1}',
              style: const AppTypography(
                color: AppColors.lightTextSecondary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );

        effectiveRows.add(
          DataRow(
            cells: [rowNumberCell, ...row.cells],
            color: zebraPattern
                ? WidgetStateProperty.resolveWith<Color?>(
                    (Set<WidgetState> states) {
                      return i % 2 == 0
                          ? evenRowColor ??
                              AppColors.lightTextSecondary
                                  .withValues(alpha: 0.05)
                          : oddRowColor;
                    },
                  )
                : null,
            onSelectChanged: onRowTap != null ? (_) => onRowTap!(i) : null,
          ),
        );
      } else {
        effectiveRows.add(
          DataRow(
            cells: row.cells,
            color: zebraPattern
                ? WidgetStateProperty.resolveWith<Color?>(
                    (Set<WidgetState> states) {
                      return i % 2 == 0
                          ? evenRowColor ??
                              AppColors.lightTextSecondary
                                  .withValues(alpha: 0.05)
                          : oddRowColor;
                    },
                  )
                : null,
            onSelectChanged: onRowTap != null ? (_) => onRowTap!(i) : null,
          ),
        );
      }
    }

    final dataTable = DataTable(
      columns: effectiveColumns.map((column) {
        return DataColumn(
          label: Expanded(
            child: Text(
              column.label is Text ? (column.label as Text).data ?? '' : '',
              style: AppTypography(
                fontWeight: FontWeight.bold,
                color: headerTextColor ?? AppColors.lightTextPrimary,
                fontSize: Layout.defaultFontSize,
              ),
              textAlign: headerTextAlign,
            ),
          ),
          onSort: column.onSort,
          tooltip: column.tooltip,
        );
      }).toList(),
      rows: effectiveRows,
      border: showCellBorder
          ? TableBorder(
              horizontalInside: BorderSide(
                width: borderWidth,
                color: borderColor ??
                    AppColors.lightTextSecondary.withValues(alpha: 0.3),
              ),
              verticalInside: BorderSide(
                width: borderWidth,
                color: borderColor ??
                    AppColors.lightTextSecondary.withValues(alpha: 0.3),
              ),
            )
          : null,
      headingRowColor: WidgetStateProperty.resolveWith<Color?>(
        (Set<WidgetState> states) {
          return headerBackgroundColor ??
              AppColors.lightTextSecondary.withValues(alpha: 0.1);
        },
      ),
      headingRowHeight: headerHeight ?? 56.0,
      dataRowMinHeight: dataRowHeight ?? 52.0,
      dataRowMaxHeight: dataRowHeight ?? 52.0,
      showCheckboxColumn: false,
      horizontalMargin: 16.0,
      columnSpacing: 16.0,
    );

    Widget tableWidget =
        addRepaintBoundaries ? RepaintBoundary(child: dataTable) : dataTable;

    // إضافة التمرير الأفقي إذا كان مطلوبًا
    if (horizontalScrollEnabled) {
      tableWidget = SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minWidth: tableWidth ?? MediaQuery.of(context).size.width,
          ),
          child: tableWidget,
        ),
      );
    }

    // إضافة التمرير العمودي إذا كان مطلوبًا
    if (verticalScrollEnabled) {
      tableWidget = SingleChildScrollView(
        child: tableWidget,
      );
    }

    // إضافة ارتفاع محدد إذا كان مطلوبًا
    if (tableHeight != null) {
      tableWidget = SizedBox(
        height: tableHeight,
        child: tableWidget,
      );
    }

    // إضافة المسافة الداخلية
    tableWidget = Padding(
      padding: padding,
      child: tableWidget,
    );

    // إضافة الحدود ونصف القطر
    tableWidget = ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.lightSurface,
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(
            color: borderColor ??
                AppColors.lightTextSecondary.withValues(alpha: 0.3),
            width: borderWidth,
          ),
          boxShadow: showShadow
              ? [
                  BoxShadow(
                    color: shadowColor ??
                        AppColors.lightTextPrimary.withValues(alpha: 0.1),
                    spreadRadius: shadowSpread,
                    blurRadius: shadowBlur,
                    offset: shadowOffset,
                  ),
                ]
              : null,
        ),
        child: addSemanticIndexes
            ? Semantics(
                container: true,
                child: tableWidget,
              )
            : tableWidget,
      ),
    );

    return tableWidget;
  }
}

/// ودجت جدول البيانات المالية
/// يستخدم لعرض البيانات المالية مثل كشف الحساب والميزانية
class FinancialDataTable extends StatelessWidget {
  /// البيانات المالية
  final List<Map<String, dynamic>> data;

  /// تعريف الأعمدة
  final List<FinancialTableColumn> columns;

  /// عنوان الجدول
  final String? title;

  /// هل يتم عرض مؤشر التحميل
  final bool isLoading;

  /// رسالة عندما تكون البيانات فارغة
  final String emptyMessage;

  /// دالة تنفذ عند النقر على صف
  final Function(int index)? onRowTap;

  /// هل يتم عرض الإجماليات
  final bool showTotals;

  /// أسماء الأعمدة التي يتم حساب إجمالياتها
  final List<String> totalColumns;

  /// دالة مخصصة لبناء خلايا الجدول
  final Widget? Function(BuildContext context, int rowIndex, int columnIndex,
      dynamic value, String field)? customCellBuilder;

  /// هل يتم عرض حدود الخلايا
  final bool showCellBorder;

  /// هل يتم عرض الصفوف بألوان متناوبة
  final bool zebraPattern;

  /// ارتفاع الجدول
  final double? tableHeight;

  /// هل يمكن التمرير أفقيًا
  final bool horizontalScrollEnabled;

  /// هل يمكن التمرير عموديًا
  final bool verticalScrollEnabled;

  /// هل يتم عرض أرقام الصفوف
  final bool showRowNumbers;

  /// هل يتم تقليص الجدول
  final bool shrinkWrap;

  const FinancialDataTable({
    Key? key,
    required this.data,
    required this.columns,
    this.title,
    this.isLoading = false,
    this.emptyMessage = 'لا توجد بيانات',
    this.onRowTap,
    this.showTotals = true,
    this.totalColumns = const [],
    this.showCellBorder = true,
    this.zebraPattern = true,
    this.tableHeight,
    this.horizontalScrollEnabled = true,
    this.verticalScrollEnabled = true,
    this.showRowNumbers = true,
    this.shrinkWrap = false,
    this.customCellBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (data.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            emptyMessage,
            style: const AppTypography(
              fontSize: Layout.mediumFontSize,
              color: AppColors.lightTextSecondary,
            ),
          ),
        ),
      );
    }

    // إنشاء أعمدة الجدول
    List<DataColumn> dataColumns = columns.map((column) {
      return DataColumn(
        label: Container(
          alignment: column.alignment == ColumnAlignment.start
              ? Alignment.centerRight
              : column.alignment == ColumnAlignment.end
                  ? Alignment.centerLeft
                  : Alignment.center,
          child: Text(
            column.title,
            style: const AppTypography(fontWeight: FontWeight.bold),
          ),
        ),
        tooltip: column.tooltip,
      );
    }).toList();

    // إنشاء صفوف الجدول
    List<DataRow> dataRows = [];

    for (int i = 0; i < data.length; i++) {
      final rowData = data[i];

      List<DataCell> cells = [];

      for (var column in columns) {
        final value = rowData[column.field];

        // تحديد لون النص بناءً على القيمة (للأعمدة المالية)
        Color? textColor;
        IconData? icon;

        if (column.isNumeric && value != null) {
          if (column.colorBasedOnValue) {
            if (value is num) {
              if (value < 0) {
                textColor = AppColors.error;
                icon = Icons.arrow_downward;
              } else if (value > 0) {
                textColor = AppColors.success;
                icon = Icons.arrow_upward;
              }
            } else if (value is String) {
              final numValue = double.tryParse(value.replaceAll(',', ''));
              if (numValue != null) {
                if (numValue < 0) {
                  textColor = AppColors.error;
                  icon = Icons.arrow_downward;
                } else if (numValue > 0) {
                  textColor = AppColors.success;
                  icon = Icons.arrow_upward;
                }
              }
            }
          }
        }

        // تنسيق القيمة
        String formattedValue = '';

        if (value != null) {
          if (column.isDate && value is String) {
            // تنسيق التاريخ
            formattedValue = value;
          } else if (column.isNumeric) {
            // تنسيق الرقم
            if (value is num) {
              formattedValue = column.formatNumber(value);
            } else if (value is String) {
              final numValue = double.tryParse(value.replaceAll(',', ''));
              if (numValue != null) {
                formattedValue = column.formatNumber(numValue);
              } else {
                formattedValue = value;
              }
            } else {
              formattedValue = value.toString();
            }
          } else {
            formattedValue = value.toString();
          }
        }

        // إنشاء خلية البيانات
        if (customCellBuilder != null) {
          // استخدام دالة البناء المخصصة إذا كانت متوفرة
          final customCell = customCellBuilder!(
              context, i, columns.indexOf(column), value, column.field);
          if (customCell != null) {
            cells.add(DataCell(customCell));
            continue;
          }
        }

        // استخدام البناء الافتراضي إذا لم تكن هناك دالة مخصصة أو إذا أعادت null
        cells.add(
          DataCell(
            Container(
              alignment: column.alignment == ColumnAlignment.start
                  ? Alignment.centerRight
                  : column.alignment == ColumnAlignment.end
                      ? Alignment.centerLeft
                      : Alignment.center,
              child: icon != null
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          formattedValue,
                          style: AppTypography(
                            color: textColor,
                            fontWeight: column.isBold ? FontWeight.bold : null,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          icon,
                          color: textColor,
                          size: 14,
                        ),
                      ],
                    )
                  : Text(
                      formattedValue,
                      style: AppTypography(
                        color: textColor,
                        fontWeight: column.isBold ? FontWeight.bold : null,
                      ),
                    ),
            ),
          ),
        );
      }

      dataRows.add(DataRow(cells: cells));
    }

    // إضافة صف الإجماليات إذا كان مطلوبًا
    if (showTotals && totalColumns.isNotEmpty) {
      List<DataCell> totalCells = [];

      for (var column in columns) {
        if (totalColumns.contains(column.field) && column.isNumeric) {
          // حساب الإجمالي
          double total = 0;
          for (var rowData in data) {
            final value = rowData[column.field];
            if (value != null) {
              if (value is num) {
                total += value;
              } else if (value is String) {
                final numValue = double.tryParse(value.replaceAll(',', ''));
                if (numValue != null) {
                  total += numValue;
                }
              }
            }
          }

          totalCells.add(
            DataCell(
              Container(
                alignment: column.alignment == ColumnAlignment.start
                    ? Alignment.centerRight
                    : column.alignment == ColumnAlignment.end
                        ? Alignment.centerLeft
                        : Alignment.center,
                child: Text(
                  column.formatNumber(total),
                  style: const AppTypography(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          );
        } else {
          // خلية فارغة للأعمدة غير المالية
          totalCells.add(
            const DataCell(
              SizedBox(),
            ),
          );
        }
      }

      // إضافة صف الإجماليات
      dataRows.add(
        DataRow(
          cells: totalCells,
          color: WidgetStateProperty.all(
              AppColors.lightTextSecondary.withValues(alpha: 0.1)),
        ),
      );
    }

    return AdvancedDataTable(
      columns: dataColumns,
      rows: dataRows,
      showCellBorder: showCellBorder,
      zebraPattern: zebraPattern,
      headerBackgroundColor:
          AppColors.lightTextSecondary.withValues(alpha: 0.1),
      headerTextColor: AppColors.lightTextPrimary,
      onRowTap: onRowTap,
      isLoading: isLoading,
      emptyMessage: emptyMessage,
      tableHeight: tableHeight,
      horizontalScrollEnabled: horizontalScrollEnabled,
      verticalScrollEnabled: verticalScrollEnabled,
      showRowNumbers: showRowNumbers,
      shrinkWrap: shrinkWrap,
    );
  }
}

/// محاذاة الأعمدة
enum ColumnAlignment {
  start,
  center,
  end,
}

/// تعريف عمود الجدول المالي
class FinancialTableColumn {
  /// عنوان العمود
  final String title;

  /// اسم الحقل في البيانات
  final String field;

  /// تلميح العمود
  final String? tooltip;

  /// محاذاة العمود
  final ColumnAlignment alignment;

  /// هل العمود رقمي
  final bool isNumeric;

  /// هل العمود تاريخ
  final bool isDate;

  /// هل يتم تلوين القيمة بناءً على قيمتها
  final bool colorBasedOnValue;

  /// هل النص عريض
  final bool isBold;

  /// عدد الخانات العشرية
  final int decimalPlaces;

  /// رمز العملة
  final String? currencySymbol;

  /// موضع رمز العملة
  final bool currencySymbolOnLeft;

  const FinancialTableColumn({
    required this.title,
    required this.field,
    this.tooltip,
    this.alignment = ColumnAlignment.start,
    this.isNumeric = false,
    this.isDate = false,
    this.colorBasedOnValue = false,
    this.isBold = false,
    this.decimalPlaces = 2,
    this.currencySymbol,
    this.currencySymbolOnLeft = true,
  });

  /// تنسيق الرقم
  String formatNumber(num value) {
    final formattedValue = value.toStringAsFixed(decimalPlaces);

    if (currencySymbol != null) {
      return currencySymbolOnLeft
          ? '$currencySymbol $formattedValue'
          : '$formattedValue $currencySymbol';
    }

    return formattedValue;
  }
}

/// ودجت جدول البيانات العام
/// يستخدم لعرض البيانات في شكل جدول مع دعم النماذج المخصصة
class DataTableWidget<T> extends StatelessWidget {
  /// عناوين الأعمدة
  final List<DataColumn> columns;

  /// العناصر التي سيتم عرضها
  final List<T> items;

  /// دالة لبناء صفوف الجدول
  final DataRow Function(T item)? rowBuilder;

  /// دالة تنفذ عند النقر على صف
  final Function(int index)? onRowTap;

  /// دالة تنفذ عند ترتيب الجدول
  final Function(int columnIndex, bool ascending)? onSort;

  /// متحكم التمرير
  final ScrollController? scrollController;

  /// هل يتم عرض مؤشر التحميل
  final bool isLoading;

  /// رسالة عندما تكون البيانات فارغة
  final String emptyMessage;

  /// هل يتم عرض حدود الخلايا
  final bool showCellBorder;

  /// هل يتم عرض الصفوف بألوان متناوبة
  final bool zebraPattern;

  const DataTableWidget({
    Key? key,
    required this.columns,
    required this.items,
    this.rowBuilder,
    this.onRowTap,
    this.onSort,
    this.scrollController,
    this.isLoading = false,
    this.emptyMessage = 'لا توجد بيانات',
    this.showCellBorder = true,
    this.zebraPattern = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (items.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            emptyMessage,
            style: const AppTypography(
              fontSize: Layout.mediumFontSize,
              color: AppColors.lightTextSecondary,
            ),
          ),
        ),
      );
    }

    // إنشاء صفوف الجدول
    List<DataRow> dataRows = [];

    for (int i = 0; i < items.length; i++) {
      final item = items[i];

      if (rowBuilder != null) {
        dataRows.add(rowBuilder!(item));
      } else {
        // إذا لم يتم توفير دالة بناء الصفوف، نستخدم صفًا فارغًا
        dataRows.add(
          DataRow(
            cells: List.generate(
              columns.length,
              (index) => const DataCell(Text('')),
            ),
          ),
        );
      }
    }

    return SingleChildScrollView(
      controller: scrollController,
      scrollDirection: Axis.vertical,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: columns,
          rows: dataRows,
          border: showCellBorder
              ? TableBorder(
                  horizontalInside: BorderSide(
                    width: 0.5,
                    color: AppColors.lightTextSecondary.withValues(alpha: 0.3),
                  ),
                  verticalInside: BorderSide(
                    width: 0.5,
                    color: AppColors.lightTextSecondary.withValues(alpha: 0.3),
                  ),
                )
              : null,
          headingRowColor: WidgetStateProperty.resolveWith<Color?>(
            (Set<WidgetState> states) {
              return AppColors.lightTextSecondary.withValues(alpha: 0.1);
            },
          ),
          dataRowColor: zebraPattern && dataRows.isNotEmpty
              ? WidgetStateProperty.resolveWith<Color?>(
                  (Set<WidgetState> states) {
                    if (states.isEmpty) return null;
                    final index = dataRows.indexOf(states.first as DataRow);
                    return index % 2 == 0
                        ? AppColors.lightTextSecondary.withValues(alpha: 0.05)
                        : null;
                  },
                )
              : null,
        ),
      ),
    );
  }
}

/// ودجت جدول البيانات المبسط
/// يستخدم لعرض البيانات في شكل جدول بسيط
class SimpleDataTable extends StatelessWidget {
  /// البيانات
  final List<Map<String, dynamic>> data;

  /// أسماء الأعمدة
  final List<String> columns;

  /// أسماء الحقول
  final List<String> fields;

  /// هل يتم عرض مؤشر التحميل
  final bool isLoading;

  /// رسالة عندما تكون البيانات فارغة
  final String emptyMessage;

  /// دالة تنفذ عند النقر على صف
  final Function(int index)? onRowTap;

  /// هل يتم عرض حدود الخلايا
  final bool showCellBorder;

  /// هل يتم عرض الصفوف بألوان متناوبة
  final bool zebraPattern;

  /// ارتفاع الجدول
  final double? tableHeight;

  /// هل يمكن التمرير أفقيًا
  final bool horizontalScrollEnabled;

  /// هل يمكن التمرير عموديًا
  final bool verticalScrollEnabled;

  /// هل يتم عرض أرقام الصفوف
  final bool showRowNumbers;

  /// هل يتم تقليص الجدول
  final bool shrinkWrap;

  const SimpleDataTable({
    Key? key,
    required this.data,
    required this.columns,
    required this.fields,
    this.isLoading = false,
    this.emptyMessage = 'لا توجد بيانات',
    this.onRowTap,
    this.showCellBorder = true,
    this.zebraPattern = true,
    this.tableHeight,
    this.horizontalScrollEnabled = true,
    this.verticalScrollEnabled = true,
    this.showRowNumbers = true,
    this.shrinkWrap = false,
  })  : assert(columns.length == fields.length,
            'عدد الأعمدة يجب أن يساوي عدد الحقول'),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (data.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            emptyMessage,
            style: const AppTypography(
              fontSize: Layout.mediumFontSize,
              color: AppColors.lightTextSecondary,
            ),
          ),
        ),
      );
    }

    // إنشاء أعمدة الجدول
    List<DataColumn> dataColumns = columns.map((column) {
      return DataColumn(
        label: Text(
          column,
          style: const AppTypography(fontWeight: FontWeight.bold),
        ),
      );
    }).toList();

    // إنشاء صفوف الجدول
    List<DataRow> dataRows = [];

    for (int i = 0; i < data.length; i++) {
      final rowData = data[i];

      List<DataCell> cells = [];

      for (var field in fields) {
        final value = rowData[field];

        cells.add(
          DataCell(
            Text(value?.toString() ?? ''),
          ),
        );
      }

      dataRows.add(DataRow(cells: cells));
    }

    return AdvancedDataTable(
      columns: dataColumns,
      rows: dataRows,
      showCellBorder: showCellBorder,
      zebraPattern: zebraPattern,
      onRowTap: onRowTap,
      isLoading: isLoading,
      emptyMessage: emptyMessage,
      tableHeight: tableHeight,
      horizontalScrollEnabled: horizontalScrollEnabled,
      verticalScrollEnabled: verticalScrollEnabled,
      showRowNumbers: showRowNumbers,
      shrinkWrap: shrinkWrap,
    );
  }
}
