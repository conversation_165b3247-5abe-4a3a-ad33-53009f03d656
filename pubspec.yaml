name: tajer_plus
description: A modern accounting and management application for small businesses.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.2
  provider: ^6.1.4
  path: ^1.8.3
  shared_preferences: ^2.2.1
  firebase_core: ^2.24.2
  firebase_auth: ^4.16.0
  firebase_storage: ^11.6.0
  google_sign_in: ^6.1.5
  flutter_secure_storage: ^9.0.0
  intl: ^0.19.0
  image_picker: ^1.0.4
  url_launcher: ^6.3.1
  path_provider: ^2.1.1
  google_fonts: ^5.1.0
  flutter_svg: ^2.0.7
  cached_network_image: ^3.2.3
  shimmer: ^3.0.0
  flutter_blurhash: ^0.7.0
  connectivity_plus: ^5.0.2
  permission_handler: ^11.3.0
  sqflite: ^2.3.2
  crypto: ^3.0.3

  carousel_slider: ^5.0.0
  flutter_local_notifications: ^19.1.0
  uuid: ^4.3.3
  archive: ^4.0.6
  mobile_scanner: ^3.5.6
  audioplayers: ^5.2.1
  flutter_image_compress: ^2.1.0
  just_the_tooltip: ^0.0.12
  http: ^1.1.0
  syncfusion_flutter_datagrid: ^24.1.41
  pdf: ^3.11.3
  printing: ^5.11.1
  fl_chart: ^0.71.0
  share_plus: ^11.0.0
  file_picker: ^10.1.2
  csv: ^6.0.0
  font_awesome_flutter: ^10.8.0
# file_picker: ^5.2.10 # Removed due to compatibility issues

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.8

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0
  sqflite_common_ffi: ^2.3.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/sounds/
    - assets/sql/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
