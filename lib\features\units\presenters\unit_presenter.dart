import 'package:flutter/foundation.dart';
import '../../../core/models/unit.dart';
import '../../../core/database/database_service.dart';

class UnitPresenter extends ChangeNotifier {
  final _db = DatabaseService.instance;
  List<Unit> _units = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Unit> get units => _units;
  List<Unit> get baseUnits => _units.where((unit) => unit.isBase).toList();
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize
  Future<void> init() async {
    await loadUnits();
  }

  // Load all units
  Future<void> loadUnits({String? type}) async {
    _setLoading(true);
    try {
      // تحديث الاستعلام ليتوافق مع جدول الوحدات الموحد
      String query = 'SELECT * FROM units WHERE is_deleted = 0';

      query += ' ORDER BY name';

      final List<Map<String, dynamic>> maps = await _db.rawQuery(query);

      _units = maps.map((map) => Unit.fromMap(map)).toList();
      _error = null;
    } catch (e) {
      _error = 'Failed to load units: ${e.toString()}';
    } finally {
      _setLoading(false);
    }
  }

  // Add a new unit
  Future<bool> addUnit(Unit unit) async {
    _setLoading(true);
    try {
      // إعداد بيانات الوحدة للإدخال
      final now = DateTime.now().toIso8601String();
      final Map<String, dynamic> unitData = {
        ...unit.toMap(),
        'created_at': now,
        'updated_at': now,
      };

      // تنفيذ استعلام SQL لإضافة الوحدة
      await _db.rawQuery('''
        INSERT INTO units (
          id, name, code, symbol, abbreviation, type, unit_type, is_base, is_base_unit,
          conversion_factor, base_unit_id, description, created_at, updated_at, is_deleted
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', [
        unitData['id'],
        unitData['name'],
        unitData['symbol'], // استخدام symbol كـ code
        unitData['symbol'],
        unitData['symbol'],
        'product', // نوع افتراضي
        'product', // نوع افتراضي
        unitData['isBase'] ? 1 : 0,
        unitData['isBase'] ? 1 : 0,
        unitData['conversionFactor'] ?? 1.0,
        unitData['baseUnitId'],
        unitData['description'],
        unitData['created_at'],
        unitData['updated_at'],
        0, // is_deleted
      ]);

      await loadUnits();
      return true;
    } catch (e) {
      _error = 'Failed to add unit: ${e.toString()}';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update an existing unit
  Future<bool> updateUnit(Unit unit) async {
    _setLoading(true);
    try {
      // إعداد بيانات الوحدة للتحديث
      final now = DateTime.now().toIso8601String();
      final Map<String, dynamic> unitData = {
        ...unit.toMap(),
        'updated_at': now,
      };

      // تنفيذ استعلام SQL لتحديث الوحدة
      await _db.rawQuery('''
        UPDATE units SET
          name = ?, code = ?, symbol = ?, abbreviation = ?, type = ?, unit_type = ?,
          is_base = ?, is_base_unit = ?, conversion_factor = ?,
          base_unit_id = ?, description = ?, updated_at = ?
        WHERE id = ? AND is_deleted = 0
        ''', [
        unitData['name'],
        unitData['symbol'], // استخدام symbol كـ code
        unitData['symbol'],
        unitData['symbol'],
        'product', // نوع افتراضي
        'product', // نوع افتراضي
        unitData['isBase'] ? 1 : 0,
        unitData['isBase'] ? 1 : 0,
        unitData['conversionFactor'] ?? 1.0,
        unitData['baseUnitId'],
        unitData['description'],
        unitData['updated_at'],
        unitData['id'], // WHERE id = ?
      ]);

      await loadUnits();
      return true;
    } catch (e) {
      _error = 'Failed to update unit: ${e.toString()}';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete a unit
  Future<bool> deleteUnit(String id) async {
    _setLoading(true);
    try {
      // تنفيذ استعلام SQL لحذف الوحدة (حذف ناعم) (تحديث للجدول الموحد)
      final now = DateTime.now().toIso8601String();
      await _db.rawQuery('''
        UPDATE units SET
          is_deleted = 1, updated_at = ?
        WHERE id = ?
        ''', [now, id]);

      await loadUnits();
      return true;
    } catch (e) {
      _error = 'Failed to delete unit: ${e.toString()}';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get a unit by ID
  Future<Unit?> getUnitById(String id) async {
    try {
      // تنفيذ استعلام SQL للحصول على الوحدة بواسطة المعرف (تحديث للجدول الموحد)
      final results = await _db.rawQuery(
          'SELECT * FROM units WHERE id = ? AND is_deleted = 0', [id]);

      // إذا لم يتم العثور على الوحدة
      if (results.isEmpty) {
        return null;
      }

      // تحويل النتيجة إلى كائن Unit
      return Unit.fromMap(results.first);
    } catch (e) {
      _error = 'Failed to get unit: ${e.toString()}';
      return null;
    }
  }

  // Convert quantity between units
  double convertQuantity(double quantity, Unit fromUnit, Unit toUnit) {
    if (fromUnit.isBase) {
      return quantity * toUnit.conversionFactor;
    } else if (toUnit.isBase) {
      return quantity / fromUnit.conversionFactor;
    } else {
      // Convert to base unit first, then to target unit
      final baseQuantity = quantity / fromUnit.conversionFactor;
      return baseQuantity * toUnit.conversionFactor;
    }
  }

  void _setLoading(bool value) {
    _isLoading = value;
    Future.microtask(() => notifyListeners());
  }
}
