import 'package:uuid/uuid.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/utils/app_logger.dart';
import '../models/cart_item.dart';
import '../../products/services/product_service.dart';

/// خدمة المبيعات
class SaleService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final ProductService _productService = ProductService();

  /// إنشاء عملية بيع جديدة
  Future<String?> createSale({
    required List<CartItem> items,
    String? customerId,
    required String paymentMethod,
    required double amountPaid,
    required double subtotal,
    required double discount,
    required bool isDiscountPercentage,
    required double tax,
    required double total,
    String? notes,
  }) async {
    final db = await _databaseHelper.database;

    try {
      // إنشاء معرف فريد للبيع
      final saleId = const Uuid().v4();
      final now = DateTime.now().toIso8601String();

      // بدء المعاملة
      await db.transaction((txn) async {
        // إدخال البيع الرئيسي
        await txn.insert(
          'sales',
          {
            'id': saleId,
            'customer_id': customerId,
            'payment_method': paymentMethod,
            'amount_paid': amountPaid,
            'subtotal': subtotal,
            'discount': discount,
            'is_discount_percentage': isDiscountPercentage ? 1 : 0,
            'tax': tax,
            'total': total,
            'notes': notes,
            'status': 'completed',
            'created_at': now,
            'updated_at': now,
            'is_deleted': 0,
          },
        );

        // إدخال عناصر البيع
        for (final item in items) {
          await txn.insert(
            'sale_items',
            {
              'id': const Uuid().v4(),
              'sale_id': saleId,
              'product_id': item.product.id,
              'quantity': item.quantity,
              'price': item.price,
              'discount': item.discount,
              'is_discount_percentage': item.isDiscountPercentage ? 1 : 0,
              'tax_rate': item.taxRate,
              'subtotal': item.subtotal,
              'discount_amount': item.discountAmount,
              'tax_amount': item.taxAmount,
              'total': item.total,
              'created_at': now,
              'is_deleted': 0,
            },
          );

          // تحديث مخزون المنتج
          if (!item.product.isService) {
            await _productService.updateProductStock(
              item.product.id,
              -item.quantity,
            );
          }
        }

        // إدخال الدفع
        await txn.insert(
          'payments',
          {
            'id': const Uuid().v4(),
            'sale_id': saleId,
            'customer_id': customerId,
            'amount': amountPaid,
            'method': paymentMethod,
            'status': 'completed',
            'created_at': now,
            'is_deleted': 0,
          },
        );

        // تحديث رصيد العميل إذا كان الدفع آجلاً
        if (customerId != null && paymentMethod == 'credit') {
          final remainingAmount = total - amountPaid;
          if (remainingAmount > 0) {
            await txn.insert(
              'customer_transactions',
              {
                'id': const Uuid().v4(),
                'customer_id': customerId,
                'amount': -remainingAmount,
                'type': 'sale',
                'reference_id': saleId,
                'notes': 'بيع آجل',
                'created_at': now,
                'is_deleted': 0,
              },
            );

            // تحديث رصيد العميل في جدول العملاء
            await txn.rawUpdate(
              'UPDATE customers SET balance = balance - ? WHERE id = ?',
              [remainingAmount, customerId],
            );
          }
        }
      });

      // إرجاع معرف البيع
      return saleId;
    } catch (e) {
      AppLogger.error('خطأ في إنشاء عملية بيع', error: e);
      return null;
    }
  }

  /// الحصول على عملية بيع بواسطة المعرف
  Future<Map<String, dynamic>?> getSaleById(String id) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على البيع الرئيسي
      final List<Map<String, dynamic>> sales = await db.query(
        'sales',
        where: 'id = ? AND is_deleted = ?',
        whereArgs: [id, 0],
      );

      if (sales.isEmpty) {
        return null;
      }

      final sale = sales.first;

      // الحصول على عناصر البيع
      final List<Map<String, dynamic>> items = await db.query(
        'sale_items',
        where: 'sale_id = ? AND is_deleted = ?',
        whereArgs: [id, 0],
      );

      // الحصول على المدفوعات
      final List<Map<String, dynamic>> payments = await db.query(
        'payments',
        where: 'sale_id = ? AND is_deleted = ?',
        whereArgs: [id, 0],
      );

      // إرجاع البيع مع العناصر والمدفوعات
      return {
        ...sale,
        'items': items,
        'payments': payments,
      };
    } catch (e) {
      AppLogger.error('خطأ في الحصول على عملية بيع', error: e);
      return null;
    }
  }

  /// الحصول على جميع عمليات البيع
  Future<List<Map<String, dynamic>>> getAllSales() async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> sales = await db.query(
        'sales',
        where: 'is_deleted = ?',
        whereArgs: [0],
        orderBy: 'created_at DESC',
      );

      return sales;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على عمليات البيع', error: e);
      return [];
    }
  }

  /// الحصول على عمليات البيع لعميل معين
  Future<List<Map<String, dynamic>>> getSalesByCustomerId(
      String customerId) async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> sales = await db.query(
        'sales',
        where: 'customer_id = ? AND is_deleted = ?',
        whereArgs: [customerId, 0],
        orderBy: 'created_at DESC',
      );

      return sales;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على عمليات البيع لعميل', error: e);
      return [];
    }
  }

  /// إلغاء عملية بيع
  Future<bool> cancelSale(String id) async {
    final db = await _databaseHelper.database;

    try {
      // بدء المعاملة
      await db.transaction((txn) async {
        final now = DateTime.now().toIso8601String();

        // الحصول على عناصر البيع
        final List<Map<String, dynamic>> items = await txn.query(
          'sale_items',
          where: 'sale_id = ? AND is_deleted = ?',
          whereArgs: [id, 0],
        );

        // إعادة المخزون
        for (final item in items) {
          final productId = item['product_id'];
          final quantity = item['quantity'] as double;

          // التحقق مما إذا كان المنتج خدمة
          final List<Map<String, dynamic>> products = await txn.query(
            'products',
            columns: ['is_service'],
            where: 'id = ?',
            whereArgs: [productId],
          );

          if (products.isNotEmpty && products.first['is_service'] == 0) {
            await _productService.updateProductStock(
              productId,
              quantity,
            );
          }
        }

        // الحصول على معلومات البيع
        final List<Map<String, dynamic>> sales = await txn.query(
          'sales',
          where: 'id = ? AND is_deleted = ?',
          whereArgs: [id, 0],
        );

        if (sales.isEmpty) {
          return false;
        }

        final sale = sales.first;
        final customerId = sale['customer_id'];

        // إذا كان البيع آجلاً، إعادة رصيد العميل
        if (customerId != null && sale['payment_method'] == 'credit') {
          final total = sale['total'] as double;
          final amountPaid = sale['amount_paid'] as double;
          final remainingAmount = total - amountPaid;

          if (remainingAmount > 0) {
            await txn.insert(
              'customer_transactions',
              {
                'id': const Uuid().v4(),
                'customer_id': customerId,
                'amount': remainingAmount,
                'type': 'sale_cancel',
                'reference_id': id,
                'notes': 'إلغاء بيع آجل',
                'created_at': now,
                'is_deleted': 0,
              },
            );

            // تحديث رصيد العميل في جدول العملاء
            await txn.rawUpdate(
              'UPDATE customers SET balance = balance + ? WHERE id = ?',
              [remainingAmount, customerId],
            );
          }
        }

        // تحديث حالة البيع
        await txn.update(
          'sales',
          {
            'status': 'cancelled',
            'updated_at': now,
          },
          where: 'id = ?',
          whereArgs: [id],
        );
      });

      return true;
    } catch (e) {
      AppLogger.error('خطأ في إلغاء عملية بيع', error: e);
      return false;
    }
  }

  /// حذف عملية بيع (حذف منطقي)
  Future<bool> deleteSale(String id) async {
    try {
      final db = await _databaseHelper.database;

      await db.update(
        'sales',
        {
          'is_deleted': 1,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      return true;
    } catch (e) {
      AppLogger.error('خطأ في حذف عملية بيع', error: e);
      return false;
    }
  }
}
