import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/cart_item.dart';
import '../../../core/models/payment.dart';
import '../../../core/theme/index.dart';

/// واجهة معاينة الإيصال
class ReceiptPreview extends StatelessWidget {
  final String saleId;
  final String customerName;
  final List<CartItem> items;
  final double subtotal;
  final double taxAmount;
  final double discount;
  final double total;
  final double amountPaid;
  final double change;
  final String paymentMethod;
  final DateTime date;

  const ReceiptPreview({
    Key? key,
    required this.saleId,
    required this.customerName,
    required this.items,
    required this.subtotal,
    required this.taxAmount,
    required this.discount,
    required this.total,
    required this.amountPaid,
    required this.change,
    required this.paymentMethod,
    required this.date,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('معاينة الإيصال'),
      content: SingleChildScrollView(
        child: Container(
          width: double.maxFinite,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.lightTextSecondary,
            border: Border.all(color: AppColors.lightSurfaceVariant),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // اسم الشركة
              const Text(
                'شركة تاجر بلس',
                style: AppTypography(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.spacing4),

              // عنوان الشركة
              const Text(
                'الرياض، المملكة العربية السعودية',
                style: AppTypography(fontSize: 12),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.spacing4),

              // رقم الهاتف
              const Text(
                'هاتف: 0123456789',
                style: AppTypography(fontSize: 12),
                textAlign: TextAlign.center,
              ),
              const Divider(height: 16),

              // رقم الفاتورة والتاريخ
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'رقم الفاتورة:',
                    style: AppTypography(fontWeight: FontWeight.bold),
                  ),
                  Text(saleId.substring(0, 8)),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing4),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'التاريخ:',
                    style: AppTypography(fontWeight: FontWeight.bold),
                  ),
                  Text(DateFormat('yyyy-MM-dd HH:mm').format(date)),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing4),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'العميل:',
                    style: AppTypography(fontWeight: FontWeight.bold),
                  ),
                  Text(customerName),
                ],
              ),
              const Divider(height: 16),

              // عناوين الجدول
              const Row(
                children: [
                  Expanded(
                    flex: 4,
                    child: Text(
                      'الصنف',
                      style: AppTypography(fontWeight: FontWeight.bold),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'الكمية',
                      style: AppTypography(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'السعر',
                      style: AppTypography(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'المجموع',
                      style: AppTypography(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),
              const Divider(height: 8),

              // عناصر الفاتورة
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: items.length,
                itemBuilder: (context, index) {
                  final item = items[index];

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 4,
                          child: Text(
                            item.product.name,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            item.quantity.toStringAsFixed(
                              item.product.isService ? 2 : 0,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            item.price.toStringAsFixed(2),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            item.subtotal.toStringAsFixed(2),
                            textAlign: TextAlign.end,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
              const Divider(height: 16),

              // ملخص الفاتورة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('المجموع الفرعي:'),
                  Text(subtotal.toStringAsFixed(2)),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing4),

              if (discount > 0) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('الخصم:'),
                    Text(discount.toStringAsFixed(2)),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacing4),
              ],

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('الضريبة:'),
                  Text(taxAmount.toStringAsFixed(2)),
                ],
              ),
              const Divider(height: 8),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'الإجمالي:',
                    style: AppTypography(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    total.toStringAsFixed(2),
                    style: const AppTypography(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing8),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('طريقة الدفع:'),
                  Text(PaymentMethods.getLocalizedName(paymentMethod)),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing4),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('المبلغ المدفوع:'),
                  Text(amountPaid.toStringAsFixed(2)),
                ],
              ),
              const SizedBox(height: AppDimensions.spacing4),

              if (change > 0)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('الباقي:'),
                    Text(change.toStringAsFixed(2)),
                  ],
                ),

              if (change < 0)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('المتبقي:'),
                    Text((-change).toStringAsFixed(2)),
                  ],
                ),

              const Divider(height: 16),

              // رسالة شكر
              const Text(
                'شكراً لتسوقكم معنا',
                style: AppTypography(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.spacing4),

              // معلومات إضافية
              const Text(
                'نسعد بخدمتكم دائماً',
                style: AppTypography(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('إغلاق'),
        ),
        ElevatedButton.icon(
          onPressed: () {
            // TODO: طباعة الإيصال
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('جاري طباعة الإيصال...'),
              ),
            );
          },
          icon: const Icon(Icons.print),
          label: const Text('طباعة'),
        ),
      ],
    );
  }
}
