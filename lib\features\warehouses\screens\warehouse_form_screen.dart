import 'package:flutter/material.dart';
import '../../../core/providers/app_providers.dart';

import '../../../core/utils/validators.dart';
import '../presenters/warehouse_presenter.dart';
import '../../../core/models/warehouse.dart';
import '../../../core/theme/index.dart';
import '../../../core/widgets/index.dart';

/// شاشة نموذج المخزن
class WarehouseFormScreen extends StatefulWidget {
  final Warehouse? warehouse;

  const WarehouseFormScreen({Key? key, this.warehouse}) : super(key: key);

  @override
  State<WarehouseFormScreen> createState() => _WarehouseFormScreenState();
}

class _WarehouseFormScreenState extends State<WarehouseFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();

  bool _isDefault = false;
  bool _isActive = true;
  bool _isLoading = false;

  late final WarehousePresenter _presenter;

  @override
  void initState() {
    super.initState();
    _presenter = AppProviders.getLazyPresenter<WarehousePresenter>(
        () => WarehousePresenter());

    if (widget.warehouse != null) {
      _nameController.text = widget.warehouse!.name;
      _codeController.text = widget.warehouse!.code ?? '';
      _addressController.text = widget.warehouse!.address ?? '';
      _isDefault = widget.warehouse!.isDefault;
      _isActive = widget.warehouse!.isActive;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.warehouse != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'تعديل مخزن' : 'إضافة مخزن'),
      ),
      body: SafeLayout(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات المخزن
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'معلومات المخزن',
                          style: AppTypography.createCustomStyle(
                            fontSize: 18,
                            fontWeight: AppTypography.weightBold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // اسم المخزن
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'اسم المخزن *',
                            border: OutlineInputBorder(),
                          ),
                          controller: _nameController,
                          validator: Validators.required('اسم المخزن'),
                        ),
                        const SizedBox(height: 16),

                        // رمز المخزن
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'رمز المخزن',
                            border: OutlineInputBorder(),
                          ),
                          controller: _codeController,
                        ),
                        const SizedBox(height: 16),

                        // عنوان المخزن
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'عنوان المخزن',
                            border: OutlineInputBorder(),
                          ),
                          controller: _addressController,
                          maxLines: 3,
                        ),
                        const SizedBox(height: 16),

                        // المخزن الافتراضي
                        SwitchListTile(
                          title: const Text('المخزن الافتراضي'),
                          value: _isDefault,
                          onChanged: (value) {
                            setState(() {
                              _isDefault = value;
                            });
                          },
                          secondary: const Icon(Icons.star),
                        ),

                        // حالة المخزن
                        SwitchListTile(
                          title: const Text('نشط'),
                          value: _isActive,
                          onChanged: (value) {
                            setState(() {
                              _isActive = value;
                            });
                          },
                          secondary: const Icon(Icons.check_circle),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // أزرار الإجراءات
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AkButton(
                      text: 'إلغاء',
                      icon: Icons.cancel,
                      onPressed: () => Navigator.pop(context),
                      type: AkButtonType.secondary,
                    ),
                    const SizedBox(width: 16),
                    AkButton(
                      text: isEditing ? 'تحديث' : 'حفظ',
                      icon: isEditing ? Icons.update : Icons.save,
                      onPressed: _isLoading ? null : _saveWarehouse,
                      type: AkButtonType.primary,
                      isLoading: _isLoading,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// حفظ المخزن
  void _saveWarehouse() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final warehouse = Warehouse(
      id: widget.warehouse?.id,
      name: _nameController.text,
      code: _codeController.text.isEmpty ? '' : _codeController.text,
      address: _addressController.text.isEmpty ? null : _addressController.text,
      isDefault: _isDefault,
      isActive: _isActive,
    );

    // تنفيذ عملية الحفظ بشكل غير متزامن
    _performSave(warehouse);
  }

  /// تنفيذ عملية الحفظ
  Future<void> _performSave(Warehouse warehouse) async {
    bool success;

    try {
      if (widget.warehouse == null) {
        // إضافة مخزن جديد
        success = await _presenter.addWarehouse(warehouse);
      } else {
        // تحديث مخزن موجود
        success = await _presenter.updateWarehouse(warehouse);
      }

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (success) {
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_presenter.errorMessage ?? 'فشل في حفظ المخزن'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ غير متوقع: ${e.toString()}'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}
