import 'dart:io';
import 'package:uuid/uuid.dart';
import '../../../core/database/database_service.dart';
import '../../../core/utils/error_tracker.dart';
import '../models/activity_log.dart';

/// خدمة سجل النشاطات
class ActivityLogService {
  final DatabaseService _db = DatabaseService.instance;

  /// الحصول على سجل النشاطات
  Future<List<ActivityLog>> getActivities({
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? action,
    String? module,
  }) async {
    try {
      // بناء استعلام SQL
      String query = 'SELECT * FROM activity_logs WHERE 1=1';
      final List<dynamic> args = [];

      // إضافة شروط الفلترة
      if (startDate != null) {
        query += ' AND timestamp >= ?';
        args.add(startDate.millisecondsSinceEpoch);
      }

      if (endDate != null) {
        query += ' AND timestamp <= ?';
        args.add(endDate.millisecondsSinceEpoch);
      }

      if (userId != null) {
        query += ' AND user_id = ?';
        args.add(userId);
      }

      if (action != null) {
        query += ' AND action = ?';
        args.add(action);
      }

      if (module != null) {
        query += ' AND module = ?';
        args.add(module);
      }

      // ترتيب النتائج
      query += ' ORDER BY timestamp DESC';

      // تنفيذ الاستعلام
      final result = await _db.rawQuery(query, args);

      // تحويل النتائج إلى قائمة من ActivityLog
      return result.map((map) => ActivityLog.fromMap(map)).toList();
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على سجل النشاطات',
        error: e,
        stackTrace: stackTrace,
        context: {
          'startDate': startDate?.toIso8601String(),
          'endDate': endDate?.toIso8601String(),
          'userId': userId,
          'action': action,
          'module': module,
        },
      );

      // في حالة الفشل، إرجاع قائمة فارغة
      return [];
    }
  }

  /// تسجيل نشاط جديد
  Future<bool> logActivity({
    required String userId,
    required String userName,
    required String action,
    required String module,
    String? details,
  }) async {
    try {
      // الحصول على عنوان IP
      final ipAddress = await _getIpAddress();

      // إنشاء كائن ActivityLog
      final activity = ActivityLog(
        id: const Uuid().v4(),
        userId: userId,
        userName: userName,
        action: action,
        module: module,
        details: details ?? '',
        ipAddress: ipAddress,
        timestamp: DateTime.now(),
      );

      // إدخال النشاط في قاعدة البيانات
      await _db.insert('activity_logs', activity.toMap());

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تسجيل النشاط',
        error: e,
        stackTrace: stackTrace,
        context: {
          'userId': userId,
          'userName': userName,
          'action': action,
          'module': module,
          'details': details,
        },
      );
      return false;
    }
  }

  /// الحصول على عنوان IP
  Future<String> _getIpAddress() async {
    try {
      final interfaces = await NetworkInterface.list(
        type: InternetAddressType.IPv4,
        includeLinkLocal: false,
      );

      // البحث عن واجهة الشبكة غير المحلية
      for (final interface in interfaces) {
        for (final addr in interface.addresses) {
          if (!addr.isLoopback) {
            return addr.address;
          }
        }
      }

      // إذا لم يتم العثور على عنوان IP غير محلي، استخدم عنوان محلي
      return '127.0.0.1';
    } catch (e) {
      // في حالة الفشل، إرجاع عنوان محلي
      return '127.0.0.1';
    }
  }

  /// حذف سجلات النشاطات القديمة
  Future<bool> cleanupOldActivities(int daysToKeep) async {
    try {
      // حساب التاريخ الأقدم للاحتفاظ بالسجلات
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));

      // حذف السجلات القديمة
      await _db.delete(
        'activity_logs',
        where: 'timestamp < ?',
        whereArgs: [cutoffDate.millisecondsSinceEpoch],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في حذف سجلات النشاطات القديمة',
        error: e,
        stackTrace: stackTrace,
        context: {
          'daysToKeep': daysToKeep,
        },
      );
      return false;
    }
  }
}
